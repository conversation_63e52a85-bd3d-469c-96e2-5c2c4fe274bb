{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nimport { isBarline } from './typeguard.js';\nimport { defined, RuntimeError } from './util.js';\nexport class MultiMeasureRest extends Element {\n  static get CATEGORY() {\n    return \"MultiMeasureRest\";\n  }\n  constructor(numberOfMeasures, options) {\n    var _a;\n    super();\n    this.xs = {\n      left: NaN,\n      right: NaN\n    };\n    this.hasPaddingLeft = false;\n    this.hasPaddingRight = false;\n    this.hasLineThickness = false;\n    this.hasSymbolSpacing = false;\n    const fontSize = (_a = options.numberGlyphPoint) !== null && _a !== void 0 ? _a : Metrics.get('MultiMeasureRest.fontSize');\n    this.fontInfo.size = fontSize;\n    this.numberOfMeasures = numberOfMeasures;\n    this.text = '';\n    const t = `${this.numberOfMeasures}`;\n    for (const digit of t) {\n      this.text += String.fromCodePoint(0xe080 + Number(digit));\n    }\n    this.hasPaddingLeft = typeof options.paddingLeft === 'number';\n    this.hasPaddingRight = typeof options.paddingRight === 'number';\n    this.hasLineThickness = typeof options.lineThickness === 'number';\n    this.hasSymbolSpacing = typeof options.symbolSpacing === 'number';\n    this.renderOptions = Object.assign({\n      useSymbols: false,\n      showNumber: true,\n      numberLine: -0.5,\n      numberGlyphPoint: fontSize,\n      line: 2,\n      spacingBetweenLinesPx: Tables.STAVE_LINE_DISTANCE,\n      serifThickness: 2,\n      semibreveRestGlyphScale: Metrics.get('fontSize'),\n      paddingLeft: 0,\n      paddingRight: 0,\n      lineThickness: 5,\n      symbolSpacing: 0\n    }, options);\n  }\n  getXs() {\n    return this.xs;\n  }\n  setStave(stave) {\n    this.stave = stave;\n    return this;\n  }\n  getStave() {\n    return this.stave;\n  }\n  checkStave() {\n    return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n  }\n  drawLine(stave, ctx, left, right) {\n    const options = this.renderOptions;\n    const y = stave.getYForLine(options.line);\n    const padding = (right - left) * 0.1;\n    left += padding;\n    right -= padding;\n    let txt = '\\ue4ef';\n    const el = new Element();\n    el.setText(txt);\n    const elWidth = el.getWidth();\n    if (!elWidth) {\n      throw new RuntimeError('Cannot drawLine if width is 0');\n    }\n    for (let i = 1; (i + 2) * elWidth + left <= right; i++) {\n      txt += '\\ue4f0';\n    }\n    txt += '\\ue4f1';\n    el.setText(txt);\n    el.renderText(ctx, left + (right - left) * 0.5 - el.getWidth() * 0.5, y);\n  }\n  drawSymbols(stave, ctx, left, right) {\n    const n4 = Math.floor(this.numberOfMeasures / 4);\n    const n = this.numberOfMeasures % 4;\n    const n2 = Math.floor(n / 2);\n    const n1 = n % 2;\n    const options = this.renderOptions;\n    const elMiddle = new Element();\n    let txt = '';\n    for (let i = 0; i < n4; ++i) {\n      txt += Glyphs.restLonga + ' ';\n    }\n    for (let i = 0; i < n2; ++i) {\n      txt += Glyphs.restDoubleWhole + ' ';\n    }\n    elMiddle.setText(txt);\n    const elTop = new Element();\n    txt = '';\n    for (let i = 0; i < n1; ++i) {\n      txt += Glyphs.restWhole + ' ';\n    }\n    elTop.setText(txt);\n    const width = elMiddle.getWidth() + elTop.getWidth();\n    let x = left + (right - left) * 0.5 - width * 0.5;\n    const line = options.line;\n    const yTop = stave.getYForLine(line - 1);\n    const yMiddle = stave.getYForLine(line);\n    elMiddle.renderText(ctx, x, yMiddle);\n    x += elMiddle.getWidth();\n    elTop.renderText(ctx, x, yTop);\n    x += elTop.getWidth();\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    const stave = this.checkStave();\n    let left = stave.getNoteStartX();\n    let right = stave.getNoteEndX();\n    const begModifiers = stave.getModifiers(StaveModifierPosition.BEGIN);\n    if (begModifiers.length === 1 && isBarline(begModifiers[0])) {\n      left -= begModifiers[0].getWidth();\n    }\n    const options = this.renderOptions;\n    if (this.hasPaddingLeft) {\n      left = stave.getX() + options.paddingLeft;\n    }\n    if (this.hasPaddingRight) {\n      right = stave.getX() + stave.getWidth() - options.paddingRight;\n    }\n    this.xs.left = left;\n    this.xs.right = right;\n    if (options.useSymbols) {\n      this.drawSymbols(stave, ctx, left, right);\n    } else {\n      this.drawLine(stave, ctx, left, right);\n    }\n    if (options.showNumber) {\n      this.renderText(ctx, left + (right - left) * 0.5 - this.width * 0.5, stave.getYForLine(options.numberLine) - this.height * 0.5);\n    }\n  }\n}", "map": {"version": 3, "names": ["Element", "Glyphs", "Metrics", "StaveModifierPosition", "Tables", "isBarline", "defined", "RuntimeError", "MultiMeasureRest", "CATEGORY", "constructor", "numberOfMeasures", "options", "_a", "xs", "left", "NaN", "right", "hasPaddingLeft", "hasPaddingRight", "hasLineThickness", "hasSymbolSpacing", "fontSize", "numberGlyphPoint", "get", "fontInfo", "size", "text", "t", "digit", "String", "fromCodePoint", "Number", "paddingLeft", "paddingRight", "lineThickness", "symbolSpacing", "renderOptions", "Object", "assign", "useSymbols", "showNumber", "numberLine", "line", "spacingBetweenLinesPx", "STAVE_LINE_DISTANCE", "serif<PERSON><PERSON><PERSON><PERSON>", "semibreveRestGlyphScale", "getXs", "setStave", "stave", "getStave", "checkStave", "drawLine", "ctx", "y", "getYForLine", "padding", "txt", "el", "setText", "<PERSON><PERSON><PERSON><PERSON>", "getWidth", "i", "renderText", "drawSymbols", "n4", "Math", "floor", "n", "n2", "n1", "<PERSON><PERSON><PERSON>", "restLonga", "restDoubleWhole", "elTop", "restWhole", "width", "x", "yTop", "yMiddle", "draw", "checkContext", "setRendered", "getNoteStartX", "getNoteEndX", "begModifiers", "getModifiers", "BEGIN", "length", "getX", "height"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/multimeasurerest.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nimport { isBarline } from './typeguard.js';\nimport { defined, RuntimeError } from './util.js';\nexport class MultiMeasureRest extends Element {\n    static get CATEGORY() {\n        return \"MultiMeasureRest\";\n    }\n    constructor(numberOfMeasures, options) {\n        var _a;\n        super();\n        this.xs = { left: NaN, right: NaN };\n        this.hasPaddingLeft = false;\n        this.hasPaddingRight = false;\n        this.hasLineThickness = false;\n        this.hasSymbolSpacing = false;\n        const fontSize = (_a = options.numberGlyphPoint) !== null && _a !== void 0 ? _a : Metrics.get('MultiMeasureRest.fontSize');\n        this.fontInfo.size = fontSize;\n        this.numberOfMeasures = numberOfMeasures;\n        this.text = '';\n        const t = `${this.numberOfMeasures}`;\n        for (const digit of t) {\n            this.text += String.fromCodePoint(0xe080 + Number(digit));\n        }\n        this.hasPaddingLeft = typeof options.paddingLeft === 'number';\n        this.hasPaddingRight = typeof options.paddingRight === 'number';\n        this.hasLineThickness = typeof options.lineThickness === 'number';\n        this.hasSymbolSpacing = typeof options.symbolSpacing === 'number';\n        this.renderOptions = Object.assign({ useSymbols: false, showNumber: true, numberLine: -0.5, numberGlyphPoint: fontSize, line: 2, spacingBetweenLinesPx: Tables.STAVE_LINE_DISTANCE, serifThickness: 2, semibreveRestGlyphScale: Metrics.get('fontSize'), paddingLeft: 0, paddingRight: 0, lineThickness: 5, symbolSpacing: 0 }, options);\n    }\n    getXs() {\n        return this.xs;\n    }\n    setStave(stave) {\n        this.stave = stave;\n        return this;\n    }\n    getStave() {\n        return this.stave;\n    }\n    checkStave() {\n        return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n    }\n    drawLine(stave, ctx, left, right) {\n        const options = this.renderOptions;\n        const y = stave.getYForLine(options.line);\n        const padding = (right - left) * 0.1;\n        left += padding;\n        right -= padding;\n        let txt = '\\ue4ef';\n        const el = new Element();\n        el.setText(txt);\n        const elWidth = el.getWidth();\n        if (!elWidth) {\n            throw new RuntimeError('Cannot drawLine if width is 0');\n        }\n        for (let i = 1; (i + 2) * elWidth + left <= right; i++) {\n            txt += '\\ue4f0';\n        }\n        txt += '\\ue4f1';\n        el.setText(txt);\n        el.renderText(ctx, left + (right - left) * 0.5 - el.getWidth() * 0.5, y);\n    }\n    drawSymbols(stave, ctx, left, right) {\n        const n4 = Math.floor(this.numberOfMeasures / 4);\n        const n = this.numberOfMeasures % 4;\n        const n2 = Math.floor(n / 2);\n        const n1 = n % 2;\n        const options = this.renderOptions;\n        const elMiddle = new Element();\n        let txt = '';\n        for (let i = 0; i < n4; ++i) {\n            txt += Glyphs.restLonga + ' ';\n        }\n        for (let i = 0; i < n2; ++i) {\n            txt += Glyphs.restDoubleWhole + ' ';\n        }\n        elMiddle.setText(txt);\n        const elTop = new Element();\n        txt = '';\n        for (let i = 0; i < n1; ++i) {\n            txt += Glyphs.restWhole + ' ';\n        }\n        elTop.setText(txt);\n        const width = elMiddle.getWidth() + elTop.getWidth();\n        let x = left + (right - left) * 0.5 - width * 0.5;\n        const line = options.line;\n        const yTop = stave.getYForLine(line - 1);\n        const yMiddle = stave.getYForLine(line);\n        elMiddle.renderText(ctx, x, yMiddle);\n        x += elMiddle.getWidth();\n        elTop.renderText(ctx, x, yTop);\n        x += elTop.getWidth();\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        const stave = this.checkStave();\n        let left = stave.getNoteStartX();\n        let right = stave.getNoteEndX();\n        const begModifiers = stave.getModifiers(StaveModifierPosition.BEGIN);\n        if (begModifiers.length === 1 && isBarline(begModifiers[0])) {\n            left -= begModifiers[0].getWidth();\n        }\n        const options = this.renderOptions;\n        if (this.hasPaddingLeft) {\n            left = stave.getX() + options.paddingLeft;\n        }\n        if (this.hasPaddingRight) {\n            right = stave.getX() + stave.getWidth() - options.paddingRight;\n        }\n        this.xs.left = left;\n        this.xs.right = right;\n        if (options.useSymbols) {\n            this.drawSymbols(stave, ctx, left, right);\n        }\n        else {\n            this.drawLine(stave, ctx, left, right);\n        }\n        if (options.showNumber) {\n            this.renderText(ctx, left + (right - left) * 0.5 - this.width * 0.5, stave.getYForLine(options.numberLine) - this.height * 0.5);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AACjD,OAAO,MAAMC,gBAAgB,SAASR,OAAO,CAAC;EAC1C,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,kBAAkB;EAC7B;EACAC,WAAWA,CAACC,gBAAgB,EAAEC,OAAO,EAAE;IACnC,IAAIC,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,EAAE,GAAG;MAAEC,IAAI,EAAEC,GAAG;MAAEC,KAAK,EAAED;IAAI,CAAC;IACnC,IAAI,CAACE,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,MAAMC,QAAQ,GAAG,CAACT,EAAE,GAAGD,OAAO,CAACW,gBAAgB,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGX,OAAO,CAACsB,GAAG,CAAC,2BAA2B,CAAC;IAC1H,IAAI,CAACC,QAAQ,CAACC,IAAI,GAAGJ,QAAQ;IAC7B,IAAI,CAACX,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACgB,IAAI,GAAG,EAAE;IACd,MAAMC,CAAC,GAAG,GAAG,IAAI,CAACjB,gBAAgB,EAAE;IACpC,KAAK,MAAMkB,KAAK,IAAID,CAAC,EAAE;MACnB,IAAI,CAACD,IAAI,IAAIG,MAAM,CAACC,aAAa,CAAC,MAAM,GAAGC,MAAM,CAACH,KAAK,CAAC,CAAC;IAC7D;IACA,IAAI,CAACX,cAAc,GAAG,OAAON,OAAO,CAACqB,WAAW,KAAK,QAAQ;IAC7D,IAAI,CAACd,eAAe,GAAG,OAAOP,OAAO,CAACsB,YAAY,KAAK,QAAQ;IAC/D,IAAI,CAACd,gBAAgB,GAAG,OAAOR,OAAO,CAACuB,aAAa,KAAK,QAAQ;IACjE,IAAI,CAACd,gBAAgB,GAAG,OAAOT,OAAO,CAACwB,aAAa,KAAK,QAAQ;IACjE,IAAI,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,UAAU,EAAE,KAAK;MAAEC,UAAU,EAAE,IAAI;MAAEC,UAAU,EAAE,CAAC,GAAG;MAAEnB,gBAAgB,EAAED,QAAQ;MAAEqB,IAAI,EAAE,CAAC;MAAEC,qBAAqB,EAAExC,MAAM,CAACyC,mBAAmB;MAAEC,cAAc,EAAE,CAAC;MAAEC,uBAAuB,EAAE7C,OAAO,CAACsB,GAAG,CAAC,UAAU,CAAC;MAAES,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,CAAC;MAAEC,aAAa,EAAE,CAAC;MAAEC,aAAa,EAAE;IAAE,CAAC,EAAExB,OAAO,CAAC;EAC5U;EACAoC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAAClC,EAAE;EAClB;EACAmC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,KAAK;EACrB;EACAE,UAAUA,CAAA,EAAG;IACT,OAAO9C,OAAO,CAAC,IAAI,CAAC4C,KAAK,EAAE,SAAS,EAAE,gCAAgC,CAAC;EAC3E;EACAG,QAAQA,CAACH,KAAK,EAAEI,GAAG,EAAEvC,IAAI,EAAEE,KAAK,EAAE;IAC9B,MAAML,OAAO,GAAG,IAAI,CAACyB,aAAa;IAClC,MAAMkB,CAAC,GAAGL,KAAK,CAACM,WAAW,CAAC5C,OAAO,CAAC+B,IAAI,CAAC;IACzC,MAAMc,OAAO,GAAG,CAACxC,KAAK,GAAGF,IAAI,IAAI,GAAG;IACpCA,IAAI,IAAI0C,OAAO;IACfxC,KAAK,IAAIwC,OAAO;IAChB,IAAIC,GAAG,GAAG,QAAQ;IAClB,MAAMC,EAAE,GAAG,IAAI3D,OAAO,CAAC,CAAC;IACxB2D,EAAE,CAACC,OAAO,CAACF,GAAG,CAAC;IACf,MAAMG,OAAO,GAAGF,EAAE,CAACG,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACD,OAAO,EAAE;MACV,MAAM,IAAItD,YAAY,CAAC,+BAA+B,CAAC;IAC3D;IACA,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAE,CAACA,CAAC,GAAG,CAAC,IAAIF,OAAO,GAAG9C,IAAI,IAAIE,KAAK,EAAE8C,CAAC,EAAE,EAAE;MACpDL,GAAG,IAAI,QAAQ;IACnB;IACAA,GAAG,IAAI,QAAQ;IACfC,EAAE,CAACC,OAAO,CAACF,GAAG,CAAC;IACfC,EAAE,CAACK,UAAU,CAACV,GAAG,EAAEvC,IAAI,GAAG,CAACE,KAAK,GAAGF,IAAI,IAAI,GAAG,GAAG4C,EAAE,CAACG,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAEP,CAAC,CAAC;EAC5E;EACAU,WAAWA,CAACf,KAAK,EAAEI,GAAG,EAAEvC,IAAI,EAAEE,KAAK,EAAE;IACjC,MAAMiD,EAAE,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACzD,gBAAgB,GAAG,CAAC,CAAC;IAChD,MAAM0D,CAAC,GAAG,IAAI,CAAC1D,gBAAgB,GAAG,CAAC;IACnC,MAAM2D,EAAE,GAAGH,IAAI,CAACC,KAAK,CAACC,CAAC,GAAG,CAAC,CAAC;IAC5B,MAAME,EAAE,GAAGF,CAAC,GAAG,CAAC;IAChB,MAAMzD,OAAO,GAAG,IAAI,CAACyB,aAAa;IAClC,MAAMmC,QAAQ,GAAG,IAAIxE,OAAO,CAAC,CAAC;IAC9B,IAAI0D,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,EAAE,EAAE,EAAEH,CAAC,EAAE;MACzBL,GAAG,IAAIzD,MAAM,CAACwE,SAAS,GAAG,GAAG;IACjC;IACA,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,EAAE,EAAE,EAAEP,CAAC,EAAE;MACzBL,GAAG,IAAIzD,MAAM,CAACyE,eAAe,GAAG,GAAG;IACvC;IACAF,QAAQ,CAACZ,OAAO,CAACF,GAAG,CAAC;IACrB,MAAMiB,KAAK,GAAG,IAAI3E,OAAO,CAAC,CAAC;IAC3B0D,GAAG,GAAG,EAAE;IACR,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,EAAE,EAAE,EAAER,CAAC,EAAE;MACzBL,GAAG,IAAIzD,MAAM,CAAC2E,SAAS,GAAG,GAAG;IACjC;IACAD,KAAK,CAACf,OAAO,CAACF,GAAG,CAAC;IAClB,MAAMmB,KAAK,GAAGL,QAAQ,CAACV,QAAQ,CAAC,CAAC,GAAGa,KAAK,CAACb,QAAQ,CAAC,CAAC;IACpD,IAAIgB,CAAC,GAAG/D,IAAI,GAAG,CAACE,KAAK,GAAGF,IAAI,IAAI,GAAG,GAAG8D,KAAK,GAAG,GAAG;IACjD,MAAMlC,IAAI,GAAG/B,OAAO,CAAC+B,IAAI;IACzB,MAAMoC,IAAI,GAAG7B,KAAK,CAACM,WAAW,CAACb,IAAI,GAAG,CAAC,CAAC;IACxC,MAAMqC,OAAO,GAAG9B,KAAK,CAACM,WAAW,CAACb,IAAI,CAAC;IACvC6B,QAAQ,CAACR,UAAU,CAACV,GAAG,EAAEwB,CAAC,EAAEE,OAAO,CAAC;IACpCF,CAAC,IAAIN,QAAQ,CAACV,QAAQ,CAAC,CAAC;IACxBa,KAAK,CAACX,UAAU,CAACV,GAAG,EAAEwB,CAAC,EAAEC,IAAI,CAAC;IAC9BD,CAAC,IAAIH,KAAK,CAACb,QAAQ,CAAC,CAAC;EACzB;EACAmB,IAAIA,CAAA,EAAG;IACH,MAAM3B,GAAG,GAAG,IAAI,CAAC4B,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMjC,KAAK,GAAG,IAAI,CAACE,UAAU,CAAC,CAAC;IAC/B,IAAIrC,IAAI,GAAGmC,KAAK,CAACkC,aAAa,CAAC,CAAC;IAChC,IAAInE,KAAK,GAAGiC,KAAK,CAACmC,WAAW,CAAC,CAAC;IAC/B,MAAMC,YAAY,GAAGpC,KAAK,CAACqC,YAAY,CAACpF,qBAAqB,CAACqF,KAAK,CAAC;IACpE,IAAIF,YAAY,CAACG,MAAM,KAAK,CAAC,IAAIpF,SAAS,CAACiF,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;MACzDvE,IAAI,IAAIuE,YAAY,CAAC,CAAC,CAAC,CAACxB,QAAQ,CAAC,CAAC;IACtC;IACA,MAAMlD,OAAO,GAAG,IAAI,CAACyB,aAAa;IAClC,IAAI,IAAI,CAACnB,cAAc,EAAE;MACrBH,IAAI,GAAGmC,KAAK,CAACwC,IAAI,CAAC,CAAC,GAAG9E,OAAO,CAACqB,WAAW;IAC7C;IACA,IAAI,IAAI,CAACd,eAAe,EAAE;MACtBF,KAAK,GAAGiC,KAAK,CAACwC,IAAI,CAAC,CAAC,GAAGxC,KAAK,CAACY,QAAQ,CAAC,CAAC,GAAGlD,OAAO,CAACsB,YAAY;IAClE;IACA,IAAI,CAACpB,EAAE,CAACC,IAAI,GAAGA,IAAI;IACnB,IAAI,CAACD,EAAE,CAACG,KAAK,GAAGA,KAAK;IACrB,IAAIL,OAAO,CAAC4B,UAAU,EAAE;MACpB,IAAI,CAACyB,WAAW,CAACf,KAAK,EAAEI,GAAG,EAAEvC,IAAI,EAAEE,KAAK,CAAC;IAC7C,CAAC,MACI;MACD,IAAI,CAACoC,QAAQ,CAACH,KAAK,EAAEI,GAAG,EAAEvC,IAAI,EAAEE,KAAK,CAAC;IAC1C;IACA,IAAIL,OAAO,CAAC6B,UAAU,EAAE;MACpB,IAAI,CAACuB,UAAU,CAACV,GAAG,EAAEvC,IAAI,GAAG,CAACE,KAAK,GAAGF,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC8D,KAAK,GAAG,GAAG,EAAE3B,KAAK,CAACM,WAAW,CAAC5C,OAAO,CAAC8B,UAAU,CAAC,GAAG,IAAI,CAACiD,MAAM,GAAG,GAAG,CAAC;IACnI;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}