{"ast": null, "code": "import { StaveModifier } from './stavemodifier.js';\nexport var VoltaType;\n(function (VoltaType) {\n  VoltaType[VoltaType[\"NONE\"] = 1] = \"NONE\";\n  VoltaType[VoltaType[\"BEGIN\"] = 2] = \"BEGIN\";\n  VoltaType[VoltaType[\"MID\"] = 3] = \"MID\";\n  VoltaType[VoltaType[\"END\"] = 4] = \"END\";\n  VoltaType[VoltaType[\"BEGIN_END\"] = 5] = \"BEGIN_END\";\n})(VoltaType || (VoltaType = {}));\nexport class Volta extends StaveModifier {\n  static get CATEGORY() {\n    return \"Volta\";\n  }\n  static get type() {\n    return VoltaType;\n  }\n  constructor(type, label, x, yShift) {\n    super();\n    this.type = type;\n    this.x = x;\n    this.yShift = yShift;\n    this.text = label;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const x = stave.getModifierXShift(this.getPosition());\n    const ctx = stave.checkContext();\n    this.setRendered();\n    let width = stave.getWidth() - x;\n    const topY = stave.getYForTopText(stave.getNumLines()) + this.yShift;\n    const vertHeight = 1.5 * stave.getSpacingBetweenLines();\n    switch (this.type) {\n      case VoltaType.BEGIN:\n        ctx.fillRect(this.x + x, topY, 1, vertHeight);\n        break;\n      case VoltaType.END:\n        width -= 5;\n        ctx.fillRect(this.x + x + width, topY, 1, vertHeight);\n        break;\n      case VoltaType.BEGIN_END:\n        width -= 3;\n        ctx.fillRect(this.x + x, topY, 1, vertHeight);\n        ctx.fillRect(this.x + x + width, topY, 1, vertHeight);\n        break;\n      default:\n        break;\n    }\n    if (this.type === VoltaType.BEGIN || this.type === VoltaType.BEGIN_END) {\n      this.renderText(ctx, x + 5, topY - this.yShift + 15);\n    }\n    ctx.fillRect(this.x + x, topY, width, 1);\n  }\n}", "map": {"version": 3, "names": ["StaveModifier", "VoltaType", "Volta", "CATEGORY", "type", "constructor", "label", "x", "yShift", "text", "draw", "stave", "checkStave", "getModifierXShift", "getPosition", "ctx", "checkContext", "setRendered", "width", "getWidth", "topY", "getYForTopText", "getNumLines", "vertHeight", "getSpacingBetweenLines", "BEGIN", "fillRect", "END", "BEGIN_END", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavevolta.js"], "sourcesContent": ["import { StaveModifier } from './stavemodifier.js';\nexport var VoltaType;\n(function (VoltaType) {\n    VoltaType[VoltaType[\"NONE\"] = 1] = \"NONE\";\n    VoltaType[VoltaType[\"BEGIN\"] = 2] = \"BEGIN\";\n    VoltaType[VoltaType[\"MID\"] = 3] = \"MID\";\n    VoltaType[VoltaType[\"END\"] = 4] = \"END\";\n    VoltaType[VoltaType[\"BEGIN_END\"] = 5] = \"BEGIN_END\";\n})(VoltaType || (VoltaType = {}));\nexport class Volta extends StaveModifier {\n    static get CATEGORY() {\n        return \"Volta\";\n    }\n    static get type() {\n        return VoltaType;\n    }\n    constructor(type, label, x, yShift) {\n        super();\n        this.type = type;\n        this.x = x;\n        this.yShift = yShift;\n        this.text = label;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const x = stave.getModifierXShift(this.getPosition());\n        const ctx = stave.checkContext();\n        this.setRendered();\n        let width = stave.getWidth() - x;\n        const topY = stave.getYForTopText(stave.getNumLines()) + this.yShift;\n        const vertHeight = 1.5 * stave.getSpacingBetweenLines();\n        switch (this.type) {\n            case VoltaType.BEGIN:\n                ctx.fillRect(this.x + x, topY, 1, vertHeight);\n                break;\n            case VoltaType.END:\n                width -= 5;\n                ctx.fillRect(this.x + x + width, topY, 1, vertHeight);\n                break;\n            case VoltaType.BEGIN_END:\n                width -= 3;\n                ctx.fillRect(this.x + x, topY, 1, vertHeight);\n                ctx.fillRect(this.x + x + width, topY, 1, vertHeight);\n                break;\n            default:\n                break;\n        }\n        if (this.type === VoltaType.BEGIN || this.type === VoltaType.BEGIN_END) {\n            this.renderText(ctx, x + 5, topY - this.yShift + 15);\n        }\n        ctx.fillRect(this.x + x, topY, width, 1);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzCA,SAAS,CAACA,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC3CA,SAAS,CAACA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACvCA,SAAS,CAACA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACvCA,SAAS,CAACA,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;AACvD,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO,MAAMC,KAAK,SAASF,aAAa,CAAC;EACrC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,OAAO;EAClB;EACA,WAAWC,IAAIA,CAAA,EAAG;IACd,OAAOH,SAAS;EACpB;EACAI,WAAWA,CAACD,IAAI,EAAEE,KAAK,EAAEC,CAAC,EAAEC,MAAM,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGH,KAAK;EACrB;EACAI,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAML,CAAC,GAAGI,KAAK,CAACE,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IACrD,MAAMC,GAAG,GAAGJ,KAAK,CAACK,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIC,KAAK,GAAGP,KAAK,CAACQ,QAAQ,CAAC,CAAC,GAAGZ,CAAC;IAChC,MAAMa,IAAI,GAAGT,KAAK,CAACU,cAAc,CAACV,KAAK,CAACW,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACd,MAAM;IACpE,MAAMe,UAAU,GAAG,GAAG,GAAGZ,KAAK,CAACa,sBAAsB,CAAC,CAAC;IACvD,QAAQ,IAAI,CAACpB,IAAI;MACb,KAAKH,SAAS,CAACwB,KAAK;QAChBV,GAAG,CAACW,QAAQ,CAAC,IAAI,CAACnB,CAAC,GAAGA,CAAC,EAAEa,IAAI,EAAE,CAAC,EAAEG,UAAU,CAAC;QAC7C;MACJ,KAAKtB,SAAS,CAAC0B,GAAG;QACdT,KAAK,IAAI,CAAC;QACVH,GAAG,CAACW,QAAQ,CAAC,IAAI,CAACnB,CAAC,GAAGA,CAAC,GAAGW,KAAK,EAAEE,IAAI,EAAE,CAAC,EAAEG,UAAU,CAAC;QACrD;MACJ,KAAKtB,SAAS,CAAC2B,SAAS;QACpBV,KAAK,IAAI,CAAC;QACVH,GAAG,CAACW,QAAQ,CAAC,IAAI,CAACnB,CAAC,GAAGA,CAAC,EAAEa,IAAI,EAAE,CAAC,EAAEG,UAAU,CAAC;QAC7CR,GAAG,CAACW,QAAQ,CAAC,IAAI,CAACnB,CAAC,GAAGA,CAAC,GAAGW,KAAK,EAAEE,IAAI,EAAE,CAAC,EAAEG,UAAU,CAAC;QACrD;MACJ;QACI;IACR;IACA,IAAI,IAAI,CAACnB,IAAI,KAAKH,SAAS,CAACwB,KAAK,IAAI,IAAI,CAACrB,IAAI,KAAKH,SAAS,CAAC2B,SAAS,EAAE;MACpE,IAAI,CAACC,UAAU,CAACd,GAAG,EAAER,CAAC,GAAG,CAAC,EAAEa,IAAI,GAAG,IAAI,CAACZ,MAAM,GAAG,EAAE,CAAC;IACxD;IACAO,GAAG,CAACW,QAAQ,CAAC,IAAI,CAACnB,CAAC,GAAGA,CAAC,EAAEa,IAAI,EAAEF,KAAK,EAAE,CAAC,CAAC;EAC5C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}