{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Helmet } from \"react-helmet\";\nimport VexTabBlock from \"./VexTabBlock.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"rgba(68, 25, 240, 0.08)\",\n      display: \"grid\",\n      gridTemplateRows: \"1fr auto auto\"\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"white\"\n    },\n    sideButton: {\n      backgroundColor: \"white\",\n      width: \"4rem\",\n      height: \"4rem\",\n      fontSize: \"1.2rem\",\n      fontWeight: \"bold\",\n      color: \"#5c14ba\",\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"1rem\",\n      cursor: \"pointer\",\n      margin: \"auto\"\n    },\n    sideButtonActive: {\n      backgroundColor: \"#9a62e3\",\n      color: \"white\",\n      border: \"2px solid #7f3bd9\",\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\"\n    }\n  };\n\n  // MUSIC STATES\n  const defaultSetup = \"tabstave notation=true tablature=false\";\n  const [curOctave, setCurOctave] = useState(4);\n  const [curStep, setCurStep] = useState(\"A\");\n  const [curAccident, setCurAccident] = useState(\"\");\n  const [key, setKey] = useState(\"G\");\n  const [time, setTime] = useState(\"4/4\");\n  const [notes, setNotes] = useState([]);\n  const rawVex = [defaultSetup + ` key=${key} time=${time}`, \"notes \" + notes.join(\" \")].join(\"\\n\");\n  const [converted, setConverted] = useState(\"\");\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\n  const VEXTAB_ACCIDENTALS = {\n    \"♯\": \"#\",\n    \"♮\": \"n\",\n    \"♭\": \"b\",\n    \"\": \"\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    style: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyItems: \"center\",\n      paddingTop: \"4rem\",\n      paddingBottom: \"4rem\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vexflow/releases/vexflow-min.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/vextab-core.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gridTemplateColumns: \"6rem 1fr 6rem\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignContent: \"center\",\n                gap: \"0.5rem\",\n                padding: \"1rem 0rem\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(o => o + 1),\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                children: curOctave\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(o => o - 1),\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"grid\",\n                gridTemplateRows: \"auto 1fr\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"grid\",\n                  gridTemplateColumns: \"repeat(7, 1fr)\",\n                  padding: \"1rem\"\n                },\n                children: NOTES.map(note => /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    ...styles.sideButton,\n                    ...(curStep === note ? styles.sideButtonActive : {})\n                  },\n                  onClick: () => setCurStep(note),\n                  children: note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  border: \"2px solid red\",\n                  padding: \"1rem\"\n                },\n                children: /*#__PURE__*/_jsxDEV(VexTabBlock, {\n                  source: rawVex\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignContent: \"center\",\n                gap: \"0.5rem\",\n                padding: \"1rem 0rem\"\n              },\n              children: ACCIDENTALS.map(acc => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  ...styles.sideButton,\n                  ...(curAccident === acc ? styles.sideButtonActive : {})\n                },\n                onClick: curAccident === acc ? () => setCurAccident(\"\") : () => setCurAccident(acc),\n                children: acc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                const note = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\n                console.log(\"Current Note: \", note);\n                setNotes(prev => [...prev, note]);\n                console.log(\"Notes after Addition: \", notes);\n                console.log(\"Raw VexTab: \", rawVex);\n                console.log(\"VexTab Text: \", document.getElementById('inputEditorRef').querySelector('textarea.editor').value);\n                console.log(\"VexTab Text HTML: \", document.getElementById(\"inputEditorRef\").innerHTML);\n              },\n              children: \"Add Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log(\"Notes before Removal: \", notes);\n                setNotes(prev => prev.slice(0, -1));\n                console.log(\"Notes after Removal: \", notes);\n              },\n              children: \"Remove Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Input Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: rawVex,\n            id: \"inputEditorRef\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Output Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vextab-auto\",\n            editor: \"true\",\n            style: {\n              minHeight: \"200px\",\n              border: \"1px solid #ccc\",\n              margin: \"1rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: converted\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"m89YG3D41fw7okYrB6cqnYOp+PM=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "<PERSON><PERSON><PERSON>", "VexTabBlock", "jsxDEV", "_jsxDEV", "Converter", "_s", "styles", "graphicDisplay", "width", "Math", "min", "window", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "sideButton", "backgroundColor", "height", "fontSize", "fontWeight", "color", "cursor", "margin", "sideButtonActive", "boxShadow", "defaultSetup", "curOctave", "setCurOctave", "curStep", "setCurStep", "curAccident", "setCurAccident", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "join", "converted", "setConverted", "NOTES", "ACCIDENTALS", "VEXTAB_ACCIDENTALS", "style", "flexDirection", "alignItems", "justifyItems", "paddingTop", "paddingBottom", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "gap", "gridTemplateColumns", "justifyContent", "align<PERSON><PERSON><PERSON>", "onClick", "o", "map", "note", "source", "acc", "console", "log", "prev", "document", "getElementById", "querySelector", "value", "innerHTML", "slice", "id", "className", "editor", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport VexTabBlock from \"./VexTabBlock.jsx\";\r\n\r\nfunction Converter() {\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"rgba(68, 25, 240, 0.08)\",\r\n      display: \"grid\",\r\n      gridTemplateRows: \"1fr auto auto\",\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"white\",\r\n    },\r\n    sideButton: {\r\n      backgroundColor: \"white\",\r\n      width: \"4rem\",\r\n      height: \"4rem\",\r\n      fontSize: \"1.2rem\",\r\n      fontWeight: \"bold\",\r\n      color: \"#5c14ba\",\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"1rem\",\r\n      cursor: \"pointer\",\r\n      margin: \"auto\",\r\n    },\r\n    sideButtonActive: {\r\n      backgroundColor: \"#9a62e3\",\r\n      color: \"white\",\r\n      border: \"2px solid #7f3bd9\",\r\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\",\r\n    },\r\n  };\r\n\r\n  // MUSIC STATES\r\n  const defaultSetup = \"tabstave notation=true tablature=false\";\r\n\r\n  const [curOctave, setCurOctave] = useState(4);\r\n  const [curStep, setCurStep] = useState(\"A\");\r\n  const [curAccident, setCurAccident] = useState(\"\");\r\n\r\n  const [key, setKey] = useState(\"G\");\r\n  const [time, setTime] = useState(\"4/4\");\r\n  const [notes, setNotes] = useState([]);\r\n\r\n  const rawVex = [\r\n    defaultSetup + ` key=${key} time=${time}`,\r\n    \"notes \" + notes.join(\" \"),\r\n  ].join(\"\\n\");\r\n\r\n  const [converted, setConverted] = useState(\"\");\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\r\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\r\n  const VEXTAB_ACCIDENTALS = { \"♯\": \"#\", \"♮\": \"n\", \"♭\": \"b\", \"\": \"\" };\r\n\r\n  return (\r\n    <main\r\n      style={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        justifyItems: \"center\",\r\n        paddingTop: \"4rem\",\r\n        paddingBottom: \"4rem\",\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vexflow/releases/vexflow-min.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/vextab-core.prod.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: \"grid\",\r\n                gridTemplateColumns: \"6rem 1fr 6rem\",\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignContent: \"center\",\r\n                  gap: \"0.5rem\",\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => setCurOctave((o) => o + 1)}\r\n                >\r\n                  ↑\r\n                </button>\r\n                <button style={styles.sideButton}>{curOctave}</button>\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => setCurOctave((o) => o - 1)}\r\n                >\r\n                  ↓\r\n                </button>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"grid\",\r\n                  gridTemplateRows: \"auto 1fr\",\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: \"grid\",\r\n                    gridTemplateColumns: \"repeat(7, 1fr)\",\r\n                    padding: \"1rem\",\r\n                  }}\r\n                >\r\n                  {NOTES.map((note) => (\r\n                    <button\r\n                      style={{\r\n                        ...styles.sideButton,\r\n                        ...(curStep === note ? styles.sideButtonActive : {}),\r\n                      }}\r\n                      onClick={() => setCurStep(note)}\r\n                    >\r\n                      {note}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n\r\n                <div style={{ border: \"2px solid red\", padding: \"1rem\" }}>\r\n                  <VexTabBlock source={rawVex} />\r\n                </div>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignContent: \"center\",\r\n                  gap: \"0.5rem\",\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                {ACCIDENTALS.map((acc) => (\r\n                  <button\r\n                    style={{\r\n                      ...styles.sideButton,\r\n                      ...(curAccident === acc ? styles.sideButtonActive : {}),\r\n                    }}\r\n                    onClick={\r\n                      curAccident === acc\r\n                        ? () => setCurAccident(\"\")\r\n                        : () => setCurAccident(acc)\r\n                    }\r\n                  >\r\n                    {acc}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            {/* ----- ADD & REMOVE NOTES ----- */}\r\n            <div>\r\n              {/* --- Add --- */}\r\n              <button\r\n                onClick={() => {\r\n                  const note = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\r\n                  console.log(\"Current Note: \", note);\r\n                  setNotes((prev) => [...prev, note]);\r\n\r\n\r\n                  console.log(\"Notes after Addition: \", notes);\r\n                  console.log(\"Raw VexTab: \", rawVex);\r\n                  console.log(\"VexTab Text: \", document.getElementById('inputEditorRef').querySelector('textarea.editor').value);\r\n                  console.log(\r\n                    \"VexTab Text HTML: \",\r\n                    document.getElementById(\"inputEditorRef\").innerHTML\r\n                  );\r\n                }}\r\n              >\r\n                Add Note\r\n              </button>\r\n              {/* --- Remove --- */}\r\n              <button\r\n                onClick={() => {\r\n                  console.log(\"Notes before Removal: \", notes);\r\n                  setNotes((prev) => prev.slice(0, -1));\r\n                  console.log(\"Notes after Removal: \", notes);\r\n                }}\r\n              >\r\n                Remove Note\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Input Editor</h4>\r\n            <VexTabBlock source={rawVex} id={\"inputEditorRef\"} />\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <h3>Output</h3>\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Output Editor</h4>\r\n            <div\r\n              className=\"vextab-auto\"\r\n              editor=\"true\"\r\n              style={{\r\n                minHeight: \"200px\",\r\n                border: \"1px solid #ccc\",\r\n                margin: \"1rem\",\r\n              }}\r\n            >\r\n              {/* Output will be populated by the processing function */}\r\n            </div>\r\n            <VexTabBlock source={converted} />\r\n          </div>\r\n        </section>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default Converter;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,WAAW,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZV,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,UAAU,EAAE;MACVC,eAAe,EAAE,OAAO;MACxBZ,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBW,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE;MAChBP,eAAe,EAAE,SAAS;MAC1BI,KAAK,EAAE,OAAO;MACdX,MAAM,EAAE,mBAAmB;MAC3Be,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,wCAAwC;EAE7D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACsC,GAAG,EAAEC,MAAM,CAAC,GAAGvC,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACwC,IAAI,EAAEC,OAAO,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM4C,MAAM,GAAG,CACbb,YAAY,GAAG,QAAQO,GAAG,SAASE,IAAI,EAAE,EACzC,QAAQ,GAAGE,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,CAC3B,CAACA,IAAI,CAAC,IAAI,CAAC;EAEZ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMgD,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,MAAMC,kBAAkB,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,EAAE,EAAE;EAAG,CAAC;EAEnE,oBACE7C,OAAA;IACE8C,KAAK,EAAE;MACLjC,OAAO,EAAE,MAAM;MACfkC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEFpD,OAAA,CAACH,MAAM;MAAAuD,QAAA,gBACLpD,OAAA;QAAQqD,GAAG,EAAC;MAAmD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACzEzD,OAAA;QAAQqD,GAAG,EAAC;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC7EzD,OAAA;QAAQqD,GAAG,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGTzD,OAAA;MAAAoD,QAAA,eACEpD,OAAA;QACE8C,KAAK,EAAE;UACLpC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfkC,aAAa,EAAE,QAAQ;UACvBxB,MAAM,EAAE,WAAW;UACnBmC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXX,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEFpD,OAAA;UAAAoD,QAAA,EAAI;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGdzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACC,cAAe;UAAAgD,QAAA,gBAChCpD,OAAA;YACE8C,KAAK,EAAE;cACLjC,OAAO,EAAE,MAAM;cACf+C,mBAAmB,EAAE;YACvB,CAAE;YAAAR,QAAA,gBAEFpD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBc,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,gBAEFpD,OAAA;gBACE8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBACzB+C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAEoC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAE;gBAAAZ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA;gBAAQ8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBAAAoC,QAAA,EAAEzB;cAAS;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACtDzD,OAAA;gBACE8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBACzB+C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAEoC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAE;gBAAAZ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENzD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAAsC,QAAA,gBAEFpD,OAAA;gBACE8C,KAAK,EAAE;kBACLjC,OAAO,EAAE,MAAM;kBACf+C,mBAAmB,EAAE,gBAAgB;kBACrCF,OAAO,EAAE;gBACX,CAAE;gBAAAN,QAAA,EAEDT,KAAK,CAACsB,GAAG,CAAEC,IAAI,iBACdlE,OAAA;kBACE8C,KAAK,EAAE;oBACL,GAAG3C,MAAM,CAACa,UAAU;oBACpB,IAAIa,OAAO,KAAKqC,IAAI,GAAG/D,MAAM,CAACqB,gBAAgB,GAAG,CAAC,CAAC;kBACrD,CAAE;kBACFuC,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAACoC,IAAI,CAAE;kBAAAd,QAAA,EAE/Bc;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzD,OAAA;gBAAK8C,KAAK,EAAE;kBAAEpC,MAAM,EAAE,eAAe;kBAAEgD,OAAO,EAAE;gBAAO,CAAE;gBAAAN,QAAA,eACvDpD,OAAA,CAACF,WAAW;kBAACqE,MAAM,EAAE5B;gBAAO;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBc,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAEDR,WAAW,CAACqB,GAAG,CAAEG,GAAG,iBACnBpE,OAAA;gBACE8C,KAAK,EAAE;kBACL,GAAG3C,MAAM,CAACa,UAAU;kBACpB,IAAIe,WAAW,KAAKqC,GAAG,GAAGjE,MAAM,CAACqB,gBAAgB,GAAG,CAAC,CAAC;gBACxD,CAAE;gBACFuC,OAAO,EACLhC,WAAW,KAAKqC,GAAG,GACf,MAAMpC,cAAc,CAAC,EAAE,CAAC,GACxB,MAAMA,cAAc,CAACoC,GAAG,CAC7B;gBAAAhB,QAAA,EAEAgB;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAAoD,QAAA,eACEpD,OAAA;cAAAoD,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGNzD,OAAA;YAAAoD,QAAA,gBAEEpD,OAAA;cACE+D,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMG,IAAI,GAAG,GAAGrC,OAAO,GAAGgB,kBAAkB,CAACd,WAAW,CAAC,IAAIJ,SAAS,EAAE;gBACxE0C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,IAAI,CAAC;gBACnC5B,QAAQ,CAAEiC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEL,IAAI,CAAC,CAAC;gBAGnCG,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjC,KAAK,CAAC;gBAC5CgC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE/B,MAAM,CAAC;gBACnC8B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEE,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC,CAACC,KAAK,CAAC;gBAC9GN,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpBE,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACG,SAC5C,CAAC;cACH,CAAE;cAAAxB,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETzD,OAAA;cACE+D,OAAO,EAAEA,CAAA,KAAM;gBACbM,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjC,KAAK,CAAC;gBAC5CC,QAAQ,CAAEiC,IAAI,IAAKA,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrCR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEjC,KAAK,CAAC;cAC7C,CAAE;cAAAe,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAINzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACY,YAAa;UAAAqC,QAAA,gBAC9BpD,OAAA;YAAAoD,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BzD,OAAA,CAACF,WAAW;YAACqE,MAAM,EAAE5B,MAAO;YAACuC,EAAE,EAAE;UAAiB;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNzD,OAAA;MAAAoD,QAAA,eACEpD,OAAA;QACE8C,KAAK,EAAE;UACLpC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfkC,aAAa,EAAE,QAAQ;UACvBxB,MAAM,EAAE,WAAW;UACnBmC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXX,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEFpD,OAAA;UAAAoD,QAAA,EAAI;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACY,YAAa;UAAAqC,QAAA,gBAC9BpD,OAAA;YAAAoD,QAAA,EAAI;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BzD,OAAA;YACE+E,SAAS,EAAC,aAAa;YACvBC,MAAM,EAAC,MAAM;YACblC,KAAK,EAAE;cACLmC,SAAS,EAAE,OAAO;cAClBvE,MAAM,EAAE,gBAAgB;cACxBa,MAAM,EAAE;YACV;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGC,CAAC,eACNzD,OAAA,CAACF,WAAW;YAACqE,MAAM,EAAE1B;UAAU;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACvD,EAAA,CA7QQD,SAAS;AAAAiF,EAAA,GAATjF,SAAS;AA+QlB,eAAeA,SAAS;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}