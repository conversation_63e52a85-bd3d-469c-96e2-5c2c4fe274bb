{"ast": null, "code": "import { Element } from './element.js';\nimport { Font, FontStyle, FontWeight } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { RenderContext } from './rendercontext.js';\nimport { Tables } from './tables.js';\nimport { normalizeAngle, prefix, RuntimeError } from './util.js';\nconst ATTRIBUTES_TO_IGNORE = {\n  path: {\n    x: true,\n    y: true,\n    width: true,\n    height: true,\n    'font-family': true,\n    'font-weight': true,\n    'font-style': true,\n    'font-size': true\n  },\n  rect: {\n    'font-family': true,\n    'font-weight': true,\n    'font-style': true,\n    'font-size': true\n  },\n  text: {\n    width: true,\n    height: true\n  }\n};\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst TWO_PI = 2 * Math.PI;\nexport class SVGContext extends RenderContext {\n  constructor(element) {\n    super();\n    this.width = 0;\n    this.height = 0;\n    this.precision = 1;\n    this.backgroundFillStyle = 'white';\n    this.fontCSSString = '';\n    this.element = element;\n    this.precision = Math.pow(10, Tables.RENDER_PRECISION_PLACES);\n    const svg = this.create('svg');\n    svg.setAttribute('pointer-events', 'none');\n    this.element.appendChild(svg);\n    this.svg = svg;\n    this.parent = this.svg;\n    this.groups = [this.svg];\n    this.path = '';\n    this.pen = {\n      x: NaN,\n      y: NaN\n    };\n    const defaultFontAttributes = {\n      'font-family': Metrics.get('fontFamily'),\n      'font-size': '10pt',\n      'font-weight': FontWeight.NORMAL,\n      'font-style': FontStyle.NORMAL\n    };\n    this.state = Object.assign({\n      scaleX: 1,\n      scaleY: 1\n    }, defaultFontAttributes);\n    this.attributes = Object.assign({\n      'stroke-width': 1.0,\n      'stroke-dasharray': 'none',\n      fill: 'black',\n      stroke: 'black',\n      shadowBlur: 0,\n      shadowColor: 'black'\n    }, defaultFontAttributes);\n    this.groupAttributes = [];\n    this.applyAttributes(svg, this.attributes);\n    this.groupAttributes.push(Object.assign({}, this.attributes));\n    this.stateStack = [];\n  }\n  round(n) {\n    return Math.round(n * this.precision) / this.precision;\n  }\n  create(svgElementType) {\n    return document.createElementNS(SVG_NS, svgElementType);\n  }\n  openGroup(cls, id) {\n    const group = this.create('g');\n    this.groups.push(group);\n    this.parent.appendChild(group);\n    this.parent = group;\n    if (cls) group.setAttribute('class', prefix(cls));\n    if (id) group.setAttribute('id', prefix(id));\n    this.applyAttributes(group, this.attributes);\n    this.groupAttributes.push(Object.assign(Object.assign({}, this.groupAttributes[this.groupAttributes.length - 1]), this.attributes));\n    return group;\n  }\n  closeGroup() {\n    this.groups.pop();\n    this.groupAttributes.pop();\n    this.parent = this.groups[this.groups.length - 1];\n  }\n  openRotation(angleDegrees, x, y) {\n    this.openGroup().setAttribute('transform', `translate(${x},${y}) rotate(${angleDegrees}) translate(-${x},-${y})`);\n  }\n  closeRotation() {\n    this.closeGroup();\n  }\n  add(elem) {\n    this.parent.appendChild(elem);\n  }\n  setFillStyle(style) {\n    this.attributes.fill = style;\n    return this;\n  }\n  setBackgroundFillStyle(style) {\n    this.backgroundFillStyle = style;\n    return this;\n  }\n  setStrokeStyle(style) {\n    this.attributes.stroke = style;\n    return this;\n  }\n  setShadowColor(color) {\n    this.attributes.shadowColor = color;\n    return this;\n  }\n  setShadowBlur(blur) {\n    this.attributes.shadowBlur = blur;\n    return this;\n  }\n  setLineWidth(width) {\n    this.attributes['stroke-width'] = width;\n    return this;\n  }\n  setLineDash(lineDash) {\n    if (Object.prototype.toString.call(lineDash) === '[object Array]') {\n      this.attributes['stroke-dasharray'] = lineDash.join(',');\n      return this;\n    } else {\n      throw new RuntimeError('ArgumentError', 'lineDash must be an array of integers.');\n    }\n  }\n  setLineCap(capType) {\n    this.attributes['stroke-linecap'] = capType;\n    return this;\n  }\n  resize(width, height) {\n    this.width = width;\n    this.height = height;\n    this.element.style.width = width.toString();\n    this.svg.style.width = width.toString();\n    this.svg.style.height = height.toString();\n    const attributes = {\n      width,\n      height\n    };\n    this.applyAttributes(this.svg, attributes);\n    this.scale(this.state.scaleX, this.state.scaleY);\n    return this;\n  }\n  scale(x, y) {\n    this.state.scaleX = this.state.scaleX ? this.state.scaleX * x : x;\n    this.state.scaleY = this.state.scaleY ? this.state.scaleY * y : y;\n    const visibleWidth = this.width / this.state.scaleX;\n    const visibleHeight = this.height / this.state.scaleY;\n    this.setViewBox(0, 0, visibleWidth, visibleHeight);\n    return this;\n  }\n  setViewBox(viewBoxOrMinX, minY, width, height) {\n    if (typeof viewBoxOrMinX === 'string') {\n      this.svg.setAttribute('viewBox', viewBoxOrMinX);\n    } else {\n      const viewBoxString = viewBoxOrMinX + ' ' + minY + ' ' + width + ' ' + height;\n      this.svg.setAttribute('viewBox', viewBoxString);\n    }\n  }\n  applyAttributes(element, attributes) {\n    const attrNamesToIgnore = ATTRIBUTES_TO_IGNORE[element.nodeName];\n    for (const attrName in attributes) {\n      if (attrNamesToIgnore && attrNamesToIgnore[attrName]) {\n        continue;\n      }\n      if (attributes[attrName] && (this.groupAttributes.length === 0 || attributes[attrName] != this.groupAttributes[this.groupAttributes.length - 1][attrName])) element.setAttributeNS(null, attrName, attributes[attrName]);\n    }\n    return element;\n  }\n  clear() {\n    while (this.svg.lastChild) {\n      this.svg.removeChild(this.svg.lastChild);\n    }\n  }\n  rect(x, y, width, height, attributes) {\n    if (height < 0) {\n      y += height;\n      height *= -1;\n    }\n    const rectangle = this.create('rect');\n    attributes = attributes !== null && attributes !== void 0 ? attributes : {\n      fill: 'none',\n      'stroke-width': this.attributes['stroke-width'],\n      stroke: 'black'\n    };\n    x = this.round(x);\n    y = this.round(y);\n    width = this.round(width);\n    height = this.round(height);\n    this.applyAttributes(rectangle, Object.assign({\n      x,\n      y,\n      width,\n      height\n    }, attributes));\n    this.add(rectangle);\n    return this;\n  }\n  fillRect(x, y, width, height) {\n    const attributes = {\n      fill: this.attributes.fill,\n      stroke: 'none'\n    };\n    this.rect(x, y, width, height, attributes);\n    return this;\n  }\n  pointerRect(x, y, width, height) {\n    const attributes = {\n      opacity: '0',\n      'pointer-events': 'auto'\n    };\n    this.rect(x, y, width, height, attributes);\n    return this;\n  }\n  clearRect(x, y, width, height) {\n    this.rect(x, y, width, height, {\n      fill: this.backgroundFillStyle,\n      stroke: 'none'\n    });\n    return this;\n  }\n  beginPath() {\n    this.path = '';\n    this.pen.x = NaN;\n    this.pen.y = NaN;\n    return this;\n  }\n  moveTo(x, y) {\n    x = this.round(x);\n    y = this.round(y);\n    this.path += 'M' + x + ' ' + y;\n    this.pen.x = x;\n    this.pen.y = y;\n    return this;\n  }\n  lineTo(x, y) {\n    x = this.round(x);\n    y = this.round(y);\n    this.path += 'L' + x + ' ' + y;\n    this.pen.x = x;\n    this.pen.y = y;\n    return this;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    x = this.round(x);\n    y = this.round(y);\n    x1 = this.round(x1);\n    y1 = this.round(y1);\n    x2 = this.round(x2);\n    y2 = this.round(y2);\n    this.path += 'C' + x1 + ' ' + y1 + ',' + x2 + ' ' + y2 + ',' + x + ' ' + y;\n    this.pen.x = x;\n    this.pen.y = y;\n    return this;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    x = this.round(x);\n    y = this.round(y);\n    x1 = this.round(x1);\n    y1 = this.round(y1);\n    this.path += 'Q' + x1 + ' ' + y1 + ',' + x + ' ' + y;\n    this.pen.x = x;\n    this.pen.y = y;\n    return this;\n  }\n  arc(x, y, radius, startAngle, endAngle, counterclockwise) {\n    let x0 = x + radius * Math.cos(startAngle);\n    let y0 = y + radius * Math.sin(startAngle);\n    x0 = this.round(x0);\n    y0 = this.round(y0);\n    const tmpStartTest = normalizeAngle(startAngle);\n    const tmpEndTest = normalizeAngle(endAngle);\n    if (!counterclockwise && endAngle - startAngle >= TWO_PI || counterclockwise && startAngle - endAngle >= TWO_PI || tmpStartTest === tmpEndTest) {\n      let x1 = x + radius * Math.cos(startAngle + Math.PI);\n      let y1 = y + radius * Math.sin(startAngle + Math.PI);\n      x1 = this.round(x1);\n      y1 = this.round(y1);\n      radius = this.round(radius);\n      this.path += `M${x0} ${y0} A${radius} ${radius} 0 0 0 ${x1} ${y1} `;\n      this.path += `A${radius} ${radius} 0 0 0 ${x0} ${y0}`;\n      this.pen.x = x0;\n      this.pen.y = y0;\n    } else {\n      let x1 = x + radius * Math.cos(endAngle);\n      let y1 = y + radius * Math.sin(endAngle);\n      startAngle = tmpStartTest;\n      endAngle = tmpEndTest;\n      let large;\n      if (Math.abs(endAngle - startAngle) < Math.PI) {\n        large = counterclockwise;\n      } else {\n        large = !counterclockwise;\n      }\n      if (startAngle > endAngle) {\n        large = !large;\n      }\n      const sweep = !counterclockwise;\n      x1 = this.round(x1);\n      y1 = this.round(y1);\n      radius = this.round(radius);\n      this.path += `M${x0} ${y0} A${radius} ${radius} 0 ${+large} ${+sweep} ${x1} ${y1}`;\n      this.pen.x = x1;\n      this.pen.y = y1;\n    }\n    return this;\n  }\n  closePath() {\n    this.path += 'Z';\n    return this;\n  }\n  getShadowStyle() {\n    return `filter: drop-shadow(0 0 ${this.attributes.shadowBlur / 1.5}px ${this.attributes.shadowColor})`;\n  }\n  fill(attributes) {\n    const path = this.create('path');\n    if (typeof attributes === 'undefined') {\n      attributes = Object.assign(Object.assign({}, this.attributes), {\n        stroke: 'none'\n      });\n    }\n    attributes.d = this.path;\n    if (this.attributes.shadowBlur > 0) {\n      attributes.style = this.getShadowStyle();\n    }\n    this.applyAttributes(path, attributes);\n    this.add(path);\n    return this;\n  }\n  stroke() {\n    const path = this.create('path');\n    const attributes = Object.assign(Object.assign({}, this.attributes), {\n      fill: 'none',\n      d: this.path\n    });\n    if (this.attributes.shadowBlur > 0) {\n      attributes.style = this.getShadowStyle();\n    }\n    this.applyAttributes(path, attributes);\n    this.add(path);\n    return this;\n  }\n  measureText(text) {\n    SVGContext.measureTextElement.setText(text);\n    SVGContext.measureTextElement.setFont(this.attributes['font-family'], this.attributes['font-size'], this.attributes['font-weight'], this.attributes['font-style']);\n    const bb = SVGContext.measureTextElement.getBoundingBox();\n    return {\n      x: bb.x,\n      y: bb.y,\n      width: bb.w,\n      height: bb.h\n    };\n  }\n  fillText(text, x, y) {\n    if (!text || text.length <= 0) {\n      return this;\n    }\n    x = this.round(x);\n    y = this.round(y);\n    const attributes = Object.assign(Object.assign({}, this.attributes), {\n      stroke: 'none',\n      x,\n      y\n    });\n    const txt = this.create('text');\n    txt.textContent = text;\n    this.applyAttributes(txt, attributes);\n    this.add(txt);\n    return this;\n  }\n  save() {\n    this.stateStack.push({\n      state: structuredClone(this.state),\n      attributes: structuredClone(this.attributes)\n    });\n    return this;\n  }\n  restore() {\n    const savedState = this.stateStack.pop();\n    if (savedState) {\n      const state = savedState;\n      this.state = structuredClone(state.state);\n      this.attributes = structuredClone(state.attributes);\n    }\n    return this;\n  }\n  set fillStyle(style) {\n    this.setFillStyle(style);\n  }\n  get fillStyle() {\n    return this.attributes.fill;\n  }\n  set strokeStyle(style) {\n    this.setStrokeStyle(style);\n  }\n  get strokeStyle() {\n    return this.attributes.stroke;\n  }\n  setFont(f, size, weight, style) {\n    const fontInfo = Font.validate(f, size, weight, style);\n    this.fontCSSString = Font.toCSSString(fontInfo);\n    const fontAttributes = {\n      'font-family': fontInfo.family,\n      'font-size': fontInfo.size,\n      'font-weight': fontInfo.weight,\n      'font-style': fontInfo.style\n    };\n    this.attributes = Object.assign(Object.assign({}, this.attributes), fontAttributes);\n    this.state = Object.assign(Object.assign({}, this.state), fontAttributes);\n    return this;\n  }\n  getFont() {\n    return this.fontCSSString;\n  }\n}\nSVGContext.measureTextElement = new Element();", "map": {"version": 3, "names": ["Element", "Font", "FontStyle", "FontWeight", "Metrics", "RenderContext", "Tables", "normalizeAngle", "prefix", "RuntimeError", "ATTRIBUTES_TO_IGNORE", "path", "x", "y", "width", "height", "rect", "text", "SVG_NS", "TWO_PI", "Math", "PI", "SVGContext", "constructor", "element", "precision", "backgroundFillStyle", "fontCSSString", "pow", "RENDER_PRECISION_PLACES", "svg", "create", "setAttribute", "append<PERSON><PERSON><PERSON>", "parent", "groups", "pen", "NaN", "defaultFontAttributes", "get", "NORMAL", "state", "Object", "assign", "scaleX", "scaleY", "attributes", "fill", "stroke", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "groupAttributes", "applyAttributes", "push", "stateStack", "round", "n", "svgElementType", "document", "createElementNS", "openGroup", "cls", "id", "group", "length", "closeGroup", "pop", "openRotation", "angleDegrees", "closeRotation", "add", "elem", "setFillStyle", "style", "setBackgroundFillStyle", "setStrokeStyle", "setShadowColor", "color", "setShadowBlur", "blur", "setLineWidth", "setLineDash", "lineDash", "prototype", "toString", "call", "join", "setLineCap", "capType", "resize", "scale", "visibleWidth", "visibleHeight", "setViewBox", "viewBoxOrMinX", "minY", "viewBoxString", "attrNamesToIgnore", "nodeName", "attrName", "setAttributeNS", "clear", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rectangle", "fillRect", "pointerRect", "opacity", "clearRect", "beginPath", "moveTo", "lineTo", "bezierCurveTo", "x1", "y1", "x2", "y2", "quadraticCurveTo", "arc", "radius", "startAngle", "endAngle", "counterclockwise", "x0", "cos", "y0", "sin", "tmpStartTest", "tmpEndTest", "large", "abs", "sweep", "closePath", "getShadowStyle", "d", "measureText", "measureTextElement", "setText", "setFont", "bb", "getBoundingBox", "w", "h", "fillText", "txt", "textContent", "save", "structuredClone", "restore", "savedState", "fillStyle", "strokeStyle", "f", "size", "weight", "fontInfo", "validate", "toCSSString", "fontAttributes", "family", "getFont"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/svgcontext.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Font, FontStyle, FontWeight } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { RenderContext } from './rendercontext.js';\nimport { Tables } from './tables.js';\nimport { normalizeAngle, prefix, RuntimeError } from './util.js';\nconst ATTRIBUTES_TO_IGNORE = {\n    path: {\n        x: true,\n        y: true,\n        width: true,\n        height: true,\n        'font-family': true,\n        'font-weight': true,\n        'font-style': true,\n        'font-size': true,\n    },\n    rect: {\n        'font-family': true,\n        'font-weight': true,\n        'font-style': true,\n        'font-size': true,\n    },\n    text: {\n        width: true,\n        height: true,\n    },\n};\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst TWO_PI = 2 * Math.PI;\nexport class SVGContext extends RenderContext {\n    constructor(element) {\n        super();\n        this.width = 0;\n        this.height = 0;\n        this.precision = 1;\n        this.backgroundFillStyle = 'white';\n        this.fontCSSString = '';\n        this.element = element;\n        this.precision = Math.pow(10, Tables.RENDER_PRECISION_PLACES);\n        const svg = this.create('svg');\n        svg.setAttribute('pointer-events', 'none');\n        this.element.appendChild(svg);\n        this.svg = svg;\n        this.parent = this.svg;\n        this.groups = [this.svg];\n        this.path = '';\n        this.pen = { x: NaN, y: NaN };\n        const defaultFontAttributes = {\n            'font-family': Metrics.get('fontFamily'),\n            'font-size': '10pt',\n            'font-weight': FontWeight.NORMAL,\n            'font-style': FontStyle.NORMAL,\n        };\n        this.state = Object.assign({ scaleX: 1, scaleY: 1 }, defaultFontAttributes);\n        this.attributes = Object.assign({ 'stroke-width': 1.0, 'stroke-dasharray': 'none', fill: 'black', stroke: 'black', shadowBlur: 0, shadowColor: 'black' }, defaultFontAttributes);\n        this.groupAttributes = [];\n        this.applyAttributes(svg, this.attributes);\n        this.groupAttributes.push(Object.assign({}, this.attributes));\n        this.stateStack = [];\n    }\n    round(n) {\n        return Math.round(n * this.precision) / this.precision;\n    }\n    create(svgElementType) {\n        return document.createElementNS(SVG_NS, svgElementType);\n    }\n    openGroup(cls, id) {\n        const group = this.create('g');\n        this.groups.push(group);\n        this.parent.appendChild(group);\n        this.parent = group;\n        if (cls)\n            group.setAttribute('class', prefix(cls));\n        if (id)\n            group.setAttribute('id', prefix(id));\n        this.applyAttributes(group, this.attributes);\n        this.groupAttributes.push(Object.assign(Object.assign({}, this.groupAttributes[this.groupAttributes.length - 1]), this.attributes));\n        return group;\n    }\n    closeGroup() {\n        this.groups.pop();\n        this.groupAttributes.pop();\n        this.parent = this.groups[this.groups.length - 1];\n    }\n    openRotation(angleDegrees, x, y) {\n        this.openGroup().setAttribute('transform', `translate(${x},${y}) rotate(${angleDegrees}) translate(-${x},-${y})`);\n    }\n    closeRotation() {\n        this.closeGroup();\n    }\n    add(elem) {\n        this.parent.appendChild(elem);\n    }\n    setFillStyle(style) {\n        this.attributes.fill = style;\n        return this;\n    }\n    setBackgroundFillStyle(style) {\n        this.backgroundFillStyle = style;\n        return this;\n    }\n    setStrokeStyle(style) {\n        this.attributes.stroke = style;\n        return this;\n    }\n    setShadowColor(color) {\n        this.attributes.shadowColor = color;\n        return this;\n    }\n    setShadowBlur(blur) {\n        this.attributes.shadowBlur = blur;\n        return this;\n    }\n    setLineWidth(width) {\n        this.attributes['stroke-width'] = width;\n        return this;\n    }\n    setLineDash(lineDash) {\n        if (Object.prototype.toString.call(lineDash) === '[object Array]') {\n            this.attributes['stroke-dasharray'] = lineDash.join(',');\n            return this;\n        }\n        else {\n            throw new RuntimeError('ArgumentError', 'lineDash must be an array of integers.');\n        }\n    }\n    setLineCap(capType) {\n        this.attributes['stroke-linecap'] = capType;\n        return this;\n    }\n    resize(width, height) {\n        this.width = width;\n        this.height = height;\n        this.element.style.width = width.toString();\n        this.svg.style.width = width.toString();\n        this.svg.style.height = height.toString();\n        const attributes = {\n            width,\n            height,\n        };\n        this.applyAttributes(this.svg, attributes);\n        this.scale(this.state.scaleX, this.state.scaleY);\n        return this;\n    }\n    scale(x, y) {\n        this.state.scaleX = this.state.scaleX ? this.state.scaleX * x : x;\n        this.state.scaleY = this.state.scaleY ? this.state.scaleY * y : y;\n        const visibleWidth = this.width / this.state.scaleX;\n        const visibleHeight = this.height / this.state.scaleY;\n        this.setViewBox(0, 0, visibleWidth, visibleHeight);\n        return this;\n    }\n    setViewBox(viewBoxOrMinX, minY, width, height) {\n        if (typeof viewBoxOrMinX === 'string') {\n            this.svg.setAttribute('viewBox', viewBoxOrMinX);\n        }\n        else {\n            const viewBoxString = viewBoxOrMinX + ' ' + minY + ' ' + width + ' ' + height;\n            this.svg.setAttribute('viewBox', viewBoxString);\n        }\n    }\n    applyAttributes(element, attributes) {\n        const attrNamesToIgnore = ATTRIBUTES_TO_IGNORE[element.nodeName];\n        for (const attrName in attributes) {\n            if (attrNamesToIgnore && attrNamesToIgnore[attrName]) {\n                continue;\n            }\n            if (attributes[attrName] &&\n                (this.groupAttributes.length === 0 ||\n                    attributes[attrName] != this.groupAttributes[this.groupAttributes.length - 1][attrName]))\n                element.setAttributeNS(null, attrName, attributes[attrName]);\n        }\n        return element;\n    }\n    clear() {\n        while (this.svg.lastChild) {\n            this.svg.removeChild(this.svg.lastChild);\n        }\n    }\n    rect(x, y, width, height, attributes) {\n        if (height < 0) {\n            y += height;\n            height *= -1;\n        }\n        const rectangle = this.create('rect');\n        attributes = attributes !== null && attributes !== void 0 ? attributes : { fill: 'none', 'stroke-width': this.attributes['stroke-width'], stroke: 'black' };\n        x = this.round(x);\n        y = this.round(y);\n        width = this.round(width);\n        height = this.round(height);\n        this.applyAttributes(rectangle, Object.assign({ x, y, width, height }, attributes));\n        this.add(rectangle);\n        return this;\n    }\n    fillRect(x, y, width, height) {\n        const attributes = { fill: this.attributes.fill, stroke: 'none' };\n        this.rect(x, y, width, height, attributes);\n        return this;\n    }\n    pointerRect(x, y, width, height) {\n        const attributes = { opacity: '0', 'pointer-events': 'auto' };\n        this.rect(x, y, width, height, attributes);\n        return this;\n    }\n    clearRect(x, y, width, height) {\n        this.rect(x, y, width, height, { fill: this.backgroundFillStyle, stroke: 'none' });\n        return this;\n    }\n    beginPath() {\n        this.path = '';\n        this.pen.x = NaN;\n        this.pen.y = NaN;\n        return this;\n    }\n    moveTo(x, y) {\n        x = this.round(x);\n        y = this.round(y);\n        this.path += 'M' + x + ' ' + y;\n        this.pen.x = x;\n        this.pen.y = y;\n        return this;\n    }\n    lineTo(x, y) {\n        x = this.round(x);\n        y = this.round(y);\n        this.path += 'L' + x + ' ' + y;\n        this.pen.x = x;\n        this.pen.y = y;\n        return this;\n    }\n    bezierCurveTo(x1, y1, x2, y2, x, y) {\n        x = this.round(x);\n        y = this.round(y);\n        x1 = this.round(x1);\n        y1 = this.round(y1);\n        x2 = this.round(x2);\n        y2 = this.round(y2);\n        this.path += 'C' + x1 + ' ' + y1 + ',' + x2 + ' ' + y2 + ',' + x + ' ' + y;\n        this.pen.x = x;\n        this.pen.y = y;\n        return this;\n    }\n    quadraticCurveTo(x1, y1, x, y) {\n        x = this.round(x);\n        y = this.round(y);\n        x1 = this.round(x1);\n        y1 = this.round(y1);\n        this.path += 'Q' + x1 + ' ' + y1 + ',' + x + ' ' + y;\n        this.pen.x = x;\n        this.pen.y = y;\n        return this;\n    }\n    arc(x, y, radius, startAngle, endAngle, counterclockwise) {\n        let x0 = x + radius * Math.cos(startAngle);\n        let y0 = y + radius * Math.sin(startAngle);\n        x0 = this.round(x0);\n        y0 = this.round(y0);\n        const tmpStartTest = normalizeAngle(startAngle);\n        const tmpEndTest = normalizeAngle(endAngle);\n        if ((!counterclockwise && endAngle - startAngle >= TWO_PI) ||\n            (counterclockwise && startAngle - endAngle >= TWO_PI) ||\n            tmpStartTest === tmpEndTest) {\n            let x1 = x + radius * Math.cos(startAngle + Math.PI);\n            let y1 = y + radius * Math.sin(startAngle + Math.PI);\n            x1 = this.round(x1);\n            y1 = this.round(y1);\n            radius = this.round(radius);\n            this.path += `M${x0} ${y0} A${radius} ${radius} 0 0 0 ${x1} ${y1} `;\n            this.path += `A${radius} ${radius} 0 0 0 ${x0} ${y0}`;\n            this.pen.x = x0;\n            this.pen.y = y0;\n        }\n        else {\n            let x1 = x + radius * Math.cos(endAngle);\n            let y1 = y + radius * Math.sin(endAngle);\n            startAngle = tmpStartTest;\n            endAngle = tmpEndTest;\n            let large;\n            if (Math.abs(endAngle - startAngle) < Math.PI) {\n                large = counterclockwise;\n            }\n            else {\n                large = !counterclockwise;\n            }\n            if (startAngle > endAngle) {\n                large = !large;\n            }\n            const sweep = !counterclockwise;\n            x1 = this.round(x1);\n            y1 = this.round(y1);\n            radius = this.round(radius);\n            this.path += `M${x0} ${y0} A${radius} ${radius} 0 ${+large} ${+sweep} ${x1} ${y1}`;\n            this.pen.x = x1;\n            this.pen.y = y1;\n        }\n        return this;\n    }\n    closePath() {\n        this.path += 'Z';\n        return this;\n    }\n    getShadowStyle() {\n        return `filter: drop-shadow(0 0 ${this.attributes.shadowBlur / 1.5}px ${this.attributes.shadowColor})`;\n    }\n    fill(attributes) {\n        const path = this.create('path');\n        if (typeof attributes === 'undefined') {\n            attributes = Object.assign(Object.assign({}, this.attributes), { stroke: 'none' });\n        }\n        attributes.d = this.path;\n        if (this.attributes.shadowBlur > 0) {\n            attributes.style = this.getShadowStyle();\n        }\n        this.applyAttributes(path, attributes);\n        this.add(path);\n        return this;\n    }\n    stroke() {\n        const path = this.create('path');\n        const attributes = Object.assign(Object.assign({}, this.attributes), { fill: 'none', d: this.path });\n        if (this.attributes.shadowBlur > 0) {\n            attributes.style = this.getShadowStyle();\n        }\n        this.applyAttributes(path, attributes);\n        this.add(path);\n        return this;\n    }\n    measureText(text) {\n        SVGContext.measureTextElement.setText(text);\n        SVGContext.measureTextElement.setFont(this.attributes['font-family'], this.attributes['font-size'], this.attributes['font-weight'], this.attributes['font-style']);\n        const bb = SVGContext.measureTextElement.getBoundingBox();\n        return { x: bb.x, y: bb.y, width: bb.w, height: bb.h };\n    }\n    fillText(text, x, y) {\n        if (!text || text.length <= 0) {\n            return this;\n        }\n        x = this.round(x);\n        y = this.round(y);\n        const attributes = Object.assign(Object.assign({}, this.attributes), { stroke: 'none', x,\n            y });\n        const txt = this.create('text');\n        txt.textContent = text;\n        this.applyAttributes(txt, attributes);\n        this.add(txt);\n        return this;\n    }\n    save() {\n        this.stateStack.push({\n            state: structuredClone(this.state),\n            attributes: structuredClone(this.attributes),\n        });\n        return this;\n    }\n    restore() {\n        const savedState = this.stateStack.pop();\n        if (savedState) {\n            const state = savedState;\n            this.state = structuredClone(state.state);\n            this.attributes = structuredClone(state.attributes);\n        }\n        return this;\n    }\n    set fillStyle(style) {\n        this.setFillStyle(style);\n    }\n    get fillStyle() {\n        return this.attributes.fill;\n    }\n    set strokeStyle(style) {\n        this.setStrokeStyle(style);\n    }\n    get strokeStyle() {\n        return this.attributes.stroke;\n    }\n    setFont(f, size, weight, style) {\n        const fontInfo = Font.validate(f, size, weight, style);\n        this.fontCSSString = Font.toCSSString(fontInfo);\n        const fontAttributes = {\n            'font-family': fontInfo.family,\n            'font-size': fontInfo.size,\n            'font-weight': fontInfo.weight,\n            'font-style': fontInfo.style,\n        };\n        this.attributes = Object.assign(Object.assign({}, this.attributes), fontAttributes);\n        this.state = Object.assign(Object.assign({}, this.state), fontAttributes);\n        return this;\n    }\n    getFont() {\n        return this.fontCSSString;\n    }\n}\nSVGContext.measureTextElement = new Element();\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,EAAEC,SAAS,EAAEC,UAAU,QAAQ,WAAW;AACvD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,QAAQ,WAAW;AAChE,MAAMC,oBAAoB,GAAG;EACzBC,IAAI,EAAE;IACFC,CAAC,EAAE,IAAI;IACPC,CAAC,EAAE,IAAI;IACPC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZ,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE;EACjB,CAAC;EACDC,IAAI,EAAE;IACF,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE;EACjB,CAAC;EACDC,IAAI,EAAE;IACFH,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE;EACZ;AACJ,CAAC;AACD,MAAMG,MAAM,GAAG,4BAA4B;AAC3C,MAAMC,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE;AAC1B,OAAO,MAAMC,UAAU,SAASjB,aAAa,CAAC;EAC1CkB,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACV,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACU,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,mBAAmB,GAAG,OAAO;IAClC,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGL,IAAI,CAACQ,GAAG,CAAC,EAAE,EAAEtB,MAAM,CAACuB,uBAAuB,CAAC;IAC7D,MAAMC,GAAG,GAAG,IAAI,CAACC,MAAM,CAAC,KAAK,CAAC;IAC9BD,GAAG,CAACE,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC1C,IAAI,CAACR,OAAO,CAACS,WAAW,CAACH,GAAG,CAAC;IAC7B,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACI,MAAM,GAAG,IAAI,CAACJ,GAAG;IACtB,IAAI,CAACK,MAAM,GAAG,CAAC,IAAI,CAACL,GAAG,CAAC;IACxB,IAAI,CAACnB,IAAI,GAAG,EAAE;IACd,IAAI,CAACyB,GAAG,GAAG;MAAExB,CAAC,EAAEyB,GAAG;MAAExB,CAAC,EAAEwB;IAAI,CAAC;IAC7B,MAAMC,qBAAqB,GAAG;MAC1B,aAAa,EAAElC,OAAO,CAACmC,GAAG,CAAC,YAAY,CAAC;MACxC,WAAW,EAAE,MAAM;MACnB,aAAa,EAAEpC,UAAU,CAACqC,MAAM;MAChC,YAAY,EAAEtC,SAAS,CAACsC;IAC5B,CAAC;IACD,IAAI,CAACC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,EAAEP,qBAAqB,CAAC;IAC3E,IAAI,CAACQ,UAAU,GAAGJ,MAAM,CAACC,MAAM,CAAC;MAAE,cAAc,EAAE,GAAG;MAAE,kBAAkB,EAAE,MAAM;MAAEI,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAE,OAAO;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAQ,CAAC,EAAEZ,qBAAqB,CAAC;IAChL,IAAI,CAACa,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,CAACtB,GAAG,EAAE,IAAI,CAACgB,UAAU,CAAC;IAC1C,IAAI,CAACK,eAAe,CAACE,IAAI,CAACX,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,CAAC;IAC7D,IAAI,CAACQ,UAAU,GAAG,EAAE;EACxB;EACAC,KAAKA,CAACC,CAAC,EAAE;IACL,OAAOpC,IAAI,CAACmC,KAAK,CAACC,CAAC,GAAG,IAAI,CAAC/B,SAAS,CAAC,GAAG,IAAI,CAACA,SAAS;EAC1D;EACAM,MAAMA,CAAC0B,cAAc,EAAE;IACnB,OAAOC,QAAQ,CAACC,eAAe,CAACzC,MAAM,EAAEuC,cAAc,CAAC;EAC3D;EACAG,SAASA,CAACC,GAAG,EAAEC,EAAE,EAAE;IACf,MAAMC,KAAK,GAAG,IAAI,CAAChC,MAAM,CAAC,GAAG,CAAC;IAC9B,IAAI,CAACI,MAAM,CAACkB,IAAI,CAACU,KAAK,CAAC;IACvB,IAAI,CAAC7B,MAAM,CAACD,WAAW,CAAC8B,KAAK,CAAC;IAC9B,IAAI,CAAC7B,MAAM,GAAG6B,KAAK;IACnB,IAAIF,GAAG,EACHE,KAAK,CAAC/B,YAAY,CAAC,OAAO,EAAExB,MAAM,CAACqD,GAAG,CAAC,CAAC;IAC5C,IAAIC,EAAE,EACFC,KAAK,CAAC/B,YAAY,CAAC,IAAI,EAAExB,MAAM,CAACsD,EAAE,CAAC,CAAC;IACxC,IAAI,CAACV,eAAe,CAACW,KAAK,EAAE,IAAI,CAACjB,UAAU,CAAC;IAC5C,IAAI,CAACK,eAAe,CAACE,IAAI,CAACX,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACQ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACa,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAClB,UAAU,CAAC,CAAC;IACnI,OAAOiB,KAAK;EAChB;EACAE,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC9B,MAAM,CAAC+B,GAAG,CAAC,CAAC;IACjB,IAAI,CAACf,eAAe,CAACe,GAAG,CAAC,CAAC;IAC1B,IAAI,CAAChC,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC6B,MAAM,GAAG,CAAC,CAAC;EACrD;EACAG,YAAYA,CAACC,YAAY,EAAExD,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAI,CAAC+C,SAAS,CAAC,CAAC,CAAC5B,YAAY,CAAC,WAAW,EAAE,aAAapB,CAAC,IAAIC,CAAC,YAAYuD,YAAY,gBAAgBxD,CAAC,KAAKC,CAAC,GAAG,CAAC;EACrH;EACAwD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,UAAU,CAAC,CAAC;EACrB;EACAK,GAAGA,CAACC,IAAI,EAAE;IACN,IAAI,CAACrC,MAAM,CAACD,WAAW,CAACsC,IAAI,CAAC;EACjC;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAAC3B,UAAU,CAACC,IAAI,GAAG0B,KAAK;IAC5B,OAAO,IAAI;EACf;EACAC,sBAAsBA,CAACD,KAAK,EAAE;IAC1B,IAAI,CAAC/C,mBAAmB,GAAG+C,KAAK;IAChC,OAAO,IAAI;EACf;EACAE,cAAcA,CAACF,KAAK,EAAE;IAClB,IAAI,CAAC3B,UAAU,CAACE,MAAM,GAAGyB,KAAK;IAC9B,OAAO,IAAI;EACf;EACAG,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAAC/B,UAAU,CAACI,WAAW,GAAG2B,KAAK;IACnC,OAAO,IAAI;EACf;EACAC,aAAaA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACjC,UAAU,CAACG,UAAU,GAAG8B,IAAI;IACjC,OAAO,IAAI;EACf;EACAC,YAAYA,CAAClE,KAAK,EAAE;IAChB,IAAI,CAACgC,UAAU,CAAC,cAAc,CAAC,GAAGhC,KAAK;IACvC,OAAO,IAAI;EACf;EACAmE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAIxC,MAAM,CAACyC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,QAAQ,CAAC,KAAK,gBAAgB,EAAE;MAC/D,IAAI,CAACpC,UAAU,CAAC,kBAAkB,CAAC,GAAGoC,QAAQ,CAACI,IAAI,CAAC,GAAG,CAAC;MACxD,OAAO,IAAI;IACf,CAAC,MACI;MACD,MAAM,IAAI7E,YAAY,CAAC,eAAe,EAAE,wCAAwC,CAAC;IACrF;EACJ;EACA8E,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAAC1C,UAAU,CAAC,gBAAgB,CAAC,GAAG0C,OAAO;IAC3C,OAAO,IAAI;EACf;EACAC,MAAMA,CAAC3E,KAAK,EAAEC,MAAM,EAAE;IAClB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACS,OAAO,CAACiD,KAAK,CAAC3D,KAAK,GAAGA,KAAK,CAACsE,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACtD,GAAG,CAAC2C,KAAK,CAAC3D,KAAK,GAAGA,KAAK,CAACsE,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACtD,GAAG,CAAC2C,KAAK,CAAC1D,MAAM,GAAGA,MAAM,CAACqE,QAAQ,CAAC,CAAC;IACzC,MAAMtC,UAAU,GAAG;MACfhC,KAAK;MACLC;IACJ,CAAC;IACD,IAAI,CAACqC,eAAe,CAAC,IAAI,CAACtB,GAAG,EAAEgB,UAAU,CAAC;IAC1C,IAAI,CAAC4C,KAAK,CAAC,IAAI,CAACjD,KAAK,CAACG,MAAM,EAAE,IAAI,CAACH,KAAK,CAACI,MAAM,CAAC;IAChD,OAAO,IAAI;EACf;EACA6C,KAAKA,CAAC9E,CAAC,EAAEC,CAAC,EAAE;IACR,IAAI,CAAC4B,KAAK,CAACG,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM,GAAGhC,CAAC,GAAGA,CAAC;IACjE,IAAI,CAAC6B,KAAK,CAACI,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACI,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACI,MAAM,GAAGhC,CAAC,GAAGA,CAAC;IACjE,MAAM8E,YAAY,GAAG,IAAI,CAAC7E,KAAK,GAAG,IAAI,CAAC2B,KAAK,CAACG,MAAM;IACnD,MAAMgD,aAAa,GAAG,IAAI,CAAC7E,MAAM,GAAG,IAAI,CAAC0B,KAAK,CAACI,MAAM;IACrD,IAAI,CAACgD,UAAU,CAAC,CAAC,EAAE,CAAC,EAAEF,YAAY,EAAEC,aAAa,CAAC;IAClD,OAAO,IAAI;EACf;EACAC,UAAUA,CAACC,aAAa,EAAEC,IAAI,EAAEjF,KAAK,EAAEC,MAAM,EAAE;IAC3C,IAAI,OAAO+E,aAAa,KAAK,QAAQ,EAAE;MACnC,IAAI,CAAChE,GAAG,CAACE,YAAY,CAAC,SAAS,EAAE8D,aAAa,CAAC;IACnD,CAAC,MACI;MACD,MAAME,aAAa,GAAGF,aAAa,GAAG,GAAG,GAAGC,IAAI,GAAG,GAAG,GAAGjF,KAAK,GAAG,GAAG,GAAGC,MAAM;MAC7E,IAAI,CAACe,GAAG,CAACE,YAAY,CAAC,SAAS,EAAEgE,aAAa,CAAC;IACnD;EACJ;EACA5C,eAAeA,CAAC5B,OAAO,EAAEsB,UAAU,EAAE;IACjC,MAAMmD,iBAAiB,GAAGvF,oBAAoB,CAACc,OAAO,CAAC0E,QAAQ,CAAC;IAChE,KAAK,MAAMC,QAAQ,IAAIrD,UAAU,EAAE;MAC/B,IAAImD,iBAAiB,IAAIA,iBAAiB,CAACE,QAAQ,CAAC,EAAE;QAClD;MACJ;MACA,IAAIrD,UAAU,CAACqD,QAAQ,CAAC,KACnB,IAAI,CAAChD,eAAe,CAACa,MAAM,KAAK,CAAC,IAC9BlB,UAAU,CAACqD,QAAQ,CAAC,IAAI,IAAI,CAAChD,eAAe,CAAC,IAAI,CAACA,eAAe,CAACa,MAAM,GAAG,CAAC,CAAC,CAACmC,QAAQ,CAAC,CAAC,EAC5F3E,OAAO,CAAC4E,cAAc,CAAC,IAAI,EAAED,QAAQ,EAAErD,UAAU,CAACqD,QAAQ,CAAC,CAAC;IACpE;IACA,OAAO3E,OAAO;EAClB;EACA6E,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACvE,GAAG,CAACwE,SAAS,EAAE;MACvB,IAAI,CAACxE,GAAG,CAACyE,WAAW,CAAC,IAAI,CAACzE,GAAG,CAACwE,SAAS,CAAC;IAC5C;EACJ;EACAtF,IAAIA,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE+B,UAAU,EAAE;IAClC,IAAI/B,MAAM,GAAG,CAAC,EAAE;MACZF,CAAC,IAAIE,MAAM;MACXA,MAAM,IAAI,CAAC,CAAC;IAChB;IACA,MAAMyF,SAAS,GAAG,IAAI,CAACzE,MAAM,CAAC,MAAM,CAAC;IACrCe,UAAU,GAAGA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAE,cAAc,EAAE,IAAI,CAACD,UAAU,CAAC,cAAc,CAAC;MAAEE,MAAM,EAAE;IAAQ,CAAC;IAC3JpC,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjBC,KAAK,GAAG,IAAI,CAACyC,KAAK,CAACzC,KAAK,CAAC;IACzBC,MAAM,GAAG,IAAI,CAACwC,KAAK,CAACxC,MAAM,CAAC;IAC3B,IAAI,CAACqC,eAAe,CAACoD,SAAS,EAAE9D,MAAM,CAACC,MAAM,CAAC;MAAE/B,CAAC;MAAEC,CAAC;MAAEC,KAAK;MAAEC;IAAO,CAAC,EAAE+B,UAAU,CAAC,CAAC;IACnF,IAAI,CAACwB,GAAG,CAACkC,SAAS,CAAC;IACnB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAC7F,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC1B,MAAM+B,UAAU,GAAG;MAAEC,IAAI,EAAE,IAAI,CAACD,UAAU,CAACC,IAAI;MAAEC,MAAM,EAAE;IAAO,CAAC;IACjE,IAAI,CAAChC,IAAI,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE+B,UAAU,CAAC;IAC1C,OAAO,IAAI;EACf;EACA4D,WAAWA,CAAC9F,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC7B,MAAM+B,UAAU,GAAG;MAAE6D,OAAO,EAAE,GAAG;MAAE,gBAAgB,EAAE;IAAO,CAAC;IAC7D,IAAI,CAAC3F,IAAI,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE+B,UAAU,CAAC;IAC1C,OAAO,IAAI;EACf;EACA8D,SAASA,CAAChG,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC3B,IAAI,CAACC,IAAI,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAE;MAAEgC,IAAI,EAAE,IAAI,CAACrB,mBAAmB;MAAEsB,MAAM,EAAE;IAAO,CAAC,CAAC;IAClF,OAAO,IAAI;EACf;EACA6D,SAASA,CAAA,EAAG;IACR,IAAI,CAAClG,IAAI,GAAG,EAAE;IACd,IAAI,CAACyB,GAAG,CAACxB,CAAC,GAAGyB,GAAG;IAChB,IAAI,CAACD,GAAG,CAACvB,CAAC,GAAGwB,GAAG;IAChB,OAAO,IAAI;EACf;EACAyE,MAAMA,CAAClG,CAAC,EAAEC,CAAC,EAAE;IACTD,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjB,IAAI,CAACF,IAAI,IAAI,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGC,CAAC;IAC9B,IAAI,CAACuB,GAAG,CAACxB,CAAC,GAAGA,CAAC;IACd,IAAI,CAACwB,GAAG,CAACvB,CAAC,GAAGA,CAAC;IACd,OAAO,IAAI;EACf;EACAkG,MAAMA,CAACnG,CAAC,EAAEC,CAAC,EAAE;IACTD,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjB,IAAI,CAACF,IAAI,IAAI,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGC,CAAC;IAC9B,IAAI,CAACuB,GAAG,CAACxB,CAAC,GAAGA,CAAC;IACd,IAAI,CAACwB,GAAG,CAACvB,CAAC,GAAGA,CAAC;IACd,OAAO,IAAI;EACf;EACAmG,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAExG,CAAC,EAAEC,CAAC,EAAE;IAChCD,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjBoG,EAAE,GAAG,IAAI,CAAC1D,KAAK,CAAC0D,EAAE,CAAC;IACnBC,EAAE,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,EAAE,CAAC;IACnBC,EAAE,GAAG,IAAI,CAAC5D,KAAK,CAAC4D,EAAE,CAAC;IACnBC,EAAE,GAAG,IAAI,CAAC7D,KAAK,CAAC6D,EAAE,CAAC;IACnB,IAAI,CAACzG,IAAI,IAAI,GAAG,GAAGsG,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,GAAGxG,CAAC,GAAG,GAAG,GAAGC,CAAC;IAC1E,IAAI,CAACuB,GAAG,CAACxB,CAAC,GAAGA,CAAC;IACd,IAAI,CAACwB,GAAG,CAACvB,CAAC,GAAGA,CAAC;IACd,OAAO,IAAI;EACf;EACAwG,gBAAgBA,CAACJ,EAAE,EAAEC,EAAE,EAAEtG,CAAC,EAAEC,CAAC,EAAE;IAC3BD,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjBoG,EAAE,GAAG,IAAI,CAAC1D,KAAK,CAAC0D,EAAE,CAAC;IACnBC,EAAE,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,EAAE,CAAC;IACnB,IAAI,CAACvG,IAAI,IAAI,GAAG,GAAGsG,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,GAAGtG,CAAC,GAAG,GAAG,GAAGC,CAAC;IACpD,IAAI,CAACuB,GAAG,CAACxB,CAAC,GAAGA,CAAC;IACd,IAAI,CAACwB,GAAG,CAACvB,CAAC,GAAGA,CAAC;IACd,OAAO,IAAI;EACf;EACAyG,GAAGA,CAAC1G,CAAC,EAAEC,CAAC,EAAE0G,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,gBAAgB,EAAE;IACtD,IAAIC,EAAE,GAAG/G,CAAC,GAAG2G,MAAM,GAAGnG,IAAI,CAACwG,GAAG,CAACJ,UAAU,CAAC;IAC1C,IAAIK,EAAE,GAAGhH,CAAC,GAAG0G,MAAM,GAAGnG,IAAI,CAAC0G,GAAG,CAACN,UAAU,CAAC;IAC1CG,EAAE,GAAG,IAAI,CAACpE,KAAK,CAACoE,EAAE,CAAC;IACnBE,EAAE,GAAG,IAAI,CAACtE,KAAK,CAACsE,EAAE,CAAC;IACnB,MAAME,YAAY,GAAGxH,cAAc,CAACiH,UAAU,CAAC;IAC/C,MAAMQ,UAAU,GAAGzH,cAAc,CAACkH,QAAQ,CAAC;IAC3C,IAAK,CAACC,gBAAgB,IAAID,QAAQ,GAAGD,UAAU,IAAIrG,MAAM,IACpDuG,gBAAgB,IAAIF,UAAU,GAAGC,QAAQ,IAAItG,MAAO,IACrD4G,YAAY,KAAKC,UAAU,EAAE;MAC7B,IAAIf,EAAE,GAAGrG,CAAC,GAAG2G,MAAM,GAAGnG,IAAI,CAACwG,GAAG,CAACJ,UAAU,GAAGpG,IAAI,CAACC,EAAE,CAAC;MACpD,IAAI6F,EAAE,GAAGrG,CAAC,GAAG0G,MAAM,GAAGnG,IAAI,CAAC0G,GAAG,CAACN,UAAU,GAAGpG,IAAI,CAACC,EAAE,CAAC;MACpD4F,EAAE,GAAG,IAAI,CAAC1D,KAAK,CAAC0D,EAAE,CAAC;MACnBC,EAAE,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,EAAE,CAAC;MACnBK,MAAM,GAAG,IAAI,CAAChE,KAAK,CAACgE,MAAM,CAAC;MAC3B,IAAI,CAAC5G,IAAI,IAAI,IAAIgH,EAAE,IAAIE,EAAE,KAAKN,MAAM,IAAIA,MAAM,UAAUN,EAAE,IAAIC,EAAE,GAAG;MACnE,IAAI,CAACvG,IAAI,IAAI,IAAI4G,MAAM,IAAIA,MAAM,UAAUI,EAAE,IAAIE,EAAE,EAAE;MACrD,IAAI,CAACzF,GAAG,CAACxB,CAAC,GAAG+G,EAAE;MACf,IAAI,CAACvF,GAAG,CAACvB,CAAC,GAAGgH,EAAE;IACnB,CAAC,MACI;MACD,IAAIZ,EAAE,GAAGrG,CAAC,GAAG2G,MAAM,GAAGnG,IAAI,CAACwG,GAAG,CAACH,QAAQ,CAAC;MACxC,IAAIP,EAAE,GAAGrG,CAAC,GAAG0G,MAAM,GAAGnG,IAAI,CAAC0G,GAAG,CAACL,QAAQ,CAAC;MACxCD,UAAU,GAAGO,YAAY;MACzBN,QAAQ,GAAGO,UAAU;MACrB,IAAIC,KAAK;MACT,IAAI7G,IAAI,CAAC8G,GAAG,CAACT,QAAQ,GAAGD,UAAU,CAAC,GAAGpG,IAAI,CAACC,EAAE,EAAE;QAC3C4G,KAAK,GAAGP,gBAAgB;MAC5B,CAAC,MACI;QACDO,KAAK,GAAG,CAACP,gBAAgB;MAC7B;MACA,IAAIF,UAAU,GAAGC,QAAQ,EAAE;QACvBQ,KAAK,GAAG,CAACA,KAAK;MAClB;MACA,MAAME,KAAK,GAAG,CAACT,gBAAgB;MAC/BT,EAAE,GAAG,IAAI,CAAC1D,KAAK,CAAC0D,EAAE,CAAC;MACnBC,EAAE,GAAG,IAAI,CAAC3D,KAAK,CAAC2D,EAAE,CAAC;MACnBK,MAAM,GAAG,IAAI,CAAChE,KAAK,CAACgE,MAAM,CAAC;MAC3B,IAAI,CAAC5G,IAAI,IAAI,IAAIgH,EAAE,IAAIE,EAAE,KAAKN,MAAM,IAAIA,MAAM,MAAM,CAACU,KAAK,IAAI,CAACE,KAAK,IAAIlB,EAAE,IAAIC,EAAE,EAAE;MAClF,IAAI,CAAC9E,GAAG,CAACxB,CAAC,GAAGqG,EAAE;MACf,IAAI,CAAC7E,GAAG,CAACvB,CAAC,GAAGqG,EAAE;IACnB;IACA,OAAO,IAAI;EACf;EACAkB,SAASA,CAAA,EAAG;IACR,IAAI,CAACzH,IAAI,IAAI,GAAG;IAChB,OAAO,IAAI;EACf;EACA0H,cAAcA,CAAA,EAAG;IACb,OAAO,2BAA2B,IAAI,CAACvF,UAAU,CAACG,UAAU,GAAG,GAAG,MAAM,IAAI,CAACH,UAAU,CAACI,WAAW,GAAG;EAC1G;EACAH,IAAIA,CAACD,UAAU,EAAE;IACb,MAAMnC,IAAI,GAAG,IAAI,CAACoB,MAAM,CAAC,MAAM,CAAC;IAChC,IAAI,OAAOe,UAAU,KAAK,WAAW,EAAE;MACnCA,UAAU,GAAGJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,EAAE;QAAEE,MAAM,EAAE;MAAO,CAAC,CAAC;IACtF;IACAF,UAAU,CAACwF,CAAC,GAAG,IAAI,CAAC3H,IAAI;IACxB,IAAI,IAAI,CAACmC,UAAU,CAACG,UAAU,GAAG,CAAC,EAAE;MAChCH,UAAU,CAAC2B,KAAK,GAAG,IAAI,CAAC4D,cAAc,CAAC,CAAC;IAC5C;IACA,IAAI,CAACjF,eAAe,CAACzC,IAAI,EAAEmC,UAAU,CAAC;IACtC,IAAI,CAACwB,GAAG,CAAC3D,IAAI,CAAC;IACd,OAAO,IAAI;EACf;EACAqC,MAAMA,CAAA,EAAG;IACL,MAAMrC,IAAI,GAAG,IAAI,CAACoB,MAAM,CAAC,MAAM,CAAC;IAChC,MAAMe,UAAU,GAAGJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,EAAE;MAAEC,IAAI,EAAE,MAAM;MAAEuF,CAAC,EAAE,IAAI,CAAC3H;IAAK,CAAC,CAAC;IACpG,IAAI,IAAI,CAACmC,UAAU,CAACG,UAAU,GAAG,CAAC,EAAE;MAChCH,UAAU,CAAC2B,KAAK,GAAG,IAAI,CAAC4D,cAAc,CAAC,CAAC;IAC5C;IACA,IAAI,CAACjF,eAAe,CAACzC,IAAI,EAAEmC,UAAU,CAAC;IACtC,IAAI,CAACwB,GAAG,CAAC3D,IAAI,CAAC;IACd,OAAO,IAAI;EACf;EACA4H,WAAWA,CAACtH,IAAI,EAAE;IACdK,UAAU,CAACkH,kBAAkB,CAACC,OAAO,CAACxH,IAAI,CAAC;IAC3CK,UAAU,CAACkH,kBAAkB,CAACE,OAAO,CAAC,IAAI,CAAC5F,UAAU,CAAC,aAAa,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,WAAW,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,aAAa,CAAC,EAAE,IAAI,CAACA,UAAU,CAAC,YAAY,CAAC,CAAC;IAClK,MAAM6F,EAAE,GAAGrH,UAAU,CAACkH,kBAAkB,CAACI,cAAc,CAAC,CAAC;IACzD,OAAO;MAAEhI,CAAC,EAAE+H,EAAE,CAAC/H,CAAC;MAAEC,CAAC,EAAE8H,EAAE,CAAC9H,CAAC;MAAEC,KAAK,EAAE6H,EAAE,CAACE,CAAC;MAAE9H,MAAM,EAAE4H,EAAE,CAACG;IAAE,CAAC;EAC1D;EACAC,QAAQA,CAAC9H,IAAI,EAAEL,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACI,IAAI,IAAIA,IAAI,CAAC+C,MAAM,IAAI,CAAC,EAAE;MAC3B,OAAO,IAAI;IACf;IACApD,CAAC,GAAG,IAAI,CAAC2C,KAAK,CAAC3C,CAAC,CAAC;IACjBC,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC1C,CAAC,CAAC;IACjB,MAAMiC,UAAU,GAAGJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,EAAE;MAAEE,MAAM,EAAE,MAAM;MAAEpC,CAAC;MACpFC;IAAE,CAAC,CAAC;IACR,MAAMmI,GAAG,GAAG,IAAI,CAACjH,MAAM,CAAC,MAAM,CAAC;IAC/BiH,GAAG,CAACC,WAAW,GAAGhI,IAAI;IACtB,IAAI,CAACmC,eAAe,CAAC4F,GAAG,EAAElG,UAAU,CAAC;IACrC,IAAI,CAACwB,GAAG,CAAC0E,GAAG,CAAC;IACb,OAAO,IAAI;EACf;EACAE,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5F,UAAU,CAACD,IAAI,CAAC;MACjBZ,KAAK,EAAE0G,eAAe,CAAC,IAAI,CAAC1G,KAAK,CAAC;MAClCK,UAAU,EAAEqG,eAAe,CAAC,IAAI,CAACrG,UAAU;IAC/C,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAsG,OAAOA,CAAA,EAAG;IACN,MAAMC,UAAU,GAAG,IAAI,CAAC/F,UAAU,CAACY,GAAG,CAAC,CAAC;IACxC,IAAImF,UAAU,EAAE;MACZ,MAAM5G,KAAK,GAAG4G,UAAU;MACxB,IAAI,CAAC5G,KAAK,GAAG0G,eAAe,CAAC1G,KAAK,CAACA,KAAK,CAAC;MACzC,IAAI,CAACK,UAAU,GAAGqG,eAAe,CAAC1G,KAAK,CAACK,UAAU,CAAC;IACvD;IACA,OAAO,IAAI;EACf;EACA,IAAIwG,SAASA,CAAC7E,KAAK,EAAE;IACjB,IAAI,CAACD,YAAY,CAACC,KAAK,CAAC;EAC5B;EACA,IAAI6E,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACxG,UAAU,CAACC,IAAI;EAC/B;EACA,IAAIwG,WAAWA,CAAC9E,KAAK,EAAE;IACnB,IAAI,CAACE,cAAc,CAACF,KAAK,CAAC;EAC9B;EACA,IAAI8E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzG,UAAU,CAACE,MAAM;EACjC;EACA0F,OAAOA,CAACc,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEjF,KAAK,EAAE;IAC5B,MAAMkF,QAAQ,GAAG1J,IAAI,CAAC2J,QAAQ,CAACJ,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEjF,KAAK,CAAC;IACtD,IAAI,CAAC9C,aAAa,GAAG1B,IAAI,CAAC4J,WAAW,CAACF,QAAQ,CAAC;IAC/C,MAAMG,cAAc,GAAG;MACnB,aAAa,EAAEH,QAAQ,CAACI,MAAM;MAC9B,WAAW,EAAEJ,QAAQ,CAACF,IAAI;MAC1B,aAAa,EAAEE,QAAQ,CAACD,MAAM;MAC9B,YAAY,EAAEC,QAAQ,CAAClF;IAC3B,CAAC;IACD,IAAI,CAAC3B,UAAU,GAAGJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,UAAU,CAAC,EAAEgH,cAAc,CAAC;IACnF,IAAI,CAACrH,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAAC,EAAEqH,cAAc,CAAC;IACzE,OAAO,IAAI;EACf;EACAE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACrI,aAAa;EAC7B;AACJ;AACAL,UAAU,CAACkH,kBAAkB,GAAG,IAAIxI,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}