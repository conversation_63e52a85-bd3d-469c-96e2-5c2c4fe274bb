{"ast": null, "code": "// ----- CONVERSION FUNCTIONS ----- //\n\n// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'\n// MIDI FORMAT: 0-127\n// PARSE FORMAT: { step, alter, octave }\n\nconst SEMI_MAP = {\n  C: 0,\n  D: 2,\n  E: 4,\n  F: 5,\n  G: 7,\n  A: 9,\n  B: 11\n};\nexport function gigaConvert(key, time, string) {\n  const defaultSetup = 'tabstave notaion=true tablature=false';\n  const notes = string.split(' ');\n  const parsedNotes = notes.map(parseNote);\n  const midiNotes = parsedNotes.map(toMidi);\n  const altoNotes = midiNotes.map(midi => fromMidi(midi - 9));\n  const altoNotesFormatted = altoNotes.map(formatNote);\n  return `${defaultSetup} key=${key} time=${time} \\nnotes ${altoNotesFormatted.join(' ')}`;\n}\nexport function parseNote(string) {\n  const m = string.match(/^([A-G])([#nb]?)[/](\\d)$/);\n  if (!m) {\n    throw new Error(`Invalid note format: ${string}`);\n  }\n  const [step, acc, octave] = m;\n  return {\n    step,\n    alter: acc === '#' ? 1 : acc === 'b' ? -1 : 0,\n    octave: parseInt(octave, 10)\n  };\n}\nexport function toMidi(parsedNote) {\n  const {\n    step,\n    alter,\n    octave\n  } = parsedNote;\n  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;\n  return midi;\n}\nexport function fromMidi(midiNote) {\n  const octave = Math.floor(midiNote / 12) - 1;\n  const semi = midiNote % 12;\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\n    if (val === semi) {\n      return {\n        step,\n        alter: 0,\n        octave\n      };\n    }\n  }\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\n    if (val + 1 === semi) {\n      return {\n        step,\n        alter: 1,\n        octave\n      };\n    }\n  }\n}\nexport function formatNote(parsedNote) {\n  const {\n    step,\n    alter,\n    octave\n  } = parsedNote;\n  let acc = '';\n  if (alter === 1) {\n    acc = '#';\n  }\n  return `${step}${acc}/${octave}`;\n}", "map": {"version": 3, "names": ["SEMI_MAP", "C", "D", "E", "F", "G", "A", "B", "gigaConvert", "key", "time", "string", "defaultSetup", "notes", "split", "parsedNotes", "map", "parseNote", "midiNotes", "<PERSON><PERSON><PERSON><PERSON>", "altoNotes", "midi", "fromMidi", "altoNotesFormatted", "formatNote", "join", "m", "match", "Error", "step", "acc", "octave", "alter", "parseInt", "parsedNote", "midiNote", "Math", "floor", "semi", "val", "Object", "entries"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/ConvertFunctions.js"], "sourcesContent": ["\r\n// ----- CONVERSION FUNCTIONS ----- //\r\n\r\n\r\n// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'\r\n// MIDI FORMAT: 0-127\r\n// PARSE FORMAT: { step, alter, octave }\r\n\r\n\r\nconst SEMI_MAP = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };\r\n\r\nexport function gigaConvert(key, time, string) {\r\n  const defaultSetup = 'tabstave notaion=true tablature=false'\r\n  const notes = string.split(' ');\r\n  const parsedNotes = notes.map(parseNote);\r\n  const midiNotes = parsedNotes.map(toMidi);\r\n  const altoNotes = midiNotes.map((midi) => fromMidi(midi - 9));\r\n  const altoNotesFormatted = altoNotes.map(formatNote);\r\n\r\n  return `${defaultSetup} key=${key} time=${time} \\nnotes ${altoNotesFormatted.join(' ')}`\r\n}\r\n\r\nexport function parseNote(string) {\r\n  const m = string.match(/^([A-G])([#nb]?)[/](\\d)$/);\r\n  if (!m) {\r\n    throw new Error(`Invalid note format: ${string}`);\r\n  }\r\n  const [ step, acc, octave ] = m;\r\n  return { \r\n    step,\r\n    alter: acc === '#' ? 1 : (acc === 'b' ? -1 : 0),\r\n    octave: parseInt(octave, 10),\r\n  };\r\n}\r\n\r\nexport function toMidi(parsedNote) {\r\n  const {step, alter, octave} = parsedNote;\r\n  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;\r\n  return midi;\r\n}\r\n\r\nexport function fromMidi(midiNote) {\r\n  const octave = Math.floor(midiNote / 12) - 1;\r\n  const semi = midiNote % 12;\r\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\r\n    if (val === semi) {\r\n      return { step, alter: 0, octave };\r\n    }\r\n  }\r\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\r\n    if (val + 1 === semi) {\r\n      return { step, alter: 1, octave };\r\n    }\r\n  }\r\n}\r\n\r\nexport function formatNote(parsedNote) {\r\n  const { step, alter, octave } = parsedNote;\r\n  let acc = '';\r\n  if (alter === 1) {\r\n    acc = '#';\r\n  }\r\n  return `${step}${acc}/${octave}`;\r\n}"], "mappings": "AACA;;AAGA;AACA;AACA;;AAGA,MAAMA,QAAQ,GAAG;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAG,CAAC;AAE9D,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC7C,MAAMC,YAAY,GAAG,uCAAuC;EAC5D,MAAMC,KAAK,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EAC/B,MAAMC,WAAW,GAAGF,KAAK,CAACG,GAAG,CAACC,SAAS,CAAC;EACxC,MAAMC,SAAS,GAAGH,WAAW,CAACC,GAAG,CAACG,MAAM,CAAC;EACzC,MAAMC,SAAS,GAAGF,SAAS,CAACF,GAAG,CAAEK,IAAI,IAAKC,QAAQ,CAACD,IAAI,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAME,kBAAkB,GAAGH,SAAS,CAACJ,GAAG,CAACQ,UAAU,CAAC;EAEpD,OAAO,GAAGZ,YAAY,QAAQH,GAAG,SAASC,IAAI,YAAYa,kBAAkB,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE;AAC1F;AAEA,OAAO,SAASR,SAASA,CAACN,MAAM,EAAE;EAChC,MAAMe,CAAC,GAAGf,MAAM,CAACgB,KAAK,CAAC,0BAA0B,CAAC;EAClD,IAAI,CAACD,CAAC,EAAE;IACN,MAAM,IAAIE,KAAK,CAAC,wBAAwBjB,MAAM,EAAE,CAAC;EACnD;EACA,MAAM,CAAEkB,IAAI,EAAEC,GAAG,EAAEC,MAAM,CAAE,GAAGL,CAAC;EAC/B,OAAO;IACLG,IAAI;IACJG,KAAK,EAAEF,GAAG,KAAK,GAAG,GAAG,CAAC,GAAIA,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAE;IAC/CC,MAAM,EAAEE,QAAQ,CAACF,MAAM,EAAE,EAAE;EAC7B,CAAC;AACH;AAEA,OAAO,SAASZ,MAAMA,CAACe,UAAU,EAAE;EACjC,MAAM;IAACL,IAAI;IAAEG,KAAK;IAAED;EAAM,CAAC,GAAGG,UAAU;EACxC,MAAMb,IAAI,GAAG,CAACU,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG/B,QAAQ,CAAC6B,IAAI,CAAC,GAAGG,KAAK;EACvD,OAAOX,IAAI;AACb;AAEA,OAAO,SAASC,QAAQA,CAACa,QAAQ,EAAE;EACjC,MAAMJ,MAAM,GAAGK,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC;EAC5C,MAAMG,IAAI,GAAGH,QAAQ,GAAG,EAAE;EAC1B,KAAK,MAAM,CAACN,IAAI,EAAEU,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACzC,QAAQ,CAAC,EAAE;IAClD,IAAIuC,GAAG,KAAKD,IAAI,EAAE;MAChB,OAAO;QAAET,IAAI;QAAEG,KAAK,EAAE,CAAC;QAAED;MAAO,CAAC;IACnC;EACF;EACA,KAAK,MAAM,CAACF,IAAI,EAAEU,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACzC,QAAQ,CAAC,EAAE;IAClD,IAAIuC,GAAG,GAAG,CAAC,KAAKD,IAAI,EAAE;MACpB,OAAO;QAAET,IAAI;QAAEG,KAAK,EAAE,CAAC;QAAED;MAAO,CAAC;IACnC;EACF;AACF;AAEA,OAAO,SAASP,UAAUA,CAACU,UAAU,EAAE;EACrC,MAAM;IAAEL,IAAI;IAAEG,KAAK;IAAED;EAAO,CAAC,GAAGG,UAAU;EAC1C,IAAIJ,GAAG,GAAG,EAAE;EACZ,IAAIE,KAAK,KAAK,CAAC,EAAE;IACfF,GAAG,GAAG,GAAG;EACX;EACA,OAAO,GAAGD,IAAI,GAAGC,GAAG,IAAIC,MAAM,EAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}