{"ast": null, "code": "export function isCategory(obj, category, checkAncestors = true) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  let constructorFcn = obj.constructor;\n  if (checkAncestors) {\n    while (obj !== null) {\n      constructorFcn = obj.constructor;\n      if ('CATEGORY' in constructorFcn && constructorFcn.CATEGORY === category) {\n        return true;\n      }\n      obj = Object.getPrototypeOf(obj);\n    }\n    return false;\n  } else {\n    return 'CATEGORY' in constructorFcn && constructorFcn.CATEGORY === category;\n  }\n}\nexport const isAccidental = obj => isCategory(obj, \"Accidental\");\nexport const isAnnotation = obj => isCategory(obj, \"Annotation\");\nexport const isBarline = obj => isCategory(obj, \"Barline\");\nexport const isDot = obj => isCategory(obj, \"Dot\");\nexport const isGraceNote = obj => isCategory(obj, \"GraceNote\");\nexport const isGraceNoteGroup = obj => isCategory(obj, \"GraceNoteGroup\");\nexport const isNote = obj => isCategory(obj, \"Note\");\nexport const isRenderContext = obj => isCategory(obj, \"RenderContext\");\nexport const isStaveNote = obj => isCategory(obj, \"StaveNote\");\nexport const isStemmableNote = obj => isCategory(obj, \"StemmableNote\");\nexport const isTabNote = obj => isCategory(obj, \"TabNote\");", "map": {"version": 3, "names": ["isCategory", "obj", "category", "checkAncestors", "constructorFcn", "constructor", "CATEGORY", "Object", "getPrototypeOf", "isAccidental", "isAnnotation", "isBarline", "isDot", "isGraceNote", "isGraceNoteGroup", "isNote", "isRenderContext", "isStaveNote", "isStemmableNote", "isTabNote"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/typeguard.js"], "sourcesContent": ["export function isCategory(obj, category, checkAncestors = true) {\n    if (typeof obj !== 'object' || obj === null) {\n        return false;\n    }\n    let constructorFcn = obj.constructor;\n    if (checkAncestors) {\n        while (obj !== null) {\n            constructorFcn = obj.constructor;\n            if ('CATEGORY' in constructorFcn && constructorFcn.CATEGORY === category) {\n                return true;\n            }\n            obj = Object.getPrototypeOf(obj);\n        }\n        return false;\n    }\n    else {\n        return 'CATEGORY' in constructorFcn && constructorFcn.CATEGORY === category;\n    }\n}\nexport const isAccidental = (obj) => isCategory(obj, \"Accidental\");\nexport const isAnnotation = (obj) => isCategory(obj, \"Annotation\");\nexport const isBarline = (obj) => isCategory(obj, \"Barline\");\nexport const isDot = (obj) => isCategory(obj, \"Dot\");\nexport const isGraceNote = (obj) => isCategory(obj, \"GraceNote\");\nexport const isGraceNoteGroup = (obj) => isCategory(obj, \"GraceNoteGroup\");\nexport const isNote = (obj) => isCategory(obj, \"Note\");\nexport const isRenderContext = (obj) => isCategory(obj, \"RenderContext\");\nexport const isStaveNote = (obj) => isCategory(obj, \"StaveNote\");\nexport const isStemmableNote = (obj) => isCategory(obj, \"StemmableNote\");\nexport const isTabNote = (obj) => isCategory(obj, \"TabNote\");\n"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,cAAc,GAAG,IAAI,EAAE;EAC7D,IAAI,OAAOF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,IAAIG,cAAc,GAAGH,GAAG,CAACI,WAAW;EACpC,IAAIF,cAAc,EAAE;IAChB,OAAOF,GAAG,KAAK,IAAI,EAAE;MACjBG,cAAc,GAAGH,GAAG,CAACI,WAAW;MAChC,IAAI,UAAU,IAAID,cAAc,IAAIA,cAAc,CAACE,QAAQ,KAAKJ,QAAQ,EAAE;QACtE,OAAO,IAAI;MACf;MACAD,GAAG,GAAGM,MAAM,CAACC,cAAc,CAACP,GAAG,CAAC;IACpC;IACA,OAAO,KAAK;EAChB,CAAC,MACI;IACD,OAAO,UAAU,IAAIG,cAAc,IAAIA,cAAc,CAACE,QAAQ,KAAKJ,QAAQ;EAC/E;AACJ;AACA,OAAO,MAAMO,YAAY,GAAIR,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,YAAY,CAAC;AAClE,OAAO,MAAMS,YAAY,GAAIT,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,YAAY,CAAC;AAClE,OAAO,MAAMU,SAAS,GAAIV,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,SAAS,CAAC;AAC5D,OAAO,MAAMW,KAAK,GAAIX,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,KAAK,CAAC;AACpD,OAAO,MAAMY,WAAW,GAAIZ,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,WAAW,CAAC;AAChE,OAAO,MAAMa,gBAAgB,GAAIb,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,gBAAgB,CAAC;AAC1E,OAAO,MAAMc,MAAM,GAAId,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,MAAM,CAAC;AACtD,OAAO,MAAMe,eAAe,GAAIf,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,eAAe,CAAC;AACxE,OAAO,MAAMgB,WAAW,GAAIhB,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,WAAW,CAAC;AAChE,OAAO,MAAMiB,eAAe,GAAIjB,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,eAAe,CAAC;AACxE,OAAO,MAAMkB,SAAS,GAAIlB,GAAG,IAAKD,UAAU,CAACC,GAAG,EAAE,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}