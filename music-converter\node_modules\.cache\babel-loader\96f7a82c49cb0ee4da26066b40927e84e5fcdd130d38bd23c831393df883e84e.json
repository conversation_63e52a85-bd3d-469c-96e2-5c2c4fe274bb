{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { drawDot } from './rendercontext.js';\nimport { Tables } from './tables.js';\nimport { Tickable } from './tickable.js';\nimport { defined, RuntimeError } from './util.js';\nexport class Note extends Tickable {\n  static get CATEGORY() {\n    return \"Note\";\n  }\n  static getGlyphProps(duration, type = 'n') {\n    duration = Tables.sanitizeDuration(duration);\n    let code = Tables.durationCodes[duration];\n    if (code === undefined) {\n      code = Tables.durationCodes['4'];\n    }\n    const codeNoteHead = Tables.codeNoteHead(type.toUpperCase(), duration);\n    if (codeNoteHead !== Glyphs.null) {\n      code = Object.assign(Object.assign({}, code), {\n        codeHead: codeNoteHead\n      });\n    }\n    return code;\n  }\n  static plotMetrics(ctx, note, yPos) {\n    var _a;\n    const metrics = note.getMetrics();\n    const xStart = note.getAbsoluteX() - metrics.modLeftPx - metrics.leftDisplacedHeadPx;\n    const xPre1 = note.getAbsoluteX() - metrics.leftDisplacedHeadPx;\n    const xAbs = note.getAbsoluteX();\n    const xPost1 = note.getAbsoluteX() + metrics.notePx;\n    const xPost2 = note.getAbsoluteX() + metrics.notePx + metrics.rightDisplacedHeadPx;\n    const xEnd = note.getAbsoluteX() + metrics.notePx + metrics.rightDisplacedHeadPx + metrics.modRightPx;\n    const xFreedomRight = xEnd + ((_a = note.getFormatterMetrics().freedom.right) !== null && _a !== void 0 ? _a : 0);\n    const xWidth = xEnd - xStart;\n    ctx.save();\n    ctx.setFont(Metrics.get('fontFamily'), 8);\n    ctx.fillText(Math.round(xWidth) + 'px', xStart + note.getXShift(), yPos);\n    const y = yPos + 7;\n    function stroke(x1, x2, color, yy = y) {\n      ctx.beginPath();\n      ctx.setStrokeStyle(color);\n      ctx.setFillStyle(color);\n      ctx.setLineWidth(3);\n      ctx.moveTo(x1 + note.getXShift(), yy);\n      ctx.lineTo(x2 + note.getXShift(), yy);\n      ctx.stroke();\n    }\n    stroke(xStart, xPre1, 'red');\n    stroke(xPre1, xAbs, '#999');\n    stroke(xAbs, xPost1, 'green');\n    stroke(xPost1, xPost2, '#999');\n    stroke(xPost2, xEnd, 'red');\n    stroke(xEnd, xFreedomRight, '#DD0');\n    stroke(xStart - note.getXShift(), xStart, '#BBB');\n    drawDot(ctx, xAbs + note.getXShift(), y, 'blue');\n    const formatterMetrics = note.getFormatterMetrics();\n    if (formatterMetrics.iterations > 0) {\n      const spaceDeviation = formatterMetrics.space.deviation;\n      const prefix = spaceDeviation >= 0 ? '+' : '';\n      ctx.setFillStyle('red');\n      ctx.fillText(prefix + Math.round(spaceDeviation), xAbs + note.getXShift(), yPos - 10);\n    }\n    ctx.restore();\n  }\n  static parseDuration(durationString) {\n    if (!durationString) {\n      return undefined;\n    }\n    const regexp = /(\\d*\\/?\\d+|[a-z])(d*)([nrhms]|$)/;\n    const result = regexp.exec(durationString);\n    if (!result) {\n      return undefined;\n    }\n    const duration = result[1];\n    const dots = result[2].length;\n    const type = result[3] || 'n';\n    return {\n      duration,\n      dots,\n      type\n    };\n  }\n  static parseNoteStruct(noteStruct) {\n    const durationProps = Note.parseDuration(noteStruct.duration);\n    if (!durationProps) {\n      return undefined;\n    }\n    let type = noteStruct.type;\n    if (type && !Tables.validTypes[type]) {\n      return undefined;\n    }\n    const customTypes = [];\n    if (!type) {\n      type = durationProps.type || 'n';\n      if (noteStruct.keys !== undefined) {\n        noteStruct.keys.forEach((k, i) => {\n          const result = k.split('/');\n          customTypes[i] = result && result.length === 3 ? result[2] : type;\n        });\n      }\n    }\n    let ticks = Tables.durationToTicks(durationProps.duration);\n    if (!ticks) {\n      return undefined;\n    }\n    const dots = noteStruct.dots ? noteStruct.dots : durationProps.dots;\n    if (typeof dots !== 'number') {\n      return undefined;\n    }\n    let currentTicks = ticks;\n    for (let i = 0; i < dots; i++) {\n      if (currentTicks <= 1) return undefined;\n      currentTicks = currentTicks / 2;\n      ticks += currentTicks;\n    }\n    return {\n      duration: durationProps.duration,\n      type,\n      customTypes,\n      dots,\n      ticks\n    };\n  }\n  constructor(noteStruct) {\n    super();\n    if (!noteStruct) {\n      throw new RuntimeError('BadArguments', 'Note must have valid initialization data to identify duration and type.');\n    }\n    const parsedNoteStruct = Note.parseNoteStruct(noteStruct);\n    if (!parsedNoteStruct) {\n      throw new RuntimeError('BadArguments', `Invalid note initialization object: ${JSON.stringify(noteStruct)}`);\n    }\n    this.keys = noteStruct.keys || [];\n    this.keyProps = [];\n    this.duration = parsedNoteStruct.duration;\n    this.noteType = parsedNoteStruct.type;\n    this.customTypes = parsedNoteStruct.customTypes;\n    if (noteStruct.durationOverride) {\n      this.setDuration(noteStruct.durationOverride);\n    } else {\n      this.setIntrinsicTicks(parsedNoteStruct.ticks);\n    }\n    this.modifiers = [];\n    this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n    this.customGlyphs = this.customTypes.map(t => Note.getGlyphProps(this.duration, t));\n    this.playNote = undefined;\n    this.ignoreTicks = false;\n    this.width = 0;\n    this.leftDisplacedHeadPx = 0;\n    this.rightDisplacedHeadPx = 0;\n    this.xShift = 0;\n    this.ys = [];\n    if (noteStruct.alignCenter) {\n      this.setCenterAlignment(noteStruct.alignCenter);\n    }\n    this.renderOptions = {\n      annotationSpacing: 5,\n      strokePx: 1,\n      yShift: 0\n    };\n  }\n  getPlayNote() {\n    return this.playNote;\n  }\n  setPlayNote(note) {\n    this.playNote = note;\n    return this;\n  }\n  isRest() {\n    return false;\n  }\n  addStroke(index, stroke) {\n    stroke.setNote(this);\n    stroke.setIndex(index);\n    this.modifiers.push(stroke);\n    this.preFormatted = false;\n    return this;\n  }\n  getStave() {\n    return this.stave;\n  }\n  checkStave() {\n    return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n  }\n  setStave(stave) {\n    this.stave = stave;\n    this.setYs([stave.getYForLine(0)]);\n    this.setContext(this.stave.getContext());\n    return this;\n  }\n  getLeftDisplacedHeadPx() {\n    return this.leftDisplacedHeadPx;\n  }\n  getRightDisplacedHeadPx() {\n    return this.rightDisplacedHeadPx;\n  }\n  setLeftDisplacedHeadPx(x) {\n    this.leftDisplacedHeadPx = x;\n    return this;\n  }\n  setRightDisplacedHeadPx(x) {\n    this.rightDisplacedHeadPx = x;\n    return this;\n  }\n  shouldIgnoreTicks() {\n    return this.ignoreTicks;\n  }\n  getLineNumber(isTopNote) {\n    return 0;\n  }\n  getLineForRest() {\n    return 0;\n  }\n  getGlyphProps() {\n    return this.glyphProps;\n  }\n  getGlyphWidth() {\n    return 0;\n  }\n  setYs(ys) {\n    this.ys = ys;\n    return this;\n  }\n  getYs() {\n    if (this.ys.length === 0) {\n      throw new RuntimeError('NoYValues', 'No Y-values calculated for this note.');\n    }\n    return this.ys;\n  }\n  getYForTopText(textLine) {\n    return this.checkStave().getYForTopText(textLine);\n  }\n  getVoice() {\n    if (!this.voice) throw new RuntimeError('NoVoice', 'Note has no voice.');\n    return this.voice;\n  }\n  setVoice(voice) {\n    this.voice = voice;\n    this.preFormatted = false;\n    return this;\n  }\n  getTickContext() {\n    return this.checkTickContext();\n  }\n  setTickContext(tc) {\n    this.tickContext = tc;\n    this.preFormatted = false;\n    return this;\n  }\n  getDuration() {\n    return this.duration;\n  }\n  isDotted() {\n    return this.getModifiersByType(\"Dot\").length > 0;\n  }\n  hasStem() {\n    return false;\n  }\n  getNoteType() {\n    return this.noteType;\n  }\n  getBeam() {\n    return this.beam;\n  }\n  checkBeam() {\n    return defined(this.beam, 'NoBeam', 'No beam attached to instance');\n  }\n  hasBeam() {\n    return this.beam !== undefined;\n  }\n  setBeam(beam) {\n    this.beam = beam;\n    return this;\n  }\n  addModifier(modifier, index = 0) {\n    const signature = 'Note.addModifier(modifier: Modifier, index: number=0)';\n    if (typeof index === 'string') {\n      index = parseInt(index);\n      console.warn(signature + ' expected a number for `index`, but received a string.');\n    }\n    if (typeof modifier !== 'object' || typeof index !== 'number') {\n      throw new RuntimeError('WrongParams', 'Incorrect call signature. Use ' + signature + ' instead.');\n    }\n    modifier.setNote(this);\n    modifier.setIndex(index);\n    super.addModifier(modifier);\n    return this;\n  }\n  getModifiersByType(type) {\n    return this.modifiers.filter(modifier => modifier.getCategory() === type);\n  }\n  getModifierStartXY(position, index, options) {\n    if (!this.preFormatted) {\n      throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n    }\n    return {\n      x: this.getAbsoluteX(),\n      y: this.ys[0]\n    };\n  }\n  getRightParenthesisPx(index) {\n    const props = this.getKeyProps()[index];\n    return props.displaced ? this.getRightDisplacedHeadPx() : 0;\n  }\n  getLeftParenthesisPx(index) {\n    const props = this.getKeyProps()[index];\n    return props.displaced ? this.getLeftDisplacedHeadPx() - this.xShift : -this.xShift;\n  }\n  getFirstDotPx() {\n    let px = this.getRightDisplacedHeadPx();\n    const parentheses = this.checkModifierContext().getMembers('Parenthesis');\n    if (parentheses.length !== 0) {\n      px += parentheses[0].getWidth() + 1;\n    }\n    return px;\n  }\n  getMetrics() {\n    if (!this.preFormatted) {\n      throw new RuntimeError('UnformattedNote', \"Can't call getMetrics on an unformatted note.\");\n    }\n    const modLeftPx = this.modifierContext ? this.modifierContext.getState().leftShift : 0;\n    const modRightPx = this.modifierContext ? this.modifierContext.getState().rightShift : 0;\n    const width = this.getWidth();\n    const glyphWidth = this.getGlyphWidth();\n    const notePx = width - modLeftPx - modRightPx - this.leftDisplacedHeadPx - this.rightDisplacedHeadPx;\n    return {\n      width,\n      glyphWidth,\n      notePx,\n      modLeftPx,\n      modRightPx,\n      leftDisplacedHeadPx: this.leftDisplacedHeadPx,\n      rightDisplacedHeadPx: this.rightDisplacedHeadPx,\n      glyphPx: 0\n    };\n  }\n  getAbsoluteX() {\n    const tickContext = this.checkTickContext(`Can't getAbsoluteX() without a TickContext.`);\n    let x = tickContext.getX();\n    if (this.stave) {\n      x += this.stave.getNoteStartX() + Metrics.get('Stave.padding', 0);\n    }\n    if (this.isCenterAligned()) {\n      x += this.getCenterXShift();\n    }\n    return x;\n  }\n  getStemDirection() {\n    throw new RuntimeError('NoStem', 'No stem attached to this note.');\n  }\n  getStemExtents() {\n    throw new RuntimeError('NoStem', 'No stem attached to this note.');\n  }\n  getTieRightX() {\n    let tieStartX = this.getAbsoluteX();\n    const noteGlyphWidth = this.getGlyphWidth();\n    tieStartX += noteGlyphWidth / 2;\n    tieStartX += -this.width / 2 + this.width + 2;\n    return tieStartX;\n  }\n  getTieLeftX() {\n    let tieEndX = this.getAbsoluteX();\n    const noteGlyphWidth = this.getGlyphWidth();\n    tieEndX += noteGlyphWidth / 2;\n    tieEndX -= this.width / 2 + 2;\n    return tieEndX;\n  }\n  getKeys() {\n    return this.keys;\n  }\n  getKeyProps() {\n    return this.keyProps;\n  }\n  getBoundingBox() {\n    const boundingBox = super.getBoundingBox();\n    for (let i = 0; i < this.modifiers.length; i++) {\n      boundingBox.mergeWith(this.modifiers[i].getBoundingBox());\n    }\n    return boundingBox;\n  }\n}", "map": {"version": 3, "names": ["Glyphs", "Metrics", "drawDot", "Tables", "Tickable", "defined", "RuntimeError", "Note", "CATEGORY", "getGlyphProps", "duration", "type", "sanitizeDuration", "code", "durationCodes", "undefined", "codeNoteHead", "toUpperCase", "null", "Object", "assign", "codeHead", "plotMetrics", "ctx", "note", "yPos", "_a", "metrics", "getMetrics", "xStart", "getAbsoluteX", "modLeftPx", "leftDisplacedHeadPx", "xPre1", "xAbs", "xPost1", "notePx", "xPost2", "rightDisplacedHeadPx", "xEnd", "modRightPx", "xFreedomRight", "getFormatterMetrics", "freedom", "right", "xWidth", "save", "setFont", "get", "fillText", "Math", "round", "getXShift", "y", "stroke", "x1", "x2", "color", "yy", "beginPath", "setStrokeStyle", "setFillStyle", "setLineWidth", "moveTo", "lineTo", "formatterMetrics", "iterations", "spaceDeviation", "space", "deviation", "prefix", "restore", "parseDuration", "durationString", "regexp", "result", "exec", "dots", "length", "parseNoteStruct", "noteStruct", "durationProps", "validTypes", "customTypes", "keys", "for<PERSON>ach", "k", "i", "split", "ticks", "durationToTicks", "currentTicks", "constructor", "parsedNoteStruct", "JSON", "stringify", "keyProps", "noteType", "durationOverride", "setDuration", "setIntrinsicTicks", "modifiers", "glyphProps", "customGlyphs", "map", "t", "playNote", "ignoreTicks", "width", "xShift", "ys", "alignCenter", "setCenterAlignment", "renderOptions", "annotationSpacing", "strokePx", "yShift", "getPlayNote", "setPlayNote", "isRest", "addStroke", "index", "setNote", "setIndex", "push", "preFormatted", "getStave", "stave", "checkStave", "setStave", "setYs", "getYForLine", "setContext", "getContext", "getLeftDisplacedHeadPx", "getRightDisplacedHeadPx", "setLeftDisplacedHeadPx", "x", "setRightDisplacedHeadPx", "shouldIgnoreTicks", "getLineNumber", "isTopNote", "getLineForRest", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getYs", "getYForTopText", "textLine", "getVoice", "voice", "setVoice", "getTickContext", "checkTickContext", "setTickContext", "tc", "tickContext", "getDuration", "isDotted", "getModifiersByType", "hasStem", "getNoteType", "getBeam", "beam", "checkBeam", "hasBeam", "setBeam", "addModifier", "modifier", "signature", "parseInt", "console", "warn", "filter", "getCategory", "getModifierStartXY", "position", "options", "getRightParenthesisPx", "props", "getKeyProps", "displaced", "getLeftParenthesisPx", "getFirstDotPx", "px", "parentheses", "checkModifierContext", "getMembers", "getWidth", "modifierContext", "getState", "leftShift", "rightShift", "glyphWidth", "glyphPx", "getX", "getNoteStartX", "isCenterAligned", "getCenterXShift", "getStemDirection", "getStemExtents", "getTieRightX", "tieStartX", "noteGlyphWidth", "getTieLeftX", "tieEndX", "get<PERSON><PERSON><PERSON>", "getBoundingBox", "boundingBox", "mergeWith"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/note.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { drawDot } from './rendercontext.js';\nimport { Tables } from './tables.js';\nimport { Tickable } from './tickable.js';\nimport { defined, RuntimeError } from './util.js';\nexport class Note extends Tickable {\n    static get CATEGORY() {\n        return \"Note\";\n    }\n    static getGlyphProps(duration, type = 'n') {\n        duration = Tables.sanitizeDuration(duration);\n        let code = Tables.durationCodes[duration];\n        if (code === undefined) {\n            code = Tables.durationCodes['4'];\n        }\n        const codeNoteHead = Tables.codeNoteHead(type.toUpperCase(), duration);\n        if (codeNoteHead !== Glyphs.null) {\n            code = Object.assign(Object.assign({}, code), { codeHead: codeNoteHead });\n        }\n        return code;\n    }\n    static plotMetrics(ctx, note, yPos) {\n        var _a;\n        const metrics = note.getMetrics();\n        const xStart = note.getAbsoluteX() - metrics.modLeftPx - metrics.leftDisplacedHeadPx;\n        const xPre1 = note.getAbsoluteX() - metrics.leftDisplacedHeadPx;\n        const xAbs = note.getAbsoluteX();\n        const xPost1 = note.getAbsoluteX() + metrics.notePx;\n        const xPost2 = note.getAbsoluteX() + metrics.notePx + metrics.rightDisplacedHeadPx;\n        const xEnd = note.getAbsoluteX() + metrics.notePx + metrics.rightDisplacedHeadPx + metrics.modRightPx;\n        const xFreedomRight = xEnd + ((_a = note.getFormatterMetrics().freedom.right) !== null && _a !== void 0 ? _a : 0);\n        const xWidth = xEnd - xStart;\n        ctx.save();\n        ctx.setFont(Metrics.get('fontFamily'), 8);\n        ctx.fillText(Math.round(xWidth) + 'px', xStart + note.getXShift(), yPos);\n        const y = yPos + 7;\n        function stroke(x1, x2, color, yy = y) {\n            ctx.beginPath();\n            ctx.setStrokeStyle(color);\n            ctx.setFillStyle(color);\n            ctx.setLineWidth(3);\n            ctx.moveTo(x1 + note.getXShift(), yy);\n            ctx.lineTo(x2 + note.getXShift(), yy);\n            ctx.stroke();\n        }\n        stroke(xStart, xPre1, 'red');\n        stroke(xPre1, xAbs, '#999');\n        stroke(xAbs, xPost1, 'green');\n        stroke(xPost1, xPost2, '#999');\n        stroke(xPost2, xEnd, 'red');\n        stroke(xEnd, xFreedomRight, '#DD0');\n        stroke(xStart - note.getXShift(), xStart, '#BBB');\n        drawDot(ctx, xAbs + note.getXShift(), y, 'blue');\n        const formatterMetrics = note.getFormatterMetrics();\n        if (formatterMetrics.iterations > 0) {\n            const spaceDeviation = formatterMetrics.space.deviation;\n            const prefix = spaceDeviation >= 0 ? '+' : '';\n            ctx.setFillStyle('red');\n            ctx.fillText(prefix + Math.round(spaceDeviation), xAbs + note.getXShift(), yPos - 10);\n        }\n        ctx.restore();\n    }\n    static parseDuration(durationString) {\n        if (!durationString) {\n            return undefined;\n        }\n        const regexp = /(\\d*\\/?\\d+|[a-z])(d*)([nrhms]|$)/;\n        const result = regexp.exec(durationString);\n        if (!result) {\n            return undefined;\n        }\n        const duration = result[1];\n        const dots = result[2].length;\n        const type = result[3] || 'n';\n        return { duration, dots, type };\n    }\n    static parseNoteStruct(noteStruct) {\n        const durationProps = Note.parseDuration(noteStruct.duration);\n        if (!durationProps) {\n            return undefined;\n        }\n        let type = noteStruct.type;\n        if (type && !Tables.validTypes[type]) {\n            return undefined;\n        }\n        const customTypes = [];\n        if (!type) {\n            type = durationProps.type || 'n';\n            if (noteStruct.keys !== undefined) {\n                noteStruct.keys.forEach((k, i) => {\n                    const result = k.split('/');\n                    customTypes[i] = (result && result.length === 3 ? result[2] : type);\n                });\n            }\n        }\n        let ticks = Tables.durationToTicks(durationProps.duration);\n        if (!ticks) {\n            return undefined;\n        }\n        const dots = noteStruct.dots ? noteStruct.dots : durationProps.dots;\n        if (typeof dots !== 'number') {\n            return undefined;\n        }\n        let currentTicks = ticks;\n        for (let i = 0; i < dots; i++) {\n            if (currentTicks <= 1)\n                return undefined;\n            currentTicks = currentTicks / 2;\n            ticks += currentTicks;\n        }\n        return {\n            duration: durationProps.duration,\n            type,\n            customTypes,\n            dots,\n            ticks,\n        };\n    }\n    constructor(noteStruct) {\n        super();\n        if (!noteStruct) {\n            throw new RuntimeError('BadArguments', 'Note must have valid initialization data to identify duration and type.');\n        }\n        const parsedNoteStruct = Note.parseNoteStruct(noteStruct);\n        if (!parsedNoteStruct) {\n            throw new RuntimeError('BadArguments', `Invalid note initialization object: ${JSON.stringify(noteStruct)}`);\n        }\n        this.keys = noteStruct.keys || [];\n        this.keyProps = [];\n        this.duration = parsedNoteStruct.duration;\n        this.noteType = parsedNoteStruct.type;\n        this.customTypes = parsedNoteStruct.customTypes;\n        if (noteStruct.durationOverride) {\n            this.setDuration(noteStruct.durationOverride);\n        }\n        else {\n            this.setIntrinsicTicks(parsedNoteStruct.ticks);\n        }\n        this.modifiers = [];\n        this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n        this.customGlyphs = this.customTypes.map((t) => Note.getGlyphProps(this.duration, t));\n        this.playNote = undefined;\n        this.ignoreTicks = false;\n        this.width = 0;\n        this.leftDisplacedHeadPx = 0;\n        this.rightDisplacedHeadPx = 0;\n        this.xShift = 0;\n        this.ys = [];\n        if (noteStruct.alignCenter) {\n            this.setCenterAlignment(noteStruct.alignCenter);\n        }\n        this.renderOptions = {\n            annotationSpacing: 5,\n            strokePx: 1,\n            yShift: 0,\n        };\n    }\n    getPlayNote() {\n        return this.playNote;\n    }\n    setPlayNote(note) {\n        this.playNote = note;\n        return this;\n    }\n    isRest() {\n        return false;\n    }\n    addStroke(index, stroke) {\n        stroke.setNote(this);\n        stroke.setIndex(index);\n        this.modifiers.push(stroke);\n        this.preFormatted = false;\n        return this;\n    }\n    getStave() {\n        return this.stave;\n    }\n    checkStave() {\n        return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n    }\n    setStave(stave) {\n        this.stave = stave;\n        this.setYs([stave.getYForLine(0)]);\n        this.setContext(this.stave.getContext());\n        return this;\n    }\n    getLeftDisplacedHeadPx() {\n        return this.leftDisplacedHeadPx;\n    }\n    getRightDisplacedHeadPx() {\n        return this.rightDisplacedHeadPx;\n    }\n    setLeftDisplacedHeadPx(x) {\n        this.leftDisplacedHeadPx = x;\n        return this;\n    }\n    setRightDisplacedHeadPx(x) {\n        this.rightDisplacedHeadPx = x;\n        return this;\n    }\n    shouldIgnoreTicks() {\n        return this.ignoreTicks;\n    }\n    getLineNumber(isTopNote) {\n        return 0;\n    }\n    getLineForRest() {\n        return 0;\n    }\n    getGlyphProps() {\n        return this.glyphProps;\n    }\n    getGlyphWidth() {\n        return 0;\n    }\n    setYs(ys) {\n        this.ys = ys;\n        return this;\n    }\n    getYs() {\n        if (this.ys.length === 0) {\n            throw new RuntimeError('NoYValues', 'No Y-values calculated for this note.');\n        }\n        return this.ys;\n    }\n    getYForTopText(textLine) {\n        return this.checkStave().getYForTopText(textLine);\n    }\n    getVoice() {\n        if (!this.voice)\n            throw new RuntimeError('NoVoice', 'Note has no voice.');\n        return this.voice;\n    }\n    setVoice(voice) {\n        this.voice = voice;\n        this.preFormatted = false;\n        return this;\n    }\n    getTickContext() {\n        return this.checkTickContext();\n    }\n    setTickContext(tc) {\n        this.tickContext = tc;\n        this.preFormatted = false;\n        return this;\n    }\n    getDuration() {\n        return this.duration;\n    }\n    isDotted() {\n        return this.getModifiersByType(\"Dot\").length > 0;\n    }\n    hasStem() {\n        return false;\n    }\n    getNoteType() {\n        return this.noteType;\n    }\n    getBeam() {\n        return this.beam;\n    }\n    checkBeam() {\n        return defined(this.beam, 'NoBeam', 'No beam attached to instance');\n    }\n    hasBeam() {\n        return this.beam !== undefined;\n    }\n    setBeam(beam) {\n        this.beam = beam;\n        return this;\n    }\n    addModifier(modifier, index = 0) {\n        const signature = 'Note.addModifier(modifier: Modifier, index: number=0)';\n        if (typeof index === 'string') {\n            index = parseInt(index);\n            console.warn(signature + ' expected a number for `index`, but received a string.');\n        }\n        if (typeof modifier !== 'object' || typeof index !== 'number') {\n            throw new RuntimeError('WrongParams', 'Incorrect call signature. Use ' + signature + ' instead.');\n        }\n        modifier.setNote(this);\n        modifier.setIndex(index);\n        super.addModifier(modifier);\n        return this;\n    }\n    getModifiersByType(type) {\n        return this.modifiers.filter((modifier) => modifier.getCategory() === type);\n    }\n    getModifierStartXY(position, index, options) {\n        if (!this.preFormatted) {\n            throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n        }\n        return {\n            x: this.getAbsoluteX(),\n            y: this.ys[0],\n        };\n    }\n    getRightParenthesisPx(index) {\n        const props = this.getKeyProps()[index];\n        return props.displaced ? this.getRightDisplacedHeadPx() : 0;\n    }\n    getLeftParenthesisPx(index) {\n        const props = this.getKeyProps()[index];\n        return props.displaced ? this.getLeftDisplacedHeadPx() - this.xShift : -this.xShift;\n    }\n    getFirstDotPx() {\n        let px = this.getRightDisplacedHeadPx();\n        const parentheses = this.checkModifierContext().getMembers('Parenthesis');\n        if (parentheses.length !== 0) {\n            px += parentheses[0].getWidth() + 1;\n        }\n        return px;\n    }\n    getMetrics() {\n        if (!this.preFormatted) {\n            throw new RuntimeError('UnformattedNote', \"Can't call getMetrics on an unformatted note.\");\n        }\n        const modLeftPx = this.modifierContext ? this.modifierContext.getState().leftShift : 0;\n        const modRightPx = this.modifierContext ? this.modifierContext.getState().rightShift : 0;\n        const width = this.getWidth();\n        const glyphWidth = this.getGlyphWidth();\n        const notePx = width -\n            modLeftPx -\n            modRightPx -\n            this.leftDisplacedHeadPx -\n            this.rightDisplacedHeadPx;\n        return {\n            width,\n            glyphWidth,\n            notePx,\n            modLeftPx,\n            modRightPx,\n            leftDisplacedHeadPx: this.leftDisplacedHeadPx,\n            rightDisplacedHeadPx: this.rightDisplacedHeadPx,\n            glyphPx: 0,\n        };\n    }\n    getAbsoluteX() {\n        const tickContext = this.checkTickContext(`Can't getAbsoluteX() without a TickContext.`);\n        let x = tickContext.getX();\n        if (this.stave) {\n            x += this.stave.getNoteStartX() + Metrics.get('Stave.padding', 0);\n        }\n        if (this.isCenterAligned()) {\n            x += this.getCenterXShift();\n        }\n        return x;\n    }\n    getStemDirection() {\n        throw new RuntimeError('NoStem', 'No stem attached to this note.');\n    }\n    getStemExtents() {\n        throw new RuntimeError('NoStem', 'No stem attached to this note.');\n    }\n    getTieRightX() {\n        let tieStartX = this.getAbsoluteX();\n        const noteGlyphWidth = this.getGlyphWidth();\n        tieStartX += noteGlyphWidth / 2;\n        tieStartX += -this.width / 2 + this.width + 2;\n        return tieStartX;\n    }\n    getTieLeftX() {\n        let tieEndX = this.getAbsoluteX();\n        const noteGlyphWidth = this.getGlyphWidth();\n        tieEndX += noteGlyphWidth / 2;\n        tieEndX -= this.width / 2 + 2;\n        return tieEndX;\n    }\n    getKeys() {\n        return this.keys;\n    }\n    getKeyProps() {\n        return this.keyProps;\n    }\n    getBoundingBox() {\n        const boundingBox = super.getBoundingBox();\n        for (let i = 0; i < this.modifiers.length; i++) {\n            boundingBox.mergeWith(this.modifiers[i].getBoundingBox());\n        }\n        return boundingBox;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AACjD,OAAO,MAAMC,IAAI,SAASH,QAAQ,CAAC;EAC/B,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACA,OAAOC,aAAaA,CAACC,QAAQ,EAAEC,IAAI,GAAG,GAAG,EAAE;IACvCD,QAAQ,GAAGP,MAAM,CAACS,gBAAgB,CAACF,QAAQ,CAAC;IAC5C,IAAIG,IAAI,GAAGV,MAAM,CAACW,aAAa,CAACJ,QAAQ,CAAC;IACzC,IAAIG,IAAI,KAAKE,SAAS,EAAE;MACpBF,IAAI,GAAGV,MAAM,CAACW,aAAa,CAAC,GAAG,CAAC;IACpC;IACA,MAAME,YAAY,GAAGb,MAAM,CAACa,YAAY,CAACL,IAAI,CAACM,WAAW,CAAC,CAAC,EAAEP,QAAQ,CAAC;IACtE,IAAIM,YAAY,KAAKhB,MAAM,CAACkB,IAAI,EAAE;MAC9BL,IAAI,GAAGM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAAC,EAAE;QAAEQ,QAAQ,EAAEL;MAAa,CAAC,CAAC;IAC7E;IACA,OAAOH,IAAI;EACf;EACA,OAAOS,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;IAChC,IAAIC,EAAE;IACN,MAAMC,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC;IACjC,MAAMC,MAAM,GAAGL,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,mBAAmB;IACpF,MAAMC,KAAK,GAAGT,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACK,mBAAmB;IAC/D,MAAME,IAAI,GAAGV,IAAI,CAACM,YAAY,CAAC,CAAC;IAChC,MAAMK,MAAM,GAAGX,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACS,MAAM;IACnD,MAAMC,MAAM,GAAGb,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACS,MAAM,GAAGT,OAAO,CAACW,oBAAoB;IAClF,MAAMC,IAAI,GAAGf,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGH,OAAO,CAACS,MAAM,GAAGT,OAAO,CAACW,oBAAoB,GAAGX,OAAO,CAACa,UAAU;IACrG,MAAMC,aAAa,GAAGF,IAAI,IAAI,CAACb,EAAE,GAAGF,IAAI,CAACkB,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACjH,MAAMmB,MAAM,GAAGN,IAAI,GAAGV,MAAM;IAC5BN,GAAG,CAACuB,IAAI,CAAC,CAAC;IACVvB,GAAG,CAACwB,OAAO,CAAC9C,OAAO,CAAC+C,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACzCzB,GAAG,CAAC0B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACN,MAAM,CAAC,GAAG,IAAI,EAAEhB,MAAM,GAAGL,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAE3B,IAAI,CAAC;IACxE,MAAM4B,CAAC,GAAG5B,IAAI,GAAG,CAAC;IAClB,SAAS6B,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEC,EAAE,GAAGL,CAAC,EAAE;MACnC9B,GAAG,CAACoC,SAAS,CAAC,CAAC;MACfpC,GAAG,CAACqC,cAAc,CAACH,KAAK,CAAC;MACzBlC,GAAG,CAACsC,YAAY,CAACJ,KAAK,CAAC;MACvBlC,GAAG,CAACuC,YAAY,CAAC,CAAC,CAAC;MACnBvC,GAAG,CAACwC,MAAM,CAACR,EAAE,GAAG/B,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAEM,EAAE,CAAC;MACrCnC,GAAG,CAACyC,MAAM,CAACR,EAAE,GAAGhC,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAEM,EAAE,CAAC;MACrCnC,GAAG,CAAC+B,MAAM,CAAC,CAAC;IAChB;IACAA,MAAM,CAACzB,MAAM,EAAEI,KAAK,EAAE,KAAK,CAAC;IAC5BqB,MAAM,CAACrB,KAAK,EAAEC,IAAI,EAAE,MAAM,CAAC;IAC3BoB,MAAM,CAACpB,IAAI,EAAEC,MAAM,EAAE,OAAO,CAAC;IAC7BmB,MAAM,CAACnB,MAAM,EAAEE,MAAM,EAAE,MAAM,CAAC;IAC9BiB,MAAM,CAACjB,MAAM,EAAEE,IAAI,EAAE,KAAK,CAAC;IAC3Be,MAAM,CAACf,IAAI,EAAEE,aAAa,EAAE,MAAM,CAAC;IACnCa,MAAM,CAACzB,MAAM,GAAGL,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAEvB,MAAM,EAAE,MAAM,CAAC;IACjD3B,OAAO,CAACqB,GAAG,EAAEW,IAAI,GAAGV,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAEC,CAAC,EAAE,MAAM,CAAC;IAChD,MAAMY,gBAAgB,GAAGzC,IAAI,CAACkB,mBAAmB,CAAC,CAAC;IACnD,IAAIuB,gBAAgB,CAACC,UAAU,GAAG,CAAC,EAAE;MACjC,MAAMC,cAAc,GAAGF,gBAAgB,CAACG,KAAK,CAACC,SAAS;MACvD,MAAMC,MAAM,GAAGH,cAAc,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;MAC7C5C,GAAG,CAACsC,YAAY,CAAC,KAAK,CAAC;MACvBtC,GAAG,CAAC0B,QAAQ,CAACqB,MAAM,GAAGpB,IAAI,CAACC,KAAK,CAACgB,cAAc,CAAC,EAAEjC,IAAI,GAAGV,IAAI,CAAC4B,SAAS,CAAC,CAAC,EAAE3B,IAAI,GAAG,EAAE,CAAC;IACzF;IACAF,GAAG,CAACgD,OAAO,CAAC,CAAC;EACjB;EACA,OAAOC,aAAaA,CAACC,cAAc,EAAE;IACjC,IAAI,CAACA,cAAc,EAAE;MACjB,OAAO1D,SAAS;IACpB;IACA,MAAM2D,MAAM,GAAG,kCAAkC;IACjD,MAAMC,MAAM,GAAGD,MAAM,CAACE,IAAI,CAACH,cAAc,CAAC;IAC1C,IAAI,CAACE,MAAM,EAAE;MACT,OAAO5D,SAAS;IACpB;IACA,MAAML,QAAQ,GAAGiE,MAAM,CAAC,CAAC,CAAC;IAC1B,MAAME,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACG,MAAM;IAC7B,MAAMnE,IAAI,GAAGgE,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG;IAC7B,OAAO;MAAEjE,QAAQ;MAAEmE,IAAI;MAAElE;IAAK,CAAC;EACnC;EACA,OAAOoE,eAAeA,CAACC,UAAU,EAAE;IAC/B,MAAMC,aAAa,GAAG1E,IAAI,CAACiE,aAAa,CAACQ,UAAU,CAACtE,QAAQ,CAAC;IAC7D,IAAI,CAACuE,aAAa,EAAE;MAChB,OAAOlE,SAAS;IACpB;IACA,IAAIJ,IAAI,GAAGqE,UAAU,CAACrE,IAAI;IAC1B,IAAIA,IAAI,IAAI,CAACR,MAAM,CAAC+E,UAAU,CAACvE,IAAI,CAAC,EAAE;MAClC,OAAOI,SAAS;IACpB;IACA,MAAMoE,WAAW,GAAG,EAAE;IACtB,IAAI,CAACxE,IAAI,EAAE;MACPA,IAAI,GAAGsE,aAAa,CAACtE,IAAI,IAAI,GAAG;MAChC,IAAIqE,UAAU,CAACI,IAAI,KAAKrE,SAAS,EAAE;QAC/BiE,UAAU,CAACI,IAAI,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UAC9B,MAAMZ,MAAM,GAAGW,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;UAC3BL,WAAW,CAACI,CAAC,CAAC,GAAIZ,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,GAAGH,MAAM,CAAC,CAAC,CAAC,GAAGhE,IAAK;QACvE,CAAC,CAAC;MACN;IACJ;IACA,IAAI8E,KAAK,GAAGtF,MAAM,CAACuF,eAAe,CAACT,aAAa,CAACvE,QAAQ,CAAC;IAC1D,IAAI,CAAC+E,KAAK,EAAE;MACR,OAAO1E,SAAS;IACpB;IACA,MAAM8D,IAAI,GAAGG,UAAU,CAACH,IAAI,GAAGG,UAAU,CAACH,IAAI,GAAGI,aAAa,CAACJ,IAAI;IACnE,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1B,OAAO9D,SAAS;IACpB;IACA,IAAI4E,YAAY,GAAGF,KAAK;IACxB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,EAAEU,CAAC,EAAE,EAAE;MAC3B,IAAII,YAAY,IAAI,CAAC,EACjB,OAAO5E,SAAS;MACpB4E,YAAY,GAAGA,YAAY,GAAG,CAAC;MAC/BF,KAAK,IAAIE,YAAY;IACzB;IACA,OAAO;MACHjF,QAAQ,EAAEuE,aAAa,CAACvE,QAAQ;MAChCC,IAAI;MACJwE,WAAW;MACXN,IAAI;MACJY;IACJ,CAAC;EACL;EACAG,WAAWA,CAACZ,UAAU,EAAE;IACpB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,UAAU,EAAE;MACb,MAAM,IAAI1E,YAAY,CAAC,cAAc,EAAE,yEAAyE,CAAC;IACrH;IACA,MAAMuF,gBAAgB,GAAGtF,IAAI,CAACwE,eAAe,CAACC,UAAU,CAAC;IACzD,IAAI,CAACa,gBAAgB,EAAE;MACnB,MAAM,IAAIvF,YAAY,CAAC,cAAc,EAAE,uCAAuCwF,IAAI,CAACC,SAAS,CAACf,UAAU,CAAC,EAAE,CAAC;IAC/G;IACA,IAAI,CAACI,IAAI,GAAGJ,UAAU,CAACI,IAAI,IAAI,EAAE;IACjC,IAAI,CAACY,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACtF,QAAQ,GAAGmF,gBAAgB,CAACnF,QAAQ;IACzC,IAAI,CAACuF,QAAQ,GAAGJ,gBAAgB,CAAClF,IAAI;IACrC,IAAI,CAACwE,WAAW,GAAGU,gBAAgB,CAACV,WAAW;IAC/C,IAAIH,UAAU,CAACkB,gBAAgB,EAAE;MAC7B,IAAI,CAACC,WAAW,CAACnB,UAAU,CAACkB,gBAAgB,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACE,iBAAiB,CAACP,gBAAgB,CAACJ,KAAK,CAAC;IAClD;IACA,IAAI,CAACY,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG/F,IAAI,CAACE,aAAa,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACuF,QAAQ,CAAC;IAClE,IAAI,CAACM,YAAY,GAAG,IAAI,CAACpB,WAAW,CAACqB,GAAG,CAAEC,CAAC,IAAKlG,IAAI,CAACE,aAAa,CAAC,IAAI,CAACC,QAAQ,EAAE+F,CAAC,CAAC,CAAC;IACrF,IAAI,CAACC,QAAQ,GAAG3F,SAAS;IACzB,IAAI,CAAC4F,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAAC5E,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACM,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACuE,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ,IAAI9B,UAAU,CAAC+B,WAAW,EAAE;MACxB,IAAI,CAACC,kBAAkB,CAAChC,UAAU,CAAC+B,WAAW,CAAC;IACnD;IACA,IAAI,CAACE,aAAa,GAAG;MACjBC,iBAAiB,EAAE,CAAC;MACpBC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACZ,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACX,QAAQ;EACxB;EACAY,WAAWA,CAAC9F,IAAI,EAAE;IACd,IAAI,CAACkF,QAAQ,GAAGlF,IAAI;IACpB,OAAO,IAAI;EACf;EACA+F,MAAMA,CAAA,EAAG;IACL,OAAO,KAAK;EAChB;EACAC,SAASA,CAACC,KAAK,EAAEnE,MAAM,EAAE;IACrBA,MAAM,CAACoE,OAAO,CAAC,IAAI,CAAC;IACpBpE,MAAM,CAACqE,QAAQ,CAACF,KAAK,CAAC;IACtB,IAAI,CAACpB,SAAS,CAACuB,IAAI,CAACtE,MAAM,CAAC;IAC3B,IAAI,CAACuE,YAAY,GAAG,KAAK;IACzB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO3H,OAAO,CAAC,IAAI,CAAC0H,KAAK,EAAE,SAAS,EAAE,gCAAgC,CAAC;EAC3E;EACAE,QAAQA,CAACF,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,KAAK,CAAC,CAACH,KAAK,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,CAACC,UAAU,CAAC,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC,CAAC,CAAC;IACxC,OAAO,IAAI;EACf;EACAC,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACtG,mBAAmB;EACnC;EACAuG,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACjG,oBAAoB;EACpC;EACAkG,sBAAsBA,CAACC,CAAC,EAAE;IACtB,IAAI,CAACzG,mBAAmB,GAAGyG,CAAC;IAC5B,OAAO,IAAI;EACf;EACAC,uBAAuBA,CAACD,CAAC,EAAE;IACvB,IAAI,CAACnG,oBAAoB,GAAGmG,CAAC;IAC7B,OAAO,IAAI;EACf;EACAE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAChC,WAAW;EAC3B;EACAiC,aAAaA,CAACC,SAAS,EAAE;IACrB,OAAO,CAAC;EACZ;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,CAAC;EACZ;EACArI,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC6F,UAAU;EAC1B;EACAyC,aAAaA,CAAA,EAAG;IACZ,OAAO,CAAC;EACZ;EACAb,KAAKA,CAACpB,EAAE,EAAE;IACN,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,OAAO,IAAI;EACf;EACAkC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAClC,EAAE,CAAChC,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIxE,YAAY,CAAC,WAAW,EAAE,uCAAuC,CAAC;IAChF;IACA,OAAO,IAAI,CAACwG,EAAE;EAClB;EACAmC,cAAcA,CAACC,QAAQ,EAAE;IACrB,OAAO,IAAI,CAAClB,UAAU,CAAC,CAAC,CAACiB,cAAc,CAACC,QAAQ,CAAC;EACrD;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACC,KAAK,EACX,MAAM,IAAI9I,YAAY,CAAC,SAAS,EAAE,oBAAoB,CAAC;IAC3D,OAAO,IAAI,CAAC8I,KAAK;EACrB;EACAC,QAAQA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACvB,YAAY,GAAG,KAAK;IACzB,OAAO,IAAI;EACf;EACAyB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAClC;EACAC,cAAcA,CAACC,EAAE,EAAE;IACf,IAAI,CAACC,WAAW,GAAGD,EAAE;IACrB,IAAI,CAAC5B,YAAY,GAAG,KAAK;IACzB,OAAO,IAAI;EACf;EACA8B,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjJ,QAAQ;EACxB;EACAkJ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,kBAAkB,CAAC,KAAK,CAAC,CAAC/E,MAAM,GAAG,CAAC;EACpD;EACAgF,OAAOA,CAAA,EAAG;IACN,OAAO,KAAK;EAChB;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9D,QAAQ;EACxB;EACA+D,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,OAAO7J,OAAO,CAAC,IAAI,CAAC4J,IAAI,EAAE,QAAQ,EAAE,8BAA8B,CAAC;EACvE;EACAE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACF,IAAI,KAAKlJ,SAAS;EAClC;EACAqJ,OAAOA,CAACH,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAI,WAAWA,CAACC,QAAQ,EAAE7C,KAAK,GAAG,CAAC,EAAE;IAC7B,MAAM8C,SAAS,GAAG,uDAAuD;IACzE,IAAI,OAAO9C,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAG+C,QAAQ,CAAC/C,KAAK,CAAC;MACvBgD,OAAO,CAACC,IAAI,CAACH,SAAS,GAAG,wDAAwD,CAAC;IACtF;IACA,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAI,OAAO7C,KAAK,KAAK,QAAQ,EAAE;MAC3D,MAAM,IAAInH,YAAY,CAAC,aAAa,EAAE,gCAAgC,GAAGiK,SAAS,GAAG,WAAW,CAAC;IACrG;IACAD,QAAQ,CAAC5C,OAAO,CAAC,IAAI,CAAC;IACtB4C,QAAQ,CAAC3C,QAAQ,CAACF,KAAK,CAAC;IACxB,KAAK,CAAC4C,WAAW,CAACC,QAAQ,CAAC;IAC3B,OAAO,IAAI;EACf;EACAT,kBAAkBA,CAAClJ,IAAI,EAAE;IACrB,OAAO,IAAI,CAAC0F,SAAS,CAACsE,MAAM,CAAEL,QAAQ,IAAKA,QAAQ,CAACM,WAAW,CAAC,CAAC,KAAKjK,IAAI,CAAC;EAC/E;EACAkK,kBAAkBA,CAACC,QAAQ,EAAErD,KAAK,EAAEsD,OAAO,EAAE;IACzC,IAAI,CAAC,IAAI,CAAClD,YAAY,EAAE;MACpB,MAAM,IAAIvH,YAAY,CAAC,iBAAiB,EAAE,sDAAsD,CAAC;IACrG;IACA,OAAO;MACHmI,CAAC,EAAE,IAAI,CAAC3G,YAAY,CAAC,CAAC;MACtBuB,CAAC,EAAE,IAAI,CAACyD,EAAE,CAAC,CAAC;IAChB,CAAC;EACL;EACAkE,qBAAqBA,CAACvD,KAAK,EAAE;IACzB,MAAMwD,KAAK,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACzD,KAAK,CAAC;IACvC,OAAOwD,KAAK,CAACE,SAAS,GAAG,IAAI,CAAC5C,uBAAuB,CAAC,CAAC,GAAG,CAAC;EAC/D;EACA6C,oBAAoBA,CAAC3D,KAAK,EAAE;IACxB,MAAMwD,KAAK,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACzD,KAAK,CAAC;IACvC,OAAOwD,KAAK,CAACE,SAAS,GAAG,IAAI,CAAC7C,sBAAsB,CAAC,CAAC,GAAG,IAAI,CAACzB,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EACvF;EACAwE,aAAaA,CAAA,EAAG;IACZ,IAAIC,EAAE,GAAG,IAAI,CAAC/C,uBAAuB,CAAC,CAAC;IACvC,MAAMgD,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAACC,UAAU,CAAC,aAAa,CAAC;IACzE,IAAIF,WAAW,CAACzG,MAAM,KAAK,CAAC,EAAE;MAC1BwG,EAAE,IAAIC,WAAW,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC;IACvC;IACA,OAAOJ,EAAE;EACb;EACA1J,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACiG,YAAY,EAAE;MACpB,MAAM,IAAIvH,YAAY,CAAC,iBAAiB,EAAE,+CAA+C,CAAC;IAC9F;IACA,MAAMyB,SAAS,GAAG,IAAI,CAAC4J,eAAe,GAAG,IAAI,CAACA,eAAe,CAACC,QAAQ,CAAC,CAAC,CAACC,SAAS,GAAG,CAAC;IACtF,MAAMrJ,UAAU,GAAG,IAAI,CAACmJ,eAAe,GAAG,IAAI,CAACA,eAAe,CAACC,QAAQ,CAAC,CAAC,CAACE,UAAU,GAAG,CAAC;IACxF,MAAMlF,KAAK,GAAG,IAAI,CAAC8E,QAAQ,CAAC,CAAC;IAC7B,MAAMK,UAAU,GAAG,IAAI,CAAChD,aAAa,CAAC,CAAC;IACvC,MAAM3G,MAAM,GAAGwE,KAAK,GAChB7E,SAAS,GACTS,UAAU,GACV,IAAI,CAACR,mBAAmB,GACxB,IAAI,CAACM,oBAAoB;IAC7B,OAAO;MACHsE,KAAK;MACLmF,UAAU;MACV3J,MAAM;MACNL,SAAS;MACTS,UAAU;MACVR,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CM,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/C0J,OAAO,EAAE;IACb,CAAC;EACL;EACAlK,YAAYA,CAAA,EAAG;IACX,MAAM4H,WAAW,GAAG,IAAI,CAACH,gBAAgB,CAAC,6CAA6C,CAAC;IACxF,IAAId,CAAC,GAAGiB,WAAW,CAACuC,IAAI,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAClE,KAAK,EAAE;MACZU,CAAC,IAAI,IAAI,CAACV,KAAK,CAACmE,aAAa,CAAC,CAAC,GAAGjM,OAAO,CAAC+C,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACrE;IACA,IAAI,IAAI,CAACmJ,eAAe,CAAC,CAAC,EAAE;MACxB1D,CAAC,IAAI,IAAI,CAAC2D,eAAe,CAAC,CAAC;IAC/B;IACA,OAAO3D,CAAC;EACZ;EACA4D,gBAAgBA,CAAA,EAAG;IACf,MAAM,IAAI/L,YAAY,CAAC,QAAQ,EAAE,gCAAgC,CAAC;EACtE;EACAgM,cAAcA,CAAA,EAAG;IACb,MAAM,IAAIhM,YAAY,CAAC,QAAQ,EAAE,gCAAgC,CAAC;EACtE;EACAiM,YAAYA,CAAA,EAAG;IACX,IAAIC,SAAS,GAAG,IAAI,CAAC1K,YAAY,CAAC,CAAC;IACnC,MAAM2K,cAAc,GAAG,IAAI,CAAC1D,aAAa,CAAC,CAAC;IAC3CyD,SAAS,IAAIC,cAAc,GAAG,CAAC;IAC/BD,SAAS,IAAI,CAAC,IAAI,CAAC5F,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;IAC7C,OAAO4F,SAAS;EACpB;EACAE,WAAWA,CAAA,EAAG;IACV,IAAIC,OAAO,GAAG,IAAI,CAAC7K,YAAY,CAAC,CAAC;IACjC,MAAM2K,cAAc,GAAG,IAAI,CAAC1D,aAAa,CAAC,CAAC;IAC3C4D,OAAO,IAAIF,cAAc,GAAG,CAAC;IAC7BE,OAAO,IAAI,IAAI,CAAC/F,KAAK,GAAG,CAAC,GAAG,CAAC;IAC7B,OAAO+F,OAAO;EAClB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACxH,IAAI;EACpB;EACA8F,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClF,QAAQ;EACxB;EACA6G,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,KAAK,CAACD,cAAc,CAAC,CAAC;IAC1C,KAAK,IAAItH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACc,SAAS,CAACvB,MAAM,EAAES,CAAC,EAAE,EAAE;MAC5CuH,WAAW,CAACC,SAAS,CAAC,IAAI,CAAC1G,SAAS,CAACd,CAAC,CAAC,CAACsH,cAAc,CAAC,CAAC,CAAC;IAC7D;IACA,OAAOC,WAAW;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}