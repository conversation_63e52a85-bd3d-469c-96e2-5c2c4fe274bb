{"ast": null, "code": "import { Element } from './element.js';\nimport { Tables } from './tables.js';\nimport { TextJustification } from './textnote.js';\nimport { RuntimeError } from './util.js';\nfunction drawArrowHead(ctx, x0, y0, x1, y1, x2, y2) {\n  ctx.beginPath();\n  ctx.moveTo(x0, y0);\n  ctx.lineTo(x1, y1);\n  ctx.lineTo(x2, y2);\n  ctx.lineTo(x0, y0);\n  ctx.closePath();\n  ctx.fill();\n}\nexport class StaveLine extends Element {\n  static get CATEGORY() {\n    return \"StaveLine\";\n  }\n  constructor(notes) {\n    super();\n    this.setNotes(notes);\n    this.text = '';\n    this.renderOptions = {\n      paddingLeft: 4,\n      paddingRight: 3,\n      lineWidth: 1,\n      lineDash: undefined,\n      roundedEnd: true,\n      color: undefined,\n      drawStartArrow: false,\n      drawEndArrow: false,\n      arrowheadLength: 10,\n      arrowheadAngle: Math.PI / 8,\n      textPositionVertical: StaveLine.TextVerticalPosition.TOP,\n      textJustification: StaveLine.TextJustification.CENTER\n    };\n  }\n  setText(text) {\n    this.text = text;\n    return this;\n  }\n  setNotes(notes) {\n    if (!notes.firstNote && !notes.lastNote) {\n      throw new RuntimeError('BadArguments', 'Notes needs to have either firstNote or lastNote set.');\n    }\n    if (!notes.firstIndexes) notes.firstIndexes = [0];\n    if (!notes.lastIndexes) notes.lastIndexes = [0];\n    if (notes.firstIndexes.length !== notes.lastIndexes.length) {\n      throw new RuntimeError('BadArguments', 'Connected notes must have same number of indexes.');\n    }\n    this.notes = notes;\n    this.firstNote = notes.firstNote;\n    this.firstIndexes = notes.firstIndexes;\n    this.lastNote = notes.lastNote;\n    this.lastIndexes = notes.lastIndexes;\n    return this;\n  }\n  applyLineStyle() {\n    const ctx = this.checkContext();\n    const renderOptions = this.renderOptions;\n    if (renderOptions.lineDash) {\n      ctx.setLineDash(renderOptions.lineDash);\n    }\n    if (renderOptions.lineWidth) {\n      ctx.setLineWidth(renderOptions.lineWidth);\n    }\n    if (renderOptions.roundedEnd) {\n      ctx.setLineCap('round');\n    } else {\n      ctx.setLineCap('square');\n    }\n  }\n  drawArrowLine(ctx, pt1, pt2) {\n    const bothArrows = this.renderOptions.drawStartArrow && this.renderOptions.drawEndArrow;\n    const x1 = pt1.x;\n    const y1 = pt1.y;\n    const x2 = pt2.x;\n    const y2 = pt2.y;\n    const distance = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n    const ratio = (distance - this.renderOptions.arrowheadLength / 3) / distance;\n    let endX;\n    let endY;\n    let startX;\n    let startY;\n    if (this.renderOptions.drawEndArrow || bothArrows) {\n      endX = Math.round(x1 + (x2 - x1) * ratio);\n      endY = Math.round(y1 + (y2 - y1) * ratio);\n    } else {\n      endX = x2;\n      endY = y2;\n    }\n    if (this.renderOptions.drawStartArrow || bothArrows) {\n      startX = x1 + (x2 - x1) * (1 - ratio);\n      startY = y1 + (y2 - y1) * (1 - ratio);\n    } else {\n      startX = x1;\n      startY = y1;\n    }\n    if (this.renderOptions.color) {\n      ctx.setStrokeStyle(this.renderOptions.color);\n      ctx.setFillStyle(this.renderOptions.color);\n    }\n    ctx.beginPath();\n    ctx.moveTo(startX, startY);\n    ctx.lineTo(endX, endY);\n    ctx.stroke();\n    ctx.closePath();\n    const lineAngle = Math.atan2(y2 - y1, x2 - x1);\n    const h = Math.abs(this.renderOptions.arrowheadLength / Math.cos(this.renderOptions.arrowheadAngle));\n    let angle1;\n    let angle2;\n    let topX;\n    let topY;\n    let bottomX;\n    let bottomY;\n    if (this.renderOptions.drawEndArrow || bothArrows) {\n      angle1 = lineAngle + Math.PI + this.renderOptions.arrowheadAngle;\n      topX = x2 + Math.cos(angle1) * h;\n      topY = y2 + Math.sin(angle1) * h;\n      angle2 = lineAngle + Math.PI - this.renderOptions.arrowheadAngle;\n      bottomX = x2 + Math.cos(angle2) * h;\n      bottomY = y2 + Math.sin(angle2) * h;\n      drawArrowHead(ctx, topX, topY, x2, y2, bottomX, bottomY);\n    }\n    if (this.renderOptions.drawStartArrow || bothArrows) {\n      angle1 = lineAngle + this.renderOptions.arrowheadAngle;\n      topX = x1 + Math.cos(angle1) * h;\n      topY = y1 + Math.sin(angle1) * h;\n      angle2 = lineAngle - this.renderOptions.arrowheadAngle;\n      bottomX = x1 + Math.cos(angle2) * h;\n      bottomY = y1 + Math.sin(angle2) * h;\n      drawArrowHead(ctx, topX, topY, x1, y1, bottomX, bottomY);\n    }\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    const firstNote = this.firstNote;\n    const lastNote = this.lastNote;\n    const renderOptions = this.renderOptions;\n    this.applyLineStyle();\n    let startPosition = {\n      x: 0,\n      y: 0\n    };\n    let endPosition = {\n      x: 0,\n      y: 0\n    };\n    this.firstIndexes.forEach((firstIndex, i) => {\n      const lastIndex = this.lastIndexes[i];\n      startPosition = firstNote.getModifierStartXY(2, firstIndex);\n      endPosition = lastNote.getModifierStartXY(1, lastIndex);\n      const upwardsSlope = startPosition.y > endPosition.y;\n      startPosition.x += firstNote.getMetrics().modRightPx + renderOptions.paddingLeft;\n      endPosition.x -= lastNote.getMetrics().modLeftPx + renderOptions.paddingRight;\n      const noteheadWidth = firstNote.getGlyphWidth();\n      const firstDisplaced = firstNote.getKeyProps()[firstIndex].displaced;\n      if (firstDisplaced && firstNote.getStemDirection() === 1) {\n        startPosition.x += noteheadWidth + renderOptions.paddingLeft;\n      }\n      const lastDisplaced = lastNote.getKeyProps()[lastIndex].displaced;\n      if (lastDisplaced && lastNote.getStemDirection() === -1) {\n        endPosition.x -= noteheadWidth + renderOptions.paddingRight;\n      }\n      startPosition.y += upwardsSlope ? -3 : 1;\n      endPosition.y += upwardsSlope ? 2 : 0;\n      this.drawArrowLine(ctx, startPosition, endPosition);\n    });\n    const textWidth = this.width;\n    const justification = renderOptions.textJustification;\n    let x = 0;\n    if (justification === StaveLine.TextJustification.LEFT) {\n      x = startPosition.x;\n    } else if (justification === StaveLine.TextJustification.CENTER) {\n      const deltaX = endPosition.x - startPosition.x;\n      const centerX = deltaX / 2 + startPosition.x;\n      x = centerX - textWidth / 2;\n    } else if (justification === StaveLine.TextJustification.RIGHT) {\n      x = endPosition.x - textWidth;\n    }\n    let y = 0;\n    const verticalPosition = renderOptions.textPositionVertical;\n    if (verticalPosition === StaveLine.TextVerticalPosition.TOP) {\n      y = firstNote.checkStave().getYForTopText();\n    } else if (verticalPosition === StaveLine.TextVerticalPosition.BOTTOM) {\n      y = firstNote.checkStave().getYForBottomText(Tables.TEXT_HEIGHT_OFFSET_HACK);\n    }\n    const color = renderOptions.color;\n    this.applyStyle(ctx, {\n      fillStyle: color,\n      strokeStyle: color\n    });\n    this.renderText(ctx, x, y);\n  }\n}\nStaveLine.TextVerticalPosition = {\n  TOP: 1,\n  BOTTOM: 2\n};\nStaveLine.TextJustification = TextJustification;", "map": {"version": 3, "names": ["Element", "Tables", "TextJustification", "RuntimeError", "drawArrowHead", "ctx", "x0", "y0", "x1", "y1", "x2", "y2", "beginPath", "moveTo", "lineTo", "closePath", "fill", "StaveLine", "CATEGORY", "constructor", "notes", "setNotes", "text", "renderOptions", "paddingLeft", "paddingRight", "lineWidth", "lineDash", "undefined", "roundedEnd", "color", "drawStartArrow", "drawEndArrow", "arrowheadLength", "arrowheadAngle", "Math", "PI", "textPositionVertical", "TextVerticalPosition", "TOP", "textJustification", "CENTER", "setText", "firstNote", "lastNote", "firstIndexes", "lastIndexes", "length", "applyLineStyle", "checkContext", "setLineDash", "setLineWidth", "setLineCap", "drawArrowLine", "pt1", "pt2", "bothArrows", "x", "y", "distance", "sqrt", "ratio", "endX", "endY", "startX", "startY", "round", "setStrokeStyle", "setFillStyle", "stroke", "lineAngle", "atan2", "h", "abs", "cos", "angle1", "angle2", "topX", "topY", "bottomX", "bottomY", "sin", "draw", "setRendered", "startPosition", "endPosition", "for<PERSON>ach", "firstIndex", "i", "lastIndex", "getModifierStartXY", "upwardsSlope", "getMetrics", "modRightPx", "modLeftPx", "noteheadWidth", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstDisplaced", "getKeyProps", "displaced", "getStemDirection", "lastDisplaced", "textWidth", "width", "justification", "LEFT", "deltaX", "centerX", "RIGHT", "verticalPosition", "checkStave", "getYForTopText", "BOTTOM", "getYForBottomText", "TEXT_HEIGHT_OFFSET_HACK", "applyStyle", "fillStyle", "strokeStyle", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/staveline.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Tables } from './tables.js';\nimport { TextJustification } from './textnote.js';\nimport { RuntimeError } from './util.js';\nfunction drawArrowHead(ctx, x0, y0, x1, y1, x2, y2) {\n    ctx.beginPath();\n    ctx.moveTo(x0, y0);\n    ctx.lineTo(x1, y1);\n    ctx.lineTo(x2, y2);\n    ctx.lineTo(x0, y0);\n    ctx.closePath();\n    ctx.fill();\n}\nexport class StaveLine extends Element {\n    static get CATEGORY() {\n        return \"StaveLine\";\n    }\n    constructor(notes) {\n        super();\n        this.setNotes(notes);\n        this.text = '';\n        this.renderOptions = {\n            paddingLeft: 4,\n            paddingRight: 3,\n            lineWidth: 1,\n            lineDash: undefined,\n            roundedEnd: true,\n            color: undefined,\n            drawStartArrow: false,\n            drawEndArrow: false,\n            arrowheadLength: 10,\n            arrowheadAngle: Math.PI / 8,\n            textPositionVertical: StaveLine.TextVerticalPosition.TOP,\n            textJustification: StaveLine.TextJustification.CENTER,\n        };\n    }\n    setText(text) {\n        this.text = text;\n        return this;\n    }\n    setNotes(notes) {\n        if (!notes.firstNote && !notes.lastNote) {\n            throw new RuntimeError('BadArguments', 'Notes needs to have either firstNote or lastNote set.');\n        }\n        if (!notes.firstIndexes)\n            notes.firstIndexes = [0];\n        if (!notes.lastIndexes)\n            notes.lastIndexes = [0];\n        if (notes.firstIndexes.length !== notes.lastIndexes.length) {\n            throw new RuntimeError('BadArguments', 'Connected notes must have same number of indexes.');\n        }\n        this.notes = notes;\n        this.firstNote = notes.firstNote;\n        this.firstIndexes = notes.firstIndexes;\n        this.lastNote = notes.lastNote;\n        this.lastIndexes = notes.lastIndexes;\n        return this;\n    }\n    applyLineStyle() {\n        const ctx = this.checkContext();\n        const renderOptions = this.renderOptions;\n        if (renderOptions.lineDash) {\n            ctx.setLineDash(renderOptions.lineDash);\n        }\n        if (renderOptions.lineWidth) {\n            ctx.setLineWidth(renderOptions.lineWidth);\n        }\n        if (renderOptions.roundedEnd) {\n            ctx.setLineCap('round');\n        }\n        else {\n            ctx.setLineCap('square');\n        }\n    }\n    drawArrowLine(ctx, pt1, pt2) {\n        const bothArrows = this.renderOptions.drawStartArrow && this.renderOptions.drawEndArrow;\n        const x1 = pt1.x;\n        const y1 = pt1.y;\n        const x2 = pt2.x;\n        const y2 = pt2.y;\n        const distance = Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));\n        const ratio = (distance - this.renderOptions.arrowheadLength / 3) / distance;\n        let endX;\n        let endY;\n        let startX;\n        let startY;\n        if (this.renderOptions.drawEndArrow || bothArrows) {\n            endX = Math.round(x1 + (x2 - x1) * ratio);\n            endY = Math.round(y1 + (y2 - y1) * ratio);\n        }\n        else {\n            endX = x2;\n            endY = y2;\n        }\n        if (this.renderOptions.drawStartArrow || bothArrows) {\n            startX = x1 + (x2 - x1) * (1 - ratio);\n            startY = y1 + (y2 - y1) * (1 - ratio);\n        }\n        else {\n            startX = x1;\n            startY = y1;\n        }\n        if (this.renderOptions.color) {\n            ctx.setStrokeStyle(this.renderOptions.color);\n            ctx.setFillStyle(this.renderOptions.color);\n        }\n        ctx.beginPath();\n        ctx.moveTo(startX, startY);\n        ctx.lineTo(endX, endY);\n        ctx.stroke();\n        ctx.closePath();\n        const lineAngle = Math.atan2(y2 - y1, x2 - x1);\n        const h = Math.abs(this.renderOptions.arrowheadLength / Math.cos(this.renderOptions.arrowheadAngle));\n        let angle1;\n        let angle2;\n        let topX;\n        let topY;\n        let bottomX;\n        let bottomY;\n        if (this.renderOptions.drawEndArrow || bothArrows) {\n            angle1 = lineAngle + Math.PI + this.renderOptions.arrowheadAngle;\n            topX = x2 + Math.cos(angle1) * h;\n            topY = y2 + Math.sin(angle1) * h;\n            angle2 = lineAngle + Math.PI - this.renderOptions.arrowheadAngle;\n            bottomX = x2 + Math.cos(angle2) * h;\n            bottomY = y2 + Math.sin(angle2) * h;\n            drawArrowHead(ctx, topX, topY, x2, y2, bottomX, bottomY);\n        }\n        if (this.renderOptions.drawStartArrow || bothArrows) {\n            angle1 = lineAngle + this.renderOptions.arrowheadAngle;\n            topX = x1 + Math.cos(angle1) * h;\n            topY = y1 + Math.sin(angle1) * h;\n            angle2 = lineAngle - this.renderOptions.arrowheadAngle;\n            bottomX = x1 + Math.cos(angle2) * h;\n            bottomY = y1 + Math.sin(angle2) * h;\n            drawArrowHead(ctx, topX, topY, x1, y1, bottomX, bottomY);\n        }\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        const firstNote = this.firstNote;\n        const lastNote = this.lastNote;\n        const renderOptions = this.renderOptions;\n        this.applyLineStyle();\n        let startPosition = { x: 0, y: 0 };\n        let endPosition = { x: 0, y: 0 };\n        this.firstIndexes.forEach((firstIndex, i) => {\n            const lastIndex = this.lastIndexes[i];\n            startPosition = firstNote.getModifierStartXY(2, firstIndex);\n            endPosition = lastNote.getModifierStartXY(1, lastIndex);\n            const upwardsSlope = startPosition.y > endPosition.y;\n            startPosition.x += firstNote.getMetrics().modRightPx + renderOptions.paddingLeft;\n            endPosition.x -= lastNote.getMetrics().modLeftPx + renderOptions.paddingRight;\n            const noteheadWidth = firstNote.getGlyphWidth();\n            const firstDisplaced = firstNote.getKeyProps()[firstIndex].displaced;\n            if (firstDisplaced && firstNote.getStemDirection() === 1) {\n                startPosition.x += noteheadWidth + renderOptions.paddingLeft;\n            }\n            const lastDisplaced = lastNote.getKeyProps()[lastIndex].displaced;\n            if (lastDisplaced && lastNote.getStemDirection() === -1) {\n                endPosition.x -= noteheadWidth + renderOptions.paddingRight;\n            }\n            startPosition.y += upwardsSlope ? -3 : 1;\n            endPosition.y += upwardsSlope ? 2 : 0;\n            this.drawArrowLine(ctx, startPosition, endPosition);\n        });\n        const textWidth = this.width;\n        const justification = renderOptions.textJustification;\n        let x = 0;\n        if (justification === StaveLine.TextJustification.LEFT) {\n            x = startPosition.x;\n        }\n        else if (justification === StaveLine.TextJustification.CENTER) {\n            const deltaX = endPosition.x - startPosition.x;\n            const centerX = deltaX / 2 + startPosition.x;\n            x = centerX - textWidth / 2;\n        }\n        else if (justification === StaveLine.TextJustification.RIGHT) {\n            x = endPosition.x - textWidth;\n        }\n        let y = 0;\n        const verticalPosition = renderOptions.textPositionVertical;\n        if (verticalPosition === StaveLine.TextVerticalPosition.TOP) {\n            y = firstNote.checkStave().getYForTopText();\n        }\n        else if (verticalPosition === StaveLine.TextVerticalPosition.BOTTOM) {\n            y = firstNote.checkStave().getYForBottomText(Tables.TEXT_HEIGHT_OFFSET_HACK);\n        }\n        const color = renderOptions.color;\n        this.applyStyle(ctx, { fillStyle: color, strokeStyle: color });\n        this.renderText(ctx, x, y);\n    }\n}\nStaveLine.TextVerticalPosition = {\n    TOP: 1,\n    BOTTOM: 2,\n};\nStaveLine.TextJustification = TextJustification;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,iBAAiB,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,aAAaA,CAACC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAChDN,GAAG,CAACO,SAAS,CAAC,CAAC;EACfP,GAAG,CAACQ,MAAM,CAACP,EAAE,EAAEC,EAAE,CAAC;EAClBF,GAAG,CAACS,MAAM,CAACN,EAAE,EAAEC,EAAE,CAAC;EAClBJ,GAAG,CAACS,MAAM,CAACJ,EAAE,EAAEC,EAAE,CAAC;EAClBN,GAAG,CAACS,MAAM,CAACR,EAAE,EAAEC,EAAE,CAAC;EAClBF,GAAG,CAACU,SAAS,CAAC,CAAC;EACfV,GAAG,CAACW,IAAI,CAAC,CAAC;AACd;AACA,OAAO,MAAMC,SAAS,SAASjB,OAAO,CAAC;EACnC,WAAWkB,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACAC,WAAWA,CAACC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC;IACpB,IAAI,CAACE,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,aAAa,GAAG;MACjBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAEC,SAAS;MACnBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAEF,SAAS;MAChBG,cAAc,EAAE,KAAK;MACrBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE,EAAE;MACnBC,cAAc,EAAEC,IAAI,CAACC,EAAE,GAAG,CAAC;MAC3BC,oBAAoB,EAAEpB,SAAS,CAACqB,oBAAoB,CAACC,GAAG;MACxDC,iBAAiB,EAAEvB,SAAS,CAACf,iBAAiB,CAACuC;IACnD,CAAC;EACL;EACAC,OAAOA,CAACpB,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAD,QAAQA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,CAACuB,SAAS,IAAI,CAACvB,KAAK,CAACwB,QAAQ,EAAE;MACrC,MAAM,IAAIzC,YAAY,CAAC,cAAc,EAAE,uDAAuD,CAAC;IACnG;IACA,IAAI,CAACiB,KAAK,CAACyB,YAAY,EACnBzB,KAAK,CAACyB,YAAY,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACzB,KAAK,CAAC0B,WAAW,EAClB1B,KAAK,CAAC0B,WAAW,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAI1B,KAAK,CAACyB,YAAY,CAACE,MAAM,KAAK3B,KAAK,CAAC0B,WAAW,CAACC,MAAM,EAAE;MACxD,MAAM,IAAI5C,YAAY,CAAC,cAAc,EAAE,mDAAmD,CAAC;IAC/F;IACA,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuB,SAAS,GAAGvB,KAAK,CAACuB,SAAS;IAChC,IAAI,CAACE,YAAY,GAAGzB,KAAK,CAACyB,YAAY;IACtC,IAAI,CAACD,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ;IAC9B,IAAI,CAACE,WAAW,GAAG1B,KAAK,CAAC0B,WAAW;IACpC,OAAO,IAAI;EACf;EACAE,cAAcA,CAAA,EAAG;IACb,MAAM3C,GAAG,GAAG,IAAI,CAAC4C,YAAY,CAAC,CAAC;IAC/B,MAAM1B,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,IAAIA,aAAa,CAACI,QAAQ,EAAE;MACxBtB,GAAG,CAAC6C,WAAW,CAAC3B,aAAa,CAACI,QAAQ,CAAC;IAC3C;IACA,IAAIJ,aAAa,CAACG,SAAS,EAAE;MACzBrB,GAAG,CAAC8C,YAAY,CAAC5B,aAAa,CAACG,SAAS,CAAC;IAC7C;IACA,IAAIH,aAAa,CAACM,UAAU,EAAE;MAC1BxB,GAAG,CAAC+C,UAAU,CAAC,OAAO,CAAC;IAC3B,CAAC,MACI;MACD/C,GAAG,CAAC+C,UAAU,CAAC,QAAQ,CAAC;IAC5B;EACJ;EACAC,aAAaA,CAAChD,GAAG,EAAEiD,GAAG,EAAEC,GAAG,EAAE;IACzB,MAAMC,UAAU,GAAG,IAAI,CAACjC,aAAa,CAACQ,cAAc,IAAI,IAAI,CAACR,aAAa,CAACS,YAAY;IACvF,MAAMxB,EAAE,GAAG8C,GAAG,CAACG,CAAC;IAChB,MAAMhD,EAAE,GAAG6C,GAAG,CAACI,CAAC;IAChB,MAAMhD,EAAE,GAAG6C,GAAG,CAACE,CAAC;IAChB,MAAM9C,EAAE,GAAG4C,GAAG,CAACG,CAAC;IAChB,MAAMC,QAAQ,GAAGxB,IAAI,CAACyB,IAAI,CAAC,CAAClD,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC,GAAG,CAACG,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC,CAAC;IACzE,MAAMoD,KAAK,GAAG,CAACF,QAAQ,GAAG,IAAI,CAACpC,aAAa,CAACU,eAAe,GAAG,CAAC,IAAI0B,QAAQ;IAC5E,IAAIG,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,MAAM;IACV,IAAIC,MAAM;IACV,IAAI,IAAI,CAAC1C,aAAa,CAACS,YAAY,IAAIwB,UAAU,EAAE;MAC/CM,IAAI,GAAG3B,IAAI,CAAC+B,KAAK,CAAC1D,EAAE,GAAG,CAACE,EAAE,GAAGF,EAAE,IAAIqD,KAAK,CAAC;MACzCE,IAAI,GAAG5B,IAAI,CAAC+B,KAAK,CAACzD,EAAE,GAAG,CAACE,EAAE,GAAGF,EAAE,IAAIoD,KAAK,CAAC;IAC7C,CAAC,MACI;MACDC,IAAI,GAAGpD,EAAE;MACTqD,IAAI,GAAGpD,EAAE;IACb;IACA,IAAI,IAAI,CAACY,aAAa,CAACQ,cAAc,IAAIyB,UAAU,EAAE;MACjDQ,MAAM,GAAGxD,EAAE,GAAG,CAACE,EAAE,GAAGF,EAAE,KAAK,CAAC,GAAGqD,KAAK,CAAC;MACrCI,MAAM,GAAGxD,EAAE,GAAG,CAACE,EAAE,GAAGF,EAAE,KAAK,CAAC,GAAGoD,KAAK,CAAC;IACzC,CAAC,MACI;MACDG,MAAM,GAAGxD,EAAE;MACXyD,MAAM,GAAGxD,EAAE;IACf;IACA,IAAI,IAAI,CAACc,aAAa,CAACO,KAAK,EAAE;MAC1BzB,GAAG,CAAC8D,cAAc,CAAC,IAAI,CAAC5C,aAAa,CAACO,KAAK,CAAC;MAC5CzB,GAAG,CAAC+D,YAAY,CAAC,IAAI,CAAC7C,aAAa,CAACO,KAAK,CAAC;IAC9C;IACAzB,GAAG,CAACO,SAAS,CAAC,CAAC;IACfP,GAAG,CAACQ,MAAM,CAACmD,MAAM,EAAEC,MAAM,CAAC;IAC1B5D,GAAG,CAACS,MAAM,CAACgD,IAAI,EAAEC,IAAI,CAAC;IACtB1D,GAAG,CAACgE,MAAM,CAAC,CAAC;IACZhE,GAAG,CAACU,SAAS,CAAC,CAAC;IACf,MAAMuD,SAAS,GAAGnC,IAAI,CAACoC,KAAK,CAAC5D,EAAE,GAAGF,EAAE,EAAEC,EAAE,GAAGF,EAAE,CAAC;IAC9C,MAAMgE,CAAC,GAAGrC,IAAI,CAACsC,GAAG,CAAC,IAAI,CAAClD,aAAa,CAACU,eAAe,GAAGE,IAAI,CAACuC,GAAG,CAAC,IAAI,CAACnD,aAAa,CAACW,cAAc,CAAC,CAAC;IACpG,IAAIyC,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,OAAO;IACX,IAAIC,OAAO;IACX,IAAI,IAAI,CAACzD,aAAa,CAACS,YAAY,IAAIwB,UAAU,EAAE;MAC/CmB,MAAM,GAAGL,SAAS,GAAGnC,IAAI,CAACC,EAAE,GAAG,IAAI,CAACb,aAAa,CAACW,cAAc;MAChE2C,IAAI,GAAGnE,EAAE,GAAGyB,IAAI,CAACuC,GAAG,CAACC,MAAM,CAAC,GAAGH,CAAC;MAChCM,IAAI,GAAGnE,EAAE,GAAGwB,IAAI,CAAC8C,GAAG,CAACN,MAAM,CAAC,GAAGH,CAAC;MAChCI,MAAM,GAAGN,SAAS,GAAGnC,IAAI,CAACC,EAAE,GAAG,IAAI,CAACb,aAAa,CAACW,cAAc;MAChE6C,OAAO,GAAGrE,EAAE,GAAGyB,IAAI,CAACuC,GAAG,CAACE,MAAM,CAAC,GAAGJ,CAAC;MACnCQ,OAAO,GAAGrE,EAAE,GAAGwB,IAAI,CAAC8C,GAAG,CAACL,MAAM,CAAC,GAAGJ,CAAC;MACnCpE,aAAa,CAACC,GAAG,EAAEwE,IAAI,EAAEC,IAAI,EAAEpE,EAAE,EAAEC,EAAE,EAAEoE,OAAO,EAAEC,OAAO,CAAC;IAC5D;IACA,IAAI,IAAI,CAACzD,aAAa,CAACQ,cAAc,IAAIyB,UAAU,EAAE;MACjDmB,MAAM,GAAGL,SAAS,GAAG,IAAI,CAAC/C,aAAa,CAACW,cAAc;MACtD2C,IAAI,GAAGrE,EAAE,GAAG2B,IAAI,CAACuC,GAAG,CAACC,MAAM,CAAC,GAAGH,CAAC;MAChCM,IAAI,GAAGrE,EAAE,GAAG0B,IAAI,CAAC8C,GAAG,CAACN,MAAM,CAAC,GAAGH,CAAC;MAChCI,MAAM,GAAGN,SAAS,GAAG,IAAI,CAAC/C,aAAa,CAACW,cAAc;MACtD6C,OAAO,GAAGvE,EAAE,GAAG2B,IAAI,CAACuC,GAAG,CAACE,MAAM,CAAC,GAAGJ,CAAC;MACnCQ,OAAO,GAAGvE,EAAE,GAAG0B,IAAI,CAAC8C,GAAG,CAACL,MAAM,CAAC,GAAGJ,CAAC;MACnCpE,aAAa,CAACC,GAAG,EAAEwE,IAAI,EAAEC,IAAI,EAAEtE,EAAE,EAAEC,EAAE,EAAEsE,OAAO,EAAEC,OAAO,CAAC;IAC5D;EACJ;EACAE,IAAIA,CAAA,EAAG;IACH,MAAM7E,GAAG,GAAG,IAAI,CAAC4C,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACkC,WAAW,CAAC,CAAC;IAClB,MAAMxC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMrB,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,IAAI,CAACyB,cAAc,CAAC,CAAC;IACrB,IAAIoC,aAAa,GAAG;MAAE3B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClC,IAAI2B,WAAW,GAAG;MAAE5B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAChC,IAAI,CAACb,YAAY,CAACyC,OAAO,CAAC,CAACC,UAAU,EAAEC,CAAC,KAAK;MACzC,MAAMC,SAAS,GAAG,IAAI,CAAC3C,WAAW,CAAC0C,CAAC,CAAC;MACrCJ,aAAa,GAAGzC,SAAS,CAAC+C,kBAAkB,CAAC,CAAC,EAAEH,UAAU,CAAC;MAC3DF,WAAW,GAAGzC,QAAQ,CAAC8C,kBAAkB,CAAC,CAAC,EAAED,SAAS,CAAC;MACvD,MAAME,YAAY,GAAGP,aAAa,CAAC1B,CAAC,GAAG2B,WAAW,CAAC3B,CAAC;MACpD0B,aAAa,CAAC3B,CAAC,IAAId,SAAS,CAACiD,UAAU,CAAC,CAAC,CAACC,UAAU,GAAGtE,aAAa,CAACC,WAAW;MAChF6D,WAAW,CAAC5B,CAAC,IAAIb,QAAQ,CAACgD,UAAU,CAAC,CAAC,CAACE,SAAS,GAAGvE,aAAa,CAACE,YAAY;MAC7E,MAAMsE,aAAa,GAAGpD,SAAS,CAACqD,aAAa,CAAC,CAAC;MAC/C,MAAMC,cAAc,GAAGtD,SAAS,CAACuD,WAAW,CAAC,CAAC,CAACX,UAAU,CAAC,CAACY,SAAS;MACpE,IAAIF,cAAc,IAAItD,SAAS,CAACyD,gBAAgB,CAAC,CAAC,KAAK,CAAC,EAAE;QACtDhB,aAAa,CAAC3B,CAAC,IAAIsC,aAAa,GAAGxE,aAAa,CAACC,WAAW;MAChE;MACA,MAAM6E,aAAa,GAAGzD,QAAQ,CAACsD,WAAW,CAAC,CAAC,CAACT,SAAS,CAAC,CAACU,SAAS;MACjE,IAAIE,aAAa,IAAIzD,QAAQ,CAACwD,gBAAgB,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACrDf,WAAW,CAAC5B,CAAC,IAAIsC,aAAa,GAAGxE,aAAa,CAACE,YAAY;MAC/D;MACA2D,aAAa,CAAC1B,CAAC,IAAIiC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;MACxCN,WAAW,CAAC3B,CAAC,IAAIiC,YAAY,GAAG,CAAC,GAAG,CAAC;MACrC,IAAI,CAACtC,aAAa,CAAChD,GAAG,EAAE+E,aAAa,EAAEC,WAAW,CAAC;IACvD,CAAC,CAAC;IACF,MAAMiB,SAAS,GAAG,IAAI,CAACC,KAAK;IAC5B,MAAMC,aAAa,GAAGjF,aAAa,CAACiB,iBAAiB;IACrD,IAAIiB,CAAC,GAAG,CAAC;IACT,IAAI+C,aAAa,KAAKvF,SAAS,CAACf,iBAAiB,CAACuG,IAAI,EAAE;MACpDhD,CAAC,GAAG2B,aAAa,CAAC3B,CAAC;IACvB,CAAC,MACI,IAAI+C,aAAa,KAAKvF,SAAS,CAACf,iBAAiB,CAACuC,MAAM,EAAE;MAC3D,MAAMiE,MAAM,GAAGrB,WAAW,CAAC5B,CAAC,GAAG2B,aAAa,CAAC3B,CAAC;MAC9C,MAAMkD,OAAO,GAAGD,MAAM,GAAG,CAAC,GAAGtB,aAAa,CAAC3B,CAAC;MAC5CA,CAAC,GAAGkD,OAAO,GAAGL,SAAS,GAAG,CAAC;IAC/B,CAAC,MACI,IAAIE,aAAa,KAAKvF,SAAS,CAACf,iBAAiB,CAAC0G,KAAK,EAAE;MAC1DnD,CAAC,GAAG4B,WAAW,CAAC5B,CAAC,GAAG6C,SAAS;IACjC;IACA,IAAI5C,CAAC,GAAG,CAAC;IACT,MAAMmD,gBAAgB,GAAGtF,aAAa,CAACc,oBAAoB;IAC3D,IAAIwE,gBAAgB,KAAK5F,SAAS,CAACqB,oBAAoB,CAACC,GAAG,EAAE;MACzDmB,CAAC,GAAGf,SAAS,CAACmE,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;IAC/C,CAAC,MACI,IAAIF,gBAAgB,KAAK5F,SAAS,CAACqB,oBAAoB,CAAC0E,MAAM,EAAE;MACjEtD,CAAC,GAAGf,SAAS,CAACmE,UAAU,CAAC,CAAC,CAACG,iBAAiB,CAAChH,MAAM,CAACiH,uBAAuB,CAAC;IAChF;IACA,MAAMpF,KAAK,GAAGP,aAAa,CAACO,KAAK;IACjC,IAAI,CAACqF,UAAU,CAAC9G,GAAG,EAAE;MAAE+G,SAAS,EAAEtF,KAAK;MAAEuF,WAAW,EAAEvF;IAAM,CAAC,CAAC;IAC9D,IAAI,CAACwF,UAAU,CAACjH,GAAG,EAAEoD,CAAC,EAAEC,CAAC,CAAC;EAC9B;AACJ;AACAzC,SAAS,CAACqB,oBAAoB,GAAG;EAC7BC,GAAG,EAAE,CAAC;EACNyE,MAAM,EAAE;AACZ,CAAC;AACD/F,SAAS,CAACf,iBAAiB,GAAGA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}