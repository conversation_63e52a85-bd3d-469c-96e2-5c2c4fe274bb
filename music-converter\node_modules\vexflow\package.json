{"name": "vexflow", "version": "5.0.0", "description": "A JavaScript library for rendering music notation and guitar tablature.", "exports": {".": {"types": "./build/types/entry/vexflow.d.ts", "require": "./build/cjs/vexflow.js", "import": "./build/esm/entry/vexflow.js"}, "./core": {"types": "./build/types/entry/vexflow-core.d.ts", "require": "./build/cjs/vexflow-core.js", "import": "./build/esm/entry/vexflow-core.js"}, "./bravura": {"types": "./build/types/entry/vexflow-bravura.d.ts", "require": "./build/cjs/vexflow-bravura.js", "import": "./build/esm/entry/vexflow-bravura.js"}}, "main": "./build/cjs/vexflow.js", "browser": "./build/cjs/vexflow.js", "module": "./build/esm/entry/vexflow.js", "types": "./build/types/entry/vexflow.d.ts", "unpkg": "./build/cjs/vexflow.js", "jsdelivr": "./build/cjs/vexflow.js", "repository": {"type": "git", "url": "https://github.com/vexflow/vexflow.git"}, "author": {"name": "VexFlow Authors", "email": "<EMAIL>", "url": "https://github.com/vexflow/vexflow/"}, "license": "MIT", "files": ["build", "tests/flow.html", "tests/flow.css", "tests/qunit/qunit.css", "tests/qunit/qunit.js", "AUTHORS.md"], "bugs": {"url": "https://github.com/vexflow/vexflow/issues"}, "devDependencies": {"@eslint/compat": "^1.2.6", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@types/node": "^22.13.1", "@types/qunit": "^2.19.12", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vexflow-fonts/academico": "^1.0.1", "@vexflow-fonts/bravura": "^1.0.2", "@vexflow-fonts/bravuratext": "^1.0.0", "@vexflow-fonts/edwin": "^1.0.1", "@vexflow-fonts/finaleash": "^1.0.1", "@vexflow-fonts/finaleashtext": "^1.0.1", "@vexflow-fonts/finalebroadway": "^1.0.0", "@vexflow-fonts/finalebroadwaytext": "^1.0.0", "@vexflow-fonts/finalejazz": "^1.0.0", "@vexflow-fonts/finalejazztext": "^1.0.0", "@vexflow-fonts/finalemaestro": "^1.0.0", "@vexflow-fonts/finalemaestrotext": "^1.0.0", "@vexflow-fonts/gonville": "1.0.0", "@vexflow-fonts/gootville": "^1.0.1", "@vexflow-fonts/gootvilletext": "^1.0.1", "@vexflow-fonts/leipzig": "^1.0.1", "@vexflow-fonts/leland": "^1.0.1", "@vexflow-fonts/lelandtext": "^1.0.1", "@vexflow-fonts/musejazz": "^1.0.1", "@vexflow-fonts/musejazztext": "^1.0.1", "@vexflow-fonts/nepomuk": "^1.0.1", "@vexflow-fonts/petaluma": "^1.0.1", "@vexflow-fonts/petalumascript": "^1.0.0", "@vexflow-fonts/petalumatext": "^1.0.1", "@vexflow-fonts/robotoslab": "^1.0.0", "@vexflow-fonts/sebastian": "^1.0.1", "@vexflow-fonts/sebastiantext": "^1.0.0", "canvas": "^3.1.0", "circular-dependency-plugin": "^5.2.2", "concurrently": "^9.1.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-simple-import-sort": "^12.1.1", "fork-ts-checker-webpack-plugin": "^9.0.2", "globals": "^15.14.0", "grunt": "^1.6.1", "grunt-cli": "^1.5.0", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-qunit": "^10.1.1", "grunt-eslint": "^25.0.0", "grunt-webpack": "^7.0.0", "jsdom": "^26.0.0", "jspdf": "^2.5.2", "opener": "^1.5.2", "opentype.js": "^1.3.4", "prettier": "^3.4.2", "puppeteer": "^24.2.0", "qunit": "^2.24.1", "recursive-copy": "^2.0.14", "release-it": "^18.1.2", "string-replace-loader": "^3.1.0", "svg2pdf.js": "^2.2.4", "terser-webpack-plugin": "^5.3.11", "ts-loader": "^9.5.2", "tsc-watch": "^6.2.1", "typescript": "^5.7.3", "webpack": "^5.97.1"}, "scripts": {"start": "grunt", "test": "grunt test", "reference": "grunt reference", "test:reference": "grunt test:reference", "test:reference:cache": "grunt test:reference:cache", "generate:current": "grunt generate:current", "generate:reference": "grunt generate:reference", "diff:reference": "grunt diff:reference", "lint": "grunt eslint"}, "homepage": "https://vexflow.com/", "keywords": ["music", "notation", "piano", "guitar", "tablature"]}