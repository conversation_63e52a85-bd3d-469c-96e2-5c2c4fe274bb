{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Helmet } from \"react-helmet\";\nimport VexTabBlock, { updateVexTabBlock } from \"./VexTabBlock.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"rgba(68, 25, 240, 0.08)\",\n      display: \"grid\",\n      gridTemplateRows: \"1fr auto auto\"\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"white\"\n    },\n    sideButton: {\n      backgroundColor: \"white\",\n      width: \"4rem\",\n      height: \"4rem\",\n      fontSize: \"1.2rem\",\n      fontWeight: \"bold\",\n      color: \"#5c14ba\",\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"1rem\",\n      cursor: \"pointer\",\n      margin: \"auto\"\n    },\n    sideButtonActive: {\n      backgroundColor: \"#9a62e3\",\n      color: \"white\",\n      border: \"2px solid #7f3bd9\",\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\"\n    }\n  };\n\n  // MUSIC STATES\n  const defaultSetup = \"tabstave notation=true tablature=false\";\n  const [curOctave, setCurOctave] = useState(4);\n  const [curStep, setCurStep] = useState(\"A\");\n  const [curAccident, setCurAccident] = useState(\"\");\n  const [key, setKey] = useState(\"G\");\n  const [time, setTime] = useState(\"4/4\");\n  const [notes, setNotes] = useState([]);\n  const rawVex = [defaultSetup + ` key=${key} time=${time}`, \"notes \" + notes.join(\" \")].join(\"\\n\");\n  const [converted, setConverted] = useState(\"\");\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\n  const VEXTAB_ACCIDENTALS = {\n    \"♯\": \"#\",\n    \"♮\": \"n\",\n    \"♭\": \"b\",\n    \"\": \"\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    style: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"center\",\n      justifyItems: \"center\",\n      paddingTop: \"4rem\",\n      paddingBottom: \"4rem\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vexflow/releases/vexflow-min.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/vextab-core.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gridTemplateColumns: \"6rem 1fr 6rem\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignContent: \"center\",\n                gap: \"0.5rem\",\n                padding: \"1rem 0rem\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(o => o + 1),\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                children: curOctave\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(o => o - 1),\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"grid\",\n                gridTemplateRows: \"auto 1fr\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"grid\",\n                  gridTemplateColumns: \"repeat(7, 1fr)\",\n                  padding: \"1rem\"\n                },\n                children: NOTES.map(note => /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    ...styles.sideButton,\n                    ...(curStep === note ? styles.sideButtonActive : {})\n                  },\n                  onClick: () => setCurStep(note),\n                  children: note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  border: \"2px solid red\",\n                  padding: \"1rem\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(VexTabBlock, {\n                  source: [defaultSetup + ` key=${key} time=${time}`, \"notes \" + notes[notes.length - 1]].join('\\n'),\n                  id: \"currentNoteRef\",\n                  editorHeight: 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), updateVexTabBlock('currentNoteRef')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                alignContent: \"center\",\n                gap: \"0.5rem\",\n                padding: \"1rem 0rem\"\n              },\n              children: ACCIDENTALS.map(acc => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  ...styles.sideButton,\n                  ...(curAccident === acc ? styles.sideButtonActive : {})\n                },\n                onClick: curAccident === acc ? () => setCurAccident(\"\") : () => setCurAccident(acc),\n                children: acc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                const note = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\n                console.log(\"Current Note: \", note);\n                setNotes(prev => [...prev, note]);\n                const wrapper = document.getElementById('inputEditorRef');\n                const curVexText = wrapper.querySelector('textarea.editor');\n                curVexText.value += note + \" \";\n                updateVexTabBlock('inputEditorRef');\n              },\n              children: \"Add Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log(\"Notes before Removal: \", notes);\n                setNotes(prev => prev.slice(0, -1));\n                const wrapper = document.getElementById('inputEditorRef');\n                const curVexText = wrapper.querySelector('textarea.editor');\n                curVexText.value = `${defaultSetup} \\nnotes ${notes.join(\" \")}`;\n                updateVexTabBlock('inputEditorRef');\n              },\n              children: \"Remove Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Input Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: rawVex,\n            id: \"inputEditorRef\",\n            editorHeight: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), updateVexTabBlock('inputEditorRef')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Output Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vextab-auto\",\n            editor: \"true\",\n            style: {\n              minHeight: \"200px\",\n              border: \"1px solid #ccc\",\n              margin: \"1rem\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: converted,\n            id: \"outputRef\",\n            editorHeight: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), updateVexTabBlock('outputEditorRef')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"m89YG3D41fw7okYrB6cqnYOp+PM=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "<PERSON><PERSON><PERSON>", "VexTabBlock", "updateVexTabBlock", "jsxDEV", "_jsxDEV", "Converter", "_s", "styles", "graphicDisplay", "width", "Math", "min", "window", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "sideButton", "backgroundColor", "height", "fontSize", "fontWeight", "color", "cursor", "margin", "sideButtonActive", "boxShadow", "defaultSetup", "curOctave", "setCurOctave", "curStep", "setCurStep", "curAccident", "setCurAccident", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "join", "converted", "setConverted", "NOTES", "ACCIDENTALS", "VEXTAB_ACCIDENTALS", "style", "flexDirection", "alignItems", "justifyItems", "paddingTop", "paddingBottom", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "gap", "gridTemplateColumns", "justifyContent", "align<PERSON><PERSON><PERSON>", "onClick", "o", "map", "note", "source", "length", "id", "<PERSON><PERSON><PERSON><PERSON>", "acc", "console", "log", "prev", "wrapper", "document", "getElementById", "curVexText", "querySelector", "value", "slice", "className", "editor", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport VexTabBlock, { updateVexTabBlock } from \"./VexTabBlock.jsx\";\r\n\r\nfunction Converter() {\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"rgba(68, 25, 240, 0.08)\",\r\n      display: \"grid\",\r\n      gridTemplateRows: \"1fr auto auto\",\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"white\",\r\n    },\r\n    sideButton: {\r\n      backgroundColor: \"white\",\r\n      width: \"4rem\",\r\n      height: \"4rem\",\r\n      fontSize: \"1.2rem\",\r\n      fontWeight: \"bold\",\r\n      color: \"#5c14ba\",\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"1rem\",\r\n      cursor: \"pointer\",\r\n      margin: \"auto\",\r\n    },\r\n    sideButtonActive: {\r\n      backgroundColor: \"#9a62e3\",\r\n      color: \"white\",\r\n      border: \"2px solid #7f3bd9\",\r\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\",\r\n    },\r\n  };\r\n\r\n  // MUSIC STATES\r\n  const defaultSetup = \"tabstave notation=true tablature=false\";\r\n\r\n  const [curOctave, setCurOctave] = useState(4);\r\n  const [curStep, setCurStep] = useState(\"A\");\r\n  const [curAccident, setCurAccident] = useState(\"\");\r\n\r\n  const [key, setKey] = useState(\"G\");\r\n  const [time, setTime] = useState(\"4/4\");\r\n  const [notes, setNotes] = useState([]);\r\n\r\n  const rawVex = [\r\n    defaultSetup + ` key=${key} time=${time}`,\r\n    \"notes \" + notes.join(\" \"),\r\n  ].join(\"\\n\");\r\n\r\n  const [converted, setConverted] = useState(\"\");\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\r\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\r\n  const VEXTAB_ACCIDENTALS = { \"♯\": \"#\", \"♮\": \"n\", \"♭\": \"b\", \"\": \"\" };\r\n\r\n  return (\r\n    <main\r\n      style={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: \"center\",\r\n        justifyItems: \"center\",\r\n        paddingTop: \"4rem\",\r\n        paddingBottom: \"4rem\",\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vexflow/releases/vexflow-min.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/vextab-core.prod.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: \"grid\",\r\n                gridTemplateColumns: \"6rem 1fr 6rem\",\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignContent: \"center\",\r\n                  gap: \"0.5rem\",\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => setCurOctave((o) => o + 1)}\r\n                >\r\n                  ↑\r\n                </button>\r\n                <button style={styles.sideButton}>{curOctave}</button>\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => setCurOctave((o) => o - 1)}\r\n                >\r\n                  ↓\r\n                </button>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"grid\",\r\n                  gridTemplateRows: \"auto 1fr\",\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: \"grid\",\r\n                    gridTemplateColumns: \"repeat(7, 1fr)\",\r\n                    padding: \"1rem\",\r\n                  }}\r\n                >\r\n                  {NOTES.map((note) => (\r\n                    <button\r\n                      style={{\r\n                        ...styles.sideButton,\r\n                        ...(curStep === note ? styles.sideButtonActive : {}),\r\n                      }}\r\n                      onClick={() => setCurStep(note)}\r\n                    >\r\n                      {note}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n\r\n                <div style={{ border: \"2px solid red\", padding: \"1rem\" }}>\r\n                  <VexTabBlock source={[defaultSetup + ` key=${key} time=${time}`,\r\n    \"notes \" + notes[notes.length-1]].join('\\n')} id={\"currentNoteRef\"} editorHeight={1}/>\r\n                  {updateVexTabBlock('currentNoteRef')}\r\n                </div>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  justifyContent: \"center\",\r\n                  alignContent: \"center\",\r\n                  gap: \"0.5rem\",\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                {ACCIDENTALS.map((acc) => (\r\n                  <button\r\n                    style={{\r\n                      ...styles.sideButton,\r\n                      ...(curAccident === acc ? styles.sideButtonActive : {}),\r\n                    }}\r\n                    onClick={\r\n                      curAccident === acc\r\n                        ? () => setCurAccident(\"\")\r\n                        : () => setCurAccident(acc)\r\n                    }\r\n                  >\r\n                    {acc}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            {/* ----- ADD & REMOVE NOTES ----- */}\r\n            <div>\r\n              {/* --- Add --- */}\r\n              <button\r\n                onClick={() => {\r\n                  const note = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\r\n                  console.log(\"Current Note: \", note);\r\n                  setNotes((prev) => [...prev, note]);\r\n                  \r\n                  const wrapper = document.getElementById('inputEditorRef');\r\n                  const curVexText = wrapper.querySelector('textarea.editor');\r\n                  curVexText.value += note + \" \";\r\n                  updateVexTabBlock('inputEditorRef');\r\n                }}\r\n              >\r\n                Add Note\r\n              </button>\r\n              {/* --- Remove --- */}\r\n              <button\r\n                onClick={() => {\r\n                  console.log(\"Notes before Removal: \", notes);\r\n                  setNotes((prev) => prev.slice(0, -1));\r\n\r\n                  const wrapper = document.getElementById('inputEditorRef');\r\n                  const curVexText = wrapper.querySelector('textarea.editor');\r\n                  curVexText.value = `${defaultSetup} \\nnotes ${notes.join(\" \")}`;\r\n                  updateVexTabBlock('inputEditorRef');\r\n                }}\r\n              >\r\n                Remove Note\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Input Editor</h4>\r\n            <VexTabBlock source={rawVex} id={\"inputEditorRef\"} editorHeight={100} />\r\n            {updateVexTabBlock('inputEditorRef')}\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <h3>Output</h3>\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Output Editor</h4>\r\n            <div\r\n              className=\"vextab-auto\"\r\n              editor=\"true\"\r\n              style={{\r\n                minHeight: \"200px\",\r\n                border: \"1px solid #ccc\",\r\n                margin: \"1rem\",\r\n              }}\r\n            >\r\n              {/* Output will be populated by the processing function */}\r\n            </div>\r\n            <VexTabBlock source={converted} id={\"outputRef\"} editorHeight={100}/>\r\n            {updateVexTabBlock('outputEditorRef')}\r\n          </div>\r\n        </section>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default Converter;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,WAAW,IAAIC,iBAAiB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZV,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,UAAU,EAAE;MACVC,eAAe,EAAE,OAAO;MACxBZ,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBW,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE;MAChBP,eAAe,EAAE,SAAS;MAC1BI,KAAK,EAAE,OAAO;MACdX,MAAM,EAAE,mBAAmB;MAC3Be,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,wCAAwC;EAE7D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM,CAACuC,GAAG,EAAEC,MAAM,CAAC,GAAGxC,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACyC,IAAI,EAAEC,OAAO,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM6C,MAAM,GAAG,CACbb,YAAY,GAAG,QAAQO,GAAG,SAASE,IAAI,EAAE,EACzC,QAAQ,GAAGE,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,CAC3B,CAACA,IAAI,CAAC,IAAI,CAAC;EAEZ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMiD,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,MAAMC,kBAAkB,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,EAAE,EAAE;EAAG,CAAC;EAEnE,oBACE7C,OAAA;IACE8C,KAAK,EAAE;MACLjC,OAAO,EAAE,MAAM;MACfkC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEFpD,OAAA,CAACJ,MAAM;MAAAwD,QAAA,gBACLpD,OAAA;QAAQqD,GAAG,EAAC;MAAmD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACzEzD,OAAA;QAAQqD,GAAG,EAAC;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC7EzD,OAAA;QAAQqD,GAAG,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGTzD,OAAA;MAAAoD,QAAA,eACEpD,OAAA;QACE8C,KAAK,EAAE;UACLpC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfkC,aAAa,EAAE,QAAQ;UACvBxB,MAAM,EAAE,WAAW;UACnBmC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXX,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEFpD,OAAA;UAAAoD,QAAA,EAAI;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGdzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACC,cAAe;UAAAgD,QAAA,gBAChCpD,OAAA;YACE8C,KAAK,EAAE;cACLjC,OAAO,EAAE,MAAM;cACf+C,mBAAmB,EAAE;YACvB,CAAE;YAAAR,QAAA,gBAEFpD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBc,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,gBAEFpD,OAAA;gBACE8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBACzB+C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAEoC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAE;gBAAAZ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzD,OAAA;gBAAQ8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBAAAoC,QAAA,EAAEzB;cAAS;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACtDzD,OAAA;gBACE8C,KAAK,EAAE3C,MAAM,CAACa,UAAW;gBACzB+C,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAEoC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAE;gBAAAZ,QAAA,EAC3C;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENzD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAAsC,QAAA,gBAEFpD,OAAA;gBACE8C,KAAK,EAAE;kBACLjC,OAAO,EAAE,MAAM;kBACf+C,mBAAmB,EAAE,gBAAgB;kBACrCF,OAAO,EAAE;gBACX,CAAE;gBAAAN,QAAA,EAEDT,KAAK,CAACsB,GAAG,CAAEC,IAAI,iBACdlE,OAAA;kBACE8C,KAAK,EAAE;oBACL,GAAG3C,MAAM,CAACa,UAAU;oBACpB,IAAIa,OAAO,KAAKqC,IAAI,GAAG/D,MAAM,CAACqB,gBAAgB,GAAG,CAAC,CAAC;kBACrD,CAAE;kBACFuC,OAAO,EAAEA,CAAA,KAAMjC,UAAU,CAACoC,IAAI,CAAE;kBAAAd,QAAA,EAE/Bc;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzD,OAAA;gBAAK8C,KAAK,EAAE;kBAAEpC,MAAM,EAAE,eAAe;kBAAEgD,OAAO,EAAE;gBAAO,CAAE;gBAAAN,QAAA,gBACvDpD,OAAA,CAACH,WAAW;kBAACsE,MAAM,EAAE,CAACzC,YAAY,GAAG,QAAQO,GAAG,SAASE,IAAI,EAAE,EAC7E,QAAQ,GAAGE,KAAK,CAACA,KAAK,CAAC+B,MAAM,GAAC,CAAC,CAAC,CAAC,CAAC5B,IAAI,CAAC,IAAI,CAAE;kBAAC6B,EAAE,EAAE,gBAAiB;kBAACC,YAAY,EAAE;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,EACvE3D,iBAAiB,CAAC,gBAAgB,CAAC;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzD,OAAA;cACE8C,KAAK,EAAE;gBACLjC,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBc,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAEDR,WAAW,CAACqB,GAAG,CAAEM,GAAG,iBACnBvE,OAAA;gBACE8C,KAAK,EAAE;kBACL,GAAG3C,MAAM,CAACa,UAAU;kBACpB,IAAIe,WAAW,KAAKwC,GAAG,GAAGpE,MAAM,CAACqB,gBAAgB,GAAG,CAAC,CAAC;gBACxD,CAAE;gBACFuC,OAAO,EACLhC,WAAW,KAAKwC,GAAG,GACf,MAAMvC,cAAc,CAAC,EAAE,CAAC,GACxB,MAAMA,cAAc,CAACuC,GAAG,CAC7B;gBAAAnB,QAAA,EAEAmB;cAAG;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA;YAAAoD,QAAA,eACEpD,OAAA;cAAAoD,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGNzD,OAAA;YAAAoD,QAAA,gBAEEpD,OAAA;cACE+D,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMG,IAAI,GAAG,GAAGrC,OAAO,GAAGgB,kBAAkB,CAACd,WAAW,CAAC,IAAIJ,SAAS,EAAE;gBACxE6C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEP,IAAI,CAAC;gBACnC5B,QAAQ,CAAEoC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAER,IAAI,CAAC,CAAC;gBAEnC,MAAMS,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;gBACzD,MAAMC,UAAU,GAAGH,OAAO,CAACI,aAAa,CAAC,iBAAiB,CAAC;gBAC3DD,UAAU,CAACE,KAAK,IAAId,IAAI,GAAG,GAAG;gBAC9BpE,iBAAiB,CAAC,gBAAgB,CAAC;cACrC,CAAE;cAAAsD,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETzD,OAAA;cACE+D,OAAO,EAAEA,CAAA,KAAM;gBACbS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEpC,KAAK,CAAC;gBAC5CC,QAAQ,CAAEoC,IAAI,IAAKA,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAErC,MAAMN,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;gBACzD,MAAMC,UAAU,GAAGH,OAAO,CAACI,aAAa,CAAC,iBAAiB,CAAC;gBAC3DD,UAAU,CAACE,KAAK,GAAG,GAAGtD,YAAY,YAAYW,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC/D1C,iBAAiB,CAAC,gBAAgB,CAAC;cACrC,CAAE;cAAAsD,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAINzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACY,YAAa;UAAAqC,QAAA,gBAC9BpD,OAAA;YAAAoD,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BzD,OAAA,CAACH,WAAW;YAACsE,MAAM,EAAE5B,MAAO;YAAC8B,EAAE,EAAE,gBAAiB;YAACC,YAAY,EAAE;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvE3D,iBAAiB,CAAC,gBAAgB,CAAC;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNzD,OAAA;MAAAoD,QAAA,eACEpD,OAAA;QACE8C,KAAK,EAAE;UACLpC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfkC,aAAa,EAAE,QAAQ;UACvBxB,MAAM,EAAE,WAAW;UACnBmC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXX,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEFpD,OAAA;UAAAoD,QAAA,EAAI;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfzD,OAAA;UAAK8C,KAAK,EAAE3C,MAAM,CAACY,YAAa;UAAAqC,QAAA,gBAC9BpD,OAAA;YAAAoD,QAAA,EAAI;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BzD,OAAA;YACEkF,SAAS,EAAC,aAAa;YACvBC,MAAM,EAAC,MAAM;YACbrC,KAAK,EAAE;cACLsC,SAAS,EAAE,OAAO;cAClB1E,MAAM,EAAE,gBAAgB;cACxBa,MAAM,EAAE;YACV;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGC,CAAC,eACNzD,OAAA,CAACH,WAAW;YAACsE,MAAM,EAAE1B,SAAU;YAAC4B,EAAE,EAAE,WAAY;YAACC,YAAY,EAAE;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC,EACpE3D,iBAAiB,CAAC,iBAAiB,CAAC;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAACvD,EAAA,CAjRQD,SAAS;AAAAoF,EAAA,GAATpF,SAAS;AAmRlB,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}