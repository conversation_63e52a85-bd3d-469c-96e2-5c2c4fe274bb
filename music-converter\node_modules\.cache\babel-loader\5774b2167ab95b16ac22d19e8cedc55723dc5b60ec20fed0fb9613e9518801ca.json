{"ast": null, "code": "import { Font } from './font.js';\nimport { RenderContext } from './rendercontext.js';\nimport { globalObject, warn } from './util.js';\nimport { isHTMLCanvas } from './web.js';\nexport class CanvasContext extends RenderContext {\n  static get WIDTH() {\n    return 600;\n  }\n  static get HEIGHT() {\n    return 400;\n  }\n  static get CANVAS_BROWSER_SIZE_LIMIT() {\n    return 32767;\n  }\n  static sanitizeCanvasDims(width, height) {\n    const limit = this.CANVAS_BROWSER_SIZE_LIMIT;\n    if (Math.max(width, height) > limit) {\n      warn('Canvas dimensions exceed browser limit. Cropping to ' + limit);\n      if (width > limit) {\n        width = limit;\n      }\n      if (height > limit) {\n        height = limit;\n      }\n    }\n    return [width, height];\n  }\n  constructor(context) {\n    super();\n    this.textHeight = 0;\n    this.context2D = context;\n    this.curTransfrom = context.getTransform();\n    if (!context.canvas) {\n      this.canvas = {\n        width: CanvasContext.WIDTH,\n        height: CanvasContext.HEIGHT\n      };\n    } else {\n      this.canvas = context.canvas;\n    }\n  }\n  clear() {\n    this.context2D.clearRect(0, 0, this.canvas.width, this.canvas.height);\n  }\n  openGroup(cls, id) {}\n  closeGroup() {}\n  openRotation(angleDegrees, x, y) {\n    this.curTransfrom = this.context2D.getTransform();\n    this.context2D.translate(x, y);\n    this.context2D.rotate(angleDegrees * Math.PI / 180);\n    this.context2D.translate(-x, -y);\n  }\n  closeRotation() {\n    this.context2D.setTransform(this.curTransfrom);\n  }\n  add(child) {}\n  setFillStyle(style) {\n    this.context2D.fillStyle = style;\n    return this;\n  }\n  setBackgroundFillStyle(style) {\n    return this;\n  }\n  setStrokeStyle(style) {\n    this.context2D.strokeStyle = style;\n    return this;\n  }\n  setShadowColor(color) {\n    this.context2D.shadowColor = color;\n    return this;\n  }\n  setShadowBlur(blur) {\n    const t = this.context2D.getTransform();\n    const scale = Math.sqrt(t.a * t.a + t.b * t.b + t.c * t.c + t.d * t.d);\n    this.context2D.shadowBlur = scale * blur;\n    return this;\n  }\n  setLineWidth(width) {\n    this.context2D.lineWidth = width;\n    return this;\n  }\n  setLineCap(capType) {\n    this.context2D.lineCap = capType;\n    return this;\n  }\n  setLineDash(dash) {\n    this.context2D.setLineDash(dash);\n    return this;\n  }\n  scale(x, y) {\n    this.context2D.scale(x, y);\n    return this;\n  }\n  resize(width, height, devicePixelRatio) {\n    var _a;\n    const canvas = this.context2D.canvas;\n    const dpr = (_a = devicePixelRatio !== null && devicePixelRatio !== void 0 ? devicePixelRatio : globalObject().devicePixelRatio) !== null && _a !== void 0 ? _a : 1;\n    [width, height] = CanvasContext.sanitizeCanvasDims(width * dpr, height * dpr);\n    width = width / dpr | 0;\n    height = height / dpr | 0;\n    canvas.width = width * dpr;\n    canvas.height = height * dpr;\n    if (isHTMLCanvas(canvas)) {\n      canvas.style.width = width + 'px';\n      canvas.style.height = height + 'px';\n    }\n    return this.scale(dpr, dpr);\n  }\n  rect(x, y, width, height) {\n    this.context2D.rect(x, y, width, height);\n    return this;\n  }\n  fillRect(x, y, width, height) {\n    this.context2D.fillRect(x, y, width, height);\n    return this;\n  }\n  pointerRect(x, y, width, height) {\n    return this;\n  }\n  clearRect(x, y, width, height) {\n    this.context2D.clearRect(x, y, width, height);\n    return this;\n  }\n  beginPath() {\n    this.context2D.beginPath();\n    return this;\n  }\n  moveTo(x, y) {\n    this.context2D.moveTo(x, y);\n    return this;\n  }\n  lineTo(x, y) {\n    this.context2D.lineTo(x, y);\n    return this;\n  }\n  bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\n    this.context2D.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);\n    return this;\n  }\n  quadraticCurveTo(cpx, cpy, x, y) {\n    this.context2D.quadraticCurveTo(cpx, cpy, x, y);\n    return this;\n  }\n  arc(x, y, radius, startAngle, endAngle, counterclockwise) {\n    this.context2D.arc(x, y, radius, startAngle, endAngle, counterclockwise);\n    return this;\n  }\n  fill() {\n    this.context2D.fill();\n    return this;\n  }\n  stroke() {\n    this.context2D.stroke();\n    return this;\n  }\n  closePath() {\n    this.context2D.closePath();\n    return this;\n  }\n  measureText(text) {\n    const metrics = this.context2D.measureText(text);\n    let y = 0;\n    let height = 0;\n    if (metrics.fontBoundingBoxAscent) {\n      y = -metrics.fontBoundingBoxAscent;\n      height = metrics.fontBoundingBoxDescent + metrics.fontBoundingBoxAscent;\n    } else {\n      y = -metrics.actualBoundingBoxAscent;\n      height = metrics.actualBoundingBoxDescent + metrics.actualBoundingBoxAscent;\n    }\n    return {\n      x: 0,\n      y: y,\n      width: metrics.width,\n      height: height\n    };\n  }\n  fillText(text, x, y) {\n    this.context2D.fillText(text, x, y);\n    return this;\n  }\n  save() {\n    this.context2D.save();\n    return this;\n  }\n  restore() {\n    this.context2D.restore();\n    return this;\n  }\n  set fillStyle(style) {\n    this.context2D.fillStyle = style;\n  }\n  get fillStyle() {\n    return this.context2D.fillStyle;\n  }\n  set strokeStyle(style) {\n    this.context2D.strokeStyle = style;\n  }\n  get strokeStyle() {\n    return this.context2D.strokeStyle;\n  }\n  setFont(f, size, weight, style) {\n    const fontInfo = Font.validate(f, size, weight, style);\n    this.context2D.font = Font.toCSSString(fontInfo);\n    this.textHeight = Font.convertSizeToPixelValue(fontInfo.size);\n    return this;\n  }\n  getFont() {\n    return this.context2D.font;\n  }\n}", "map": {"version": 3, "names": ["Font", "RenderContext", "globalObject", "warn", "isHTMLCanvas", "CanvasContext", "WIDTH", "HEIGHT", "CANVAS_BROWSER_SIZE_LIMIT", "sanitizeCanvasDims", "width", "height", "limit", "Math", "max", "constructor", "context", "textHeight", "context2D", "curTransfrom", "getTransform", "canvas", "clear", "clearRect", "openGroup", "cls", "id", "closeGroup", "openRotation", "angleDegrees", "x", "y", "translate", "rotate", "PI", "closeRotation", "setTransform", "add", "child", "setFillStyle", "style", "fillStyle", "setBackgroundFillStyle", "setStrokeStyle", "strokeStyle", "setShadowColor", "color", "shadowColor", "setShadowBlur", "blur", "t", "scale", "sqrt", "a", "b", "c", "d", "<PERSON><PERSON><PERSON><PERSON>", "setLineWidth", "lineWidth", "setLineCap", "capType", "lineCap", "setLineDash", "dash", "resize", "devicePixelRatio", "_a", "dpr", "rect", "fillRect", "pointerRect", "beginPath", "moveTo", "lineTo", "bezierCurveTo", "cp1x", "cp1y", "cp2x", "cp2y", "quadraticCurveTo", "cpx", "cpy", "arc", "radius", "startAngle", "endAngle", "counterclockwise", "fill", "stroke", "closePath", "measureText", "text", "metrics", "fontBoundingBoxAscent", "fontBoundingBoxDescent", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "fillText", "save", "restore", "setFont", "f", "size", "weight", "fontInfo", "validate", "font", "toCSSString", "convertSizeToPixelValue", "getFont"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/canvascontext.js"], "sourcesContent": ["import { Font } from './font.js';\nimport { RenderContext } from './rendercontext.js';\nimport { globalObject, warn } from './util.js';\nimport { isHTMLCanvas } from './web.js';\nexport class CanvasContext extends RenderContext {\n    static get WIDTH() {\n        return 600;\n    }\n    static get HEIGHT() {\n        return 400;\n    }\n    static get CANVAS_BROWSER_SIZE_LIMIT() {\n        return 32767;\n    }\n    static sanitizeCanvasDims(width, height) {\n        const limit = this.CANVAS_BROWSER_SIZE_LIMIT;\n        if (Math.max(width, height) > limit) {\n            warn('Canvas dimensions exceed browser limit. Cropping to ' + limit);\n            if (width > limit) {\n                width = limit;\n            }\n            if (height > limit) {\n                height = limit;\n            }\n        }\n        return [width, height];\n    }\n    constructor(context) {\n        super();\n        this.textHeight = 0;\n        this.context2D = context;\n        this.curTransfrom = context.getTransform();\n        if (!context.canvas) {\n            this.canvas = {\n                width: CanvasContext.WIDTH,\n                height: CanvasContext.HEIGHT,\n            };\n        }\n        else {\n            this.canvas = context.canvas;\n        }\n    }\n    clear() {\n        this.context2D.clearRect(0, 0, this.canvas.width, this.canvas.height);\n    }\n    openGroup(cls, id) {\n    }\n    closeGroup() {\n    }\n    openRotation(angleDegrees, x, y) {\n        this.curTransfrom = this.context2D.getTransform();\n        this.context2D.translate(x, y);\n        this.context2D.rotate((angleDegrees * Math.PI) / 180);\n        this.context2D.translate(-x, -y);\n    }\n    closeRotation() {\n        this.context2D.setTransform(this.curTransfrom);\n    }\n    add(child) {\n    }\n    setFillStyle(style) {\n        this.context2D.fillStyle = style;\n        return this;\n    }\n    setBackgroundFillStyle(style) {\n        return this;\n    }\n    setStrokeStyle(style) {\n        this.context2D.strokeStyle = style;\n        return this;\n    }\n    setShadowColor(color) {\n        this.context2D.shadowColor = color;\n        return this;\n    }\n    setShadowBlur(blur) {\n        const t = this.context2D.getTransform();\n        const scale = Math.sqrt(t.a * t.a + t.b * t.b + t.c * t.c + t.d * t.d);\n        this.context2D.shadowBlur = scale * blur;\n        return this;\n    }\n    setLineWidth(width) {\n        this.context2D.lineWidth = width;\n        return this;\n    }\n    setLineCap(capType) {\n        this.context2D.lineCap = capType;\n        return this;\n    }\n    setLineDash(dash) {\n        this.context2D.setLineDash(dash);\n        return this;\n    }\n    scale(x, y) {\n        this.context2D.scale(x, y);\n        return this;\n    }\n    resize(width, height, devicePixelRatio) {\n        var _a;\n        const canvas = this.context2D.canvas;\n        const dpr = (_a = devicePixelRatio !== null && devicePixelRatio !== void 0 ? devicePixelRatio : globalObject().devicePixelRatio) !== null && _a !== void 0 ? _a : 1;\n        [width, height] = CanvasContext.sanitizeCanvasDims(width * dpr, height * dpr);\n        width = (width / dpr) | 0;\n        height = (height / dpr) | 0;\n        canvas.width = width * dpr;\n        canvas.height = height * dpr;\n        if (isHTMLCanvas(canvas)) {\n            canvas.style.width = width + 'px';\n            canvas.style.height = height + 'px';\n        }\n        return this.scale(dpr, dpr);\n    }\n    rect(x, y, width, height) {\n        this.context2D.rect(x, y, width, height);\n        return this;\n    }\n    fillRect(x, y, width, height) {\n        this.context2D.fillRect(x, y, width, height);\n        return this;\n    }\n    pointerRect(x, y, width, height) {\n        return this;\n    }\n    clearRect(x, y, width, height) {\n        this.context2D.clearRect(x, y, width, height);\n        return this;\n    }\n    beginPath() {\n        this.context2D.beginPath();\n        return this;\n    }\n    moveTo(x, y) {\n        this.context2D.moveTo(x, y);\n        return this;\n    }\n    lineTo(x, y) {\n        this.context2D.lineTo(x, y);\n        return this;\n    }\n    bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y) {\n        this.context2D.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);\n        return this;\n    }\n    quadraticCurveTo(cpx, cpy, x, y) {\n        this.context2D.quadraticCurveTo(cpx, cpy, x, y);\n        return this;\n    }\n    arc(x, y, radius, startAngle, endAngle, counterclockwise) {\n        this.context2D.arc(x, y, radius, startAngle, endAngle, counterclockwise);\n        return this;\n    }\n    fill() {\n        this.context2D.fill();\n        return this;\n    }\n    stroke() {\n        this.context2D.stroke();\n        return this;\n    }\n    closePath() {\n        this.context2D.closePath();\n        return this;\n    }\n    measureText(text) {\n        const metrics = this.context2D.measureText(text);\n        let y = 0;\n        let height = 0;\n        if (metrics.fontBoundingBoxAscent) {\n            y = -metrics.fontBoundingBoxAscent;\n            height = metrics.fontBoundingBoxDescent + metrics.fontBoundingBoxAscent;\n        }\n        else {\n            y = -metrics.actualBoundingBoxAscent;\n            height = metrics.actualBoundingBoxDescent + metrics.actualBoundingBoxAscent;\n        }\n        return {\n            x: 0,\n            y: y,\n            width: metrics.width,\n            height: height,\n        };\n    }\n    fillText(text, x, y) {\n        this.context2D.fillText(text, x, y);\n        return this;\n    }\n    save() {\n        this.context2D.save();\n        return this;\n    }\n    restore() {\n        this.context2D.restore();\n        return this;\n    }\n    set fillStyle(style) {\n        this.context2D.fillStyle = style;\n    }\n    get fillStyle() {\n        return this.context2D.fillStyle;\n    }\n    set strokeStyle(style) {\n        this.context2D.strokeStyle = style;\n    }\n    get strokeStyle() {\n        return this.context2D.strokeStyle;\n    }\n    setFont(f, size, weight, style) {\n        const fontInfo = Font.validate(f, size, weight, style);\n        this.context2D.font = Font.toCSSString(fontInfo);\n        this.textHeight = Font.convertSizeToPixelValue(fontInfo.size);\n        return this;\n    }\n    getFont() {\n        return this.context2D.font;\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,EAAEC,IAAI,QAAQ,WAAW;AAC9C,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAO,MAAMC,aAAa,SAASJ,aAAa,CAAC;EAC7C,WAAWK,KAAKA,CAAA,EAAG;IACf,OAAO,GAAG;EACd;EACA,WAAWC,MAAMA,CAAA,EAAG;IAChB,OAAO,GAAG;EACd;EACA,WAAWC,yBAAyBA,CAAA,EAAG;IACnC,OAAO,KAAK;EAChB;EACA,OAAOC,kBAAkBA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACrC,MAAMC,KAAK,GAAG,IAAI,CAACJ,yBAAyB;IAC5C,IAAIK,IAAI,CAACC,GAAG,CAACJ,KAAK,EAAEC,MAAM,CAAC,GAAGC,KAAK,EAAE;MACjCT,IAAI,CAAC,sDAAsD,GAAGS,KAAK,CAAC;MACpE,IAAIF,KAAK,GAAGE,KAAK,EAAE;QACfF,KAAK,GAAGE,KAAK;MACjB;MACA,IAAID,MAAM,GAAGC,KAAK,EAAE;QAChBD,MAAM,GAAGC,KAAK;MAClB;IACJ;IACA,OAAO,CAACF,KAAK,EAAEC,MAAM,CAAC;EAC1B;EACAI,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGF,OAAO;IACxB,IAAI,CAACG,YAAY,GAAGH,OAAO,CAACI,YAAY,CAAC,CAAC;IAC1C,IAAI,CAACJ,OAAO,CAACK,MAAM,EAAE;MACjB,IAAI,CAACA,MAAM,GAAG;QACVX,KAAK,EAAEL,aAAa,CAACC,KAAK;QAC1BK,MAAM,EAAEN,aAAa,CAACE;MAC1B,CAAC;IACL,CAAC,MACI;MACD,IAAI,CAACc,MAAM,GAAGL,OAAO,CAACK,MAAM;IAChC;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACJ,SAAS,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACF,MAAM,CAACX,KAAK,EAAE,IAAI,CAACW,MAAM,CAACV,MAAM,CAAC;EACzE;EACAa,SAASA,CAACC,GAAG,EAAEC,EAAE,EAAE,CACnB;EACAC,UAAUA,CAAA,EAAG,CACb;EACAC,YAAYA,CAACC,YAAY,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACD,SAAS,CAACE,YAAY,CAAC,CAAC;IACjD,IAAI,CAACF,SAAS,CAACc,SAAS,CAACF,CAAC,EAAEC,CAAC,CAAC;IAC9B,IAAI,CAACb,SAAS,CAACe,MAAM,CAAEJ,YAAY,GAAGhB,IAAI,CAACqB,EAAE,GAAI,GAAG,CAAC;IACrD,IAAI,CAAChB,SAAS,CAACc,SAAS,CAAC,CAACF,CAAC,EAAE,CAACC,CAAC,CAAC;EACpC;EACAI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACjB,SAAS,CAACkB,YAAY,CAAC,IAAI,CAACjB,YAAY,CAAC;EAClD;EACAkB,GAAGA,CAACC,KAAK,EAAE,CACX;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACtB,SAAS,CAACuB,SAAS,GAAGD,KAAK;IAChC,OAAO,IAAI;EACf;EACAE,sBAAsBA,CAACF,KAAK,EAAE;IAC1B,OAAO,IAAI;EACf;EACAG,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,CAACtB,SAAS,CAAC0B,WAAW,GAAGJ,KAAK;IAClC,OAAO,IAAI;EACf;EACAK,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAAC5B,SAAS,CAAC6B,WAAW,GAAGD,KAAK;IAClC,OAAO,IAAI;EACf;EACAE,aAAaA,CAACC,IAAI,EAAE;IAChB,MAAMC,CAAC,GAAG,IAAI,CAAChC,SAAS,CAACE,YAAY,CAAC,CAAC;IACvC,MAAM+B,KAAK,GAAGtC,IAAI,CAACuC,IAAI,CAACF,CAAC,CAACG,CAAC,GAAGH,CAAC,CAACG,CAAC,GAAGH,CAAC,CAACI,CAAC,GAAGJ,CAAC,CAACI,CAAC,GAAGJ,CAAC,CAACK,CAAC,GAAGL,CAAC,CAACK,CAAC,GAAGL,CAAC,CAACM,CAAC,GAAGN,CAAC,CAACM,CAAC,CAAC;IACtE,IAAI,CAACtC,SAAS,CAACuC,UAAU,GAAGN,KAAK,GAAGF,IAAI;IACxC,OAAO,IAAI;EACf;EACAS,YAAYA,CAAChD,KAAK,EAAE;IAChB,IAAI,CAACQ,SAAS,CAACyC,SAAS,GAAGjD,KAAK;IAChC,OAAO,IAAI;EACf;EACAkD,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAAC3C,SAAS,CAAC4C,OAAO,GAAGD,OAAO;IAChC,OAAO,IAAI;EACf;EACAE,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAAC9C,SAAS,CAAC6C,WAAW,CAACC,IAAI,CAAC;IAChC,OAAO,IAAI;EACf;EACAb,KAAKA,CAACrB,CAAC,EAAEC,CAAC,EAAE;IACR,IAAI,CAACb,SAAS,CAACiC,KAAK,CAACrB,CAAC,EAAEC,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACAkC,MAAMA,CAACvD,KAAK,EAAEC,MAAM,EAAEuD,gBAAgB,EAAE;IACpC,IAAIC,EAAE;IACN,MAAM9C,MAAM,GAAG,IAAI,CAACH,SAAS,CAACG,MAAM;IACpC,MAAM+C,GAAG,GAAG,CAACD,EAAE,GAAGD,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGhE,YAAY,CAAC,CAAC,CAACgE,gBAAgB,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACnK,CAACzD,KAAK,EAAEC,MAAM,CAAC,GAAGN,aAAa,CAACI,kBAAkB,CAACC,KAAK,GAAG0D,GAAG,EAAEzD,MAAM,GAAGyD,GAAG,CAAC;IAC7E1D,KAAK,GAAIA,KAAK,GAAG0D,GAAG,GAAI,CAAC;IACzBzD,MAAM,GAAIA,MAAM,GAAGyD,GAAG,GAAI,CAAC;IAC3B/C,MAAM,CAACX,KAAK,GAAGA,KAAK,GAAG0D,GAAG;IAC1B/C,MAAM,CAACV,MAAM,GAAGA,MAAM,GAAGyD,GAAG;IAC5B,IAAIhE,YAAY,CAACiB,MAAM,CAAC,EAAE;MACtBA,MAAM,CAACmB,KAAK,CAAC9B,KAAK,GAAGA,KAAK,GAAG,IAAI;MACjCW,MAAM,CAACmB,KAAK,CAAC7B,MAAM,GAAGA,MAAM,GAAG,IAAI;IACvC;IACA,OAAO,IAAI,CAACwC,KAAK,CAACiB,GAAG,EAAEA,GAAG,CAAC;EAC/B;EACAC,IAAIA,CAACvC,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,EAAE;IACtB,IAAI,CAACO,SAAS,CAACmD,IAAI,CAACvC,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACA2D,QAAQA,CAACxC,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACO,SAAS,CAACoD,QAAQ,CAACxC,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,CAAC;IAC5C,OAAO,IAAI;EACf;EACA4D,WAAWA,CAACzC,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,EAAE;IAC7B,OAAO,IAAI;EACf;EACAY,SAASA,CAACO,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,EAAE;IAC3B,IAAI,CAACO,SAAS,CAACK,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAErB,KAAK,EAAEC,MAAM,CAAC;IAC7C,OAAO,IAAI;EACf;EACA6D,SAASA,CAAA,EAAG;IACR,IAAI,CAACtD,SAAS,CAACsD,SAAS,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACAC,MAAMA,CAAC3C,CAAC,EAAEC,CAAC,EAAE;IACT,IAAI,CAACb,SAAS,CAACuD,MAAM,CAAC3C,CAAC,EAAEC,CAAC,CAAC;IAC3B,OAAO,IAAI;EACf;EACA2C,MAAMA,CAAC5C,CAAC,EAAEC,CAAC,EAAE;IACT,IAAI,CAACb,SAAS,CAACwD,MAAM,CAAC5C,CAAC,EAAEC,CAAC,CAAC;IAC3B,OAAO,IAAI;EACf;EACA4C,aAAaA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEjD,CAAC,EAAEC,CAAC,EAAE;IACxC,IAAI,CAACb,SAAS,CAACyD,aAAa,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEjD,CAAC,EAAEC,CAAC,CAAC;IAC1D,OAAO,IAAI;EACf;EACAiD,gBAAgBA,CAACC,GAAG,EAAEC,GAAG,EAAEpD,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAI,CAACb,SAAS,CAAC8D,gBAAgB,CAACC,GAAG,EAAEC,GAAG,EAAEpD,CAAC,EAAEC,CAAC,CAAC;IAC/C,OAAO,IAAI;EACf;EACAoD,GAAGA,CAACrD,CAAC,EAAEC,CAAC,EAAEqD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,gBAAgB,EAAE;IACtD,IAAI,CAACrE,SAAS,CAACiE,GAAG,CAACrD,CAAC,EAAEC,CAAC,EAAEqD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,gBAAgB,CAAC;IACxE,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACtE,SAAS,CAACsE,IAAI,CAAC,CAAC;IACrB,OAAO,IAAI;EACf;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACvE,SAAS,CAACuE,MAAM,CAAC,CAAC;IACvB,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACxE,SAAS,CAACwE,SAAS,CAAC,CAAC;IAC1B,OAAO,IAAI;EACf;EACAC,WAAWA,CAACC,IAAI,EAAE;IACd,MAAMC,OAAO,GAAG,IAAI,CAAC3E,SAAS,CAACyE,WAAW,CAACC,IAAI,CAAC;IAChD,IAAI7D,CAAC,GAAG,CAAC;IACT,IAAIpB,MAAM,GAAG,CAAC;IACd,IAAIkF,OAAO,CAACC,qBAAqB,EAAE;MAC/B/D,CAAC,GAAG,CAAC8D,OAAO,CAACC,qBAAqB;MAClCnF,MAAM,GAAGkF,OAAO,CAACE,sBAAsB,GAAGF,OAAO,CAACC,qBAAqB;IAC3E,CAAC,MACI;MACD/D,CAAC,GAAG,CAAC8D,OAAO,CAACG,uBAAuB;MACpCrF,MAAM,GAAGkF,OAAO,CAACI,wBAAwB,GAAGJ,OAAO,CAACG,uBAAuB;IAC/E;IACA,OAAO;MACHlE,CAAC,EAAE,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJrB,KAAK,EAAEmF,OAAO,CAACnF,KAAK;MACpBC,MAAM,EAAEA;IACZ,CAAC;EACL;EACAuF,QAAQA,CAACN,IAAI,EAAE9D,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACb,SAAS,CAACgF,QAAQ,CAACN,IAAI,EAAE9D,CAAC,EAAEC,CAAC,CAAC;IACnC,OAAO,IAAI;EACf;EACAoE,IAAIA,CAAA,EAAG;IACH,IAAI,CAACjF,SAAS,CAACiF,IAAI,CAAC,CAAC;IACrB,OAAO,IAAI;EACf;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAClF,SAAS,CAACkF,OAAO,CAAC,CAAC;IACxB,OAAO,IAAI;EACf;EACA,IAAI3D,SAASA,CAACD,KAAK,EAAE;IACjB,IAAI,CAACtB,SAAS,CAACuB,SAAS,GAAGD,KAAK;EACpC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvB,SAAS,CAACuB,SAAS;EACnC;EACA,IAAIG,WAAWA,CAACJ,KAAK,EAAE;IACnB,IAAI,CAACtB,SAAS,CAAC0B,WAAW,GAAGJ,KAAK;EACtC;EACA,IAAII,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1B,SAAS,CAAC0B,WAAW;EACrC;EACAyD,OAAOA,CAACC,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEhE,KAAK,EAAE;IAC5B,MAAMiE,QAAQ,GAAGzG,IAAI,CAAC0G,QAAQ,CAACJ,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEhE,KAAK,CAAC;IACtD,IAAI,CAACtB,SAAS,CAACyF,IAAI,GAAG3G,IAAI,CAAC4G,WAAW,CAACH,QAAQ,CAAC;IAChD,IAAI,CAACxF,UAAU,GAAGjB,IAAI,CAAC6G,uBAAuB,CAACJ,QAAQ,CAACF,IAAI,CAAC;IAC7D,OAAO,IAAI;EACf;EACAO,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC5F,SAAS,CAACyF,IAAI;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}