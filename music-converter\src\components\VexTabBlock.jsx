import React, { useEffect, useRef } from 'react';

export function updateVexTabBlock(blockId) {
  const wrapper = document.getElementById(blockId);
  const curVexText = wrapper.querySelector('textarea.editor');
  curVexText.dispatchEvent(new KeyboardEvent("keyup", { bubbles: true, cancelable: true, key: " " , code: "Space" }));
}

function VexTabBlock({ source, id, editorHeight }) {
  const divRef = useRef(null);
  const originalSourceRef = useRef(source);

  useEffect(() => {
    const el = divRef.current;
    if (!el || !window.VexTab || !window.VexTab.Div) return;

    const handleInput = (e) => {
      const curText = e.target.textContent;
      originalSourceRef.current = curText;

      setTimeout(() => {
        new window.VexTab.Div(el);
      }, 0);
    }

    el.addEventListener('input', handleInput);
    console.log("Original Source: ", originalSourceRef.current);

    originalSourceRef.current = source;
    el.textContent = source;
    while (el.firstChild) {
      el.removeChild(el.firstChild);
    }
    new window.VexTab.Div(el); 
  }, [source, id]);

  const defaultText = `tabstave notation=true tablature=false key=G time=4/4 \nnotes A/4`;

  return (
    <div
      className="vextab-auto"
      style={{ minHeight: '0rem', display: 'hidden' }}
      editor="true"
      editor-height={editorHeight}
      contentEditable="true"
      id={id}
      ref={divRef}
    >
      {defaultText}
    </div>
  )  
}

export default VexTabBlock;