import React, { useEffect, useRef } from 'react';

function VexTabBlock({ source, id }) {
  const divRef = useRef(null);

  useEffect(() => {
    const el = divRef.current;
    if (!el || !window.VexTab || !window.VexTab.Div) return;

    el.textContent = source;
    while (el.firstChild) {
      el.removeChild(el.firstChild);
    }

    new window.VexTab.Div(el);
  }, [source]);

  return (
    <div
      className="vextab-auto"
      style={{ minHeight: '6rem' }}
      editor="true"
      contentEditable="true"
      id={id}
      ref={divRef}
    />
  )  
}

export default VexTabBlock;