{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isGraceNote, isStaveNote, isStemmableNote, isTabNote } from './typeguard.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (Articulation.DEBUG) log('VexFlow.Articulation', args);\n}\nconst {\n  ABOVE,\n  BELOW\n} = Modifier.Position;\nfunction roundToNearestHalf(mathFn, value) {\n  return mathFn(value / 0.5) * 0.5;\n}\nfunction isWithinLines(line, position) {\n  return position === ABOVE ? line <= 5 : line >= 1;\n}\nfunction getRoundingFunction(line, position) {\n  if (isWithinLines(line, position)) {\n    if (position === ABOVE) {\n      return Math.ceil;\n    } else {\n      return Math.floor;\n    }\n  } else {\n    return Math.round;\n  }\n}\nfunction snapLineToStaff(canSitBetweenLines, line, position, offsetDirection) {\n  const snappedLine = roundToNearestHalf(getRoundingFunction(line, position), line);\n  const canSnapToStaffSpace = canSitBetweenLines && isWithinLines(snappedLine, position);\n  const onStaffLine = snappedLine % 1 === 0;\n  if (canSnapToStaffSpace && onStaffLine) {\n    const HALF_STAFF_SPACE = 0.5;\n    return snappedLine + HALF_STAFF_SPACE * -offsetDirection;\n  } else {\n    return snappedLine;\n  }\n}\nconst isStaveOrGraceNote = note => isStaveNote(note) || isGraceNote(note);\nexport function getTopY(note, textLine) {\n  const stemDirection = note.getStemDirection();\n  const {\n    topY: stemTipY,\n    baseY: stemBaseY\n  } = note.getStemExtents();\n  if (isStaveOrGraceNote(note)) {\n    if (note.hasStem()) {\n      if (stemDirection === Stem.UP) {\n        return stemTipY;\n      } else {\n        return stemBaseY;\n      }\n    } else {\n      return Math.min(...note.getYs());\n    }\n  } else if (isTabNote(note)) {\n    if (note.hasStem()) {\n      if (stemDirection === Stem.UP) {\n        return stemTipY;\n      } else {\n        return note.checkStave().getYForTopText(textLine);\n      }\n    } else {\n      return note.checkStave().getYForTopText(textLine);\n    }\n  } else {\n    throw new RuntimeError('UnknownCategory', 'Only can get the top and bottom ys of stavenotes and tabnotes');\n  }\n}\nexport function getBottomY(note, textLine) {\n  const stemDirection = note.getStemDirection();\n  const {\n    topY: stemTipY,\n    baseY: stemBaseY\n  } = note.getStemExtents();\n  if (isStaveOrGraceNote(note)) {\n    if (note.hasStem()) {\n      if (stemDirection === Stem.UP) {\n        return stemBaseY;\n      } else {\n        return stemTipY;\n      }\n    } else {\n      return Math.max(...note.getYs());\n    }\n  } else if (isTabNote(note)) {\n    if (note.hasStem()) {\n      if (stemDirection === Stem.UP) {\n        return note.checkStave().getYForBottomText(textLine);\n      } else {\n        return stemTipY;\n      }\n    } else {\n      return note.checkStave().getYForBottomText(textLine);\n    }\n  } else {\n    throw new RuntimeError('UnknownCategory', 'Only can get the top and bottom ys of stavenotes and tabnotes');\n  }\n}\nexport function getInitialOffset(note, position) {\n  const isOnStemTip = position === ABOVE && note.getStemDirection() === Stem.UP || position === BELOW && note.getStemDirection() === Stem.DOWN;\n  if (isStaveOrGraceNote(note)) {\n    if (note.hasStem() && isOnStemTip) {\n      return 0.5;\n    } else {\n      return 1;\n    }\n  } else {\n    if (note.hasStem() && isOnStemTip) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n}\nexport class Articulation extends Modifier {\n  static get CATEGORY() {\n    return \"Articulation\";\n  }\n  static format(articulations, state) {\n    if (!articulations || articulations.length === 0) return false;\n    const margin = 0.5;\n    let maxGlyphWidth = 0;\n    const getIncrement = (articulation, line, position) => roundToNearestHalf(getRoundingFunction(line, position), articulation.height / 10 + margin);\n    articulations.forEach(articulation => {\n      const note = articulation.checkAttachedNote();\n      maxGlyphWidth = Math.max(note.getGlyphWidth(), maxGlyphWidth);\n      let lines = 5;\n      const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n      let stemHeight = 0;\n      if (isStemmableNote(note)) {\n        const stem = note.getStem();\n        if (stem) {\n          stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n        }\n      }\n      const stave = note.getStave();\n      if (stave) {\n        lines = stave.getNumLines();\n      }\n      if (articulation.getPosition() === ABOVE) {\n        let noteLine = note.getLineNumber(true);\n        if (stemDirection === Stem.UP) {\n          noteLine += stemHeight;\n        }\n        let increment = getIncrement(articulation, state.topTextLine, ABOVE);\n        const curTop = noteLine + state.topTextLine + 0.5;\n        if (!articulation.articulation.betweenLines && curTop < lines) {\n          increment += lines - curTop;\n        }\n        articulation.setTextLine(state.topTextLine);\n        state.topTextLine += increment;\n        articulation.setOrigin(0.5, 1);\n      } else if (articulation.getPosition() === BELOW) {\n        let noteLine = Math.max(lines - note.getLineNumber(), 0);\n        if (stemDirection === Stem.DOWN) {\n          noteLine += stemHeight;\n        }\n        let increment = getIncrement(articulation, state.textLine, BELOW);\n        const curBottom = noteLine + state.textLine + 0.5;\n        if (!articulation.articulation.betweenLines && curBottom < lines) {\n          increment += lines - curBottom;\n        }\n        articulation.setTextLine(state.textLine);\n        state.textLine += increment;\n        articulation.setOrigin(0.5, 0);\n      }\n    });\n    const width = articulations.map(articulation => articulation.getWidth()).reduce((maxWidth, articWidth) => Math.max(articWidth, maxWidth));\n    const overlap = Math.min(Math.max(width - maxGlyphWidth, 0), Math.max(width - (state.leftShift + state.rightShift), 0));\n    state.leftShift += overlap / 2;\n    state.rightShift += overlap / 2;\n    return true;\n  }\n  static easyScoreHook({\n    articulations\n  }, note, builder) {\n    if (!articulations) return;\n    const articNameToCode = {\n      staccato: 'a.',\n      tenuto: 'a-',\n      accent: 'a>'\n    };\n    articulations.split(',').map(articString => articString.trim().split('.')).map(([name, position]) => {\n      const artic = {\n        type: articNameToCode[name]\n      };\n      if (position) artic.position = Modifier.PositionString[position];\n      return builder.getFactory().Articulation(artic);\n    }).map(artic => note.addModifier(artic, 0));\n  }\n  constructor(type) {\n    var _a;\n    super();\n    this.heightShift = 0;\n    this.type = type;\n    this.position = ABOVE;\n    if (!Tables.articulationCodes(this.type)) {\n      if (((_a = this.type.codePointAt(0)) !== null && _a !== void 0 ? _a : 0) % 2 === 0) this.position = ABOVE;else this.position = BELOW;\n    }\n    this.articulation = {\n      betweenLines: false\n    };\n    this.reset();\n  }\n  reset() {\n    this.articulation = Tables.articulationCodes(this.type);\n    if (!this.articulation) {\n      this.articulation = {\n        code: this.type,\n        betweenLines: false\n      };\n    }\n    const code = (this.position === ABOVE ? this.articulation.aboveCode : this.articulation.belowCode) || this.articulation.code || Glyphs.null;\n    this.text = code;\n  }\n  setBetweenLines(betweenLines = true) {\n    this.articulation.betweenLines = betweenLines;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const index = this.checkIndex();\n    const {\n      position,\n      textLine\n    } = this;\n    const canSitBetweenLines = this.articulation.betweenLines;\n    const stave = note.checkStave();\n    const staffSpace = stave.getSpacingBetweenLines();\n    const isTab = isTabNote(note);\n    const {\n      x\n    } = note.getModifierStartXY(position, index);\n    const shouldSitOutsideStaff = !canSitBetweenLines || isTab;\n    const initialOffset = getInitialOffset(note, position);\n    let y = {\n      [ABOVE]: () => {\n        const y = getTopY(note, textLine) - (textLine + initialOffset) * staffSpace;\n        return shouldSitOutsideStaff ? Math.min(stave.getYForTopText(Articulation.INITIAL_OFFSET), y) : y;\n      },\n      [BELOW]: () => {\n        const y = getBottomY(note, textLine) + (textLine + initialOffset) * staffSpace;\n        return shouldSitOutsideStaff ? Math.max(stave.getYForBottomText(Articulation.INITIAL_OFFSET), y) : y;\n      }\n    }[position]();\n    if (!isTab) {\n      const offsetDirection = position === ABOVE ? -1 : +1;\n      const noteLine = note.getKeyProps()[index].line;\n      const distanceFromNote = (note.getYs()[index] - y) / staffSpace;\n      const articLine = distanceFromNote + Number(noteLine);\n      const snappedLine = snapLineToStaff(canSitBetweenLines, articLine, position, offsetDirection);\n      if (isWithinLines(snappedLine, position)) this.setOrigin(0.5, 0.5);\n      y += Math.abs(snappedLine - articLine) * staffSpace * offsetDirection;\n    }\n    L(`Rendering articulation at (x: ${x}, y: ${y})`);\n    this.x = x;\n    this.y = y;\n    this.renderText(ctx, 0, 0);\n  }\n}\nArticulation.DEBUG = false;\nArticulation.INITIAL_OFFSET = -0.5;", "map": {"version": 3, "names": ["Glyphs", "Modifier", "<PERSON><PERSON>", "Tables", "isGraceNote", "isStaveNote", "isStemmableNote", "isTabNote", "log", "RuntimeError", "L", "args", "Articulation", "DEBUG", "ABOVE", "BELOW", "Position", "roundToNearestHalf", "mathFn", "value", "isWithinLines", "line", "position", "getRoundingFunction", "Math", "ceil", "floor", "round", "snapLineToStaff", "canSitBetweenLines", "offsetDirection", "snappedLine", "canSnapToStaffSpace", "onStaffLine", "HALF_STAFF_SPACE", "isStaveOrGraceNote", "note", "getTopY", "textLine", "stemDirection", "getStemDirection", "topY", "stemTipY", "baseY", "stemBaseY", "getStemExtents", "hasStem", "UP", "min", "getYs", "checkStave", "getYForTopText", "getBottomY", "max", "getYForBottomText", "getInitialOffset", "isOnStemTip", "DOWN", "CATEGORY", "format", "articulations", "state", "length", "margin", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getIncrement", "articulation", "height", "for<PERSON>ach", "checkAttachedNote", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lines", "stemHeight", "stem", "getStem", "abs", "getHeight", "STAVE_LINE_DISTANCE", "stave", "getStave", "getNumLines", "getPosition", "noteLine", "getLineNumber", "increment", "topTextLine", "curTop", "betweenLines", "setTextLine", "<PERSON><PERSON><PERSON><PERSON>", "cur<PERSON><PERSON><PERSON>", "width", "map", "getWidth", "reduce", "max<PERSON><PERSON><PERSON>", "articWidth", "overlap", "leftShift", "rightShift", "easyScoreHook", "builder", "articNameToCode", "staccato", "tenuto", "accent", "split", "articString", "trim", "name", "artic", "type", "PositionString", "getFactory", "addModifier", "constructor", "_a", "heightShift", "articulationCodes", "codePointAt", "reset", "code", "aboveCode", "belowCode", "null", "text", "setBetweenLines", "draw", "ctx", "checkContext", "setRendered", "index", "checkIndex", "staffSpace", "getSpacingBetweenLines", "isTab", "x", "getModifierStartXY", "shouldSitOutsideStaff", "initialOffset", "y", "INITIAL_OFFSET", "getKeyProps", "distanceFromNote", "articLine", "Number", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/articulation.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isGraceNote, isStaveNote, isStemmableNote, isTabNote } from './typeguard.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (Articulation.DEBUG)\n        log('VexFlow.Articulation', args);\n}\nconst { ABOVE, BELOW } = Modifier.Position;\nfunction roundToNearestHalf(mathFn, value) {\n    return mathFn(value / 0.5) * 0.5;\n}\nfunction isWithinLines(line, position) {\n    return position === ABOVE ? line <= 5 : line >= 1;\n}\nfunction getRoundingFunction(line, position) {\n    if (isWithinLines(line, position)) {\n        if (position === ABOVE) {\n            return Math.ceil;\n        }\n        else {\n            return Math.floor;\n        }\n    }\n    else {\n        return Math.round;\n    }\n}\nfunction snapLineToStaff(canSitBetweenLines, line, position, offsetDirection) {\n    const snappedLine = roundToNearestHalf(getRoundingFunction(line, position), line);\n    const canSnapToStaffSpace = canSitBetweenLines && isWithinLines(snappedLine, position);\n    const onStaffLine = snappedLine % 1 === 0;\n    if (canSnapToStaffSpace && onStaffLine) {\n        const HALF_STAFF_SPACE = 0.5;\n        return snappedLine + HALF_STAFF_SPACE * -offsetDirection;\n    }\n    else {\n        return snappedLine;\n    }\n}\nconst isStaveOrGraceNote = (note) => isStaveNote(note) || isGraceNote(note);\nexport function getTopY(note, textLine) {\n    const stemDirection = note.getStemDirection();\n    const { topY: stemTipY, baseY: stemBaseY } = note.getStemExtents();\n    if (isStaveOrGraceNote(note)) {\n        if (note.hasStem()) {\n            if (stemDirection === Stem.UP) {\n                return stemTipY;\n            }\n            else {\n                return stemBaseY;\n            }\n        }\n        else {\n            return Math.min(...note.getYs());\n        }\n    }\n    else if (isTabNote(note)) {\n        if (note.hasStem()) {\n            if (stemDirection === Stem.UP) {\n                return stemTipY;\n            }\n            else {\n                return note.checkStave().getYForTopText(textLine);\n            }\n        }\n        else {\n            return note.checkStave().getYForTopText(textLine);\n        }\n    }\n    else {\n        throw new RuntimeError('UnknownCategory', 'Only can get the top and bottom ys of stavenotes and tabnotes');\n    }\n}\nexport function getBottomY(note, textLine) {\n    const stemDirection = note.getStemDirection();\n    const { topY: stemTipY, baseY: stemBaseY } = note.getStemExtents();\n    if (isStaveOrGraceNote(note)) {\n        if (note.hasStem()) {\n            if (stemDirection === Stem.UP) {\n                return stemBaseY;\n            }\n            else {\n                return stemTipY;\n            }\n        }\n        else {\n            return Math.max(...note.getYs());\n        }\n    }\n    else if (isTabNote(note)) {\n        if (note.hasStem()) {\n            if (stemDirection === Stem.UP) {\n                return note.checkStave().getYForBottomText(textLine);\n            }\n            else {\n                return stemTipY;\n            }\n        }\n        else {\n            return note.checkStave().getYForBottomText(textLine);\n        }\n    }\n    else {\n        throw new RuntimeError('UnknownCategory', 'Only can get the top and bottom ys of stavenotes and tabnotes');\n    }\n}\nexport function getInitialOffset(note, position) {\n    const isOnStemTip = (position === ABOVE && note.getStemDirection() === Stem.UP) ||\n        (position === BELOW && note.getStemDirection() === Stem.DOWN);\n    if (isStaveOrGraceNote(note)) {\n        if (note.hasStem() && isOnStemTip) {\n            return 0.5;\n        }\n        else {\n            return 1;\n        }\n    }\n    else {\n        if (note.hasStem() && isOnStemTip) {\n            return 1;\n        }\n        else {\n            return 0;\n        }\n    }\n}\nexport class Articulation extends Modifier {\n    static get CATEGORY() {\n        return \"Articulation\";\n    }\n    static format(articulations, state) {\n        if (!articulations || articulations.length === 0)\n            return false;\n        const margin = 0.5;\n        let maxGlyphWidth = 0;\n        const getIncrement = (articulation, line, position) => roundToNearestHalf(getRoundingFunction(line, position), articulation.height / 10 + margin);\n        articulations.forEach((articulation) => {\n            const note = articulation.checkAttachedNote();\n            maxGlyphWidth = Math.max(note.getGlyphWidth(), maxGlyphWidth);\n            let lines = 5;\n            const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n            let stemHeight = 0;\n            if (isStemmableNote(note)) {\n                const stem = note.getStem();\n                if (stem) {\n                    stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n                }\n            }\n            const stave = note.getStave();\n            if (stave) {\n                lines = stave.getNumLines();\n            }\n            if (articulation.getPosition() === ABOVE) {\n                let noteLine = note.getLineNumber(true);\n                if (stemDirection === Stem.UP) {\n                    noteLine += stemHeight;\n                }\n                let increment = getIncrement(articulation, state.topTextLine, ABOVE);\n                const curTop = noteLine + state.topTextLine + 0.5;\n                if (!articulation.articulation.betweenLines && curTop < lines) {\n                    increment += lines - curTop;\n                }\n                articulation.setTextLine(state.topTextLine);\n                state.topTextLine += increment;\n                articulation.setOrigin(0.5, 1);\n            }\n            else if (articulation.getPosition() === BELOW) {\n                let noteLine = Math.max(lines - note.getLineNumber(), 0);\n                if (stemDirection === Stem.DOWN) {\n                    noteLine += stemHeight;\n                }\n                let increment = getIncrement(articulation, state.textLine, BELOW);\n                const curBottom = noteLine + state.textLine + 0.5;\n                if (!articulation.articulation.betweenLines && curBottom < lines) {\n                    increment += lines - curBottom;\n                }\n                articulation.setTextLine(state.textLine);\n                state.textLine += increment;\n                articulation.setOrigin(0.5, 0);\n            }\n        });\n        const width = articulations\n            .map((articulation) => articulation.getWidth())\n            .reduce((maxWidth, articWidth) => Math.max(articWidth, maxWidth));\n        const overlap = Math.min(Math.max(width - maxGlyphWidth, 0), Math.max(width - (state.leftShift + state.rightShift), 0));\n        state.leftShift += overlap / 2;\n        state.rightShift += overlap / 2;\n        return true;\n    }\n    static easyScoreHook({ articulations }, note, builder) {\n        if (!articulations)\n            return;\n        const articNameToCode = {\n            staccato: 'a.',\n            tenuto: 'a-',\n            accent: 'a>',\n        };\n        articulations\n            .split(',')\n            .map((articString) => articString.trim().split('.'))\n            .map(([name, position]) => {\n            const artic = { type: articNameToCode[name] };\n            if (position)\n                artic.position = Modifier.PositionString[position];\n            return builder.getFactory().Articulation(artic);\n        })\n            .map((artic) => note.addModifier(artic, 0));\n    }\n    constructor(type) {\n        var _a;\n        super();\n        this.heightShift = 0;\n        this.type = type;\n        this.position = ABOVE;\n        if (!Tables.articulationCodes(this.type)) {\n            if (((_a = this.type.codePointAt(0)) !== null && _a !== void 0 ? _a : 0) % 2 === 0)\n                this.position = ABOVE;\n            else\n                this.position = BELOW;\n        }\n        this.articulation = { betweenLines: false };\n        this.reset();\n    }\n    reset() {\n        this.articulation = Tables.articulationCodes(this.type);\n        if (!this.articulation) {\n            this.articulation = { code: this.type, betweenLines: false };\n        }\n        const code = (this.position === ABOVE ? this.articulation.aboveCode : this.articulation.belowCode) ||\n            this.articulation.code ||\n            Glyphs.null;\n        this.text = code;\n    }\n    setBetweenLines(betweenLines = true) {\n        this.articulation.betweenLines = betweenLines;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const index = this.checkIndex();\n        const { position, textLine } = this;\n        const canSitBetweenLines = this.articulation.betweenLines;\n        const stave = note.checkStave();\n        const staffSpace = stave.getSpacingBetweenLines();\n        const isTab = isTabNote(note);\n        const { x } = note.getModifierStartXY(position, index);\n        const shouldSitOutsideStaff = !canSitBetweenLines || isTab;\n        const initialOffset = getInitialOffset(note, position);\n        let y = {\n            [ABOVE]: () => {\n                const y = getTopY(note, textLine) - (textLine + initialOffset) * staffSpace;\n                return shouldSitOutsideStaff ? Math.min(stave.getYForTopText(Articulation.INITIAL_OFFSET), y) : y;\n            },\n            [BELOW]: () => {\n                const y = getBottomY(note, textLine) + (textLine + initialOffset) * staffSpace;\n                return shouldSitOutsideStaff ? Math.max(stave.getYForBottomText(Articulation.INITIAL_OFFSET), y) : y;\n            },\n        }[position]();\n        if (!isTab) {\n            const offsetDirection = position === ABOVE ? -1 : +1;\n            const noteLine = note.getKeyProps()[index].line;\n            const distanceFromNote = (note.getYs()[index] - y) / staffSpace;\n            const articLine = distanceFromNote + Number(noteLine);\n            const snappedLine = snapLineToStaff(canSitBetweenLines, articLine, position, offsetDirection);\n            if (isWithinLines(snappedLine, position))\n                this.setOrigin(0.5, 0.5);\n            y += Math.abs(snappedLine - articLine) * staffSpace * offsetDirection;\n        }\n        L(`Rendering articulation at (x: ${x}, y: ${y})`);\n        this.x = x;\n        this.y = y;\n        this.renderText(ctx, 0, 0);\n    }\n}\nArticulation.DEBUG = false;\nArticulation.INITIAL_OFFSET = -0.5;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAEC,SAAS,QAAQ,gBAAgB;AACrF,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,YAAY,CAACC,KAAK,EAClBL,GAAG,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AACzC;AACA,MAAM;EAAEG,KAAK;EAAEC;AAAM,CAAC,GAAGd,QAAQ,CAACe,QAAQ;AAC1C,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACvC,OAAOD,MAAM,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACpC;AACA,SAASC,aAAaA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EACnC,OAAOA,QAAQ,KAAKR,KAAK,GAAGO,IAAI,IAAI,CAAC,GAAGA,IAAI,IAAI,CAAC;AACrD;AACA,SAASE,mBAAmBA,CAACF,IAAI,EAAEC,QAAQ,EAAE;EACzC,IAAIF,aAAa,CAACC,IAAI,EAAEC,QAAQ,CAAC,EAAE;IAC/B,IAAIA,QAAQ,KAAKR,KAAK,EAAE;MACpB,OAAOU,IAAI,CAACC,IAAI;IACpB,CAAC,MACI;MACD,OAAOD,IAAI,CAACE,KAAK;IACrB;EACJ,CAAC,MACI;IACD,OAAOF,IAAI,CAACG,KAAK;EACrB;AACJ;AACA,SAASC,eAAeA,CAACC,kBAAkB,EAAER,IAAI,EAAEC,QAAQ,EAAEQ,eAAe,EAAE;EAC1E,MAAMC,WAAW,GAAGd,kBAAkB,CAACM,mBAAmB,CAACF,IAAI,EAAEC,QAAQ,CAAC,EAAED,IAAI,CAAC;EACjF,MAAMW,mBAAmB,GAAGH,kBAAkB,IAAIT,aAAa,CAACW,WAAW,EAAET,QAAQ,CAAC;EACtF,MAAMW,WAAW,GAAGF,WAAW,GAAG,CAAC,KAAK,CAAC;EACzC,IAAIC,mBAAmB,IAAIC,WAAW,EAAE;IACpC,MAAMC,gBAAgB,GAAG,GAAG;IAC5B,OAAOH,WAAW,GAAGG,gBAAgB,GAAG,CAACJ,eAAe;EAC5D,CAAC,MACI;IACD,OAAOC,WAAW;EACtB;AACJ;AACA,MAAMI,kBAAkB,GAAIC,IAAI,IAAK/B,WAAW,CAAC+B,IAAI,CAAC,IAAIhC,WAAW,CAACgC,IAAI,CAAC;AAC3E,OAAO,SAASC,OAAOA,CAACD,IAAI,EAAEE,QAAQ,EAAE;EACpC,MAAMC,aAAa,GAAGH,IAAI,CAACI,gBAAgB,CAAC,CAAC;EAC7C,MAAM;IAAEC,IAAI,EAAEC,QAAQ;IAAEC,KAAK,EAAEC;EAAU,CAAC,GAAGR,IAAI,CAACS,cAAc,CAAC,CAAC;EAClE,IAAIV,kBAAkB,CAACC,IAAI,CAAC,EAAE;IAC1B,IAAIA,IAAI,CAACU,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIP,aAAa,KAAKrC,IAAI,CAAC6C,EAAE,EAAE;QAC3B,OAAOL,QAAQ;MACnB,CAAC,MACI;QACD,OAAOE,SAAS;MACpB;IACJ,CAAC,MACI;MACD,OAAOpB,IAAI,CAACwB,GAAG,CAAC,GAAGZ,IAAI,CAACa,KAAK,CAAC,CAAC,CAAC;IACpC;EACJ,CAAC,MACI,IAAI1C,SAAS,CAAC6B,IAAI,CAAC,EAAE;IACtB,IAAIA,IAAI,CAACU,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIP,aAAa,KAAKrC,IAAI,CAAC6C,EAAE,EAAE;QAC3B,OAAOL,QAAQ;MACnB,CAAC,MACI;QACD,OAAON,IAAI,CAACc,UAAU,CAAC,CAAC,CAACC,cAAc,CAACb,QAAQ,CAAC;MACrD;IACJ,CAAC,MACI;MACD,OAAOF,IAAI,CAACc,UAAU,CAAC,CAAC,CAACC,cAAc,CAACb,QAAQ,CAAC;IACrD;EACJ,CAAC,MACI;IACD,MAAM,IAAI7B,YAAY,CAAC,iBAAiB,EAAE,+DAA+D,CAAC;EAC9G;AACJ;AACA,OAAO,SAAS2C,UAAUA,CAAChB,IAAI,EAAEE,QAAQ,EAAE;EACvC,MAAMC,aAAa,GAAGH,IAAI,CAACI,gBAAgB,CAAC,CAAC;EAC7C,MAAM;IAAEC,IAAI,EAAEC,QAAQ;IAAEC,KAAK,EAAEC;EAAU,CAAC,GAAGR,IAAI,CAACS,cAAc,CAAC,CAAC;EAClE,IAAIV,kBAAkB,CAACC,IAAI,CAAC,EAAE;IAC1B,IAAIA,IAAI,CAACU,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIP,aAAa,KAAKrC,IAAI,CAAC6C,EAAE,EAAE;QAC3B,OAAOH,SAAS;MACpB,CAAC,MACI;QACD,OAAOF,QAAQ;MACnB;IACJ,CAAC,MACI;MACD,OAAOlB,IAAI,CAAC6B,GAAG,CAAC,GAAGjB,IAAI,CAACa,KAAK,CAAC,CAAC,CAAC;IACpC;EACJ,CAAC,MACI,IAAI1C,SAAS,CAAC6B,IAAI,CAAC,EAAE;IACtB,IAAIA,IAAI,CAACU,OAAO,CAAC,CAAC,EAAE;MAChB,IAAIP,aAAa,KAAKrC,IAAI,CAAC6C,EAAE,EAAE;QAC3B,OAAOX,IAAI,CAACc,UAAU,CAAC,CAAC,CAACI,iBAAiB,CAAChB,QAAQ,CAAC;MACxD,CAAC,MACI;QACD,OAAOI,QAAQ;MACnB;IACJ,CAAC,MACI;MACD,OAAON,IAAI,CAACc,UAAU,CAAC,CAAC,CAACI,iBAAiB,CAAChB,QAAQ,CAAC;IACxD;EACJ,CAAC,MACI;IACD,MAAM,IAAI7B,YAAY,CAAC,iBAAiB,EAAE,+DAA+D,CAAC;EAC9G;AACJ;AACA,OAAO,SAAS8C,gBAAgBA,CAACnB,IAAI,EAAEd,QAAQ,EAAE;EAC7C,MAAMkC,WAAW,GAAIlC,QAAQ,KAAKR,KAAK,IAAIsB,IAAI,CAACI,gBAAgB,CAAC,CAAC,KAAKtC,IAAI,CAAC6C,EAAE,IACzEzB,QAAQ,KAAKP,KAAK,IAAIqB,IAAI,CAACI,gBAAgB,CAAC,CAAC,KAAKtC,IAAI,CAACuD,IAAK;EACjE,IAAItB,kBAAkB,CAACC,IAAI,CAAC,EAAE;IAC1B,IAAIA,IAAI,CAACU,OAAO,CAAC,CAAC,IAAIU,WAAW,EAAE;MAC/B,OAAO,GAAG;IACd,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ,CAAC,MACI;IACD,IAAIpB,IAAI,CAACU,OAAO,CAAC,CAAC,IAAIU,WAAW,EAAE;MAC/B,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;AACJ;AACA,OAAO,MAAM5C,YAAY,SAASX,QAAQ,CAAC;EACvC,WAAWyD,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,OAAOC,MAAMA,CAACC,aAAa,EAAEC,KAAK,EAAE;IAChC,IAAI,CAACD,aAAa,IAAIA,aAAa,CAACE,MAAM,KAAK,CAAC,EAC5C,OAAO,KAAK;IAChB,MAAMC,MAAM,GAAG,GAAG;IAClB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,YAAY,GAAGA,CAACC,YAAY,EAAE7C,IAAI,EAAEC,QAAQ,KAAKL,kBAAkB,CAACM,mBAAmB,CAACF,IAAI,EAAEC,QAAQ,CAAC,EAAE4C,YAAY,CAACC,MAAM,GAAG,EAAE,GAAGJ,MAAM,CAAC;IACjJH,aAAa,CAACQ,OAAO,CAAEF,YAAY,IAAK;MACpC,MAAM9B,IAAI,GAAG8B,YAAY,CAACG,iBAAiB,CAAC,CAAC;MAC7CL,aAAa,GAAGxC,IAAI,CAAC6B,GAAG,CAACjB,IAAI,CAACkC,aAAa,CAAC,CAAC,EAAEN,aAAa,CAAC;MAC7D,IAAIO,KAAK,GAAG,CAAC;MACb,MAAMhC,aAAa,GAAGH,IAAI,CAACU,OAAO,CAAC,CAAC,GAAGV,IAAI,CAACI,gBAAgB,CAAC,CAAC,GAAGtC,IAAI,CAAC6C,EAAE;MACxE,IAAIyB,UAAU,GAAG,CAAC;MAClB,IAAIlE,eAAe,CAAC8B,IAAI,CAAC,EAAE;QACvB,MAAMqC,IAAI,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;QAC3B,IAAID,IAAI,EAAE;UACND,UAAU,GAAGhD,IAAI,CAACmD,GAAG,CAACF,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGzE,MAAM,CAAC0E,mBAAmB;QACxE;MACJ;MACA,MAAMC,KAAK,GAAG1C,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACPP,KAAK,GAAGO,KAAK,CAACE,WAAW,CAAC,CAAC;MAC/B;MACA,IAAId,YAAY,CAACe,WAAW,CAAC,CAAC,KAAKnE,KAAK,EAAE;QACtC,IAAIoE,QAAQ,GAAG9C,IAAI,CAAC+C,aAAa,CAAC,IAAI,CAAC;QACvC,IAAI5C,aAAa,KAAKrC,IAAI,CAAC6C,EAAE,EAAE;UAC3BmC,QAAQ,IAAIV,UAAU;QAC1B;QACA,IAAIY,SAAS,GAAGnB,YAAY,CAACC,YAAY,EAAEL,KAAK,CAACwB,WAAW,EAAEvE,KAAK,CAAC;QACpE,MAAMwE,MAAM,GAAGJ,QAAQ,GAAGrB,KAAK,CAACwB,WAAW,GAAG,GAAG;QACjD,IAAI,CAACnB,YAAY,CAACA,YAAY,CAACqB,YAAY,IAAID,MAAM,GAAGf,KAAK,EAAE;UAC3Da,SAAS,IAAIb,KAAK,GAAGe,MAAM;QAC/B;QACApB,YAAY,CAACsB,WAAW,CAAC3B,KAAK,CAACwB,WAAW,CAAC;QAC3CxB,KAAK,CAACwB,WAAW,IAAID,SAAS;QAC9BlB,YAAY,CAACuB,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;MAClC,CAAC,MACI,IAAIvB,YAAY,CAACe,WAAW,CAAC,CAAC,KAAKlE,KAAK,EAAE;QAC3C,IAAImE,QAAQ,GAAG1D,IAAI,CAAC6B,GAAG,CAACkB,KAAK,GAAGnC,IAAI,CAAC+C,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACxD,IAAI5C,aAAa,KAAKrC,IAAI,CAACuD,IAAI,EAAE;UAC7ByB,QAAQ,IAAIV,UAAU;QAC1B;QACA,IAAIY,SAAS,GAAGnB,YAAY,CAACC,YAAY,EAAEL,KAAK,CAACvB,QAAQ,EAAEvB,KAAK,CAAC;QACjE,MAAM2E,SAAS,GAAGR,QAAQ,GAAGrB,KAAK,CAACvB,QAAQ,GAAG,GAAG;QACjD,IAAI,CAAC4B,YAAY,CAACA,YAAY,CAACqB,YAAY,IAAIG,SAAS,GAAGnB,KAAK,EAAE;UAC9Da,SAAS,IAAIb,KAAK,GAAGmB,SAAS;QAClC;QACAxB,YAAY,CAACsB,WAAW,CAAC3B,KAAK,CAACvB,QAAQ,CAAC;QACxCuB,KAAK,CAACvB,QAAQ,IAAI8C,SAAS;QAC3BlB,YAAY,CAACuB,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;MAClC;IACJ,CAAC,CAAC;IACF,MAAME,KAAK,GAAG/B,aAAa,CACtBgC,GAAG,CAAE1B,YAAY,IAAKA,YAAY,CAAC2B,QAAQ,CAAC,CAAC,CAAC,CAC9CC,MAAM,CAAC,CAACC,QAAQ,EAAEC,UAAU,KAAKxE,IAAI,CAAC6B,GAAG,CAAC2C,UAAU,EAAED,QAAQ,CAAC,CAAC;IACrE,MAAME,OAAO,GAAGzE,IAAI,CAACwB,GAAG,CAACxB,IAAI,CAAC6B,GAAG,CAACsC,KAAK,GAAG3B,aAAa,EAAE,CAAC,CAAC,EAAExC,IAAI,CAAC6B,GAAG,CAACsC,KAAK,IAAI9B,KAAK,CAACqC,SAAS,GAAGrC,KAAK,CAACsC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IACvHtC,KAAK,CAACqC,SAAS,IAAID,OAAO,GAAG,CAAC;IAC9BpC,KAAK,CAACsC,UAAU,IAAIF,OAAO,GAAG,CAAC;IAC/B,OAAO,IAAI;EACf;EACA,OAAOG,aAAaA,CAAC;IAAExC;EAAc,CAAC,EAAExB,IAAI,EAAEiE,OAAO,EAAE;IACnD,IAAI,CAACzC,aAAa,EACd;IACJ,MAAM0C,eAAe,GAAG;MACpBC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;IACZ,CAAC;IACD7C,aAAa,CACR8C,KAAK,CAAC,GAAG,CAAC,CACVd,GAAG,CAAEe,WAAW,IAAKA,WAAW,CAACC,IAAI,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CACnDd,GAAG,CAAC,CAAC,CAACiB,IAAI,EAAEvF,QAAQ,CAAC,KAAK;MAC3B,MAAMwF,KAAK,GAAG;QAAEC,IAAI,EAAET,eAAe,CAACO,IAAI;MAAE,CAAC;MAC7C,IAAIvF,QAAQ,EACRwF,KAAK,CAACxF,QAAQ,GAAGrB,QAAQ,CAAC+G,cAAc,CAAC1F,QAAQ,CAAC;MACtD,OAAO+E,OAAO,CAACY,UAAU,CAAC,CAAC,CAACrG,YAAY,CAACkG,KAAK,CAAC;IACnD,CAAC,CAAC,CACGlB,GAAG,CAAEkB,KAAK,IAAK1E,IAAI,CAAC8E,WAAW,CAACJ,KAAK,EAAE,CAAC,CAAC,CAAC;EACnD;EACAK,WAAWA,CAACJ,IAAI,EAAE;IACd,IAAIK,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACN,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACzF,QAAQ,GAAGR,KAAK;IACrB,IAAI,CAACX,MAAM,CAACmH,iBAAiB,CAAC,IAAI,CAACP,IAAI,CAAC,EAAE;MACtC,IAAI,CAAC,CAACK,EAAE,GAAG,IAAI,CAACL,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAC9E,IAAI,CAAC9F,QAAQ,GAAGR,KAAK,CAAC,KAEtB,IAAI,CAACQ,QAAQ,GAAGP,KAAK;IAC7B;IACA,IAAI,CAACmD,YAAY,GAAG;MAAEqB,YAAY,EAAE;IAAM,CAAC;IAC3C,IAAI,CAACiC,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACtD,YAAY,GAAG/D,MAAM,CAACmH,iBAAiB,CAAC,IAAI,CAACP,IAAI,CAAC;IACvD,IAAI,CAAC,IAAI,CAAC7C,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG;QAAEuD,IAAI,EAAE,IAAI,CAACV,IAAI;QAAExB,YAAY,EAAE;MAAM,CAAC;IAChE;IACA,MAAMkC,IAAI,GAAG,CAAC,IAAI,CAACnG,QAAQ,KAAKR,KAAK,GAAG,IAAI,CAACoD,YAAY,CAACwD,SAAS,GAAG,IAAI,CAACxD,YAAY,CAACyD,SAAS,KAC7F,IAAI,CAACzD,YAAY,CAACuD,IAAI,IACtBzH,MAAM,CAAC4H,IAAI;IACf,IAAI,CAACC,IAAI,GAAGJ,IAAI;EACpB;EACAK,eAAeA,CAACvC,YAAY,GAAG,IAAI,EAAE;IACjC,IAAI,CAACrB,YAAY,CAACqB,YAAY,GAAGA,YAAY;IAC7C,OAAO,IAAI;EACf;EACAwC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAM7F,IAAI,GAAG,IAAI,CAACiC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAAC6D,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAM;MAAE9G,QAAQ;MAAEgB;IAAS,CAAC,GAAG,IAAI;IACnC,MAAMT,kBAAkB,GAAG,IAAI,CAACqC,YAAY,CAACqB,YAAY;IACzD,MAAMT,KAAK,GAAG1C,IAAI,CAACc,UAAU,CAAC,CAAC;IAC/B,MAAMmF,UAAU,GAAGvD,KAAK,CAACwD,sBAAsB,CAAC,CAAC;IACjD,MAAMC,KAAK,GAAGhI,SAAS,CAAC6B,IAAI,CAAC;IAC7B,MAAM;MAAEoG;IAAE,CAAC,GAAGpG,IAAI,CAACqG,kBAAkB,CAACnH,QAAQ,EAAE6G,KAAK,CAAC;IACtD,MAAMO,qBAAqB,GAAG,CAAC7G,kBAAkB,IAAI0G,KAAK;IAC1D,MAAMI,aAAa,GAAGpF,gBAAgB,CAACnB,IAAI,EAAEd,QAAQ,CAAC;IACtD,IAAIsH,CAAC,GAAG;MACJ,CAAC9H,KAAK,GAAG,MAAM;QACX,MAAM8H,CAAC,GAAGvG,OAAO,CAACD,IAAI,EAAEE,QAAQ,CAAC,GAAG,CAACA,QAAQ,GAAGqG,aAAa,IAAIN,UAAU;QAC3E,OAAOK,qBAAqB,GAAGlH,IAAI,CAACwB,GAAG,CAAC8B,KAAK,CAAC3B,cAAc,CAACvC,YAAY,CAACiI,cAAc,CAAC,EAAED,CAAC,CAAC,GAAGA,CAAC;MACrG,CAAC;MACD,CAAC7H,KAAK,GAAG,MAAM;QACX,MAAM6H,CAAC,GAAGxF,UAAU,CAAChB,IAAI,EAAEE,QAAQ,CAAC,GAAG,CAACA,QAAQ,GAAGqG,aAAa,IAAIN,UAAU;QAC9E,OAAOK,qBAAqB,GAAGlH,IAAI,CAAC6B,GAAG,CAACyB,KAAK,CAACxB,iBAAiB,CAAC1C,YAAY,CAACiI,cAAc,CAAC,EAAED,CAAC,CAAC,GAAGA,CAAC;MACxG;IACJ,CAAC,CAACtH,QAAQ,CAAC,CAAC,CAAC;IACb,IAAI,CAACiH,KAAK,EAAE;MACR,MAAMzG,eAAe,GAAGR,QAAQ,KAAKR,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MACpD,MAAMoE,QAAQ,GAAG9C,IAAI,CAAC0G,WAAW,CAAC,CAAC,CAACX,KAAK,CAAC,CAAC9G,IAAI;MAC/C,MAAM0H,gBAAgB,GAAG,CAAC3G,IAAI,CAACa,KAAK,CAAC,CAAC,CAACkF,KAAK,CAAC,GAAGS,CAAC,IAAIP,UAAU;MAC/D,MAAMW,SAAS,GAAGD,gBAAgB,GAAGE,MAAM,CAAC/D,QAAQ,CAAC;MACrD,MAAMnD,WAAW,GAAGH,eAAe,CAACC,kBAAkB,EAAEmH,SAAS,EAAE1H,QAAQ,EAAEQ,eAAe,CAAC;MAC7F,IAAIV,aAAa,CAACW,WAAW,EAAET,QAAQ,CAAC,EACpC,IAAI,CAACmE,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;MAC5BmD,CAAC,IAAIpH,IAAI,CAACmD,GAAG,CAAC5C,WAAW,GAAGiH,SAAS,CAAC,GAAGX,UAAU,GAAGvG,eAAe;IACzE;IACApB,CAAC,CAAC,iCAAiC8H,CAAC,QAAQI,CAAC,GAAG,CAAC;IACjD,IAAI,CAACJ,CAAC,GAAGA,CAAC;IACV,IAAI,CAACI,CAAC,GAAGA,CAAC;IACV,IAAI,CAACM,UAAU,CAAClB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B;AACJ;AACApH,YAAY,CAACC,KAAK,GAAG,KAAK;AAC1BD,YAAY,CAACiI,cAAc,GAAG,CAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}