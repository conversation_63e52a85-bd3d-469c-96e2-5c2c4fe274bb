{"ast": null, "code": "import { Accidental } from './accidental.js';\nimport { Annotation, AnnotationHorizontalJustify, AnnotationVerticalJustify } from './annotation.js';\nimport { Articulation } from './articulation.js';\nimport { BarNote } from './barnote.js';\nimport { Beam } from './beam.js';\nimport { ChordSymbol } from './chordsymbol.js';\nimport { ClefNote } from './clefnote.js';\nimport { Curve } from './curve.js';\nimport { EasyScore } from './easyscore.js';\nimport { Formatter } from './formatter.js';\nimport { FretHandFinger } from './frethandfinger.js';\nimport { GhostNote } from './ghostnote.js';\nimport { GlyphNote } from './glyphnote.js';\nimport { GraceNote } from './gracenote.js';\nimport { GraceNoteGroup } from './gracenotegroup.js';\nimport { KeySigNote } from './keysignote.js';\nimport { ModifierContext } from './modifiercontext.js';\nimport { MultiMeasureRest } from './multimeasurerest.js';\nimport { NoteSubGroup } from './notesubgroup.js';\nimport { Ornament } from './ornament.js';\nimport { PedalMarking } from './pedalmarking.js';\nimport { Renderer } from './renderer.js';\nimport { RepeatNote } from './repeatnote.js';\nimport { Stave } from './stave.js';\nimport { StaveConnector } from './staveconnector.js';\nimport { StaveLine } from './staveline.js';\nimport { StaveNote } from './stavenote.js';\nimport { StaveTie } from './stavetie.js';\nimport { StringNumber } from './stringnumber.js';\nimport { System } from './system.js';\nimport { TabNote } from './tabnote.js';\nimport { TabStave } from './tabstave.js';\nimport { TextBracket } from './textbracket.js';\nimport { TextDynamics } from './textdynamics.js';\nimport { TextNote } from './textnote.js';\nimport { TickContext } from './tickcontext.js';\nimport { TimeSigNote } from './timesignote.js';\nimport { Tuplet } from './tuplet.js';\nimport { defined, log, RuntimeError } from './util.js';\nimport { VibratoBracket } from './vibratobracket.js';\nimport { Voice } from './voice.js';\nimport { isHTMLCanvas } from './web.js';\nfunction L(...args) {\n  if (Factory.DEBUG) log('VexFlow.Factory', args);\n}\nexport class Factory {\n  static newFromElementId(elementId, width = 500, height = 200) {\n    return new Factory({\n      renderer: {\n        elementId,\n        width,\n        height\n      }\n    });\n  }\n  constructor(options = {}) {\n    L('New factory: ', options);\n    this.options = {\n      stave: {\n        space: 10\n      },\n      renderer: {\n        elementId: '',\n        width: 500,\n        height: 200,\n        background: '#FFF'\n      }\n    };\n    this.setOptions(options);\n  }\n  reset() {\n    this.renderQ = [];\n    this.systems = [];\n    this.staves = [];\n    this.voices = [];\n    this.stave = undefined;\n  }\n  setOptions(options) {\n    this.options = Object.assign(Object.assign({}, this.options), options);\n    this.initRenderer();\n    this.reset();\n  }\n  initRenderer() {\n    const {\n      elementId,\n      width,\n      height,\n      background\n    } = this.options.renderer;\n    if (elementId === null) {\n      return;\n    }\n    if (elementId === '') {\n      L(this);\n      throw new RuntimeError('renderer.elementId not set in FactoryOptions');\n    }\n    let backend = this.options.renderer.backend;\n    if (backend === undefined) {\n      const elem = document.getElementById(elementId);\n      if (isHTMLCanvas(elem)) {\n        backend = Renderer.Backends.CANVAS;\n      } else {\n        backend = Renderer.Backends.SVG;\n      }\n    }\n    this.context = Renderer.buildContext(elementId, backend, width, height, background);\n  }\n  getContext() {\n    return this.context;\n  }\n  setContext(context) {\n    this.context = context;\n    return this;\n  }\n  getStave() {\n    return this.stave;\n  }\n  getVoices() {\n    return this.voices;\n  }\n  Stave(params) {\n    const staveSpace = this.options.stave.space;\n    const p = Object.assign({\n      x: 0,\n      y: 0,\n      width: this.options.renderer.width - staveSpace * 1.0,\n      options: {\n        spacingBetweenLinesPx: staveSpace * 1.0\n      }\n    }, params);\n    const stave = new Stave(p.x, p.y, p.width, p.options);\n    this.staves.push(stave);\n    stave.setContext(this.context);\n    this.stave = stave;\n    return stave;\n  }\n  TabStave(params) {\n    const staveSpace = this.options.stave.space;\n    const p = Object.assign({\n      x: 0,\n      y: 0,\n      width: this.options.renderer.width - staveSpace * 1.0,\n      options: {\n        spacingBetweenLinesPx: staveSpace * 1.3\n      }\n    }, params);\n    const stave = new TabStave(p.x, p.y, p.width, p.options);\n    this.staves.push(stave);\n    stave.setContext(this.context);\n    this.stave = stave;\n    return stave;\n  }\n  StaveNote(noteStruct) {\n    const note = new StaveNote(noteStruct);\n    if (this.stave) note.setStave(this.stave);\n    note.setContext(this.context);\n    this.renderQ.push(note);\n    return note;\n  }\n  GlyphNote(glyph, noteStruct, options) {\n    const note = new GlyphNote(glyph, noteStruct, options);\n    if (this.stave) note.setStave(this.stave);\n    note.setContext(this.context);\n    this.renderQ.push(note);\n    return note;\n  }\n  RepeatNote(type, noteStruct, options) {\n    const note = new RepeatNote(type, noteStruct, options);\n    if (this.stave) note.setStave(this.stave);\n    note.setContext(this.context);\n    this.renderQ.push(note);\n    return note;\n  }\n  GhostNote(noteStruct) {\n    const ghostNote = new GhostNote(noteStruct);\n    if (this.stave) ghostNote.setStave(this.stave);\n    ghostNote.setContext(this.context);\n    this.renderQ.push(ghostNote);\n    return ghostNote;\n  }\n  TextNote(noteStruct) {\n    const textNote = new TextNote(noteStruct);\n    if (this.stave) textNote.setStave(this.stave);\n    textNote.setContext(this.context);\n    this.renderQ.push(textNote);\n    return textNote;\n  }\n  BarNote(params = {}) {\n    const barNote = new BarNote(params.type);\n    if (this.stave) barNote.setStave(this.stave);\n    barNote.setContext(this.context);\n    this.renderQ.push(barNote);\n    return barNote;\n  }\n  ClefNote(params) {\n    const p = Object.assign({\n      type: 'treble',\n      options: {\n        size: 'default',\n        annotation: undefined\n      }\n    }, params);\n    const clefNote = new ClefNote(p.type, p.options.size, p.options.annotation);\n    if (this.stave) clefNote.setStave(this.stave);\n    clefNote.setContext(this.context);\n    this.renderQ.push(clefNote);\n    return clefNote;\n  }\n  TimeSigNote(params) {\n    const p = Object.assign({\n      time: '4/4'\n    }, params);\n    const timeSigNote = new TimeSigNote(p.time);\n    if (this.stave) timeSigNote.setStave(this.stave);\n    timeSigNote.setContext(this.context);\n    this.renderQ.push(timeSigNote);\n    return timeSigNote;\n  }\n  KeySigNote(params) {\n    const keySigNote = new KeySigNote(params.key, params.cancelKey, params.alterKey);\n    if (this.stave) keySigNote.setStave(this.stave);\n    keySigNote.setContext(this.context);\n    this.renderQ.push(keySigNote);\n    return keySigNote;\n  }\n  TabNote(noteStruct) {\n    const note = new TabNote(noteStruct);\n    if (this.stave) note.setStave(this.stave);\n    note.setContext(this.context);\n    this.renderQ.push(note);\n    return note;\n  }\n  GraceNote(noteStruct) {\n    const note = new GraceNote(noteStruct);\n    if (this.stave) note.setStave(this.stave);\n    note.setContext(this.context);\n    return note;\n  }\n  GraceNoteGroup(params) {\n    const group = new GraceNoteGroup(params.notes, params.slur);\n    group.setContext(this.context);\n    return group;\n  }\n  Accidental(params) {\n    const accid = new Accidental(params.type);\n    accid.setContext(this.context);\n    return accid;\n  }\n  Annotation(params) {\n    const p = Object.assign({\n      text: 'p',\n      hJustify: AnnotationHorizontalJustify.CENTER,\n      vJustify: AnnotationVerticalJustify.BOTTOM\n    }, params);\n    const annotation = new Annotation(p.text);\n    annotation.setJustification(p.hJustify);\n    annotation.setVerticalJustification(p.vJustify);\n    annotation.setFont(p.font);\n    annotation.setContext(this.context);\n    return annotation;\n  }\n  ChordSymbol(params) {\n    const p = Object.assign({\n      vJustify: 'top',\n      hJustify: 'center'\n    }, params);\n    const chordSymbol = new ChordSymbol();\n    chordSymbol.setHorizontal(p.hJustify);\n    chordSymbol.setVertical(p.vJustify);\n    if (typeof p.fontFamily === 'string' && typeof p.fontSize === 'number') {\n      if (typeof p.fontWeight === 'string') chordSymbol.setFont(p.fontFamily, p.fontSize, p.fontWeight);else chordSymbol.setFont(p.fontFamily, p.fontSize, '');\n    } else if (typeof p.fontSize === 'number') {\n      chordSymbol.setFontSize(p.fontSize);\n    }\n    chordSymbol.setContext(this.context);\n    return chordSymbol;\n  }\n  Articulation(params) {\n    var _a;\n    const articulation = new Articulation((_a = params === null || params === void 0 ? void 0 : params.type) !== null && _a !== void 0 ? _a : 'a.');\n    if ((params === null || params === void 0 ? void 0 : params.position) !== undefined) articulation.setPosition(params.position);\n    if ((params === null || params === void 0 ? void 0 : params.betweenLines) !== undefined) articulation.setBetweenLines(params.betweenLines);\n    articulation.setContext(this.context);\n    return articulation;\n  }\n  Ornament(type, params) {\n    const options = Object.assign({\n      type,\n      accidental: ''\n    }, params);\n    const ornament = new Ornament(type);\n    if ((params === null || params === void 0 ? void 0 : params.position) !== undefined) {\n      ornament.setPosition(params.position);\n    }\n    if (options.upperAccidental) {\n      ornament.setUpperAccidental(options.upperAccidental);\n    }\n    if (options.lowerAccidental) {\n      ornament.setLowerAccidental(options.lowerAccidental);\n    }\n    if (typeof options.delayed !== 'undefined') {\n      ornament.setDelayed(options.delayed);\n    }\n    ornament.setContext(this.context);\n    return ornament;\n  }\n  TextDynamics(params) {\n    const p = Object.assign({\n      text: 'p',\n      duration: 'q',\n      dots: 0,\n      line: 0\n    }, params);\n    const text = new TextDynamics({\n      text: p.text,\n      line: p.line,\n      duration: p.duration,\n      dots: p.dots\n    });\n    if (this.stave) text.setStave(this.stave);\n    text.setContext(this.context);\n    this.renderQ.push(text);\n    return text;\n  }\n  Fingering(params) {\n    const p = Object.assign({\n      number: '0',\n      position: 'left'\n    }, params);\n    const fingering = new FretHandFinger(p.number);\n    fingering.setPosition(p.position);\n    fingering.setContext(this.context);\n    return fingering;\n  }\n  StringNumber(params, drawCircle = true) {\n    const stringNumber = new StringNumber(params.number);\n    stringNumber.setPosition(params.position);\n    stringNumber.setContext(this.context);\n    stringNumber.setDrawCircle(drawCircle);\n    return stringNumber;\n  }\n  TickContext() {\n    return new TickContext();\n  }\n  ModifierContext() {\n    return new ModifierContext();\n  }\n  MultiMeasureRest(params) {\n    const numMeasures = defined(params.numberOfMeasures, 'NoNumberOfMeasures');\n    const multiMeasureRest = new MultiMeasureRest(numMeasures, params);\n    multiMeasureRest.setContext(this.context);\n    this.renderQ.push(multiMeasureRest);\n    return multiMeasureRest;\n  }\n  Voice(params) {\n    const p = Object.assign({\n      time: '4/4'\n    }, params);\n    const voice = new Voice(p.time);\n    this.voices.push(voice);\n    return voice;\n  }\n  StaveConnector(params) {\n    const connector = new StaveConnector(params.topStave, params.bottomStave);\n    connector.setType(params.type).setContext(this.context);\n    this.renderQ.push(connector);\n    return connector;\n  }\n  Formatter(options) {\n    return new Formatter(options);\n  }\n  Tuplet(params) {\n    const p = Object.assign({\n      notes: [],\n      options: {}\n    }, params);\n    const tuplet = new Tuplet(p.notes, p.options).setContext(this.context);\n    this.renderQ.push(tuplet);\n    return tuplet;\n  }\n  Beam(params) {\n    var _a, _b, _c, _d, _e;\n    const beam = new Beam(params.notes, (_a = params.options) === null || _a === void 0 ? void 0 : _a.autoStem).setContext(this.context);\n    beam.breakSecondaryAt((_c = (_b = params.options) === null || _b === void 0 ? void 0 : _b.secondaryBeamBreaks) !== null && _c !== void 0 ? _c : []);\n    if ((_d = params.options) === null || _d === void 0 ? void 0 : _d.partialBeamDirections) {\n      Object.entries((_e = params.options) === null || _e === void 0 ? void 0 : _e.partialBeamDirections).forEach(([noteIndex, direction]) => {\n        beam.setPartialBeamSideAt(Number(noteIndex), direction);\n      });\n    }\n    this.renderQ.push(beam);\n    return beam;\n  }\n  Curve(params) {\n    const curve = new Curve(params.from, params.to, params.options).setContext(this.context);\n    this.renderQ.push(curve);\n    return curve;\n  }\n  StaveTie(params) {\n    var _a;\n    const tie = new StaveTie({\n      firstNote: params.from,\n      lastNote: params.to,\n      firstIndexes: params.firstIndexes,\n      lastIndexes: params.lastIndexes\n    }, params.text);\n    if ((_a = params.options) === null || _a === void 0 ? void 0 : _a.direction) tie.setDirection(params.options.direction);\n    tie.setContext(this.context);\n    this.renderQ.push(tie);\n    return tie;\n  }\n  StaveLine(params) {\n    var _a, _b;\n    const line = new StaveLine({\n      firstNote: params.from,\n      lastNote: params.to,\n      firstIndexes: params.firstIndexes,\n      lastIndexes: params.lastIndexes\n    });\n    if ((_a = params.options) === null || _a === void 0 ? void 0 : _a.text) line.setText(params.options.text);\n    if ((_b = params.options) === null || _b === void 0 ? void 0 : _b.font) line.setFont(params.options.font);\n    line.setContext(this.context);\n    this.renderQ.push(line);\n    return line;\n  }\n  VibratoBracket(params) {\n    const vibratoBracket = new VibratoBracket({\n      start: params.from,\n      stop: params.to\n    });\n    if (params.options.line) vibratoBracket.setLine(params.options.line);\n    if (params.options.code) vibratoBracket.setVibratoCode(params.options.code);\n    vibratoBracket.setContext(this.context);\n    this.renderQ.push(vibratoBracket);\n    return vibratoBracket;\n  }\n  TextBracket(params) {\n    const textBracket = new TextBracket({\n      start: params.from,\n      stop: params.to,\n      text: params.text,\n      superscript: params.options.superscript,\n      position: params.options.position\n    });\n    if (params.options.line) textBracket.setLine(params.options.line);\n    if (params.options.font) textBracket.setFont(params.options.font);\n    textBracket.setContext(this.context);\n    this.renderQ.push(textBracket);\n    return textBracket;\n  }\n  System(params = {}) {\n    params.factory = this;\n    const system = new System(params).setContext(this.context);\n    this.systems.push(system);\n    return system;\n  }\n  EasyScore(options = {}) {\n    options.factory = this;\n    return new EasyScore(options);\n  }\n  PedalMarking(params) {\n    const p = Object.assign({\n      notes: [],\n      options: {\n        style: 'mixed'\n      }\n    }, params);\n    const pedal = new PedalMarking(p.notes);\n    pedal.setType(PedalMarking.typeString[p.options.style]);\n    pedal.setContext(this.context);\n    this.renderQ.push(pedal);\n    return pedal;\n  }\n  NoteSubGroup(params) {\n    const p = Object.assign({\n      notes: []\n    }, params);\n    const group = new NoteSubGroup(p.notes);\n    group.setContext(this.context);\n    return group;\n  }\n  draw() {\n    const ctx = this.context;\n    this.systems.forEach(s => s.setContext(ctx).format());\n    this.staves.forEach(s => s.setContext(ctx).drawWithStyle());\n    this.voices.forEach(v => v.setContext(ctx).drawWithStyle());\n    this.renderQ.forEach(e => {\n      if (!e.isRendered()) e.setContext(ctx).drawWithStyle();\n    });\n    this.systems.forEach(s => s.setContext(ctx).drawWithStyle());\n    this.reset();\n  }\n}\nFactory.DEBUG = false;", "map": {"version": 3, "names": ["Accidental", "Annotation", "AnnotationHorizontalJustify", "AnnotationVerticalJustify", "Articulation", "Bar<PERSON><PERSON>", "<PERSON><PERSON>", "ChordSymbol", "ClefNote", "Curve", "EasyScore", "<PERSON><PERSON><PERSON>", "FretHandFinger", "GhostNote", "GlyphNote", "<PERSON><PERSON><PERSON>", "GraceNoteGroup", "KeySigNote", "ModifierContext", "MultiMeasureRest", "NoteSubGroup", "Ornament", "PedalMarking", "<PERSON><PERSON><PERSON>", "RepeatNote", "Stave", "StaveConnector", "StaveLine", "StaveNote", "StaveTie", "StringNumber", "System", "TabNote", "TabStave", "TextBracket", "TextDynamics", "TextNote", "TickContext", "TimeSigNote", "Tuplet", "defined", "log", "RuntimeError", "VibratoBracket", "Voice", "isHTMLCanvas", "L", "args", "Factory", "DEBUG", "newFromElementId", "elementId", "width", "height", "renderer", "constructor", "options", "stave", "space", "background", "setOptions", "reset", "renderQ", "systems", "staves", "voices", "undefined", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backend", "elem", "document", "getElementById", "Backends", "CANVAS", "SVG", "context", "buildContext", "getContext", "setContext", "getStave", "getVoices", "params", "staveSpace", "p", "x", "y", "spacingBetweenLinesPx", "push", "noteStruct", "note", "setStave", "glyph", "type", "ghostNote", "textNote", "bar<PERSON><PERSON>", "size", "annotation", "clefNote", "time", "timeSigNote", "keySigNote", "key", "cancel<PERSON>ey", "<PERSON><PERSON><PERSON>", "group", "notes", "slur", "accid", "text", "hJustify", "CENTER", "vJustify", "BOTTOM", "setJustification", "setVerticalJustification", "setFont", "font", "chordSymbol", "setHorizontal", "setVertical", "fontFamily", "fontSize", "fontWeight", "setFontSize", "_a", "articulation", "position", "setPosition", "betweenLines", "setBetweenLines", "accidental", "ornament", "upperAccidental", "setUpperAccidental", "lowerAccidental", "setLowerAccidental", "delayed", "<PERSON><PERSON><PERSON><PERSON>", "duration", "dots", "line", "Fingering", "number", "fingering", "drawCircle", "stringNumber", "setDrawCircle", "numMeasures", "numberOfMeasures", "multiMeasureRest", "voice", "connector", "topStave", "bottomStave", "setType", "tuplet", "_b", "_c", "_d", "_e", "beam", "autoStem", "breakSecondaryAt", "secondaryBeamBreaks", "partialBeamDirections", "entries", "for<PERSON>ach", "noteIndex", "direction", "setPartialBeamSideAt", "Number", "curve", "from", "to", "tie", "firstNote", "lastNote", "firstIndexes", "lastIndexes", "setDirection", "setText", "vibratoBracket", "start", "stop", "setLine", "code", "setVibratoCode", "textBracket", "superscript", "factory", "system", "style", "pedal", "typeString", "draw", "ctx", "s", "format", "drawWithStyle", "v", "e", "isRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/factory.js"], "sourcesContent": ["import { Accidental } from './accidental.js';\nimport { Annotation, AnnotationHorizontalJustify, AnnotationVerticalJustify } from './annotation.js';\nimport { Articulation } from './articulation.js';\nimport { BarNote } from './barnote.js';\nimport { Beam } from './beam.js';\nimport { ChordSymbol } from './chordsymbol.js';\nimport { ClefNote } from './clefnote.js';\nimport { Curve } from './curve.js';\nimport { EasyScore } from './easyscore.js';\nimport { Formatter } from './formatter.js';\nimport { FretHandFinger } from './frethandfinger.js';\nimport { GhostNote } from './ghostnote.js';\nimport { GlyphNote } from './glyphnote.js';\nimport { GraceNote } from './gracenote.js';\nimport { GraceNoteGroup } from './gracenotegroup.js';\nimport { KeySigNote } from './keysignote.js';\nimport { ModifierContext } from './modifiercontext.js';\nimport { MultiMeasureRest } from './multimeasurerest.js';\nimport { NoteSubGroup } from './notesubgroup.js';\nimport { Ornament } from './ornament.js';\nimport { PedalMarking } from './pedalmarking.js';\nimport { Renderer } from './renderer.js';\nimport { RepeatNote } from './repeatnote.js';\nimport { Stave } from './stave.js';\nimport { StaveConnector } from './staveconnector.js';\nimport { StaveLine } from './staveline.js';\nimport { StaveNote } from './stavenote.js';\nimport { StaveTie } from './stavetie.js';\nimport { StringNumber } from './stringnumber.js';\nimport { System } from './system.js';\nimport { TabNote } from './tabnote.js';\nimport { TabStave } from './tabstave.js';\nimport { TextBracket } from './textbracket.js';\nimport { TextDynamics } from './textdynamics.js';\nimport { TextNote } from './textnote.js';\nimport { TickContext } from './tickcontext.js';\nimport { TimeSigNote } from './timesignote.js';\nimport { Tuplet } from './tuplet.js';\nimport { defined, log, RuntimeError } from './util.js';\nimport { VibratoBracket } from './vibratobracket.js';\nimport { Voice } from './voice.js';\nimport { isHTMLCanvas } from './web.js';\nfunction L(...args) {\n    if (Factory.DEBUG)\n        log('VexFlow.Factory', args);\n}\nexport class Factory {\n    static newFromElementId(elementId, width = 500, height = 200) {\n        return new Factory({ renderer: { elementId, width, height } });\n    }\n    constructor(options = {}) {\n        L('New factory: ', options);\n        this.options = {\n            stave: {\n                space: 10,\n            },\n            renderer: {\n                elementId: '',\n                width: 500,\n                height: 200,\n                background: '#FFF',\n            },\n        };\n        this.setOptions(options);\n    }\n    reset() {\n        this.renderQ = [];\n        this.systems = [];\n        this.staves = [];\n        this.voices = [];\n        this.stave = undefined;\n    }\n    setOptions(options) {\n        this.options = Object.assign(Object.assign({}, this.options), options);\n        this.initRenderer();\n        this.reset();\n    }\n    initRenderer() {\n        const { elementId, width, height, background } = this.options.renderer;\n        if (elementId === null) {\n            return;\n        }\n        if (elementId === '') {\n            L(this);\n            throw new RuntimeError('renderer.elementId not set in FactoryOptions');\n        }\n        let backend = this.options.renderer.backend;\n        if (backend === undefined) {\n            const elem = document.getElementById(elementId);\n            if (isHTMLCanvas(elem)) {\n                backend = Renderer.Backends.CANVAS;\n            }\n            else {\n                backend = Renderer.Backends.SVG;\n            }\n        }\n        this.context = Renderer.buildContext(elementId, backend, width, height, background);\n    }\n    getContext() {\n        return this.context;\n    }\n    setContext(context) {\n        this.context = context;\n        return this;\n    }\n    getStave() {\n        return this.stave;\n    }\n    getVoices() {\n        return this.voices;\n    }\n    Stave(params) {\n        const staveSpace = this.options.stave.space;\n        const p = Object.assign({ x: 0, y: 0, width: this.options.renderer.width - staveSpace * 1.0, options: { spacingBetweenLinesPx: staveSpace * 1.0 } }, params);\n        const stave = new Stave(p.x, p.y, p.width, p.options);\n        this.staves.push(stave);\n        stave.setContext(this.context);\n        this.stave = stave;\n        return stave;\n    }\n    TabStave(params) {\n        const staveSpace = this.options.stave.space;\n        const p = Object.assign({ x: 0, y: 0, width: this.options.renderer.width - staveSpace * 1.0, options: { spacingBetweenLinesPx: staveSpace * 1.3 } }, params);\n        const stave = new TabStave(p.x, p.y, p.width, p.options);\n        this.staves.push(stave);\n        stave.setContext(this.context);\n        this.stave = stave;\n        return stave;\n    }\n    StaveNote(noteStruct) {\n        const note = new StaveNote(noteStruct);\n        if (this.stave)\n            note.setStave(this.stave);\n        note.setContext(this.context);\n        this.renderQ.push(note);\n        return note;\n    }\n    GlyphNote(glyph, noteStruct, options) {\n        const note = new GlyphNote(glyph, noteStruct, options);\n        if (this.stave)\n            note.setStave(this.stave);\n        note.setContext(this.context);\n        this.renderQ.push(note);\n        return note;\n    }\n    RepeatNote(type, noteStruct, options) {\n        const note = new RepeatNote(type, noteStruct, options);\n        if (this.stave)\n            note.setStave(this.stave);\n        note.setContext(this.context);\n        this.renderQ.push(note);\n        return note;\n    }\n    GhostNote(noteStruct) {\n        const ghostNote = new GhostNote(noteStruct);\n        if (this.stave)\n            ghostNote.setStave(this.stave);\n        ghostNote.setContext(this.context);\n        this.renderQ.push(ghostNote);\n        return ghostNote;\n    }\n    TextNote(noteStruct) {\n        const textNote = new TextNote(noteStruct);\n        if (this.stave)\n            textNote.setStave(this.stave);\n        textNote.setContext(this.context);\n        this.renderQ.push(textNote);\n        return textNote;\n    }\n    BarNote(params = {}) {\n        const barNote = new BarNote(params.type);\n        if (this.stave)\n            barNote.setStave(this.stave);\n        barNote.setContext(this.context);\n        this.renderQ.push(barNote);\n        return barNote;\n    }\n    ClefNote(params) {\n        const p = Object.assign({ type: 'treble', options: {\n                size: 'default',\n                annotation: undefined,\n            } }, params);\n        const clefNote = new ClefNote(p.type, p.options.size, p.options.annotation);\n        if (this.stave)\n            clefNote.setStave(this.stave);\n        clefNote.setContext(this.context);\n        this.renderQ.push(clefNote);\n        return clefNote;\n    }\n    TimeSigNote(params) {\n        const p = Object.assign({ time: '4/4' }, params);\n        const timeSigNote = new TimeSigNote(p.time);\n        if (this.stave)\n            timeSigNote.setStave(this.stave);\n        timeSigNote.setContext(this.context);\n        this.renderQ.push(timeSigNote);\n        return timeSigNote;\n    }\n    KeySigNote(params) {\n        const keySigNote = new KeySigNote(params.key, params.cancelKey, params.alterKey);\n        if (this.stave)\n            keySigNote.setStave(this.stave);\n        keySigNote.setContext(this.context);\n        this.renderQ.push(keySigNote);\n        return keySigNote;\n    }\n    TabNote(noteStruct) {\n        const note = new TabNote(noteStruct);\n        if (this.stave)\n            note.setStave(this.stave);\n        note.setContext(this.context);\n        this.renderQ.push(note);\n        return note;\n    }\n    GraceNote(noteStruct) {\n        const note = new GraceNote(noteStruct);\n        if (this.stave)\n            note.setStave(this.stave);\n        note.setContext(this.context);\n        return note;\n    }\n    GraceNoteGroup(params) {\n        const group = new GraceNoteGroup(params.notes, params.slur);\n        group.setContext(this.context);\n        return group;\n    }\n    Accidental(params) {\n        const accid = new Accidental(params.type);\n        accid.setContext(this.context);\n        return accid;\n    }\n    Annotation(params) {\n        const p = Object.assign({ text: 'p', hJustify: AnnotationHorizontalJustify.CENTER, vJustify: AnnotationVerticalJustify.BOTTOM }, params);\n        const annotation = new Annotation(p.text);\n        annotation.setJustification(p.hJustify);\n        annotation.setVerticalJustification(p.vJustify);\n        annotation.setFont(p.font);\n        annotation.setContext(this.context);\n        return annotation;\n    }\n    ChordSymbol(params) {\n        const p = Object.assign({ vJustify: 'top', hJustify: 'center' }, params);\n        const chordSymbol = new ChordSymbol();\n        chordSymbol.setHorizontal(p.hJustify);\n        chordSymbol.setVertical(p.vJustify);\n        if (typeof p.fontFamily === 'string' && typeof p.fontSize === 'number') {\n            if (typeof p.fontWeight === 'string')\n                chordSymbol.setFont(p.fontFamily, p.fontSize, p.fontWeight);\n            else\n                chordSymbol.setFont(p.fontFamily, p.fontSize, '');\n        }\n        else if (typeof p.fontSize === 'number') {\n            chordSymbol.setFontSize(p.fontSize);\n        }\n        chordSymbol.setContext(this.context);\n        return chordSymbol;\n    }\n    Articulation(params) {\n        var _a;\n        const articulation = new Articulation((_a = params === null || params === void 0 ? void 0 : params.type) !== null && _a !== void 0 ? _a : 'a.');\n        if ((params === null || params === void 0 ? void 0 : params.position) !== undefined)\n            articulation.setPosition(params.position);\n        if ((params === null || params === void 0 ? void 0 : params.betweenLines) !== undefined)\n            articulation.setBetweenLines(params.betweenLines);\n        articulation.setContext(this.context);\n        return articulation;\n    }\n    Ornament(type, params) {\n        const options = Object.assign({ type, accidental: '' }, params);\n        const ornament = new Ornament(type);\n        if ((params === null || params === void 0 ? void 0 : params.position) !== undefined) {\n            ornament.setPosition(params.position);\n        }\n        if (options.upperAccidental) {\n            ornament.setUpperAccidental(options.upperAccidental);\n        }\n        if (options.lowerAccidental) {\n            ornament.setLowerAccidental(options.lowerAccidental);\n        }\n        if (typeof options.delayed !== 'undefined') {\n            ornament.setDelayed(options.delayed);\n        }\n        ornament.setContext(this.context);\n        return ornament;\n    }\n    TextDynamics(params) {\n        const p = Object.assign({ text: 'p', duration: 'q', dots: 0, line: 0 }, params);\n        const text = new TextDynamics({\n            text: p.text,\n            line: p.line,\n            duration: p.duration,\n            dots: p.dots,\n        });\n        if (this.stave)\n            text.setStave(this.stave);\n        text.setContext(this.context);\n        this.renderQ.push(text);\n        return text;\n    }\n    Fingering(params) {\n        const p = Object.assign({ number: '0', position: 'left' }, params);\n        const fingering = new FretHandFinger(p.number);\n        fingering.setPosition(p.position);\n        fingering.setContext(this.context);\n        return fingering;\n    }\n    StringNumber(params, drawCircle = true) {\n        const stringNumber = new StringNumber(params.number);\n        stringNumber.setPosition(params.position);\n        stringNumber.setContext(this.context);\n        stringNumber.setDrawCircle(drawCircle);\n        return stringNumber;\n    }\n    TickContext() {\n        return new TickContext();\n    }\n    ModifierContext() {\n        return new ModifierContext();\n    }\n    MultiMeasureRest(params) {\n        const numMeasures = defined(params.numberOfMeasures, 'NoNumberOfMeasures');\n        const multiMeasureRest = new MultiMeasureRest(numMeasures, params);\n        multiMeasureRest.setContext(this.context);\n        this.renderQ.push(multiMeasureRest);\n        return multiMeasureRest;\n    }\n    Voice(params) {\n        const p = Object.assign({ time: '4/4' }, params);\n        const voice = new Voice(p.time);\n        this.voices.push(voice);\n        return voice;\n    }\n    StaveConnector(params) {\n        const connector = new StaveConnector(params.topStave, params.bottomStave);\n        connector.setType(params.type).setContext(this.context);\n        this.renderQ.push(connector);\n        return connector;\n    }\n    Formatter(options) {\n        return new Formatter(options);\n    }\n    Tuplet(params) {\n        const p = Object.assign({ notes: [], options: {} }, params);\n        const tuplet = new Tuplet(p.notes, p.options).setContext(this.context);\n        this.renderQ.push(tuplet);\n        return tuplet;\n    }\n    Beam(params) {\n        var _a, _b, _c, _d, _e;\n        const beam = new Beam(params.notes, (_a = params.options) === null || _a === void 0 ? void 0 : _a.autoStem).setContext(this.context);\n        beam.breakSecondaryAt((_c = (_b = params.options) === null || _b === void 0 ? void 0 : _b.secondaryBeamBreaks) !== null && _c !== void 0 ? _c : []);\n        if ((_d = params.options) === null || _d === void 0 ? void 0 : _d.partialBeamDirections) {\n            Object.entries((_e = params.options) === null || _e === void 0 ? void 0 : _e.partialBeamDirections).forEach(([noteIndex, direction]) => {\n                beam.setPartialBeamSideAt(Number(noteIndex), direction);\n            });\n        }\n        this.renderQ.push(beam);\n        return beam;\n    }\n    Curve(params) {\n        const curve = new Curve(params.from, params.to, params.options).setContext(this.context);\n        this.renderQ.push(curve);\n        return curve;\n    }\n    StaveTie(params) {\n        var _a;\n        const tie = new StaveTie({\n            firstNote: params.from,\n            lastNote: params.to,\n            firstIndexes: params.firstIndexes,\n            lastIndexes: params.lastIndexes,\n        }, params.text);\n        if ((_a = params.options) === null || _a === void 0 ? void 0 : _a.direction)\n            tie.setDirection(params.options.direction);\n        tie.setContext(this.context);\n        this.renderQ.push(tie);\n        return tie;\n    }\n    StaveLine(params) {\n        var _a, _b;\n        const line = new StaveLine({\n            firstNote: params.from,\n            lastNote: params.to,\n            firstIndexes: params.firstIndexes,\n            lastIndexes: params.lastIndexes,\n        });\n        if ((_a = params.options) === null || _a === void 0 ? void 0 : _a.text)\n            line.setText(params.options.text);\n        if ((_b = params.options) === null || _b === void 0 ? void 0 : _b.font)\n            line.setFont(params.options.font);\n        line.setContext(this.context);\n        this.renderQ.push(line);\n        return line;\n    }\n    VibratoBracket(params) {\n        const vibratoBracket = new VibratoBracket({\n            start: params.from,\n            stop: params.to,\n        });\n        if (params.options.line)\n            vibratoBracket.setLine(params.options.line);\n        if (params.options.code)\n            vibratoBracket.setVibratoCode(params.options.code);\n        vibratoBracket.setContext(this.context);\n        this.renderQ.push(vibratoBracket);\n        return vibratoBracket;\n    }\n    TextBracket(params) {\n        const textBracket = new TextBracket({\n            start: params.from,\n            stop: params.to,\n            text: params.text,\n            superscript: params.options.superscript,\n            position: params.options.position,\n        });\n        if (params.options.line)\n            textBracket.setLine(params.options.line);\n        if (params.options.font)\n            textBracket.setFont(params.options.font);\n        textBracket.setContext(this.context);\n        this.renderQ.push(textBracket);\n        return textBracket;\n    }\n    System(params = {}) {\n        params.factory = this;\n        const system = new System(params).setContext(this.context);\n        this.systems.push(system);\n        return system;\n    }\n    EasyScore(options = {}) {\n        options.factory = this;\n        return new EasyScore(options);\n    }\n    PedalMarking(params) {\n        const p = Object.assign({ notes: [], options: {\n                style: 'mixed',\n            } }, params);\n        const pedal = new PedalMarking(p.notes);\n        pedal.setType(PedalMarking.typeString[p.options.style]);\n        pedal.setContext(this.context);\n        this.renderQ.push(pedal);\n        return pedal;\n    }\n    NoteSubGroup(params) {\n        const p = Object.assign({ notes: [] }, params);\n        const group = new NoteSubGroup(p.notes);\n        group.setContext(this.context);\n        return group;\n    }\n    draw() {\n        const ctx = this.context;\n        this.systems.forEach((s) => s.setContext(ctx).format());\n        this.staves.forEach((s) => s.setContext(ctx).drawWithStyle());\n        this.voices.forEach((v) => v.setContext(ctx).drawWithStyle());\n        this.renderQ.forEach((e) => {\n            if (!e.isRendered())\n                e.setContext(ctx).drawWithStyle();\n        });\n        this.systems.forEach((s) => s.setContext(ctx).drawWithStyle());\n        this.reset();\n    }\n}\nFactory.DEBUG = false;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,UAAU,EAAEC,2BAA2B,EAAEC,yBAAyB,QAAQ,iBAAiB;AACpG,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,EAAEC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AACtD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,OAAO,CAACC,KAAK,EACbR,GAAG,CAAC,iBAAiB,EAAEM,IAAI,CAAC;AACpC;AACA,OAAO,MAAMC,OAAO,CAAC;EACjB,OAAOE,gBAAgBA,CAACC,SAAS,EAAEC,KAAK,GAAG,GAAG,EAAEC,MAAM,GAAG,GAAG,EAAE;IAC1D,OAAO,IAAIL,OAAO,CAAC;MAAEM,QAAQ,EAAE;QAAEH,SAAS;QAAEC,KAAK;QAAEC;MAAO;IAAE,CAAC,CAAC;EAClE;EACAE,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtBV,CAAC,CAAC,eAAe,EAAEU,OAAO,CAAC;IAC3B,IAAI,CAACA,OAAO,GAAG;MACXC,KAAK,EAAE;QACHC,KAAK,EAAE;MACX,CAAC;MACDJ,QAAQ,EAAE;QACNH,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXM,UAAU,EAAE;MAChB;IACJ,CAAC;IACD,IAAI,CAACC,UAAU,CAACJ,OAAO,CAAC;EAC5B;EACAK,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACR,KAAK,GAAGS,SAAS;EAC1B;EACAN,UAAUA,CAACJ,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGW,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACZ,OAAO,CAAC,EAAEA,OAAO,CAAC;IACtE,IAAI,CAACa,YAAY,CAAC,CAAC;IACnB,IAAI,CAACR,KAAK,CAAC,CAAC;EAChB;EACAQ,YAAYA,CAAA,EAAG;IACX,MAAM;MAAElB,SAAS;MAAEC,KAAK;MAAEC,MAAM;MAAEM;IAAW,CAAC,GAAG,IAAI,CAACH,OAAO,CAACF,QAAQ;IACtE,IAAIH,SAAS,KAAK,IAAI,EAAE;MACpB;IACJ;IACA,IAAIA,SAAS,KAAK,EAAE,EAAE;MAClBL,CAAC,CAAC,IAAI,CAAC;MACP,MAAM,IAAIJ,YAAY,CAAC,8CAA8C,CAAC;IAC1E;IACA,IAAI4B,OAAO,GAAG,IAAI,CAACd,OAAO,CAACF,QAAQ,CAACgB,OAAO;IAC3C,IAAIA,OAAO,KAAKJ,SAAS,EAAE;MACvB,MAAMK,IAAI,GAAGC,QAAQ,CAACC,cAAc,CAACtB,SAAS,CAAC;MAC/C,IAAIN,YAAY,CAAC0B,IAAI,CAAC,EAAE;QACpBD,OAAO,GAAG/C,QAAQ,CAACmD,QAAQ,CAACC,MAAM;MACtC,CAAC,MACI;QACDL,OAAO,GAAG/C,QAAQ,CAACmD,QAAQ,CAACE,GAAG;MACnC;IACJ;IACA,IAAI,CAACC,OAAO,GAAGtD,QAAQ,CAACuD,YAAY,CAAC3B,SAAS,EAAEmB,OAAO,EAAElB,KAAK,EAAEC,MAAM,EAAEM,UAAU,CAAC;EACvF;EACAoB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,OAAO;EACvB;EACAG,UAAUA,CAACH,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,KAAK;EACrB;EACAyB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjB,MAAM;EACtB;EACAxC,KAAKA,CAAC0D,MAAM,EAAE;IACV,MAAMC,UAAU,GAAG,IAAI,CAAC5B,OAAO,CAACC,KAAK,CAACC,KAAK;IAC3C,MAAM2B,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEkB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEnC,KAAK,EAAE,IAAI,CAACI,OAAO,CAACF,QAAQ,CAACF,KAAK,GAAGgC,UAAU,GAAG,GAAG;MAAE5B,OAAO,EAAE;QAAEgC,qBAAqB,EAAEJ,UAAU,GAAG;MAAI;IAAE,CAAC,EAAED,MAAM,CAAC;IAC5J,MAAM1B,KAAK,GAAG,IAAIhC,KAAK,CAAC4D,CAAC,CAACC,CAAC,EAAED,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACjC,KAAK,EAAEiC,CAAC,CAAC7B,OAAO,CAAC;IACrD,IAAI,CAACQ,MAAM,CAACyB,IAAI,CAAChC,KAAK,CAAC;IACvBA,KAAK,CAACuB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,IAAI,CAACpB,KAAK,GAAGA,KAAK;IAClB,OAAOA,KAAK;EAChB;EACAxB,QAAQA,CAACkD,MAAM,EAAE;IACb,MAAMC,UAAU,GAAG,IAAI,CAAC5B,OAAO,CAACC,KAAK,CAACC,KAAK;IAC3C,MAAM2B,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEkB,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEnC,KAAK,EAAE,IAAI,CAACI,OAAO,CAACF,QAAQ,CAACF,KAAK,GAAGgC,UAAU,GAAG,GAAG;MAAE5B,OAAO,EAAE;QAAEgC,qBAAqB,EAAEJ,UAAU,GAAG;MAAI;IAAE,CAAC,EAAED,MAAM,CAAC;IAC5J,MAAM1B,KAAK,GAAG,IAAIxB,QAAQ,CAACoD,CAAC,CAACC,CAAC,EAAED,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACjC,KAAK,EAAEiC,CAAC,CAAC7B,OAAO,CAAC;IACxD,IAAI,CAACQ,MAAM,CAACyB,IAAI,CAAChC,KAAK,CAAC;IACvBA,KAAK,CAACuB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,IAAI,CAACpB,KAAK,GAAGA,KAAK;IAClB,OAAOA,KAAK;EAChB;EACA7B,SAASA,CAAC8D,UAAU,EAAE;IAClB,MAAMC,IAAI,GAAG,IAAI/D,SAAS,CAAC8D,UAAU,CAAC;IACtC,IAAI,IAAI,CAACjC,KAAK,EACVkC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BkC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACE,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACA7E,SAASA,CAAC+E,KAAK,EAAEH,UAAU,EAAElC,OAAO,EAAE;IAClC,MAAMmC,IAAI,GAAG,IAAI7E,SAAS,CAAC+E,KAAK,EAAEH,UAAU,EAAElC,OAAO,CAAC;IACtD,IAAI,IAAI,CAACC,KAAK,EACVkC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BkC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACE,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACAnE,UAAUA,CAACsE,IAAI,EAAEJ,UAAU,EAAElC,OAAO,EAAE;IAClC,MAAMmC,IAAI,GAAG,IAAInE,UAAU,CAACsE,IAAI,EAAEJ,UAAU,EAAElC,OAAO,CAAC;IACtD,IAAI,IAAI,CAACC,KAAK,EACVkC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BkC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACE,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACA9E,SAASA,CAAC6E,UAAU,EAAE;IAClB,MAAMK,SAAS,GAAG,IAAIlF,SAAS,CAAC6E,UAAU,CAAC;IAC3C,IAAI,IAAI,CAACjC,KAAK,EACVsC,SAAS,CAACH,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAClCsC,SAAS,CAACf,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAClC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACM,SAAS,CAAC;IAC5B,OAAOA,SAAS;EACpB;EACA3D,QAAQA,CAACsD,UAAU,EAAE;IACjB,MAAMM,QAAQ,GAAG,IAAI5D,QAAQ,CAACsD,UAAU,CAAC;IACzC,IAAI,IAAI,CAACjC,KAAK,EACVuC,QAAQ,CAACJ,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IACjCuC,QAAQ,CAAChB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACjC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACO,QAAQ,CAAC;IAC3B,OAAOA,QAAQ;EACnB;EACA3F,OAAOA,CAAC8E,MAAM,GAAG,CAAC,CAAC,EAAE;IACjB,MAAMc,OAAO,GAAG,IAAI5F,OAAO,CAAC8E,MAAM,CAACW,IAAI,CAAC;IACxC,IAAI,IAAI,CAACrC,KAAK,EACVwC,OAAO,CAACL,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAChCwC,OAAO,CAACjB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAChC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACQ,OAAO,CAAC;IAC1B,OAAOA,OAAO;EAClB;EACAzF,QAAQA,CAAC2E,MAAM,EAAE;IACb,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAE0B,IAAI,EAAE,QAAQ;MAAEtC,OAAO,EAAE;QAC3C0C,IAAI,EAAE,SAAS;QACfC,UAAU,EAAEjC;MAChB;IAAE,CAAC,EAAEiB,MAAM,CAAC;IAChB,MAAMiB,QAAQ,GAAG,IAAI5F,QAAQ,CAAC6E,CAAC,CAACS,IAAI,EAAET,CAAC,CAAC7B,OAAO,CAAC0C,IAAI,EAAEb,CAAC,CAAC7B,OAAO,CAAC2C,UAAU,CAAC;IAC3E,IAAI,IAAI,CAAC1C,KAAK,EACV2C,QAAQ,CAACR,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IACjC2C,QAAQ,CAACpB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACjC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACW,QAAQ,CAAC;IAC3B,OAAOA,QAAQ;EACnB;EACA9D,WAAWA,CAAC6C,MAAM,EAAE;IAChB,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEiC,IAAI,EAAE;IAAM,CAAC,EAAElB,MAAM,CAAC;IAChD,MAAMmB,WAAW,GAAG,IAAIhE,WAAW,CAAC+C,CAAC,CAACgB,IAAI,CAAC;IAC3C,IAAI,IAAI,CAAC5C,KAAK,EACV6C,WAAW,CAACV,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IACpC6C,WAAW,CAACtB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACpC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACa,WAAW,CAAC;IAC9B,OAAOA,WAAW;EACtB;EACArF,UAAUA,CAACkE,MAAM,EAAE;IACf,MAAMoB,UAAU,GAAG,IAAItF,UAAU,CAACkE,MAAM,CAACqB,GAAG,EAAErB,MAAM,CAACsB,SAAS,EAAEtB,MAAM,CAACuB,QAAQ,CAAC;IAChF,IAAI,IAAI,CAACjD,KAAK,EACV8C,UAAU,CAACX,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IACnC8C,UAAU,CAACvB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACnC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACc,UAAU,CAAC;IAC7B,OAAOA,UAAU;EACrB;EACAvE,OAAOA,CAAC0D,UAAU,EAAE;IAChB,MAAMC,IAAI,GAAG,IAAI3D,OAAO,CAAC0D,UAAU,CAAC;IACpC,IAAI,IAAI,CAACjC,KAAK,EACVkC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BkC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACE,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACA5E,SAASA,CAAC2E,UAAU,EAAE;IAClB,MAAMC,IAAI,GAAG,IAAI5E,SAAS,CAAC2E,UAAU,CAAC;IACtC,IAAI,IAAI,CAACjC,KAAK,EACVkC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BkC,IAAI,CAACX,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,OAAOc,IAAI;EACf;EACA3E,cAAcA,CAACmE,MAAM,EAAE;IACnB,MAAMwB,KAAK,GAAG,IAAI3F,cAAc,CAACmE,MAAM,CAACyB,KAAK,EAAEzB,MAAM,CAAC0B,IAAI,CAAC;IAC3DF,KAAK,CAAC3B,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,OAAO8B,KAAK;EAChB;EACA3G,UAAUA,CAACmF,MAAM,EAAE;IACf,MAAM2B,KAAK,GAAG,IAAI9G,UAAU,CAACmF,MAAM,CAACW,IAAI,CAAC;IACzCgB,KAAK,CAAC9B,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,OAAOiC,KAAK;EAChB;EACA7G,UAAUA,CAACkF,MAAM,EAAE;IACf,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAE2C,IAAI,EAAE,GAAG;MAAEC,QAAQ,EAAE9G,2BAA2B,CAAC+G,MAAM;MAAEC,QAAQ,EAAE/G,yBAAyB,CAACgH;IAAO,CAAC,EAAEhC,MAAM,CAAC;IACxI,MAAMgB,UAAU,GAAG,IAAIlG,UAAU,CAACoF,CAAC,CAAC0B,IAAI,CAAC;IACzCZ,UAAU,CAACiB,gBAAgB,CAAC/B,CAAC,CAAC2B,QAAQ,CAAC;IACvCb,UAAU,CAACkB,wBAAwB,CAAChC,CAAC,CAAC6B,QAAQ,CAAC;IAC/Cf,UAAU,CAACmB,OAAO,CAACjC,CAAC,CAACkC,IAAI,CAAC;IAC1BpB,UAAU,CAACnB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACnC,OAAOsB,UAAU;EACrB;EACA5F,WAAWA,CAAC4E,MAAM,EAAE;IAChB,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAE8C,QAAQ,EAAE,KAAK;MAAEF,QAAQ,EAAE;IAAS,CAAC,EAAE7B,MAAM,CAAC;IACxE,MAAMqC,WAAW,GAAG,IAAIjH,WAAW,CAAC,CAAC;IACrCiH,WAAW,CAACC,aAAa,CAACpC,CAAC,CAAC2B,QAAQ,CAAC;IACrCQ,WAAW,CAACE,WAAW,CAACrC,CAAC,CAAC6B,QAAQ,CAAC;IACnC,IAAI,OAAO7B,CAAC,CAACsC,UAAU,KAAK,QAAQ,IAAI,OAAOtC,CAAC,CAACuC,QAAQ,KAAK,QAAQ,EAAE;MACpE,IAAI,OAAOvC,CAAC,CAACwC,UAAU,KAAK,QAAQ,EAChCL,WAAW,CAACF,OAAO,CAACjC,CAAC,CAACsC,UAAU,EAAEtC,CAAC,CAACuC,QAAQ,EAAEvC,CAAC,CAACwC,UAAU,CAAC,CAAC,KAE5DL,WAAW,CAACF,OAAO,CAACjC,CAAC,CAACsC,UAAU,EAAEtC,CAAC,CAACuC,QAAQ,EAAE,EAAE,CAAC;IACzD,CAAC,MACI,IAAI,OAAOvC,CAAC,CAACuC,QAAQ,KAAK,QAAQ,EAAE;MACrCJ,WAAW,CAACM,WAAW,CAACzC,CAAC,CAACuC,QAAQ,CAAC;IACvC;IACAJ,WAAW,CAACxC,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACpC,OAAO2C,WAAW;EACtB;EACApH,YAAYA,CAAC+E,MAAM,EAAE;IACjB,IAAI4C,EAAE;IACN,MAAMC,YAAY,GAAG,IAAI5H,YAAY,CAAC,CAAC2H,EAAE,GAAG5C,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACW,IAAI,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC;IAC/I,IAAI,CAAC5C,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8C,QAAQ,MAAM/D,SAAS,EAC/E8D,YAAY,CAACE,WAAW,CAAC/C,MAAM,CAAC8C,QAAQ,CAAC;IAC7C,IAAI,CAAC9C,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgD,YAAY,MAAMjE,SAAS,EACnF8D,YAAY,CAACI,eAAe,CAACjD,MAAM,CAACgD,YAAY,CAAC;IACrDH,YAAY,CAAChD,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACrC,OAAOmD,YAAY;EACvB;EACA3G,QAAQA,CAACyE,IAAI,EAAEX,MAAM,EAAE;IACnB,MAAM3B,OAAO,GAAGW,MAAM,CAACC,MAAM,CAAC;MAAE0B,IAAI;MAAEuC,UAAU,EAAE;IAAG,CAAC,EAAElD,MAAM,CAAC;IAC/D,MAAMmD,QAAQ,GAAG,IAAIjH,QAAQ,CAACyE,IAAI,CAAC;IACnC,IAAI,CAACX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8C,QAAQ,MAAM/D,SAAS,EAAE;MACjFoE,QAAQ,CAACJ,WAAW,CAAC/C,MAAM,CAAC8C,QAAQ,CAAC;IACzC;IACA,IAAIzE,OAAO,CAAC+E,eAAe,EAAE;MACzBD,QAAQ,CAACE,kBAAkB,CAAChF,OAAO,CAAC+E,eAAe,CAAC;IACxD;IACA,IAAI/E,OAAO,CAACiF,eAAe,EAAE;MACzBH,QAAQ,CAACI,kBAAkB,CAAClF,OAAO,CAACiF,eAAe,CAAC;IACxD;IACA,IAAI,OAAOjF,OAAO,CAACmF,OAAO,KAAK,WAAW,EAAE;MACxCL,QAAQ,CAACM,UAAU,CAACpF,OAAO,CAACmF,OAAO,CAAC;IACxC;IACAL,QAAQ,CAACtD,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACjC,OAAOyD,QAAQ;EACnB;EACAnG,YAAYA,CAACgD,MAAM,EAAE;IACjB,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAE2C,IAAI,EAAE,GAAG;MAAE8B,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,EAAE5D,MAAM,CAAC;IAC/E,MAAM4B,IAAI,GAAG,IAAI5E,YAAY,CAAC;MAC1B4E,IAAI,EAAE1B,CAAC,CAAC0B,IAAI;MACZgC,IAAI,EAAE1D,CAAC,CAAC0D,IAAI;MACZF,QAAQ,EAAExD,CAAC,CAACwD,QAAQ;MACpBC,IAAI,EAAEzD,CAAC,CAACyD;IACZ,CAAC,CAAC;IACF,IAAI,IAAI,CAACrF,KAAK,EACVsD,IAAI,CAACnB,QAAQ,CAAC,IAAI,CAACnC,KAAK,CAAC;IAC7BsD,IAAI,CAAC/B,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACsB,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACAiC,SAASA,CAAC7D,MAAM,EAAE;IACd,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAE6E,MAAM,EAAE,GAAG;MAAEhB,QAAQ,EAAE;IAAO,CAAC,EAAE9C,MAAM,CAAC;IAClE,MAAM+D,SAAS,GAAG,IAAItI,cAAc,CAACyE,CAAC,CAAC4D,MAAM,CAAC;IAC9CC,SAAS,CAAChB,WAAW,CAAC7C,CAAC,CAAC4C,QAAQ,CAAC;IACjCiB,SAAS,CAAClE,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAClC,OAAOqE,SAAS;EACpB;EACApH,YAAYA,CAACqD,MAAM,EAAEgE,UAAU,GAAG,IAAI,EAAE;IACpC,MAAMC,YAAY,GAAG,IAAItH,YAAY,CAACqD,MAAM,CAAC8D,MAAM,CAAC;IACpDG,YAAY,CAAClB,WAAW,CAAC/C,MAAM,CAAC8C,QAAQ,CAAC;IACzCmB,YAAY,CAACpE,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACrCuE,YAAY,CAACC,aAAa,CAACF,UAAU,CAAC;IACtC,OAAOC,YAAY;EACvB;EACA/G,WAAWA,CAAA,EAAG;IACV,OAAO,IAAIA,WAAW,CAAC,CAAC;EAC5B;EACAnB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAIA,eAAe,CAAC,CAAC;EAChC;EACAC,gBAAgBA,CAACgE,MAAM,EAAE;IACrB,MAAMmE,WAAW,GAAG9G,OAAO,CAAC2C,MAAM,CAACoE,gBAAgB,EAAE,oBAAoB,CAAC;IAC1E,MAAMC,gBAAgB,GAAG,IAAIrI,gBAAgB,CAACmI,WAAW,EAAEnE,MAAM,CAAC;IAClEqE,gBAAgB,CAACxE,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACzC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAAC+D,gBAAgB,CAAC;IACnC,OAAOA,gBAAgB;EAC3B;EACA5G,KAAKA,CAACuC,MAAM,EAAE;IACV,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEiC,IAAI,EAAE;IAAM,CAAC,EAAElB,MAAM,CAAC;IAChD,MAAMsE,KAAK,GAAG,IAAI7G,KAAK,CAACyC,CAAC,CAACgB,IAAI,CAAC;IAC/B,IAAI,CAACpC,MAAM,CAACwB,IAAI,CAACgE,KAAK,CAAC;IACvB,OAAOA,KAAK;EAChB;EACA/H,cAAcA,CAACyD,MAAM,EAAE;IACnB,MAAMuE,SAAS,GAAG,IAAIhI,cAAc,CAACyD,MAAM,CAACwE,QAAQ,EAAExE,MAAM,CAACyE,WAAW,CAAC;IACzEF,SAAS,CAACG,OAAO,CAAC1E,MAAM,CAACW,IAAI,CAAC,CAACd,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACvD,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACiE,SAAS,CAAC;IAC5B,OAAOA,SAAS;EACpB;EACA/I,SAASA,CAAC6C,OAAO,EAAE;IACf,OAAO,IAAI7C,SAAS,CAAC6C,OAAO,CAAC;EACjC;EACAjB,MAAMA,CAAC4C,MAAM,EAAE;IACX,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEwC,KAAK,EAAE,EAAE;MAAEpD,OAAO,EAAE,CAAC;IAAE,CAAC,EAAE2B,MAAM,CAAC;IAC3D,MAAM2E,MAAM,GAAG,IAAIvH,MAAM,CAAC8C,CAAC,CAACuB,KAAK,EAAEvB,CAAC,CAAC7B,OAAO,CAAC,CAACwB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACtE,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACqE,MAAM,CAAC;IACzB,OAAOA,MAAM;EACjB;EACAxJ,IAAIA,CAAC6E,MAAM,EAAE;IACT,IAAI4C,EAAE,EAAEgC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAI7J,IAAI,CAAC6E,MAAM,CAACyB,KAAK,EAAE,CAACmB,EAAE,GAAG5C,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIuE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,QAAQ,CAAC,CAACpF,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACpIsF,IAAI,CAACE,gBAAgB,CAAC,CAACL,EAAE,GAAG,CAACD,EAAE,GAAG5E,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIuG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,mBAAmB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;IACnJ,IAAI,CAACC,EAAE,GAAG9E,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIyG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,qBAAqB,EAAE;MACrFpG,MAAM,CAACqG,OAAO,CAAC,CAACN,EAAE,GAAG/E,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAI0G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,qBAAqB,CAAC,CAACE,OAAO,CAAC,CAAC,CAACC,SAAS,EAAEC,SAAS,CAAC,KAAK;QACpIR,IAAI,CAACS,oBAAoB,CAACC,MAAM,CAACH,SAAS,CAAC,EAAEC,SAAS,CAAC;MAC3D,CAAC,CAAC;IACN;IACA,IAAI,CAAC7G,OAAO,CAAC2B,IAAI,CAAC0E,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACA1J,KAAKA,CAAC0E,MAAM,EAAE;IACV,MAAM2F,KAAK,GAAG,IAAIrK,KAAK,CAAC0E,MAAM,CAAC4F,IAAI,EAAE5F,MAAM,CAAC6F,EAAE,EAAE7F,MAAM,CAAC3B,OAAO,CAAC,CAACwB,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACxF,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACqF,KAAK,CAAC;IACxB,OAAOA,KAAK;EAChB;EACAjJ,QAAQA,CAACsD,MAAM,EAAE;IACb,IAAI4C,EAAE;IACN,MAAMkD,GAAG,GAAG,IAAIpJ,QAAQ,CAAC;MACrBqJ,SAAS,EAAE/F,MAAM,CAAC4F,IAAI;MACtBI,QAAQ,EAAEhG,MAAM,CAAC6F,EAAE;MACnBI,YAAY,EAAEjG,MAAM,CAACiG,YAAY;MACjCC,WAAW,EAAElG,MAAM,CAACkG;IACxB,CAAC,EAAElG,MAAM,CAAC4B,IAAI,CAAC;IACf,IAAI,CAACgB,EAAE,GAAG5C,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIuE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4C,SAAS,EACvEM,GAAG,CAACK,YAAY,CAACnG,MAAM,CAAC3B,OAAO,CAACmH,SAAS,CAAC;IAC9CM,GAAG,CAACjG,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC5B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACwF,GAAG,CAAC;IACtB,OAAOA,GAAG;EACd;EACAtJ,SAASA,CAACwD,MAAM,EAAE;IACd,IAAI4C,EAAE,EAAEgC,EAAE;IACV,MAAMhB,IAAI,GAAG,IAAIpH,SAAS,CAAC;MACvBuJ,SAAS,EAAE/F,MAAM,CAAC4F,IAAI;MACtBI,QAAQ,EAAEhG,MAAM,CAAC6F,EAAE;MACnBI,YAAY,EAAEjG,MAAM,CAACiG,YAAY;MACjCC,WAAW,EAAElG,MAAM,CAACkG;IACxB,CAAC,CAAC;IACF,IAAI,CAACtD,EAAE,GAAG5C,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIuE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChB,IAAI,EAClEgC,IAAI,CAACwC,OAAO,CAACpG,MAAM,CAAC3B,OAAO,CAACuD,IAAI,CAAC;IACrC,IAAI,CAACgD,EAAE,GAAG5E,MAAM,CAAC3B,OAAO,MAAM,IAAI,IAAIuG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACxC,IAAI,EAClEwB,IAAI,CAACzB,OAAO,CAACnC,MAAM,CAAC3B,OAAO,CAAC+D,IAAI,CAAC;IACrCwB,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACsD,IAAI,CAAC;IACvB,OAAOA,IAAI;EACf;EACApG,cAAcA,CAACwC,MAAM,EAAE;IACnB,MAAMqG,cAAc,GAAG,IAAI7I,cAAc,CAAC;MACtC8I,KAAK,EAAEtG,MAAM,CAAC4F,IAAI;MAClBW,IAAI,EAAEvG,MAAM,CAAC6F;IACjB,CAAC,CAAC;IACF,IAAI7F,MAAM,CAAC3B,OAAO,CAACuF,IAAI,EACnByC,cAAc,CAACG,OAAO,CAACxG,MAAM,CAAC3B,OAAO,CAACuF,IAAI,CAAC;IAC/C,IAAI5D,MAAM,CAAC3B,OAAO,CAACoI,IAAI,EACnBJ,cAAc,CAACK,cAAc,CAAC1G,MAAM,CAAC3B,OAAO,CAACoI,IAAI,CAAC;IACtDJ,cAAc,CAACxG,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACvC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAAC+F,cAAc,CAAC;IACjC,OAAOA,cAAc;EACzB;EACAtJ,WAAWA,CAACiD,MAAM,EAAE;IAChB,MAAM2G,WAAW,GAAG,IAAI5J,WAAW,CAAC;MAChCuJ,KAAK,EAAEtG,MAAM,CAAC4F,IAAI;MAClBW,IAAI,EAAEvG,MAAM,CAAC6F,EAAE;MACfjE,IAAI,EAAE5B,MAAM,CAAC4B,IAAI;MACjBgF,WAAW,EAAE5G,MAAM,CAAC3B,OAAO,CAACuI,WAAW;MACvC9D,QAAQ,EAAE9C,MAAM,CAAC3B,OAAO,CAACyE;IAC7B,CAAC,CAAC;IACF,IAAI9C,MAAM,CAAC3B,OAAO,CAACuF,IAAI,EACnB+C,WAAW,CAACH,OAAO,CAACxG,MAAM,CAAC3B,OAAO,CAACuF,IAAI,CAAC;IAC5C,IAAI5D,MAAM,CAAC3B,OAAO,CAAC+D,IAAI,EACnBuE,WAAW,CAACxE,OAAO,CAACnC,MAAM,CAAC3B,OAAO,CAAC+D,IAAI,CAAC;IAC5CuE,WAAW,CAAC9G,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IACpC,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAACqG,WAAW,CAAC;IAC9B,OAAOA,WAAW;EACtB;EACA/J,MAAMA,CAACoD,MAAM,GAAG,CAAC,CAAC,EAAE;IAChBA,MAAM,CAAC6G,OAAO,GAAG,IAAI;IACrB,MAAMC,MAAM,GAAG,IAAIlK,MAAM,CAACoD,MAAM,CAAC,CAACH,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC1D,IAAI,CAACd,OAAO,CAAC0B,IAAI,CAACwG,MAAM,CAAC;IACzB,OAAOA,MAAM;EACjB;EACAvL,SAASA,CAAC8C,OAAO,GAAG,CAAC,CAAC,EAAE;IACpBA,OAAO,CAACwI,OAAO,GAAG,IAAI;IACtB,OAAO,IAAItL,SAAS,CAAC8C,OAAO,CAAC;EACjC;EACAlC,YAAYA,CAAC6D,MAAM,EAAE;IACjB,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEwC,KAAK,EAAE,EAAE;MAAEpD,OAAO,EAAE;QACtC0I,KAAK,EAAE;MACX;IAAE,CAAC,EAAE/G,MAAM,CAAC;IAChB,MAAMgH,KAAK,GAAG,IAAI7K,YAAY,CAAC+D,CAAC,CAACuB,KAAK,CAAC;IACvCuF,KAAK,CAACtC,OAAO,CAACvI,YAAY,CAAC8K,UAAU,CAAC/G,CAAC,CAAC7B,OAAO,CAAC0I,KAAK,CAAC,CAAC;IACvDC,KAAK,CAACnH,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,IAAI,CAACf,OAAO,CAAC2B,IAAI,CAAC0G,KAAK,CAAC;IACxB,OAAOA,KAAK;EAChB;EACA/K,YAAYA,CAAC+D,MAAM,EAAE;IACjB,MAAME,CAAC,GAAGlB,MAAM,CAACC,MAAM,CAAC;MAAEwC,KAAK,EAAE;IAAG,CAAC,EAAEzB,MAAM,CAAC;IAC9C,MAAMwB,KAAK,GAAG,IAAIvF,YAAY,CAACiE,CAAC,CAACuB,KAAK,CAAC;IACvCD,KAAK,CAAC3B,UAAU,CAAC,IAAI,CAACH,OAAO,CAAC;IAC9B,OAAO8B,KAAK;EAChB;EACA0F,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACzH,OAAO;IACxB,IAAI,CAACd,OAAO,CAAC0G,OAAO,CAAE8B,CAAC,IAAKA,CAAC,CAACvH,UAAU,CAACsH,GAAG,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC;IACvD,IAAI,CAACxI,MAAM,CAACyG,OAAO,CAAE8B,CAAC,IAAKA,CAAC,CAACvH,UAAU,CAACsH,GAAG,CAAC,CAACG,aAAa,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACxI,MAAM,CAACwG,OAAO,CAAEiC,CAAC,IAAKA,CAAC,CAAC1H,UAAU,CAACsH,GAAG,CAAC,CAACG,aAAa,CAAC,CAAC,CAAC;IAC7D,IAAI,CAAC3I,OAAO,CAAC2G,OAAO,CAAEkC,CAAC,IAAK;MACxB,IAAI,CAACA,CAAC,CAACC,UAAU,CAAC,CAAC,EACfD,CAAC,CAAC3H,UAAU,CAACsH,GAAG,CAAC,CAACG,aAAa,CAAC,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAAC1I,OAAO,CAAC0G,OAAO,CAAE8B,CAAC,IAAKA,CAAC,CAACvH,UAAU,CAACsH,GAAG,CAAC,CAACG,aAAa,CAAC,CAAC,CAAC;IAC9D,IAAI,CAAC5I,KAAK,CAAC,CAAC;EAChB;AACJ;AACAb,OAAO,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}