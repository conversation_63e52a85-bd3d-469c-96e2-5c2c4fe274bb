{"ast": null, "code": "import { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { defined, log } from './util.js';\nfunction L(...args) {\n  if (NoteHead.DEBUG) log('VexFlow.NoteHead', args);\n}\nexport class NoteHead extends Note {\n  static get CATEGORY() {\n    return \"NoteHead\";\n  }\n  constructor(noteStruct) {\n    var _a;\n    super(noteStruct);\n    this.customGlyph = false;\n    this.ledger = {\n      '\\ue4e3': '\\ue4f4',\n      '\\ue4e4': '\\ue4f5'\n    };\n    this.index = noteStruct.index;\n    this.x = noteStruct.x || 0;\n    this.y = noteStruct.y || 0;\n    if (noteStruct.noteType) this.noteType = noteStruct.noteType;\n    this.displaced = noteStruct.displaced || false;\n    this.stemDirection = noteStruct.stemDirection || Stem.UP;\n    this.line = noteStruct.line || 0;\n    this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n    defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n    if ((this.line > 5 || this.line < 0) && this.ledger[this.glyphProps.codeHead]) {\n      this.glyphProps.codeHead = this.ledger[this.glyphProps.codeHead];\n    }\n    this.text = this.glyphProps.codeHead;\n    if (noteStruct.customGlyphCode) {\n      this.customGlyph = true;\n      this.text = noteStruct.customGlyphCode;\n    }\n    this.setStyle((_a = noteStruct.style) !== null && _a !== void 0 ? _a : {});\n    this.slashed = noteStruct.slashed || false;\n    this.renderOptions = Object.assign({}, this.renderOptions);\n  }\n  getWidth() {\n    return this.width;\n  }\n  isDisplaced() {\n    return this.displaced === true;\n  }\n  getLine() {\n    return this.line;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  getAbsoluteX() {\n    const x = !this.preFormatted ? this.x : super.getAbsoluteX();\n    const displacementStemAdjustment = Stem.WIDTH / 2;\n    return x + (this.displaced ? (this.width - displacementStemAdjustment) * this.stemDirection : 0);\n  }\n  setStave(stave) {\n    const line = this.getLine();\n    this.stave = stave;\n    if (this.stave) {\n      this.setY(this.stave.getYForNote(line));\n      this.setContext(this.stave.getContext());\n    }\n    return this;\n  }\n  preFormat() {\n    if (this.preFormatted) return this;\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    var _a;\n    const ctx = this.checkContext();\n    this.setRendered();\n    ctx.openGroup('notehead', this.getAttribute('id'));\n    L(\"Drawing note head '\", this.noteType, this.duration, \"' at\", this.x, this.y);\n    this.x = this.getAbsoluteX();\n    this.renderText(ctx, 0, 0);\n    (_a = this.parent) === null || _a === void 0 ? void 0 : _a.drawModifiers(this);\n    ctx.closeGroup();\n  }\n}\nNoteHead.DEBUG = false;", "map": {"version": 3, "names": ["Note", "<PERSON><PERSON>", "defined", "log", "L", "args", "NoteHead", "DEBUG", "CATEGORY", "constructor", "noteStruct", "_a", "customGlyph", "ledger", "index", "x", "y", "noteType", "displaced", "stemDirection", "UP", "line", "glyphProps", "getGlyphProps", "duration", "codeHead", "text", "customGlyphCode", "setStyle", "style", "slashed", "renderOptions", "Object", "assign", "getWidth", "width", "isDisplaced", "getLine", "setLine", "getAbsoluteX", "preFormatted", "displacementStemAdjustment", "WIDTH", "setStave", "stave", "setY", "getYForNote", "setContext", "getContext", "preFormat", "draw", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "renderText", "parent", "drawModifiers", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/notehead.js"], "sourcesContent": ["import { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { defined, log } from './util.js';\nfunction L(...args) {\n    if (NoteHead.DEBUG)\n        log('VexFlow.NoteHead', args);\n}\nexport class NoteHead extends Note {\n    static get CATEGORY() {\n        return \"NoteHead\";\n    }\n    constructor(noteStruct) {\n        var _a;\n        super(noteStruct);\n        this.customGlyph = false;\n        this.ledger = {\n            '\\ue4e3': '\\ue4f4',\n            '\\ue4e4': '\\ue4f5',\n        };\n        this.index = noteStruct.index;\n        this.x = noteStruct.x || 0;\n        this.y = noteStruct.y || 0;\n        if (noteStruct.noteType)\n            this.noteType = noteStruct.noteType;\n        this.displaced = noteStruct.displaced || false;\n        this.stemDirection = noteStruct.stemDirection || Stem.UP;\n        this.line = noteStruct.line || 0;\n        this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n        defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n        if ((this.line > 5 || this.line < 0) && this.ledger[this.glyphProps.codeHead]) {\n            this.glyphProps.codeHead = this.ledger[this.glyphProps.codeHead];\n        }\n        this.text = this.glyphProps.codeHead;\n        if (noteStruct.customGlyphCode) {\n            this.customGlyph = true;\n            this.text = noteStruct.customGlyphCode;\n        }\n        this.setStyle((_a = noteStruct.style) !== null && _a !== void 0 ? _a : {});\n        this.slashed = noteStruct.slashed || false;\n        this.renderOptions = Object.assign({}, this.renderOptions);\n    }\n    getWidth() {\n        return this.width;\n    }\n    isDisplaced() {\n        return this.displaced === true;\n    }\n    getLine() {\n        return this.line;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    getAbsoluteX() {\n        const x = !this.preFormatted ? this.x : super.getAbsoluteX();\n        const displacementStemAdjustment = Stem.WIDTH / 2;\n        return x + (this.displaced ? (this.width - displacementStemAdjustment) * this.stemDirection : 0);\n    }\n    setStave(stave) {\n        const line = this.getLine();\n        this.stave = stave;\n        if (this.stave) {\n            this.setY(this.stave.getYForNote(line));\n            this.setContext(this.stave.getContext());\n        }\n        return this;\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return this;\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        var _a;\n        const ctx = this.checkContext();\n        this.setRendered();\n        ctx.openGroup('notehead', this.getAttribute('id'));\n        L(\"Drawing note head '\", this.noteType, this.duration, \"' at\", this.x, this.y);\n        this.x = this.getAbsoluteX();\n        this.renderText(ctx, 0, 0);\n        (_a = this.parent) === null || _a === void 0 ? void 0 : _a.drawModifiers(this);\n        ctx.closeGroup();\n    }\n}\nNoteHead.DEBUG = false;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,EAAEC,GAAG,QAAQ,WAAW;AACxC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,QAAQ,CAACC,KAAK,EACdJ,GAAG,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACrC;AACA,OAAO,MAAMC,QAAQ,SAASN,IAAI,CAAC;EAC/B,WAAWQ,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIC,EAAE;IACN,KAAK,CAACD,UAAU,CAAC;IACjB,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,MAAM,GAAG;MACV,QAAQ,EAAE,QAAQ;MAClB,QAAQ,EAAE;IACd,CAAC;IACD,IAAI,CAACC,KAAK,GAAGJ,UAAU,CAACI,KAAK;IAC7B,IAAI,CAACC,CAAC,GAAGL,UAAU,CAACK,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACC,CAAC,GAAGN,UAAU,CAACM,CAAC,IAAI,CAAC;IAC1B,IAAIN,UAAU,CAACO,QAAQ,EACnB,IAAI,CAACA,QAAQ,GAAGP,UAAU,CAACO,QAAQ;IACvC,IAAI,CAACC,SAAS,GAAGR,UAAU,CAACQ,SAAS,IAAI,KAAK;IAC9C,IAAI,CAACC,aAAa,GAAGT,UAAU,CAACS,aAAa,IAAIlB,IAAI,CAACmB,EAAE;IACxD,IAAI,CAACC,IAAI,GAAGX,UAAU,CAACW,IAAI,IAAI,CAAC;IAChC,IAAI,CAACC,UAAU,GAAGtB,IAAI,CAACuB,aAAa,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACP,QAAQ,CAAC;IAClEf,OAAO,CAAC,IAAI,CAACoB,UAAU,EAAE,cAAc,EAAE,gCAAgC,IAAI,CAACE,QAAQ,eAAe,IAAI,CAACP,QAAQ,GAAG,CAAC;IACtH,IAAI,CAAC,IAAI,CAACI,IAAI,GAAG,CAAC,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,KAAK,IAAI,CAACR,MAAM,CAAC,IAAI,CAACS,UAAU,CAACG,QAAQ,CAAC,EAAE;MAC3E,IAAI,CAACH,UAAU,CAACG,QAAQ,GAAG,IAAI,CAACZ,MAAM,CAAC,IAAI,CAACS,UAAU,CAACG,QAAQ,CAAC;IACpE;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACJ,UAAU,CAACG,QAAQ;IACpC,IAAIf,UAAU,CAACiB,eAAe,EAAE;MAC5B,IAAI,CAACf,WAAW,GAAG,IAAI;MACvB,IAAI,CAACc,IAAI,GAAGhB,UAAU,CAACiB,eAAe;IAC1C;IACA,IAAI,CAACC,QAAQ,CAAC,CAACjB,EAAE,GAAGD,UAAU,CAACmB,KAAK,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E,IAAI,CAACmB,OAAO,GAAGpB,UAAU,CAACoB,OAAO,IAAI,KAAK;IAC1C,IAAI,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,aAAa,CAAC;EAC9D;EACAG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClB,SAAS,KAAK,IAAI;EAClC;EACAmB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAChB,IAAI;EACpB;EACAiB,OAAOA,CAACjB,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAkB,YAAYA,CAAA,EAAG;IACX,MAAMxB,CAAC,GAAG,CAAC,IAAI,CAACyB,YAAY,GAAG,IAAI,CAACzB,CAAC,GAAG,KAAK,CAACwB,YAAY,CAAC,CAAC;IAC5D,MAAME,0BAA0B,GAAGxC,IAAI,CAACyC,KAAK,GAAG,CAAC;IACjD,OAAO3B,CAAC,IAAI,IAAI,CAACG,SAAS,GAAG,CAAC,IAAI,CAACiB,KAAK,GAAGM,0BAA0B,IAAI,IAAI,CAACtB,aAAa,GAAG,CAAC,CAAC;EACpG;EACAwB,QAAQA,CAACC,KAAK,EAAE;IACZ,MAAMvB,IAAI,GAAG,IAAI,CAACgB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACO,KAAK,GAAGA,KAAK;IAClB,IAAI,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,CAACC,IAAI,CAAC,IAAI,CAACD,KAAK,CAACE,WAAW,CAACzB,IAAI,CAAC,CAAC;MACvC,IAAI,CAAC0B,UAAU,CAAC,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC,CAAC,CAAC;IAC5C;IACA,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,YAAY,EACjB,OAAO,IAAI;IACf,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAU,IAAIA,CAAA,EAAG;IACH,IAAIvC,EAAE;IACN,MAAMwC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClDnD,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAACa,QAAQ,EAAE,IAAI,CAACO,QAAQ,EAAE,MAAM,EAAE,IAAI,CAACT,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC9E,IAAI,CAACD,CAAC,GAAG,IAAI,CAACwB,YAAY,CAAC,CAAC;IAC5B,IAAI,CAACiB,UAAU,CAACL,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,CAACxC,EAAE,GAAG,IAAI,CAAC8C,MAAM,MAAM,IAAI,IAAI9C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+C,aAAa,CAAC,IAAI,CAAC;IAC9EP,GAAG,CAACQ,UAAU,CAAC,CAAC;EACpB;AACJ;AACArD,QAAQ,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}