{"ast": null, "code": "import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Note } from './note.js';\nexport var TextJustification;\n(function (TextJustification) {\n  TextJustification[TextJustification[\"LEFT\"] = 1] = \"LEFT\";\n  TextJustification[TextJustification[\"CENTER\"] = 2] = \"CENTER\";\n  TextJustification[TextJustification[\"RIGHT\"] = 3] = \"RIGHT\";\n})(TextJustification || (TextJustification = {}));\nexport class TextNote extends Note {\n  static get CATEGORY() {\n    return \"TextNote\";\n  }\n  static get GLYPHS() {\n    return {\n      segno: Glyphs.segno,\n      tr: Glyphs.ornamentTrill,\n      mordent: Glyphs.ornamentMordent,\n      mordentUpper: Glyphs.ornamentShortTrill,\n      mordentLower: Glyphs.ornamentMordent,\n      f: Glyphs.dynamicForte,\n      p: Glyphs.dynamicPiano,\n      m: Glyphs.dynamicMezzo,\n      s: Glyphs.dynamicSforzando,\n      z: Glyphs.dynamicZ,\n      coda: Glyphs.coda,\n      pedalOpen: Glyphs.keyboardPedalPed,\n      pedalClose: Glyphs.keyboardPedalUp,\n      caesuraStraight: Glyphs.caesura,\n      caesuraCurved: Glyphs.caesuraCurved,\n      breath: Glyphs.breathMarkComma,\n      tick: Glyphs.breathMarkTick,\n      turn: Glyphs.ornamentTurn,\n      turnInverted: Glyphs.ornamentTurnSlash\n    };\n  }\n  constructor(noteStruct) {\n    var _a, _b;\n    super(noteStruct);\n    this.text = (_a = noteStruct.text) !== null && _a !== void 0 ? _a : '';\n    if (noteStruct.glyph) {\n      this.text += TextNote.GLYPHS[noteStruct.glyph] || noteStruct.glyph;\n    }\n    if (noteStruct.font) {\n      this.setFont(noteStruct.font);\n    } else if (noteStruct.glyph === undefined) {\n      this.setFont(Metrics.getFontInfo('TextNote.text.fontSize'));\n    }\n    const smallerFontSize = Font.convertSizeToPointValue(this.fontInfo.size) * 0.769231;\n    if (noteStruct.superscript) {\n      this.superscript = new Element('TexNote.subSuper');\n      this.superscript.setText(noteStruct.superscript);\n      this.superscript.setFontSize(smallerFontSize);\n    }\n    if (noteStruct.subscript) {\n      this.subscript = new Element('TexNote.subSuper');\n      this.subscript.setText(noteStruct.subscript);\n      this.subscript.setFontSize(smallerFontSize);\n    }\n    this.line = (_b = noteStruct.line) !== null && _b !== void 0 ? _b : 0;\n    this.smooth = noteStruct.smooth || false;\n    this.ignoreTicks = noteStruct.ignoreTicks || false;\n    this.justification = TextJustification.LEFT;\n  }\n  setJustification(just) {\n    this.justification = just;\n    return this;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  getLine() {\n    return this.line;\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    const tickContext = this.checkTickContext(`Can't preformat without a TickContext.`);\n    if (this.justification === TextJustification.CENTER) {\n      this.leftDisplacedHeadPx = this.width / 2;\n    } else if (this.justification === TextJustification.RIGHT) {\n      this.leftDisplacedHeadPx = this.width;\n    }\n    this.rightDisplacedHeadPx = tickContext.getMetrics().glyphPx / 2;\n    this.preFormatted = true;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const stave = this.checkStave();\n    const tickContext = this.checkTickContext(`Can't draw without a TickContext.`);\n    this.setRendered();\n    let x = this.getAbsoluteX() + tickContext.getMetrics().glyphPx / 2;\n    const width = this.getWidth();\n    if (this.justification === TextJustification.CENTER) {\n      x -= width / 2;\n    } else if (this.justification === TextJustification.RIGHT) {\n      x -= width;\n    }\n    const y = stave.getYForLine(this.line + -3);\n    this.renderText(ctx, x, y);\n    const height = this.getHeight();\n    if (this.superscript) {\n      this.superscript.renderText(ctx, x + this.width + 2, y - height / 2.2);\n    }\n    if (this.subscript) {\n      this.subscript.renderText(ctx, x + this.width + 2, y + height / 2.2 - 1);\n    }\n  }\n}\nTextNote.Justification = TextJustification;", "map": {"version": 3, "names": ["Element", "Font", "Glyphs", "Metrics", "Note", "TextJustification", "TextNote", "CATEGORY", "GLYPHS", "segno", "tr", "ornamentTrill", "mordent", "ornamentMordent", "mordent<PERSON>pper", "ornamentShortTrill", "mord<PERSON><PERSON><PERSON><PERSON>", "f", "dynamicForte", "p", "dynamicPiano", "m", "<PERSON><PERSON><PERSON><PERSON>", "s", "dynamicSforzando", "z", "dynamicZ", "coda", "pedalOpen", "keyboardPedalPed", "pedalClose", "keyboardPedalUp", "caesuraStraight", "caesura", "caesuraCurved", "breath", "breathMarkComma", "tick", "breathMarkTick", "turn", "ornamentTurn", "turnInverted", "ornamentTurnSlash", "constructor", "noteStruct", "_a", "_b", "text", "glyph", "font", "setFont", "undefined", "getFontInfo", "smallerFontSize", "convertSizeToPointValue", "fontInfo", "size", "superscript", "setText", "setFontSize", "subscript", "line", "smooth", "ignoreTicks", "justification", "LEFT", "setJustification", "just", "setLine", "getLine", "preFormat", "preFormatted", "tickContext", "checkTickContext", "CENTER", "leftDisplacedHeadPx", "width", "RIGHT", "rightDisplacedHeadPx", "getMetrics", "glyphPx", "draw", "ctx", "checkContext", "stave", "checkStave", "setRendered", "x", "getAbsoluteX", "getWidth", "y", "getYForLine", "renderText", "height", "getHeight", "Justification"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/textnote.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Note } from './note.js';\nexport var TextJustification;\n(function (TextJustification) {\n    TextJustification[TextJustification[\"LEFT\"] = 1] = \"LEFT\";\n    TextJustification[TextJustification[\"CENTER\"] = 2] = \"CENTER\";\n    TextJustification[TextJustification[\"RIGHT\"] = 3] = \"RIGHT\";\n})(TextJustification || (TextJustification = {}));\nexport class TextNote extends Note {\n    static get CATEGORY() {\n        return \"TextNote\";\n    }\n    static get GLYPHS() {\n        return {\n            segno: Glyphs.segno,\n            tr: Glyphs.ornamentTrill,\n            mordent: Glyphs.ornamentMordent,\n            mordentUpper: Glyphs.ornamentShortTrill,\n            mordentLower: Glyphs.ornamentMordent,\n            f: Glyphs.dynamicForte,\n            p: Glyphs.dynamic<PERSON>iano,\n            m: Glyphs.dynamicMezzo,\n            s: Glyphs.dynamicSforzando,\n            z: Glyphs.dynamicZ,\n            coda: Glyphs.coda,\n            pedalOpen: Glyphs.keyboardPedalPed,\n            pedalClose: Glyphs.keyboardPedalUp,\n            caesuraStraight: Glyphs.caesura,\n            caesuraCurved: Glyphs.caesuraCurved,\n            breath: Glyphs.breathMarkComma,\n            tick: Glyphs.breathMarkTick,\n            turn: Glyphs.ornamentTurn,\n            turnInverted: Glyphs.ornamentTurnSlash,\n        };\n    }\n    constructor(noteStruct) {\n        var _a, _b;\n        super(noteStruct);\n        this.text = (_a = noteStruct.text) !== null && _a !== void 0 ? _a : '';\n        if (noteStruct.glyph) {\n            this.text += TextNote.GLYPHS[noteStruct.glyph] || noteStruct.glyph;\n        }\n        if (noteStruct.font) {\n            this.setFont(noteStruct.font);\n        }\n        else if (noteStruct.glyph === undefined) {\n            this.setFont(Metrics.getFontInfo('TextNote.text.fontSize'));\n        }\n        const smallerFontSize = Font.convertSizeToPointValue(this.fontInfo.size) * 0.769231;\n        if (noteStruct.superscript) {\n            this.superscript = new Element('TexNote.subSuper');\n            this.superscript.setText(noteStruct.superscript);\n            this.superscript.setFontSize(smallerFontSize);\n        }\n        if (noteStruct.subscript) {\n            this.subscript = new Element('TexNote.subSuper');\n            this.subscript.setText(noteStruct.subscript);\n            this.subscript.setFontSize(smallerFontSize);\n        }\n        this.line = (_b = noteStruct.line) !== null && _b !== void 0 ? _b : 0;\n        this.smooth = noteStruct.smooth || false;\n        this.ignoreTicks = noteStruct.ignoreTicks || false;\n        this.justification = TextJustification.LEFT;\n    }\n    setJustification(just) {\n        this.justification = just;\n        return this;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    getLine() {\n        return this.line;\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        const tickContext = this.checkTickContext(`Can't preformat without a TickContext.`);\n        if (this.justification === TextJustification.CENTER) {\n            this.leftDisplacedHeadPx = this.width / 2;\n        }\n        else if (this.justification === TextJustification.RIGHT) {\n            this.leftDisplacedHeadPx = this.width;\n        }\n        this.rightDisplacedHeadPx = tickContext.getMetrics().glyphPx / 2;\n        this.preFormatted = true;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const stave = this.checkStave();\n        const tickContext = this.checkTickContext(`Can't draw without a TickContext.`);\n        this.setRendered();\n        let x = this.getAbsoluteX() + tickContext.getMetrics().glyphPx / 2;\n        const width = this.getWidth();\n        if (this.justification === TextJustification.CENTER) {\n            x -= width / 2;\n        }\n        else if (this.justification === TextJustification.RIGHT) {\n            x -= width;\n        }\n        const y = stave.getYForLine(this.line + -3);\n        this.renderText(ctx, x, y);\n        const height = this.getHeight();\n        if (this.superscript) {\n            this.superscript.renderText(ctx, x + this.width + 2, y - height / 2.2);\n        }\n        if (this.subscript) {\n            this.subscript.renderText(ctx, x + this.width + 2, y + height / 2.2 - 1);\n        }\n    }\n}\nTextNote.Justification = TextJustification;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,OAAO,IAAIC,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAACA,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzDA,iBAAiB,CAACA,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC7DA,iBAAiB,CAACA,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC/D,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,OAAO,MAAMC,QAAQ,SAASF,IAAI,CAAC;EAC/B,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACA,WAAWC,MAAMA,CAAA,EAAG;IAChB,OAAO;MACHC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,EAAE,EAAER,MAAM,CAACS,aAAa;MACxBC,OAAO,EAAEV,MAAM,CAACW,eAAe;MAC/BC,YAAY,EAAEZ,MAAM,CAACa,kBAAkB;MACvCC,YAAY,EAAEd,MAAM,CAACW,eAAe;MACpCI,CAAC,EAAEf,MAAM,CAACgB,YAAY;MACtBC,CAAC,EAAEjB,MAAM,CAACkB,YAAY;MACtBC,CAAC,EAAEnB,MAAM,CAACoB,YAAY;MACtBC,CAAC,EAAErB,MAAM,CAACsB,gBAAgB;MAC1BC,CAAC,EAAEvB,MAAM,CAACwB,QAAQ;MAClBC,IAAI,EAAEzB,MAAM,CAACyB,IAAI;MACjBC,SAAS,EAAE1B,MAAM,CAAC2B,gBAAgB;MAClCC,UAAU,EAAE5B,MAAM,CAAC6B,eAAe;MAClCC,eAAe,EAAE9B,MAAM,CAAC+B,OAAO;MAC/BC,aAAa,EAAEhC,MAAM,CAACgC,aAAa;MACnCC,MAAM,EAAEjC,MAAM,CAACkC,eAAe;MAC9BC,IAAI,EAAEnC,MAAM,CAACoC,cAAc;MAC3BC,IAAI,EAAErC,MAAM,CAACsC,YAAY;MACzBC,YAAY,EAAEvC,MAAM,CAACwC;IACzB,CAAC;EACL;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIC,EAAE,EAAEC,EAAE;IACV,KAAK,CAACF,UAAU,CAAC;IACjB,IAAI,CAACG,IAAI,GAAG,CAACF,EAAE,GAAGD,UAAU,CAACG,IAAI,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IACtE,IAAID,UAAU,CAACI,KAAK,EAAE;MAClB,IAAI,CAACD,IAAI,IAAIzC,QAAQ,CAACE,MAAM,CAACoC,UAAU,CAACI,KAAK,CAAC,IAAIJ,UAAU,CAACI,KAAK;IACtE;IACA,IAAIJ,UAAU,CAACK,IAAI,EAAE;MACjB,IAAI,CAACC,OAAO,CAACN,UAAU,CAACK,IAAI,CAAC;IACjC,CAAC,MACI,IAAIL,UAAU,CAACI,KAAK,KAAKG,SAAS,EAAE;MACrC,IAAI,CAACD,OAAO,CAAC/C,OAAO,CAACiD,WAAW,CAAC,wBAAwB,CAAC,CAAC;IAC/D;IACA,MAAMC,eAAe,GAAGpD,IAAI,CAACqD,uBAAuB,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,GAAG,QAAQ;IACnF,IAAIZ,UAAU,CAACa,WAAW,EAAE;MACxB,IAAI,CAACA,WAAW,GAAG,IAAIzD,OAAO,CAAC,kBAAkB,CAAC;MAClD,IAAI,CAACyD,WAAW,CAACC,OAAO,CAACd,UAAU,CAACa,WAAW,CAAC;MAChD,IAAI,CAACA,WAAW,CAACE,WAAW,CAACN,eAAe,CAAC;IACjD;IACA,IAAIT,UAAU,CAACgB,SAAS,EAAE;MACtB,IAAI,CAACA,SAAS,GAAG,IAAI5D,OAAO,CAAC,kBAAkB,CAAC;MAChD,IAAI,CAAC4D,SAAS,CAACF,OAAO,CAACd,UAAU,CAACgB,SAAS,CAAC;MAC5C,IAAI,CAACA,SAAS,CAACD,WAAW,CAACN,eAAe,CAAC;IAC/C;IACA,IAAI,CAACQ,IAAI,GAAG,CAACf,EAAE,GAAGF,UAAU,CAACiB,IAAI,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACrE,IAAI,CAACgB,MAAM,GAAGlB,UAAU,CAACkB,MAAM,IAAI,KAAK;IACxC,IAAI,CAACC,WAAW,GAAGnB,UAAU,CAACmB,WAAW,IAAI,KAAK;IAClD,IAAI,CAACC,aAAa,GAAG3D,iBAAiB,CAAC4D,IAAI;EAC/C;EACAC,gBAAgBA,CAACC,IAAI,EAAE;IACnB,IAAI,CAACH,aAAa,GAAGG,IAAI;IACzB,OAAO,IAAI;EACf;EACAC,OAAOA,CAACP,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAQ,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACR,IAAI;EACpB;EACAS,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACC,YAAY,EACjB;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,wCAAwC,CAAC;IACnF,IAAI,IAAI,CAACT,aAAa,KAAK3D,iBAAiB,CAACqE,MAAM,EAAE;MACjD,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACC,KAAK,GAAG,CAAC;IAC7C,CAAC,MACI,IAAI,IAAI,CAACZ,aAAa,KAAK3D,iBAAiB,CAACwE,KAAK,EAAE;MACrD,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACC,KAAK;IACzC;IACA,IAAI,CAACE,oBAAoB,GAAGN,WAAW,CAACO,UAAU,CAAC,CAAC,CAACC,OAAO,GAAG,CAAC;IAChE,IAAI,CAACT,YAAY,GAAG,IAAI;EAC5B;EACAU,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMb,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,mCAAmC,CAAC;IAC9E,IAAI,CAACa,WAAW,CAAC,CAAC;IAClB,IAAIC,CAAC,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAGhB,WAAW,CAACO,UAAU,CAAC,CAAC,CAACC,OAAO,GAAG,CAAC;IAClE,MAAMJ,KAAK,GAAG,IAAI,CAACa,QAAQ,CAAC,CAAC;IAC7B,IAAI,IAAI,CAACzB,aAAa,KAAK3D,iBAAiB,CAACqE,MAAM,EAAE;MACjDa,CAAC,IAAIX,KAAK,GAAG,CAAC;IAClB,CAAC,MACI,IAAI,IAAI,CAACZ,aAAa,KAAK3D,iBAAiB,CAACwE,KAAK,EAAE;MACrDU,CAAC,IAAIX,KAAK;IACd;IACA,MAAMc,CAAC,GAAGN,KAAK,CAACO,WAAW,CAAC,IAAI,CAAC9B,IAAI,GAAG,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC+B,UAAU,CAACV,GAAG,EAAEK,CAAC,EAAEG,CAAC,CAAC;IAC1B,MAAMG,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACrC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACmC,UAAU,CAACV,GAAG,EAAEK,CAAC,GAAG,IAAI,CAACX,KAAK,GAAG,CAAC,EAAEc,CAAC,GAAGG,MAAM,GAAG,GAAG,CAAC;IAC1E;IACA,IAAI,IAAI,CAACjC,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACgC,UAAU,CAACV,GAAG,EAAEK,CAAC,GAAG,IAAI,CAACX,KAAK,GAAG,CAAC,EAAEc,CAAC,GAAGG,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;IAC5E;EACJ;AACJ;AACAvF,QAAQ,CAACyF,aAAa,GAAG1F,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}