{"ast": null, "code": "import { KeySignature } from './keysignature.js';\nimport { Note } from './note.js';\nexport class KeySigNote extends Note {\n  static get CATEGORY() {\n    return \"KeySigNote\";\n  }\n  constructor(keySpec, cancelKeySpec, alterKeySpec) {\n    super({\n      duration: 'b'\n    });\n    this.keySignature = new KeySignature(keySpec, cancelKeySpec, alterKeySpec);\n    this.ignoreTicks = true;\n  }\n  addToModifierContext(mc) {\n    return this;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    this.keySignature.setStave(this.checkStave());\n    this.setWidth(this.keySignature.getWidth());\n    return this;\n  }\n  draw() {\n    const ctx = this.checkStave().checkContext();\n    this.setRendered();\n    this.keySignature.setX(this.getAbsoluteX());\n    this.keySignature.setContext(ctx);\n    this.keySignature.drawWithStyle();\n  }\n}", "map": {"version": 3, "names": ["KeySignature", "Note", "KeySigNote", "CATEGORY", "constructor", "keySpec", "cancelKeySpec", "alterKeySpec", "duration", "keySignature", "ignoreTicks", "addToModifierContext", "mc", "preFormat", "preFormatted", "setStave", "checkStave", "<PERSON><PERSON><PERSON><PERSON>", "getWidth", "draw", "ctx", "checkContext", "setRendered", "setX", "getAbsoluteX", "setContext", "drawWithStyle"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/keysignote.js"], "sourcesContent": ["import { KeySignature } from './keysignature.js';\nimport { Note } from './note.js';\nexport class KeySigNote extends Note {\n    static get CATEGORY() {\n        return \"KeySigNote\";\n    }\n    constructor(keySpec, cancelKeySpec, alterKeySpec) {\n        super({ duration: 'b' });\n        this.keySignature = new KeySignature(keySpec, cancelKeySpec, alterKeySpec);\n        this.ignoreTicks = true;\n    }\n    addToModifierContext(mc) {\n        return this;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        this.keySignature.setStave(this.checkStave());\n        this.setWidth(this.keySignature.getWidth());\n        return this;\n    }\n    draw() {\n        const ctx = this.checkStave().checkContext();\n        this.setRendered();\n        this.keySignature.setX(this.getAbsoluteX());\n        this.keySignature.setContext(ctx);\n        this.keySignature.drawWithStyle();\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,IAAI,QAAQ,WAAW;AAChC,OAAO,MAAMC,UAAU,SAASD,IAAI,CAAC;EACjC,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACAC,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAE;IAC9C,KAAK,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,YAAY,GAAG,IAAIT,YAAY,CAACK,OAAO,EAAEC,aAAa,EAAEC,YAAY,CAAC;IAC1E,IAAI,CAACG,WAAW,GAAG,IAAI;EAC3B;EACAC,oBAAoBA,CAACC,EAAE,EAAE;IACrB,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACL,YAAY,CAACM,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACR,YAAY,CAACS,QAAQ,CAAC,CAAC,CAAC;IAC3C,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACJ,UAAU,CAAC,CAAC,CAACK,YAAY,CAAC,CAAC;IAC5C,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACb,YAAY,CAACc,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACf,YAAY,CAACgB,UAAU,CAACL,GAAG,CAAC;IACjC,IAAI,CAACX,YAAY,CAACiB,aAAa,CAAC,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}