{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Helmet } from \"react-helmet\";\nimport VexTabBlock, { updateVexTabBlock } from \"./VexTabBlock.jsx\";\nimport { gigaConvert, parseNote, toMidi, fromMidi, formatNote } from \"../ConvertFunctions.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"rgba(68, 25, 240, 0.08)\",\n      display: \"grid\",\n      gridTemplateRows: \"auto auto 1fr auto auto\"\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"2rem\",\n      background: \"white\"\n    },\n    sideButton: {\n      backgroundColor: \"white\",\n      width: \"4rem\",\n      height: \"4rem\",\n      fontSize: \"1.2rem\",\n      fontWeight: \"bold\",\n      color: \"#5c14ba\",\n      border: \"2px solid #9a62e3\",\n      borderRadius: \"1rem\",\n      cursor: \"pointer\",\n      margin: \"auto\",\n      transition: 'all 0.2s ease'\n    },\n    sideButtonActive: {\n      backgroundColor: \"#9a62e3\",\n      color: \"white\",\n      border: \"2px solid #7f3bd9\",\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\",\n      transition: 'all 0.2s ease'\n    }\n  };\n\n  // MUSIC STATES\n  const defaultSetup = \"tabstave notation=true tablature=false\";\n  const [curStep, setCurStep] = useState('A');\n  const [curAccident, setCurAccident] = useState('');\n  const [curOctave, setCurOctave] = useState(4);\n  const [curAccent, setCurAccent] = useState('');\n  const [curSlur, setCurSlur] = useState(' ');\n  const curNote = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`;\n  const [key, setKey] = useState(\"G\");\n  const [time, setTime] = useState(\"4/4\");\n  const [notes, setNotes] = useState(['A/4 ']);\n  const rawVex = [defaultSetup + ` key=${key} time=${time}`, \"notes \" + notes.join(\" \")].join(\"\\n\");\n  const [converted, setConverted] = useState(\"\");\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\n  const VEX_ACC = {\n    \"♯\": \"#\",\n    \"♮\": \"n\",\n    \"♭\": \"@\",\n    \"\": \"\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    style: {\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: 'center',\n      paddingTop: \"4rem\",\n      paddingBottom: \"4rem\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vexflow/releases/vexflow-min.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/vextab-core.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gridTemplateColumns: \"6rem 1fr 6rem\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignSelf: 'flex-start',\n                gap: '2rem',\n                padding: \"1rem 0rem\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => {\n                  setCurOctave(o => o + 1);\n                  const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\n                  curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${curAccident}/${curOctave + 1}${curAccent}${curSlur}`;\n                  updateVexTabBlock('currentNoteRef');\n                },\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                children: curOctave\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => {\n                  setCurOctave(o => o - 1);\n                  const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\n                  curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${curAccident}/${curOctave - 1}${curAccent}${curSlur}`;\n                  updateVexTabBlock('currentNoteRef');\n                },\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"grid\",\n                gridTemplateRows: \"auto 1fr\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"grid\",\n                  gridTemplateColumns: \"repeat(7, 1fr)\",\n                  padding: \"1rem\"\n                },\n                children: NOTES.map(note => /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    ...styles.sideButton,\n                    ...(curStep === note ? styles.sideButtonActive : {})\n                  },\n                  onClick: () => {\n                    setCurStep(note);\n                    const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\n                    curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${note}${curAccident}/${curOctave}${curAccent}${curSlur}`;\n                    updateVexTabBlock('currentNoteRef');\n                  },\n                  children: note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: \"1rem\"\n                },\n                children: /*#__PURE__*/_jsxDEV(VexTabBlock, {\n                  source: [` key=${key} time=${time}`, \"notes \" + curNote].join('\\n'),\n                  id: \"currentNoteRef\",\n                  editorHeight: 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignSelf: 'flex-start',\n                gap: \"2rem\",\n                padding: \"1rem 0rem\"\n              },\n              children: ACCIDENTALS.map(acc => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  ...styles.sideButton,\n                  ...(curAccident === VEX_ACC[acc] ? styles.sideButtonActive : {})\n                },\n                onClick: () => {\n                  const accident = curAccident === VEX_ACC[acc] ? '' : VEX_ACC[acc];\n                  setCurAccident(accident);\n                  const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\n                  curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${accident}/${curOctave}${curAccent}${curSlur}`;\n                  updateVexTabBlock('currentNoteRef');\n                },\n                children: acc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                const note = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`;\n                setNotes(prev => [...prev, note]);\n                const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');\n                curVexText.value += note;\n                updateVexTabBlock('inputEditorRef');\n                console.log(\"Adding to notes: \", notes);\n              },\n              children: \"Add Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log(\"Notes before Removal: \", notes);\n                setNotes(prev => prev.slice(0, -1));\n                const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');\n                curVexText.value = `${defaultSetup} \\nnotes ${notes.slice(0, -1).join(\" \")}`;\n                updateVexTabBlock('inputEditorRef');\n                console.log(\"Subtracting from notes: \", notes);\n              },\n              children: \"Remove Note\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setConverted(gigaConvert(key, notes));\n          },\n          children: \"Convert\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Input Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: rawVex,\n            id: \"inputEditorRef\",\n            editorHeight: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: \"2px solid #9a62e3\",\n          borderRadius: \"2rem\",\n          background: \"rgba(120, 25, 240, 0.06)\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          margin: \"2rem 4rem\",\n          padding: \"2rem\",\n          gap: \"2rem\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Output Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(VexTabBlock, {\n            source: converted,\n            id: \"outputRef\",\n            editorHeight: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"xxoy1GQ8AJppLI2+rBpGoYVNq18=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "<PERSON><PERSON><PERSON>", "VexTabBlock", "updateVexTabBlock", "gigaConvert", "parseNote", "<PERSON><PERSON><PERSON><PERSON>", "fromMidi", "formatNote", "jsxDEV", "_jsxDEV", "Converter", "_s", "styles", "graphicDisplay", "width", "Math", "min", "window", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "sideButton", "backgroundColor", "height", "fontSize", "fontWeight", "color", "cursor", "margin", "transition", "sideButtonActive", "boxShadow", "defaultSetup", "curStep", "setCurStep", "curAccident", "setCurAccident", "curOctave", "setCurOctave", "curAccent", "setCurAccent", "curSlur", "setCurSlur", "cur<PERSON><PERSON>", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "join", "converted", "setConverted", "NOTES", "ACCIDENTALS", "VEX_ACC", "style", "flexDirection", "alignItems", "paddingTop", "paddingBottom", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "gap", "gridTemplateColumns", "alignSelf", "onClick", "o", "curVexText", "document", "getElementById", "querySelector", "value", "map", "note", "source", "id", "<PERSON><PERSON><PERSON><PERSON>", "acc", "accident", "prev", "console", "log", "slice", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport VexTabBlock, { updateVexTabBlock } from \"./VexTabBlock.jsx\";\r\nimport { gigaConvert, parseNote, toMidi, fromMidi, formatNote } from \"../ConvertFunctions.js\";\r\n\r\nfunction Converter() {\r\n\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"rgba(68, 25, 240, 0.08)\",\r\n      display: \"grid\",\r\n      gridTemplateRows: \"auto auto 1fr auto auto\",\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"2rem\",\r\n      background: \"white\",\r\n    },\r\n    sideButton: {\r\n      backgroundColor: \"white\",\r\n      width: \"4rem\",\r\n      height: \"4rem\",\r\n      fontSize: \"1.2rem\",\r\n      fontWeight: \"bold\",\r\n      color: \"#5c14ba\",\r\n      border: \"2px solid #9a62e3\",\r\n      borderRadius: \"1rem\",\r\n      cursor: \"pointer\",\r\n      margin: \"auto\",\r\n      transition: 'all 0.2s ease',\r\n    },\r\n    sideButtonActive: {\r\n      backgroundColor: \"#9a62e3\",\r\n      color: \"white\",\r\n      border: \"2px solid #7f3bd9\",\r\n      boxShadow: \"0 4px 8px rgba(127, 59, 217, 0.5)\",\r\n      transition: 'all 0.2s ease'\r\n    },\r\n  };\r\n\r\n  // MUSIC STATES\r\n  const defaultSetup = \"tabstave notation=true tablature=false\";\r\n\r\n  \r\n  const [curStep, setCurStep]         =  useState('A'); \r\n  const [curAccident, setCurAccident] =  useState('');  \r\n  const [curOctave, setCurOctave]     =  useState(4);   \r\n  const [curAccent, setCurAccent]     =  useState('');  \r\n  const [curSlur, setCurSlur]         =  useState(' '); \r\n  const curNote = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`\r\n\r\n\r\n  const [key, setKey] = useState(\"G\");\r\n  const [time, setTime] = useState(\"4/4\");\r\n  const [notes, setNotes] = useState(['A/4 ']);\r\n\r\n  const rawVex = [\r\n    defaultSetup + ` key=${key} time=${time}`,\r\n    \"notes \" + notes.join(\" \"),\r\n  ].join(\"\\n\");\r\n\r\n  const [converted, setConverted] = useState(\"\");\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = [\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\"];\r\n  const ACCIDENTALS = [\"♯\", \"♮\", \"♭\"];\r\n  const VEX_ACC = { \"♯\": \"#\", \"♮\": \"n\", \"♭\": \"@\", \"\": \"\" };\r\n\r\n  return (\r\n    <main\r\n      style={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        alignItems: 'center',\r\n        paddingTop: \"4rem\",\r\n        paddingBottom: \"4rem\",\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vexflow/releases/vexflow-min.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/vextab-core.prod.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n          }}\r\n        >\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: \"grid\",\r\n                gridTemplateColumns: \"6rem 1fr 6rem\",\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignSelf: 'flex-start',\r\n                  gap: '2rem',\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => {\r\n                    setCurOctave((o) => o + 1)\r\n                    const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\r\n                    curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${curAccident}/${curOctave+1}${curAccent}${curSlur}`;\r\n                    updateVexTabBlock('currentNoteRef');\r\n                  }}\r\n                >\r\n                  ↑\r\n                </button>\r\n                <button style={styles.sideButton}>{curOctave}</button>\r\n                <button\r\n                  style={styles.sideButton}\r\n                  onClick={() => {\r\n                    setCurOctave((o) => o - 1)\r\n                    const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\r\n                    curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${curAccident}/${curOctave-1}${curAccent}${curSlur}`;\r\n                    updateVexTabBlock('currentNoteRef');\r\n                  }}\r\n                >\r\n                  ↓\r\n                </button>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"grid\",\r\n                  gridTemplateRows: \"auto 1fr\",\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: \"grid\",\r\n                    gridTemplateColumns: \"repeat(7, 1fr)\",\r\n                    padding: \"1rem\",\r\n                  }}\r\n                >\r\n                  {NOTES.map((note) => (\r\n                    <button\r\n                      style={{\r\n                        ...styles.sideButton,\r\n                        ...(curStep === note ? styles.sideButtonActive : {}),\r\n                      }}\r\n                      onClick={() => {\r\n                        setCurStep(note);\r\n                        const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\r\n                        curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${note}${curAccident}/${curOctave}${curAccent}${curSlur}`;\r\n                        updateVexTabBlock('currentNoteRef');\r\n                      }}\r\n                    >\r\n                      {note}\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n\r\n                <div style={{ padding: \"1rem\" }}>\r\n                  <VexTabBlock source={[` key=${key} time=${time}`,\r\n    \"notes \" + curNote].join('\\n')} id={\"currentNoteRef\"} editorHeight={1}/>\r\n                </div>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignSelf: 'flex-start',\r\n                  gap: \"2rem\",\r\n                  padding: \"1rem 0rem\",\r\n                }}\r\n              >\r\n                {ACCIDENTALS.map((acc) => (\r\n                  <button\r\n                    style={{\r\n                      ...styles.sideButton,\r\n                      ...(curAccident === VEX_ACC[acc] ? styles.sideButtonActive : {}),\r\n                    }}\r\n                    onClick={() => {\r\n                      const accident = curAccident === VEX_ACC[acc] ? '' : VEX_ACC[acc];\r\n                      setCurAccident(accident);\r\n                      const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');\r\n                      curVexText.value = defaultSetup + ` key=${key} time=${time} \\nnotes ` + `${curStep}${accident}/${curOctave}${curAccent}${curSlur}`;\r\n                      updateVexTabBlock('currentNoteRef');\r\n                    }}\r\n                  >\r\n                    {acc}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            {/* ----- ADD & REMOVE NOTES ----- */}\r\n            <div>\r\n              {/* --- Add --- */}\r\n              <button\r\n                onClick={() => {\r\n                  const note = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`;\r\n                  setNotes((prev) => [...prev, note]);\r\n                  const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');\r\n                  curVexText.value += note;\r\n                  updateVexTabBlock('inputEditorRef');\r\n                  console.log(\"Adding to notes: \", notes);\r\n                }}\r\n              >\r\n                Add Note\r\n              </button>\r\n              {/* --- Remove --- */}\r\n              <button\r\n                onClick={() => {\r\n                  console.log(\"Notes before Removal: \", notes);\r\n                  setNotes((prev) => prev.slice(0, -1));\r\n                  const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');\r\n                  curVexText.value = `${defaultSetup} \\nnotes ${notes.slice(0, -1).join(\" \")}`;\r\n                  updateVexTabBlock('inputEditorRef');\r\n                  console.log(\"Subtracting from notes: \", notes);\r\n                }}\r\n              >\r\n                Remove Note\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          <button\r\n            onClick={() => {\r\n              setConverted(gigaConvert(key, notes))\r\n            }}\r\n          >\r\n            Convert\r\n          </button>\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Input Editor</h4>\r\n            <VexTabBlock source={rawVex} id={\"inputEditorRef\"} editorHeight={100} />\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section\r\n          style={{\r\n            border: \"2px solid #9a62e3\",\r\n            borderRadius: \"2rem\",\r\n            background: \"rgba(120, 25, 240, 0.06)\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            margin: \"2rem 4rem\",\r\n            padding: \"2rem\",\r\n            gap: \"2rem\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <h3>Output</h3>\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Output Editor</h4>\r\n            <VexTabBlock source={converted} id={\"outputRef\"} editorHeight={100}/>\r\n          </div>\r\n        </section>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n\r\nexport default Converter;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAOC,WAAW,IAAIC,iBAAiB,QAAQ,mBAAmB;AAClE,SAASC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9F,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAEnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZV,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,UAAU,EAAE;MACVC,eAAe,EAAE,OAAO;MACxBZ,KAAK,EAAE,MAAM;MACba,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBW,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE;IACd,CAAC;IACDC,gBAAgB,EAAE;MAChBR,eAAe,EAAE,SAAS;MAC1BI,KAAK,EAAE,OAAO;MACdX,MAAM,EAAE,mBAAmB;MAC3BgB,SAAS,EAAE,mCAAmC;MAC9CF,UAAU,EAAE;IACd;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG,wCAAwC;EAG7D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAYxC,QAAQ,CAAC,GAAG,CAAC;EACpD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAI1C,QAAQ,CAAC,EAAE,CAAC;EACnD,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAQ5C,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAQ9C,QAAQ,CAAC,EAAE,CAAC;EACnD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAYhD,QAAQ,CAAC,GAAG,CAAC;EACpD,MAAMiD,OAAO,GAAG,GAAGV,OAAO,GAAGE,WAAW,IAAIE,SAAS,GAAGE,SAAS,GAAGE,OAAO,EAAE;EAG7E,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAGnD,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACoD,IAAI,EAAEC,OAAO,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;EAE5C,MAAMwD,MAAM,GAAG,CACblB,YAAY,GAAG,QAAQY,GAAG,SAASE,IAAI,EAAE,EACzC,QAAQ,GAAGE,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC,CAC3B,CAACA,IAAI,CAAC,IAAI,CAAC;EAEZ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM4D,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,MAAMC,OAAO,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,EAAE,EAAE;EAAG,CAAC;EAExD,oBACEnD,OAAA;IACEoD,KAAK,EAAE;MACLvC,OAAO,EAAE,MAAM;MACfwC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACjB,CAAE;IAAAC,QAAA,gBAEFzD,OAAA,CAACT,MAAM;MAAAkE,QAAA,gBACLzD,OAAA;QAAQ0D,GAAG,EAAC;MAAmD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACzE9D,OAAA;QAAQ0D,GAAG,EAAC;MAAuD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC7E9D,OAAA;QAAQ0D,GAAG,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGT9D,OAAA;MAAAyD,QAAA,eACEzD,OAAA;QACEoD,KAAK,EAAE;UACL1C,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfwC,aAAa,EAAE,QAAQ;UACvB9B,MAAM,EAAE,WAAW;UACnBwC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE;QACP,CAAE;QAAAP,QAAA,gBAEFzD,OAAA;UAAAyD,QAAA,EAAI;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGd9D,OAAA;UAAKoD,KAAK,EAAEjD,MAAM,CAACC,cAAe;UAAAqD,QAAA,gBAChCzD,OAAA;YACEoD,KAAK,EAAE;cACLvC,OAAO,EAAE,MAAM;cACfoD,mBAAmB,EAAE;YACvB,CAAE;YAAAR,QAAA,gBAEFzD,OAAA;cACEoD,KAAK,EAAE;gBACLvC,OAAO,EAAE,MAAM;gBACfwC,aAAa,EAAE,QAAQ;gBACvBa,SAAS,EAAE,YAAY;gBACvBF,GAAG,EAAE,MAAM;gBACXD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,gBAEFzD,OAAA;gBACEoD,KAAK,EAAEjD,MAAM,CAACa,UAAW;gBACzBmD,OAAO,EAAEA,CAAA,KAAM;kBACblC,YAAY,CAAEmC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;kBAC1B,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;kBAC7FH,UAAU,CAACI,KAAK,GAAG9C,YAAY,GAAG,QAAQY,GAAG,SAASE,IAAI,WAAW,GAAG,GAAGb,OAAO,GAAGE,WAAW,IAAIE,SAAS,GAAC,CAAC,GAAGE,SAAS,GAAGE,OAAO,EAAE;kBACvI3C,iBAAiB,CAAC,gBAAgB,CAAC;gBACrC,CAAE;gBAAAgE,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9D,OAAA;gBAAQoD,KAAK,EAAEjD,MAAM,CAACa,UAAW;gBAAAyC,QAAA,EAAEzB;cAAS;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACtD9D,OAAA;gBACEoD,KAAK,EAAEjD,MAAM,CAACa,UAAW;gBACzBmD,OAAO,EAAEA,CAAA,KAAM;kBACblC,YAAY,CAAEmC,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;kBAC1B,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;kBAC7FH,UAAU,CAACI,KAAK,GAAG9C,YAAY,GAAG,QAAQY,GAAG,SAASE,IAAI,WAAW,GAAG,GAAGb,OAAO,GAAGE,WAAW,IAAIE,SAAS,GAAC,CAAC,GAAGE,SAAS,GAAGE,OAAO,EAAE;kBACvI3C,iBAAiB,CAAC,gBAAgB,CAAC;gBACrC,CAAE;gBAAAgE,QAAA,EACH;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9D,OAAA;cACEoD,KAAK,EAAE;gBACLvC,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAA2C,QAAA,gBAEFzD,OAAA;gBACEoD,KAAK,EAAE;kBACLvC,OAAO,EAAE,MAAM;kBACfoD,mBAAmB,EAAE,gBAAgB;kBACrCF,OAAO,EAAE;gBACX,CAAE;gBAAAN,QAAA,EAEDR,KAAK,CAACyB,GAAG,CAAEC,IAAI,iBACd3E,OAAA;kBACEoD,KAAK,EAAE;oBACL,GAAGjD,MAAM,CAACa,UAAU;oBACpB,IAAIY,OAAO,KAAK+C,IAAI,GAAGxE,MAAM,CAACsB,gBAAgB,GAAG,CAAC,CAAC;kBACrD,CAAE;kBACF0C,OAAO,EAAEA,CAAA,KAAM;oBACbtC,UAAU,CAAC8C,IAAI,CAAC;oBAChB,MAAMN,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;oBAC7FH,UAAU,CAACI,KAAK,GAAG9C,YAAY,GAAG,QAAQY,GAAG,SAASE,IAAI,WAAW,GAAG,GAAGkC,IAAI,GAAG7C,WAAW,IAAIE,SAAS,GAAGE,SAAS,GAAGE,OAAO,EAAE;oBAClI3C,iBAAiB,CAAC,gBAAgB,CAAC;kBACrC,CAAE;kBAAAgE,QAAA,EAEDkB;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9D,OAAA;gBAAKoD,KAAK,EAAE;kBAAEW,OAAO,EAAE;gBAAO,CAAE;gBAAAN,QAAA,eAC9BzD,OAAA,CAACR,WAAW;kBAACoF,MAAM,EAAE,CAAC,QAAQrC,GAAG,SAASE,IAAI,EAAE,EAC9D,QAAQ,GAAGH,OAAO,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAE;kBAAC+B,EAAE,EAAE,gBAAiB;kBAACC,YAAY,EAAE;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9D,OAAA;cACEoD,KAAK,EAAE;gBACLvC,OAAO,EAAE,MAAM;gBACfwC,aAAa,EAAE,QAAQ;gBACvBa,SAAS,EAAE,YAAY;gBACvBF,GAAG,EAAE,MAAM;gBACXD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAEDP,WAAW,CAACwB,GAAG,CAAEK,GAAG,iBACnB/E,OAAA;gBACEoD,KAAK,EAAE;kBACL,GAAGjD,MAAM,CAACa,UAAU;kBACpB,IAAIc,WAAW,KAAKqB,OAAO,CAAC4B,GAAG,CAAC,GAAG5E,MAAM,CAACsB,gBAAgB,GAAG,CAAC,CAAC;gBACjE,CAAE;gBACF0C,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAMa,QAAQ,GAAGlD,WAAW,KAAKqB,OAAO,CAAC4B,GAAG,CAAC,GAAG,EAAE,GAAG5B,OAAO,CAAC4B,GAAG,CAAC;kBACjEhD,cAAc,CAACiD,QAAQ,CAAC;kBACxB,MAAMX,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;kBAC7FH,UAAU,CAACI,KAAK,GAAG9C,YAAY,GAAG,QAAQY,GAAG,SAASE,IAAI,WAAW,GAAG,GAAGb,OAAO,GAAGoD,QAAQ,IAAIhD,SAAS,GAAGE,SAAS,GAAGE,OAAO,EAAE;kBAClI3C,iBAAiB,CAAC,gBAAgB,CAAC;gBACrC,CAAE;gBAAAgE,QAAA,EAEDsB;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9D,OAAA;YAAAyD,QAAA,eACEzD,OAAA;cAAAyD,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAGN9D,OAAA;YAAAyD,QAAA,gBAEEzD,OAAA;cACEmE,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMQ,IAAI,GAAG,GAAG/C,OAAO,GAAGE,WAAW,IAAIE,SAAS,GAAGE,SAAS,GAAGE,OAAO,EAAE;gBAC1EQ,QAAQ,CAAEqC,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEN,IAAI,CAAC,CAAC;gBACnC,MAAMN,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;gBAC7FH,UAAU,CAACI,KAAK,IAAIE,IAAI;gBACxBlF,iBAAiB,CAAC,gBAAgB,CAAC;gBACnCyF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExC,KAAK,CAAC;cACzC,CAAE;cAAAc,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET9D,OAAA;cACEmE,OAAO,EAAEA,CAAA,KAAM;gBACbe,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAExC,KAAK,CAAC;gBAC5CC,QAAQ,CAAEqC,IAAI,IAAKA,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMf,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,aAAa,CAAC,iBAAiB,CAAC;gBAC7FH,UAAU,CAACI,KAAK,GAAG,GAAG9C,YAAY,YAAYgB,KAAK,CAACyC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACtC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC5ErD,iBAAiB,CAAC,gBAAgB,CAAC;gBACnCyF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAExC,KAAK,CAAC;cAChD,CAAE;cAAAc,QAAA,EACH;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9D,OAAA;UACEmE,OAAO,EAAEA,CAAA,KAAM;YACbnB,YAAY,CAACtD,WAAW,CAAC6C,GAAG,EAAEI,KAAK,CAAC,CAAC;UACvC,CAAE;UAAAc,QAAA,EACH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAGT9D,OAAA;UAAKoD,KAAK,EAAEjD,MAAM,CAACY,YAAa;UAAA0C,QAAA,gBAC9BzD,OAAA;YAAAyD,QAAA,EAAI;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5B9D,OAAA,CAACR,WAAW;YAACoF,MAAM,EAAE/B,MAAO;YAACgC,EAAE,EAAE,gBAAiB;YAACC,YAAY,EAAE;UAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGN9D,OAAA;MAAAyD,QAAA,eACEzD,OAAA;QACEoD,KAAK,EAAE;UACL1C,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfwC,aAAa,EAAE,QAAQ;UACvB9B,MAAM,EAAE,WAAW;UACnBwC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXV,UAAU,EAAE;QACd,CAAE;QAAAG,QAAA,gBAEFzD,OAAA;UAAAyD,QAAA,EAAI;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACf9D,OAAA;UAAKoD,KAAK,EAAEjD,MAAM,CAACY,YAAa;UAAA0C,QAAA,gBAC9BzD,OAAA;YAAAyD,QAAA,EAAI;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7B9D,OAAA,CAACR,WAAW;YAACoF,MAAM,EAAE7B,SAAU;YAAC8B,EAAE,EAAE,WAAY;YAACC,YAAY,EAAE;UAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX;AAAC5D,EAAA,CA7RQD,SAAS;AAAAoF,EAAA,GAATpF,SAAS;AA+RlB,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}