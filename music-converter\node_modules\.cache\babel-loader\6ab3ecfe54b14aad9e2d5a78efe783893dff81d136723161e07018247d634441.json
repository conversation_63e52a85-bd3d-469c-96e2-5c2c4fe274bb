{"ast": null, "code": "import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Renderer } from './renderer.js';\nimport { Tables } from './tables.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (TextBracket.DEBUG) log('VexFlow.TextBracket', args);\n}\nexport var TextBracketPosition;\n(function (TextBracketPosition) {\n  TextBracketPosition[TextBracketPosition[\"TOP\"] = 1] = \"TOP\";\n  TextBracketPosition[TextBracketPosition[\"BOTTOM\"] = -1] = \"BOTTOM\";\n})(TextBracketPosition || (TextBracketPosition = {}));\nexport class TextBracket extends Element {\n  static get CATEGORY() {\n    return \"TextBracket\";\n  }\n  static get Position() {\n    return TextBracketPosition;\n  }\n  static get PositionString() {\n    return {\n      top: TextBracketPosition.TOP,\n      bottom: TextBracketPosition.BOTTOM\n    };\n  }\n  constructor({\n    start,\n    stop,\n    text = '',\n    superscript = '',\n    position = TextBracketPosition.TOP\n  }) {\n    super();\n    this.start = start;\n    this.stop = stop;\n    this.textElement = new Element('TextBracket');\n    this.textElement.setText(text);\n    this.superscriptElement = new Element('TextBracket');\n    this.superscriptElement.setText(superscript);\n    const smallerFontSize = Font.scaleSize(this.fontInfo.size, 0.714286);\n    this.superscriptElement.setFontSize(smallerFontSize);\n    this.position = typeof position === 'string' ? TextBracket.PositionString[position] : position;\n    this.line = 1;\n    this.renderOptions = {\n      dashed: true,\n      dash: [5],\n      color: 'black',\n      lineWidth: 1,\n      showBracket: true,\n      bracketHeight: 8,\n      underlineSuperscript: true\n    };\n  }\n  applyStyle(ctx) {\n    this.textElement.setFont(this.fontInfo);\n    const {\n      family,\n      size,\n      weight,\n      style\n    } = this.fontInfo;\n    const smallerFontSize = Font.scaleSize(size, 0.714286);\n    this.superscriptElement.setFont(family, smallerFontSize, weight, style);\n    const options = this.renderOptions;\n    ctx.setStrokeStyle(options.color);\n    ctx.setFillStyle(options.color);\n    ctx.setLineWidth(options.lineWidth);\n    return this;\n  }\n  setDashed(dashed, dash) {\n    this.renderOptions.dashed = dashed;\n    if (dash) this.renderOptions.dash = dash;\n    return this;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    let y = 0;\n    switch (this.position) {\n      case TextBracketPosition.TOP:\n        y = this.start.checkStave().getYForTopText(this.line);\n        break;\n      case TextBracketPosition.BOTTOM:\n        y = this.start.checkStave().getYForBottomText(this.line + Tables.TEXT_HEIGHT_OFFSET_HACK);\n        break;\n      default:\n        throw new RuntimeError('InvalidPosition', `The position ${this.position} is invalid.`);\n    }\n    const start = {\n      x: this.start.getAbsoluteX(),\n      y\n    };\n    const stop = {\n      x: this.stop.getAbsoluteX(),\n      y\n    };\n    L('Rendering TextBracket: start:', start, 'stop:', stop, 'y:', y);\n    const bracketHeight = this.renderOptions.bracketHeight * this.position;\n    this.textElement.renderText(ctx, start.x, start.y);\n    const mainWidth = this.textElement.getWidth();\n    const mainHeight = this.textElement.getHeight();\n    const superY = start.y - mainHeight / 2.5;\n    this.superscriptElement.renderText(ctx, start.x + mainWidth + 1, superY);\n    const superWidth = this.superscriptElement.getWidth();\n    const superHeight = this.superscriptElement.getHeight();\n    let startX = start.x;\n    let lineY = superY;\n    const endX = stop.x + this.stop.getGlyphWidth();\n    if (this.position === TextBracketPosition.TOP) {\n      startX += mainWidth + superWidth + 5;\n      lineY -= superHeight / 2.7;\n    } else if (this.position === TextBracketPosition.BOTTOM) {\n      lineY += superHeight / 2.7;\n      startX += mainWidth + 2;\n      if (!this.renderOptions.underlineSuperscript) {\n        startX += superWidth;\n      }\n    }\n    if (this.renderOptions.dashed) {\n      Renderer.drawDashedLine(ctx, startX, lineY, endX, lineY, this.renderOptions.dash);\n      if (this.renderOptions.showBracket) {\n        Renderer.drawDashedLine(ctx, endX, lineY + 1 * this.position, endX, lineY + bracketHeight, this.renderOptions.dash);\n      }\n    } else {\n      ctx.beginPath();\n      ctx.moveTo(startX, lineY);\n      ctx.lineTo(endX, lineY);\n      if (this.renderOptions.showBracket) {\n        ctx.lineTo(endX, lineY + bracketHeight);\n      }\n      ctx.stroke();\n      ctx.closePath();\n    }\n  }\n}\nTextBracket.DEBUG = false;", "map": {"version": 3, "names": ["Element", "Font", "<PERSON><PERSON><PERSON>", "Tables", "log", "RuntimeError", "L", "args", "TextBracket", "DEBUG", "TextBracketPosition", "CATEGORY", "Position", "PositionString", "top", "TOP", "bottom", "BOTTOM", "constructor", "start", "stop", "text", "superscript", "position", "textElement", "setText", "superscriptElement", "smallerFontSize", "scaleSize", "fontInfo", "size", "setFontSize", "line", "renderOptions", "dashed", "dash", "color", "lineWidth", "showBracket", "bracketHeight", "underlineSuperscript", "applyStyle", "ctx", "setFont", "family", "weight", "style", "options", "setStrokeStyle", "setFillStyle", "setLineWidth", "setDashed", "setLine", "draw", "checkContext", "setRendered", "y", "checkStave", "getYForTopText", "getYForBottomText", "TEXT_HEIGHT_OFFSET_HACK", "x", "getAbsoluteX", "renderText", "mainWidth", "getWidth", "mainHeight", "getHeight", "superY", "superWidth", "superHeight", "startX", "lineY", "endX", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drawDashedLine", "beginPath", "moveTo", "lineTo", "stroke", "closePath"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/textbracket.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Renderer } from './renderer.js';\nimport { Tables } from './tables.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (TextBracket.DEBUG)\n        log('VexFlow.TextBracket', args);\n}\nexport var TextBracketPosition;\n(function (TextBracketPosition) {\n    TextBracketPosition[TextBracketPosition[\"TOP\"] = 1] = \"TOP\";\n    TextBracketPosition[TextBracketPosition[\"BOTTOM\"] = -1] = \"BOTTOM\";\n})(TextBracketPosition || (TextBracketPosition = {}));\nexport class TextBracket extends Element {\n    static get CATEGORY() {\n        return \"TextBracket\";\n    }\n    static get Position() {\n        return TextBracketPosition;\n    }\n    static get PositionString() {\n        return {\n            top: TextBracketPosition.TOP,\n            bottom: TextBracketPosition.BOTTOM,\n        };\n    }\n    constructor({ start, stop, text = '', superscript = '', position = TextBracketPosition.TOP }) {\n        super();\n        this.start = start;\n        this.stop = stop;\n        this.textElement = new Element('TextBracket');\n        this.textElement.setText(text);\n        this.superscriptElement = new Element('TextBracket');\n        this.superscriptElement.setText(superscript);\n        const smallerFontSize = Font.scaleSize(this.fontInfo.size, 0.714286);\n        this.superscriptElement.setFontSize(smallerFontSize);\n        this.position = typeof position === 'string' ? TextBracket.PositionString[position] : position;\n        this.line = 1;\n        this.renderOptions = {\n            dashed: true,\n            dash: [5],\n            color: 'black',\n            lineWidth: 1,\n            showBracket: true,\n            bracketHeight: 8,\n            underlineSuperscript: true,\n        };\n    }\n    applyStyle(ctx) {\n        this.textElement.setFont(this.fontInfo);\n        const { family, size, weight, style } = this.fontInfo;\n        const smallerFontSize = Font.scaleSize(size, 0.714286);\n        this.superscriptElement.setFont(family, smallerFontSize, weight, style);\n        const options = this.renderOptions;\n        ctx.setStrokeStyle(options.color);\n        ctx.setFillStyle(options.color);\n        ctx.setLineWidth(options.lineWidth);\n        return this;\n    }\n    setDashed(dashed, dash) {\n        this.renderOptions.dashed = dashed;\n        if (dash)\n            this.renderOptions.dash = dash;\n        return this;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        let y = 0;\n        switch (this.position) {\n            case TextBracketPosition.TOP:\n                y = this.start.checkStave().getYForTopText(this.line);\n                break;\n            case TextBracketPosition.BOTTOM:\n                y = this.start.checkStave().getYForBottomText(this.line + Tables.TEXT_HEIGHT_OFFSET_HACK);\n                break;\n            default:\n                throw new RuntimeError('InvalidPosition', `The position ${this.position} is invalid.`);\n        }\n        const start = { x: this.start.getAbsoluteX(), y };\n        const stop = { x: this.stop.getAbsoluteX(), y };\n        L('Rendering TextBracket: start:', start, 'stop:', stop, 'y:', y);\n        const bracketHeight = this.renderOptions.bracketHeight * this.position;\n        this.textElement.renderText(ctx, start.x, start.y);\n        const mainWidth = this.textElement.getWidth();\n        const mainHeight = this.textElement.getHeight();\n        const superY = start.y - mainHeight / 2.5;\n        this.superscriptElement.renderText(ctx, start.x + mainWidth + 1, superY);\n        const superWidth = this.superscriptElement.getWidth();\n        const superHeight = this.superscriptElement.getHeight();\n        let startX = start.x;\n        let lineY = superY;\n        const endX = stop.x + this.stop.getGlyphWidth();\n        if (this.position === TextBracketPosition.TOP) {\n            startX += mainWidth + superWidth + 5;\n            lineY -= superHeight / 2.7;\n        }\n        else if (this.position === TextBracketPosition.BOTTOM) {\n            lineY += superHeight / 2.7;\n            startX += mainWidth + 2;\n            if (!this.renderOptions.underlineSuperscript) {\n                startX += superWidth;\n            }\n        }\n        if (this.renderOptions.dashed) {\n            Renderer.drawDashedLine(ctx, startX, lineY, endX, lineY, this.renderOptions.dash);\n            if (this.renderOptions.showBracket) {\n                Renderer.drawDashedLine(ctx, endX, lineY + 1 * this.position, endX, lineY + bracketHeight, this.renderOptions.dash);\n            }\n        }\n        else {\n            ctx.beginPath();\n            ctx.moveTo(startX, lineY);\n            ctx.lineTo(endX, lineY);\n            if (this.renderOptions.showBracket) {\n                ctx.lineTo(endX, lineY + bracketHeight);\n            }\n            ctx.stroke();\n            ctx.closePath();\n        }\n    }\n}\nTextBracket.DEBUG = false;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,WAAW,CAACC,KAAK,EACjBL,GAAG,CAAC,qBAAqB,EAAEG,IAAI,CAAC;AACxC;AACA,OAAO,IAAIG,mBAAmB;AAC9B,CAAC,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAACA,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC3DA,mBAAmB,CAACA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ;AACtE,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,OAAO,MAAMF,WAAW,SAASR,OAAO,CAAC;EACrC,WAAWW,QAAQA,CAAA,EAAG;IAClB,OAAO,aAAa;EACxB;EACA,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAOF,mBAAmB;EAC9B;EACA,WAAWG,cAAcA,CAAA,EAAG;IACxB,OAAO;MACHC,GAAG,EAAEJ,mBAAmB,CAACK,GAAG;MAC5BC,MAAM,EAAEN,mBAAmB,CAACO;IAChC,CAAC;EACL;EACAC,WAAWA,CAAC;IAAEC,KAAK;IAAEC,IAAI;IAAEC,IAAI,GAAG,EAAE;IAAEC,WAAW,GAAG,EAAE;IAAEC,QAAQ,GAAGb,mBAAmB,CAACK;EAAI,CAAC,EAAE;IAC1F,KAAK,CAAC,CAAC;IACP,IAAI,CAACI,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,WAAW,GAAG,IAAIxB,OAAO,CAAC,aAAa,CAAC;IAC7C,IAAI,CAACwB,WAAW,CAACC,OAAO,CAACJ,IAAI,CAAC;IAC9B,IAAI,CAACK,kBAAkB,GAAG,IAAI1B,OAAO,CAAC,aAAa,CAAC;IACpD,IAAI,CAAC0B,kBAAkB,CAACD,OAAO,CAACH,WAAW,CAAC;IAC5C,MAAMK,eAAe,GAAG1B,IAAI,CAAC2B,SAAS,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE,QAAQ,CAAC;IACpE,IAAI,CAACJ,kBAAkB,CAACK,WAAW,CAACJ,eAAe,CAAC;IACpD,IAAI,CAACJ,QAAQ,GAAG,OAAOA,QAAQ,KAAK,QAAQ,GAAGf,WAAW,CAACK,cAAc,CAACU,QAAQ,CAAC,GAAGA,QAAQ;IAC9F,IAAI,CAACS,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,aAAa,GAAG;MACjBC,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,CAAC,CAAC,CAAC;MACTC,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,CAAC;MAChBC,oBAAoB,EAAE;IAC1B,CAAC;EACL;EACAC,UAAUA,CAACC,GAAG,EAAE;IACZ,IAAI,CAAClB,WAAW,CAACmB,OAAO,CAAC,IAAI,CAACd,QAAQ,CAAC;IACvC,MAAM;MAAEe,MAAM;MAAEd,IAAI;MAAEe,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI,CAACjB,QAAQ;IACrD,MAAMF,eAAe,GAAG1B,IAAI,CAAC2B,SAAS,CAACE,IAAI,EAAE,QAAQ,CAAC;IACtD,IAAI,CAACJ,kBAAkB,CAACiB,OAAO,CAACC,MAAM,EAAEjB,eAAe,EAAEkB,MAAM,EAAEC,KAAK,CAAC;IACvE,MAAMC,OAAO,GAAG,IAAI,CAACd,aAAa;IAClCS,GAAG,CAACM,cAAc,CAACD,OAAO,CAACX,KAAK,CAAC;IACjCM,GAAG,CAACO,YAAY,CAACF,OAAO,CAACX,KAAK,CAAC;IAC/BM,GAAG,CAACQ,YAAY,CAACH,OAAO,CAACV,SAAS,CAAC;IACnC,OAAO,IAAI;EACf;EACAc,SAASA,CAACjB,MAAM,EAAEC,IAAI,EAAE;IACpB,IAAI,CAACF,aAAa,CAACC,MAAM,GAAGA,MAAM;IAClC,IAAIC,IAAI,EACJ,IAAI,CAACF,aAAa,CAACE,IAAI,GAAGA,IAAI;IAClC,OAAO,IAAI;EACf;EACAiB,OAAOA,CAACpB,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAqB,IAAIA,CAAA,EAAG;IACH,MAAMX,GAAG,GAAG,IAAI,CAACY,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIC,CAAC,GAAG,CAAC;IACT,QAAQ,IAAI,CAACjC,QAAQ;MACjB,KAAKb,mBAAmB,CAACK,GAAG;QACxByC,CAAC,GAAG,IAAI,CAACrC,KAAK,CAACsC,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC1B,IAAI,CAAC;QACrD;MACJ,KAAKtB,mBAAmB,CAACO,MAAM;QAC3BuC,CAAC,GAAG,IAAI,CAACrC,KAAK,CAACsC,UAAU,CAAC,CAAC,CAACE,iBAAiB,CAAC,IAAI,CAAC3B,IAAI,GAAG7B,MAAM,CAACyD,uBAAuB,CAAC;QACzF;MACJ;QACI,MAAM,IAAIvD,YAAY,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAACkB,QAAQ,cAAc,CAAC;IAC9F;IACA,MAAMJ,KAAK,GAAG;MAAE0C,CAAC,EAAE,IAAI,CAAC1C,KAAK,CAAC2C,YAAY,CAAC,CAAC;MAAEN;IAAE,CAAC;IACjD,MAAMpC,IAAI,GAAG;MAAEyC,CAAC,EAAE,IAAI,CAACzC,IAAI,CAAC0C,YAAY,CAAC,CAAC;MAAEN;IAAE,CAAC;IAC/ClD,CAAC,CAAC,+BAA+B,EAAEa,KAAK,EAAE,OAAO,EAAEC,IAAI,EAAE,IAAI,EAAEoC,CAAC,CAAC;IACjE,MAAMjB,aAAa,GAAG,IAAI,CAACN,aAAa,CAACM,aAAa,GAAG,IAAI,CAAChB,QAAQ;IACtE,IAAI,CAACC,WAAW,CAACuC,UAAU,CAACrB,GAAG,EAAEvB,KAAK,CAAC0C,CAAC,EAAE1C,KAAK,CAACqC,CAAC,CAAC;IAClD,MAAMQ,SAAS,GAAG,IAAI,CAACxC,WAAW,CAACyC,QAAQ,CAAC,CAAC;IAC7C,MAAMC,UAAU,GAAG,IAAI,CAAC1C,WAAW,CAAC2C,SAAS,CAAC,CAAC;IAC/C,MAAMC,MAAM,GAAGjD,KAAK,CAACqC,CAAC,GAAGU,UAAU,GAAG,GAAG;IACzC,IAAI,CAACxC,kBAAkB,CAACqC,UAAU,CAACrB,GAAG,EAAEvB,KAAK,CAAC0C,CAAC,GAAGG,SAAS,GAAG,CAAC,EAAEI,MAAM,CAAC;IACxE,MAAMC,UAAU,GAAG,IAAI,CAAC3C,kBAAkB,CAACuC,QAAQ,CAAC,CAAC;IACrD,MAAMK,WAAW,GAAG,IAAI,CAAC5C,kBAAkB,CAACyC,SAAS,CAAC,CAAC;IACvD,IAAII,MAAM,GAAGpD,KAAK,CAAC0C,CAAC;IACpB,IAAIW,KAAK,GAAGJ,MAAM;IAClB,MAAMK,IAAI,GAAGrD,IAAI,CAACyC,CAAC,GAAG,IAAI,CAACzC,IAAI,CAACsD,aAAa,CAAC,CAAC;IAC/C,IAAI,IAAI,CAACnD,QAAQ,KAAKb,mBAAmB,CAACK,GAAG,EAAE;MAC3CwD,MAAM,IAAIP,SAAS,GAAGK,UAAU,GAAG,CAAC;MACpCG,KAAK,IAAIF,WAAW,GAAG,GAAG;IAC9B,CAAC,MACI,IAAI,IAAI,CAAC/C,QAAQ,KAAKb,mBAAmB,CAACO,MAAM,EAAE;MACnDuD,KAAK,IAAIF,WAAW,GAAG,GAAG;MAC1BC,MAAM,IAAIP,SAAS,GAAG,CAAC;MACvB,IAAI,CAAC,IAAI,CAAC/B,aAAa,CAACO,oBAAoB,EAAE;QAC1C+B,MAAM,IAAIF,UAAU;MACxB;IACJ;IACA,IAAI,IAAI,CAACpC,aAAa,CAACC,MAAM,EAAE;MAC3BhC,QAAQ,CAACyE,cAAc,CAACjC,GAAG,EAAE6B,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAED,KAAK,EAAE,IAAI,CAACvC,aAAa,CAACE,IAAI,CAAC;MACjF,IAAI,IAAI,CAACF,aAAa,CAACK,WAAW,EAAE;QAChCpC,QAAQ,CAACyE,cAAc,CAACjC,GAAG,EAAE+B,IAAI,EAAED,KAAK,GAAG,CAAC,GAAG,IAAI,CAACjD,QAAQ,EAAEkD,IAAI,EAAED,KAAK,GAAGjC,aAAa,EAAE,IAAI,CAACN,aAAa,CAACE,IAAI,CAAC;MACvH;IACJ,CAAC,MACI;MACDO,GAAG,CAACkC,SAAS,CAAC,CAAC;MACflC,GAAG,CAACmC,MAAM,CAACN,MAAM,EAAEC,KAAK,CAAC;MACzB9B,GAAG,CAACoC,MAAM,CAACL,IAAI,EAAED,KAAK,CAAC;MACvB,IAAI,IAAI,CAACvC,aAAa,CAACK,WAAW,EAAE;QAChCI,GAAG,CAACoC,MAAM,CAACL,IAAI,EAAED,KAAK,GAAGjC,aAAa,CAAC;MAC3C;MACAG,GAAG,CAACqC,MAAM,CAAC,CAAC;MACZrC,GAAG,CAACsC,SAAS,CAAC,CAAC;IACnB;EACJ;AACJ;AACAxE,WAAW,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}