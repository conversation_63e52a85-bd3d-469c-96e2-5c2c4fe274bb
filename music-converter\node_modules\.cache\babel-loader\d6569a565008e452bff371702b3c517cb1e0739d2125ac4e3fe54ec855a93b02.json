{"ast": null, "code": "import { GlyphNote } from './glyphnote.js';\nimport { Glyphs } from './glyphs.js';\nconst CODES = {\n  '1': Glyphs.repeat1Bar,\n  '2': Glyphs.repeat2Bars,\n  '4': Glyphs.repeat4Bars,\n  slash: Glyphs.repeatBarSlash\n};\nexport class RepeatNote extends GlyphNote {\n  static get CATEGORY() {\n    return \"RepeatNote\";\n  }\n  constructor(type, noteStruct, options) {\n    const glyphCode = CODES[type] || '\\uE500';\n    super(glyphCode, Object.assign({\n      duration: 'q',\n      alignCenter: type !== 'slash'\n    }, noteStruct), options);\n  }\n}", "map": {"version": 3, "names": ["GlyphNote", "Glyphs", "CODES", "repeat1Bar", "repeat2Bars", "repeat4Bars", "slash", "repeatBarSlash", "RepeatNote", "CATEGORY", "constructor", "type", "noteStruct", "options", "glyphCode", "Object", "assign", "duration", "alignCenter"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/repeatnote.js"], "sourcesContent": ["import { GlyphNote } from './glyphnote.js';\nimport { Glyphs } from './glyphs.js';\nconst CODES = {\n    '1': Glyphs.repeat1Bar,\n    '2': Glyphs.repeat2Bars,\n    '4': Glyphs.repeat4Bars,\n    slash: Glyphs.repeatBarSlash,\n};\nexport class RepeatNote extends GlyphNote {\n    static get CATEGORY() {\n        return \"RepeatNote\";\n    }\n    constructor(type, noteStruct, options) {\n        const glyphCode = CODES[type] || '\\uE500';\n        super(glyphCode, Object.assign({ duration: 'q', alignCenter: type !== 'slash' }, noteStruct), options);\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,MAAMC,KAAK,GAAG;EACV,GAAG,EAAED,MAAM,CAACE,UAAU;EACtB,GAAG,EAAEF,MAAM,CAACG,WAAW;EACvB,GAAG,EAAEH,MAAM,CAACI,WAAW;EACvBC,KAAK,EAAEL,MAAM,CAACM;AAClB,CAAC;AACD,OAAO,MAAMC,UAAU,SAASR,SAAS,CAAC;EACtC,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACAC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACnC,MAAMC,SAAS,GAAGZ,KAAK,CAACS,IAAI,CAAC,IAAI,QAAQ;IACzC,KAAK,CAACG,SAAS,EAAEC,MAAM,CAACC,MAAM,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,WAAW,EAAEP,IAAI,KAAK;IAAQ,CAAC,EAAEC,UAAU,CAAC,EAAEC,OAAO,CAAC;EAC1G;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}