{"ast": null, "code": "import { VexFlow } from '../src/vexflow.js';\nimport { Font } from '../src/font.js';\nimport { Academico } from '../src/fonts/academico.js';\nimport { AcademicoBold } from '../src/fonts/academicobold.js';\nimport { Bravura } from '../src/fonts/bravura.js';\nimport { Gonville } from '../src/fonts/gonville.js';\nimport { Petaluma } from '../src/fonts/petaluma.js';\nimport { PetalumaScript } from '../src/fonts/petalumascript.js';\nconst block = {\n  display: 'block'\n};\nconst swap = {\n  display: 'swap'\n};\nconst swapBold = {\n  display: 'swap',\n  weight: 'bold'\n};\nconst fontBravura = Font.load('Bravura', Bravura, block);\nconst fontAcademico = Font.load('Academico', Academico, swap);\nconst fontAcademicoBold = Font.load('Academico', AcademicoBold, swapBold);\nconst fontGonville = Font.load('Gonville', Gonville, block);\nconst fontPetaluma = Font.load('Petaluma', Petaluma, block);\nconst fontPetalumaScript = Font.load('Petaluma Script', PetalumaScript, swap);\nconst fontLoadPromises = [fontBravura, fontAcademico, fontAcademicoBold, fontGonville, fontPetaluma, fontPetalumaScript];\nVexFlow.BUILD.INFO = 'vexflow';\nVexFlow.setFonts('Bravura', 'Academico');\nPromise.allSettled(fontLoadPromises).then(() => {});\nexport * from '../src/index.js';\nexport default VexFlow;", "map": {"version": 3, "names": ["VexFlow", "Font", "<PERSON><PERSON>", "AcademicoBold", "Bravura", "Gonville", "Petaluma", "PetalumaScript", "block", "display", "swap", "swapBold", "weight", "fontBravura", "load", "fontAcademico", "fontAcademicoBold", "fontGonville", "fontPetaluma", "fontPetalumaScript", "fontLoadPromises", "BUILD", "INFO", "setFonts", "Promise", "allSettled", "then"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/entry/vexflow.js"], "sourcesContent": ["import { VexFlow } from '../src/vexflow.js';\nimport { Font } from '../src/font.js';\nimport { Academico } from '../src/fonts/academico.js';\nimport { AcademicoBold } from '../src/fonts/academicobold.js';\nimport { Bravura } from '../src/fonts/bravura.js';\nimport { Gonville } from '../src/fonts/gonville.js';\nimport { Petaluma } from '../src/fonts/petaluma.js';\nimport { PetalumaScript } from '../src/fonts/petalumascript.js';\nconst block = { display: 'block' };\nconst swap = { display: 'swap' };\nconst swapBold = { display: 'swap', weight: 'bold' };\nconst fontBravura = Font.load('Bravura', Bravura, block);\nconst fontAcademico = Font.load('Academico', Academico, swap);\nconst fontAcademicoBold = Font.load('Academico', AcademicoBold, swapBold);\nconst fontGonville = Font.load('Gonville', Gonville, block);\nconst fontPetaluma = Font.load('Petaluma', Petaluma, block);\nconst fontPetalumaScript = Font.load('Petaluma Script', PetalumaScript, swap);\nconst fontLoadPromises = [\n    fontBravura,\n    fontAcademico,\n    fontAcademicoBold,\n    fontGonville,\n    fontPetaluma,\n    fontPetalumaScript,\n];\nVexFlow.BUILD.INFO = 'vexflow';\nVexFlow.setFonts('Bravura', 'Academico');\nPromise.allSettled(fontLoadPromises).then(() => {\n});\nexport * from '../src/index.js';\nexport default VexFlow;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,MAAMC,KAAK,GAAG;EAAEC,OAAO,EAAE;AAAQ,CAAC;AAClC,MAAMC,IAAI,GAAG;EAAED,OAAO,EAAE;AAAO,CAAC;AAChC,MAAME,QAAQ,GAAG;EAAEF,OAAO,EAAE,MAAM;EAAEG,MAAM,EAAE;AAAO,CAAC;AACpD,MAAMC,WAAW,GAAGZ,IAAI,CAACa,IAAI,CAAC,SAAS,EAAEV,OAAO,EAAEI,KAAK,CAAC;AACxD,MAAMO,aAAa,GAAGd,IAAI,CAACa,IAAI,CAAC,WAAW,EAAEZ,SAAS,EAAEQ,IAAI,CAAC;AAC7D,MAAMM,iBAAiB,GAAGf,IAAI,CAACa,IAAI,CAAC,WAAW,EAAEX,aAAa,EAAEQ,QAAQ,CAAC;AACzE,MAAMM,YAAY,GAAGhB,IAAI,CAACa,IAAI,CAAC,UAAU,EAAET,QAAQ,EAAEG,KAAK,CAAC;AAC3D,MAAMU,YAAY,GAAGjB,IAAI,CAACa,IAAI,CAAC,UAAU,EAAER,QAAQ,EAAEE,KAAK,CAAC;AAC3D,MAAMW,kBAAkB,GAAGlB,IAAI,CAACa,IAAI,CAAC,iBAAiB,EAAEP,cAAc,EAAEG,IAAI,CAAC;AAC7E,MAAMU,gBAAgB,GAAG,CACrBP,WAAW,EACXE,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,kBAAkB,CACrB;AACDnB,OAAO,CAACqB,KAAK,CAACC,IAAI,GAAG,SAAS;AAC9BtB,OAAO,CAACuB,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC;AACxCC,OAAO,CAACC,UAAU,CAACL,gBAAgB,CAAC,CAACM,IAAI,CAAC,MAAM,CAChD,CAAC,CAAC;AACF,cAAc,iBAAiB;AAC/B,eAAe1B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}