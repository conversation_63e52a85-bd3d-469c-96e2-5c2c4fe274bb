{"ast": null, "code": "import { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (Parser.DEBUG) log('VexFlow.Parser', args);\n}\nconst NO_ERROR_POS = -1;\nfunction flattenMatches(r) {\n  if ('matchedString' in r) return r.matchedString;\n  if ('results' in r) return flattenMatches(r.results);\n  const results = r;\n  if (results.length === 1) return flattenMatches(results[0]);\n  if (results.length === 0) return null;\n  return results.map(flattenMatches);\n}\nexport class Parser {\n  constructor(grammar) {\n    this.grammar = grammar;\n    this.line = '';\n    this.pos = 0;\n    this.errorPos = NO_ERROR_POS;\n  }\n  parse(line) {\n    this.line = line;\n    this.pos = 0;\n    this.errorPos = NO_ERROR_POS;\n    const result = this.expect(this.grammar.begin());\n    result.errorPos = this.errorPos;\n    return result;\n  }\n  matchFail(returnPos) {\n    if (this.errorPos === NO_ERROR_POS) this.errorPos = this.pos;\n    this.pos = returnPos;\n  }\n  matchSuccess() {\n    this.errorPos = NO_ERROR_POS;\n  }\n  matchToken(token, noSpace = false) {\n    const regexp = noSpace ? new RegExp('^((' + token + '))') : new RegExp('^((' + token + ')\\\\s*)');\n    const workingLine = this.line.slice(this.pos);\n    const result = workingLine.match(regexp);\n    if (result !== null) {\n      return {\n        success: true,\n        matchedString: result[2],\n        incrementPos: result[1].length,\n        pos: this.pos\n      };\n    } else {\n      return {\n        success: false,\n        pos: this.pos\n      };\n    }\n  }\n  expectOne(rule, maybe = false) {\n    const results = [];\n    const pos = this.pos;\n    let allMatches = true;\n    let oneMatch = false;\n    maybe = maybe === true || rule.maybe === true;\n    if (rule.expect) {\n      for (const next of rule.expect) {\n        const localPos = this.pos;\n        const result = this.expect(next);\n        if (result.success) {\n          results.push(result);\n          oneMatch = true;\n          if (rule.or) break;\n        } else {\n          allMatches = false;\n          if (!rule.or) {\n            this.pos = localPos;\n            break;\n          }\n        }\n      }\n    }\n    const gotOne = rule.or && oneMatch || allMatches;\n    const success = gotOne || maybe === true;\n    const numMatches = gotOne ? 1 : 0;\n    if (maybe && !gotOne) this.pos = pos;\n    if (success) {\n      this.matchSuccess();\n    } else {\n      this.matchFail(pos);\n    }\n    return {\n      success,\n      results,\n      numMatches\n    };\n  }\n  expectOneOrMore(rule, maybe = false) {\n    const results = [];\n    const pos = this.pos;\n    let numMatches = 0;\n    let more = true;\n    do {\n      const result = this.expectOne(rule);\n      if (result.success && result.results) {\n        numMatches++;\n        results.push(result.results);\n      } else {\n        more = false;\n      }\n    } while (more);\n    const success = numMatches > 0 || maybe === true;\n    if (maybe && !(numMatches > 0)) this.pos = pos;\n    if (success) {\n      this.matchSuccess();\n    } else {\n      this.matchFail(pos);\n    }\n    return {\n      success,\n      results,\n      numMatches\n    };\n  }\n  expectZeroOrMore(rule) {\n    return this.expectOneOrMore(rule, true);\n  }\n  expect(ruleFunc) {\n    L('Evaluating rule function:', ruleFunc);\n    if (!ruleFunc) {\n      throw new RuntimeError('Invalid rule function');\n    }\n    let result;\n    const rule = ruleFunc.bind(this.grammar)();\n    if (rule.token) {\n      result = this.matchToken(rule.token, rule.noSpace === true);\n      if (result.success) {\n        this.pos += result.incrementPos;\n      }\n    } else if (rule.expect) {\n      if (rule.oneOrMore) {\n        result = this.expectOneOrMore(rule);\n      } else if (rule.zeroOrMore) {\n        result = this.expectZeroOrMore(rule);\n      } else {\n        result = this.expectOne(rule);\n      }\n    } else {\n      L(rule);\n      throw new RuntimeError('Bad grammar! No `token` or `expect` property ' + rule);\n    }\n    const matches = [];\n    result.matches = matches;\n    if (result.results) {\n      result.results.forEach(r => matches.push(flattenMatches(r)));\n    }\n    if (rule.run && result.success) {\n      rule.run({\n        matches\n      });\n    }\n    return result;\n  }\n}\nParser.DEBUG = false;", "map": {"version": 3, "names": ["log", "RuntimeError", "L", "args", "<PERSON><PERSON><PERSON>", "DEBUG", "NO_ERROR_POS", "flattenMatches", "r", "matchedString", "results", "length", "map", "constructor", "grammar", "line", "pos", "errorPos", "parse", "result", "expect", "begin", "matchFail", "returnPos", "matchSuccess", "matchToken", "token", "noSpace", "regexp", "RegExp", "workingLine", "slice", "match", "success", "incrementPos", "expectOne", "rule", "maybe", "allMatches", "oneMatch", "next", "localPos", "push", "or", "gotOne", "numMatches", "expectOneOrMore", "more", "expectZeroOrMore", "ruleFunc", "bind", "oneOrMore", "zeroOrMore", "matches", "for<PERSON>ach", "run"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/parser.js"], "sourcesContent": ["import { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (Parser.DEBUG)\n        log('VexFlow.Parser', args);\n}\nconst NO_ERROR_POS = -1;\nfunction flattenMatches(r) {\n    if ('matchedString' in r)\n        return r.matchedString;\n    if ('results' in r)\n        return flattenMatches(r.results);\n    const results = r;\n    if (results.length === 1)\n        return flattenMatches(results[0]);\n    if (results.length === 0)\n        return null;\n    return results.map(flattenMatches);\n}\nexport class Parser {\n    constructor(grammar) {\n        this.grammar = grammar;\n        this.line = '';\n        this.pos = 0;\n        this.errorPos = NO_ERROR_POS;\n    }\n    parse(line) {\n        this.line = line;\n        this.pos = 0;\n        this.errorPos = NO_ERROR_POS;\n        const result = this.expect(this.grammar.begin());\n        result.errorPos = this.errorPos;\n        return result;\n    }\n    matchFail(returnPos) {\n        if (this.errorPos === NO_ERROR_POS)\n            this.errorPos = this.pos;\n        this.pos = returnPos;\n    }\n    matchSuccess() {\n        this.errorPos = NO_ERROR_POS;\n    }\n    matchToken(token, noSpace = false) {\n        const regexp = noSpace ? new RegExp('^((' + token + '))') : new RegExp('^((' + token + ')\\\\s*)');\n        const workingLine = this.line.slice(this.pos);\n        const result = workingLine.match(regexp);\n        if (result !== null) {\n            return {\n                success: true,\n                matchedString: result[2],\n                incrementPos: result[1].length,\n                pos: this.pos,\n            };\n        }\n        else {\n            return { success: false, pos: this.pos };\n        }\n    }\n    expectOne(rule, maybe = false) {\n        const results = [];\n        const pos = this.pos;\n        let allMatches = true;\n        let oneMatch = false;\n        maybe = maybe === true || rule.maybe === true;\n        if (rule.expect) {\n            for (const next of rule.expect) {\n                const localPos = this.pos;\n                const result = this.expect(next);\n                if (result.success) {\n                    results.push(result);\n                    oneMatch = true;\n                    if (rule.or)\n                        break;\n                }\n                else {\n                    allMatches = false;\n                    if (!rule.or) {\n                        this.pos = localPos;\n                        break;\n                    }\n                }\n            }\n        }\n        const gotOne = (rule.or && oneMatch) || allMatches;\n        const success = gotOne || maybe === true;\n        const numMatches = gotOne ? 1 : 0;\n        if (maybe && !gotOne)\n            this.pos = pos;\n        if (success) {\n            this.matchSuccess();\n        }\n        else {\n            this.matchFail(pos);\n        }\n        return { success, results, numMatches };\n    }\n    expectOneOrMore(rule, maybe = false) {\n        const results = [];\n        const pos = this.pos;\n        let numMatches = 0;\n        let more = true;\n        do {\n            const result = this.expectOne(rule);\n            if (result.success && result.results) {\n                numMatches++;\n                results.push(result.results);\n            }\n            else {\n                more = false;\n            }\n        } while (more);\n        const success = numMatches > 0 || maybe === true;\n        if (maybe && !(numMatches > 0))\n            this.pos = pos;\n        if (success) {\n            this.matchSuccess();\n        }\n        else {\n            this.matchFail(pos);\n        }\n        return { success, results, numMatches };\n    }\n    expectZeroOrMore(rule) {\n        return this.expectOneOrMore(rule, true);\n    }\n    expect(ruleFunc) {\n        L('Evaluating rule function:', ruleFunc);\n        if (!ruleFunc) {\n            throw new RuntimeError('Invalid rule function');\n        }\n        let result;\n        const rule = ruleFunc.bind(this.grammar)();\n        if (rule.token) {\n            result = this.matchToken(rule.token, rule.noSpace === true);\n            if (result.success) {\n                this.pos += result.incrementPos;\n            }\n        }\n        else if (rule.expect) {\n            if (rule.oneOrMore) {\n                result = this.expectOneOrMore(rule);\n            }\n            else if (rule.zeroOrMore) {\n                result = this.expectZeroOrMore(rule);\n            }\n            else {\n                result = this.expectOne(rule);\n            }\n        }\n        else {\n            L(rule);\n            throw new RuntimeError('Bad grammar! No `token` or `expect` property ' + rule);\n        }\n        const matches = [];\n        result.matches = matches;\n        if (result.results) {\n            result.results.forEach((r) => matches.push(flattenMatches(r)));\n        }\n        if (rule.run && result.success) {\n            rule.run({ matches });\n        }\n        return result;\n    }\n}\nParser.DEBUG = false;\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,MAAM,CAACC,KAAK,EACZL,GAAG,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACnC;AACA,MAAMG,YAAY,GAAG,CAAC,CAAC;AACvB,SAASC,cAAcA,CAACC,CAAC,EAAE;EACvB,IAAI,eAAe,IAAIA,CAAC,EACpB,OAAOA,CAAC,CAACC,aAAa;EAC1B,IAAI,SAAS,IAAID,CAAC,EACd,OAAOD,cAAc,CAACC,CAAC,CAACE,OAAO,CAAC;EACpC,MAAMA,OAAO,GAAGF,CAAC;EACjB,IAAIE,OAAO,CAACC,MAAM,KAAK,CAAC,EACpB,OAAOJ,cAAc,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;EACrC,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EACpB,OAAO,IAAI;EACf,OAAOD,OAAO,CAACE,GAAG,CAACL,cAAc,CAAC;AACtC;AACA,OAAO,MAAMH,MAAM,CAAC;EAChBS,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,QAAQ,GAAGX,YAAY;EAChC;EACAY,KAAKA,CAACH,IAAI,EAAE;IACR,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,QAAQ,GAAGX,YAAY;IAC5B,MAAMa,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,IAAI,CAACN,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC;IAChDF,MAAM,CAACF,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC/B,OAAOE,MAAM;EACjB;EACAG,SAASA,CAACC,SAAS,EAAE;IACjB,IAAI,IAAI,CAACN,QAAQ,KAAKX,YAAY,EAC9B,IAAI,CAACW,QAAQ,GAAG,IAAI,CAACD,GAAG;IAC5B,IAAI,CAACA,GAAG,GAAGO,SAAS;EACxB;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,CAACP,QAAQ,GAAGX,YAAY;EAChC;EACAmB,UAAUA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,EAAE;IAC/B,MAAMC,MAAM,GAAGD,OAAO,GAAG,IAAIE,MAAM,CAAC,KAAK,GAAGH,KAAK,GAAG,IAAI,CAAC,GAAG,IAAIG,MAAM,CAAC,KAAK,GAAGH,KAAK,GAAG,QAAQ,CAAC;IAChG,MAAMI,WAAW,GAAG,IAAI,CAACf,IAAI,CAACgB,KAAK,CAAC,IAAI,CAACf,GAAG,CAAC;IAC7C,MAAMG,MAAM,GAAGW,WAAW,CAACE,KAAK,CAACJ,MAAM,CAAC;IACxC,IAAIT,MAAM,KAAK,IAAI,EAAE;MACjB,OAAO;QACHc,OAAO,EAAE,IAAI;QACbxB,aAAa,EAAEU,MAAM,CAAC,CAAC,CAAC;QACxBe,YAAY,EAAEf,MAAM,CAAC,CAAC,CAAC,CAACR,MAAM;QAC9BK,GAAG,EAAE,IAAI,CAACA;MACd,CAAC;IACL,CAAC,MACI;MACD,OAAO;QAAEiB,OAAO,EAAE,KAAK;QAAEjB,GAAG,EAAE,IAAI,CAACA;MAAI,CAAC;IAC5C;EACJ;EACAmB,SAASA,CAACC,IAAI,EAAEC,KAAK,GAAG,KAAK,EAAE;IAC3B,MAAM3B,OAAO,GAAG,EAAE;IAClB,MAAMM,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,IAAIsB,UAAU,GAAG,IAAI;IACrB,IAAIC,QAAQ,GAAG,KAAK;IACpBF,KAAK,GAAGA,KAAK,KAAK,IAAI,IAAID,IAAI,CAACC,KAAK,KAAK,IAAI;IAC7C,IAAID,IAAI,CAAChB,MAAM,EAAE;MACb,KAAK,MAAMoB,IAAI,IAAIJ,IAAI,CAAChB,MAAM,EAAE;QAC5B,MAAMqB,QAAQ,GAAG,IAAI,CAACzB,GAAG;QACzB,MAAMG,MAAM,GAAG,IAAI,CAACC,MAAM,CAACoB,IAAI,CAAC;QAChC,IAAIrB,MAAM,CAACc,OAAO,EAAE;UAChBvB,OAAO,CAACgC,IAAI,CAACvB,MAAM,CAAC;UACpBoB,QAAQ,GAAG,IAAI;UACf,IAAIH,IAAI,CAACO,EAAE,EACP;QACR,CAAC,MACI;UACDL,UAAU,GAAG,KAAK;UAClB,IAAI,CAACF,IAAI,CAACO,EAAE,EAAE;YACV,IAAI,CAAC3B,GAAG,GAAGyB,QAAQ;YACnB;UACJ;QACJ;MACJ;IACJ;IACA,MAAMG,MAAM,GAAIR,IAAI,CAACO,EAAE,IAAIJ,QAAQ,IAAKD,UAAU;IAClD,MAAML,OAAO,GAAGW,MAAM,IAAIP,KAAK,KAAK,IAAI;IACxC,MAAMQ,UAAU,GAAGD,MAAM,GAAG,CAAC,GAAG,CAAC;IACjC,IAAIP,KAAK,IAAI,CAACO,MAAM,EAChB,IAAI,CAAC5B,GAAG,GAAGA,GAAG;IAClB,IAAIiB,OAAO,EAAE;MACT,IAAI,CAACT,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACF,SAAS,CAACN,GAAG,CAAC;IACvB;IACA,OAAO;MAAEiB,OAAO;MAAEvB,OAAO;MAAEmC;IAAW,CAAC;EAC3C;EACAC,eAAeA,CAACV,IAAI,EAAEC,KAAK,GAAG,KAAK,EAAE;IACjC,MAAM3B,OAAO,GAAG,EAAE;IAClB,MAAMM,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,IAAI6B,UAAU,GAAG,CAAC;IAClB,IAAIE,IAAI,GAAG,IAAI;IACf,GAAG;MACC,MAAM5B,MAAM,GAAG,IAAI,CAACgB,SAAS,CAACC,IAAI,CAAC;MACnC,IAAIjB,MAAM,CAACc,OAAO,IAAId,MAAM,CAACT,OAAO,EAAE;QAClCmC,UAAU,EAAE;QACZnC,OAAO,CAACgC,IAAI,CAACvB,MAAM,CAACT,OAAO,CAAC;MAChC,CAAC,MACI;QACDqC,IAAI,GAAG,KAAK;MAChB;IACJ,CAAC,QAAQA,IAAI;IACb,MAAMd,OAAO,GAAGY,UAAU,GAAG,CAAC,IAAIR,KAAK,KAAK,IAAI;IAChD,IAAIA,KAAK,IAAI,EAAEQ,UAAU,GAAG,CAAC,CAAC,EAC1B,IAAI,CAAC7B,GAAG,GAAGA,GAAG;IAClB,IAAIiB,OAAO,EAAE;MACT,IAAI,CAACT,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACF,SAAS,CAACN,GAAG,CAAC;IACvB;IACA,OAAO;MAAEiB,OAAO;MAAEvB,OAAO;MAAEmC;IAAW,CAAC;EAC3C;EACAG,gBAAgBA,CAACZ,IAAI,EAAE;IACnB,OAAO,IAAI,CAACU,eAAe,CAACV,IAAI,EAAE,IAAI,CAAC;EAC3C;EACAhB,MAAMA,CAAC6B,QAAQ,EAAE;IACb/C,CAAC,CAAC,2BAA2B,EAAE+C,QAAQ,CAAC;IACxC,IAAI,CAACA,QAAQ,EAAE;MACX,MAAM,IAAIhD,YAAY,CAAC,uBAAuB,CAAC;IACnD;IACA,IAAIkB,MAAM;IACV,MAAMiB,IAAI,GAAGa,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACpC,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAIsB,IAAI,CAACV,KAAK,EAAE;MACZP,MAAM,GAAG,IAAI,CAACM,UAAU,CAACW,IAAI,CAACV,KAAK,EAAEU,IAAI,CAACT,OAAO,KAAK,IAAI,CAAC;MAC3D,IAAIR,MAAM,CAACc,OAAO,EAAE;QAChB,IAAI,CAACjB,GAAG,IAAIG,MAAM,CAACe,YAAY;MACnC;IACJ,CAAC,MACI,IAAIE,IAAI,CAAChB,MAAM,EAAE;MAClB,IAAIgB,IAAI,CAACe,SAAS,EAAE;QAChBhC,MAAM,GAAG,IAAI,CAAC2B,eAAe,CAACV,IAAI,CAAC;MACvC,CAAC,MACI,IAAIA,IAAI,CAACgB,UAAU,EAAE;QACtBjC,MAAM,GAAG,IAAI,CAAC6B,gBAAgB,CAACZ,IAAI,CAAC;MACxC,CAAC,MACI;QACDjB,MAAM,GAAG,IAAI,CAACgB,SAAS,CAACC,IAAI,CAAC;MACjC;IACJ,CAAC,MACI;MACDlC,CAAC,CAACkC,IAAI,CAAC;MACP,MAAM,IAAInC,YAAY,CAAC,+CAA+C,GAAGmC,IAAI,CAAC;IAClF;IACA,MAAMiB,OAAO,GAAG,EAAE;IAClBlC,MAAM,CAACkC,OAAO,GAAGA,OAAO;IACxB,IAAIlC,MAAM,CAACT,OAAO,EAAE;MAChBS,MAAM,CAACT,OAAO,CAAC4C,OAAO,CAAE9C,CAAC,IAAK6C,OAAO,CAACX,IAAI,CAACnC,cAAc,CAACC,CAAC,CAAC,CAAC,CAAC;IAClE;IACA,IAAI4B,IAAI,CAACmB,GAAG,IAAIpC,MAAM,CAACc,OAAO,EAAE;MAC5BG,IAAI,CAACmB,GAAG,CAAC;QAAEF;MAAQ,CAAC,CAAC;IACzB;IACA,OAAOlC,MAAM;EACjB;AACJ;AACAf,MAAM,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}