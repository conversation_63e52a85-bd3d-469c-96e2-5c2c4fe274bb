{"ast": null, "code": "import { Modifier, ModifierPosition } from './modifier.js';\nexport class Parenthesis extends Modifier {\n  static get CATEGORY() {\n    return \"Parenthesis\";\n  }\n  static buildAndAttach(notes) {\n    for (const note of notes) {\n      for (let i = 0; i < note.keys.length; i++) {\n        note.addModifier(new Parenthesis(ModifierPosition.LEFT), i);\n        note.addModifier(new Parenthesis(ModifierPosition.RIGHT), i);\n      }\n    }\n  }\n  static format(parentheses, state) {\n    if (!parentheses || parentheses.length === 0) return false;\n    let xWidthL = 0;\n    let xWidthR = 0;\n    for (let i = 0; i < parentheses.length; ++i) {\n      const parenthesis = parentheses[i];\n      const note = parenthesis.getNote();\n      const pos = parenthesis.getPosition();\n      const index = parenthesis.checkIndex();\n      let shift = 0;\n      if (pos === ModifierPosition.RIGHT) {\n        shift = note.getRightParenthesisPx(index);\n        xWidthR = xWidthR > shift + parenthesis.width ? xWidthR : shift + parenthesis.width;\n      }\n      if (pos === ModifierPosition.LEFT) {\n        shift = note.getLeftParenthesisPx(index) + parenthesis.width;\n        xWidthL = xWidthL > shift + parenthesis.width ? xWidthL : shift + parenthesis.width;\n      }\n      parenthesis.setXShift(shift);\n    }\n    state.leftShift += xWidthL;\n    state.rightShift += xWidthR;\n    return true;\n  }\n  constructor(position) {\n    super();\n    this.position = position !== null && position !== void 0 ? position : Modifier.Position.LEFT;\n    if (this.position === Modifier.Position.RIGHT) {\n      this.text = '\\uE0F6';\n    } else if (this.position === Modifier.Position.LEFT) {\n      this.text = '\\uE0F5';\n    }\n  }\n  setNote(note) {\n    this.note = note;\n    this.setFont(note.getFont());\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(this.position, this.index, {\n      forceFlagRight: true\n    });\n    this.renderText(ctx, start.x, start.y);\n  }\n}", "map": {"version": 3, "names": ["Modifier", "ModifierPosition", "Parenthesis", "CATEGORY", "buildAndAttach", "notes", "note", "i", "keys", "length", "addModifier", "LEFT", "RIGHT", "format", "parentheses", "state", "xWidthL", "xWidthR", "parenthesis", "getNote", "pos", "getPosition", "index", "checkIndex", "shift", "getRightParenthesisPx", "width", "getLeftParenthesisPx", "setXShift", "leftShift", "rightShift", "constructor", "position", "Position", "text", "setNote", "setFont", "getFont", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "forceFlagRight", "renderText", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/parenthesis.js"], "sourcesContent": ["import { Modifier, ModifierPosition } from './modifier.js';\nexport class Parenthesis extends Modifier {\n    static get CATEGORY() {\n        return \"Parenthesis\";\n    }\n    static buildAndAttach(notes) {\n        for (const note of notes) {\n            for (let i = 0; i < note.keys.length; i++) {\n                note.addModifier(new Parenthesis(ModifierPosition.LEFT), i);\n                note.addModifier(new Parenthesis(ModifierPosition.RIGHT), i);\n            }\n        }\n    }\n    static format(parentheses, state) {\n        if (!parentheses || parentheses.length === 0)\n            return false;\n        let xWidthL = 0;\n        let xWidthR = 0;\n        for (let i = 0; i < parentheses.length; ++i) {\n            const parenthesis = parentheses[i];\n            const note = parenthesis.getNote();\n            const pos = parenthesis.getPosition();\n            const index = parenthesis.checkIndex();\n            let shift = 0;\n            if (pos === ModifierPosition.RIGHT) {\n                shift = note.getRightParenthesisPx(index);\n                xWidthR = xWidthR > shift + parenthesis.width ? xWidthR : shift + parenthesis.width;\n            }\n            if (pos === ModifierPosition.LEFT) {\n                shift = note.getLeftParenthesisPx(index) + parenthesis.width;\n                xWidthL = xWidthL > shift + parenthesis.width ? xWidthL : shift + parenthesis.width;\n            }\n            parenthesis.setXShift(shift);\n        }\n        state.leftShift += xWidthL;\n        state.rightShift += xWidthR;\n        return true;\n    }\n    constructor(position) {\n        super();\n        this.position = position !== null && position !== void 0 ? position : Modifier.Position.LEFT;\n        if (this.position === Modifier.Position.RIGHT) {\n            this.text = '\\uE0F6';\n        }\n        else if (this.position === Modifier.Position.LEFT) {\n            this.text = '\\uE0F5';\n        }\n    }\n    setNote(note) {\n        this.note = note;\n        this.setFont(note.getFont());\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(this.position, this.index, { forceFlagRight: true });\n        this.renderText(ctx, start.x, start.y);\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,OAAO,MAAMC,WAAW,SAASF,QAAQ,CAAC;EACtC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,aAAa;EACxB;EACA,OAAOC,cAAcA,CAACC,KAAK,EAAE;IACzB,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACtB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACvCD,IAAI,CAACI,WAAW,CAAC,IAAIR,WAAW,CAACD,gBAAgB,CAACU,IAAI,CAAC,EAAEJ,CAAC,CAAC;QAC3DD,IAAI,CAACI,WAAW,CAAC,IAAIR,WAAW,CAACD,gBAAgB,CAACW,KAAK,CAAC,EAAEL,CAAC,CAAC;MAChE;IACJ;EACJ;EACA,OAAOM,MAAMA,CAACC,WAAW,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACL,MAAM,KAAK,CAAC,EACxC,OAAO,KAAK;IAChB,IAAIO,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,WAAW,CAACL,MAAM,EAAE,EAAEF,CAAC,EAAE;MACzC,MAAMW,WAAW,GAAGJ,WAAW,CAACP,CAAC,CAAC;MAClC,MAAMD,IAAI,GAAGY,WAAW,CAACC,OAAO,CAAC,CAAC;MAClC,MAAMC,GAAG,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;MACrC,MAAMC,KAAK,GAAGJ,WAAW,CAACK,UAAU,CAAC,CAAC;MACtC,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIJ,GAAG,KAAKnB,gBAAgB,CAACW,KAAK,EAAE;QAChCY,KAAK,GAAGlB,IAAI,CAACmB,qBAAqB,CAACH,KAAK,CAAC;QACzCL,OAAO,GAAGA,OAAO,GAAGO,KAAK,GAAGN,WAAW,CAACQ,KAAK,GAAGT,OAAO,GAAGO,KAAK,GAAGN,WAAW,CAACQ,KAAK;MACvF;MACA,IAAIN,GAAG,KAAKnB,gBAAgB,CAACU,IAAI,EAAE;QAC/Ba,KAAK,GAAGlB,IAAI,CAACqB,oBAAoB,CAACL,KAAK,CAAC,GAAGJ,WAAW,CAACQ,KAAK;QAC5DV,OAAO,GAAGA,OAAO,GAAGQ,KAAK,GAAGN,WAAW,CAACQ,KAAK,GAAGV,OAAO,GAAGQ,KAAK,GAAGN,WAAW,CAACQ,KAAK;MACvF;MACAR,WAAW,CAACU,SAAS,CAACJ,KAAK,CAAC;IAChC;IACAT,KAAK,CAACc,SAAS,IAAIb,OAAO;IAC1BD,KAAK,CAACe,UAAU,IAAIb,OAAO;IAC3B,OAAO,IAAI;EACf;EACAc,WAAWA,CAACC,QAAQ,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGhC,QAAQ,CAACiC,QAAQ,CAACtB,IAAI;IAC5F,IAAI,IAAI,CAACqB,QAAQ,KAAKhC,QAAQ,CAACiC,QAAQ,CAACrB,KAAK,EAAE;MAC3C,IAAI,CAACsB,IAAI,GAAG,QAAQ;IACxB,CAAC,MACI,IAAI,IAAI,CAACF,QAAQ,KAAKhC,QAAQ,CAACiC,QAAQ,CAACtB,IAAI,EAAE;MAC/C,IAAI,CAACuB,IAAI,GAAG,QAAQ;IACxB;EACJ;EACAC,OAAOA,CAAC7B,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC8B,OAAO,CAAC9B,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAAC;IAC5B,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMlC,IAAI,GAAG,IAAI,CAACmC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGrC,IAAI,CAACsC,kBAAkB,CAAC,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACV,KAAK,EAAE;MAAEuB,cAAc,EAAE;IAAK,CAAC,CAAC;IAC1F,IAAI,CAACC,UAAU,CAACP,GAAG,EAAEI,KAAK,CAACI,CAAC,EAAEJ,KAAK,CAACK,CAAC,CAAC;EAC1C;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}