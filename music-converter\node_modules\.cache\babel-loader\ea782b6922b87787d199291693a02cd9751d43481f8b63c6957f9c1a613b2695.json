{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Note } from './note.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (TextDynamics.DEBUG) log('VexFlow.TextDynamics', args);\n}\nexport class TextDynamics extends Note {\n  static get CATEGORY() {\n    return \"TextDynamics\";\n  }\n  static get GLYPHS() {\n    return {\n      f: Glyphs.dynamicForte,\n      p: Glyphs.dynamicPiano,\n      m: Glyphs.dynamicMezzo,\n      s: Glyphs.dynamicSforzando,\n      z: Glyphs.dynamicZ,\n      r: Glyphs.dynamicRinforzando\n    };\n  }\n  constructor(noteStruct) {\n    var _a, _b;\n    super(noteStruct);\n    this.sequence = ((_a = noteStruct.text) !== null && _a !== void 0 ? _a : '').toLowerCase();\n    this.line = (_b = noteStruct.line) !== null && _b !== void 0 ? _b : 0;\n    this.text = '';\n    L('New Dynamics Text: ', this.sequence);\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  preFormat() {\n    this.text = '';\n    this.sequence.split('').forEach(letter => {\n      const glyph = TextDynamics.GLYPHS[letter];\n      if (!glyph) throw new RuntimeError('Invalid dynamics character: ' + letter);\n      this.text += glyph;\n    });\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    this.setRendered();\n    const x = this.getAbsoluteX();\n    const y = this.checkStave().getYForLine(this.line + -3);\n    L('Rendering Dynamics: ', this.sequence);\n    this.renderText(this.checkContext(), x, y);\n  }\n}\nTextDynamics.DEBUG = false;", "map": {"version": 3, "names": ["Glyphs", "Note", "log", "RuntimeError", "L", "args", "TextDynamics", "DEBUG", "CATEGORY", "GLYPHS", "f", "dynamicForte", "p", "dynamicPiano", "m", "<PERSON><PERSON><PERSON><PERSON>", "s", "dynamicSforzando", "z", "dynamicZ", "r", "dynamicRinforzando", "constructor", "noteStruct", "_a", "_b", "sequence", "text", "toLowerCase", "line", "setLine", "preFormat", "split", "for<PERSON>ach", "letter", "glyph", "preFormatted", "draw", "setRendered", "x", "getAbsoluteX", "y", "checkStave", "getYForLine", "renderText", "checkContext"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/textdynamics.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Note } from './note.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (TextDynamics.DEBUG)\n        log('VexFlow.TextDynamics', args);\n}\nexport class TextDynamics extends Note {\n    static get CATEGORY() {\n        return \"TextDynamics\";\n    }\n    static get GLYPHS() {\n        return {\n            f: Glyphs.dynamicForte,\n            p: Glyphs.dynamicPiano,\n            m: Glyphs.dynamicMezzo,\n            s: Glyphs.dynamicSforzando,\n            z: Glyphs.dynamicZ,\n            r: Glyphs.dynamicRinforzando,\n        };\n    }\n    constructor(noteStruct) {\n        var _a, _b;\n        super(noteStruct);\n        this.sequence = ((_a = noteStruct.text) !== null && _a !== void 0 ? _a : '').toLowerCase();\n        this.line = (_b = noteStruct.line) !== null && _b !== void 0 ? _b : 0;\n        this.text = '';\n        L('New Dynamics Text: ', this.sequence);\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    preFormat() {\n        this.text = '';\n        this.sequence.split('').forEach((letter) => {\n            const glyph = TextDynamics.GLYPHS[letter];\n            if (!glyph)\n                throw new RuntimeError('Invalid dynamics character: ' + letter);\n            this.text += glyph;\n        });\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        this.setRendered();\n        const x = this.getAbsoluteX();\n        const y = this.checkStave().getYForLine(this.line + -3);\n        L('Rendering Dynamics: ', this.sequence);\n        this.renderText(this.checkContext(), x, y);\n    }\n}\nTextDynamics.DEBUG = false;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,YAAY,CAACC,KAAK,EAClBL,GAAG,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AACzC;AACA,OAAO,MAAMC,YAAY,SAASL,IAAI,CAAC;EACnC,WAAWO,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,WAAWC,MAAMA,CAAA,EAAG;IAChB,OAAO;MACHC,CAAC,EAAEV,MAAM,CAACW,YAAY;MACtBC,CAAC,EAAEZ,MAAM,CAACa,YAAY;MACtBC,CAAC,EAAEd,MAAM,CAACe,YAAY;MACtBC,CAAC,EAAEhB,MAAM,CAACiB,gBAAgB;MAC1BC,CAAC,EAAElB,MAAM,CAACmB,QAAQ;MAClBC,CAAC,EAAEpB,MAAM,CAACqB;IACd,CAAC;EACL;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIC,EAAE,EAAEC,EAAE;IACV,KAAK,CAACF,UAAU,CAAC;IACjB,IAAI,CAACG,QAAQ,GAAG,CAAC,CAACF,EAAE,GAAGD,UAAU,CAACI,IAAI,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEI,WAAW,CAAC,CAAC;IAC1F,IAAI,CAACC,IAAI,GAAG,CAACJ,EAAE,GAAGF,UAAU,CAACM,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACrE,IAAI,CAACE,IAAI,GAAG,EAAE;IACdvB,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAACsB,QAAQ,CAAC;EAC3C;EACAI,OAAOA,CAACD,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAE,SAASA,CAAA,EAAG;IACR,IAAI,CAACJ,IAAI,GAAG,EAAE;IACd,IAAI,CAACD,QAAQ,CAACM,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAEC,MAAM,IAAK;MACxC,MAAMC,KAAK,GAAG7B,YAAY,CAACG,MAAM,CAACyB,MAAM,CAAC;MACzC,IAAI,CAACC,KAAK,EACN,MAAM,IAAIhC,YAAY,CAAC,8BAA8B,GAAG+B,MAAM,CAAC;MACnE,IAAI,CAACP,IAAI,IAAIQ,KAAK;IACtB,CAAC,CAAC;IACF,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,CAAC,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC7B,MAAMC,CAAC,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,WAAW,CAAC,IAAI,CAACd,IAAI,GAAG,CAAC,CAAC,CAAC;IACvDzB,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAACsB,QAAQ,CAAC;IACxC,IAAI,CAACkB,UAAU,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAEN,CAAC,EAAEE,CAAC,CAAC;EAC9C;AACJ;AACAnC,YAAY,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}