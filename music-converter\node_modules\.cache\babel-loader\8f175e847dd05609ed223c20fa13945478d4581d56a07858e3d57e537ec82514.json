{"ast": null, "code": "import { Stave } from './stave.js';\nexport class TabStave extends Stave {\n  static get CATEGORY() {\n    return \"TabStave\";\n  }\n  constructor(x, y, width, options) {\n    const tabOptions = Object.assign({\n      spacingBetweenLinesPx: 13,\n      numLines: 6,\n      topTextPosition: 1\n    }, options);\n    super(x, y, width, tabOptions);\n  }\n  getYForGlyphs() {\n    return this.getYForLine(2.5);\n  }\n  addTabGlyph() {\n    this.addClef('tab');\n    return this;\n  }\n}", "map": {"version": 3, "names": ["Stave", "TabStave", "CATEGORY", "constructor", "x", "y", "width", "options", "tabOptions", "Object", "assign", "spacingBetweenLinesPx", "numLines", "topTextPosition", "getYForGlyphs", "getYForLine", "addTabGlyph", "addClef"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tabstave.js"], "sourcesContent": ["import { Stave } from './stave.js';\nexport class TabStave extends Stave {\n    static get CATEGORY() {\n        return \"TabStave\";\n    }\n    constructor(x, y, width, options) {\n        const tabOptions = Object.assign({ spacingBetweenLinesPx: 13, numLines: 6, topTextPosition: 1 }, options);\n        super(x, y, width, tabOptions);\n    }\n    getYForGlyphs() {\n        return this.getYForLine(2.5);\n    }\n    addTabGlyph() {\n        this.addClef('tab');\n        return this;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,OAAO,MAAMC,QAAQ,SAASD,KAAK,CAAC;EAChC,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACAC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC9B,MAAMC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,qBAAqB,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,eAAe,EAAE;IAAE,CAAC,EAAEN,OAAO,CAAC;IACzG,KAAK,CAACH,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEE,UAAU,CAAC;EAClC;EACAM,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,WAAW,CAAC,GAAG,CAAC;EAChC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,CAAC,KAAK,CAAC;IACnB,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}