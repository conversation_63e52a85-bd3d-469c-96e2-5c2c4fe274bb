{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Dot extends Modifier {\n  static get CATEGORY() {\n    return \"Dot\";\n  }\n  static getDots(note) {\n    return note.getModifiersByType(Dot.CATEGORY);\n  }\n  static buildAndAttach(notes, options) {\n    for (const note of notes) {\n      if (options === null || options === void 0 ? void 0 : options.all) {\n        for (let i = 0; i < note.keys.length; i++) {\n          const dot = new Dot();\n          note.addModifier(dot, i);\n        }\n      } else if ((options === null || options === void 0 ? void 0 : options.index) !== undefined) {\n        const dot = new Dot();\n        note.addModifier(dot, options === null || options === void 0 ? void 0 : options.index);\n      } else {\n        const dot = new Dot();\n        note.addModifier(dot, 0);\n      }\n    }\n  }\n  static format(dots, state) {\n    const rightShift = state.rightShift;\n    const dotSpacing = 1;\n    if (!dots || dots.length === 0) return false;\n    const dotList = [];\n    const maxShiftMap = {};\n    for (let i = 0; i < dots.length; ++i) {\n      const dot = dots[i];\n      const note = dot.getNote();\n      let props;\n      let shift;\n      if (isStaveNote(note)) {\n        const index = dot.checkIndex();\n        props = note.getKeyProps()[index];\n        shift = note.getFirstDotPx();\n      } else if (isTabNote(note)) {\n        props = {\n          line: 0.5\n        };\n        shift = rightShift;\n      } else {\n        throw new RuntimeError('Internal', 'Unexpected instance.');\n      }\n      const noteId = note.getAttribute('id');\n      dotList.push({\n        line: props.line,\n        note,\n        noteId,\n        dot\n      });\n      maxShiftMap[noteId] = Math.max(maxShiftMap[noteId] || shift, shift);\n    }\n    dotList.sort((a, b) => b.line - a.line);\n    let dotShift = rightShift;\n    let xWidth = 0;\n    let lastLine = null;\n    let lastNote = null;\n    let prevDottedSpace = null;\n    let halfShiftY = 0;\n    for (let i = 0; i < dotList.length; ++i) {\n      const {\n        dot,\n        note,\n        noteId,\n        line\n      } = dotList[i];\n      if (line !== lastLine || note !== lastNote) {\n        dotShift = maxShiftMap[noteId];\n      }\n      if (!note.isRest() && line !== lastLine) {\n        if (Math.abs(line % 1) === 0.5) {\n          halfShiftY = 0;\n        } else {\n          halfShiftY = 0.5;\n          if (lastNote !== null && !lastNote.isRest() && lastLine !== null && lastLine - line === 0.5) {\n            halfShiftY = -0.5;\n          } else if (line + halfShiftY === prevDottedSpace) {\n            halfShiftY = -0.5;\n          }\n        }\n      }\n      if (note.isRest()) {\n        dot.dotShiftY += -halfShiftY;\n      } else {\n        dot.dotShiftY = -halfShiftY;\n      }\n      prevDottedSpace = line + halfShiftY;\n      dot.setXShift(dotShift);\n      dotShift += dot.getWidth() + dotSpacing;\n      xWidth = dotShift > xWidth ? dotShift : xWidth;\n      lastLine = line;\n      lastNote = note;\n    }\n    state.rightShift += xWidth;\n    return true;\n  }\n  constructor() {\n    super();\n    this.position = Modifier.Position.RIGHT;\n    this.setText(Glyphs.augmentationDot);\n    this.dotShiftY = 0;\n  }\n  setNote(note) {\n    this.note = note;\n    this.font = note.font;\n    return this;\n  }\n  setDotShiftY(y) {\n    this.dotShiftY = y;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const stave = note.checkStave();\n    const lineSpace = stave.getSpacingBetweenLines();\n    const start = note.getModifierStartXY(this.position, this.index, {\n      forceFlagRight: true\n    });\n    if (isTabNote(note)) {\n      start.y = note.getStemExtents().baseY;\n    }\n    this.x = start.x;\n    this.y = start.y + this.dotShiftY * lineSpace;\n    this.renderText(ctx, 0, 0);\n  }\n}", "map": {"version": 3, "names": ["Glyphs", "Modifier", "isStaveNote", "isTabNote", "RuntimeError", "Dot", "CATEGORY", "getDots", "note", "getModifiersByType", "buildAndAttach", "notes", "options", "all", "i", "keys", "length", "dot", "addModifier", "index", "undefined", "format", "dots", "state", "rightShift", "dotSpacing", "dotList", "maxShiftMap", "getNote", "props", "shift", "checkIndex", "getKeyProps", "getFirstDotPx", "line", "noteId", "getAttribute", "push", "Math", "max", "sort", "a", "b", "dotShift", "xWidth", "lastLine", "lastNote", "prevDottedSpace", "halfShiftY", "isRest", "abs", "dotShiftY", "setXShift", "getWidth", "constructor", "position", "Position", "RIGHT", "setText", "augmentationDot", "setNote", "font", "setDotShiftY", "y", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "stave", "checkStave", "lineSpace", "getSpacingBetweenLines", "start", "getModifierStartXY", "forceFlagRight", "getStemExtents", "baseY", "x", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/dot.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Dot extends Modifier {\n    static get CATEGORY() {\n        return \"Dot\";\n    }\n    static getDots(note) {\n        return note.getModifiersByType(Dot.CATEGORY);\n    }\n    static buildAndAttach(notes, options) {\n        for (const note of notes) {\n            if (options === null || options === void 0 ? void 0 : options.all) {\n                for (let i = 0; i < note.keys.length; i++) {\n                    const dot = new Dot();\n                    note.addModifier(dot, i);\n                }\n            }\n            else if ((options === null || options === void 0 ? void 0 : options.index) !== undefined) {\n                const dot = new Dot();\n                note.addModifier(dot, options === null || options === void 0 ? void 0 : options.index);\n            }\n            else {\n                const dot = new Dot();\n                note.addModifier(dot, 0);\n            }\n        }\n    }\n    static format(dots, state) {\n        const rightShift = state.rightShift;\n        const dotSpacing = 1;\n        if (!dots || dots.length === 0)\n            return false;\n        const dotList = [];\n        const maxShiftMap = {};\n        for (let i = 0; i < dots.length; ++i) {\n            const dot = dots[i];\n            const note = dot.getNote();\n            let props;\n            let shift;\n            if (isStaveNote(note)) {\n                const index = dot.checkIndex();\n                props = note.getKeyProps()[index];\n                shift = note.getFirstDotPx();\n            }\n            else if (isTabNote(note)) {\n                props = { line: 0.5 };\n                shift = rightShift;\n            }\n            else {\n                throw new RuntimeError('Internal', 'Unexpected instance.');\n            }\n            const noteId = note.getAttribute('id');\n            dotList.push({ line: props.line, note, noteId, dot });\n            maxShiftMap[noteId] = Math.max(maxShiftMap[noteId] || shift, shift);\n        }\n        dotList.sort((a, b) => b.line - a.line);\n        let dotShift = rightShift;\n        let xWidth = 0;\n        let lastLine = null;\n        let lastNote = null;\n        let prevDottedSpace = null;\n        let halfShiftY = 0;\n        for (let i = 0; i < dotList.length; ++i) {\n            const { dot, note, noteId, line } = dotList[i];\n            if (line !== lastLine || note !== lastNote) {\n                dotShift = maxShiftMap[noteId];\n            }\n            if (!note.isRest() && line !== lastLine) {\n                if (Math.abs(line % 1) === 0.5) {\n                    halfShiftY = 0;\n                }\n                else {\n                    halfShiftY = 0.5;\n                    if (lastNote !== null && !lastNote.isRest() && lastLine !== null && lastLine - line === 0.5) {\n                        halfShiftY = -0.5;\n                    }\n                    else if (line + halfShiftY === prevDottedSpace) {\n                        halfShiftY = -0.5;\n                    }\n                }\n            }\n            if (note.isRest()) {\n                dot.dotShiftY += -halfShiftY;\n            }\n            else {\n                dot.dotShiftY = -halfShiftY;\n            }\n            prevDottedSpace = line + halfShiftY;\n            dot.setXShift(dotShift);\n            dotShift += dot.getWidth() + dotSpacing;\n            xWidth = dotShift > xWidth ? dotShift : xWidth;\n            lastLine = line;\n            lastNote = note;\n        }\n        state.rightShift += xWidth;\n        return true;\n    }\n    constructor() {\n        super();\n        this.position = Modifier.Position.RIGHT;\n        this.setText(Glyphs.augmentationDot);\n        this.dotShiftY = 0;\n    }\n    setNote(note) {\n        this.note = note;\n        this.font = note.font;\n        return this;\n    }\n    setDotShiftY(y) {\n        this.dotShiftY = y;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const stave = note.checkStave();\n        const lineSpace = stave.getSpacingBetweenLines();\n        const start = note.getModifierStartXY(this.position, this.index, { forceFlagRight: true });\n        if (isTabNote(note)) {\n            start.y = note.getStemExtents().baseY;\n        }\n        this.x = start.x;\n        this.y = start.y + this.dotShiftY * lineSpace;\n        this.renderText(ctx, 0, 0);\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,GAAG,SAASJ,QAAQ,CAAC;EAC9B,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,KAAK;EAChB;EACA,OAAOC,OAAOA,CAACC,IAAI,EAAE;IACjB,OAAOA,IAAI,CAACC,kBAAkB,CAACJ,GAAG,CAACC,QAAQ,CAAC;EAChD;EACA,OAAOI,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAClC,KAAK,MAAMJ,IAAI,IAAIG,KAAK,EAAE;MACtB,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,GAAG,EAAE;QAC/D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UACvC,MAAMG,GAAG,GAAG,IAAIZ,GAAG,CAAC,CAAC;UACrBG,IAAI,CAACU,WAAW,CAACD,GAAG,EAAEH,CAAC,CAAC;QAC5B;MACJ,CAAC,MACI,IAAI,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,KAAK,MAAMC,SAAS,EAAE;QACtF,MAAMH,GAAG,GAAG,IAAIZ,GAAG,CAAC,CAAC;QACrBG,IAAI,CAACU,WAAW,CAACD,GAAG,EAAEL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,KAAK,CAAC;MAC1F,CAAC,MACI;QACD,MAAMF,GAAG,GAAG,IAAIZ,GAAG,CAAC,CAAC;QACrBG,IAAI,CAACU,WAAW,CAACD,GAAG,EAAE,CAAC,CAAC;MAC5B;IACJ;EACJ;EACA,OAAOI,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACvB,MAAMC,UAAU,GAAGD,KAAK,CAACC,UAAU;IACnC,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACN,MAAM,KAAK,CAAC,EAC1B,OAAO,KAAK;IAChB,MAAMU,OAAO,GAAG,EAAE;IAClB,MAAMC,WAAW,GAAG,CAAC,CAAC;IACtB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,IAAI,CAACN,MAAM,EAAE,EAAEF,CAAC,EAAE;MAClC,MAAMG,GAAG,GAAGK,IAAI,CAACR,CAAC,CAAC;MACnB,MAAMN,IAAI,GAAGS,GAAG,CAACW,OAAO,CAAC,CAAC;MAC1B,IAAIC,KAAK;MACT,IAAIC,KAAK;MACT,IAAI5B,WAAW,CAACM,IAAI,CAAC,EAAE;QACnB,MAAMW,KAAK,GAAGF,GAAG,CAACc,UAAU,CAAC,CAAC;QAC9BF,KAAK,GAAGrB,IAAI,CAACwB,WAAW,CAAC,CAAC,CAACb,KAAK,CAAC;QACjCW,KAAK,GAAGtB,IAAI,CAACyB,aAAa,CAAC,CAAC;MAChC,CAAC,MACI,IAAI9B,SAAS,CAACK,IAAI,CAAC,EAAE;QACtBqB,KAAK,GAAG;UAAEK,IAAI,EAAE;QAAI,CAAC;QACrBJ,KAAK,GAAGN,UAAU;MACtB,CAAC,MACI;QACD,MAAM,IAAIpB,YAAY,CAAC,UAAU,EAAE,sBAAsB,CAAC;MAC9D;MACA,MAAM+B,MAAM,GAAG3B,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC;MACtCV,OAAO,CAACW,IAAI,CAAC;QAAEH,IAAI,EAAEL,KAAK,CAACK,IAAI;QAAE1B,IAAI;QAAE2B,MAAM;QAAElB;MAAI,CAAC,CAAC;MACrDU,WAAW,CAACQ,MAAM,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACZ,WAAW,CAACQ,MAAM,CAAC,IAAIL,KAAK,EAAEA,KAAK,CAAC;IACvE;IACAJ,OAAO,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACR,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;IACvC,IAAIS,QAAQ,GAAGnB,UAAU;IACzB,IAAIoB,MAAM,GAAG,CAAC;IACd,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,OAAO,CAACV,MAAM,EAAE,EAAEF,CAAC,EAAE;MACrC,MAAM;QAAEG,GAAG;QAAET,IAAI;QAAE2B,MAAM;QAAED;MAAK,CAAC,GAAGR,OAAO,CAACZ,CAAC,CAAC;MAC9C,IAAIoB,IAAI,KAAKW,QAAQ,IAAIrC,IAAI,KAAKsC,QAAQ,EAAE;QACxCH,QAAQ,GAAGhB,WAAW,CAACQ,MAAM,CAAC;MAClC;MACA,IAAI,CAAC3B,IAAI,CAACyC,MAAM,CAAC,CAAC,IAAIf,IAAI,KAAKW,QAAQ,EAAE;QACrC,IAAIP,IAAI,CAACY,GAAG,CAAChB,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5Bc,UAAU,GAAG,CAAC;QAClB,CAAC,MACI;UACDA,UAAU,GAAG,GAAG;UAChB,IAAIF,QAAQ,KAAK,IAAI,IAAI,CAACA,QAAQ,CAACG,MAAM,CAAC,CAAC,IAAIJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,GAAGX,IAAI,KAAK,GAAG,EAAE;YACzFc,UAAU,GAAG,CAAC,GAAG;UACrB,CAAC,MACI,IAAId,IAAI,GAAGc,UAAU,KAAKD,eAAe,EAAE;YAC5CC,UAAU,GAAG,CAAC,GAAG;UACrB;QACJ;MACJ;MACA,IAAIxC,IAAI,CAACyC,MAAM,CAAC,CAAC,EAAE;QACfhC,GAAG,CAACkC,SAAS,IAAI,CAACH,UAAU;MAChC,CAAC,MACI;QACD/B,GAAG,CAACkC,SAAS,GAAG,CAACH,UAAU;MAC/B;MACAD,eAAe,GAAGb,IAAI,GAAGc,UAAU;MACnC/B,GAAG,CAACmC,SAAS,CAACT,QAAQ,CAAC;MACvBA,QAAQ,IAAI1B,GAAG,CAACoC,QAAQ,CAAC,CAAC,GAAG5B,UAAU;MACvCmB,MAAM,GAAGD,QAAQ,GAAGC,MAAM,GAAGD,QAAQ,GAAGC,MAAM;MAC9CC,QAAQ,GAAGX,IAAI;MACfY,QAAQ,GAAGtC,IAAI;IACnB;IACAe,KAAK,CAACC,UAAU,IAAIoB,MAAM;IAC1B,OAAO,IAAI;EACf;EACAU,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,GAAGtD,QAAQ,CAACuD,QAAQ,CAACC,KAAK;IACvC,IAAI,CAACC,OAAO,CAAC1D,MAAM,CAAC2D,eAAe,CAAC;IACpC,IAAI,CAACR,SAAS,GAAG,CAAC;EACtB;EACAS,OAAOA,CAACpD,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqD,IAAI,GAAGrD,IAAI,CAACqD,IAAI;IACrB,OAAO,IAAI;EACf;EACAC,YAAYA,CAACC,CAAC,EAAE;IACZ,IAAI,CAACZ,SAAS,GAAGY,CAAC;IAClB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAM1D,IAAI,GAAG,IAAI,CAAC2D,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG7D,IAAI,CAAC8D,UAAU,CAAC,CAAC;IAC/B,MAAMC,SAAS,GAAGF,KAAK,CAACG,sBAAsB,CAAC,CAAC;IAChD,MAAMC,KAAK,GAAGjE,IAAI,CAACkE,kBAAkB,CAAC,IAAI,CAACnB,QAAQ,EAAE,IAAI,CAACpC,KAAK,EAAE;MAAEwD,cAAc,EAAE;IAAK,CAAC,CAAC;IAC1F,IAAIxE,SAAS,CAACK,IAAI,CAAC,EAAE;MACjBiE,KAAK,CAACV,CAAC,GAAGvD,IAAI,CAACoE,cAAc,CAAC,CAAC,CAACC,KAAK;IACzC;IACA,IAAI,CAACC,CAAC,GAAGL,KAAK,CAACK,CAAC;IAChB,IAAI,CAACf,CAAC,GAAGU,KAAK,CAACV,CAAC,GAAG,IAAI,CAACZ,SAAS,GAAGoB,SAAS;IAC7C,IAAI,CAACQ,UAAU,CAACd,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}