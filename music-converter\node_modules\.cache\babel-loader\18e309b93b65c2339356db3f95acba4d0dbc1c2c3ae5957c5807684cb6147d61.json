{"ast": null, "code": "import { Bend } from './bend.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Vibrato extends Modifier {\n  static get CATEGORY() {\n    return \"Vibrato\";\n  }\n  static format(vibratos, state, context) {\n    if (!vibratos || vibratos.length === 0) return false;\n    let textLine = state.topTextLine;\n    let width = 0;\n    let shift = state.rightShift - 7;\n    const bends = context.getMembers(Bend.CATEGORY);\n    if (bends && bends.length > 0) {\n      const bendHeight = bends.map(bb => bb.getTextHeight()).reduce((a, b) => a > b ? a : b) / Tables.STAVE_LINE_DISTANCE;\n      textLine = textLine - (bendHeight + 1);\n    } else {\n      state.topTextLine += 1;\n    }\n    for (let i = 0; i < vibratos.length; ++i) {\n      const vibrato = vibratos[i];\n      vibrato.setXShift(shift);\n      vibrato.setTextLine(textLine);\n      width += vibrato.getWidth();\n      shift += width;\n    }\n    state.rightShift += width;\n    return true;\n  }\n  constructor() {\n    super();\n    this.position = Modifier.Position.RIGHT;\n    this.renderOptions = {\n      code: 0xeab0,\n      width: 20\n    };\n    this.setVibratoWidth(this.renderOptions.width);\n  }\n  setVibratoWidth(width) {\n    this.renderOptions.width = width;\n    this.text = String.fromCodePoint(this.renderOptions.code);\n    const myWidth = this.getWidth();\n    if (!myWidth) {\n      throw new RuntimeError('Cannot set vibrato width if width is 0');\n    }\n    const items = Math.round(this.renderOptions.width / myWidth);\n    for (let i = 1; i < items; i++) {\n      this.text += String.fromCodePoint(this.renderOptions.code);\n    }\n    return this;\n  }\n  setVibratoCode(code) {\n    this.renderOptions.code = code;\n    return this.setVibratoWidth(this.renderOptions.width);\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(Modifier.Position.RIGHT, this.index);\n    const vx = start.x;\n    const vy = note.getYForTopText(this.textLine) + 5;\n    this.renderText(ctx, vx, vy);\n  }\n}", "map": {"version": 3, "names": ["Bend", "Modifier", "Tables", "RuntimeError", "Vibrato", "CATEGORY", "format", "vibratos", "state", "context", "length", "textLine", "topTextLine", "width", "shift", "rightShift", "bends", "getMembers", "bendHeight", "map", "bb", "getTextHeight", "reduce", "a", "b", "STAVE_LINE_DISTANCE", "i", "vibrato", "setXShift", "setTextLine", "getWidth", "constructor", "position", "Position", "RIGHT", "renderOptions", "code", "setVibratoWidth", "text", "String", "fromCodePoint", "myWidth", "items", "Math", "round", "setVibratoCode", "draw", "ctx", "checkContext", "note", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "index", "vx", "x", "vy", "getYForTopText", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/vibrato.js"], "sourcesContent": ["import { Bend } from './bend.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Vibrato extends Modifier {\n    static get CATEGORY() {\n        return \"Vibrato\";\n    }\n    static format(vibratos, state, context) {\n        if (!vibratos || vibratos.length === 0)\n            return false;\n        let textLine = state.topTextLine;\n        let width = 0;\n        let shift = state.rightShift - 7;\n        const bends = context.getMembers(Bend.CATEGORY);\n        if (bends && bends.length > 0) {\n            const bendHeight = bends.map((bb) => bb.getTextHeight()).reduce((a, b) => (a > b ? a : b)) / Tables.STAVE_LINE_DISTANCE;\n            textLine = textLine - (bendHeight + 1);\n        }\n        else {\n            state.topTextLine += 1;\n        }\n        for (let i = 0; i < vibratos.length; ++i) {\n            const vibrato = vibratos[i];\n            vibrato.setXShift(shift);\n            vibrato.setTextLine(textLine);\n            width += vibrato.getWidth();\n            shift += width;\n        }\n        state.rightShift += width;\n        return true;\n    }\n    constructor() {\n        super();\n        this.position = Modifier.Position.RIGHT;\n        this.renderOptions = {\n            code: 0xeab0,\n            width: 20,\n        };\n        this.setVibratoWidth(this.renderOptions.width);\n    }\n    setVibratoWidth(width) {\n        this.renderOptions.width = width;\n        this.text = String.fromCodePoint(this.renderOptions.code);\n        const myWidth = this.getWidth();\n        if (!myWidth) {\n            throw new RuntimeError('Cannot set vibrato width if width is 0');\n        }\n        const items = Math.round(this.renderOptions.width / myWidth);\n        for (let i = 1; i < items; i++) {\n            this.text += String.fromCodePoint(this.renderOptions.code);\n        }\n        return this;\n    }\n    setVibratoCode(code) {\n        this.renderOptions.code = code;\n        return this.setVibratoWidth(this.renderOptions.width);\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(Modifier.Position.RIGHT, this.index);\n        const vx = start.x;\n        const vy = note.getYForTopText(this.textLine) + 5;\n        this.renderText(ctx, vx, vy);\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,OAAO,SAASH,QAAQ,CAAC;EAClC,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACA,OAAOC,MAAMA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACpC,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAK,CAAC,EAClC,OAAO,KAAK;IAChB,IAAIC,QAAQ,GAAGH,KAAK,CAACI,WAAW;IAChC,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAGN,KAAK,CAACO,UAAU,GAAG,CAAC;IAChC,MAAMC,KAAK,GAAGP,OAAO,CAACQ,UAAU,CAACjB,IAAI,CAACK,QAAQ,CAAC;IAC/C,IAAIW,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMQ,UAAU,GAAGF,KAAK,CAACG,GAAG,CAAEC,EAAE,IAAKA,EAAE,CAACC,aAAa,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC,GAAGtB,MAAM,CAACuB,mBAAmB;MACvHd,QAAQ,GAAGA,QAAQ,IAAIO,UAAU,GAAG,CAAC,CAAC;IAC1C,CAAC,MACI;MACDV,KAAK,CAACI,WAAW,IAAI,CAAC;IAC1B;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,QAAQ,CAACG,MAAM,EAAE,EAAEgB,CAAC,EAAE;MACtC,MAAMC,OAAO,GAAGpB,QAAQ,CAACmB,CAAC,CAAC;MAC3BC,OAAO,CAACC,SAAS,CAACd,KAAK,CAAC;MACxBa,OAAO,CAACE,WAAW,CAAClB,QAAQ,CAAC;MAC7BE,KAAK,IAAIc,OAAO,CAACG,QAAQ,CAAC,CAAC;MAC3BhB,KAAK,IAAID,KAAK;IAClB;IACAL,KAAK,CAACO,UAAU,IAAIF,KAAK;IACzB,OAAO,IAAI;EACf;EACAkB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,GAAG/B,QAAQ,CAACgC,QAAQ,CAACC,KAAK;IACvC,IAAI,CAACC,aAAa,GAAG;MACjBC,IAAI,EAAE,MAAM;MACZvB,KAAK,EAAE;IACX,CAAC;IACD,IAAI,CAACwB,eAAe,CAAC,IAAI,CAACF,aAAa,CAACtB,KAAK,CAAC;EAClD;EACAwB,eAAeA,CAACxB,KAAK,EAAE;IACnB,IAAI,CAACsB,aAAa,CAACtB,KAAK,GAAGA,KAAK;IAChC,IAAI,CAACyB,IAAI,GAAGC,MAAM,CAACC,aAAa,CAAC,IAAI,CAACL,aAAa,CAACC,IAAI,CAAC;IACzD,MAAMK,OAAO,GAAG,IAAI,CAACX,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACW,OAAO,EAAE;MACV,MAAM,IAAItC,YAAY,CAAC,wCAAwC,CAAC;IACpE;IACA,MAAMuC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACT,aAAa,CAACtB,KAAK,GAAG4B,OAAO,CAAC;IAC5D,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,KAAK,EAAEhB,CAAC,EAAE,EAAE;MAC5B,IAAI,CAACY,IAAI,IAAIC,MAAM,CAACC,aAAa,CAAC,IAAI,CAACL,aAAa,CAACC,IAAI,CAAC;IAC9D;IACA,OAAO,IAAI;EACf;EACAS,cAAcA,CAACT,IAAI,EAAE;IACjB,IAAI,CAACD,aAAa,CAACC,IAAI,GAAGA,IAAI;IAC9B,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,CAACF,aAAa,CAACtB,KAAK,CAAC;EACzD;EACAiC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMC,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGH,IAAI,CAACI,kBAAkB,CAACpD,QAAQ,CAACgC,QAAQ,CAACC,KAAK,EAAE,IAAI,CAACoB,KAAK,CAAC;IAC1E,MAAMC,EAAE,GAAGH,KAAK,CAACI,CAAC;IAClB,MAAMC,EAAE,GAAGR,IAAI,CAACS,cAAc,CAAC,IAAI,CAAC/C,QAAQ,CAAC,GAAG,CAAC;IACjD,IAAI,CAACgD,UAAU,CAACZ,GAAG,EAAEQ,EAAE,EAAEE,EAAE,CAAC;EAChC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}