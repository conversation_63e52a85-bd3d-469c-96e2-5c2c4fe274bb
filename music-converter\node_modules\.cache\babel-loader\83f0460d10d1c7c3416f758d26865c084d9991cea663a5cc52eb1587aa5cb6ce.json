{"ast": null, "code": "import { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Tables } from './tables.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (Stem.DEBUG) log('VexFlow.Stem', args);\n}\nexport class Stem extends Element {\n  static get CATEGORY() {\n    return \"Stem\";\n  }\n  static get UP() {\n    return 1;\n  }\n  static get DOWN() {\n    return -1;\n  }\n  static get WIDTH() {\n    return Tables.STEM_WIDTH;\n  }\n  static get HEIGHT() {\n    return Tables.STEM_HEIGHT;\n  }\n  constructor(options) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    super();\n    this.stemUpYOffset = 0;\n    this.stemDownYOffset = 0;\n    this.stemUpYBaseOffset = 0;\n    this.stemDownYBaseOffset = 0;\n    this.xBegin = (_a = options === null || options === void 0 ? void 0 : options.xBegin) !== null && _a !== void 0 ? _a : 0;\n    this.xEnd = (_b = options === null || options === void 0 ? void 0 : options.xEnd) !== null && _b !== void 0 ? _b : 0;\n    this.yTop = (_c = options === null || options === void 0 ? void 0 : options.yTop) !== null && _c !== void 0 ? _c : 0;\n    this.yBottom = (_d = options === null || options === void 0 ? void 0 : options.yBottom) !== null && _d !== void 0 ? _d : 0;\n    this.stemExtension = (_e = options === null || options === void 0 ? void 0 : options.stemExtension) !== null && _e !== void 0 ? _e : 0;\n    this.stemDirection = (_f = options === null || options === void 0 ? void 0 : options.stemDirection) !== null && _f !== void 0 ? _f : 0;\n    this.hide = (options === null || options === void 0 ? void 0 : options.hide) || false;\n    this.isStemlet = (options === null || options === void 0 ? void 0 : options.isStemlet) || false;\n    this.stemletHeight = (_g = options === null || options === void 0 ? void 0 : options.stemletHeight) !== null && _g !== void 0 ? _g : 0;\n    this.renderHeightAdjustment = 0;\n    this.setOptions(options);\n  }\n  setOptions(options) {\n    var _a, _b, _c, _d;\n    this.stemUpYOffset = (_a = options === null || options === void 0 ? void 0 : options.stemUpYOffset) !== null && _a !== void 0 ? _a : 0;\n    this.stemDownYOffset = (_b = options === null || options === void 0 ? void 0 : options.stemDownYOffset) !== null && _b !== void 0 ? _b : 0;\n    this.stemUpYBaseOffset = (_c = options === null || options === void 0 ? void 0 : options.stemUpYBaseOffset) !== null && _c !== void 0 ? _c : 0;\n    this.stemDownYBaseOffset = (_d = options === null || options === void 0 ? void 0 : options.stemDownYBaseOffset) !== null && _d !== void 0 ? _d : 0;\n  }\n  setNoteHeadXBounds(xBegin, xEnd) {\n    this.xBegin = xBegin;\n    this.xEnd = xEnd;\n    return this;\n  }\n  setDirection(direction) {\n    this.stemDirection = direction;\n  }\n  setExtension(ext) {\n    this.stemExtension = ext;\n  }\n  getExtension() {\n    return this.stemExtension;\n  }\n  setYBounds(yTop, yBottom) {\n    this.yTop = yTop;\n    this.yBottom = yBottom;\n  }\n  getHeight() {\n    const yOffset = this.stemDirection === Stem.UP ? this.stemUpYOffset : this.stemDownYOffset;\n    const unsignedHeight = this.yBottom - this.yTop + (Stem.HEIGHT - yOffset + this.stemExtension);\n    return unsignedHeight * this.stemDirection;\n  }\n  getBoundingBox() {\n    throw new RuntimeError('NotImplemented', 'getBoundingBox() not implemented.');\n  }\n  getExtents() {\n    const isStemUp = this.stemDirection === Stem.UP;\n    const ys = [this.yTop, this.yBottom];\n    const stemHeight = Stem.HEIGHT + this.stemExtension;\n    const innerMostNoteheadY = (isStemUp ? Math.min : Math.max)(...ys);\n    const outerMostNoteheadY = (isStemUp ? Math.max : Math.min)(...ys);\n    const stemTipY = innerMostNoteheadY + stemHeight * -this.stemDirection;\n    return {\n      topY: stemTipY,\n      baseY: outerMostNoteheadY\n    };\n  }\n  setVisibility(isVisible) {\n    this.hide = !isVisible;\n    return this;\n  }\n  setStemlet(isStemlet, stemletHeight) {\n    this.isStemlet = isStemlet;\n    this.stemletHeight = stemletHeight;\n    return this;\n  }\n  adjustHeightForFlag() {\n    this.renderHeightAdjustment = Metrics.get('Stem.heightAdjustmentForFlag', -3);\n  }\n  adjustHeightForBeam() {\n    this.renderHeightAdjustment = -Stem.WIDTH / 2;\n  }\n  draw() {\n    this.setRendered();\n    if (this.hide) return;\n    const ctx = this.checkContext();\n    let stemX;\n    let stemY;\n    const stemDirection = this.stemDirection;\n    let yBaseOffset = 0;\n    if (stemDirection === Stem.DOWN) {\n      stemX = this.xBegin;\n      stemY = this.yTop + this.stemDownYOffset;\n      yBaseOffset = this.stemDownYBaseOffset;\n    } else {\n      stemX = this.xEnd;\n      stemY = this.yBottom - this.stemUpYOffset;\n      yBaseOffset = this.stemUpYBaseOffset;\n    }\n    const stemHeight = this.getHeight();\n    L('Rendering stem - ', 'Top Y: ', this.yTop, 'Bottom Y: ', this.yBottom);\n    const stemletYOffset = this.isStemlet ? stemHeight - this.stemletHeight * this.stemDirection : 0;\n    ctx.openGroup('stem', this.getAttribute('id'));\n    ctx.beginPath();\n    ctx.setLineWidth(Stem.WIDTH);\n    ctx.moveTo(stemX, stemY - stemletYOffset + yBaseOffset);\n    ctx.lineTo(stemX, stemY - stemHeight - this.renderHeightAdjustment * stemDirection);\n    ctx.stroke();\n    ctx.closeGroup();\n  }\n}\nStem.DEBUG = false;", "map": {"version": 3, "names": ["Element", "Metrics", "Tables", "log", "RuntimeError", "L", "args", "<PERSON><PERSON>", "DEBUG", "CATEGORY", "UP", "DOWN", "WIDTH", "STEM_WIDTH", "HEIGHT", "STEM_HEIGHT", "constructor", "options", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "stemUpYOffset", "stemDownYOffset", "stemUpYBaseOffset", "stemDownYBaseOffset", "xBegin", "xEnd", "yTop", "yBottom", "stemExtension", "stemDirection", "hide", "isStemlet", "stemletHeight", "renderHeightAdjustment", "setOptions", "setNoteHeadXBounds", "setDirection", "direction", "setExtension", "ext", "getExtension", "setYBounds", "getHeight", "yOffset", "unsignedHeight", "getBoundingBox", "getExtents", "isStemUp", "ys", "stemHeight", "innerMostNoteheadY", "Math", "min", "max", "outerMostNoteheadY", "stemTipY", "topY", "baseY", "setVisibility", "isVisible", "setStemlet", "adjustHeightForFlag", "get", "adjustHeightForBeam", "draw", "setRendered", "ctx", "checkContext", "stemX", "stemY", "yBaseOffset", "stemletYOffset", "openGroup", "getAttribute", "beginPath", "setLineWidth", "moveTo", "lineTo", "stroke", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stem.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Tables } from './tables.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (Stem.DEBUG)\n        log('VexFlow.Stem', args);\n}\nexport class Stem extends Element {\n    static get CATEGORY() {\n        return \"Stem\";\n    }\n    static get UP() {\n        return 1;\n    }\n    static get DOWN() {\n        return -1;\n    }\n    static get WIDTH() {\n        return Tables.STEM_WIDTH;\n    }\n    static get HEIGHT() {\n        return Tables.STEM_HEIGHT;\n    }\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        super();\n        this.stemUpYOffset = 0;\n        this.stemDownYOffset = 0;\n        this.stemUpYBaseOffset = 0;\n        this.stemDownYBaseOffset = 0;\n        this.xBegin = (_a = options === null || options === void 0 ? void 0 : options.xBegin) !== null && _a !== void 0 ? _a : 0;\n        this.xEnd = (_b = options === null || options === void 0 ? void 0 : options.xEnd) !== null && _b !== void 0 ? _b : 0;\n        this.yTop = (_c = options === null || options === void 0 ? void 0 : options.yTop) !== null && _c !== void 0 ? _c : 0;\n        this.yBottom = (_d = options === null || options === void 0 ? void 0 : options.yBottom) !== null && _d !== void 0 ? _d : 0;\n        this.stemExtension = (_e = options === null || options === void 0 ? void 0 : options.stemExtension) !== null && _e !== void 0 ? _e : 0;\n        this.stemDirection = (_f = options === null || options === void 0 ? void 0 : options.stemDirection) !== null && _f !== void 0 ? _f : 0;\n        this.hide = (options === null || options === void 0 ? void 0 : options.hide) || false;\n        this.isStemlet = (options === null || options === void 0 ? void 0 : options.isStemlet) || false;\n        this.stemletHeight = (_g = options === null || options === void 0 ? void 0 : options.stemletHeight) !== null && _g !== void 0 ? _g : 0;\n        this.renderHeightAdjustment = 0;\n        this.setOptions(options);\n    }\n    setOptions(options) {\n        var _a, _b, _c, _d;\n        this.stemUpYOffset = (_a = options === null || options === void 0 ? void 0 : options.stemUpYOffset) !== null && _a !== void 0 ? _a : 0;\n        this.stemDownYOffset = (_b = options === null || options === void 0 ? void 0 : options.stemDownYOffset) !== null && _b !== void 0 ? _b : 0;\n        this.stemUpYBaseOffset = (_c = options === null || options === void 0 ? void 0 : options.stemUpYBaseOffset) !== null && _c !== void 0 ? _c : 0;\n        this.stemDownYBaseOffset = (_d = options === null || options === void 0 ? void 0 : options.stemDownYBaseOffset) !== null && _d !== void 0 ? _d : 0;\n    }\n    setNoteHeadXBounds(xBegin, xEnd) {\n        this.xBegin = xBegin;\n        this.xEnd = xEnd;\n        return this;\n    }\n    setDirection(direction) {\n        this.stemDirection = direction;\n    }\n    setExtension(ext) {\n        this.stemExtension = ext;\n    }\n    getExtension() {\n        return this.stemExtension;\n    }\n    setYBounds(yTop, yBottom) {\n        this.yTop = yTop;\n        this.yBottom = yBottom;\n    }\n    getHeight() {\n        const yOffset = this.stemDirection === Stem.UP ? this.stemUpYOffset : this.stemDownYOffset;\n        const unsignedHeight = this.yBottom - this.yTop + (Stem.HEIGHT - yOffset + this.stemExtension);\n        return unsignedHeight * this.stemDirection;\n    }\n    getBoundingBox() {\n        throw new RuntimeError('NotImplemented', 'getBoundingBox() not implemented.');\n    }\n    getExtents() {\n        const isStemUp = this.stemDirection === Stem.UP;\n        const ys = [this.yTop, this.yBottom];\n        const stemHeight = Stem.HEIGHT + this.stemExtension;\n        const innerMostNoteheadY = (isStemUp ? Math.min : Math.max)(...ys);\n        const outerMostNoteheadY = (isStemUp ? Math.max : Math.min)(...ys);\n        const stemTipY = innerMostNoteheadY + stemHeight * -this.stemDirection;\n        return { topY: stemTipY, baseY: outerMostNoteheadY };\n    }\n    setVisibility(isVisible) {\n        this.hide = !isVisible;\n        return this;\n    }\n    setStemlet(isStemlet, stemletHeight) {\n        this.isStemlet = isStemlet;\n        this.stemletHeight = stemletHeight;\n        return this;\n    }\n    adjustHeightForFlag() {\n        this.renderHeightAdjustment = Metrics.get('Stem.heightAdjustmentForFlag', -3);\n    }\n    adjustHeightForBeam() {\n        this.renderHeightAdjustment = -Stem.WIDTH / 2;\n    }\n    draw() {\n        this.setRendered();\n        if (this.hide)\n            return;\n        const ctx = this.checkContext();\n        let stemX;\n        let stemY;\n        const stemDirection = this.stemDirection;\n        let yBaseOffset = 0;\n        if (stemDirection === Stem.DOWN) {\n            stemX = this.xBegin;\n            stemY = this.yTop + this.stemDownYOffset;\n            yBaseOffset = this.stemDownYBaseOffset;\n        }\n        else {\n            stemX = this.xEnd;\n            stemY = this.yBottom - this.stemUpYOffset;\n            yBaseOffset = this.stemUpYBaseOffset;\n        }\n        const stemHeight = this.getHeight();\n        L('Rendering stem - ', 'Top Y: ', this.yTop, 'Bottom Y: ', this.yBottom);\n        const stemletYOffset = this.isStemlet ? stemHeight - this.stemletHeight * this.stemDirection : 0;\n        ctx.openGroup('stem', this.getAttribute('id'));\n        ctx.beginPath();\n        ctx.setLineWidth(Stem.WIDTH);\n        ctx.moveTo(stemX, stemY - stemletYOffset + yBaseOffset);\n        ctx.lineTo(stemX, stemY - stemHeight - this.renderHeightAdjustment * stemDirection);\n        ctx.stroke();\n        ctx.closeGroup();\n    }\n}\nStem.DEBUG = false;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,IAAI,CAACC,KAAK,EACVL,GAAG,CAAC,cAAc,EAAEG,IAAI,CAAC;AACjC;AACA,OAAO,MAAMC,IAAI,SAASP,OAAO,CAAC;EAC9B,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACA,WAAWC,EAAEA,CAAA,EAAG;IACZ,OAAO,CAAC;EACZ;EACA,WAAWC,IAAIA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC;EACb;EACA,WAAWC,KAAKA,CAAA,EAAG;IACf,OAAOV,MAAM,CAACW,UAAU;EAC5B;EACA,WAAWC,MAAMA,CAAA,EAAG;IAChB,OAAOZ,MAAM,CAACa,WAAW;EAC7B;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,MAAM,GAAG,CAACX,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACxH,IAAI,CAACY,IAAI,GAAG,CAACX,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,IAAI,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACpH,IAAI,CAACY,IAAI,GAAG,CAACX,EAAE,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,IAAI,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACpH,IAAI,CAACY,OAAO,GAAG,CAACX,EAAE,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC1H,IAAI,CAACY,aAAa,GAAG,CAACX,EAAE,GAAGL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,aAAa,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACtI,IAAI,CAACY,aAAa,GAAG,CAACX,EAAE,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,aAAa,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACtI,IAAI,CAACY,IAAI,GAAG,CAAClB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkB,IAAI,KAAK,KAAK;IACrF,IAAI,CAACC,SAAS,GAAG,CAACnB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB,SAAS,KAAK,KAAK;IAC/F,IAAI,CAACC,aAAa,GAAG,CAACb,EAAE,GAAGP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,aAAa,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACtI,IAAI,CAACc,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACC,UAAU,CAACtB,OAAO,CAAC;EAC5B;EACAsB,UAAUA,CAACtB,OAAO,EAAE;IAChB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,IAAI,CAACI,aAAa,GAAG,CAACP,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,aAAa,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACtI,IAAI,CAACQ,eAAe,GAAG,CAACP,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACS,eAAe,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC1I,IAAI,CAACQ,iBAAiB,GAAG,CAACP,EAAE,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACU,iBAAiB,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC9I,IAAI,CAACQ,mBAAmB,GAAG,CAACP,EAAE,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,mBAAmB,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;EACtJ;EACAmB,kBAAkBA,CAACX,MAAM,EAAEC,IAAI,EAAE;IAC7B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAW,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACR,aAAa,GAAGQ,SAAS;EAClC;EACAC,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAACX,aAAa,GAAGW,GAAG;EAC5B;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACZ,aAAa;EAC7B;EACAa,UAAUA,CAACf,IAAI,EAAEC,OAAO,EAAE;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAe,SAASA,CAAA,EAAG;IACR,MAAMC,OAAO,GAAG,IAAI,CAACd,aAAa,KAAK3B,IAAI,CAACG,EAAE,GAAG,IAAI,CAACe,aAAa,GAAG,IAAI,CAACC,eAAe;IAC1F,MAAMuB,cAAc,GAAG,IAAI,CAACjB,OAAO,GAAG,IAAI,CAACD,IAAI,IAAIxB,IAAI,CAACO,MAAM,GAAGkC,OAAO,GAAG,IAAI,CAACf,aAAa,CAAC;IAC9F,OAAOgB,cAAc,GAAG,IAAI,CAACf,aAAa;EAC9C;EACAgB,cAAcA,CAAA,EAAG;IACb,MAAM,IAAI9C,YAAY,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;EACjF;EACA+C,UAAUA,CAAA,EAAG;IACT,MAAMC,QAAQ,GAAG,IAAI,CAAClB,aAAa,KAAK3B,IAAI,CAACG,EAAE;IAC/C,MAAM2C,EAAE,GAAG,CAAC,IAAI,CAACtB,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC;IACpC,MAAMsB,UAAU,GAAG/C,IAAI,CAACO,MAAM,GAAG,IAAI,CAACmB,aAAa;IACnD,MAAMsB,kBAAkB,GAAG,CAACH,QAAQ,GAAGI,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,GAAG,EAAE,GAAGL,EAAE,CAAC;IAClE,MAAMM,kBAAkB,GAAG,CAACP,QAAQ,GAAGI,IAAI,CAACE,GAAG,GAAGF,IAAI,CAACC,GAAG,EAAE,GAAGJ,EAAE,CAAC;IAClE,MAAMO,QAAQ,GAAGL,kBAAkB,GAAGD,UAAU,GAAG,CAAC,IAAI,CAACpB,aAAa;IACtE,OAAO;MAAE2B,IAAI,EAAED,QAAQ;MAAEE,KAAK,EAAEH;IAAmB,CAAC;EACxD;EACAI,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAAC7B,IAAI,GAAG,CAAC6B,SAAS;IACtB,OAAO,IAAI;EACf;EACAC,UAAUA,CAAC7B,SAAS,EAAEC,aAAa,EAAE;IACjC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACf;EACA6B,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC5B,sBAAsB,GAAGrC,OAAO,CAACkE,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;EACjF;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC9B,sBAAsB,GAAG,CAAC/B,IAAI,CAACK,KAAK,GAAG,CAAC;EACjD;EACAyD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAACnC,IAAI,EACT;IACJ,MAAMoC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAIC,KAAK;IACT,IAAIC,KAAK;IACT,MAAMxC,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,IAAIyC,WAAW,GAAG,CAAC;IACnB,IAAIzC,aAAa,KAAK3B,IAAI,CAACI,IAAI,EAAE;MAC7B8D,KAAK,GAAG,IAAI,CAAC5C,MAAM;MACnB6C,KAAK,GAAG,IAAI,CAAC3C,IAAI,GAAG,IAAI,CAACL,eAAe;MACxCiD,WAAW,GAAG,IAAI,CAAC/C,mBAAmB;IAC1C,CAAC,MACI;MACD6C,KAAK,GAAG,IAAI,CAAC3C,IAAI;MACjB4C,KAAK,GAAG,IAAI,CAAC1C,OAAO,GAAG,IAAI,CAACP,aAAa;MACzCkD,WAAW,GAAG,IAAI,CAAChD,iBAAiB;IACxC;IACA,MAAM2B,UAAU,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IACnC1C,CAAC,CAAC,mBAAmB,EAAE,SAAS,EAAE,IAAI,CAAC0B,IAAI,EAAE,YAAY,EAAE,IAAI,CAACC,OAAO,CAAC;IACxE,MAAM4C,cAAc,GAAG,IAAI,CAACxC,SAAS,GAAGkB,UAAU,GAAG,IAAI,CAACjB,aAAa,GAAG,IAAI,CAACH,aAAa,GAAG,CAAC;IAChGqC,GAAG,CAACM,SAAS,CAAC,MAAM,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9CP,GAAG,CAACQ,SAAS,CAAC,CAAC;IACfR,GAAG,CAACS,YAAY,CAACzE,IAAI,CAACK,KAAK,CAAC;IAC5B2D,GAAG,CAACU,MAAM,CAACR,KAAK,EAAEC,KAAK,GAAGE,cAAc,GAAGD,WAAW,CAAC;IACvDJ,GAAG,CAACW,MAAM,CAACT,KAAK,EAAEC,KAAK,GAAGpB,UAAU,GAAG,IAAI,CAAChB,sBAAsB,GAAGJ,aAAa,CAAC;IACnFqC,GAAG,CAACY,MAAM,CAAC,CAAC;IACZZ,GAAG,CAACa,UAAU,CAAC,CAAC;EACpB;AACJ;AACA7E,IAAI,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}