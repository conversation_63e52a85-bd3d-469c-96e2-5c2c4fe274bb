{"ast": null, "code": "export class RuntimeError extends Error {\n  constructor(code, message = '') {\n    super('[RuntimeError] ' + code + ': ' + message);\n    this.code = code;\n  }\n}\nexport function globalObject() {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  }\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  return Function('return this')();\n}\nexport function defined(x, code = 'undefined', message = '') {\n  if (x === undefined) {\n    throw new RuntimeError(code, message);\n  }\n  return x;\n}\nexport function log(block, ...args) {\n  if (!args) return;\n  const line = Array.prototype.slice.call(args).join(' ');\n  globalObject().console.log(block + ': ' + line);\n}\nexport function warn(...args) {\n  const line = args.join(' ');\n  const err = new Error();\n  globalObject().console.log('Warning: ', line, err.stack);\n}\nfunction roundN(x, n) {\n  return x % n >= n / 2 ? parseInt(`${x / n}`, 10) * n + n : parseInt(`${x / n}`, 10) * n;\n}\nexport function midLine(a, b) {\n  let midLine = b + (a - b) / 2;\n  if (midLine % 2 > 0) {\n    midLine = roundN(midLine * 10, 5) / 10;\n  }\n  return midLine;\n}\nexport function prefix(text) {\n  return `vf-${text}`;\n}\nexport function upperFirst(s = '') {\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\nexport function normalizeAngle(a) {\n  a = a % (2 * Math.PI);\n  if (a < 0) {\n    a += 2 * Math.PI;\n  }\n  return a;\n}\nexport function sumArray(arr) {\n  return arr.reduce((a, b) => a + b, 0);\n}", "map": {"version": 3, "names": ["RuntimeError", "Error", "constructor", "code", "message", "globalObject", "globalThis", "self", "window", "global", "Function", "defined", "x", "undefined", "log", "block", "args", "line", "Array", "prototype", "slice", "call", "join", "console", "warn", "err", "stack", "roundN", "n", "parseInt", "midLine", "a", "b", "prefix", "text", "upperFirst", "s", "char<PERSON>t", "toUpperCase", "normalizeAngle", "Math", "PI", "sumArray", "arr", "reduce"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/util.js"], "sourcesContent": ["export class RuntimeError extends Error {\n    constructor(code, message = '') {\n        super('[RuntimeError] ' + code + ': ' + message);\n        this.code = code;\n    }\n}\nexport function globalObject() {\n    if (typeof globalThis !== 'undefined') {\n        return globalThis;\n    }\n    if (typeof self !== 'undefined') {\n        return self;\n    }\n    if (typeof window !== 'undefined') {\n        return window;\n    }\n    if (typeof global !== 'undefined') {\n        return global;\n    }\n    return Function('return this')();\n}\nexport function defined(x, code = 'undefined', message = '') {\n    if (x === undefined) {\n        throw new RuntimeError(code, message);\n    }\n    return x;\n}\nexport function log(block, ...args) {\n    if (!args)\n        return;\n    const line = Array.prototype.slice.call(args).join(' ');\n    globalObject().console.log(block + ': ' + line);\n}\nexport function warn(...args) {\n    const line = args.join(' ');\n    const err = new Error();\n    globalObject().console.log('Warning: ', line, err.stack);\n}\nfunction roundN(x, n) {\n    return x % n >= n / 2 ? parseInt(`${x / n}`, 10) * n + n : parseInt(`${x / n}`, 10) * n;\n}\nexport function midLine(a, b) {\n    let midLine = b + (a - b) / 2;\n    if (midLine % 2 > 0) {\n        midLine = roundN(midLine * 10, 5) / 10;\n    }\n    return midLine;\n}\nexport function prefix(text) {\n    return `vf-${text}`;\n}\nexport function upperFirst(s = '') {\n    return s.charAt(0).toUpperCase() + s.slice(1);\n}\nexport function normalizeAngle(a) {\n    a = a % (2 * Math.PI);\n    if (a < 0) {\n        a += 2 * Math.PI;\n    }\n    return a;\n}\nexport function sumArray(arr) {\n    return arr.reduce((a, b) => a + b, 0);\n}\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,SAASC,KAAK,CAAC;EACpCC,WAAWA,CAACC,IAAI,EAAEC,OAAO,GAAG,EAAE,EAAE;IAC5B,KAAK,CAAC,iBAAiB,GAAGD,IAAI,GAAG,IAAI,GAAGC,OAAO,CAAC;IAChD,IAAI,CAACD,IAAI,GAAGA,IAAI;EACpB;AACJ;AACA,OAAO,SAASE,YAAYA,CAAA,EAAG;EAC3B,IAAI,OAAOC,UAAU,KAAK,WAAW,EAAE;IACnC,OAAOA,UAAU;EACrB;EACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,OAAOC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AACpC;AACA,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAET,IAAI,GAAG,WAAW,EAAEC,OAAO,GAAG,EAAE,EAAE;EACzD,IAAIQ,CAAC,KAAKC,SAAS,EAAE;IACjB,MAAM,IAAIb,YAAY,CAACG,IAAI,EAAEC,OAAO,CAAC;EACzC;EACA,OAAOQ,CAAC;AACZ;AACA,OAAO,SAASE,GAAGA,CAACC,KAAK,EAAE,GAAGC,IAAI,EAAE;EAChC,IAAI,CAACA,IAAI,EACL;EACJ,MAAMC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;EACvDjB,YAAY,CAAC,CAAC,CAACkB,OAAO,CAACT,GAAG,CAACC,KAAK,GAAG,IAAI,GAAGE,IAAI,CAAC;AACnD;AACA,OAAO,SAASO,IAAIA,CAAC,GAAGR,IAAI,EAAE;EAC1B,MAAMC,IAAI,GAAGD,IAAI,CAACM,IAAI,CAAC,GAAG,CAAC;EAC3B,MAAMG,GAAG,GAAG,IAAIxB,KAAK,CAAC,CAAC;EACvBI,YAAY,CAAC,CAAC,CAACkB,OAAO,CAACT,GAAG,CAAC,WAAW,EAAEG,IAAI,EAAEQ,GAAG,CAACC,KAAK,CAAC;AAC5D;AACA,SAASC,MAAMA,CAACf,CAAC,EAAEgB,CAAC,EAAE;EAClB,OAAOhB,CAAC,GAAGgB,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGC,QAAQ,CAAC,GAAGjB,CAAC,GAAGgB,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGC,QAAQ,CAAC,GAAGjB,CAAC,GAAGgB,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGA,CAAC;AAC3F;AACA,OAAO,SAASE,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAIF,OAAO,GAAGE,CAAC,GAAG,CAACD,CAAC,GAAGC,CAAC,IAAI,CAAC;EAC7B,IAAIF,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;IACjBA,OAAO,GAAGH,MAAM,CAACG,OAAO,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE;EAC1C;EACA,OAAOA,OAAO;AAClB;AACA,OAAO,SAASG,MAAMA,CAACC,IAAI,EAAE;EACzB,OAAO,MAAMA,IAAI,EAAE;AACvB;AACA,OAAO,SAASC,UAAUA,CAACC,CAAC,GAAG,EAAE,EAAE;EAC/B,OAAOA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,CAAC,CAAChB,KAAK,CAAC,CAAC,CAAC;AACjD;AACA,OAAO,SAASmB,cAAcA,CAACR,CAAC,EAAE;EAC9BA,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAGS,IAAI,CAACC,EAAE,CAAC;EACrB,IAAIV,CAAC,GAAG,CAAC,EAAE;IACPA,CAAC,IAAI,CAAC,GAAGS,IAAI,CAACC,EAAE;EACpB;EACA,OAAOV,CAAC;AACZ;AACA,OAAO,SAASW,QAAQA,CAACC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACC,MAAM,CAAC,CAACb,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}