{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\VexTabBlock.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function updateVexTabBlock(blockId) {\n  const curVexText = document.getElementById(blockId).querySelector(textarea.editor);\n  curVexText.dispatchEvent(new KeyboardEvent(\"keyup\", {\n    bubbles: true,\n    cancelable: true,\n    key: \" \",\n    code: \"Space\"\n  }));\n}\nfunction VexTabBlock({\n  source,\n  id\n}) {\n  _s();\n  const divRef = useRef(null);\n  const originalSourceRef = useRef(source);\n  useEffect(() => {\n    const el = divRef.current;\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\n    const handleInput = e => {\n      const curText = e.target.textContent;\n      originalSourceRef.current = curText;\n      setTimeout(() => {\n        new window.VexTab.Div(el);\n      }, 0);\n    };\n    el.addEventListener('input', handleInput);\n    console.log(\"Original Source: \", originalSourceRef.current);\n    originalSourceRef.current = source;\n    el.textContent = source;\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n    new window.VexTab.Div(el);\n  }, [source, id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    style: {\n      minHeight: '6rem'\n    },\n    editor: \"true\",\n    contentEditable: \"true\",\n    id: id,\n    ref: divRef\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n}\n_s(VexTabBlock, \"KVlM2XxlLuyxsCbVLLNK4aY8Z9o=\");\n_c = VexTabBlock;\nexport default VexTabBlock;\nvar _c;\n$RefreshReg$(_c, \"VexTabBlock\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "updateVexTabBlock", "blockId", "curVexText", "document", "getElementById", "querySelector", "textarea", "editor", "dispatchEvent", "KeyboardEvent", "bubbles", "cancelable", "key", "code", "VexTabBlock", "source", "id", "_s", "divRef", "originalSourceRef", "el", "current", "window", "VexTab", "Div", "handleInput", "e", "curText", "target", "textContent", "setTimeout", "addEventListener", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "className", "style", "minHeight", "contentEditable", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/VexTabBlock.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\n\r\nexport function updateVexTabBlock(blockId) {\r\n  const curVexText = document.getElementById(blockId).querySelector(textarea.editor);\r\n  curVexText.dispatchEvent(new KeyboardEvent(\"keyup\", { bubbles: true, cancelable: true, key: \" \" , code: \"Space\" }));\r\n}\r\n\r\nfunction VexTabBlock({ source, id }) {\r\n  const divRef = useRef(null);\r\n  const originalSourceRef = useRef(source);\r\n\r\n  useEffect(() => {\r\n    const el = divRef.current;\r\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\r\n\r\n    const handleInput = (e) => {\r\n      const curText = e.target.textContent;\r\n      originalSourceRef.current = curText;\r\n\r\n      setTimeout(() => {\r\n        new window.VexTab.Div(el);\r\n      }, 0);\r\n    }\r\n\r\n    el.addEventListener('input', handleInput);\r\n    console.log(\"Original Source: \", originalSourceRef.current);\r\n\r\n    originalSourceRef.current = source;\r\n    el.textContent = source;\r\n    while (el.firstChild) {\r\n      el.removeChild(el.firstChild);\r\n    }\r\n    new window.VexTab.Div(el); \r\n  }, [source, id]);\r\n\r\n  return (\r\n    <div\r\n      className=\"vextab-auto\"\r\n      style={{ minHeight: '6rem' }}\r\n      editor=\"true\"\r\n      contentEditable=\"true\"\r\n      id={id}\r\n      ref={divRef}\r\n    />\r\n  )  \r\n}\r\n\r\nexport default VexTabBlock;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EACzC,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAC,CAACI,aAAa,CAACC,QAAQ,CAACC,MAAM,CAAC;EAClFL,UAAU,CAACM,aAAa,CAAC,IAAIC,aAAa,CAAC,OAAO,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,GAAG,EAAE,GAAG;IAAGC,IAAI,EAAE;EAAQ,CAAC,CAAC,CAAC;AACrH;AAEA,SAASC,WAAWA,CAAC;EAAEC,MAAM;EAAEC;AAAG,CAAC,EAAE;EAAAC,EAAA;EACnC,MAAMC,MAAM,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMsB,iBAAiB,GAAGtB,MAAM,CAACkB,MAAM,CAAC;EAExCnB,SAAS,CAAC,MAAM;IACd,MAAMwB,EAAE,GAAGF,MAAM,CAACG,OAAO;IACzB,IAAI,CAACD,EAAE,IAAI,CAACE,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;IAEjD,MAAMC,WAAW,GAAIC,CAAC,IAAK;MACzB,MAAMC,OAAO,GAAGD,CAAC,CAACE,MAAM,CAACC,WAAW;MACpCV,iBAAiB,CAACE,OAAO,GAAGM,OAAO;MAEnCG,UAAU,CAAC,MAAM;QACf,IAAIR,MAAM,CAACC,MAAM,CAACC,GAAG,CAACJ,EAAE,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAEDA,EAAE,CAACW,gBAAgB,CAAC,OAAO,EAAEN,WAAW,CAAC;IACzCO,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEd,iBAAiB,CAACE,OAAO,CAAC;IAE3DF,iBAAiB,CAACE,OAAO,GAAGN,MAAM;IAClCK,EAAE,CAACS,WAAW,GAAGd,MAAM;IACvB,OAAOK,EAAE,CAACc,UAAU,EAAE;MACpBd,EAAE,CAACe,WAAW,CAACf,EAAE,CAACc,UAAU,CAAC;IAC/B;IACA,IAAIZ,MAAM,CAACC,MAAM,CAACC,GAAG,CAACJ,EAAE,CAAC;EAC3B,CAAC,EAAE,CAACL,MAAM,EAAEC,EAAE,CAAC,CAAC;EAEhB,oBACEjB,OAAA;IACEqC,SAAS,EAAC,aAAa;IACvBC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAO,CAAE;IAC7B/B,MAAM,EAAC,MAAM;IACbgC,eAAe,EAAC,MAAM;IACtBvB,EAAE,EAAEA,EAAG;IACPwB,GAAG,EAAEtB;EAAO;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEN;AAAC3B,EAAA,CAtCQH,WAAW;AAAA+B,EAAA,GAAX/B,WAAW;AAwCpB,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}