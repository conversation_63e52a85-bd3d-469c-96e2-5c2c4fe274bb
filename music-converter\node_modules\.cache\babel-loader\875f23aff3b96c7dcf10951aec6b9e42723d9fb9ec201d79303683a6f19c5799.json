{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\VexTabBlock.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VexTabBlock({\n  source,\n  id,\n  onTextChange\n}) {\n  _s();\n  const divRef = useRef(null);\n\n  // Function to get current text content\n  const getCurrentText = () => {\n    if (divRef.current) {\n      // Try to get text from VexTab editor if it exists\n      const textArea = divRef.current.querySelector('textarea');\n      if (textArea) {\n        return textArea.value;\n      }\n      // Fallback to div text content\n      return divRef.current.textContent || '';\n    }\n    return '';\n  };\n  useEffect(() => {\n    const el = divRef.current;\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\n    console.log(`VexTabBlock ${id || 'unnamed'} content:`, source);\n    el.textContent = source;\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n    new window.VexTab.Div(el);\n  }, [source, id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    style: {\n      minHeight: '6rem'\n    },\n    editor: \"true\",\n    contentEditable: \"true\",\n    id: id,\n    ref: divRef\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n}\n_s(VexTabBlock, \"yu+j3H3uMpPkc7UDcPsbvSQj1vE=\");\n_c = VexTabBlock;\nexport default VexTabBlock;\nvar _c;\n$RefreshReg$(_c, \"VexTabBlock\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "VexTabBlock", "source", "id", "onTextChange", "_s", "divRef", "getCurrentText", "current", "textArea", "querySelector", "value", "textContent", "el", "window", "VexTab", "Div", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "className", "style", "minHeight", "editor", "contentEditable", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/VexTabBlock.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\n\r\nfunction VexTabBlock({ source, id, onTextChange }) {\r\n  const divRef = useRef(null);\r\n\r\n  // Function to get current text content\r\n  const getCurrentText = () => {\r\n    if (divRef.current) {\r\n      // Try to get text from VexTab editor if it exists\r\n      const textArea = divRef.current.querySelector('textarea');\r\n      if (textArea) {\r\n        return textArea.value;\r\n      }\r\n      // Fallback to div text content\r\n      return divRef.current.textContent || '';\r\n    }\r\n    return '';\r\n  };\r\n\r\n  useEffect(() => {\r\n    const el = divRef.current;\r\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\r\n\r\n    console.log(`VexTabBlock ${id || 'unnamed'} content:`, source);\r\n\r\n    el.textContent = source;\r\n    while (el.firstChild) {\r\n      el.removeChild(el.firstChild);\r\n    }\r\n\r\n    new window.VexTab.Div(el);\r\n  }, [source, id]);\r\n\r\n  return (\r\n    <div\r\n      className=\"vextab-auto\"\r\n      style={{ minHeight: '6rem' }}\r\n      editor=\"true\"\r\n      contentEditable=\"true\"\r\n      id={id}\r\n      ref={divRef}\r\n    />\r\n  )  \r\n}\r\n\r\nexport default VexTabBlock;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,WAAWA,CAAC;EAAEC,MAAM;EAAEC,EAAE;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACjD,MAAMC,MAAM,GAAGR,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAID,MAAM,CAACE,OAAO,EAAE;MAClB;MACA,MAAMC,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACE,aAAa,CAAC,UAAU,CAAC;MACzD,IAAID,QAAQ,EAAE;QACZ,OAAOA,QAAQ,CAACE,KAAK;MACvB;MACA;MACA,OAAOL,MAAM,CAACE,OAAO,CAACI,WAAW,IAAI,EAAE;IACzC;IACA,OAAO,EAAE;EACX,CAAC;EAEDf,SAAS,CAAC,MAAM;IACd,MAAMgB,EAAE,GAAGP,MAAM,CAACE,OAAO;IACzB,IAAI,CAACK,EAAE,IAAI,CAACC,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;IAEjDC,OAAO,CAACC,GAAG,CAAC,eAAef,EAAE,IAAI,SAAS,WAAW,EAAED,MAAM,CAAC;IAE9DW,EAAE,CAACD,WAAW,GAAGV,MAAM;IACvB,OAAOW,EAAE,CAACM,UAAU,EAAE;MACpBN,EAAE,CAACO,WAAW,CAACP,EAAE,CAACM,UAAU,CAAC;IAC/B;IAEA,IAAIL,MAAM,CAACC,MAAM,CAACC,GAAG,CAACH,EAAE,CAAC;EAC3B,CAAC,EAAE,CAACX,MAAM,EAAEC,EAAE,CAAC,CAAC;EAEhB,oBACEH,OAAA;IACEqB,SAAS,EAAC,aAAa;IACvBC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAO,CAAE;IAC7BC,MAAM,EAAC,MAAM;IACbC,eAAe,EAAC,MAAM;IACtBtB,EAAE,EAAEA,EAAG;IACPuB,GAAG,EAAEpB;EAAO;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEN;AAACzB,EAAA,CAzCQJ,WAAW;AAAA8B,EAAA,GAAX9B,WAAW;AA2CpB,eAAeA,WAAW;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}