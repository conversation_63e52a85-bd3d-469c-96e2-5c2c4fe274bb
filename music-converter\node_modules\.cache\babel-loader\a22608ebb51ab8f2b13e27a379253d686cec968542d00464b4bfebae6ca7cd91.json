{"ast": null, "code": "import { Beam } from './beam.js';\nimport { Formatter } from './formatter.js';\nimport { Modifier } from './modifier.js';\nimport { StaveNote } from './stavenote.js';\nimport { StaveTie } from './stavetie.js';\nimport { Tables } from './tables.js';\nimport { TabTie } from './tabtie.js';\nimport { isStaveNote } from './typeguard.js';\nimport { log } from './util.js';\nimport { Voice } from './voice.js';\nfunction L(...args) {\n  if (GraceNoteGroup.DEBUG) log('VexFlow.GraceNoteGroup', args);\n}\nexport class GraceNoteGroup extends Modifier {\n  static get CATEGORY() {\n    return \"GraceNoteGroup\";\n  }\n  static format(gracenoteGroups, state) {\n    const groupSpacingStave = 4;\n    const groupSpacingTab = 0;\n    if (!gracenoteGroups || gracenoteGroups.length === 0) return false;\n    const groupList = [];\n    let prevNote = null;\n    let shift = 0;\n    for (let i = 0; i < gracenoteGroups.length; ++i) {\n      const gracenoteGroup = gracenoteGroups[i];\n      const note = gracenoteGroup.getNote();\n      const isStavenote = isStaveNote(note);\n      const spacing = isStavenote ? groupSpacingStave : groupSpacingTab;\n      if (isStavenote && note !== prevNote) {\n        for (let n = 0; n < note.keys.length; ++n) {\n          shift = Math.max(note.getLeftDisplacedHeadPx(), shift);\n        }\n        prevNote = note;\n      }\n      groupList.push({\n        shift: shift,\n        gracenoteGroup,\n        spacing\n      });\n    }\n    let groupShift = groupList[0].shift;\n    let formatWidth;\n    let right = false;\n    let left = false;\n    for (let i = 0; i < groupList.length; ++i) {\n      const gracenoteGroup = groupList[i].gracenoteGroup;\n      if (gracenoteGroup.position === Modifier.Position.RIGHT) right = true;else left = true;\n      gracenoteGroup.preFormat();\n      formatWidth = gracenoteGroup.getWidth() + groupList[i].spacing;\n      groupShift = Math.max(formatWidth, groupShift);\n    }\n    for (let i = 0; i < groupList.length; ++i) {\n      const gracenoteGroup = groupList[i].gracenoteGroup;\n      formatWidth = gracenoteGroup.getWidth() + groupList[i].spacing;\n      gracenoteGroup.setSpacingFromNextModifier(groupShift - Math.min(formatWidth, groupShift) + StaveNote.minNoteheadPadding);\n    }\n    if (right) state.rightShift += groupShift;\n    if (left) state.leftShift += groupShift;\n    return true;\n  }\n  constructor(graceNotes, showSlur) {\n    super();\n    this.preFormatted = false;\n    this.position = Modifier.Position.LEFT;\n    this.graceNotes = graceNotes;\n    this.width = 0;\n    this.showSlur = showSlur;\n    this.slur = undefined;\n    this.voice = new Voice({\n      numBeats: 4,\n      beatValue: 4,\n      resolution: Tables.RESOLUTION\n    }).setStrict(false);\n    this.renderOptions = {\n      slurYShift: 0\n    };\n    this.beams = [];\n    this.voice.addTickables(this.graceNotes);\n    return this;\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    if (!this.formatter) {\n      this.formatter = new Formatter();\n    }\n    this.formatter.joinVoices([this.voice]).format([this.voice], 0, {});\n    this.setWidth(this.formatter.getMinTotalWidth());\n    this.preFormatted = true;\n  }\n  beamNotes(graceNotes) {\n    graceNotes = graceNotes || this.graceNotes;\n    if (graceNotes.length > 1) {\n      const beam = new Beam(graceNotes);\n      beam.renderOptions.beamWidth = 3;\n      beam.renderOptions.partialBeamLength = 4;\n      this.beams.push(beam);\n    }\n    return this;\n  }\n  setWidth(width) {\n    this.width = width;\n    return this;\n  }\n  getWidth() {\n    return this.width + StaveNote.minNoteheadPadding;\n  }\n  getGraceNotes() {\n    return this.graceNotes;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    L('Drawing grace note group for:', note);\n    this.alignSubNotesWithNote(this.getGraceNotes(), note, this.position);\n    this.graceNotes.forEach(graceNote => graceNote.setContext(ctx).drawWithStyle());\n    this.beams.forEach(beam => beam.setContext(ctx).drawWithStyle());\n    if (this.showSlur) {\n      const isStavenote = isStaveNote(note);\n      const TieClass = isStavenote ? StaveTie : TabTie;\n      this.slur = new TieClass({\n        lastNote: this.graceNotes[0],\n        firstNote: note,\n        firstIndexes: [0],\n        lastIndexes: [0]\n      });\n      this.slur.renderOptions.cp2 = 12;\n      this.slur.renderOptions.yShift = (isStavenote ? 7 : 5) + this.renderOptions.slurYShift;\n      this.slur.setContext(ctx).drawWithStyle();\n    }\n  }\n}\nGraceNoteGroup.DEBUG = false;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Modifier", "StaveNote", "StaveTie", "Tables", "<PERSON><PERSON><PERSON><PERSON>", "isStaveNote", "log", "Voice", "L", "args", "GraceNoteGroup", "DEBUG", "CATEGORY", "format", "gracenoteGroups", "state", "groupSpacingStave", "groupSpacingTab", "length", "groupList", "prevNote", "shift", "i", "gracenoteGroup", "note", "getNote", "isStavenote", "spacing", "n", "keys", "Math", "max", "getLeftDisplacedHeadPx", "push", "groupShift", "formatWidth", "right", "left", "position", "Position", "RIGHT", "preFormat", "getWidth", "setSpacingFromNextModifier", "min", "minNoteheadPadding", "rightShift", "leftShift", "constructor", "grace<PERSON>otes", "showSlur", "preFormatted", "LEFT", "width", "slur", "undefined", "voice", "numBeats", "beatValue", "resolution", "RESOLUTION", "setStrict", "renderOptions", "slurYShift", "beams", "addTickables", "formatter", "joinVoices", "<PERSON><PERSON><PERSON><PERSON>", "getMinTotalWidth", "beamNotes", "beam", "beamWidth", "partialBeam<PERSON>ength", "getGraceNotes", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "alignSubNotesWithNote", "for<PERSON>ach", "grace<PERSON>ote", "setContext", "drawWithStyle", "TieClass", "lastNote", "firstNote", "firstIndexes", "lastIndexes", "cp2", "yShift"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/gracenotegroup.js"], "sourcesContent": ["import { Beam } from './beam.js';\nimport { Formatter } from './formatter.js';\nimport { Modifier } from './modifier.js';\nimport { StaveNote } from './stavenote.js';\nimport { StaveTie } from './stavetie.js';\nimport { Tables } from './tables.js';\nimport { TabTie } from './tabtie.js';\nimport { isStaveNote } from './typeguard.js';\nimport { log } from './util.js';\nimport { Voice } from './voice.js';\nfunction L(...args) {\n    if (GraceNoteGroup.DEBUG)\n        log('VexFlow.GraceNoteGroup', args);\n}\nexport class GraceNoteGroup extends Modifier {\n    static get CATEGORY() {\n        return \"GraceNoteGroup\";\n    }\n    static format(gracenoteGroups, state) {\n        const groupSpacingStave = 4;\n        const groupSpacingTab = 0;\n        if (!gracenoteGroups || gracenoteGroups.length === 0)\n            return false;\n        const groupList = [];\n        let prevNote = null;\n        let shift = 0;\n        for (let i = 0; i < gracenoteGroups.length; ++i) {\n            const gracenoteGroup = gracenoteGroups[i];\n            const note = gracenoteGroup.getNote();\n            const isStavenote = isStaveNote(note);\n            const spacing = isStavenote ? groupSpacingStave : groupSpacingTab;\n            if (isStavenote && note !== prevNote) {\n                for (let n = 0; n < note.keys.length; ++n) {\n                    shift = Math.max(note.getLeftDisplacedHeadPx(), shift);\n                }\n                prevNote = note;\n            }\n            groupList.push({ shift: shift, gracenoteGroup, spacing });\n        }\n        let groupShift = groupList[0].shift;\n        let formatWidth;\n        let right = false;\n        let left = false;\n        for (let i = 0; i < groupList.length; ++i) {\n            const gracenoteGroup = groupList[i].gracenoteGroup;\n            if (gracenoteGroup.position === Modifier.Position.RIGHT)\n                right = true;\n            else\n                left = true;\n            gracenoteGroup.preFormat();\n            formatWidth = gracenoteGroup.getWidth() + groupList[i].spacing;\n            groupShift = Math.max(formatWidth, groupShift);\n        }\n        for (let i = 0; i < groupList.length; ++i) {\n            const gracenoteGroup = groupList[i].gracenoteGroup;\n            formatWidth = gracenoteGroup.getWidth() + groupList[i].spacing;\n            gracenoteGroup.setSpacingFromNextModifier(groupShift - Math.min(formatWidth, groupShift) + StaveNote.minNoteheadPadding);\n        }\n        if (right)\n            state.rightShift += groupShift;\n        if (left)\n            state.leftShift += groupShift;\n        return true;\n    }\n    constructor(graceNotes, showSlur) {\n        super();\n        this.preFormatted = false;\n        this.position = Modifier.Position.LEFT;\n        this.graceNotes = graceNotes;\n        this.width = 0;\n        this.showSlur = showSlur;\n        this.slur = undefined;\n        this.voice = new Voice({\n            numBeats: 4,\n            beatValue: 4,\n            resolution: Tables.RESOLUTION,\n        }).setStrict(false);\n        this.renderOptions = {\n            slurYShift: 0,\n        };\n        this.beams = [];\n        this.voice.addTickables(this.graceNotes);\n        return this;\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        if (!this.formatter) {\n            this.formatter = new Formatter();\n        }\n        this.formatter.joinVoices([this.voice]).format([this.voice], 0, {});\n        this.setWidth(this.formatter.getMinTotalWidth());\n        this.preFormatted = true;\n    }\n    beamNotes(graceNotes) {\n        graceNotes = graceNotes || this.graceNotes;\n        if (graceNotes.length > 1) {\n            const beam = new Beam(graceNotes);\n            beam.renderOptions.beamWidth = 3;\n            beam.renderOptions.partialBeamLength = 4;\n            this.beams.push(beam);\n        }\n        return this;\n    }\n    setWidth(width) {\n        this.width = width;\n        return this;\n    }\n    getWidth() {\n        return this.width + StaveNote.minNoteheadPadding;\n    }\n    getGraceNotes() {\n        return this.graceNotes;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        L('Drawing grace note group for:', note);\n        this.alignSubNotesWithNote(this.getGraceNotes(), note, this.position);\n        this.graceNotes.forEach((graceNote) => graceNote.setContext(ctx).drawWithStyle());\n        this.beams.forEach((beam) => beam.setContext(ctx).drawWithStyle());\n        if (this.showSlur) {\n            const isStavenote = isStaveNote(note);\n            const TieClass = isStavenote ? StaveTie : TabTie;\n            this.slur = new TieClass({\n                lastNote: this.graceNotes[0],\n                firstNote: note,\n                firstIndexes: [0],\n                lastIndexes: [0],\n            });\n            this.slur.renderOptions.cp2 = 12;\n            this.slur.renderOptions.yShift = (isStavenote ? 7 : 5) + this.renderOptions.slurYShift;\n            this.slur.setContext(ctx).drawWithStyle();\n        }\n    }\n}\nGraceNoteGroup.DEBUG = false;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,cAAc,CAACC,KAAK,EACpBL,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAAC;AAC3C;AACA,OAAO,MAAMC,cAAc,SAASV,QAAQ,CAAC;EACzC,WAAWY,QAAQA,CAAA,EAAG;IAClB,OAAO,gBAAgB;EAC3B;EACA,OAAOC,MAAMA,CAACC,eAAe,EAAEC,KAAK,EAAE;IAClC,MAAMC,iBAAiB,GAAG,CAAC;IAC3B,MAAMC,eAAe,GAAG,CAAC;IACzB,IAAI,CAACH,eAAe,IAAIA,eAAe,CAACI,MAAM,KAAK,CAAC,EAChD,OAAO,KAAK;IAChB,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,eAAe,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;MAC7C,MAAMC,cAAc,GAAGT,eAAe,CAACQ,CAAC,CAAC;MACzC,MAAME,IAAI,GAAGD,cAAc,CAACE,OAAO,CAAC,CAAC;MACrC,MAAMC,WAAW,GAAGrB,WAAW,CAACmB,IAAI,CAAC;MACrC,MAAMG,OAAO,GAAGD,WAAW,GAAGV,iBAAiB,GAAGC,eAAe;MACjE,IAAIS,WAAW,IAAIF,IAAI,KAAKJ,QAAQ,EAAE;QAClC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,IAAI,CAACX,MAAM,EAAE,EAAEU,CAAC,EAAE;UACvCP,KAAK,GAAGS,IAAI,CAACC,GAAG,CAACP,IAAI,CAACQ,sBAAsB,CAAC,CAAC,EAAEX,KAAK,CAAC;QAC1D;QACAD,QAAQ,GAAGI,IAAI;MACnB;MACAL,SAAS,CAACc,IAAI,CAAC;QAAEZ,KAAK,EAAEA,KAAK;QAAEE,cAAc;QAAEI;MAAQ,CAAC,CAAC;IAC7D;IACA,IAAIO,UAAU,GAAGf,SAAS,CAAC,CAAC,CAAC,CAACE,KAAK;IACnC,IAAIc,WAAW;IACf,IAAIC,KAAK,GAAG,KAAK;IACjB,IAAIC,IAAI,GAAG,KAAK;IAChB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACD,MAAM,EAAE,EAAEI,CAAC,EAAE;MACvC,MAAMC,cAAc,GAAGJ,SAAS,CAACG,CAAC,CAAC,CAACC,cAAc;MAClD,IAAIA,cAAc,CAACe,QAAQ,KAAKtC,QAAQ,CAACuC,QAAQ,CAACC,KAAK,EACnDJ,KAAK,GAAG,IAAI,CAAC,KAEbC,IAAI,GAAG,IAAI;MACfd,cAAc,CAACkB,SAAS,CAAC,CAAC;MAC1BN,WAAW,GAAGZ,cAAc,CAACmB,QAAQ,CAAC,CAAC,GAAGvB,SAAS,CAACG,CAAC,CAAC,CAACK,OAAO;MAC9DO,UAAU,GAAGJ,IAAI,CAACC,GAAG,CAACI,WAAW,EAAED,UAAU,CAAC;IAClD;IACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACD,MAAM,EAAE,EAAEI,CAAC,EAAE;MACvC,MAAMC,cAAc,GAAGJ,SAAS,CAACG,CAAC,CAAC,CAACC,cAAc;MAClDY,WAAW,GAAGZ,cAAc,CAACmB,QAAQ,CAAC,CAAC,GAAGvB,SAAS,CAACG,CAAC,CAAC,CAACK,OAAO;MAC9DJ,cAAc,CAACoB,0BAA0B,CAACT,UAAU,GAAGJ,IAAI,CAACc,GAAG,CAACT,WAAW,EAAED,UAAU,CAAC,GAAGjC,SAAS,CAAC4C,kBAAkB,CAAC;IAC5H;IACA,IAAIT,KAAK,EACLrB,KAAK,CAAC+B,UAAU,IAAIZ,UAAU;IAClC,IAAIG,IAAI,EACJtB,KAAK,CAACgC,SAAS,IAAIb,UAAU;IACjC,OAAO,IAAI;EACf;EACAc,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACb,QAAQ,GAAGtC,QAAQ,CAACuC,QAAQ,CAACa,IAAI;IACtC,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACI,KAAK,GAAG,CAAC;IACd,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,IAAI,GAAGC,SAAS;IACrB,IAAI,CAACC,KAAK,GAAG,IAAIjD,KAAK,CAAC;MACnBkD,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAExD,MAAM,CAACyD;IACvB,CAAC,CAAC,CAACC,SAAS,CAAC,KAAK,CAAC;IACnB,IAAI,CAACC,aAAa,GAAG;MACjBC,UAAU,EAAE;IAChB,CAAC;IACD,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACR,KAAK,CAACS,YAAY,CAAC,IAAI,CAAChB,UAAU,CAAC;IACxC,OAAO,IAAI;EACf;EACAR,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACU,YAAY,EACjB;IACJ,IAAI,CAAC,IAAI,CAACe,SAAS,EAAE;MACjB,IAAI,CAACA,SAAS,GAAG,IAAInE,SAAS,CAAC,CAAC;IACpC;IACA,IAAI,CAACmE,SAAS,CAACC,UAAU,CAAC,CAAC,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC3C,MAAM,CAAC,CAAC,IAAI,CAAC2C,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnE,IAAI,CAACY,QAAQ,CAAC,IAAI,CAACF,SAAS,CAACG,gBAAgB,CAAC,CAAC,CAAC;IAChD,IAAI,CAAClB,YAAY,GAAG,IAAI;EAC5B;EACAmB,SAASA,CAACrB,UAAU,EAAE;IAClBA,UAAU,GAAGA,UAAU,IAAI,IAAI,CAACA,UAAU;IAC1C,IAAIA,UAAU,CAAC/B,MAAM,GAAG,CAAC,EAAE;MACvB,MAAMqD,IAAI,GAAG,IAAIzE,IAAI,CAACmD,UAAU,CAAC;MACjCsB,IAAI,CAACT,aAAa,CAACU,SAAS,GAAG,CAAC;MAChCD,IAAI,CAACT,aAAa,CAACW,iBAAiB,GAAG,CAAC;MACxC,IAAI,CAACT,KAAK,CAAC/B,IAAI,CAACsC,IAAI,CAAC;IACzB;IACA,OAAO,IAAI;EACf;EACAH,QAAQA,CAACf,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAX,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACW,KAAK,GAAGpD,SAAS,CAAC4C,kBAAkB;EACpD;EACA6B,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACzB,UAAU;EAC1B;EACA0B,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMrD,IAAI,GAAG,IAAI,CAACsD,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBvE,CAAC,CAAC,+BAA+B,EAAEgB,IAAI,CAAC;IACxC,IAAI,CAACwD,qBAAqB,CAAC,IAAI,CAACN,aAAa,CAAC,CAAC,EAAElD,IAAI,EAAE,IAAI,CAACc,QAAQ,CAAC;IACrE,IAAI,CAACW,UAAU,CAACgC,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACC,UAAU,CAACP,GAAG,CAAC,CAACQ,aAAa,CAAC,CAAC,CAAC;IACjF,IAAI,CAACpB,KAAK,CAACiB,OAAO,CAAEV,IAAI,IAAKA,IAAI,CAACY,UAAU,CAACP,GAAG,CAAC,CAACQ,aAAa,CAAC,CAAC,CAAC;IAClE,IAAI,IAAI,CAAClC,QAAQ,EAAE;MACf,MAAMxB,WAAW,GAAGrB,WAAW,CAACmB,IAAI,CAAC;MACrC,MAAM6D,QAAQ,GAAG3D,WAAW,GAAGxB,QAAQ,GAAGE,MAAM;MAChD,IAAI,CAACkD,IAAI,GAAG,IAAI+B,QAAQ,CAAC;QACrBC,QAAQ,EAAE,IAAI,CAACrC,UAAU,CAAC,CAAC,CAAC;QAC5BsC,SAAS,EAAE/D,IAAI;QACfgE,YAAY,EAAE,CAAC,CAAC,CAAC;QACjBC,WAAW,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,IAAI,CAACnC,IAAI,CAACQ,aAAa,CAAC4B,GAAG,GAAG,EAAE;MAChC,IAAI,CAACpC,IAAI,CAACQ,aAAa,CAAC6B,MAAM,GAAG,CAACjE,WAAW,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAACoC,aAAa,CAACC,UAAU;MACtF,IAAI,CAACT,IAAI,CAAC6B,UAAU,CAACP,GAAG,CAAC,CAACQ,aAAa,CAAC,CAAC;IAC7C;EACJ;AACJ;AACA1E,cAAc,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}