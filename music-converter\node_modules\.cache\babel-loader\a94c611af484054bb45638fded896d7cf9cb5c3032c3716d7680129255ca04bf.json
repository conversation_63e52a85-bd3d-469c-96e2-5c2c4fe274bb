{"ast": null, "code": "import { Element } from './element.js';\nimport { RuntimeError } from './util.js';\nexport var CurvePosition;\n(function (CurvePosition) {\n  CurvePosition[CurvePosition[\"NEAR_HEAD\"] = 1] = \"NEAR_HEAD\";\n  CurvePosition[CurvePosition[\"NEAR_TOP\"] = 2] = \"NEAR_TOP\";\n})(CurvePosition || (CurvePosition = {}));\nexport class Curve extends Element {\n  static get CATEGORY() {\n    return \"Curve\";\n  }\n  static get Position() {\n    return CurvePosition;\n  }\n  static get PositionString() {\n    return {\n      nearHead: CurvePosition.NEAR_HEAD,\n      nearTop: CurvePosition.NEAR_TOP\n    };\n  }\n  constructor(from, to, options) {\n    super();\n    this.renderOptions = Object.assign({\n      thickness: 2,\n      xShift: 0,\n      yShift: 10,\n      position: CurvePosition.NEAR_HEAD,\n      positionEnd: CurvePosition.NEAR_HEAD,\n      invert: false,\n      cps: [{\n        x: 0,\n        y: 10\n      }, {\n        x: 0,\n        y: 10\n      }],\n      openingDirection: 'auto'\n    }, options);\n    this.setNotes(from, to);\n  }\n  setNotes(from, to) {\n    if (!from && !to) {\n      throw new RuntimeError('BadArguments', 'Curve needs to have either `from` or `to` set.');\n    }\n    this.from = from;\n    this.to = to;\n    return this;\n  }\n  isPartial() {\n    return !this.from || !this.to;\n  }\n  renderCurve(params) {\n    var _a, _b;\n    const ctx = this.checkContext();\n    const xShift = this.renderOptions.xShift;\n    const yShift = this.renderOptions.yShift * params.direction;\n    const firstX = params.firstX + xShift;\n    const firstY = params.firstY + yShift;\n    const lastX = params.lastX - xShift;\n    const lastY = params.lastY + yShift;\n    const thickness = this.renderOptions.thickness;\n    const cps = this.renderOptions.cps;\n    const {\n      x: cp0x,\n      y: cp0y\n    } = cps[0];\n    const {\n      x: cp1x,\n      y: cp1y\n    } = cps[1];\n    const controlPointSpacing = (lastX - firstX) / (cps.length + 2);\n    ctx.beginPath();\n    ctx.moveTo(firstX, firstY);\n    ctx.bezierCurveTo(firstX + controlPointSpacing + cp0x, firstY + cp0y * params.direction, lastX - controlPointSpacing + cp1x, lastY + cp1y * params.direction, lastX, lastY);\n    if (!((_a = this.style) === null || _a === void 0 ? void 0 : _a.lineDash)) ctx.bezierCurveTo(lastX - controlPointSpacing + cp1x, lastY + (cp1y + thickness) * params.direction, firstX + controlPointSpacing + cp0x, firstY + (cp0y + thickness) * params.direction, firstX, firstY);\n    ctx.stroke();\n    ctx.closePath();\n    if (!((_b = this.style) === null || _b === void 0 ? void 0 : _b.lineDash)) ctx.fill();\n  }\n  draw() {\n    this.checkContext();\n    this.setRendered();\n    const firstNote = this.from;\n    const lastNote = this.to;\n    let firstX;\n    let lastX;\n    let firstY;\n    let lastY;\n    let stemDirection = 0;\n    let metric = 'baseY';\n    let endMetric = 'baseY';\n    function getPosition(position) {\n      return typeof position === 'string' ? Curve.PositionString[position] : position;\n    }\n    const position = getPosition(this.renderOptions.position);\n    const positionEnd = getPosition(this.renderOptions.positionEnd);\n    if (position === CurvePosition.NEAR_TOP) {\n      metric = 'topY';\n      endMetric = 'topY';\n    }\n    if (positionEnd === CurvePosition.NEAR_HEAD) {\n      endMetric = 'baseY';\n    } else if (positionEnd === CurvePosition.NEAR_TOP) {\n      endMetric = 'topY';\n    }\n    if (firstNote) {\n      firstX = firstNote.getTieRightX();\n      stemDirection = firstNote.getStemDirection();\n      firstY = firstNote.getStemExtents()[metric];\n    } else {\n      const stave = lastNote.checkStave();\n      firstX = stave.getTieStartX();\n      firstY = lastNote.getStemExtents()[metric];\n    }\n    if (lastNote) {\n      lastX = lastNote.getTieLeftX();\n      stemDirection = lastNote.getStemDirection();\n      lastY = lastNote.getStemExtents()[endMetric];\n    } else {\n      const stave = firstNote.checkStave();\n      lastX = stave.getTieEndX();\n      lastY = firstNote.getStemExtents()[endMetric];\n    }\n    if (this.renderOptions.openingDirection === 'up') {\n      stemDirection = 1;\n    }\n    if (this.renderOptions.openingDirection === 'down') {\n      stemDirection = -1;\n    }\n    this.renderCurve({\n      firstX,\n      lastX,\n      firstY,\n      lastY,\n      direction: stemDirection * (this.renderOptions.invert === true ? -1 : 1)\n    });\n    return true;\n  }\n}", "map": {"version": 3, "names": ["Element", "RuntimeError", "CurvePosition", "Curve", "CATEGORY", "Position", "PositionString", "nearHead", "NEAR_HEAD", "nearTop", "NEAR_TOP", "constructor", "from", "to", "options", "renderOptions", "Object", "assign", "thickness", "xShift", "yShift", "position", "positionEnd", "invert", "cps", "x", "y", "openingDirection", "setNotes", "isPartial", "renderCurve", "params", "_a", "_b", "ctx", "checkContext", "direction", "firstX", "firstY", "lastX", "lastY", "cp0x", "cp0y", "cp1x", "cp1y", "controlPointSpacing", "length", "beginPath", "moveTo", "bezierCurveTo", "style", "lineDash", "stroke", "closePath", "fill", "draw", "setRendered", "firstNote", "lastNote", "stemDirection", "metric", "endMetric", "getPosition", "getTieRightX", "getStemDirection", "getStemExtents", "stave", "checkStave", "getTieStartX", "getTieLeftX", "getTieEndX"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/curve.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { RuntimeError } from './util.js';\nexport var CurvePosition;\n(function (CurvePosition) {\n    CurvePosition[CurvePosition[\"NEAR_HEAD\"] = 1] = \"NEAR_HEAD\";\n    CurvePosition[CurvePosition[\"NEAR_TOP\"] = 2] = \"NEAR_TOP\";\n})(CurvePosition || (CurvePosition = {}));\nexport class Curve extends Element {\n    static get CATEGORY() {\n        return \"Curve\";\n    }\n    static get Position() {\n        return CurvePosition;\n    }\n    static get PositionString() {\n        return {\n            nearHead: CurvePosition.NEAR_HEAD,\n            nearTop: CurvePosition.NEAR_TOP,\n        };\n    }\n    constructor(from, to, options) {\n        super();\n        this.renderOptions = Object.assign({ thickness: 2, xShift: 0, yShift: 10, position: CurvePosition.NEAR_HEAD, positionEnd: CurvePosition.NEAR_HEAD, invert: false, cps: [\n                { x: 0, y: 10 },\n                { x: 0, y: 10 },\n            ], openingDirection: 'auto' }, options);\n        this.setNotes(from, to);\n    }\n    setNotes(from, to) {\n        if (!from && !to) {\n            throw new RuntimeError('BadArguments', 'Curve needs to have either `from` or `to` set.');\n        }\n        this.from = from;\n        this.to = to;\n        return this;\n    }\n    isPartial() {\n        return !this.from || !this.to;\n    }\n    renderCurve(params) {\n        var _a, _b;\n        const ctx = this.checkContext();\n        const xShift = this.renderOptions.xShift;\n        const yShift = this.renderOptions.yShift * params.direction;\n        const firstX = params.firstX + xShift;\n        const firstY = params.firstY + yShift;\n        const lastX = params.lastX - xShift;\n        const lastY = params.lastY + yShift;\n        const thickness = this.renderOptions.thickness;\n        const cps = this.renderOptions.cps;\n        const { x: cp0x, y: cp0y } = cps[0];\n        const { x: cp1x, y: cp1y } = cps[1];\n        const controlPointSpacing = (lastX - firstX) / (cps.length + 2);\n        ctx.beginPath();\n        ctx.moveTo(firstX, firstY);\n        ctx.bezierCurveTo(firstX + controlPointSpacing + cp0x, firstY + cp0y * params.direction, lastX - controlPointSpacing + cp1x, lastY + cp1y * params.direction, lastX, lastY);\n        if (!((_a = this.style) === null || _a === void 0 ? void 0 : _a.lineDash))\n            ctx.bezierCurveTo(lastX - controlPointSpacing + cp1x, lastY + (cp1y + thickness) * params.direction, firstX + controlPointSpacing + cp0x, firstY + (cp0y + thickness) * params.direction, firstX, firstY);\n        ctx.stroke();\n        ctx.closePath();\n        if (!((_b = this.style) === null || _b === void 0 ? void 0 : _b.lineDash))\n            ctx.fill();\n    }\n    draw() {\n        this.checkContext();\n        this.setRendered();\n        const firstNote = this.from;\n        const lastNote = this.to;\n        let firstX;\n        let lastX;\n        let firstY;\n        let lastY;\n        let stemDirection = 0;\n        let metric = 'baseY';\n        let endMetric = 'baseY';\n        function getPosition(position) {\n            return typeof position === 'string' ? Curve.PositionString[position] : position;\n        }\n        const position = getPosition(this.renderOptions.position);\n        const positionEnd = getPosition(this.renderOptions.positionEnd);\n        if (position === CurvePosition.NEAR_TOP) {\n            metric = 'topY';\n            endMetric = 'topY';\n        }\n        if (positionEnd === CurvePosition.NEAR_HEAD) {\n            endMetric = 'baseY';\n        }\n        else if (positionEnd === CurvePosition.NEAR_TOP) {\n            endMetric = 'topY';\n        }\n        if (firstNote) {\n            firstX = firstNote.getTieRightX();\n            stemDirection = firstNote.getStemDirection();\n            firstY = firstNote.getStemExtents()[metric];\n        }\n        else {\n            const stave = lastNote.checkStave();\n            firstX = stave.getTieStartX();\n            firstY = lastNote.getStemExtents()[metric];\n        }\n        if (lastNote) {\n            lastX = lastNote.getTieLeftX();\n            stemDirection = lastNote.getStemDirection();\n            lastY = lastNote.getStemExtents()[endMetric];\n        }\n        else {\n            const stave = firstNote.checkStave();\n            lastX = stave.getTieEndX();\n            lastY = firstNote.getStemExtents()[endMetric];\n        }\n        if (this.renderOptions.openingDirection === 'up') {\n            stemDirection = 1;\n        }\n        if (this.renderOptions.openingDirection === 'down') {\n            stemDirection = -1;\n        }\n        this.renderCurve({\n            firstX,\n            lastX,\n            firstY,\n            lastY,\n            direction: stemDirection * (this.renderOptions.invert === true ? -1 : 1),\n        });\n        return true;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,IAAIC,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAACA,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC3DA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AAC7D,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,OAAO,MAAMC,KAAK,SAASH,OAAO,CAAC;EAC/B,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,OAAO;EAClB;EACA,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAOH,aAAa;EACxB;EACA,WAAWI,cAAcA,CAAA,EAAG;IACxB,OAAO;MACHC,QAAQ,EAAEL,aAAa,CAACM,SAAS;MACjCC,OAAO,EAAEP,aAAa,CAACQ;IAC3B,CAAC;EACL;EACAC,WAAWA,CAACC,IAAI,EAAEC,EAAE,EAAEC,OAAO,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,SAAS,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,EAAE;MAAEC,QAAQ,EAAEnB,aAAa,CAACM,SAAS;MAAEc,WAAW,EAAEpB,aAAa,CAACM,SAAS;MAAEe,MAAM,EAAE,KAAK;MAAEC,GAAG,EAAE,CAC/J;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC,EACf;QAAED,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAC,CAClB;MAAEC,gBAAgB,EAAE;IAAO,CAAC,EAAEb,OAAO,CAAC;IAC3C,IAAI,CAACc,QAAQ,CAAChB,IAAI,EAAEC,EAAE,CAAC;EAC3B;EACAe,QAAQA,CAAChB,IAAI,EAAEC,EAAE,EAAE;IACf,IAAI,CAACD,IAAI,IAAI,CAACC,EAAE,EAAE;MACd,MAAM,IAAIZ,YAAY,CAAC,cAAc,EAAE,gDAAgD,CAAC;IAC5F;IACA,IAAI,CAACW,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,OAAO,IAAI;EACf;EACAgB,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACjB,IAAI,IAAI,CAAC,IAAI,CAACC,EAAE;EACjC;EACAiB,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMhB,MAAM,GAAG,IAAI,CAACJ,aAAa,CAACI,MAAM;IACxC,MAAMC,MAAM,GAAG,IAAI,CAACL,aAAa,CAACK,MAAM,GAAGW,MAAM,CAACK,SAAS;IAC3D,MAAMC,MAAM,GAAGN,MAAM,CAACM,MAAM,GAAGlB,MAAM;IACrC,MAAMmB,MAAM,GAAGP,MAAM,CAACO,MAAM,GAAGlB,MAAM;IACrC,MAAMmB,KAAK,GAAGR,MAAM,CAACQ,KAAK,GAAGpB,MAAM;IACnC,MAAMqB,KAAK,GAAGT,MAAM,CAACS,KAAK,GAAGpB,MAAM;IACnC,MAAMF,SAAS,GAAG,IAAI,CAACH,aAAa,CAACG,SAAS;IAC9C,MAAMM,GAAG,GAAG,IAAI,CAACT,aAAa,CAACS,GAAG;IAClC,MAAM;MAAEC,CAAC,EAAEgB,IAAI;MAAEf,CAAC,EAAEgB;IAAK,CAAC,GAAGlB,GAAG,CAAC,CAAC,CAAC;IACnC,MAAM;MAAEC,CAAC,EAAEkB,IAAI;MAAEjB,CAAC,EAAEkB;IAAK,CAAC,GAAGpB,GAAG,CAAC,CAAC,CAAC;IACnC,MAAMqB,mBAAmB,GAAG,CAACN,KAAK,GAAGF,MAAM,KAAKb,GAAG,CAACsB,MAAM,GAAG,CAAC,CAAC;IAC/DZ,GAAG,CAACa,SAAS,CAAC,CAAC;IACfb,GAAG,CAACc,MAAM,CAACX,MAAM,EAAEC,MAAM,CAAC;IAC1BJ,GAAG,CAACe,aAAa,CAACZ,MAAM,GAAGQ,mBAAmB,GAAGJ,IAAI,EAAEH,MAAM,GAAGI,IAAI,GAAGX,MAAM,CAACK,SAAS,EAAEG,KAAK,GAAGM,mBAAmB,GAAGF,IAAI,EAAEH,KAAK,GAAGI,IAAI,GAAGb,MAAM,CAACK,SAAS,EAAEG,KAAK,EAAEC,KAAK,CAAC;IAC3K,IAAI,EAAE,CAACR,EAAE,GAAG,IAAI,CAACkB,KAAK,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,QAAQ,CAAC,EACrEjB,GAAG,CAACe,aAAa,CAACV,KAAK,GAAGM,mBAAmB,GAAGF,IAAI,EAAEH,KAAK,GAAG,CAACI,IAAI,GAAG1B,SAAS,IAAIa,MAAM,CAACK,SAAS,EAAEC,MAAM,GAAGQ,mBAAmB,GAAGJ,IAAI,EAAEH,MAAM,GAAG,CAACI,IAAI,GAAGxB,SAAS,IAAIa,MAAM,CAACK,SAAS,EAAEC,MAAM,EAAEC,MAAM,CAAC;IAC7MJ,GAAG,CAACkB,MAAM,CAAC,CAAC;IACZlB,GAAG,CAACmB,SAAS,CAAC,CAAC;IACf,IAAI,EAAE,CAACpB,EAAE,GAAG,IAAI,CAACiB,KAAK,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,QAAQ,CAAC,EACrEjB,GAAG,CAACoB,IAAI,CAAC,CAAC;EAClB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACpB,YAAY,CAAC,CAAC;IACnB,IAAI,CAACqB,WAAW,CAAC,CAAC;IAClB,MAAMC,SAAS,GAAG,IAAI,CAAC7C,IAAI;IAC3B,MAAM8C,QAAQ,GAAG,IAAI,CAAC7C,EAAE;IACxB,IAAIwB,MAAM;IACV,IAAIE,KAAK;IACT,IAAID,MAAM;IACV,IAAIE,KAAK;IACT,IAAImB,aAAa,GAAG,CAAC;IACrB,IAAIC,MAAM,GAAG,OAAO;IACpB,IAAIC,SAAS,GAAG,OAAO;IACvB,SAASC,WAAWA,CAACzC,QAAQ,EAAE;MAC3B,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAAGlB,KAAK,CAACG,cAAc,CAACe,QAAQ,CAAC,GAAGA,QAAQ;IACnF;IACA,MAAMA,QAAQ,GAAGyC,WAAW,CAAC,IAAI,CAAC/C,aAAa,CAACM,QAAQ,CAAC;IACzD,MAAMC,WAAW,GAAGwC,WAAW,CAAC,IAAI,CAAC/C,aAAa,CAACO,WAAW,CAAC;IAC/D,IAAID,QAAQ,KAAKnB,aAAa,CAACQ,QAAQ,EAAE;MACrCkD,MAAM,GAAG,MAAM;MACfC,SAAS,GAAG,MAAM;IACtB;IACA,IAAIvC,WAAW,KAAKpB,aAAa,CAACM,SAAS,EAAE;MACzCqD,SAAS,GAAG,OAAO;IACvB,CAAC,MACI,IAAIvC,WAAW,KAAKpB,aAAa,CAACQ,QAAQ,EAAE;MAC7CmD,SAAS,GAAG,MAAM;IACtB;IACA,IAAIJ,SAAS,EAAE;MACXpB,MAAM,GAAGoB,SAAS,CAACM,YAAY,CAAC,CAAC;MACjCJ,aAAa,GAAGF,SAAS,CAACO,gBAAgB,CAAC,CAAC;MAC5C1B,MAAM,GAAGmB,SAAS,CAACQ,cAAc,CAAC,CAAC,CAACL,MAAM,CAAC;IAC/C,CAAC,MACI;MACD,MAAMM,KAAK,GAAGR,QAAQ,CAACS,UAAU,CAAC,CAAC;MACnC9B,MAAM,GAAG6B,KAAK,CAACE,YAAY,CAAC,CAAC;MAC7B9B,MAAM,GAAGoB,QAAQ,CAACO,cAAc,CAAC,CAAC,CAACL,MAAM,CAAC;IAC9C;IACA,IAAIF,QAAQ,EAAE;MACVnB,KAAK,GAAGmB,QAAQ,CAACW,WAAW,CAAC,CAAC;MAC9BV,aAAa,GAAGD,QAAQ,CAACM,gBAAgB,CAAC,CAAC;MAC3CxB,KAAK,GAAGkB,QAAQ,CAACO,cAAc,CAAC,CAAC,CAACJ,SAAS,CAAC;IAChD,CAAC,MACI;MACD,MAAMK,KAAK,GAAGT,SAAS,CAACU,UAAU,CAAC,CAAC;MACpC5B,KAAK,GAAG2B,KAAK,CAACI,UAAU,CAAC,CAAC;MAC1B9B,KAAK,GAAGiB,SAAS,CAACQ,cAAc,CAAC,CAAC,CAACJ,SAAS,CAAC;IACjD;IACA,IAAI,IAAI,CAAC9C,aAAa,CAACY,gBAAgB,KAAK,IAAI,EAAE;MAC9CgC,aAAa,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC5C,aAAa,CAACY,gBAAgB,KAAK,MAAM,EAAE;MAChDgC,aAAa,GAAG,CAAC,CAAC;IACtB;IACA,IAAI,CAAC7B,WAAW,CAAC;MACbO,MAAM;MACNE,KAAK;MACLD,MAAM;MACNE,KAAK;MACLJ,SAAS,EAAEuB,aAAa,IAAI,IAAI,CAAC5C,aAAa,CAACQ,MAAM,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}