{"ast": null, "code": "import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Tables } from './tables.js';\nimport { defined, RuntimeError, sumArray } from './util.js';\nexport var VoiceMode;\n(function (VoiceMode) {\n  VoiceMode[VoiceMode[\"STRICT\"] = 1] = \"STRICT\";\n  VoiceMode[VoiceMode[\"SOFT\"] = 2] = \"SOFT\";\n  VoiceMode[VoiceMode[\"FULL\"] = 3] = \"FULL\";\n})(VoiceMode || (VoiceMode = {}));\nexport class Voice extends Element {\n  static get CATEGORY() {\n    return \"Voice\";\n  }\n  static get Mode() {\n    return VoiceMode;\n  }\n  constructor(time) {\n    super();\n    this.resolutionMultiplier = 1;\n    this.mode = VoiceMode.STRICT;\n    this.preFormatted = false;\n    this.ticksUsed = new Fraction(0, 1);\n    this.largestTickWidth = 0;\n    this.tickables = [];\n    this.options = {\n      softmaxFactor: Tables.SOFTMAX_FACTOR\n    };\n    let voiceTime;\n    if (typeof time === 'string') {\n      const match = time.match(/(\\d+)\\/(\\d+)/);\n      if (match) {\n        voiceTime = {\n          numBeats: parseInt(match[1]),\n          beatValue: parseInt(match[2])\n        };\n      }\n    } else {\n      voiceTime = time;\n    }\n    this.time = Object.assign({\n      numBeats: 4,\n      beatValue: 4,\n      resolution: Tables.RESOLUTION\n    }, voiceTime);\n    this.totalTicks = new Fraction(this.time.numBeats * (this.time.resolution / this.time.beatValue), 1);\n    this.smallestTickCount = this.totalTicks.clone();\n  }\n  getTotalTicks() {\n    return this.totalTicks;\n  }\n  getTicksUsed() {\n    return this.ticksUsed;\n  }\n  getLargestTickWidth() {\n    return this.largestTickWidth;\n  }\n  getSmallestTickCount() {\n    return this.smallestTickCount;\n  }\n  getTickables() {\n    return this.tickables;\n  }\n  getMode() {\n    return this.mode;\n  }\n  setMode(mode) {\n    this.mode = mode;\n    return this;\n  }\n  getResolutionMultiplier() {\n    return this.resolutionMultiplier;\n  }\n  getActualResolution() {\n    return this.resolutionMultiplier * this.time.resolution;\n  }\n  setStave(stave) {\n    this.stave = stave;\n    return this;\n  }\n  getStave() {\n    return this.stave;\n  }\n  getBoundingBox() {\n    const boundingBox = this.tickables[0].getBoundingBox();\n    for (let i = 1; i < this.tickables.length; ++i) {\n      const tickable = this.tickables[i];\n      if (!tickable.getStave() && this.stave) tickable.setStave(this.stave);\n      const bb = tickable.getBoundingBox();\n      boundingBox.mergeWith(bb);\n    }\n    return boundingBox;\n  }\n  setStrict(strict) {\n    this.mode = strict ? VoiceMode.STRICT : VoiceMode.SOFT;\n    return this;\n  }\n  isComplete() {\n    if (this.mode === VoiceMode.STRICT || this.mode === VoiceMode.FULL) {\n      return this.ticksUsed.equals(this.totalTicks);\n    } else {\n      return true;\n    }\n  }\n  setSoftmaxFactor(factor) {\n    this.options.softmaxFactor = factor;\n    this.expTicksUsed = 0;\n    return this;\n  }\n  reCalculateExpTicksUsed() {\n    const totalTicks = this.ticksUsed.value();\n    const exp = tickable => Math.pow(this.options.softmaxFactor, tickable.getTicks().value() / totalTicks);\n    this.expTicksUsed = sumArray(this.tickables.map(exp));\n    return this.expTicksUsed;\n  }\n  softmax(tickValue) {\n    if (!this.expTicksUsed) {\n      this.expTicksUsed = this.reCalculateExpTicksUsed();\n    }\n    const totalTicks = this.ticksUsed.value();\n    const exp = v => Math.pow(this.options.softmaxFactor, v / totalTicks);\n    const sm = exp(tickValue) / this.expTicksUsed;\n    return sm;\n  }\n  addTickable(tickable) {\n    if (!tickable.shouldIgnoreTicks()) {\n      const ticks = tickable.getTicks();\n      this.ticksUsed.add(ticks);\n      this.expTicksUsed = 0;\n      if ((this.mode === VoiceMode.STRICT || this.mode === VoiceMode.FULL) && this.ticksUsed.greaterThan(this.totalTicks)) {\n        this.ticksUsed.subtract(ticks);\n        throw new RuntimeError('BadArgument', 'Too many ticks.');\n      }\n      if (ticks.lessThan(this.smallestTickCount)) {\n        this.smallestTickCount = ticks.clone();\n      }\n      this.resolutionMultiplier = this.ticksUsed.denominator;\n      this.totalTicks.add(0, this.ticksUsed.denominator);\n    }\n    this.tickables.push(tickable);\n    tickable.setVoice(this);\n    return this;\n  }\n  addTickables(tickables) {\n    for (let i = 0; i < tickables.length; ++i) {\n      this.addTickable(tickables[i]);\n    }\n    return this;\n  }\n  preFormat() {\n    if (this.preFormatted) return this;\n    const stave = this.checkStave();\n    this.tickables.forEach(tickable => {\n      if (!tickable.getStave()) {\n        tickable.setStave(stave);\n      }\n    });\n    this.preFormatted = true;\n    return this;\n  }\n  checkStave() {\n    return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n  }\n  draw(context = this.checkContext(), stave) {\n    stave = stave !== null && stave !== void 0 ? stave : this.stave;\n    this.setRendered();\n    for (let i = 0; i < this.tickables.length; ++i) {\n      const tickable = this.tickables[i];\n      if (stave) {\n        tickable.setStave(stave);\n      }\n      defined(tickable.getStave(), 'MissingStave', 'The voice cannot draw tickables without staves.');\n      tickable.setContext(context);\n      tickable.drawWithStyle();\n    }\n  }\n}", "map": {"version": 3, "names": ["Element", "Fraction", "Tables", "defined", "RuntimeError", "sumArray", "VoiceMode", "Voice", "CATEGORY", "Mode", "constructor", "time", "resolutionMultiplier", "mode", "STRICT", "preFormatted", "ticksUsed", "largestTickWidth", "tickables", "options", "softmaxFactor", "SOFTMAX_FACTOR", "voiceTime", "match", "numBeats", "parseInt", "beatValue", "Object", "assign", "resolution", "RESOLUTION", "totalTicks", "smallestTickCount", "clone", "getTotalTicks", "getTicksUsed", "getLargestTickWidth", "getSmallestTickCount", "getTickables", "getMode", "setMode", "getResolutionMultiplier", "getActualResolution", "setStave", "stave", "getStave", "getBoundingBox", "boundingBox", "i", "length", "tickable", "bb", "mergeWith", "setStrict", "strict", "SOFT", "isComplete", "FULL", "equals", "setSoftmaxFactor", "factor", "expTicksUsed", "reCalculateExpTicksUsed", "value", "exp", "Math", "pow", "getTicks", "map", "softmax", "tickValue", "v", "sm", "addTickable", "shouldIgnoreTicks", "ticks", "add", "greaterThan", "subtract", "lessThan", "denominator", "push", "setVoice", "addTickables", "preFormat", "checkStave", "for<PERSON>ach", "draw", "context", "checkContext", "setRendered", "setContext", "drawWithStyle"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/voice.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Tables } from './tables.js';\nimport { defined, RuntimeError, sumArray } from './util.js';\nexport var VoiceMode;\n(function (VoiceMode) {\n    VoiceMode[VoiceMode[\"STRICT\"] = 1] = \"STRICT\";\n    VoiceMode[VoiceMode[\"SOFT\"] = 2] = \"SOFT\";\n    VoiceMode[VoiceMode[\"FULL\"] = 3] = \"FULL\";\n})(VoiceMode || (VoiceMode = {}));\nexport class Voice extends Element {\n    static get CATEGORY() {\n        return \"Voice\";\n    }\n    static get Mode() {\n        return VoiceMode;\n    }\n    constructor(time) {\n        super();\n        this.resolutionMultiplier = 1;\n        this.mode = VoiceMode.STRICT;\n        this.preFormatted = false;\n        this.ticksUsed = new Fraction(0, 1);\n        this.largestTickWidth = 0;\n        this.tickables = [];\n        this.options = {\n            softmaxFactor: Tables.SOFTMAX_FACTOR,\n        };\n        let voiceTime;\n        if (typeof time === 'string') {\n            const match = time.match(/(\\d+)\\/(\\d+)/);\n            if (match) {\n                voiceTime = {\n                    numBeats: parseInt(match[1]),\n                    beatValue: parseInt(match[2]),\n                };\n            }\n        }\n        else {\n            voiceTime = time;\n        }\n        this.time = Object.assign({ numBeats: 4, beatValue: 4, resolution: Tables.RESOLUTION }, voiceTime);\n        this.totalTicks = new Fraction(this.time.numBeats * (this.time.resolution / this.time.beatValue), 1);\n        this.smallestTickCount = this.totalTicks.clone();\n    }\n    getTotalTicks() {\n        return this.totalTicks;\n    }\n    getTicksUsed() {\n        return this.ticksUsed;\n    }\n    getLargestTickWidth() {\n        return this.largestTickWidth;\n    }\n    getSmallestTickCount() {\n        return this.smallestTickCount;\n    }\n    getTickables() {\n        return this.tickables;\n    }\n    getMode() {\n        return this.mode;\n    }\n    setMode(mode) {\n        this.mode = mode;\n        return this;\n    }\n    getResolutionMultiplier() {\n        return this.resolutionMultiplier;\n    }\n    getActualResolution() {\n        return this.resolutionMultiplier * this.time.resolution;\n    }\n    setStave(stave) {\n        this.stave = stave;\n        return this;\n    }\n    getStave() {\n        return this.stave;\n    }\n    getBoundingBox() {\n        const boundingBox = this.tickables[0].getBoundingBox();\n        for (let i = 1; i < this.tickables.length; ++i) {\n            const tickable = this.tickables[i];\n            if (!tickable.getStave() && this.stave)\n                tickable.setStave(this.stave);\n            const bb = tickable.getBoundingBox();\n            boundingBox.mergeWith(bb);\n        }\n        return boundingBox;\n    }\n    setStrict(strict) {\n        this.mode = strict ? VoiceMode.STRICT : VoiceMode.SOFT;\n        return this;\n    }\n    isComplete() {\n        if (this.mode === VoiceMode.STRICT || this.mode === VoiceMode.FULL) {\n            return this.ticksUsed.equals(this.totalTicks);\n        }\n        else {\n            return true;\n        }\n    }\n    setSoftmaxFactor(factor) {\n        this.options.softmaxFactor = factor;\n        this.expTicksUsed = 0;\n        return this;\n    }\n    reCalculateExpTicksUsed() {\n        const totalTicks = this.ticksUsed.value();\n        const exp = (tickable) => Math.pow(this.options.softmaxFactor, tickable.getTicks().value() / totalTicks);\n        this.expTicksUsed = sumArray(this.tickables.map(exp));\n        return this.expTicksUsed;\n    }\n    softmax(tickValue) {\n        if (!this.expTicksUsed) {\n            this.expTicksUsed = this.reCalculateExpTicksUsed();\n        }\n        const totalTicks = this.ticksUsed.value();\n        const exp = (v) => Math.pow(this.options.softmaxFactor, v / totalTicks);\n        const sm = exp(tickValue) / this.expTicksUsed;\n        return sm;\n    }\n    addTickable(tickable) {\n        if (!tickable.shouldIgnoreTicks()) {\n            const ticks = tickable.getTicks();\n            this.ticksUsed.add(ticks);\n            this.expTicksUsed = 0;\n            if ((this.mode === VoiceMode.STRICT || this.mode === VoiceMode.FULL) &&\n                this.ticksUsed.greaterThan(this.totalTicks)) {\n                this.ticksUsed.subtract(ticks);\n                throw new RuntimeError('BadArgument', 'Too many ticks.');\n            }\n            if (ticks.lessThan(this.smallestTickCount)) {\n                this.smallestTickCount = ticks.clone();\n            }\n            this.resolutionMultiplier = this.ticksUsed.denominator;\n            this.totalTicks.add(0, this.ticksUsed.denominator);\n        }\n        this.tickables.push(tickable);\n        tickable.setVoice(this);\n        return this;\n    }\n    addTickables(tickables) {\n        for (let i = 0; i < tickables.length; ++i) {\n            this.addTickable(tickables[i]);\n        }\n        return this;\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return this;\n        const stave = this.checkStave();\n        this.tickables.forEach((tickable) => {\n            if (!tickable.getStave()) {\n                tickable.setStave(stave);\n            }\n        });\n        this.preFormatted = true;\n        return this;\n    }\n    checkStave() {\n        return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n    }\n    draw(context = this.checkContext(), stave) {\n        stave = stave !== null && stave !== void 0 ? stave : this.stave;\n        this.setRendered();\n        for (let i = 0; i < this.tickables.length; ++i) {\n            const tickable = this.tickables[i];\n            if (stave) {\n                tickable.setStave(stave);\n            }\n            defined(tickable.getStave(), 'MissingStave', 'The voice cannot draw tickables without staves.');\n            tickable.setContext(context);\n            tickable.drawWithStyle();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,WAAW;AAC3D,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC7CA,SAAS,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzCA,SAAS,CAACA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAC7C,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,OAAO,MAAMC,KAAK,SAASP,OAAO,CAAC;EAC/B,WAAWQ,QAAQA,CAAA,EAAG;IAClB,OAAO,OAAO;EAClB;EACA,WAAWC,IAAIA,CAAA,EAAG;IACd,OAAOH,SAAS;EACpB;EACAI,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,MAAM;IAC5B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,SAAS,GAAG,IAAIf,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,IAAI,CAACgB,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG;MACXC,aAAa,EAAElB,MAAM,CAACmB;IAC1B,CAAC;IACD,IAAIC,SAAS;IACb,IAAI,OAAOX,IAAI,KAAK,QAAQ,EAAE;MAC1B,MAAMY,KAAK,GAAGZ,IAAI,CAACY,KAAK,CAAC,cAAc,CAAC;MACxC,IAAIA,KAAK,EAAE;QACPD,SAAS,GAAG;UACRE,QAAQ,EAAEC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5BG,SAAS,EAAED,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC;QAChC,CAAC;MACL;IACJ,CAAC,MACI;MACDD,SAAS,GAAGX,IAAI;IACpB;IACA,IAAI,CAACA,IAAI,GAAGgB,MAAM,CAACC,MAAM,CAAC;MAAEJ,QAAQ,EAAE,CAAC;MAAEE,SAAS,EAAE,CAAC;MAAEG,UAAU,EAAE3B,MAAM,CAAC4B;IAAW,CAAC,EAAER,SAAS,CAAC;IAClG,IAAI,CAACS,UAAU,GAAG,IAAI9B,QAAQ,CAAC,IAAI,CAACU,IAAI,CAACa,QAAQ,IAAI,IAAI,CAACb,IAAI,CAACkB,UAAU,GAAG,IAAI,CAAClB,IAAI,CAACe,SAAS,CAAC,EAAE,CAAC,CAAC;IACpG,IAAI,CAACM,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACE,KAAK,CAAC,CAAC;EACpD;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACH,UAAU;EAC1B;EACAI,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnB,SAAS;EACzB;EACAoB,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnB,gBAAgB;EAChC;EACAoB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACL,iBAAiB;EACjC;EACAM,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpB,SAAS;EACzB;EACAqB,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC1B,IAAI;EACpB;EACA2B,OAAOA,CAAC3B,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACA4B,uBAAuBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAC7B,oBAAoB;EACpC;EACA8B,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9B,oBAAoB,GAAG,IAAI,CAACD,IAAI,CAACkB,UAAU;EAC3D;EACAc,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,KAAK;EACrB;EACAE,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAAC4B,cAAc,CAAC,CAAC;IACtD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,EAAE,EAAED,CAAC,EAAE;MAC5C,MAAME,QAAQ,GAAG,IAAI,CAAChC,SAAS,CAAC8B,CAAC,CAAC;MAClC,IAAI,CAACE,QAAQ,CAACL,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACD,KAAK,EAClCM,QAAQ,CAACP,QAAQ,CAAC,IAAI,CAACC,KAAK,CAAC;MACjC,MAAMO,EAAE,GAAGD,QAAQ,CAACJ,cAAc,CAAC,CAAC;MACpCC,WAAW,CAACK,SAAS,CAACD,EAAE,CAAC;IAC7B;IACA,OAAOJ,WAAW;EACtB;EACAM,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACzC,IAAI,GAAGyC,MAAM,GAAGhD,SAAS,CAACQ,MAAM,GAAGR,SAAS,CAACiD,IAAI;IACtD,OAAO,IAAI;EACf;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAC3C,IAAI,KAAKP,SAAS,CAACQ,MAAM,IAAI,IAAI,CAACD,IAAI,KAAKP,SAAS,CAACmD,IAAI,EAAE;MAChE,OAAO,IAAI,CAACzC,SAAS,CAAC0C,MAAM,CAAC,IAAI,CAAC3B,UAAU,CAAC;IACjD,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ;EACA4B,gBAAgBA,CAACC,MAAM,EAAE;IACrB,IAAI,CAACzC,OAAO,CAACC,aAAa,GAAGwC,MAAM;IACnC,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,OAAO,IAAI;EACf;EACAC,uBAAuBA,CAAA,EAAG;IACtB,MAAM/B,UAAU,GAAG,IAAI,CAACf,SAAS,CAAC+C,KAAK,CAAC,CAAC;IACzC,MAAMC,GAAG,GAAId,QAAQ,IAAKe,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,OAAO,CAACC,aAAa,EAAE8B,QAAQ,CAACiB,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,CAAC,GAAGhC,UAAU,CAAC;IACxG,IAAI,CAAC8B,YAAY,GAAGxD,QAAQ,CAAC,IAAI,CAACa,SAAS,CAACkD,GAAG,CAACJ,GAAG,CAAC,CAAC;IACrD,OAAO,IAAI,CAACH,YAAY;EAC5B;EACAQ,OAAOA,CAACC,SAAS,EAAE;IACf,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE;MACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;IACtD;IACA,MAAM/B,UAAU,GAAG,IAAI,CAACf,SAAS,CAAC+C,KAAK,CAAC,CAAC;IACzC,MAAMC,GAAG,GAAIO,CAAC,IAAKN,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,OAAO,CAACC,aAAa,EAAEmD,CAAC,GAAGxC,UAAU,CAAC;IACvE,MAAMyC,EAAE,GAAGR,GAAG,CAACM,SAAS,CAAC,GAAG,IAAI,CAACT,YAAY;IAC7C,OAAOW,EAAE;EACb;EACAC,WAAWA,CAACvB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,CAACwB,iBAAiB,CAAC,CAAC,EAAE;MAC/B,MAAMC,KAAK,GAAGzB,QAAQ,CAACiB,QAAQ,CAAC,CAAC;MACjC,IAAI,CAACnD,SAAS,CAAC4D,GAAG,CAACD,KAAK,CAAC;MACzB,IAAI,CAACd,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC,IAAI,CAAChD,IAAI,KAAKP,SAAS,CAACQ,MAAM,IAAI,IAAI,CAACD,IAAI,KAAKP,SAAS,CAACmD,IAAI,KAC/D,IAAI,CAACzC,SAAS,CAAC6D,WAAW,CAAC,IAAI,CAAC9C,UAAU,CAAC,EAAE;QAC7C,IAAI,CAACf,SAAS,CAAC8D,QAAQ,CAACH,KAAK,CAAC;QAC9B,MAAM,IAAIvE,YAAY,CAAC,aAAa,EAAE,iBAAiB,CAAC;MAC5D;MACA,IAAIuE,KAAK,CAACI,QAAQ,CAAC,IAAI,CAAC/C,iBAAiB,CAAC,EAAE;QACxC,IAAI,CAACA,iBAAiB,GAAG2C,KAAK,CAAC1C,KAAK,CAAC,CAAC;MAC1C;MACA,IAAI,CAACrB,oBAAoB,GAAG,IAAI,CAACI,SAAS,CAACgE,WAAW;MACtD,IAAI,CAACjD,UAAU,CAAC6C,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5D,SAAS,CAACgE,WAAW,CAAC;IACtD;IACA,IAAI,CAAC9D,SAAS,CAAC+D,IAAI,CAAC/B,QAAQ,CAAC;IAC7BA,QAAQ,CAACgC,QAAQ,CAAC,IAAI,CAAC;IACvB,OAAO,IAAI;EACf;EACAC,YAAYA,CAACjE,SAAS,EAAE;IACpB,KAAK,IAAI8B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,SAAS,CAAC+B,MAAM,EAAE,EAAED,CAAC,EAAE;MACvC,IAAI,CAACyB,WAAW,CAACvD,SAAS,CAAC8B,CAAC,CAAC,CAAC;IAClC;IACA,OAAO,IAAI;EACf;EACAoC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACrE,YAAY,EACjB,OAAO,IAAI;IACf,MAAM6B,KAAK,GAAG,IAAI,CAACyC,UAAU,CAAC,CAAC;IAC/B,IAAI,CAACnE,SAAS,CAACoE,OAAO,CAAEpC,QAAQ,IAAK;MACjC,IAAI,CAACA,QAAQ,CAACL,QAAQ,CAAC,CAAC,EAAE;QACtBK,QAAQ,CAACP,QAAQ,CAACC,KAAK,CAAC;MAC5B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7B,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAsE,UAAUA,CAAA,EAAG;IACT,OAAOlF,OAAO,CAAC,IAAI,CAACyC,KAAK,EAAE,SAAS,EAAE,gCAAgC,CAAC;EAC3E;EACA2C,IAAIA,CAACC,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE7C,KAAK,EAAE;IACvCA,KAAK,GAAGA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI,CAACA,KAAK;IAC/D,IAAI,CAAC8C,WAAW,CAAC,CAAC;IAClB,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,MAAM,EAAE,EAAED,CAAC,EAAE;MAC5C,MAAME,QAAQ,GAAG,IAAI,CAAChC,SAAS,CAAC8B,CAAC,CAAC;MAClC,IAAIJ,KAAK,EAAE;QACPM,QAAQ,CAACP,QAAQ,CAACC,KAAK,CAAC;MAC5B;MACAzC,OAAO,CAAC+C,QAAQ,CAACL,QAAQ,CAAC,CAAC,EAAE,cAAc,EAAE,iDAAiD,CAAC;MAC/FK,QAAQ,CAACyC,UAAU,CAACH,OAAO,CAAC;MAC5BtC,QAAQ,CAAC0C,aAAa,CAAC,CAAC;IAC5B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}