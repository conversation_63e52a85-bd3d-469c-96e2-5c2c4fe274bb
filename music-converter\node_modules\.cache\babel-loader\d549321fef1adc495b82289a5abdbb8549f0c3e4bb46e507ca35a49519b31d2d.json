{"ast": null, "code": "import { Articulation } from './articulation.js';\nimport { Dot } from './dot.js';\nimport { FretHand<PERSON><PERSON> } from './frethandfinger.js';\nimport { Music } from './music.js';\nimport { Parser } from './parser.js';\nimport { Stem } from './stem.js';\nimport { defined, log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (EasyScore.DEBUG) log('VexFlow.EasyScore', args);\n}\nexport class EasyScoreGrammar {\n  constructor(builder) {\n    this.builder = builder;\n  }\n  begin() {\n    return this.LINE;\n  }\n  LINE() {\n    return {\n      expect: [this.PIECE, this.PIECES, this.EOL]\n    };\n  }\n  PIECE() {\n    return {\n      expect: [this.CHORDORNOTE, this.PARAMS],\n      run: () => this.builder.commitPiece()\n    };\n  }\n  PIECES() {\n    return {\n      expect: [this.COMMA, this.PIECE],\n      zeroOrMore: true\n    };\n  }\n  PARAMS() {\n    return {\n      expect: [this.DURATION, this.TYPE, this.DOTS, this.OPTS]\n    };\n  }\n  CHORDORNOTE() {\n    return {\n      expect: [this.CHORD, this.SINGLENOTE],\n      or: true\n    };\n  }\n  CHORD() {\n    return {\n      expect: [this.LPAREN, this.NOTES, this.RPAREN],\n      run: state => this.builder.addChord(state.matches[1])\n    };\n  }\n  NOTES() {\n    return {\n      expect: [this.NOTE],\n      oneOrMore: true\n    };\n  }\n  NOTE() {\n    return {\n      expect: [this.NOTENAME, this.ACCIDENTAL, this.OCTAVE]\n    };\n  }\n  SINGLENOTE() {\n    return {\n      expect: [this.NOTENAME, this.ACCIDENTAL, this.OCTAVE],\n      run: state => {\n        const s = state;\n        this.builder.addSingleNote(s.matches[0], s.matches[1], s.matches[2]);\n      }\n    };\n  }\n  ACCIDENTAL() {\n    return {\n      expect: [this.MICROTONES, this.ACCIDENTALS],\n      maybe: true,\n      or: true\n    };\n  }\n  DOTS() {\n    return {\n      expect: [this.DOT],\n      zeroOrMore: true,\n      run: state => this.builder.setNoteDots(state.matches)\n    };\n  }\n  TYPE() {\n    return {\n      expect: [this.SLASH, this.MAYBESLASH, this.TYPES],\n      maybe: true,\n      run: state => this.builder.setNoteType(state.matches[2])\n    };\n  }\n  DURATION() {\n    return {\n      expect: [this.SLASH, this.DURATIONS],\n      maybe: true,\n      run: state => this.builder.setNoteDuration(state.matches[1])\n    };\n  }\n  OPTS() {\n    return {\n      expect: [this.LBRACKET, this.KEYVAL, this.KEYVALS, this.RBRACKET],\n      maybe: true\n    };\n  }\n  KEYVALS() {\n    return {\n      expect: [this.COMMA, this.KEYVAL],\n      zeroOrMore: true\n    };\n  }\n  KEYVAL() {\n    const unquote = str => str.slice(1, -1);\n    return {\n      expect: [this.KEY, this.EQUALS, this.VAL],\n      run: state => this.builder.addNoteOption(state.matches[0], unquote(state.matches[2]))\n    };\n  }\n  VAL() {\n    return {\n      expect: [this.SVAL, this.DVAL],\n      or: true\n    };\n  }\n  KEY() {\n    return {\n      token: '[a-zA-Z][a-zA-Z0-9]*'\n    };\n  }\n  DVAL() {\n    return {\n      token: '[\"][^\"]*[\"]'\n    };\n  }\n  SVAL() {\n    return {\n      token: \"['][^']*[']\"\n    };\n  }\n  NOTENAME() {\n    return {\n      token: '[a-gA-G]'\n    };\n  }\n  OCTAVE() {\n    return {\n      token: '[0-9]+'\n    };\n  }\n  ACCIDENTALS() {\n    return {\n      token: 'bb|b|##|#|n'\n    };\n  }\n  MICROTONES() {\n    return {\n      token: 'bbs|bss|bs|db|d|\\\\+\\\\+-|\\\\+-|\\\\+\\\\+|\\\\+|k|o'\n    };\n  }\n  DURATIONS() {\n    return {\n      token: '[0-9whq]+'\n    };\n  }\n  TYPES() {\n    return {\n      token: '[rRsSmMhHgG]'\n    };\n  }\n  LPAREN() {\n    return {\n      token: '[(]'\n    };\n  }\n  RPAREN() {\n    return {\n      token: '[)]'\n    };\n  }\n  COMMA() {\n    return {\n      token: '[,]'\n    };\n  }\n  DOT() {\n    return {\n      token: '[.]'\n    };\n  }\n  SLASH() {\n    return {\n      token: '[/]'\n    };\n  }\n  MAYBESLASH() {\n    return {\n      token: '[/]?'\n    };\n  }\n  EQUALS() {\n    return {\n      token: '[=]'\n    };\n  }\n  LBRACKET() {\n    return {\n      token: '\\\\['\n    };\n  }\n  RBRACKET() {\n    return {\n      token: '\\\\]'\n    };\n  }\n  EOL() {\n    return {\n      token: '$'\n    };\n  }\n}\nexport class Piece {\n  constructor(duration) {\n    this.chord = [];\n    this.dots = 0;\n    this.options = {};\n    this.duration = duration;\n  }\n}\nexport class Builder {\n  constructor(factory) {\n    this.commitHooks = [];\n    this.factory = factory;\n    this.reset();\n  }\n  reset(options) {\n    this.options = Object.assign({\n      stem: 'auto',\n      clef: 'treble'\n    }, options);\n    this.elements = {\n      notes: [],\n      accidentals: []\n    };\n    this.rollingDuration = '8';\n    this.resetPiece();\n  }\n  getFactory() {\n    return this.factory;\n  }\n  getElements() {\n    return this.elements;\n  }\n  addCommitHook(commitHook) {\n    this.commitHooks.push(commitHook);\n  }\n  resetPiece() {\n    L('resetPiece');\n    this.piece = new Piece(this.rollingDuration);\n  }\n  setNoteDots(dots) {\n    L('setNoteDots:', dots);\n    if (dots) this.piece.dots = dots.length;\n  }\n  setNoteDuration(duration) {\n    L('setNoteDuration:', duration);\n    this.rollingDuration = this.piece.duration = duration || this.rollingDuration;\n  }\n  setNoteType(type) {\n    L('setNoteType:', type);\n    if (type) this.piece.type = type;\n  }\n  addNoteOption(key, value) {\n    L('addNoteOption: key:', key, 'value:', value);\n    this.piece.options[key] = value;\n  }\n  addNote(key, accid, octave) {\n    L('addNote:', key, accid, octave);\n    this.piece.chord.push({\n      key: key,\n      accid,\n      octave\n    });\n  }\n  addSingleNote(key, accid, octave) {\n    L('addSingleNote:', key, accid, octave);\n    this.addNote(key, accid, octave);\n  }\n  addChord(notes) {\n    L('startChord');\n    if (typeof notes[0] !== 'object') {\n      this.addSingleNote(notes[0]);\n    } else {\n      notes.forEach(n => {\n        if (n) this.addNote(...n);\n      });\n    }\n    L('endChord');\n  }\n  commitPiece() {\n    L('commitPiece');\n    const {\n      factory\n    } = this;\n    if (!factory) return;\n    const options = Object.assign(Object.assign({}, this.options), this.piece.options);\n    const stem = defined(options.stem, 'BadArguments', 'options.stem is not defined').toLowerCase();\n    const clef = defined(options.clef, 'BadArguments', 'options.clef is not defined').toLowerCase();\n    const {\n      chord,\n      duration,\n      dots,\n      type\n    } = this.piece;\n    const standardAccidentals = Music.accidentals;\n    const keys = chord.map(notePiece => {\n      var _a;\n      return notePiece.key + (standardAccidentals.includes((_a = notePiece.accid) !== null && _a !== void 0 ? _a : '') ? notePiece.accid : '') + '/' + notePiece.octave;\n    });\n    const autoStem = stem === 'auto';\n    const note = (type === null || type === void 0 ? void 0 : type.toLowerCase()) === 'g' ? factory.GhostNote({\n      duration,\n      dots\n    }) : factory.StaveNote({\n      keys,\n      duration,\n      dots,\n      type,\n      clef,\n      autoStem\n    });\n    if (!autoStem) note.setStemDirection(stem === 'up' ? Stem.UP : Stem.DOWN);\n    const accidentals = [];\n    chord.forEach((notePiece, index) => {\n      const accid = notePiece.accid;\n      if (typeof accid === 'string') {\n        const accidental = factory.Accidental({\n          type: accid\n        });\n        note.addModifier(accidental, index);\n        accidentals.push(accidental);\n      } else {\n        accidentals.push(undefined);\n      }\n    });\n    for (let i = 0; i < dots; i++) Dot.buildAndAttach([note], {\n      all: true\n    });\n    this.commitHooks.forEach(commitHook => commitHook(options, note, this));\n    this.elements.notes.push(note);\n    this.elements.accidentals.push(accidentals);\n    this.resetPiece();\n  }\n}\nfunction setId(options, note) {\n  if (options.id === undefined) return;\n  note.setAttribute('id', options.id);\n}\nconst commaSeparatedRegex = /\\s*,\\s*/;\nfunction setClass(options, note) {\n  if (options.class === undefined) return;\n  options.class.split(commaSeparatedRegex).forEach(className => note.addClass(className));\n}\nexport class EasyScore {\n  constructor(options = {}) {\n    this.defaults = {\n      clef: 'treble',\n      time: '4/4',\n      stem: 'auto'\n    };\n    this.setOptions(options);\n  }\n  set(defaults) {\n    this.defaults = Object.assign(Object.assign({}, this.defaults), defaults);\n    return this;\n  }\n  setOptions(options) {\n    var _a, _b;\n    const factory = options.factory;\n    const builder = (_a = options.builder) !== null && _a !== void 0 ? _a : new Builder(factory);\n    this.options = Object.assign(Object.assign({\n      commitHooks: [setId, setClass, Articulation.easyScoreHook, FretHandFinger.easyScoreHook],\n      throwOnError: false\n    }, options), {\n      factory,\n      builder\n    });\n    this.factory = factory;\n    this.builder = builder;\n    this.grammar = new EasyScoreGrammar(this.builder);\n    this.parser = new Parser(this.grammar);\n    (_b = this.options.commitHooks) === null || _b === void 0 ? void 0 : _b.forEach(commitHook => this.addCommitHook(commitHook));\n    return this;\n  }\n  setContext(context) {\n    this.factory.setContext(context);\n    return this;\n  }\n  parse(line, options = {}) {\n    this.builder.reset(options);\n    const result = this.parser.parse(line);\n    if (!result.success && this.options.throwOnError) {\n      L(result);\n      throw new RuntimeError('Error parsing line: ' + line);\n    }\n    return result;\n  }\n  beam(notes, options) {\n    this.factory.Beam({\n      notes,\n      options\n    });\n    return notes;\n  }\n  tuplet(notes, options) {\n    this.factory.Tuplet({\n      notes,\n      options\n    });\n    return notes;\n  }\n  notes(line, options = {}) {\n    options = Object.assign({\n      clef: this.defaults.clef,\n      stem: this.defaults.stem\n    }, options);\n    this.parse(line, options);\n    return this.builder.getElements().notes;\n  }\n  voice(notes, options = {}) {\n    options = Object.assign({\n      time: this.defaults.time\n    }, options);\n    return this.factory.Voice(options).addTickables(notes);\n  }\n  addCommitHook(commitHook) {\n    this.builder.addCommitHook(commitHook);\n  }\n}\nEasyScore.DEBUG = false;", "map": {"version": 3, "names": ["Articulation", "Dot", "FretHandFinger", "Music", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "defined", "log", "RuntimeError", "L", "args", "EasyScore", "DEBUG", "EasyScoreGrammar", "constructor", "builder", "begin", "LINE", "expect", "PIECE", "PIECES", "EOL", "CHORDORNOTE", "PARAMS", "run", "<PERSON><PERSON><PERSON><PERSON>", "COMMA", "zeroOrMore", "DURATION", "TYPE", "DOTS", "OPTS", "CHORD", "SINGLENOTE", "or", "LPAREN", "NOTES", "RPAREN", "state", "addChord", "matches", "NOTE", "oneOrMore", "NOTENAME", "ACCIDENTAL", "OCTAVE", "s", "addSingleNote", "MICROTONES", "ACCIDENTALS", "maybe", "DOT", "setNoteDots", "SLASH", "MAYBESLASH", "TYPES", "setNoteType", "DURATIONS", "setNoteDuration", "LBRACKET", "KEYVAL", "KEYVALS", "RBRACKET", "unquote", "str", "slice", "KEY", "EQUALS", "VAL", "addNoteOption", "SVAL", "DVAL", "token", "Piece", "duration", "chord", "dots", "options", "Builder", "factory", "<PERSON><PERSON>ooks", "reset", "Object", "assign", "stem", "clef", "elements", "notes", "accidentals", "rollingDuration", "reset<PERSON><PERSON><PERSON>", "getFactory", "getElements", "addCommitHook", "commitHook", "push", "piece", "length", "type", "key", "value", "addNote", "accid", "octave", "for<PERSON>ach", "n", "toLowerCase", "standardAccidentals", "keys", "map", "notePiece", "_a", "includes", "autoStem", "note", "GhostNote", "StaveNote", "setStemDirection", "UP", "DOWN", "index", "accidental", "Accidental", "addModifier", "undefined", "i", "buildAndAttach", "all", "setId", "id", "setAttribute", "commaSeparatedRegex", "setClass", "class", "split", "className", "addClass", "defaults", "time", "setOptions", "set", "_b", "easyScoreHook", "throwOnError", "grammar", "parser", "setContext", "context", "parse", "line", "result", "success", "beam", "<PERSON><PERSON>", "tuplet", "Tuplet", "voice", "Voice", "addTickables"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/easyscore.js"], "sourcesContent": ["import { Articulation } from './articulation.js';\nimport { Dot } from './dot.js';\nimport { FretHand<PERSON><PERSON> } from './frethandfinger.js';\nimport { Music } from './music.js';\nimport { Parser } from './parser.js';\nimport { Stem } from './stem.js';\nimport { defined, log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (EasyScore.DEBUG)\n        log('VexFlow.EasyScore', args);\n}\nexport class EasyScoreGrammar {\n    constructor(builder) {\n        this.builder = builder;\n    }\n    begin() {\n        return this.LINE;\n    }\n    LINE() {\n        return {\n            expect: [this.PIECE, this.PIECES, this.EOL],\n        };\n    }\n    PIECE() {\n        return {\n            expect: [this.CHORDORNOTE, this.PARAMS],\n            run: () => this.builder.commitPiece(),\n        };\n    }\n    PIECES() {\n        return {\n            expect: [this.COMMA, this.PIECE],\n            zeroOrMore: true,\n        };\n    }\n    PARAMS() {\n        return {\n            expect: [this.DURATION, this.TYPE, this.DOTS, this.OPTS],\n        };\n    }\n    CHORDORNOTE() {\n        return {\n            expect: [this.CHORD, this.SINGLENOTE],\n            or: true,\n        };\n    }\n    CHORD() {\n        return {\n            expect: [this.LPAREN, this.NOTES, this.RPAREN],\n            run: (state) => this.builder.addChord(state.matches[1]),\n        };\n    }\n    NOTES() {\n        return {\n            expect: [this.NOTE],\n            oneOrMore: true,\n        };\n    }\n    NOTE() {\n        return {\n            expect: [this.NOTENAME, this.ACCIDENTAL, this.OCTAVE],\n        };\n    }\n    SINGLENOTE() {\n        return {\n            expect: [this.NOTENAME, this.ACCIDENTAL, this.OCTAVE],\n            run: (state) => {\n                const s = state;\n                this.builder.addSingleNote(s.matches[0], s.matches[1], s.matches[2]);\n            },\n        };\n    }\n    ACCIDENTAL() {\n        return {\n            expect: [this.MICROTONES, this.ACCIDENTALS],\n            maybe: true,\n            or: true,\n        };\n    }\n    DOTS() {\n        return {\n            expect: [this.DOT],\n            zeroOrMore: true,\n            run: (state) => this.builder.setNoteDots(state.matches),\n        };\n    }\n    TYPE() {\n        return {\n            expect: [this.SLASH, this.MAYBESLASH, this.TYPES],\n            maybe: true,\n            run: (state) => this.builder.setNoteType(state.matches[2]),\n        };\n    }\n    DURATION() {\n        return {\n            expect: [this.SLASH, this.DURATIONS],\n            maybe: true,\n            run: (state) => this.builder.setNoteDuration(state.matches[1]),\n        };\n    }\n    OPTS() {\n        return {\n            expect: [this.LBRACKET, this.KEYVAL, this.KEYVALS, this.RBRACKET],\n            maybe: true,\n        };\n    }\n    KEYVALS() {\n        return {\n            expect: [this.COMMA, this.KEYVAL],\n            zeroOrMore: true,\n        };\n    }\n    KEYVAL() {\n        const unquote = (str) => str.slice(1, -1);\n        return {\n            expect: [this.KEY, this.EQUALS, this.VAL],\n            run: (state) => this.builder.addNoteOption(state.matches[0], unquote(state.matches[2])),\n        };\n    }\n    VAL() {\n        return {\n            expect: [this.SVAL, this.DVAL],\n            or: true,\n        };\n    }\n    KEY() {\n        return { token: '[a-zA-Z][a-zA-Z0-9]*' };\n    }\n    DVAL() {\n        return { token: '[\"][^\"]*[\"]' };\n    }\n    SVAL() {\n        return { token: \"['][^']*[']\" };\n    }\n    NOTENAME() {\n        return { token: '[a-gA-G]' };\n    }\n    OCTAVE() {\n        return { token: '[0-9]+' };\n    }\n    ACCIDENTALS() {\n        return { token: 'bb|b|##|#|n' };\n    }\n    MICROTONES() {\n        return { token: 'bbs|bss|bs|db|d|\\\\+\\\\+-|\\\\+-|\\\\+\\\\+|\\\\+|k|o' };\n    }\n    DURATIONS() {\n        return { token: '[0-9whq]+' };\n    }\n    TYPES() {\n        return { token: '[rRsSmMhHgG]' };\n    }\n    LPAREN() {\n        return { token: '[(]' };\n    }\n    RPAREN() {\n        return { token: '[)]' };\n    }\n    COMMA() {\n        return { token: '[,]' };\n    }\n    DOT() {\n        return { token: '[.]' };\n    }\n    SLASH() {\n        return { token: '[/]' };\n    }\n    MAYBESLASH() {\n        return { token: '[/]?' };\n    }\n    EQUALS() {\n        return { token: '[=]' };\n    }\n    LBRACKET() {\n        return { token: '\\\\[' };\n    }\n    RBRACKET() {\n        return { token: '\\\\]' };\n    }\n    EOL() {\n        return { token: '$' };\n    }\n}\nexport class Piece {\n    constructor(duration) {\n        this.chord = [];\n        this.dots = 0;\n        this.options = {};\n        this.duration = duration;\n    }\n}\nexport class Builder {\n    constructor(factory) {\n        this.commitHooks = [];\n        this.factory = factory;\n        this.reset();\n    }\n    reset(options) {\n        this.options = Object.assign({ stem: 'auto', clef: 'treble' }, options);\n        this.elements = { notes: [], accidentals: [] };\n        this.rollingDuration = '8';\n        this.resetPiece();\n    }\n    getFactory() {\n        return this.factory;\n    }\n    getElements() {\n        return this.elements;\n    }\n    addCommitHook(commitHook) {\n        this.commitHooks.push(commitHook);\n    }\n    resetPiece() {\n        L('resetPiece');\n        this.piece = new Piece(this.rollingDuration);\n    }\n    setNoteDots(dots) {\n        L('setNoteDots:', dots);\n        if (dots)\n            this.piece.dots = dots.length;\n    }\n    setNoteDuration(duration) {\n        L('setNoteDuration:', duration);\n        this.rollingDuration = this.piece.duration = duration || this.rollingDuration;\n    }\n    setNoteType(type) {\n        L('setNoteType:', type);\n        if (type)\n            this.piece.type = type;\n    }\n    addNoteOption(key, value) {\n        L('addNoteOption: key:', key, 'value:', value);\n        this.piece.options[key] = value;\n    }\n    addNote(key, accid, octave) {\n        L('addNote:', key, accid, octave);\n        this.piece.chord.push({\n            key: key,\n            accid,\n            octave,\n        });\n    }\n    addSingleNote(key, accid, octave) {\n        L('addSingleNote:', key, accid, octave);\n        this.addNote(key, accid, octave);\n    }\n    addChord(notes) {\n        L('startChord');\n        if (typeof notes[0] !== 'object') {\n            this.addSingleNote(notes[0]);\n        }\n        else {\n            notes.forEach((n) => {\n                if (n)\n                    this.addNote(...n);\n            });\n        }\n        L('endChord');\n    }\n    commitPiece() {\n        L('commitPiece');\n        const { factory } = this;\n        if (!factory)\n            return;\n        const options = Object.assign(Object.assign({}, this.options), this.piece.options);\n        const stem = defined(options.stem, 'BadArguments', 'options.stem is not defined').toLowerCase();\n        const clef = defined(options.clef, 'BadArguments', 'options.clef is not defined').toLowerCase();\n        const { chord, duration, dots, type } = this.piece;\n        const standardAccidentals = Music.accidentals;\n        const keys = chord.map((notePiece) => {\n            var _a;\n            return notePiece.key +\n                (standardAccidentals.includes((_a = notePiece.accid) !== null && _a !== void 0 ? _a : '') ? notePiece.accid : '') +\n                '/' +\n                notePiece.octave;\n        });\n        const autoStem = stem === 'auto';\n        const note = (type === null || type === void 0 ? void 0 : type.toLowerCase()) === 'g'\n            ? factory.GhostNote({ duration, dots })\n            : factory.StaveNote({ keys, duration, dots, type, clef, autoStem });\n        if (!autoStem)\n            note.setStemDirection(stem === 'up' ? Stem.UP : Stem.DOWN);\n        const accidentals = [];\n        chord.forEach((notePiece, index) => {\n            const accid = notePiece.accid;\n            if (typeof accid === 'string') {\n                const accidental = factory.Accidental({ type: accid });\n                note.addModifier(accidental, index);\n                accidentals.push(accidental);\n            }\n            else {\n                accidentals.push(undefined);\n            }\n        });\n        for (let i = 0; i < dots; i++)\n            Dot.buildAndAttach([note], { all: true });\n        this.commitHooks.forEach((commitHook) => commitHook(options, note, this));\n        this.elements.notes.push(note);\n        this.elements.accidentals.push(accidentals);\n        this.resetPiece();\n    }\n}\nfunction setId(options, note) {\n    if (options.id === undefined)\n        return;\n    note.setAttribute('id', options.id);\n}\nconst commaSeparatedRegex = /\\s*,\\s*/;\nfunction setClass(options, note) {\n    if (options.class === undefined)\n        return;\n    options.class.split(commaSeparatedRegex).forEach((className) => note.addClass(className));\n}\nexport class EasyScore {\n    constructor(options = {}) {\n        this.defaults = {\n            clef: 'treble',\n            time: '4/4',\n            stem: 'auto',\n        };\n        this.setOptions(options);\n    }\n    set(defaults) {\n        this.defaults = Object.assign(Object.assign({}, this.defaults), defaults);\n        return this;\n    }\n    setOptions(options) {\n        var _a, _b;\n        const factory = options.factory;\n        const builder = (_a = options.builder) !== null && _a !== void 0 ? _a : new Builder(factory);\n        this.options = Object.assign(Object.assign({ commitHooks: [setId, setClass, Articulation.easyScoreHook, FretHandFinger.easyScoreHook], throwOnError: false }, options), { factory,\n            builder });\n        this.factory = factory;\n        this.builder = builder;\n        this.grammar = new EasyScoreGrammar(this.builder);\n        this.parser = new Parser(this.grammar);\n        (_b = this.options.commitHooks) === null || _b === void 0 ? void 0 : _b.forEach((commitHook) => this.addCommitHook(commitHook));\n        return this;\n    }\n    setContext(context) {\n        this.factory.setContext(context);\n        return this;\n    }\n    parse(line, options = {}) {\n        this.builder.reset(options);\n        const result = this.parser.parse(line);\n        if (!result.success && this.options.throwOnError) {\n            L(result);\n            throw new RuntimeError('Error parsing line: ' + line);\n        }\n        return result;\n    }\n    beam(notes, options) {\n        this.factory.Beam({ notes, options });\n        return notes;\n    }\n    tuplet(notes, options) {\n        this.factory.Tuplet({ notes, options });\n        return notes;\n    }\n    notes(line, options = {}) {\n        options = Object.assign({ clef: this.defaults.clef, stem: this.defaults.stem }, options);\n        this.parse(line, options);\n        return this.builder.getElements().notes;\n    }\n    voice(notes, options = {}) {\n        options = Object.assign({ time: this.defaults.time }, options);\n        return this.factory.Voice(options).addTickables(notes);\n    }\n    addCommitHook(commitHook) {\n        this.builder.addCommitHook(commitHook);\n    }\n}\nEasyScore.DEBUG = false;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,GAAG,QAAQ,UAAU;AAC9B,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,EAAEC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AACtD,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,SAAS,CAACC,KAAK,EACfL,GAAG,CAAC,mBAAmB,EAAEG,IAAI,CAAC;AACtC;AACA,OAAO,MAAMG,gBAAgB,CAAC;EAC1BC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACC,IAAI;EACpB;EACAA,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,MAAM,EAAE,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,GAAG;IAC9C,CAAC;EACL;EACAF,KAAKA,CAAA,EAAG;IACJ,OAAO;MACHD,MAAM,EAAE,CAAC,IAAI,CAACI,WAAW,EAAE,IAAI,CAACC,MAAM,CAAC;MACvCC,GAAG,EAAEA,CAAA,KAAM,IAAI,CAACT,OAAO,CAACU,WAAW,CAAC;IACxC,CAAC;EACL;EACAL,MAAMA,CAAA,EAAG;IACL,OAAO;MACHF,MAAM,EAAE,CAAC,IAAI,CAACQ,KAAK,EAAE,IAAI,CAACP,KAAK,CAAC;MAChCQ,UAAU,EAAE;IAChB,CAAC;EACL;EACAJ,MAAMA,CAAA,EAAG;IACL,OAAO;MACHL,MAAM,EAAE,CAAC,IAAI,CAACU,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI;IAC3D,CAAC;EACL;EACAT,WAAWA,CAAA,EAAG;IACV,OAAO;MACHJ,MAAM,EAAE,CAAC,IAAI,CAACc,KAAK,EAAE,IAAI,CAACC,UAAU,CAAC;MACrCC,EAAE,EAAE;IACR,CAAC;EACL;EACAF,KAAKA,CAAA,EAAG;IACJ,OAAO;MACHd,MAAM,EAAE,CAAC,IAAI,CAACiB,MAAM,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC;MAC9Cb,GAAG,EAAGc,KAAK,IAAK,IAAI,CAACvB,OAAO,CAACwB,QAAQ,CAACD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IAC1D,CAAC;EACL;EACAJ,KAAKA,CAAA,EAAG;IACJ,OAAO;MACHlB,MAAM,EAAE,CAAC,IAAI,CAACuB,IAAI,CAAC;MACnBC,SAAS,EAAE;IACf,CAAC;EACL;EACAD,IAAIA,CAAA,EAAG;IACH,OAAO;MACHvB,MAAM,EAAE,CAAC,IAAI,CAACyB,QAAQ,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,MAAM;IACxD,CAAC;EACL;EACAZ,UAAUA,CAAA,EAAG;IACT,OAAO;MACHf,MAAM,EAAE,CAAC,IAAI,CAACyB,QAAQ,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,MAAM,CAAC;MACrDrB,GAAG,EAAGc,KAAK,IAAK;QACZ,MAAMQ,CAAC,GAAGR,KAAK;QACf,IAAI,CAACvB,OAAO,CAACgC,aAAa,CAACD,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,EAAEM,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,EAAEM,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,CAAC;MACxE;IACJ,CAAC;EACL;EACAI,UAAUA,CAAA,EAAG;IACT,OAAO;MACH1B,MAAM,EAAE,CAAC,IAAI,CAAC8B,UAAU,EAAE,IAAI,CAACC,WAAW,CAAC;MAC3CC,KAAK,EAAE,IAAI;MACXhB,EAAE,EAAE;IACR,CAAC;EACL;EACAJ,IAAIA,CAAA,EAAG;IACH,OAAO;MACHZ,MAAM,EAAE,CAAC,IAAI,CAACiC,GAAG,CAAC;MAClBxB,UAAU,EAAE,IAAI;MAChBH,GAAG,EAAGc,KAAK,IAAK,IAAI,CAACvB,OAAO,CAACqC,WAAW,CAACd,KAAK,CAACE,OAAO;IAC1D,CAAC;EACL;EACAX,IAAIA,CAAA,EAAG;IACH,OAAO;MACHX,MAAM,EAAE,CAAC,IAAI,CAACmC,KAAK,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,KAAK,CAAC;MACjDL,KAAK,EAAE,IAAI;MACX1B,GAAG,EAAGc,KAAK,IAAK,IAAI,CAACvB,OAAO,CAACyC,WAAW,CAAClB,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;EACL;EACAZ,QAAQA,CAAA,EAAG;IACP,OAAO;MACHV,MAAM,EAAE,CAAC,IAAI,CAACmC,KAAK,EAAE,IAAI,CAACI,SAAS,CAAC;MACpCP,KAAK,EAAE,IAAI;MACX1B,GAAG,EAAGc,KAAK,IAAK,IAAI,CAACvB,OAAO,CAAC2C,eAAe,CAACpB,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;EACL;EACAT,IAAIA,CAAA,EAAG;IACH,OAAO;MACHb,MAAM,EAAE,CAAC,IAAI,CAACyC,QAAQ,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAC;MACjEZ,KAAK,EAAE;IACX,CAAC;EACL;EACAW,OAAOA,CAAA,EAAG;IACN,OAAO;MACH3C,MAAM,EAAE,CAAC,IAAI,CAACQ,KAAK,EAAE,IAAI,CAACkC,MAAM,CAAC;MACjCjC,UAAU,EAAE;IAChB,CAAC;EACL;EACAiC,MAAMA,CAAA,EAAG;IACL,MAAMG,OAAO,GAAIC,GAAG,IAAKA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,OAAO;MACH/C,MAAM,EAAE,CAAC,IAAI,CAACgD,GAAG,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,GAAG,CAAC;MACzC5C,GAAG,EAAGc,KAAK,IAAK,IAAI,CAACvB,OAAO,CAACsD,aAAa,CAAC/B,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,EAAEuB,OAAO,CAACzB,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1F,CAAC;EACL;EACA4B,GAAGA,CAAA,EAAG;IACF,OAAO;MACHlD,MAAM,EAAE,CAAC,IAAI,CAACoD,IAAI,EAAE,IAAI,CAACC,IAAI,CAAC;MAC9BrC,EAAE,EAAE;IACR,CAAC;EACL;EACAgC,GAAGA,CAAA,EAAG;IACF,OAAO;MAAEM,KAAK,EAAE;IAAuB,CAAC;EAC5C;EACAD,IAAIA,CAAA,EAAG;IACH,OAAO;MAAEC,KAAK,EAAE;IAAc,CAAC;EACnC;EACAF,IAAIA,CAAA,EAAG;IACH,OAAO;MAAEE,KAAK,EAAE;IAAc,CAAC;EACnC;EACA7B,QAAQA,CAAA,EAAG;IACP,OAAO;MAAE6B,KAAK,EAAE;IAAW,CAAC;EAChC;EACA3B,MAAMA,CAAA,EAAG;IACL,OAAO;MAAE2B,KAAK,EAAE;IAAS,CAAC;EAC9B;EACAvB,WAAWA,CAAA,EAAG;IACV,OAAO;MAAEuB,KAAK,EAAE;IAAc,CAAC;EACnC;EACAxB,UAAUA,CAAA,EAAG;IACT,OAAO;MAAEwB,KAAK,EAAE;IAA8C,CAAC;EACnE;EACAf,SAASA,CAAA,EAAG;IACR,OAAO;MAAEe,KAAK,EAAE;IAAY,CAAC;EACjC;EACAjB,KAAKA,CAAA,EAAG;IACJ,OAAO;MAAEiB,KAAK,EAAE;IAAe,CAAC;EACpC;EACArC,MAAMA,CAAA,EAAG;IACL,OAAO;MAAEqC,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAnC,MAAMA,CAAA,EAAG;IACL,OAAO;MAAEmC,KAAK,EAAE;IAAM,CAAC;EAC3B;EACA9C,KAAKA,CAAA,EAAG;IACJ,OAAO;MAAE8C,KAAK,EAAE;IAAM,CAAC;EAC3B;EACArB,GAAGA,CAAA,EAAG;IACF,OAAO;MAAEqB,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAnB,KAAKA,CAAA,EAAG;IACJ,OAAO;MAAEmB,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAlB,UAAUA,CAAA,EAAG;IACT,OAAO;MAAEkB,KAAK,EAAE;IAAO,CAAC;EAC5B;EACAL,MAAMA,CAAA,EAAG;IACL,OAAO;MAAEK,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAb,QAAQA,CAAA,EAAG;IACP,OAAO;MAAEa,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAV,QAAQA,CAAA,EAAG;IACP,OAAO;MAAEU,KAAK,EAAE;IAAM,CAAC;EAC3B;EACAnD,GAAGA,CAAA,EAAG;IACF,OAAO;MAAEmD,KAAK,EAAE;IAAI,CAAC;EACzB;AACJ;AACA,OAAO,MAAMC,KAAK,CAAC;EACf3D,WAAWA,CAAC4D,QAAQ,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACH,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA,OAAO,MAAMI,OAAO,CAAC;EACjBhE,WAAWA,CAACiE,OAAO,EAAE;IACjB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAACJ,OAAO,EAAE;IACX,IAAI,CAACA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAS,CAAC,EAAER,OAAO,CAAC;IACvE,IAAI,CAACS,QAAQ,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC;IAC9C,IAAI,CAACC,eAAe,GAAG,GAAG;IAC1B,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACZ,OAAO;EACvB;EACAa,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACN,QAAQ;EACxB;EACAO,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACd,WAAW,CAACe,IAAI,CAACD,UAAU,CAAC;EACrC;EACAJ,UAAUA,CAAA,EAAG;IACTjF,CAAC,CAAC,YAAY,CAAC;IACf,IAAI,CAACuF,KAAK,GAAG,IAAIvB,KAAK,CAAC,IAAI,CAACgB,eAAe,CAAC;EAChD;EACArC,WAAWA,CAACwB,IAAI,EAAE;IACdnE,CAAC,CAAC,cAAc,EAAEmE,IAAI,CAAC;IACvB,IAAIA,IAAI,EACJ,IAAI,CAACoB,KAAK,CAACpB,IAAI,GAAGA,IAAI,CAACqB,MAAM;EACrC;EACAvC,eAAeA,CAACgB,QAAQ,EAAE;IACtBjE,CAAC,CAAC,kBAAkB,EAAEiE,QAAQ,CAAC;IAC/B,IAAI,CAACe,eAAe,GAAG,IAAI,CAACO,KAAK,CAACtB,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACe,eAAe;EACjF;EACAjC,WAAWA,CAAC0C,IAAI,EAAE;IACdzF,CAAC,CAAC,cAAc,EAAEyF,IAAI,CAAC;IACvB,IAAIA,IAAI,EACJ,IAAI,CAACF,KAAK,CAACE,IAAI,GAAGA,IAAI;EAC9B;EACA7B,aAAaA,CAAC8B,GAAG,EAAEC,KAAK,EAAE;IACtB3F,CAAC,CAAC,qBAAqB,EAAE0F,GAAG,EAAE,QAAQ,EAAEC,KAAK,CAAC;IAC9C,IAAI,CAACJ,KAAK,CAACnB,OAAO,CAACsB,GAAG,CAAC,GAAGC,KAAK;EACnC;EACAC,OAAOA,CAACF,GAAG,EAAEG,KAAK,EAAEC,MAAM,EAAE;IACxB9F,CAAC,CAAC,UAAU,EAAE0F,GAAG,EAAEG,KAAK,EAAEC,MAAM,CAAC;IACjC,IAAI,CAACP,KAAK,CAACrB,KAAK,CAACoB,IAAI,CAAC;MAClBI,GAAG,EAAEA,GAAG;MACRG,KAAK;MACLC;IACJ,CAAC,CAAC;EACN;EACAxD,aAAaA,CAACoD,GAAG,EAAEG,KAAK,EAAEC,MAAM,EAAE;IAC9B9F,CAAC,CAAC,gBAAgB,EAAE0F,GAAG,EAAEG,KAAK,EAAEC,MAAM,CAAC;IACvC,IAAI,CAACF,OAAO,CAACF,GAAG,EAAEG,KAAK,EAAEC,MAAM,CAAC;EACpC;EACAhE,QAAQA,CAACgD,KAAK,EAAE;IACZ9E,CAAC,CAAC,YAAY,CAAC;IACf,IAAI,OAAO8E,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACxC,aAAa,CAACwC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,MACI;MACDA,KAAK,CAACiB,OAAO,CAAEC,CAAC,IAAK;QACjB,IAAIA,CAAC,EACD,IAAI,CAACJ,OAAO,CAAC,GAAGI,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACAhG,CAAC,CAAC,UAAU,CAAC;EACjB;EACAgB,WAAWA,CAAA,EAAG;IACVhB,CAAC,CAAC,aAAa,CAAC;IAChB,MAAM;MAAEsE;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAI,CAACA,OAAO,EACR;IACJ,MAAMF,OAAO,GAAGK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACN,OAAO,CAAC,EAAE,IAAI,CAACmB,KAAK,CAACnB,OAAO,CAAC;IAClF,MAAMO,IAAI,GAAG9E,OAAO,CAACuE,OAAO,CAACO,IAAI,EAAE,cAAc,EAAE,6BAA6B,CAAC,CAACsB,WAAW,CAAC,CAAC;IAC/F,MAAMrB,IAAI,GAAG/E,OAAO,CAACuE,OAAO,CAACQ,IAAI,EAAE,cAAc,EAAE,6BAA6B,CAAC,CAACqB,WAAW,CAAC,CAAC;IAC/F,MAAM;MAAE/B,KAAK;MAAED,QAAQ;MAAEE,IAAI;MAAEsB;IAAK,CAAC,GAAG,IAAI,CAACF,KAAK;IAClD,MAAMW,mBAAmB,GAAGxG,KAAK,CAACqF,WAAW;IAC7C,MAAMoB,IAAI,GAAGjC,KAAK,CAACkC,GAAG,CAAEC,SAAS,IAAK;MAClC,IAAIC,EAAE;MACN,OAAOD,SAAS,CAACX,GAAG,IACfQ,mBAAmB,CAACK,QAAQ,CAAC,CAACD,EAAE,GAAGD,SAAS,CAACR,KAAK,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,GAAGD,SAAS,CAACR,KAAK,GAAG,EAAE,CAAC,GACjH,GAAG,GACHQ,SAAS,CAACP,MAAM;IACxB,CAAC,CAAC;IACF,MAAMU,QAAQ,GAAG7B,IAAI,KAAK,MAAM;IAChC,MAAM8B,IAAI,GAAG,CAAChB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,WAAW,CAAC,CAAC,MAAM,GAAG,GAC/E3B,OAAO,CAACoC,SAAS,CAAC;MAAEzC,QAAQ;MAAEE;IAAK,CAAC,CAAC,GACrCG,OAAO,CAACqC,SAAS,CAAC;MAAER,IAAI;MAAElC,QAAQ;MAAEE,IAAI;MAAEsB,IAAI;MAAEb,IAAI;MAAE4B;IAAS,CAAC,CAAC;IACvE,IAAI,CAACA,QAAQ,EACTC,IAAI,CAACG,gBAAgB,CAACjC,IAAI,KAAK,IAAI,GAAG/E,IAAI,CAACiH,EAAE,GAAGjH,IAAI,CAACkH,IAAI,CAAC;IAC9D,MAAM/B,WAAW,GAAG,EAAE;IACtBb,KAAK,CAAC6B,OAAO,CAAC,CAACM,SAAS,EAAEU,KAAK,KAAK;MAChC,MAAMlB,KAAK,GAAGQ,SAAS,CAACR,KAAK;MAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,MAAMmB,UAAU,GAAG1C,OAAO,CAAC2C,UAAU,CAAC;UAAExB,IAAI,EAAEI;QAAM,CAAC,CAAC;QACtDY,IAAI,CAACS,WAAW,CAACF,UAAU,EAAED,KAAK,CAAC;QACnChC,WAAW,CAACO,IAAI,CAAC0B,UAAU,CAAC;MAChC,CAAC,MACI;QACDjC,WAAW,CAACO,IAAI,CAAC6B,SAAS,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjD,IAAI,EAAEiD,CAAC,EAAE,EACzB5H,GAAG,CAAC6H,cAAc,CAAC,CAACZ,IAAI,CAAC,EAAE;MAAEa,GAAG,EAAE;IAAK,CAAC,CAAC;IAC7C,IAAI,CAAC/C,WAAW,CAACwB,OAAO,CAAEV,UAAU,IAAKA,UAAU,CAACjB,OAAO,EAAEqC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAAC5B,QAAQ,CAACC,KAAK,CAACQ,IAAI,CAACmB,IAAI,CAAC;IAC9B,IAAI,CAAC5B,QAAQ,CAACE,WAAW,CAACO,IAAI,CAACP,WAAW,CAAC;IAC3C,IAAI,CAACE,UAAU,CAAC,CAAC;EACrB;AACJ;AACA,SAASsC,KAAKA,CAACnD,OAAO,EAAEqC,IAAI,EAAE;EAC1B,IAAIrC,OAAO,CAACoD,EAAE,KAAKL,SAAS,EACxB;EACJV,IAAI,CAACgB,YAAY,CAAC,IAAI,EAAErD,OAAO,CAACoD,EAAE,CAAC;AACvC;AACA,MAAME,mBAAmB,GAAG,SAAS;AACrC,SAASC,QAAQA,CAACvD,OAAO,EAAEqC,IAAI,EAAE;EAC7B,IAAIrC,OAAO,CAACwD,KAAK,KAAKT,SAAS,EAC3B;EACJ/C,OAAO,CAACwD,KAAK,CAACC,KAAK,CAACH,mBAAmB,CAAC,CAAC3B,OAAO,CAAE+B,SAAS,IAAKrB,IAAI,CAACsB,QAAQ,CAACD,SAAS,CAAC,CAAC;AAC7F;AACA,OAAO,MAAM5H,SAAS,CAAC;EACnBG,WAAWA,CAAC+D,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAAC4D,QAAQ,GAAG;MACZpD,IAAI,EAAE,QAAQ;MACdqD,IAAI,EAAE,KAAK;MACXtD,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACuD,UAAU,CAAC9D,OAAO,CAAC;EAC5B;EACA+D,GAAGA,CAACH,QAAQ,EAAE;IACV,IAAI,CAACA,QAAQ,GAAGvD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsD,QAAQ,CAAC,EAAEA,QAAQ,CAAC;IACzE,OAAO,IAAI;EACf;EACAE,UAAUA,CAAC9D,OAAO,EAAE;IAChB,IAAIkC,EAAE,EAAE8B,EAAE;IACV,MAAM9D,OAAO,GAAGF,OAAO,CAACE,OAAO;IAC/B,MAAMhE,OAAO,GAAG,CAACgG,EAAE,GAAGlC,OAAO,CAAC9D,OAAO,MAAM,IAAI,IAAIgG,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAIjC,OAAO,CAACC,OAAO,CAAC;IAC5F,IAAI,CAACF,OAAO,GAAGK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAAEH,WAAW,EAAE,CAACgD,KAAK,EAAEI,QAAQ,EAAEpI,YAAY,CAAC8I,aAAa,EAAE5I,cAAc,CAAC4I,aAAa,CAAC;MAAEC,YAAY,EAAE;IAAM,CAAC,EAAElE,OAAO,CAAC,EAAE;MAAEE,OAAO;MAC7KhE;IAAQ,CAAC,CAAC;IACd,IAAI,CAACgE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiI,OAAO,GAAG,IAAInI,gBAAgB,CAAC,IAAI,CAACE,OAAO,CAAC;IACjD,IAAI,CAACkI,MAAM,GAAG,IAAI7I,MAAM,CAAC,IAAI,CAAC4I,OAAO,CAAC;IACtC,CAACH,EAAE,GAAG,IAAI,CAAChE,OAAO,CAACG,WAAW,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrC,OAAO,CAAEV,UAAU,IAAK,IAAI,CAACD,aAAa,CAACC,UAAU,CAAC,CAAC;IAC/H,OAAO,IAAI;EACf;EACAoD,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAACpE,OAAO,CAACmE,UAAU,CAACC,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACAC,KAAKA,CAACC,IAAI,EAAExE,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAAC9D,OAAO,CAACkE,KAAK,CAACJ,OAAO,CAAC;IAC3B,MAAMyE,MAAM,GAAG,IAAI,CAACL,MAAM,CAACG,KAAK,CAACC,IAAI,CAAC;IACtC,IAAI,CAACC,MAAM,CAACC,OAAO,IAAI,IAAI,CAAC1E,OAAO,CAACkE,YAAY,EAAE;MAC9CtI,CAAC,CAAC6I,MAAM,CAAC;MACT,MAAM,IAAI9I,YAAY,CAAC,sBAAsB,GAAG6I,IAAI,CAAC;IACzD;IACA,OAAOC,MAAM;EACjB;EACAE,IAAIA,CAACjE,KAAK,EAAEV,OAAO,EAAE;IACjB,IAAI,CAACE,OAAO,CAAC0E,IAAI,CAAC;MAAElE,KAAK;MAAEV;IAAQ,CAAC,CAAC;IACrC,OAAOU,KAAK;EAChB;EACAmE,MAAMA,CAACnE,KAAK,EAAEV,OAAO,EAAE;IACnB,IAAI,CAACE,OAAO,CAAC4E,MAAM,CAAC;MAAEpE,KAAK;MAAEV;IAAQ,CAAC,CAAC;IACvC,OAAOU,KAAK;EAChB;EACAA,KAAKA,CAAC8D,IAAI,EAAExE,OAAO,GAAG,CAAC,CAAC,EAAE;IACtBA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC;MAAEE,IAAI,EAAE,IAAI,CAACoD,QAAQ,CAACpD,IAAI;MAAED,IAAI,EAAE,IAAI,CAACqD,QAAQ,CAACrD;IAAK,CAAC,EAAEP,OAAO,CAAC;IACxF,IAAI,CAACuE,KAAK,CAACC,IAAI,EAAExE,OAAO,CAAC;IACzB,OAAO,IAAI,CAAC9D,OAAO,CAAC6E,WAAW,CAAC,CAAC,CAACL,KAAK;EAC3C;EACAqE,KAAKA,CAACrE,KAAK,EAAEV,OAAO,GAAG,CAAC,CAAC,EAAE;IACvBA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC;MAAEuD,IAAI,EAAE,IAAI,CAACD,QAAQ,CAACC;IAAK,CAAC,EAAE7D,OAAO,CAAC;IAC9D,OAAO,IAAI,CAACE,OAAO,CAAC8E,KAAK,CAAChF,OAAO,CAAC,CAACiF,YAAY,CAACvE,KAAK,CAAC;EAC1D;EACAM,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAAC/E,OAAO,CAAC8E,aAAa,CAACC,UAAU,CAAC;EAC1C;AACJ;AACAnF,SAAS,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}