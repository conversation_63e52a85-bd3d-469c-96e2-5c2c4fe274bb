{"ast": null, "code": "// ----- CONVERSION FUNCTIONS ----- //\n\n// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'\n// MIDI FORMAT: 0-127\n// PARSE FORMAT: { step, alter, octave }\n\nconst SEMI_MAP = {\n  C: 0,\n  D: 2,\n  E: 4,\n  F: 5,\n  G: 7,\n  A: 9,\n  B: 11\n};\nexport function gigaConvert(key, time, notes) {\n  const defaultSetup = 'tabstave notaion=true tablature=false';\n  console.log(\"Key: \", key);\n  console.log(\"Time: \", time);\n  console.log(\"Notes: \", notes);\n  const parsedNotes = notes.map(parseNote);\n  const midiNotes = parsedNotes.map(toMidi + 9);\n  const newNotes = midiNotes.map(fromMidi);\n  const formattedNotes = newNotes.map(formatNote);\n  return `${defaultSetup} key=${key} time=${time} \\nnotes ${formattedNotes.join(' ')}`;\n}\nexport function parseNote(string) {\n  const m = string.match(/^([A-G])([#nb]?)[/](\\d)$/);\n  console.log(\"Match: \", m);\n  if (!m) {\n    throw new Error(`Invalid note format: ${string}`);\n  }\n  const [step, acc, octave] = m;\n  return {\n    step,\n    alter: acc === '#' ? 1 : acc === 'b' ? -1 : 0,\n    octave: parseInt(octave, 10)\n  };\n}\nexport function toMidi(parsedNote) {\n  const {\n    step,\n    alter,\n    octave\n  } = parsedNote;\n  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;\n  return midi;\n}\nexport function fromMidi(midiNote) {\n  const octave = Math.floor(midiNote / 12) - 1;\n  const semi = midiNote % 12;\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\n    if (val === semi) {\n      return {\n        step,\n        alter: 0,\n        octave\n      };\n    }\n  }\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\n    if (val + 1 === semi) {\n      return {\n        step,\n        alter: 1,\n        octave\n      };\n    }\n  }\n}\nexport function formatNote(parsedNote) {\n  const {\n    step,\n    alter,\n    octave\n  } = parsedNote;\n  let acc = '';\n  if (alter === 1) {\n    acc = '#';\n  }\n  return `${step}${acc}/${octave}`;\n}", "map": {"version": 3, "names": ["SEMI_MAP", "C", "D", "E", "F", "G", "A", "B", "gigaConvert", "key", "time", "notes", "defaultSetup", "console", "log", "parsedNotes", "map", "parseNote", "midiNotes", "<PERSON><PERSON><PERSON><PERSON>", "newNotes", "fromMidi", "formattedNotes", "formatNote", "join", "string", "m", "match", "Error", "step", "acc", "octave", "alter", "parseInt", "parsedNote", "midi", "midiNote", "Math", "floor", "semi", "val", "Object", "entries"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/ConvertFunctions.js"], "sourcesContent": ["\r\n// ----- CONVERSION FUNCTIONS ----- //\r\n\r\n\r\n// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'\r\n// MIDI FORMAT: 0-127\r\n// PARSE FORMAT: { step, alter, octave }\r\n\r\n\r\nconst SEMI_MAP = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };\r\n\r\nexport function gigaConvert(key, time, notes) {\r\n  const defaultSetup = 'tabstave notaion=true tablature=false'\r\n  console.log(\"Key: \", key);\r\n  console.log(\"Time: \", time);\r\n  console.log(\"Notes: \", notes);\r\n\r\n  const parsedNotes = notes.map(parseNote);\r\n  const midiNotes = parsedNotes.map(toMidi + 9);\r\n  const newNotes = midiNotes.map(fromMidi);\r\n  const formattedNotes = newNotes.map(formatNote);\r\n\r\n  return `${defaultSetup} key=${key} time=${time} \\nnotes ${formattedNotes.join(' ')}`;\r\n}\r\n\r\nexport function parseNote(string) {\r\n  const m = string.match(/^([A-G])([#nb]?)[/](\\d)$/);\r\n  console.log(\"Match: \", m);\r\n  if (!m) {\r\n    throw new Error(`Invalid note format: ${string}`);\r\n  }\r\n  const [ step, acc, octave ] = m;\r\n  return { \r\n    step,\r\n    alter: acc === '#' ? 1 : (acc === 'b' ? -1 : 0),\r\n    octave: parseInt(octave, 10),\r\n  };\r\n}\r\n\r\nexport function toMidi(parsedNote) {\r\n  const {step, alter, octave} = parsedNote;\r\n  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;\r\n  return midi;\r\n}\r\n\r\nexport function fromMidi(midiNote) {\r\n  const octave = Math.floor(midiNote / 12) - 1;\r\n  const semi = midiNote % 12;\r\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\r\n    if (val === semi) {\r\n      return { step, alter: 0, octave };\r\n    }\r\n  }\r\n  for (const [step, val] of Object.entries(SEMI_MAP)) {\r\n    if (val + 1 === semi) {\r\n      return { step, alter: 1, octave };\r\n    }\r\n  }\r\n}\r\n\r\nexport function formatNote(parsedNote) {\r\n  const { step, alter, octave } = parsedNote;\r\n  let acc = '';\r\n  if (alter === 1) {\r\n    acc = '#';\r\n  }\r\n  return `${step}${acc}/${octave}`;\r\n}"], "mappings": "AACA;;AAGA;AACA;AACA;;AAGA,MAAMA,QAAQ,GAAG;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE,CAAC;EAAEC,CAAC,EAAE;AAAG,CAAC;AAE9D,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC5C,MAAMC,YAAY,GAAG,uCAAuC;EAC5DC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEL,GAAG,CAAC;EACzBI,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEJ,IAAI,CAAC;EAC3BG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,KAAK,CAAC;EAE7B,MAAMI,WAAW,GAAGJ,KAAK,CAACK,GAAG,CAACC,SAAS,CAAC;EACxC,MAAMC,SAAS,GAAGH,WAAW,CAACC,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;EAC7C,MAAMC,QAAQ,GAAGF,SAAS,CAACF,GAAG,CAACK,QAAQ,CAAC;EACxC,MAAMC,cAAc,GAAGF,QAAQ,CAACJ,GAAG,CAACO,UAAU,CAAC;EAE/C,OAAO,GAAGX,YAAY,QAAQH,GAAG,SAASC,IAAI,YAAYY,cAAc,CAACE,IAAI,CAAC,GAAG,CAAC,EAAE;AACtF;AAEA,OAAO,SAASP,SAASA,CAACQ,MAAM,EAAE;EAChC,MAAMC,CAAC,GAAGD,MAAM,CAACE,KAAK,CAAC,0BAA0B,CAAC;EAClDd,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEY,CAAC,CAAC;EACzB,IAAI,CAACA,CAAC,EAAE;IACN,MAAM,IAAIE,KAAK,CAAC,wBAAwBH,MAAM,EAAE,CAAC;EACnD;EACA,MAAM,CAAEI,IAAI,EAAEC,GAAG,EAAEC,MAAM,CAAE,GAAGL,CAAC;EAC/B,OAAO;IACLG,IAAI;IACJG,KAAK,EAAEF,GAAG,KAAK,GAAG,GAAG,CAAC,GAAIA,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAE;IAC/CC,MAAM,EAAEE,QAAQ,CAACF,MAAM,EAAE,EAAE;EAC7B,CAAC;AACH;AAEA,OAAO,SAASZ,MAAMA,CAACe,UAAU,EAAE;EACjC,MAAM;IAACL,IAAI;IAAEG,KAAK;IAAED;EAAM,CAAC,GAAGG,UAAU;EACxC,MAAMC,IAAI,GAAG,CAACJ,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG/B,QAAQ,CAAC6B,IAAI,CAAC,GAAGG,KAAK;EACvD,OAAOG,IAAI;AACb;AAEA,OAAO,SAASd,QAAQA,CAACe,QAAQ,EAAE;EACjC,MAAML,MAAM,GAAGM,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC;EAC5C,MAAMG,IAAI,GAAGH,QAAQ,GAAG,EAAE;EAC1B,KAAK,MAAM,CAACP,IAAI,EAAEW,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC1C,QAAQ,CAAC,EAAE;IAClD,IAAIwC,GAAG,KAAKD,IAAI,EAAE;MAChB,OAAO;QAAEV,IAAI;QAAEG,KAAK,EAAE,CAAC;QAAED;MAAO,CAAC;IACnC;EACF;EACA,KAAK,MAAM,CAACF,IAAI,EAAEW,GAAG,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC1C,QAAQ,CAAC,EAAE;IAClD,IAAIwC,GAAG,GAAG,CAAC,KAAKD,IAAI,EAAE;MACpB,OAAO;QAAEV,IAAI;QAAEG,KAAK,EAAE,CAAC;QAAED;MAAO,CAAC;IACnC;EACF;AACF;AAEA,OAAO,SAASR,UAAUA,CAACW,UAAU,EAAE;EACrC,MAAM;IAAEL,IAAI;IAAEG,KAAK;IAAED;EAAO,CAAC,GAAGG,UAAU;EAC1C,IAAIJ,GAAG,GAAG,EAAE;EACZ,IAAIE,KAAK,KAAK,CAAC,EAAE;IACfF,GAAG,GAAG,GAAG;EACX;EACA,OAAO,GAAGD,IAAI,GAAGC,GAAG,IAAIC,MAAM,EAAE;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}