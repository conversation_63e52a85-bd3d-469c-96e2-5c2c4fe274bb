{"ast": null, "code": "import { Note } from './note.js';\nimport { Barline, BarlineType } from './stavebarline.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (BarNote.DEBUG) log('VexFlow.BarNote', args);\n}\nexport class BarNote extends Note {\n  static get CATEGORY() {\n    return \"BarNote\";\n  }\n  constructor(type = BarlineType.SINGLE) {\n    super({\n      duration: 'b'\n    });\n    this.metrics = {\n      widths: {}\n    };\n    const TYPE = BarlineType;\n    this.metrics.widths = {\n      [TYPE.SINGLE]: 8,\n      [TYPE.DOUBLE]: 12,\n      [TYPE.END]: 15,\n      [TYPE.REPEAT_BEGIN]: 14,\n      [TYPE.REPEAT_END]: 14,\n      [TYPE.REPEAT_BOTH]: 18,\n      [TYPE.NONE]: 0\n    };\n    this.ignoreTicks = true;\n    this.setType(type);\n  }\n  getType() {\n    return this.type;\n  }\n  setType(type) {\n    this.type = typeof type === 'string' ? Barline.typeString[type] : type;\n    this.setWidth(this.metrics.widths[this.type]);\n    return this;\n  }\n  addToModifierContext(mc) {\n    return this;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    L('Rendering bar line at: ', this.getAbsoluteX());\n    const barline = new Barline(this.type);\n    barline.setX(this.getAbsoluteX());\n    barline.setStave(this.checkStave());\n    barline.setContext(ctx);\n    barline.drawWithStyle();\n    this.setRendered();\n  }\n}\nBarNote.DEBUG = false;", "map": {"version": 3, "names": ["Note", "Barline", "BarlineType", "log", "L", "args", "Bar<PERSON><PERSON>", "DEBUG", "CATEGORY", "constructor", "type", "SINGLE", "duration", "metrics", "widths", "TYPE", "DOUBLE", "END", "REPEAT_BEGIN", "REPEAT_END", "REPEAT_BOTH", "NONE", "ignoreTicks", "setType", "getType", "typeString", "<PERSON><PERSON><PERSON><PERSON>", "addToModifierContext", "mc", "preFormat", "preFormatted", "draw", "ctx", "checkContext", "getAbsoluteX", "barline", "setX", "setStave", "checkStave", "setContext", "drawWithStyle", "setRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/barnote.js"], "sourcesContent": ["import { Note } from './note.js';\nimport { Barline, BarlineType } from './stavebarline.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (BarNote.DEBUG)\n        log('VexFlow.BarNote', args);\n}\nexport class BarNote extends Note {\n    static get CATEGORY() {\n        return \"BarNote\";\n    }\n    constructor(type = BarlineType.SINGLE) {\n        super({ duration: 'b' });\n        this.metrics = {\n            widths: {},\n        };\n        const TYPE = BarlineType;\n        this.metrics.widths = {\n            [TYPE.SINGLE]: 8,\n            [TYPE.DOUBLE]: 12,\n            [TYPE.END]: 15,\n            [TYPE.REPEAT_BEGIN]: 14,\n            [TYPE.REPEAT_END]: 14,\n            [TYPE.REPEAT_BOTH]: 18,\n            [TYPE.NONE]: 0,\n        };\n        this.ignoreTicks = true;\n        this.setType(type);\n    }\n    getType() {\n        return this.type;\n    }\n    setType(type) {\n        this.type = typeof type === 'string' ? Barline.typeString[type] : type;\n        this.setWidth(this.metrics.widths[this.type]);\n        return this;\n    }\n    addToModifierContext(mc) {\n        return this;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        L('Rendering bar line at: ', this.getAbsoluteX());\n        const barline = new Barline(this.type);\n        barline.setX(this.getAbsoluteX());\n        barline.setStave(this.checkStave());\n        barline.setContext(ctx);\n        barline.drawWithStyle();\n        this.setRendered();\n    }\n}\nBarNote.DEBUG = false;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,EAAEC,WAAW,QAAQ,mBAAmB;AACxD,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,OAAO,CAACC,KAAK,EACbJ,GAAG,CAAC,iBAAiB,EAAEE,IAAI,CAAC;AACpC;AACA,OAAO,MAAMC,OAAO,SAASN,IAAI,CAAC;EAC9B,WAAWQ,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACAC,WAAWA,CAACC,IAAI,GAAGR,WAAW,CAACS,MAAM,EAAE;IACnC,KAAK,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,OAAO,GAAG;MACXC,MAAM,EAAE,CAAC;IACb,CAAC;IACD,MAAMC,IAAI,GAAGb,WAAW;IACxB,IAAI,CAACW,OAAO,CAACC,MAAM,GAAG;MAClB,CAACC,IAAI,CAACJ,MAAM,GAAG,CAAC;MAChB,CAACI,IAAI,CAACC,MAAM,GAAG,EAAE;MACjB,CAACD,IAAI,CAACE,GAAG,GAAG,EAAE;MACd,CAACF,IAAI,CAACG,YAAY,GAAG,EAAE;MACvB,CAACH,IAAI,CAACI,UAAU,GAAG,EAAE;MACrB,CAACJ,IAAI,CAACK,WAAW,GAAG,EAAE;MACtB,CAACL,IAAI,CAACM,IAAI,GAAG;IACjB,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,OAAO,CAACb,IAAI,CAAC;EACtB;EACAc,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACd,IAAI;EACpB;EACAa,OAAOA,CAACb,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAG,OAAOA,IAAI,KAAK,QAAQ,GAAGT,OAAO,CAACwB,UAAU,CAACf,IAAI,CAAC,GAAGA,IAAI;IACtE,IAAI,CAACgB,QAAQ,CAAC,IAAI,CAACb,OAAO,CAACC,MAAM,CAAC,IAAI,CAACJ,IAAI,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACAiB,oBAAoBA,CAACC,EAAE,EAAE;IACrB,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B7B,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC8B,YAAY,CAAC,CAAC,CAAC;IACjD,MAAMC,OAAO,GAAG,IAAIlC,OAAO,CAAC,IAAI,CAACS,IAAI,CAAC;IACtCyB,OAAO,CAACC,IAAI,CAAC,IAAI,CAACF,YAAY,CAAC,CAAC,CAAC;IACjCC,OAAO,CAACE,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;IACnCH,OAAO,CAACI,UAAU,CAACP,GAAG,CAAC;IACvBG,OAAO,CAACK,aAAa,CAAC,CAAC;IACvB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;AACJ;AACAnC,OAAO,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}