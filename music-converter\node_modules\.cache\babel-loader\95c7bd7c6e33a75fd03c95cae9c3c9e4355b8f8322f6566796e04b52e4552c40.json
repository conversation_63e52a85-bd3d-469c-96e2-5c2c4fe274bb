{"ast": null, "code": "import { globalObject } from './util.js';\nexport function isHTMLCanvas(element) {\n  if (!element) return false;\n  const global = globalObject();\n  return typeof global.HTMLCanvasElement === 'function' && element instanceof global.HTMLCanvasElement || typeof element.getContext === 'function' && typeof element.toDataURL === 'function';\n}\nexport function isHTMLDiv(element) {\n  if (!element) return false;\n  const global = globalObject();\n  return typeof global.HTMLDivElement === 'function' && element instanceof global.HTMLDivElement || typeof element.appendChild === 'function' && typeof element.style === 'object';\n}", "map": {"version": 3, "names": ["globalObject", "isHTMLCanvas", "element", "global", "HTMLCanvasElement", "getContext", "toDataURL", "isHTMLDiv", "HTMLDivElement", "append<PERSON><PERSON><PERSON>", "style"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/web.js"], "sourcesContent": ["import { globalObject } from './util.js';\nexport function isHTMLCanvas(element) {\n    if (!element)\n        return false;\n    const global = globalObject();\n    return ((typeof global.HTMLCanvasElement === 'function' && element instanceof global.HTMLCanvasElement) ||\n        (typeof element.getContext === 'function' && typeof element.toDataURL === 'function'));\n}\nexport function isHTMLDiv(element) {\n    if (!element)\n        return false;\n    const global = globalObject();\n    return ((typeof global.HTMLDivElement === 'function' && element instanceof global.HTMLDivElement) ||\n        (typeof element.appendChild === 'function' && typeof element.style === 'object'));\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,WAAW;AACxC,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EAClC,IAAI,CAACA,OAAO,EACR,OAAO,KAAK;EAChB,MAAMC,MAAM,GAAGH,YAAY,CAAC,CAAC;EAC7B,OAAS,OAAOG,MAAM,CAACC,iBAAiB,KAAK,UAAU,IAAIF,OAAO,YAAYC,MAAM,CAACC,iBAAiB,IACjG,OAAOF,OAAO,CAACG,UAAU,KAAK,UAAU,IAAI,OAAOH,OAAO,CAACI,SAAS,KAAK,UAAW;AAC7F;AACA,OAAO,SAASC,SAASA,CAACL,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EACR,OAAO,KAAK;EAChB,MAAMC,MAAM,GAAGH,YAAY,CAAC,CAAC;EAC7B,OAAS,OAAOG,MAAM,CAACK,cAAc,KAAK,UAAU,IAAIN,OAAO,YAAYC,MAAM,CAACK,cAAc,IAC3F,OAAON,OAAO,CAACO,WAAW,KAAK,UAAU,IAAI,OAAOP,OAAO,CAACQ,KAAK,KAAK,QAAS;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}