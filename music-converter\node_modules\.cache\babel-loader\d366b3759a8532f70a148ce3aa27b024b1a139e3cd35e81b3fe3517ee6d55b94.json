{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (Clef.DEBUG) log('VexFlow.Clef', args);\n}\nexport class Clef extends StaveModifier {\n  static get CATEGORY() {\n    return \"Clef\";\n  }\n  static get types() {\n    const {\n      gClef,\n      fClef,\n      cClef,\n      unpitchedPercussionClef1,\n      sixStringTabClef\n    } = Glyphs;\n    return {\n      treble: {\n        code: gClef,\n        line: 3\n      },\n      bass: {\n        code: fClef,\n        line: 1\n      },\n      alto: {\n        code: cClef,\n        line: 2\n      },\n      tenor: {\n        code: cClef,\n        line: 1\n      },\n      percussion: {\n        code: unpitchedPercussionClef1,\n        line: 2\n      },\n      soprano: {\n        code: cClef,\n        line: 4\n      },\n      'mezzo-soprano': {\n        code: cClef,\n        line: 3\n      },\n      'baritone-c': {\n        code: cClef,\n        line: 0\n      },\n      'baritone-f': {\n        code: fClef,\n        line: 2\n      },\n      subbass: {\n        code: fClef,\n        line: 0\n      },\n      french: {\n        code: gClef,\n        line: 4\n      },\n      tab: {\n        code: sixStringTabClef,\n        line: 2.5\n      }\n    };\n  }\n  constructor(type, size, annotation) {\n    super();\n    this.code = Clef.types['treble'].code;\n    this.line = Clef.types['treble'].line;\n    this.size = 'default';\n    this.type = 'treble';\n    this.setPosition(StaveModifierPosition.BEGIN);\n    this.setType(type, size, annotation);\n    L('Creating clef:', type);\n  }\n  setType(type, size = 'default', annotation) {\n    this.type = type;\n    this.code = Clef.types[type].code;\n    this.line = Clef.types[type].line;\n    this.size = size !== null && size !== void 0 ? size : 'default';\n    if (annotation === '8va') {\n      if (this.code === Glyphs.gClef) {\n        this.code = Glyphs.gClef8va;\n      }\n      if (this.code === Glyphs.fClef) {\n        this.code = Glyphs.fClef8va;\n      }\n    }\n    if (annotation === '8vb') {\n      if (this.code === Glyphs.gClef) {\n        this.code = Glyphs.gClef8vb;\n      }\n      if (this.code === Glyphs.fClef) {\n        this.code = Glyphs.fClef8vb;\n      }\n    }\n    this.text = this.code;\n    this.fontInfo.size = Math.floor(Clef.getPoint(this.size));\n    return this;\n  }\n  static getPoint(size) {\n    return size === 'default' ? Metrics.get('fontSize') : Metrics.get('fontSize') * 2 / 3;\n  }\n  setStave(stave) {\n    this.stave = stave;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    ctx.openGroup('clef', this.getAttribute('id'));\n    this.y = stave.getYForLine(this.line);\n    this.renderText(ctx, 0, 0);\n    ctx.closeGroup();\n  }\n}\nClef.DEBUG = false;", "map": {"version": 3, "names": ["Glyphs", "Metrics", "StaveModifier", "StaveModifierPosition", "log", "L", "args", "<PERSON><PERSON><PERSON>", "DEBUG", "CATEGORY", "types", "g<PERSON>lef", "fClef", "cClef", "unpitchedPercussionClef1", "sixStringTabClef", "treble", "code", "line", "bass", "alto", "tenor", "percussion", "soprano", "subbass", "french", "tab", "constructor", "type", "size", "annotation", "setPosition", "BEGIN", "setType", "gClef8va", "fClef8va", "gClef8vb", "fClef8vb", "text", "fontInfo", "Math", "floor", "getPoint", "get", "setStave", "stave", "draw", "checkStave", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "y", "getYForLine", "renderText", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/clef.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (Clef.DEBUG)\n        log('VexFlow.Clef', args);\n}\nexport class Clef extends StaveModifier {\n    static get CATEGORY() {\n        return \"Clef\";\n    }\n    static get types() {\n        const { gClef, fClef, cClef, unpitchedPercussionClef1, sixStringTabClef } = Glyphs;\n        return {\n            treble: {\n                code: gClef,\n                line: 3,\n            },\n            bass: {\n                code: fClef,\n                line: 1,\n            },\n            alto: {\n                code: cClef,\n                line: 2,\n            },\n            tenor: {\n                code: cClef,\n                line: 1,\n            },\n            percussion: {\n                code: unpitchedPercussionClef1,\n                line: 2,\n            },\n            soprano: {\n                code: cClef,\n                line: 4,\n            },\n            'mezzo-soprano': {\n                code: cClef,\n                line: 3,\n            },\n            'baritone-c': {\n                code: cClef,\n                line: 0,\n            },\n            'baritone-f': {\n                code: fClef,\n                line: 2,\n            },\n            subbass: {\n                code: fClef,\n                line: 0,\n            },\n            french: {\n                code: gClef,\n                line: 4,\n            },\n            tab: {\n                code: sixStringTabClef,\n                line: 2.5,\n            },\n        };\n    }\n    constructor(type, size, annotation) {\n        super();\n        this.code = Clef.types['treble'].code;\n        this.line = Clef.types['treble'].line;\n        this.size = 'default';\n        this.type = 'treble';\n        this.setPosition(StaveModifierPosition.BEGIN);\n        this.setType(type, size, annotation);\n        L('Creating clef:', type);\n    }\n    setType(type, size = 'default', annotation) {\n        this.type = type;\n        this.code = Clef.types[type].code;\n        this.line = Clef.types[type].line;\n        this.size = size !== null && size !== void 0 ? size : 'default';\n        if (annotation === '8va') {\n            if (this.code === Glyphs.gClef) {\n                this.code = Glyphs.gClef8va;\n            }\n            if (this.code === Glyphs.fClef) {\n                this.code = Glyphs.fClef8va;\n            }\n        }\n        if (annotation === '8vb') {\n            if (this.code === Glyphs.gClef) {\n                this.code = Glyphs.gClef8vb;\n            }\n            if (this.code === Glyphs.fClef) {\n                this.code = Glyphs.fClef8vb;\n            }\n        }\n        this.text = this.code;\n        this.fontInfo.size = Math.floor(Clef.getPoint(this.size));\n        return this;\n    }\n    static getPoint(size) {\n        return size === 'default' ? Metrics.get('fontSize') : (Metrics.get('fontSize') * 2) / 3;\n    }\n    setStave(stave) {\n        this.stave = stave;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        ctx.openGroup('clef', this.getAttribute('id'));\n        this.y = stave.getYForLine(this.line);\n        this.renderText(ctx, 0, 0);\n        ctx.closeGroup();\n    }\n}\nClef.DEBUG = false;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,IAAI,CAACC,KAAK,EACVJ,GAAG,CAAC,cAAc,EAAEE,IAAI,CAAC;AACjC;AACA,OAAO,MAAMC,IAAI,SAASL,aAAa,CAAC;EACpC,WAAWO,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACA,WAAWC,KAAKA,CAAA,EAAG;IACf,MAAM;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MAAEC,wBAAwB;MAAEC;IAAiB,CAAC,GAAGf,MAAM;IAClF,OAAO;MACHgB,MAAM,EAAE;QACJC,IAAI,EAAEN,KAAK;QACXO,IAAI,EAAE;MACV,CAAC;MACDC,IAAI,EAAE;QACFF,IAAI,EAAEL,KAAK;QACXM,IAAI,EAAE;MACV,CAAC;MACDE,IAAI,EAAE;QACFH,IAAI,EAAEJ,KAAK;QACXK,IAAI,EAAE;MACV,CAAC;MACDG,KAAK,EAAE;QACHJ,IAAI,EAAEJ,KAAK;QACXK,IAAI,EAAE;MACV,CAAC;MACDI,UAAU,EAAE;QACRL,IAAI,EAAEH,wBAAwB;QAC9BI,IAAI,EAAE;MACV,CAAC;MACDK,OAAO,EAAE;QACLN,IAAI,EAAEJ,KAAK;QACXK,IAAI,EAAE;MACV,CAAC;MACD,eAAe,EAAE;QACbD,IAAI,EAAEJ,KAAK;QACXK,IAAI,EAAE;MACV,CAAC;MACD,YAAY,EAAE;QACVD,IAAI,EAAEJ,KAAK;QACXK,IAAI,EAAE;MACV,CAAC;MACD,YAAY,EAAE;QACVD,IAAI,EAAEL,KAAK;QACXM,IAAI,EAAE;MACV,CAAC;MACDM,OAAO,EAAE;QACLP,IAAI,EAAEL,KAAK;QACXM,IAAI,EAAE;MACV,CAAC;MACDO,MAAM,EAAE;QACJR,IAAI,EAAEN,KAAK;QACXO,IAAI,EAAE;MACV,CAAC;MACDQ,GAAG,EAAE;QACDT,IAAI,EAAEF,gBAAgB;QACtBG,IAAI,EAAE;MACV;IACJ,CAAC;EACL;EACAS,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAE;IAChC,KAAK,CAAC,CAAC;IACP,IAAI,CAACb,IAAI,GAAGV,IAAI,CAACG,KAAK,CAAC,QAAQ,CAAC,CAACO,IAAI;IACrC,IAAI,CAACC,IAAI,GAAGX,IAAI,CAACG,KAAK,CAAC,QAAQ,CAAC,CAACQ,IAAI;IACrC,IAAI,CAACW,IAAI,GAAG,SAAS;IACrB,IAAI,CAACD,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACG,WAAW,CAAC5B,qBAAqB,CAAC6B,KAAK,CAAC;IAC7C,IAAI,CAACC,OAAO,CAACL,IAAI,EAAEC,IAAI,EAAEC,UAAU,CAAC;IACpCzB,CAAC,CAAC,gBAAgB,EAAEuB,IAAI,CAAC;EAC7B;EACAK,OAAOA,CAACL,IAAI,EAAEC,IAAI,GAAG,SAAS,EAAEC,UAAU,EAAE;IACxC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACX,IAAI,GAAGV,IAAI,CAACG,KAAK,CAACkB,IAAI,CAAC,CAACX,IAAI;IACjC,IAAI,CAACC,IAAI,GAAGX,IAAI,CAACG,KAAK,CAACkB,IAAI,CAAC,CAACV,IAAI;IACjC,IAAI,CAACW,IAAI,GAAGA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,SAAS;IAC/D,IAAIC,UAAU,KAAK,KAAK,EAAE;MACtB,IAAI,IAAI,CAACb,IAAI,KAAKjB,MAAM,CAACW,KAAK,EAAE;QAC5B,IAAI,CAACM,IAAI,GAAGjB,MAAM,CAACkC,QAAQ;MAC/B;MACA,IAAI,IAAI,CAACjB,IAAI,KAAKjB,MAAM,CAACY,KAAK,EAAE;QAC5B,IAAI,CAACK,IAAI,GAAGjB,MAAM,CAACmC,QAAQ;MAC/B;IACJ;IACA,IAAIL,UAAU,KAAK,KAAK,EAAE;MACtB,IAAI,IAAI,CAACb,IAAI,KAAKjB,MAAM,CAACW,KAAK,EAAE;QAC5B,IAAI,CAACM,IAAI,GAAGjB,MAAM,CAACoC,QAAQ;MAC/B;MACA,IAAI,IAAI,CAACnB,IAAI,KAAKjB,MAAM,CAACY,KAAK,EAAE;QAC5B,IAAI,CAACK,IAAI,GAAGjB,MAAM,CAACqC,QAAQ;MAC/B;IACJ;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,IAAI;IACrB,IAAI,CAACsB,QAAQ,CAACV,IAAI,GAAGW,IAAI,CAACC,KAAK,CAAClC,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACb,IAAI,CAAC,CAAC;IACzD,OAAO,IAAI;EACf;EACA,OAAOa,QAAQA,CAACb,IAAI,EAAE;IAClB,OAAOA,IAAI,KAAK,SAAS,GAAG5B,OAAO,CAAC0C,GAAG,CAAC,UAAU,CAAC,GAAI1C,OAAO,CAAC0C,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,GAAI,CAAC;EAC3F;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMD,KAAK,GAAG,IAAI,CAACE,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGH,KAAK,CAACI,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,MAAM,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,CAACC,CAAC,GAAGR,KAAK,CAACS,WAAW,CAAC,IAAI,CAACpC,IAAI,CAAC;IACrC,IAAI,CAACqC,UAAU,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1BA,GAAG,CAACQ,UAAU,CAAC,CAAC;EACpB;AACJ;AACAjD,IAAI,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}