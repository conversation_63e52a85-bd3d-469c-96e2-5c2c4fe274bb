{"ast": null, "code": "import { Element } from './element.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (Flag.DEBUG) log('VexFlow.Flag', args);\n}\nexport class Flag extends Element {\n  static get CATEGORY() {\n    return \"Flag\";\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    ctx.openGroup('flag', this.getAttribute('id'));\n    L(\"Drawing flag '\", this.text, \"' at\", this.x, this.y);\n    this.renderText(ctx, 0, 0);\n    ctx.closeGroup();\n  }\n}\nFlag.DEBUG = false;", "map": {"version": 3, "names": ["Element", "log", "L", "args", "Flag", "DEBUG", "CATEGORY", "draw", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "text", "x", "y", "renderText", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/flag.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (Flag.DEBUG)\n        log('VexFlow.Flag', args);\n}\nexport class Flag extends Element {\n    static get CATEGORY() {\n        return \"Flag\";\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        ctx.openGroup('flag', this.getAttribute('id'));\n        L(\"Drawing flag '\", this.text, \"' at\", this.x, this.y);\n        this.renderText(ctx, 0, 0);\n        ctx.closeGroup();\n    }\n}\nFlag.DEBUG = false;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,IAAI,CAACC,KAAK,EACVJ,GAAG,CAAC,cAAc,EAAEE,IAAI,CAAC;AACjC;AACA,OAAO,MAAMC,IAAI,SAASJ,OAAO,CAAC;EAC9B,WAAWM,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,MAAM,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9CV,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACW,IAAI,EAAE,MAAM,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IACtD,IAAI,CAACC,UAAU,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1BA,GAAG,CAACS,UAAU,CAAC,CAAC;EACpB;AACJ;AACAb,IAAI,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}