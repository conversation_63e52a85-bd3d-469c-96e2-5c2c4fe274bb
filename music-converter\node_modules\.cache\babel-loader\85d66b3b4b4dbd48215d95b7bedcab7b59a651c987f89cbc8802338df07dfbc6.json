{"ast": null, "code": "import { Fraction } from './fraction.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Music } from './music.js';\nimport { Tables } from './tables.js';\nimport { isAccidental, isGraceNote, isGraceNoteGroup, isStaveNote } from './typeguard.js';\nimport { defined, log } from './util.js';\nfunction L(...args) {\n  if (Accidental.DEBUG) log('VexFlow.Accidental', args);\n}\nexport class Accidental extends Modifier {\n  static get CATEGORY() {\n    return \"Accidental\";\n  }\n  static format(accidentals, state) {\n    if (!accidentals || accidentals.length === 0) return;\n    const noteheadAccidentalPadding = Metrics.get('Accidental.noteheadAccidentalPadding');\n    const leftShift = state.leftShift + noteheadAccidentalPadding;\n    const accidentalSpacing = Metrics.get('Accidental.accidentalSpacing');\n    const additionalPadding = Metrics.get('Accidental.leftPadding');\n    const accidentalLinePositionsAndSpaceNeeds = [];\n    let prevNote = undefined;\n    let extraXSpaceNeededForLeftDisplacedNotehead = 0;\n    for (let i = 0; i < accidentals.length; ++i) {\n      const accidental = accidentals[i];\n      const note = accidental.getNote();\n      const stave = note.getStave();\n      const index = accidental.checkIndex();\n      const props = note.getKeyProps()[index];\n      if (note !== prevNote) {\n        for (let n = 0; n < note.keys.length; ++n) {\n          extraXSpaceNeededForLeftDisplacedNotehead = Math.max(note.getLeftDisplacedHeadPx() - note.getXShift(), extraXSpaceNeededForLeftDisplacedNotehead);\n        }\n        prevNote = note;\n      }\n      if (stave) {\n        const lineSpace = stave.getSpacingBetweenLines();\n        const y = stave.getYForLine(props.line);\n        const accLine = Math.round(y / lineSpace * 2) / 2;\n        accidentalLinePositionsAndSpaceNeeds.push({\n          y,\n          line: accLine,\n          extraXSpaceNeeded: extraXSpaceNeededForLeftDisplacedNotehead,\n          accidental: accidental,\n          spacingBetweenStaveLines: lineSpace\n        });\n      } else {\n        accidentalLinePositionsAndSpaceNeeds.push({\n          line: props.line,\n          extraXSpaceNeeded: extraXSpaceNeededForLeftDisplacedNotehead,\n          accidental: accidental\n        });\n      }\n    }\n    accidentalLinePositionsAndSpaceNeeds.sort((a, b) => b.line - a.line);\n    const staveLineAccidentalLayoutMetrics = [];\n    let maxExtraXSpaceNeeded = 0;\n    for (let i = 0; i < accidentalLinePositionsAndSpaceNeeds.length; i++) {\n      const accidentalLinePositionAndSpaceNeeds = accidentalLinePositionsAndSpaceNeeds[i];\n      const accidentalType = accidentalLinePositionAndSpaceNeeds.accidental.type;\n      const priorLineMetric = staveLineAccidentalLayoutMetrics[staveLineAccidentalLayoutMetrics.length - 1];\n      let currentLineMetric;\n      if (!priorLineMetric || (priorLineMetric === null || priorLineMetric === void 0 ? void 0 : priorLineMetric.line) !== accidentalLinePositionAndSpaceNeeds.line) {\n        currentLineMetric = {\n          line: accidentalLinePositionAndSpaceNeeds.line,\n          flatLine: true,\n          dblSharpLine: true,\n          numAcc: 0,\n          width: 0,\n          column: 0\n        };\n        staveLineAccidentalLayoutMetrics.push(currentLineMetric);\n      } else {\n        currentLineMetric = priorLineMetric;\n      }\n      if (accidentalType !== 'b' && accidentalType !== 'bb') {\n        currentLineMetric.flatLine = false;\n      }\n      if (accidentalType !== '##') {\n        currentLineMetric.dblSharpLine = false;\n      }\n      currentLineMetric.numAcc++;\n      currentLineMetric.width += accidentalLinePositionAndSpaceNeeds.accidental.getWidth() + accidentalSpacing;\n      maxExtraXSpaceNeeded = Math.max(accidentalLinePositionAndSpaceNeeds.extraXSpaceNeeded, maxExtraXSpaceNeeded);\n    }\n    let totalColumns = 0;\n    for (let i = 0; i < staveLineAccidentalLayoutMetrics.length; i++) {\n      let noFurtherConflicts = false;\n      const groupStart = i;\n      let groupEnd = i;\n      while (groupEnd + 1 < staveLineAccidentalLayoutMetrics.length && !noFurtherConflicts) {\n        if (this.checkCollision(staveLineAccidentalLayoutMetrics[groupEnd], staveLineAccidentalLayoutMetrics[groupEnd + 1])) {\n          groupEnd++;\n        } else {\n          noFurtherConflicts = true;\n        }\n      }\n      const getGroupLine = index => staveLineAccidentalLayoutMetrics[groupStart + index];\n      const getGroupLines = indexes => indexes.map(getGroupLine);\n      const lineDifference = (indexA, indexB) => {\n        const [a, b] = getGroupLines([indexA, indexB]).map(item => item.line);\n        return a - b;\n      };\n      const notColliding = (...indexPairs) => indexPairs.map(getGroupLines).every(([line1, line2]) => !this.checkCollision(line1, line2));\n      const groupLength = groupEnd - groupStart + 1;\n      let endCase = this.checkCollision(staveLineAccidentalLayoutMetrics[groupStart], staveLineAccidentalLayoutMetrics[groupEnd]) ? 'a' : 'b';\n      switch (groupLength) {\n        case 3:\n          if (endCase === 'a' && lineDifference(1, 2) === 0.5 && lineDifference(0, 1) !== 0.5) {\n            endCase = 'secondOnBottom';\n          }\n          break;\n        case 4:\n          if (notColliding([0, 2], [1, 3])) {\n            endCase = 'spacedOutTetrachord';\n          }\n          break;\n        case 5:\n          if (endCase === 'b' && notColliding([1, 3])) {\n            endCase = 'spacedOutPentachord';\n            if (notColliding([0, 2], [2, 4])) {\n              endCase = 'verySpacedOutPentachord';\n            }\n          }\n          break;\n        case 6:\n          if (notColliding([0, 3], [1, 4], [2, 5])) {\n            endCase = 'spacedOutHexachord';\n          }\n          if (notColliding([0, 2], [2, 4], [1, 3], [3, 5])) {\n            endCase = 'verySpacedOutHexachord';\n          }\n          break;\n        default:\n          break;\n      }\n      let groupMember;\n      let column;\n      if (groupLength >= 7) {\n        let patternLength = 2;\n        let collisionDetected = true;\n        while (collisionDetected === true) {\n          collisionDetected = false;\n          for (let line = 0; line + patternLength < staveLineAccidentalLayoutMetrics.length; line++) {\n            if (this.checkCollision(staveLineAccidentalLayoutMetrics[line], staveLineAccidentalLayoutMetrics[line + patternLength])) {\n              collisionDetected = true;\n              patternLength++;\n              break;\n            }\n          }\n        }\n        for (groupMember = i; groupMember <= groupEnd; groupMember++) {\n          column = (groupMember - i) % patternLength + 1;\n          staveLineAccidentalLayoutMetrics[groupMember].column = column;\n          totalColumns = totalColumns > column ? totalColumns : column;\n        }\n      } else {\n        for (groupMember = i; groupMember <= groupEnd; groupMember++) {\n          column = Tables.accidentalColumnsTable[groupLength][endCase][groupMember - i];\n          staveLineAccidentalLayoutMetrics[groupMember].column = column;\n          totalColumns = totalColumns > column ? totalColumns : column;\n        }\n      }\n      i = groupEnd;\n    }\n    const columnWidths = [];\n    const columnXOffsets = [];\n    for (let i = 0; i <= totalColumns; i++) {\n      columnWidths[i] = 0;\n      columnXOffsets[i] = 0;\n    }\n    columnWidths[0] = leftShift + maxExtraXSpaceNeeded;\n    columnXOffsets[0] = leftShift;\n    staveLineAccidentalLayoutMetrics.forEach(line => {\n      if (line.width > columnWidths[line.column]) columnWidths[line.column] = line.width;\n    });\n    for (let i = 1; i < columnWidths.length; i++) {\n      columnXOffsets[i] = columnWidths[i] + columnXOffsets[i - 1];\n    }\n    const totalShift = columnXOffsets[columnXOffsets.length - 1];\n    let accCount = 0;\n    staveLineAccidentalLayoutMetrics.forEach(line => {\n      let lineWidth = 0;\n      const lastAccOnLine = accCount + line.numAcc;\n      for (accCount; accCount < lastAccOnLine; accCount++) {\n        const xShift = columnXOffsets[line.column - 1] + lineWidth + maxExtraXSpaceNeeded;\n        accidentalLinePositionsAndSpaceNeeds[accCount].accidental.setXShift(xShift);\n        lineWidth += accidentalLinePositionsAndSpaceNeeds[accCount].accidental.getWidth() + accidentalSpacing;\n        L('Line, accCount, shift: ', line.line, accCount, xShift);\n      }\n    });\n    state.leftShift = totalShift + additionalPadding;\n  }\n  static checkCollision(line1, line2) {\n    let clearance = line2.line - line1.line;\n    let clearanceRequired = 3;\n    if (clearance > 0) {\n      clearanceRequired = line2.flatLine || line2.dblSharpLine ? 2.5 : 3.0;\n      if (line1.dblSharpLine) clearance -= 0.5;\n    } else {\n      clearanceRequired = line1.flatLine || line1.dblSharpLine ? 2.5 : 3.0;\n      if (line2.dblSharpLine) clearance -= 0.5;\n    }\n    const collision = Math.abs(clearance) < clearanceRequired;\n    L('Line1, Line2, Collision: ', line1.line, line2.line, collision);\n    return collision;\n  }\n  static applyAccidentals(voices, keySignature) {\n    const tickPositions = [];\n    const tickNoteMap = {};\n    voices.forEach(voice => {\n      const tickPosition = new Fraction(0, 1);\n      const tickable = voice.getTickables();\n      tickable.forEach(t => {\n        if (t.shouldIgnoreTicks()) return;\n        const notesAtPosition = tickNoteMap[tickPosition.value()];\n        if (!notesAtPosition) {\n          tickPositions.push(tickPosition.value());\n          tickNoteMap[tickPosition.value()] = [t];\n        } else {\n          notesAtPosition.push(t);\n        }\n        tickPosition.add(t.getTicks());\n      });\n    });\n    const music = new Music();\n    if (!keySignature) keySignature = 'C';\n    const scaleMapKey = music.createScaleMap(keySignature);\n    const scaleMap = {};\n    tickPositions.forEach(tickPos => {\n      const tickables = tickNoteMap[tickPos];\n      const modifiedPitches = [];\n      const processNote = t => {\n        if (!isStaveNote(t) || t.isRest() || t.shouldIgnoreTicks()) {\n          return;\n        }\n        const staveNote = t;\n        staveNote.keys.forEach((keyString, keyIndex) => {\n          const key = music.getNoteParts(keyString.split('/')[0]);\n          const octave = keyString.split('/')[1];\n          const accidentalString = key.accidental || 'n';\n          const pitch = key.root + accidentalString;\n          if (!scaleMap[key.root + octave]) scaleMap[key.root + octave] = scaleMapKey[key.root];\n          const sameAccidental = scaleMap[key.root + octave] === pitch;\n          const previouslyModified = modifiedPitches.indexOf(keyString) > -1;\n          staveNote.getModifiers().forEach((modifier, index) => {\n            if (isAccidental(modifier) && modifier.type == accidentalString && modifier.getIndex() == keyIndex) {\n              staveNote.getModifiers().splice(index, 1);\n            }\n          });\n          if (!sameAccidental || sameAccidental && previouslyModified) {\n            scaleMap[key.root + octave] = pitch;\n            const accidental = new Accidental(accidentalString);\n            staveNote.addModifier(accidental, keyIndex);\n            modifiedPitches.push(keyString);\n          }\n        });\n        staveNote.getModifiers().forEach(modifier => {\n          if (isGraceNoteGroup(modifier)) {\n            modifier.getGraceNotes().forEach(processNote);\n          }\n        });\n      };\n      tickables.forEach(processNote);\n    });\n  }\n  constructor(type) {\n    super();\n    L('New accidental: ', type);\n    this.type = type;\n    this.position = Modifier.Position.LEFT;\n    this.cautionary = false;\n    this.reset();\n  }\n  reset() {\n    this.text = '';\n    if (!this.cautionary) {\n      this.text += Tables.accidentalCodes(this.type);\n      this.fontInfo.size = Metrics.get('Accidental.fontSize');\n    } else {\n      this.text += Tables.accidentalCodes('{');\n      this.text += Tables.accidentalCodes(this.type);\n      this.text += Tables.accidentalCodes('}');\n      this.fontInfo.size = Metrics.get('Accidental.cautionary.fontSize');\n    }\n    if (isGraceNote(this.note)) {\n      this.fontInfo.size = Metrics.get('Accidental.grace.fontSize');\n    }\n  }\n  setNote(note) {\n    defined(note, 'ArgumentError', `Bad note value: ${note}`);\n    this.note = note;\n    this.reset();\n    return this;\n  }\n  setAsCautionary() {\n    this.cautionary = true;\n    this.reset();\n    return this;\n  }\n  draw() {\n    const {\n      type,\n      position,\n      index\n    } = this;\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(position, index);\n    this.x = start.x - this.width;\n    this.y = start.y;\n    L('Rendering: ', type, start.x, start.y);\n    this.renderText(ctx, 0, 0);\n  }\n}\nAccidental.DEBUG = false;", "map": {"version": 3, "names": ["Fraction", "Metrics", "Modifier", "Music", "Tables", "isAccidental", "isGraceNote", "isGraceNoteGroup", "isStaveNote", "defined", "log", "L", "args", "Accidental", "DEBUG", "CATEGORY", "format", "accidentals", "state", "length", "noteheadAccidentalPadding", "get", "leftShift", "accidentalSpacing", "additionalPadding", "accidentalLinePositionsAndSpaceNeeds", "prevNote", "undefined", "extraXSpaceNeededForLeftDisplacedNotehead", "i", "accidental", "note", "getNote", "stave", "getStave", "index", "checkIndex", "props", "getKeyProps", "n", "keys", "Math", "max", "getLeftDisplacedHeadPx", "getXShift", "lineSpace", "getSpacingBetweenLines", "y", "getYForLine", "line", "accLine", "round", "push", "extraXSpaceNeeded", "spacingBetweenStaveLines", "sort", "a", "b", "staveLineAccidentalLayoutMetrics", "maxExtraXSpaceNeeded", "accidentalLinePositionAndSpaceNeeds", "accidentalType", "type", "priorLineMetric", "currentLineMetric", "flatLine", "dblSharpLine", "numAcc", "width", "column", "getWidth", "totalColumns", "noFurtherConflicts", "groupStart", "groupEnd", "checkCollision", "getGroupLine", "getGroupLines", "indexes", "map", "lineDifference", "indexA", "indexB", "item", "notColliding", "indexPairs", "every", "line1", "line2", "groupLength", "endCase", "groupMember", "<PERSON><PERSON><PERSON><PERSON>", "collisionDetected", "accidentalColumnsTable", "columnWidths", "columnXOffsets", "for<PERSON>ach", "totalShift", "accCount", "lineWidth", "lastAccOnLine", "xShift", "setXShift", "clearance", "clearanceRequired", "collision", "abs", "applyAccidentals", "voices", "keySignature", "tickPositions", "tickNoteMap", "voice", "tickPosition", "tickable", "getTickables", "t", "shouldIgnoreTicks", "notesAtPosition", "value", "add", "getTicks", "music", "scaleMapKey", "createScaleMap", "scaleMap", "tickPos", "tickables", "modifiedPitches", "processNote", "isRest", "staveNote", "keyString", "keyIndex", "key", "getNoteParts", "split", "octave", "accidentalString", "pitch", "root", "sameAccidental", "previouslyModified", "indexOf", "getModifiers", "modifier", "getIndex", "splice", "addModifier", "getGraceNotes", "constructor", "position", "Position", "LEFT", "cautionary", "reset", "text", "accidentalCodes", "fontInfo", "size", "setNote", "setAsCautionary", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "x", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/accidental.js"], "sourcesContent": ["import { Fraction } from './fraction.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Music } from './music.js';\nimport { Tables } from './tables.js';\nimport { isAccidental, isGraceNote, isGraceNoteGroup, isStaveNote } from './typeguard.js';\nimport { defined, log } from './util.js';\nfunction L(...args) {\n    if (Accidental.DEBUG)\n        log('VexFlow.Accidental', args);\n}\nexport class Accidental extends Modifier {\n    static get CATEGORY() {\n        return \"Accidental\";\n    }\n    static format(accidentals, state) {\n        if (!accidentals || accidentals.length === 0)\n            return;\n        const noteheadAccidentalPadding = Metrics.get('Accidental.noteheadAccidentalPadding');\n        const leftShift = state.leftShift + noteheadAccidentalPadding;\n        const accidentalSpacing = Metrics.get('Accidental.accidentalSpacing');\n        const additionalPadding = Metrics.get('Accidental.leftPadding');\n        const accidentalLinePositionsAndSpaceNeeds = [];\n        let prevNote = undefined;\n        let extraXSpaceNeededForLeftDisplacedNotehead = 0;\n        for (let i = 0; i < accidentals.length; ++i) {\n            const accidental = accidentals[i];\n            const note = accidental.getNote();\n            const stave = note.getStave();\n            const index = accidental.checkIndex();\n            const props = note.getKeyProps()[index];\n            if (note !== prevNote) {\n                for (let n = 0; n < note.keys.length; ++n) {\n                    extraXSpaceNeededForLeftDisplacedNotehead = Math.max(note.getLeftDisplacedHeadPx() - note.getXShift(), extraXSpaceNeededForLeftDisplacedNotehead);\n                }\n                prevNote = note;\n            }\n            if (stave) {\n                const lineSpace = stave.getSpacingBetweenLines();\n                const y = stave.getYForLine(props.line);\n                const accLine = Math.round((y / lineSpace) * 2) / 2;\n                accidentalLinePositionsAndSpaceNeeds.push({\n                    y,\n                    line: accLine,\n                    extraXSpaceNeeded: extraXSpaceNeededForLeftDisplacedNotehead,\n                    accidental: accidental,\n                    spacingBetweenStaveLines: lineSpace,\n                });\n            }\n            else {\n                accidentalLinePositionsAndSpaceNeeds.push({\n                    line: props.line,\n                    extraXSpaceNeeded: extraXSpaceNeededForLeftDisplacedNotehead,\n                    accidental: accidental,\n                });\n            }\n        }\n        accidentalLinePositionsAndSpaceNeeds.sort((a, b) => b.line - a.line);\n        const staveLineAccidentalLayoutMetrics = [];\n        let maxExtraXSpaceNeeded = 0;\n        for (let i = 0; i < accidentalLinePositionsAndSpaceNeeds.length; i++) {\n            const accidentalLinePositionAndSpaceNeeds = accidentalLinePositionsAndSpaceNeeds[i];\n            const accidentalType = accidentalLinePositionAndSpaceNeeds.accidental.type;\n            const priorLineMetric = staveLineAccidentalLayoutMetrics[staveLineAccidentalLayoutMetrics.length - 1];\n            let currentLineMetric;\n            if (!priorLineMetric || (priorLineMetric === null || priorLineMetric === void 0 ? void 0 : priorLineMetric.line) !== accidentalLinePositionAndSpaceNeeds.line) {\n                currentLineMetric = {\n                    line: accidentalLinePositionAndSpaceNeeds.line,\n                    flatLine: true,\n                    dblSharpLine: true,\n                    numAcc: 0,\n                    width: 0,\n                    column: 0,\n                };\n                staveLineAccidentalLayoutMetrics.push(currentLineMetric);\n            }\n            else {\n                currentLineMetric = priorLineMetric;\n            }\n            if (accidentalType !== 'b' && accidentalType !== 'bb') {\n                currentLineMetric.flatLine = false;\n            }\n            if (accidentalType !== '##') {\n                currentLineMetric.dblSharpLine = false;\n            }\n            currentLineMetric.numAcc++;\n            currentLineMetric.width += accidentalLinePositionAndSpaceNeeds.accidental.getWidth() + accidentalSpacing;\n            maxExtraXSpaceNeeded = Math.max(accidentalLinePositionAndSpaceNeeds.extraXSpaceNeeded, maxExtraXSpaceNeeded);\n        }\n        let totalColumns = 0;\n        for (let i = 0; i < staveLineAccidentalLayoutMetrics.length; i++) {\n            let noFurtherConflicts = false;\n            const groupStart = i;\n            let groupEnd = i;\n            while (groupEnd + 1 < staveLineAccidentalLayoutMetrics.length && !noFurtherConflicts) {\n                if (this.checkCollision(staveLineAccidentalLayoutMetrics[groupEnd], staveLineAccidentalLayoutMetrics[groupEnd + 1])) {\n                    groupEnd++;\n                }\n                else {\n                    noFurtherConflicts = true;\n                }\n            }\n            const getGroupLine = (index) => staveLineAccidentalLayoutMetrics[groupStart + index];\n            const getGroupLines = (indexes) => indexes.map(getGroupLine);\n            const lineDifference = (indexA, indexB) => {\n                const [a, b] = getGroupLines([indexA, indexB]).map((item) => item.line);\n                return a - b;\n            };\n            const notColliding = (...indexPairs) => indexPairs.map(getGroupLines).every(([line1, line2]) => !this.checkCollision(line1, line2));\n            const groupLength = groupEnd - groupStart + 1;\n            let endCase = this.checkCollision(staveLineAccidentalLayoutMetrics[groupStart], staveLineAccidentalLayoutMetrics[groupEnd])\n                ? 'a'\n                : 'b';\n            switch (groupLength) {\n                case 3:\n                    if (endCase === 'a' && lineDifference(1, 2) === 0.5 && lineDifference(0, 1) !== 0.5) {\n                        endCase = 'secondOnBottom';\n                    }\n                    break;\n                case 4:\n                    if (notColliding([0, 2], [1, 3])) {\n                        endCase = 'spacedOutTetrachord';\n                    }\n                    break;\n                case 5:\n                    if (endCase === 'b' && notColliding([1, 3])) {\n                        endCase = 'spacedOutPentachord';\n                        if (notColliding([0, 2], [2, 4])) {\n                            endCase = 'verySpacedOutPentachord';\n                        }\n                    }\n                    break;\n                case 6:\n                    if (notColliding([0, 3], [1, 4], [2, 5])) {\n                        endCase = 'spacedOutHexachord';\n                    }\n                    if (notColliding([0, 2], [2, 4], [1, 3], [3, 5])) {\n                        endCase = 'verySpacedOutHexachord';\n                    }\n                    break;\n                default:\n                    break;\n            }\n            let groupMember;\n            let column;\n            if (groupLength >= 7) {\n                let patternLength = 2;\n                let collisionDetected = true;\n                while (collisionDetected === true) {\n                    collisionDetected = false;\n                    for (let line = 0; line + patternLength < staveLineAccidentalLayoutMetrics.length; line++) {\n                        if (this.checkCollision(staveLineAccidentalLayoutMetrics[line], staveLineAccidentalLayoutMetrics[line + patternLength])) {\n                            collisionDetected = true;\n                            patternLength++;\n                            break;\n                        }\n                    }\n                }\n                for (groupMember = i; groupMember <= groupEnd; groupMember++) {\n                    column = ((groupMember - i) % patternLength) + 1;\n                    staveLineAccidentalLayoutMetrics[groupMember].column = column;\n                    totalColumns = totalColumns > column ? totalColumns : column;\n                }\n            }\n            else {\n                for (groupMember = i; groupMember <= groupEnd; groupMember++) {\n                    column = Tables.accidentalColumnsTable[groupLength][endCase][groupMember - i];\n                    staveLineAccidentalLayoutMetrics[groupMember].column = column;\n                    totalColumns = totalColumns > column ? totalColumns : column;\n                }\n            }\n            i = groupEnd;\n        }\n        const columnWidths = [];\n        const columnXOffsets = [];\n        for (let i = 0; i <= totalColumns; i++) {\n            columnWidths[i] = 0;\n            columnXOffsets[i] = 0;\n        }\n        columnWidths[0] = leftShift + maxExtraXSpaceNeeded;\n        columnXOffsets[0] = leftShift;\n        staveLineAccidentalLayoutMetrics.forEach((line) => {\n            if (line.width > columnWidths[line.column])\n                columnWidths[line.column] = line.width;\n        });\n        for (let i = 1; i < columnWidths.length; i++) {\n            columnXOffsets[i] = columnWidths[i] + columnXOffsets[i - 1];\n        }\n        const totalShift = columnXOffsets[columnXOffsets.length - 1];\n        let accCount = 0;\n        staveLineAccidentalLayoutMetrics.forEach((line) => {\n            let lineWidth = 0;\n            const lastAccOnLine = accCount + line.numAcc;\n            for (accCount; accCount < lastAccOnLine; accCount++) {\n                const xShift = columnXOffsets[line.column - 1] + lineWidth + maxExtraXSpaceNeeded;\n                accidentalLinePositionsAndSpaceNeeds[accCount].accidental.setXShift(xShift);\n                lineWidth += accidentalLinePositionsAndSpaceNeeds[accCount].accidental.getWidth() + accidentalSpacing;\n                L('Line, accCount, shift: ', line.line, accCount, xShift);\n            }\n        });\n        state.leftShift = totalShift + additionalPadding;\n    }\n    static checkCollision(line1, line2) {\n        let clearance = line2.line - line1.line;\n        let clearanceRequired = 3;\n        if (clearance > 0) {\n            clearanceRequired = line2.flatLine || line2.dblSharpLine ? 2.5 : 3.0;\n            if (line1.dblSharpLine)\n                clearance -= 0.5;\n        }\n        else {\n            clearanceRequired = line1.flatLine || line1.dblSharpLine ? 2.5 : 3.0;\n            if (line2.dblSharpLine)\n                clearance -= 0.5;\n        }\n        const collision = Math.abs(clearance) < clearanceRequired;\n        L('Line1, Line2, Collision: ', line1.line, line2.line, collision);\n        return collision;\n    }\n    static applyAccidentals(voices, keySignature) {\n        const tickPositions = [];\n        const tickNoteMap = {};\n        voices.forEach((voice) => {\n            const tickPosition = new Fraction(0, 1);\n            const tickable = voice.getTickables();\n            tickable.forEach((t) => {\n                if (t.shouldIgnoreTicks())\n                    return;\n                const notesAtPosition = tickNoteMap[tickPosition.value()];\n                if (!notesAtPosition) {\n                    tickPositions.push(tickPosition.value());\n                    tickNoteMap[tickPosition.value()] = [t];\n                }\n                else {\n                    notesAtPosition.push(t);\n                }\n                tickPosition.add(t.getTicks());\n            });\n        });\n        const music = new Music();\n        if (!keySignature)\n            keySignature = 'C';\n        const scaleMapKey = music.createScaleMap(keySignature);\n        const scaleMap = {};\n        tickPositions.forEach((tickPos) => {\n            const tickables = tickNoteMap[tickPos];\n            const modifiedPitches = [];\n            const processNote = (t) => {\n                if (!isStaveNote(t) || t.isRest() || t.shouldIgnoreTicks()) {\n                    return;\n                }\n                const staveNote = t;\n                staveNote.keys.forEach((keyString, keyIndex) => {\n                    const key = music.getNoteParts(keyString.split('/')[0]);\n                    const octave = keyString.split('/')[1];\n                    const accidentalString = key.accidental || 'n';\n                    const pitch = key.root + accidentalString;\n                    if (!scaleMap[key.root + octave])\n                        scaleMap[key.root + octave] = scaleMapKey[key.root];\n                    const sameAccidental = scaleMap[key.root + octave] === pitch;\n                    const previouslyModified = modifiedPitches.indexOf(keyString) > -1;\n                    staveNote.getModifiers().forEach((modifier, index) => {\n                        if (isAccidental(modifier) && modifier.type == accidentalString && modifier.getIndex() == keyIndex) {\n                            staveNote.getModifiers().splice(index, 1);\n                        }\n                    });\n                    if (!sameAccidental || (sameAccidental && previouslyModified)) {\n                        scaleMap[key.root + octave] = pitch;\n                        const accidental = new Accidental(accidentalString);\n                        staveNote.addModifier(accidental, keyIndex);\n                        modifiedPitches.push(keyString);\n                    }\n                });\n                staveNote.getModifiers().forEach((modifier) => {\n                    if (isGraceNoteGroup(modifier)) {\n                        modifier.getGraceNotes().forEach(processNote);\n                    }\n                });\n            };\n            tickables.forEach(processNote);\n        });\n    }\n    constructor(type) {\n        super();\n        L('New accidental: ', type);\n        this.type = type;\n        this.position = Modifier.Position.LEFT;\n        this.cautionary = false;\n        this.reset();\n    }\n    reset() {\n        this.text = '';\n        if (!this.cautionary) {\n            this.text += Tables.accidentalCodes(this.type);\n            this.fontInfo.size = Metrics.get('Accidental.fontSize');\n        }\n        else {\n            this.text += Tables.accidentalCodes('{');\n            this.text += Tables.accidentalCodes(this.type);\n            this.text += Tables.accidentalCodes('}');\n            this.fontInfo.size = Metrics.get('Accidental.cautionary.fontSize');\n        }\n        if (isGraceNote(this.note)) {\n            this.fontInfo.size = Metrics.get('Accidental.grace.fontSize');\n        }\n    }\n    setNote(note) {\n        defined(note, 'ArgumentError', `Bad note value: ${note}`);\n        this.note = note;\n        this.reset();\n        return this;\n    }\n    setAsCautionary() {\n        this.cautionary = true;\n        this.reset();\n        return this;\n    }\n    draw() {\n        const { type, position, index } = this;\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(position, index);\n        this.x = start.x - this.width;\n        this.y = start.y;\n        L('Rendering: ', type, start.x, start.y);\n        this.renderText(ctx, 0, 0);\n    }\n}\nAccidental.DEBUG = false;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,gBAAgB;AACzF,SAASC,OAAO,EAAEC,GAAG,QAAQ,WAAW;AACxC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,UAAU,CAACC,KAAK,EAChBJ,GAAG,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACvC;AACA,OAAO,MAAMC,UAAU,SAASX,QAAQ,CAAC;EACrC,WAAWa,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACA,OAAOC,MAAMA,CAACC,WAAW,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,KAAK,CAAC,EACxC;IACJ,MAAMC,yBAAyB,GAAGnB,OAAO,CAACoB,GAAG,CAAC,sCAAsC,CAAC;IACrF,MAAMC,SAAS,GAAGJ,KAAK,CAACI,SAAS,GAAGF,yBAAyB;IAC7D,MAAMG,iBAAiB,GAAGtB,OAAO,CAACoB,GAAG,CAAC,8BAA8B,CAAC;IACrE,MAAMG,iBAAiB,GAAGvB,OAAO,CAACoB,GAAG,CAAC,wBAAwB,CAAC;IAC/D,MAAMI,oCAAoC,GAAG,EAAE;IAC/C,IAAIC,QAAQ,GAAGC,SAAS;IACxB,IAAIC,yCAAyC,GAAG,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,WAAW,CAACE,MAAM,EAAE,EAAEU,CAAC,EAAE;MACzC,MAAMC,UAAU,GAAGb,WAAW,CAACY,CAAC,CAAC;MACjC,MAAME,IAAI,GAAGD,UAAU,CAACE,OAAO,CAAC,CAAC;MACjC,MAAMC,KAAK,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC;MAC7B,MAAMC,KAAK,GAAGL,UAAU,CAACM,UAAU,CAAC,CAAC;MACrC,MAAMC,KAAK,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC,CAACH,KAAK,CAAC;MACvC,IAAIJ,IAAI,KAAKL,QAAQ,EAAE;QACnB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACS,IAAI,CAACrB,MAAM,EAAE,EAAEoB,CAAC,EAAE;UACvCX,yCAAyC,GAAGa,IAAI,CAACC,GAAG,CAACX,IAAI,CAACY,sBAAsB,CAAC,CAAC,GAAGZ,IAAI,CAACa,SAAS,CAAC,CAAC,EAAEhB,yCAAyC,CAAC;QACrJ;QACAF,QAAQ,GAAGK,IAAI;MACnB;MACA,IAAIE,KAAK,EAAE;QACP,MAAMY,SAAS,GAAGZ,KAAK,CAACa,sBAAsB,CAAC,CAAC;QAChD,MAAMC,CAAC,GAAGd,KAAK,CAACe,WAAW,CAACX,KAAK,CAACY,IAAI,CAAC;QACvC,MAAMC,OAAO,GAAGT,IAAI,CAACU,KAAK,CAAEJ,CAAC,GAAGF,SAAS,GAAI,CAAC,CAAC,GAAG,CAAC;QACnDpB,oCAAoC,CAAC2B,IAAI,CAAC;UACtCL,CAAC;UACDE,IAAI,EAAEC,OAAO;UACbG,iBAAiB,EAAEzB,yCAAyC;UAC5DE,UAAU,EAAEA,UAAU;UACtBwB,wBAAwB,EAAET;QAC9B,CAAC,CAAC;MACN,CAAC,MACI;QACDpB,oCAAoC,CAAC2B,IAAI,CAAC;UACtCH,IAAI,EAAEZ,KAAK,CAACY,IAAI;UAChBI,iBAAiB,EAAEzB,yCAAyC;UAC5DE,UAAU,EAAEA;QAChB,CAAC,CAAC;MACN;IACJ;IACAL,oCAAoC,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACR,IAAI,GAAGO,CAAC,CAACP,IAAI,CAAC;IACpE,MAAMS,gCAAgC,GAAG,EAAE;IAC3C,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,oCAAoC,CAACN,MAAM,EAAEU,CAAC,EAAE,EAAE;MAClE,MAAM+B,mCAAmC,GAAGnC,oCAAoC,CAACI,CAAC,CAAC;MACnF,MAAMgC,cAAc,GAAGD,mCAAmC,CAAC9B,UAAU,CAACgC,IAAI;MAC1E,MAAMC,eAAe,GAAGL,gCAAgC,CAACA,gCAAgC,CAACvC,MAAM,GAAG,CAAC,CAAC;MACrG,IAAI6C,iBAAiB;MACrB,IAAI,CAACD,eAAe,IAAI,CAACA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACd,IAAI,MAAMW,mCAAmC,CAACX,IAAI,EAAE;QAC3Je,iBAAiB,GAAG;UAChBf,IAAI,EAAEW,mCAAmC,CAACX,IAAI;UAC9CgB,QAAQ,EAAE,IAAI;UACdC,YAAY,EAAE,IAAI;UAClBC,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;QACZ,CAAC;QACDX,gCAAgC,CAACN,IAAI,CAACY,iBAAiB,CAAC;MAC5D,CAAC,MACI;QACDA,iBAAiB,GAAGD,eAAe;MACvC;MACA,IAAIF,cAAc,KAAK,GAAG,IAAIA,cAAc,KAAK,IAAI,EAAE;QACnDG,iBAAiB,CAACC,QAAQ,GAAG,KAAK;MACtC;MACA,IAAIJ,cAAc,KAAK,IAAI,EAAE;QACzBG,iBAAiB,CAACE,YAAY,GAAG,KAAK;MAC1C;MACAF,iBAAiB,CAACG,MAAM,EAAE;MAC1BH,iBAAiB,CAACI,KAAK,IAAIR,mCAAmC,CAAC9B,UAAU,CAACwC,QAAQ,CAAC,CAAC,GAAG/C,iBAAiB;MACxGoC,oBAAoB,GAAGlB,IAAI,CAACC,GAAG,CAACkB,mCAAmC,CAACP,iBAAiB,EAAEM,oBAAoB,CAAC;IAChH;IACA,IAAIY,YAAY,GAAG,CAAC;IACpB,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,gCAAgC,CAACvC,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC9D,IAAI2C,kBAAkB,GAAG,KAAK;MAC9B,MAAMC,UAAU,GAAG5C,CAAC;MACpB,IAAI6C,QAAQ,GAAG7C,CAAC;MAChB,OAAO6C,QAAQ,GAAG,CAAC,GAAGhB,gCAAgC,CAACvC,MAAM,IAAI,CAACqD,kBAAkB,EAAE;QAClF,IAAI,IAAI,CAACG,cAAc,CAACjB,gCAAgC,CAACgB,QAAQ,CAAC,EAAEhB,gCAAgC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE;UACjHA,QAAQ,EAAE;QACd,CAAC,MACI;UACDF,kBAAkB,GAAG,IAAI;QAC7B;MACJ;MACA,MAAMI,YAAY,GAAIzC,KAAK,IAAKuB,gCAAgC,CAACe,UAAU,GAAGtC,KAAK,CAAC;MACpF,MAAM0C,aAAa,GAAIC,OAAO,IAAKA,OAAO,CAACC,GAAG,CAACH,YAAY,CAAC;MAC5D,MAAMI,cAAc,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;QACvC,MAAM,CAAC1B,CAAC,EAAEC,CAAC,CAAC,GAAGoB,aAAa,CAAC,CAACI,MAAM,EAAEC,MAAM,CAAC,CAAC,CAACH,GAAG,CAAEI,IAAI,IAAKA,IAAI,CAAClC,IAAI,CAAC;QACvE,OAAOO,CAAC,GAAGC,CAAC;MAChB,CAAC;MACD,MAAM2B,YAAY,GAAGA,CAAC,GAAGC,UAAU,KAAKA,UAAU,CAACN,GAAG,CAACF,aAAa,CAAC,CAACS,KAAK,CAAC,CAAC,CAACC,KAAK,EAAEC,KAAK,CAAC,KAAK,CAAC,IAAI,CAACb,cAAc,CAACY,KAAK,EAAEC,KAAK,CAAC,CAAC;MACnI,MAAMC,WAAW,GAAGf,QAAQ,GAAGD,UAAU,GAAG,CAAC;MAC7C,IAAIiB,OAAO,GAAG,IAAI,CAACf,cAAc,CAACjB,gCAAgC,CAACe,UAAU,CAAC,EAAEf,gCAAgC,CAACgB,QAAQ,CAAC,CAAC,GACrH,GAAG,GACH,GAAG;MACT,QAAQe,WAAW;QACf,KAAK,CAAC;UACF,IAAIC,OAAO,KAAK,GAAG,IAAIV,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,IAAIA,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE;YACjFU,OAAO,GAAG,gBAAgB;UAC9B;UACA;QACJ,KAAK,CAAC;UACF,IAAIN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC9BM,OAAO,GAAG,qBAAqB;UACnC;UACA;QACJ,KAAK,CAAC;UACF,IAAIA,OAAO,KAAK,GAAG,IAAIN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACzCM,OAAO,GAAG,qBAAqB;YAC/B,IAAIN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;cAC9BM,OAAO,GAAG,yBAAyB;YACvC;UACJ;UACA;QACJ,KAAK,CAAC;UACF,IAAIN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACtCM,OAAO,GAAG,oBAAoB;UAClC;UACA,IAAIN,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC9CM,OAAO,GAAG,wBAAwB;UACtC;UACA;QACJ;UACI;MACR;MACA,IAAIC,WAAW;MACf,IAAItB,MAAM;MACV,IAAIoB,WAAW,IAAI,CAAC,EAAE;QAClB,IAAIG,aAAa,GAAG,CAAC;QACrB,IAAIC,iBAAiB,GAAG,IAAI;QAC5B,OAAOA,iBAAiB,KAAK,IAAI,EAAE;UAC/BA,iBAAiB,GAAG,KAAK;UACzB,KAAK,IAAI5C,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG2C,aAAa,GAAGlC,gCAAgC,CAACvC,MAAM,EAAE8B,IAAI,EAAE,EAAE;YACvF,IAAI,IAAI,CAAC0B,cAAc,CAACjB,gCAAgC,CAACT,IAAI,CAAC,EAAES,gCAAgC,CAACT,IAAI,GAAG2C,aAAa,CAAC,CAAC,EAAE;cACrHC,iBAAiB,GAAG,IAAI;cACxBD,aAAa,EAAE;cACf;YACJ;UACJ;QACJ;QACA,KAAKD,WAAW,GAAG9D,CAAC,EAAE8D,WAAW,IAAIjB,QAAQ,EAAEiB,WAAW,EAAE,EAAE;UAC1DtB,MAAM,GAAI,CAACsB,WAAW,GAAG9D,CAAC,IAAI+D,aAAa,GAAI,CAAC;UAChDlC,gCAAgC,CAACiC,WAAW,CAAC,CAACtB,MAAM,GAAGA,MAAM;UAC7DE,YAAY,GAAGA,YAAY,GAAGF,MAAM,GAAGE,YAAY,GAAGF,MAAM;QAChE;MACJ,CAAC,MACI;QACD,KAAKsB,WAAW,GAAG9D,CAAC,EAAE8D,WAAW,IAAIjB,QAAQ,EAAEiB,WAAW,EAAE,EAAE;UAC1DtB,MAAM,GAAGjE,MAAM,CAAC0F,sBAAsB,CAACL,WAAW,CAAC,CAACC,OAAO,CAAC,CAACC,WAAW,GAAG9D,CAAC,CAAC;UAC7E6B,gCAAgC,CAACiC,WAAW,CAAC,CAACtB,MAAM,GAAGA,MAAM;UAC7DE,YAAY,GAAGA,YAAY,GAAGF,MAAM,GAAGE,YAAY,GAAGF,MAAM;QAChE;MACJ;MACAxC,CAAC,GAAG6C,QAAQ;IAChB;IACA,MAAMqB,YAAY,GAAG,EAAE;IACvB,MAAMC,cAAc,GAAG,EAAE;IACzB,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI0C,YAAY,EAAE1C,CAAC,EAAE,EAAE;MACpCkE,YAAY,CAAClE,CAAC,CAAC,GAAG,CAAC;MACnBmE,cAAc,CAACnE,CAAC,CAAC,GAAG,CAAC;IACzB;IACAkE,YAAY,CAAC,CAAC,CAAC,GAAGzE,SAAS,GAAGqC,oBAAoB;IAClDqC,cAAc,CAAC,CAAC,CAAC,GAAG1E,SAAS;IAC7BoC,gCAAgC,CAACuC,OAAO,CAAEhD,IAAI,IAAK;MAC/C,IAAIA,IAAI,CAACmB,KAAK,GAAG2B,YAAY,CAAC9C,IAAI,CAACoB,MAAM,CAAC,EACtC0B,YAAY,CAAC9C,IAAI,CAACoB,MAAM,CAAC,GAAGpB,IAAI,CAACmB,KAAK;IAC9C,CAAC,CAAC;IACF,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,YAAY,CAAC5E,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC1CmE,cAAc,CAACnE,CAAC,CAAC,GAAGkE,YAAY,CAAClE,CAAC,CAAC,GAAGmE,cAAc,CAACnE,CAAC,GAAG,CAAC,CAAC;IAC/D;IACA,MAAMqE,UAAU,GAAGF,cAAc,CAACA,cAAc,CAAC7E,MAAM,GAAG,CAAC,CAAC;IAC5D,IAAIgF,QAAQ,GAAG,CAAC;IAChBzC,gCAAgC,CAACuC,OAAO,CAAEhD,IAAI,IAAK;MAC/C,IAAImD,SAAS,GAAG,CAAC;MACjB,MAAMC,aAAa,GAAGF,QAAQ,GAAGlD,IAAI,CAACkB,MAAM;MAC5C,KAAKgC,QAAQ,EAAEA,QAAQ,GAAGE,aAAa,EAAEF,QAAQ,EAAE,EAAE;QACjD,MAAMG,MAAM,GAAGN,cAAc,CAAC/C,IAAI,CAACoB,MAAM,GAAG,CAAC,CAAC,GAAG+B,SAAS,GAAGzC,oBAAoB;QACjFlC,oCAAoC,CAAC0E,QAAQ,CAAC,CAACrE,UAAU,CAACyE,SAAS,CAACD,MAAM,CAAC;QAC3EF,SAAS,IAAI3E,oCAAoC,CAAC0E,QAAQ,CAAC,CAACrE,UAAU,CAACwC,QAAQ,CAAC,CAAC,GAAG/C,iBAAiB;QACrGZ,CAAC,CAAC,yBAAyB,EAAEsC,IAAI,CAACA,IAAI,EAAEkD,QAAQ,EAAEG,MAAM,CAAC;MAC7D;IACJ,CAAC,CAAC;IACFpF,KAAK,CAACI,SAAS,GAAG4E,UAAU,GAAG1E,iBAAiB;EACpD;EACA,OAAOmD,cAAcA,CAACY,KAAK,EAAEC,KAAK,EAAE;IAChC,IAAIgB,SAAS,GAAGhB,KAAK,CAACvC,IAAI,GAAGsC,KAAK,CAACtC,IAAI;IACvC,IAAIwD,iBAAiB,GAAG,CAAC;IACzB,IAAID,SAAS,GAAG,CAAC,EAAE;MACfC,iBAAiB,GAAGjB,KAAK,CAACvB,QAAQ,IAAIuB,KAAK,CAACtB,YAAY,GAAG,GAAG,GAAG,GAAG;MACpE,IAAIqB,KAAK,CAACrB,YAAY,EAClBsC,SAAS,IAAI,GAAG;IACxB,CAAC,MACI;MACDC,iBAAiB,GAAGlB,KAAK,CAACtB,QAAQ,IAAIsB,KAAK,CAACrB,YAAY,GAAG,GAAG,GAAG,GAAG;MACpE,IAAIsB,KAAK,CAACtB,YAAY,EAClBsC,SAAS,IAAI,GAAG;IACxB;IACA,MAAME,SAAS,GAAGjE,IAAI,CAACkE,GAAG,CAACH,SAAS,CAAC,GAAGC,iBAAiB;IACzD9F,CAAC,CAAC,2BAA2B,EAAE4E,KAAK,CAACtC,IAAI,EAAEuC,KAAK,CAACvC,IAAI,EAAEyD,SAAS,CAAC;IACjE,OAAOA,SAAS;EACpB;EACA,OAAOE,gBAAgBA,CAACC,MAAM,EAAEC,YAAY,EAAE;IAC1C,MAAMC,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,CAAC,CAAC;IACtBH,MAAM,CAACZ,OAAO,CAAEgB,KAAK,IAAK;MACtB,MAAMC,YAAY,GAAG,IAAIlH,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvC,MAAMmH,QAAQ,GAAGF,KAAK,CAACG,YAAY,CAAC,CAAC;MACrCD,QAAQ,CAAClB,OAAO,CAAEoB,CAAC,IAAK;QACpB,IAAIA,CAAC,CAACC,iBAAiB,CAAC,CAAC,EACrB;QACJ,MAAMC,eAAe,GAAGP,WAAW,CAACE,YAAY,CAACM,KAAK,CAAC,CAAC,CAAC;QACzD,IAAI,CAACD,eAAe,EAAE;UAClBR,aAAa,CAAC3D,IAAI,CAAC8D,YAAY,CAACM,KAAK,CAAC,CAAC,CAAC;UACxCR,WAAW,CAACE,YAAY,CAACM,KAAK,CAAC,CAAC,CAAC,GAAG,CAACH,CAAC,CAAC;QAC3C,CAAC,MACI;UACDE,eAAe,CAACnE,IAAI,CAACiE,CAAC,CAAC;QAC3B;QACAH,YAAY,CAACO,GAAG,CAACJ,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,MAAMC,KAAK,GAAG,IAAIxH,KAAK,CAAC,CAAC;IACzB,IAAI,CAAC2G,YAAY,EACbA,YAAY,GAAG,GAAG;IACtB,MAAMc,WAAW,GAAGD,KAAK,CAACE,cAAc,CAACf,YAAY,CAAC;IACtD,MAAMgB,QAAQ,GAAG,CAAC,CAAC;IACnBf,aAAa,CAACd,OAAO,CAAE8B,OAAO,IAAK;MAC/B,MAAMC,SAAS,GAAGhB,WAAW,CAACe,OAAO,CAAC;MACtC,MAAME,eAAe,GAAG,EAAE;MAC1B,MAAMC,WAAW,GAAIb,CAAC,IAAK;QACvB,IAAI,CAAC7G,WAAW,CAAC6G,CAAC,CAAC,IAAIA,CAAC,CAACc,MAAM,CAAC,CAAC,IAAId,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;UACxD;QACJ;QACA,MAAMc,SAAS,GAAGf,CAAC;QACnBe,SAAS,CAAC5F,IAAI,CAACyD,OAAO,CAAC,CAACoC,SAAS,EAAEC,QAAQ,KAAK;UAC5C,MAAMC,GAAG,GAAGZ,KAAK,CAACa,YAAY,CAACH,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,MAAMC,MAAM,GAAGL,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACtC,MAAME,gBAAgB,GAAGJ,GAAG,CAACzG,UAAU,IAAI,GAAG;UAC9C,MAAM8G,KAAK,GAAGL,GAAG,CAACM,IAAI,GAAGF,gBAAgB;UACzC,IAAI,CAACb,QAAQ,CAACS,GAAG,CAACM,IAAI,GAAGH,MAAM,CAAC,EAC5BZ,QAAQ,CAACS,GAAG,CAACM,IAAI,GAAGH,MAAM,CAAC,GAAGd,WAAW,CAACW,GAAG,CAACM,IAAI,CAAC;UACvD,MAAMC,cAAc,GAAGhB,QAAQ,CAACS,GAAG,CAACM,IAAI,GAAGH,MAAM,CAAC,KAAKE,KAAK;UAC5D,MAAMG,kBAAkB,GAAGd,eAAe,CAACe,OAAO,CAACX,SAAS,CAAC,GAAG,CAAC,CAAC;UAClED,SAAS,CAACa,YAAY,CAAC,CAAC,CAAChD,OAAO,CAAC,CAACiD,QAAQ,EAAE/G,KAAK,KAAK;YAClD,IAAI9B,YAAY,CAAC6I,QAAQ,CAAC,IAAIA,QAAQ,CAACpF,IAAI,IAAI6E,gBAAgB,IAAIO,QAAQ,CAACC,QAAQ,CAAC,CAAC,IAAIb,QAAQ,EAAE;cAChGF,SAAS,CAACa,YAAY,CAAC,CAAC,CAACG,MAAM,CAACjH,KAAK,EAAE,CAAC,CAAC;YAC7C;UACJ,CAAC,CAAC;UACF,IAAI,CAAC2G,cAAc,IAAKA,cAAc,IAAIC,kBAAmB,EAAE;YAC3DjB,QAAQ,CAACS,GAAG,CAACM,IAAI,GAAGH,MAAM,CAAC,GAAGE,KAAK;YACnC,MAAM9G,UAAU,GAAG,IAAIjB,UAAU,CAAC8H,gBAAgB,CAAC;YACnDP,SAAS,CAACiB,WAAW,CAACvH,UAAU,EAAEwG,QAAQ,CAAC;YAC3CL,eAAe,CAAC7E,IAAI,CAACiF,SAAS,CAAC;UACnC;QACJ,CAAC,CAAC;QACFD,SAAS,CAACa,YAAY,CAAC,CAAC,CAAChD,OAAO,CAAEiD,QAAQ,IAAK;UAC3C,IAAI3I,gBAAgB,CAAC2I,QAAQ,CAAC,EAAE;YAC5BA,QAAQ,CAACI,aAAa,CAAC,CAAC,CAACrD,OAAO,CAACiC,WAAW,CAAC;UACjD;QACJ,CAAC,CAAC;MACN,CAAC;MACDF,SAAS,CAAC/B,OAAO,CAACiC,WAAW,CAAC;IAClC,CAAC,CAAC;EACN;EACAqB,WAAWA,CAACzF,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACPnD,CAAC,CAAC,kBAAkB,EAAEmD,IAAI,CAAC;IAC3B,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC0F,QAAQ,GAAGtJ,QAAQ,CAACuJ,QAAQ,CAACC,IAAI;IACtC,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE;MAClB,IAAI,CAACE,IAAI,IAAIzJ,MAAM,CAAC0J,eAAe,CAAC,IAAI,CAAChG,IAAI,CAAC;MAC9C,IAAI,CAACiG,QAAQ,CAACC,IAAI,GAAG/J,OAAO,CAACoB,GAAG,CAAC,qBAAqB,CAAC;IAC3D,CAAC,MACI;MACD,IAAI,CAACwI,IAAI,IAAIzJ,MAAM,CAAC0J,eAAe,CAAC,GAAG,CAAC;MACxC,IAAI,CAACD,IAAI,IAAIzJ,MAAM,CAAC0J,eAAe,CAAC,IAAI,CAAChG,IAAI,CAAC;MAC9C,IAAI,CAAC+F,IAAI,IAAIzJ,MAAM,CAAC0J,eAAe,CAAC,GAAG,CAAC;MACxC,IAAI,CAACC,QAAQ,CAACC,IAAI,GAAG/J,OAAO,CAACoB,GAAG,CAAC,gCAAgC,CAAC;IACtE;IACA,IAAIf,WAAW,CAAC,IAAI,CAACyB,IAAI,CAAC,EAAE;MACxB,IAAI,CAACgI,QAAQ,CAACC,IAAI,GAAG/J,OAAO,CAACoB,GAAG,CAAC,2BAA2B,CAAC;IACjE;EACJ;EACA4I,OAAOA,CAAClI,IAAI,EAAE;IACVtB,OAAO,CAACsB,IAAI,EAAE,eAAe,EAAE,mBAAmBA,IAAI,EAAE,CAAC;IACzD,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC6H,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf;EACAM,eAAeA,CAAA,EAAG;IACd,IAAI,CAACP,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf;EACAO,IAAIA,CAAA,EAAG;IACH,MAAM;MAAErG,IAAI;MAAE0F,QAAQ;MAAErH;IAAM,CAAC,GAAG,IAAI;IACtC,MAAMiI,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMtI,IAAI,GAAG,IAAI,CAACuI,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGzI,IAAI,CAAC0I,kBAAkB,CAACjB,QAAQ,EAAErH,KAAK,CAAC;IACtD,IAAI,CAACuI,CAAC,GAAGF,KAAK,CAACE,CAAC,GAAG,IAAI,CAACtG,KAAK;IAC7B,IAAI,CAACrB,CAAC,GAAGyH,KAAK,CAACzH,CAAC;IAChBpC,CAAC,CAAC,aAAa,EAAEmD,IAAI,EAAE0G,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACzH,CAAC,CAAC;IACxC,IAAI,CAAC4H,UAAU,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B;AACJ;AACAvJ,UAAU,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}