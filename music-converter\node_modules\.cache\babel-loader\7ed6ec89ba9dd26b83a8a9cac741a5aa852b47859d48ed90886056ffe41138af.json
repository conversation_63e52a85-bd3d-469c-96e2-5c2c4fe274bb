{"ast": null, "code": "import { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Tuning {\n  static get names() {\n    return {\n      standard: 'E/5,B/4,G/4,D/4,A/3,E/3',\n      dagdad: 'D/5,A/4,G/4,D/4,A/3,D/3',\n      dropd: 'E/5,B/4,G/4,D/4,A/3,D/3',\n      eb: 'Eb/5,Bb/4,Gb/4,Db/4,Ab/3,Db/3',\n      standardBanjo: 'D/5,B/4,G/4,D/4,G/5'\n    };\n  }\n  constructor(tuningString = 'E/5,B/4,G/4,D/4,A/3,E/3,B/2,E/2') {\n    this.tuningValues = [];\n    this.setTuning(tuningString);\n  }\n  noteToInteger(noteString) {\n    var _a;\n    return (_a = Tables.keyProperties(noteString).intValue) !== null && _a !== void 0 ? _a : -1;\n  }\n  setTuning(tuningString) {\n    if (Tuning.names[tuningString]) {\n      tuningString = Tuning.names[tuningString];\n    }\n    this.tuningValues = [];\n    const keys = tuningString.split(/\\s*,\\s*/);\n    if (keys.length === 0) {\n      throw new RuntimeError('BadArguments', `Invalid tuning string: ${tuningString}`);\n    }\n    for (let i = 0; i < keys.length; ++i) {\n      this.tuningValues[i] = this.noteToInteger(keys[i]);\n    }\n  }\n  getValueForString(stringNum) {\n    const s = Number(stringNum);\n    if (s < 1 || s > this.tuningValues.length) {\n      throw new RuntimeError('BadArguments', `String number must be between 1 and ${this.tuningValues.length}:${stringNum}`);\n    }\n    return this.tuningValues[s - 1];\n  }\n  getValueForFret(fretNum, stringNum) {\n    const stringValue = this.getValueForString(stringNum);\n    const f = Number(fretNum);\n    if (f < 0) {\n      throw new RuntimeError('BadArguments', `Fret number must be 0 or higher: ${fretNum}`);\n    }\n    return stringValue + f;\n  }\n  getNoteForFret(fretNum, stringNum) {\n    const noteValue = this.getValueForFret(fretNum, stringNum);\n    const octave = Math.floor(noteValue / 12);\n    const value = noteValue % 12;\n    return `${Tables.integerToNote(value)}/${octave}`;\n  }\n}", "map": {"version": 3, "names": ["Tables", "RuntimeError", "Tuning", "names", "standard", "dag<PERSON>", "dropd", "eb", "standardBanjo", "constructor", "tuningString", "tuningValues", "setTuning", "noteToInteger", "noteString", "_a", "keyProperties", "intValue", "keys", "split", "length", "i", "getValueForString", "stringNum", "s", "Number", "getValueForFret", "fretNum", "stringValue", "f", "getNoteForFret", "noteValue", "octave", "Math", "floor", "value", "integerToNote"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tuning.js"], "sourcesContent": ["import { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Tuning {\n    static get names() {\n        return {\n            standard: 'E/5,B/4,G/4,D/4,A/3,E/3',\n            dagdad: 'D/5,A/4,G/4,D/4,A/3,D/3',\n            dropd: 'E/5,B/4,G/4,D/4,A/3,D/3',\n            eb: 'Eb/5,Bb/4,Gb/4,Db/4,Ab/3,Db/3',\n            standardBanjo: 'D/5,B/4,G/4,D/4,G/5',\n        };\n    }\n    constructor(tuningString = 'E/5,B/4,G/4,D/4,A/3,E/3,B/2,E/2') {\n        this.tuningValues = [];\n        this.setTuning(tuningString);\n    }\n    noteToInteger(noteString) {\n        var _a;\n        return (_a = Tables.keyProperties(noteString).intValue) !== null && _a !== void 0 ? _a : -1;\n    }\n    setTuning(tuningString) {\n        if (Tuning.names[tuningString]) {\n            tuningString = Tuning.names[tuningString];\n        }\n        this.tuningValues = [];\n        const keys = tuningString.split(/\\s*,\\s*/);\n        if (keys.length === 0) {\n            throw new RuntimeError('BadArguments', `Invalid tuning string: ${tuningString}`);\n        }\n        for (let i = 0; i < keys.length; ++i) {\n            this.tuningValues[i] = this.noteToInteger(keys[i]);\n        }\n    }\n    getValueForString(stringNum) {\n        const s = Number(stringNum);\n        if (s < 1 || s > this.tuningValues.length) {\n            throw new RuntimeError('BadArguments', `String number must be between 1 and ${this.tuningValues.length}:${stringNum}`);\n        }\n        return this.tuningValues[s - 1];\n    }\n    getValueForFret(fretNum, stringNum) {\n        const stringValue = this.getValueForString(stringNum);\n        const f = Number(fretNum);\n        if (f < 0) {\n            throw new RuntimeError('BadArguments', `Fret number must be 0 or higher: ${fretNum}`);\n        }\n        return stringValue + f;\n    }\n    getNoteForFret(fretNum, stringNum) {\n        const noteValue = this.getValueForFret(fretNum, stringNum);\n        const octave = Math.floor(noteValue / 12);\n        const value = noteValue % 12;\n        return `${Tables.integerToNote(value)}/${octave}`;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,MAAM,CAAC;EAChB,WAAWC,KAAKA,CAAA,EAAG;IACf,OAAO;MACHC,QAAQ,EAAE,yBAAyB;MACnCC,MAAM,EAAE,yBAAyB;MACjCC,KAAK,EAAE,yBAAyB;MAChCC,EAAE,EAAE,+BAA+B;MACnCC,aAAa,EAAE;IACnB,CAAC;EACL;EACAC,WAAWA,CAACC,YAAY,GAAG,iCAAiC,EAAE;IAC1D,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,SAAS,CAACF,YAAY,CAAC;EAChC;EACAG,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAIC,EAAE;IACN,OAAO,CAACA,EAAE,GAAGf,MAAM,CAACgB,aAAa,CAACF,UAAU,CAAC,CAACG,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EAC/F;EACAH,SAASA,CAACF,YAAY,EAAE;IACpB,IAAIR,MAAM,CAACC,KAAK,CAACO,YAAY,CAAC,EAAE;MAC5BA,YAAY,GAAGR,MAAM,CAACC,KAAK,CAACO,YAAY,CAAC;IAC7C;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,MAAMO,IAAI,GAAGR,YAAY,CAACS,KAAK,CAAC,SAAS,CAAC;IAC1C,IAAID,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;MACnB,MAAM,IAAInB,YAAY,CAAC,cAAc,EAAE,0BAA0BS,YAAY,EAAE,CAAC;IACpF;IACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACE,MAAM,EAAE,EAAEC,CAAC,EAAE;MAClC,IAAI,CAACV,YAAY,CAACU,CAAC,CAAC,GAAG,IAAI,CAACR,aAAa,CAACK,IAAI,CAACG,CAAC,CAAC,CAAC;IACtD;EACJ;EACAC,iBAAiBA,CAACC,SAAS,EAAE;IACzB,MAAMC,CAAC,GAAGC,MAAM,CAACF,SAAS,CAAC;IAC3B,IAAIC,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACb,YAAY,CAACS,MAAM,EAAE;MACvC,MAAM,IAAInB,YAAY,CAAC,cAAc,EAAE,uCAAuC,IAAI,CAACU,YAAY,CAACS,MAAM,IAAIG,SAAS,EAAE,CAAC;IAC1H;IACA,OAAO,IAAI,CAACZ,YAAY,CAACa,CAAC,GAAG,CAAC,CAAC;EACnC;EACAE,eAAeA,CAACC,OAAO,EAAEJ,SAAS,EAAE;IAChC,MAAMK,WAAW,GAAG,IAAI,CAACN,iBAAiB,CAACC,SAAS,CAAC;IACrD,MAAMM,CAAC,GAAGJ,MAAM,CAACE,OAAO,CAAC;IACzB,IAAIE,CAAC,GAAG,CAAC,EAAE;MACP,MAAM,IAAI5B,YAAY,CAAC,cAAc,EAAE,oCAAoC0B,OAAO,EAAE,CAAC;IACzF;IACA,OAAOC,WAAW,GAAGC,CAAC;EAC1B;EACAC,cAAcA,CAACH,OAAO,EAAEJ,SAAS,EAAE;IAC/B,MAAMQ,SAAS,GAAG,IAAI,CAACL,eAAe,CAACC,OAAO,EAAEJ,SAAS,CAAC;IAC1D,MAAMS,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,GAAG,EAAE,CAAC;IACzC,MAAMI,KAAK,GAAGJ,SAAS,GAAG,EAAE;IAC5B,OAAO,GAAG/B,MAAM,CAACoC,aAAa,CAACD,KAAK,CAAC,IAAIH,MAAM,EAAE;EACrD;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}