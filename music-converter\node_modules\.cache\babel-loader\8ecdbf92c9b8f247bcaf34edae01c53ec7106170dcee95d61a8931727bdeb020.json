{"ast": null, "code": "export class Metrics {\n  static clear(key) {\n    if (key) {\n      this.cacheFont.delete(key);\n      this.cacheStyle.delete(key);\n    } else {\n      this.cacheFont.clear();\n      this.cacheStyle.clear();\n    }\n  }\n  static getFontInfo(key) {\n    let font = this.cacheFont.get(key);\n    if (!font) {\n      font = {\n        family: Metrics.get(`${key}.fontFamily`),\n        size: Metrics.get(`${key}.fontSize`) * Metrics.get(`${key}.fontScale`),\n        weight: Metrics.get(`${key}.fontWeight`),\n        style: Metrics.get(`${key}.fontStyle`)\n      };\n      this.cacheFont.set(key, font);\n    }\n    return structuredClone(font);\n  }\n  static getStyle(key) {\n    let style = this.cacheStyle.get(key);\n    if (!style) {\n      style = {\n        fillStyle: Metrics.get(`${key}.fillStyle`),\n        strokeStyle: Metrics.get(`${key}.strokeStyle`),\n        lineWidth: Metrics.get(`${key}.lineWidth`),\n        lineDash: Metrics.get(`${key}.lineDash`),\n        shadowBlur: Metrics.get(`${key}.shadowBlur`),\n        shadowColor: Metrics.get(`${key}.shadowColor`)\n      };\n      this.cacheStyle.set(key, style);\n    }\n    return structuredClone(style);\n  }\n  static get(key, defaultValue) {\n    var _a;\n    const keyParts = key.split('.');\n    const lastKeyPart = keyParts.pop();\n    let curr = MetricsDefaults;\n    let retVal = defaultValue;\n    while (curr) {\n      retVal = (_a = curr[lastKeyPart]) !== null && _a !== void 0 ? _a : retVal;\n      const keyPart = keyParts.shift();\n      if (keyPart) {\n        curr = curr[keyPart];\n      } else {\n        break;\n      }\n    }\n    return retVal;\n  }\n}\nMetrics.cacheStyle = new Map();\nMetrics.cacheFont = new Map();\nexport const MetricsDefaults = {\n  fontFamily: 'Bravura,Academico',\n  fontSize: 30,\n  fontScale: 1.0,\n  fontWeight: 'normal',\n  fontStyle: 'normal',\n  Accidental: {\n    cautionary: {\n      fontSize: 20\n    },\n    grace: {\n      fontSize: 20\n    },\n    noteheadAccidentalPadding: 1,\n    leftPadding: 2,\n    accidentalSpacing: 3\n  },\n  Annotation: {\n    fontSize: 10\n  },\n  Bend: {\n    fontSize: 10,\n    line: {\n      strokeStyle: '#777777',\n      lineWidth: 1\n    }\n  },\n  ChordSymbol: {\n    fontSize: 12,\n    spacing: 0.05,\n    subscriptOffset: 0.2,\n    superscriptOffset: -0.4,\n    superSubRatio: 0.6\n  },\n  FretHandFinger: {\n    fontSize: 9,\n    fontWeight: 'bold'\n  },\n  GraceNote: {\n    fontScale: 2 / 3\n  },\n  GraceTabNote: {\n    fontScale: 2 / 3\n  },\n  NoteHead: {\n    minPadding: 2\n  },\n  PedalMarking: {\n    text: {\n      fontSize: 12,\n      fontStyle: 'italic'\n    }\n  },\n  Repetition: {\n    text: {\n      fontSize: 12,\n      fontWeight: 'bold',\n      offsetX: 12,\n      offsetY: 25,\n      spacing: 5\n    },\n    coda: {\n      offsetY: 25\n    },\n    segno: {\n      offsetY: 10\n    }\n  },\n  Stave: {\n    strokeStyle: '#999999',\n    fontSize: 8,\n    padding: 12,\n    endPaddingMax: 10,\n    endPaddingMin: 5,\n    unalignedNotePadding: 10\n  },\n  StaveConnector: {\n    text: {\n      fontSize: 16\n    }\n  },\n  StaveLine: {\n    fontSize: 10\n  },\n  StaveSection: {\n    fontSize: 10,\n    fontWeight: 'bold',\n    lineWidth: 2,\n    padding: 2,\n    strokeStyle: 'black'\n  },\n  StaveTempo: {\n    fontSize: 14,\n    glyph: {\n      fontSize: 25\n    },\n    name: {\n      fontWeight: 'bold'\n    }\n  },\n  StaveText: {\n    fontSize: 16\n  },\n  StaveTie: {\n    fontSize: 10\n  },\n  Stem: {\n    strokeStyle: 'black'\n  },\n  StringNumber: {\n    fontSize: 10,\n    fontWeight: 'bold',\n    verticalPadding: 8,\n    stemPadding: 2,\n    leftPadding: 5,\n    rightPadding: 6\n  },\n  Stroke: {\n    text: {\n      fontSize: 10,\n      fontStyle: 'italic',\n      fontWeight: 'bold'\n    }\n  },\n  TabNote: {\n    text: {\n      fontSize: 9\n    }\n  },\n  TabSlide: {\n    fontSize: 10,\n    fontStyle: 'italic',\n    fontWeight: 'bold'\n  },\n  TabStave: {\n    strokeStyle: '#999999',\n    fontSize: 8\n  },\n  TabTie: {\n    fontSize: 10\n  },\n  TextBracket: {\n    fontSize: 15,\n    fontStyle: 'italic'\n  },\n  TextNote: {\n    text: {\n      fontSize: 12\n    }\n  },\n  Tremolo: {\n    spacing: 7\n  },\n  Tuplet: {\n    yOffset: 0,\n    textYOffset: 2\n  },\n  Volta: {\n    fontSize: 9,\n    fontWeight: 'bold'\n  }\n};", "map": {"version": 3, "names": ["Metrics", "clear", "key", "cacheFont", "delete", "cacheStyle", "getFontInfo", "font", "get", "family", "size", "weight", "style", "set", "structuredClone", "getStyle", "fillStyle", "strokeStyle", "lineWidth", "lineDash", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "defaultValue", "_a", "keyParts", "split", "last<PERSON>eyPart", "pop", "curr", "MetricsDefaults", "retVal", "key<PERSON><PERSON>", "shift", "Map", "fontFamily", "fontSize", "fontScale", "fontWeight", "fontStyle", "Accidental", "cautionary", "grace", "noteheadAccidentalPadding", "leftPadding", "accidentalSpacing", "Annotation", "Bend", "line", "ChordSymbol", "spacing", "subscriptOffset", "superscriptOffset", "superSubRatio", "FretHandFinger", "<PERSON><PERSON><PERSON>", "GraceTabNote", "NoteHead", "minPadding", "PedalMarking", "text", "Repetition", "offsetX", "offsetY", "coda", "segno", "Stave", "padding", "endPaddingMax", "endPaddingMin", "unalignedNotePadding", "StaveConnector", "StaveLine", "StaveSection", "StaveTempo", "glyph", "name", "StaveText", "StaveTie", "<PERSON><PERSON>", "StringNumber", "verticalPadding", "stemPadding", "rightPadding", "Stroke", "TabNote", "TabSlide", "TabStave", "<PERSON><PERSON><PERSON><PERSON>", "TextBracket", "TextNote", "Tremolo", "Tuplet", "yOffset", "textYOffset", "Volta"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/metrics.js"], "sourcesContent": ["export class Metrics {\n    static clear(key) {\n        if (key) {\n            this.cacheFont.delete(key);\n            this.cacheStyle.delete(key);\n        }\n        else {\n            this.cacheFont.clear();\n            this.cacheStyle.clear();\n        }\n    }\n    static getFontInfo(key) {\n        let font = this.cacheFont.get(key);\n        if (!font) {\n            font = {\n                family: Metrics.get(`${key}.fontFamily`),\n                size: Metrics.get(`${key}.fontSize`) * Metrics.get(`${key}.fontScale`),\n                weight: Metrics.get(`${key}.fontWeight`),\n                style: Metrics.get(`${key}.fontStyle`),\n            };\n            this.cacheFont.set(key, font);\n        }\n        return structuredClone(font);\n    }\n    static getStyle(key) {\n        let style = this.cacheStyle.get(key);\n        if (!style) {\n            style = {\n                fillStyle: Metrics.get(`${key}.fillStyle`),\n                strokeStyle: Metrics.get(`${key}.strokeStyle`),\n                lineWidth: Metrics.get(`${key}.lineWidth`),\n                lineDash: Metrics.get(`${key}.lineDash`),\n                shadowBlur: Metrics.get(`${key}.shadowBlur`),\n                shadowColor: Metrics.get(`${key}.shadowColor`),\n            };\n            this.cacheStyle.set(key, style);\n        }\n        return structuredClone(style);\n    }\n    static get(key, defaultValue) {\n        var _a;\n        const keyParts = key.split('.');\n        const lastKeyPart = keyParts.pop();\n        let curr = MetricsDefaults;\n        let retVal = defaultValue;\n        while (curr) {\n            retVal = (_a = curr[lastKeyPart]) !== null && _a !== void 0 ? _a : retVal;\n            const keyPart = keyParts.shift();\n            if (keyPart) {\n                curr = curr[keyPart];\n            }\n            else {\n                break;\n            }\n        }\n        return retVal;\n    }\n}\nMetrics.cacheStyle = new Map();\nMetrics.cacheFont = new Map();\nexport const MetricsDefaults = {\n    fontFamily: 'Bravura,Academico',\n    fontSize: 30,\n    fontScale: 1.0,\n    fontWeight: 'normal',\n    fontStyle: 'normal',\n    Accidental: {\n        cautionary: {\n            fontSize: 20,\n        },\n        grace: {\n            fontSize: 20,\n        },\n        noteheadAccidentalPadding: 1,\n        leftPadding: 2,\n        accidentalSpacing: 3,\n    },\n    Annotation: {\n        fontSize: 10,\n    },\n    Bend: {\n        fontSize: 10,\n        line: {\n            strokeStyle: '#777777',\n            lineWidth: 1,\n        },\n    },\n    ChordSymbol: {\n        fontSize: 12,\n        spacing: 0.05,\n        subscriptOffset: 0.2,\n        superscriptOffset: -0.4,\n        superSubRatio: 0.6,\n    },\n    FretHandFinger: {\n        fontSize: 9,\n        fontWeight: 'bold',\n    },\n    GraceNote: {\n        fontScale: 2 / 3,\n    },\n    GraceTabNote: {\n        fontScale: 2 / 3,\n    },\n    NoteHead: {\n        minPadding: 2,\n    },\n    PedalMarking: {\n        text: {\n            fontSize: 12,\n            fontStyle: 'italic',\n        },\n    },\n    Repetition: {\n        text: {\n            fontSize: 12,\n            fontWeight: 'bold',\n            offsetX: 12,\n            offsetY: 25,\n            spacing: 5,\n        },\n        coda: {\n            offsetY: 25,\n        },\n        segno: {\n            offsetY: 10,\n        },\n    },\n    Stave: {\n        strokeStyle: '#999999',\n        fontSize: 8,\n        padding: 12,\n        endPaddingMax: 10,\n        endPaddingMin: 5,\n        unalignedNotePadding: 10,\n    },\n    StaveConnector: {\n        text: {\n            fontSize: 16,\n        },\n    },\n    StaveLine: {\n        fontSize: 10,\n    },\n    StaveSection: {\n        fontSize: 10,\n        fontWeight: 'bold',\n        lineWidth: 2,\n        padding: 2,\n        strokeStyle: 'black',\n    },\n    StaveTempo: {\n        fontSize: 14,\n        glyph: {\n            fontSize: 25,\n        },\n        name: {\n            fontWeight: 'bold',\n        },\n    },\n    StaveText: {\n        fontSize: 16,\n    },\n    StaveTie: {\n        fontSize: 10,\n    },\n    Stem: {\n        strokeStyle: 'black',\n    },\n    StringNumber: {\n        fontSize: 10,\n        fontWeight: 'bold',\n        verticalPadding: 8,\n        stemPadding: 2,\n        leftPadding: 5,\n        rightPadding: 6,\n    },\n    Stroke: {\n        text: {\n            fontSize: 10,\n            fontStyle: 'italic',\n            fontWeight: 'bold',\n        },\n    },\n    TabNote: {\n        text: {\n            fontSize: 9,\n        },\n    },\n    TabSlide: {\n        fontSize: 10,\n        fontStyle: 'italic',\n        fontWeight: 'bold',\n    },\n    TabStave: {\n        strokeStyle: '#999999',\n        fontSize: 8,\n    },\n    TabTie: {\n        fontSize: 10,\n    },\n    TextBracket: {\n        fontSize: 15,\n        fontStyle: 'italic',\n    },\n    TextNote: {\n        text: {\n            fontSize: 12,\n        },\n    },\n    Tremolo: {\n        spacing: 7,\n    },\n    Tuplet: {\n        yOffset: 0,\n        textYOffset: 2,\n    },\n    Volta: {\n        fontSize: 9,\n        fontWeight: 'bold',\n    },\n};\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,CAAC;EACjB,OAAOC,KAAKA,CAACC,GAAG,EAAE;IACd,IAAIA,GAAG,EAAE;MACL,IAAI,CAACC,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC;MAC1B,IAAI,CAACG,UAAU,CAACD,MAAM,CAACF,GAAG,CAAC;IAC/B,CAAC,MACI;MACD,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC;MACtB,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC,CAAC;IAC3B;EACJ;EACA,OAAOK,WAAWA,CAACJ,GAAG,EAAE;IACpB,IAAIK,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACK,GAAG,CAACN,GAAG,CAAC;IAClC,IAAI,CAACK,IAAI,EAAE;MACPA,IAAI,GAAG;QACHE,MAAM,EAAET,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,aAAa,CAAC;QACxCQ,IAAI,EAAEV,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,WAAW,CAAC,GAAGF,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,YAAY,CAAC;QACtES,MAAM,EAAEX,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,aAAa,CAAC;QACxCU,KAAK,EAAEZ,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,YAAY;MACzC,CAAC;MACD,IAAI,CAACC,SAAS,CAACU,GAAG,CAACX,GAAG,EAAEK,IAAI,CAAC;IACjC;IACA,OAAOO,eAAe,CAACP,IAAI,CAAC;EAChC;EACA,OAAOQ,QAAQA,CAACb,GAAG,EAAE;IACjB,IAAIU,KAAK,GAAG,IAAI,CAACP,UAAU,CAACG,GAAG,CAACN,GAAG,CAAC;IACpC,IAAI,CAACU,KAAK,EAAE;MACRA,KAAK,GAAG;QACJI,SAAS,EAAEhB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,YAAY,CAAC;QAC1Ce,WAAW,EAAEjB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,cAAc,CAAC;QAC9CgB,SAAS,EAAElB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,YAAY,CAAC;QAC1CiB,QAAQ,EAAEnB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,WAAW,CAAC;QACxCkB,UAAU,EAAEpB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,aAAa,CAAC;QAC5CmB,WAAW,EAAErB,OAAO,CAACQ,GAAG,CAAC,GAAGN,GAAG,cAAc;MACjD,CAAC;MACD,IAAI,CAACG,UAAU,CAACQ,GAAG,CAACX,GAAG,EAAEU,KAAK,CAAC;IACnC;IACA,OAAOE,eAAe,CAACF,KAAK,CAAC;EACjC;EACA,OAAOJ,GAAGA,CAACN,GAAG,EAAEoB,YAAY,EAAE;IAC1B,IAAIC,EAAE;IACN,MAAMC,QAAQ,GAAGtB,GAAG,CAACuB,KAAK,CAAC,GAAG,CAAC;IAC/B,MAAMC,WAAW,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC;IAClC,IAAIC,IAAI,GAAGC,eAAe;IAC1B,IAAIC,MAAM,GAAGR,YAAY;IACzB,OAAOM,IAAI,EAAE;MACTE,MAAM,GAAG,CAACP,EAAE,GAAGK,IAAI,CAACF,WAAW,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGO,MAAM;MACzE,MAAMC,OAAO,GAAGP,QAAQ,CAACQ,KAAK,CAAC,CAAC;MAChC,IAAID,OAAO,EAAE;QACTH,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC;MACxB,CAAC,MACI;QACD;MACJ;IACJ;IACA,OAAOD,MAAM;EACjB;AACJ;AACA9B,OAAO,CAACK,UAAU,GAAG,IAAI4B,GAAG,CAAC,CAAC;AAC9BjC,OAAO,CAACG,SAAS,GAAG,IAAI8B,GAAG,CAAC,CAAC;AAC7B,OAAO,MAAMJ,eAAe,GAAG;EAC3BK,UAAU,EAAE,mBAAmB;EAC/BC,QAAQ,EAAE,EAAE;EACZC,SAAS,EAAE,GAAG;EACdC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,QAAQ;EACnBC,UAAU,EAAE;IACRC,UAAU,EAAE;MACRL,QAAQ,EAAE;IACd,CAAC;IACDM,KAAK,EAAE;MACHN,QAAQ,EAAE;IACd,CAAC;IACDO,yBAAyB,EAAE,CAAC;IAC5BC,WAAW,EAAE,CAAC;IACdC,iBAAiB,EAAE;EACvB,CAAC;EACDC,UAAU,EAAE;IACRV,QAAQ,EAAE;EACd,CAAC;EACDW,IAAI,EAAE;IACFX,QAAQ,EAAE,EAAE;IACZY,IAAI,EAAE;MACF9B,WAAW,EAAE,SAAS;MACtBC,SAAS,EAAE;IACf;EACJ,CAAC;EACD8B,WAAW,EAAE;IACTb,QAAQ,EAAE,EAAE;IACZc,OAAO,EAAE,IAAI;IACbC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,CAAC,GAAG;IACvBC,aAAa,EAAE;EACnB,CAAC;EACDC,cAAc,EAAE;IACZlB,QAAQ,EAAE,CAAC;IACXE,UAAU,EAAE;EAChB,CAAC;EACDiB,SAAS,EAAE;IACPlB,SAAS,EAAE,CAAC,GAAG;EACnB,CAAC;EACDmB,YAAY,EAAE;IACVnB,SAAS,EAAE,CAAC,GAAG;EACnB,CAAC;EACDoB,QAAQ,EAAE;IACNC,UAAU,EAAE;EAChB,CAAC;EACDC,YAAY,EAAE;IACVC,IAAI,EAAE;MACFxB,QAAQ,EAAE,EAAE;MACZG,SAAS,EAAE;IACf;EACJ,CAAC;EACDsB,UAAU,EAAE;IACRD,IAAI,EAAE;MACFxB,QAAQ,EAAE,EAAE;MACZE,UAAU,EAAE,MAAM;MAClBwB,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXb,OAAO,EAAE;IACb,CAAC;IACDc,IAAI,EAAE;MACFD,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHF,OAAO,EAAE;IACb;EACJ,CAAC;EACDG,KAAK,EAAE;IACHhD,WAAW,EAAE,SAAS;IACtBkB,QAAQ,EAAE,CAAC;IACX+B,OAAO,EAAE,EAAE;IACXC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,CAAC;IAChBC,oBAAoB,EAAE;EAC1B,CAAC;EACDC,cAAc,EAAE;IACZX,IAAI,EAAE;MACFxB,QAAQ,EAAE;IACd;EACJ,CAAC;EACDoC,SAAS,EAAE;IACPpC,QAAQ,EAAE;EACd,CAAC;EACDqC,YAAY,EAAE;IACVrC,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,MAAM;IAClBnB,SAAS,EAAE,CAAC;IACZgD,OAAO,EAAE,CAAC;IACVjD,WAAW,EAAE;EACjB,CAAC;EACDwD,UAAU,EAAE;IACRtC,QAAQ,EAAE,EAAE;IACZuC,KAAK,EAAE;MACHvC,QAAQ,EAAE;IACd,CAAC;IACDwC,IAAI,EAAE;MACFtC,UAAU,EAAE;IAChB;EACJ,CAAC;EACDuC,SAAS,EAAE;IACPzC,QAAQ,EAAE;EACd,CAAC;EACD0C,QAAQ,EAAE;IACN1C,QAAQ,EAAE;EACd,CAAC;EACD2C,IAAI,EAAE;IACF7D,WAAW,EAAE;EACjB,CAAC;EACD8D,YAAY,EAAE;IACV5C,QAAQ,EAAE,EAAE;IACZE,UAAU,EAAE,MAAM;IAClB2C,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdtC,WAAW,EAAE,CAAC;IACduC,YAAY,EAAE;EAClB,CAAC;EACDC,MAAM,EAAE;IACJxB,IAAI,EAAE;MACFxB,QAAQ,EAAE,EAAE;MACZG,SAAS,EAAE,QAAQ;MACnBD,UAAU,EAAE;IAChB;EACJ,CAAC;EACD+C,OAAO,EAAE;IACLzB,IAAI,EAAE;MACFxB,QAAQ,EAAE;IACd;EACJ,CAAC;EACDkD,QAAQ,EAAE;IACNlD,QAAQ,EAAE,EAAE;IACZG,SAAS,EAAE,QAAQ;IACnBD,UAAU,EAAE;EAChB,CAAC;EACDiD,QAAQ,EAAE;IACNrE,WAAW,EAAE,SAAS;IACtBkB,QAAQ,EAAE;EACd,CAAC;EACDoD,MAAM,EAAE;IACJpD,QAAQ,EAAE;EACd,CAAC;EACDqD,WAAW,EAAE;IACTrD,QAAQ,EAAE,EAAE;IACZG,SAAS,EAAE;EACf,CAAC;EACDmD,QAAQ,EAAE;IACN9B,IAAI,EAAE;MACFxB,QAAQ,EAAE;IACd;EACJ,CAAC;EACDuD,OAAO,EAAE;IACLzC,OAAO,EAAE;EACb,CAAC;EACD0C,MAAM,EAAE;IACJC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE;EACjB,CAAC;EACDC,KAAK,EAAE;IACH3D,QAAQ,EAAE,CAAC;IACXE,UAAU,EAAE;EAChB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}