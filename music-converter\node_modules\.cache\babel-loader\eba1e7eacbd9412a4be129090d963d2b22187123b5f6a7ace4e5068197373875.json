{"ast": null, "code": "import { BoundingBox } from './boundingbox.js';\nimport { Clef } from './clef.js';\nimport { Element } from './element.js';\nimport { KeySignature } from './keysignature.js';\nimport { Metrics } from './metrics.js';\nimport { Barline, BarlineType } from './stavebarline.js';\nimport { StaveModifierPosition } from './stavemodifier.js';\nimport { Repetition } from './staverepetition.js';\nimport { StaveSection } from './stavesection.js';\nimport { StaveTempo } from './stavetempo.js';\nimport { StaveText } from './stavetext.js';\nimport { Volta } from './stavevolta.js';\nimport { Tables } from './tables.js';\nimport { TimeSignature } from './timesignature.js';\nimport { isBarline } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nconst SORT_ORDER_BEG_MODIFIERS = {\n  [Barline.CATEGORY]: 0,\n  [Clef.CATEGORY]: 1,\n  [KeySignature.CATEGORY]: 2,\n  [TimeSignature.CATEGORY]: 3\n};\nconst SORT_ORDER_END_MODIFIERS = {\n  [TimeSignature.CATEGORY]: 0,\n  [KeySignature.CATEGORY]: 1,\n  [Barline.CATEGORY]: 2,\n  [Clef.CATEGORY]: 3\n};\nexport class Stave extends Element {\n  static get CATEGORY() {\n    return \"Stave\";\n  }\n  static get defaultPadding() {\n    return Metrics.get('Stave.padding') + Metrics.get('Stave.endPaddingMax');\n  }\n  static get rightPadding() {\n    return Metrics.get('Stave.endPaddingMax');\n  }\n  constructor(x, y, width, options) {\n    super();\n    this.x = x;\n    this.y = y;\n    this.width = width;\n    this.formatted = false;\n    this.startX = x + 5;\n    this.endX = x + width;\n    this.modifiers = [];\n    this.measure = 0;\n    this.clef = 'treble';\n    this.endClef = undefined;\n    this.options = Object.assign({\n      verticalBarWidth: 10,\n      numLines: 5,\n      leftBar: true,\n      rightBar: true,\n      spacingBetweenLinesPx: Tables.STAVE_LINE_DISTANCE,\n      spaceAboveStaffLn: 4,\n      spaceBelowStaffLn: 4,\n      topTextPosition: 1,\n      bottomTextPosition: 4,\n      lineConfig: []\n    }, options);\n    this.bounds = {\n      x: this.x,\n      y: this.y,\n      w: this.width,\n      h: 0\n    };\n    this.defaultLedgerLineStyle = {\n      strokeStyle: '#444',\n      lineWidth: 2\n    };\n    this.resetLines();\n    this.addModifier(new Barline(this.options.leftBar ? BarlineType.SINGLE : BarlineType.NONE));\n    this.addEndModifier(new Barline(this.options.rightBar ? BarlineType.SINGLE : BarlineType.NONE));\n  }\n  setDefaultLedgerLineStyle(style) {\n    this.defaultLedgerLineStyle = style;\n  }\n  getDefaultLedgerLineStyle() {\n    return Object.assign(Object.assign({}, this.getStyle()), this.defaultLedgerLineStyle);\n  }\n  space(spacing) {\n    return this.options.spacingBetweenLinesPx * spacing;\n  }\n  resetLines() {\n    this.options.lineConfig = [];\n    for (let i = 0; i < this.options.numLines; i++) {\n      this.options.lineConfig.push({\n        visible: true\n      });\n    }\n    this.height = (this.options.numLines + this.options.spaceAboveStaffLn) * this.options.spacingBetweenLinesPx;\n    this.options.bottomTextPosition = this.options.numLines;\n  }\n  setNoteStartX(x) {\n    if (!this.formatted) this.format();\n    this.startX = x;\n    return this;\n  }\n  getNoteStartX() {\n    if (!this.formatted) this.format();\n    return this.startX;\n  }\n  getNoteEndX() {\n    if (!this.formatted) this.format();\n    return this.endX;\n  }\n  getTieStartX() {\n    return this.startX;\n  }\n  getTieEndX() {\n    return this.endX;\n  }\n  getNumLines() {\n    return this.options.numLines;\n  }\n  setNumLines(n) {\n    this.options.numLines = n;\n    this.resetLines();\n    return this;\n  }\n  getTopLineTopY() {\n    return this.getYForLine(0);\n  }\n  getBottomLineBottomY() {\n    var _a;\n    return this.getYForLine(this.getNumLines() - 1) + ((_a = this.getStyle().lineWidth) !== null && _a !== void 0 ? _a : 1);\n  }\n  setX(x) {\n    const shift = x - this.x;\n    this.formatted = false;\n    this.x = x;\n    this.startX += shift;\n    this.endX += shift;\n    for (let i = 0; i < this.modifiers.length; i++) {\n      const mod = this.modifiers[i];\n      mod.setX(mod.getX() + shift);\n    }\n    return this;\n  }\n  setWidth(width) {\n    this.formatted = false;\n    this.width = width;\n    this.endX = this.x + width;\n    return this;\n  }\n  setMeasure(measure) {\n    this.measure = measure;\n    return this;\n  }\n  getMeasure() {\n    return this.measure;\n  }\n  getModifierXShift(index = 0) {\n    if (typeof index !== 'number') {\n      throw new RuntimeError('InvalidIndex', 'Must be of number type');\n    }\n    if (!this.formatted) this.format();\n    if (this.getModifiers(StaveModifierPosition.BEGIN).length === 1) {\n      return 0;\n    }\n    if (this.modifiers[index].getPosition() === StaveModifierPosition.RIGHT) {\n      return 0;\n    }\n    let startX = this.startX - this.x;\n    const begBarline = this.modifiers[0];\n    if (begBarline.getType() === BarlineType.REPEAT_BEGIN && startX > begBarline.getWidth()) {\n      startX -= begBarline.getWidth();\n    }\n    return startX;\n  }\n  setRepetitionType(type, yShift = 0) {\n    this.modifiers.push(new Repetition(type, this.x, yShift));\n    return this;\n  }\n  setVoltaType(type, label, y) {\n    this.modifiers.push(new Volta(type, label, this.x, y));\n    return this;\n  }\n  setSection(section, y, xOffset = 0, fontSize, drawRect = true) {\n    const staveSection = new StaveSection(section).setYShift(y).setXShift(xOffset).setDrawRect(drawRect);\n    if (fontSize) {\n      staveSection.setFontSize(fontSize);\n    }\n    this.addModifier(staveSection);\n    return this;\n  }\n  setTempo(tempo, y) {\n    this.modifiers.push(new StaveTempo(tempo, this.x, y));\n    return this;\n  }\n  setStaveText(text, position, options = {}) {\n    this.modifiers.push(new StaveText(text, position, options));\n    return this;\n  }\n  getSpacingBetweenLines() {\n    return this.options.spacingBetweenLinesPx;\n  }\n  getBoundingBox() {\n    return new BoundingBox(this.x, this.y, this.width, this.getBottomY() - this.y);\n  }\n  getBottomY() {\n    const options = this.options;\n    const spacing = options.spacingBetweenLinesPx;\n    const scoreBottom = this.getYForLine(options.numLines) + options.spaceBelowStaffLn * spacing;\n    return scoreBottom;\n  }\n  getBottomLineY() {\n    return this.getYForLine(this.options.numLines);\n  }\n  getYForLine(line) {\n    const options = this.options;\n    const spacing = options.spacingBetweenLinesPx;\n    const headroom = options.spaceAboveStaffLn;\n    const y = this.y + line * spacing + headroom * spacing;\n    return y;\n  }\n  getLineForY(y) {\n    const options = this.options;\n    const spacing = options.spacingBetweenLinesPx;\n    const headroom = options.spaceAboveStaffLn;\n    return (y - this.y) / spacing - headroom;\n  }\n  getYForTopText(line = 0) {\n    return this.getYForLine(-line - this.options.topTextPosition);\n  }\n  getYForBottomText(line = 0) {\n    return this.getYForLine(this.options.bottomTextPosition + line);\n  }\n  getYForNote(line) {\n    const options = this.options;\n    const spacing = options.spacingBetweenLinesPx;\n    const headroom = options.spaceAboveStaffLn;\n    return this.y + headroom * spacing + 5 * spacing - line * spacing;\n  }\n  getYForGlyphs() {\n    return this.getYForLine(3);\n  }\n  addModifier(modifier, position) {\n    if (position !== undefined) {\n      modifier.setPosition(position);\n    }\n    modifier.setStave(this);\n    this.formatted = false;\n    this.modifiers.push(modifier);\n    return this;\n  }\n  addEndModifier(modifier) {\n    this.addModifier(modifier, StaveModifierPosition.END);\n    return this;\n  }\n  setBegBarType(type) {\n    const {\n      SINGLE,\n      REPEAT_BEGIN,\n      NONE\n    } = BarlineType;\n    if (type === SINGLE || type === REPEAT_BEGIN || type === NONE) {\n      this.modifiers[0].setType(type);\n      this.formatted = false;\n    }\n    return this;\n  }\n  setEndBarType(type) {\n    if (type !== BarlineType.REPEAT_BEGIN) {\n      this.modifiers[1].setType(type);\n      this.formatted = false;\n    }\n    return this;\n  }\n  setClef(clefSpec, size, annotation, position) {\n    if (position === undefined) {\n      position = StaveModifierPosition.BEGIN;\n    }\n    if (position === StaveModifierPosition.END) {\n      this.endClef = clefSpec;\n    } else {\n      this.clef = clefSpec;\n    }\n    const clefs = this.getModifiers(position, Clef.CATEGORY);\n    if (clefs.length === 0) {\n      this.addClef(clefSpec, size, annotation, position);\n    } else {\n      clefs[0].setType(clefSpec, size, annotation);\n    }\n    return this;\n  }\n  getClef() {\n    return this.clef;\n  }\n  setEndClef(clefSpec, size, annotation) {\n    this.setClef(clefSpec, size, annotation, StaveModifierPosition.END);\n    return this;\n  }\n  getEndClef() {\n    return this.endClef;\n  }\n  setKeySignature(keySpec, cancelKeySpec, position) {\n    if (position === undefined) {\n      position = StaveModifierPosition.BEGIN;\n    }\n    const keySignatures = this.getModifiers(position, KeySignature.CATEGORY);\n    if (keySignatures.length === 0) {\n      this.addKeySignature(keySpec, cancelKeySpec, position);\n    } else {\n      keySignatures[0].setKeySig(keySpec, cancelKeySpec);\n    }\n    return this;\n  }\n  setEndKeySignature(keySpec, cancelKeySpec) {\n    this.setKeySignature(keySpec, cancelKeySpec, StaveModifierPosition.END);\n    return this;\n  }\n  setTimeSignature(timeSpec, customPadding, position) {\n    if (position === undefined) {\n      position = StaveModifierPosition.BEGIN;\n    }\n    const timeSignatures = this.getModifiers(position, TimeSignature.CATEGORY);\n    if (timeSignatures.length === 0) {\n      this.addTimeSignature(timeSpec, customPadding, position);\n    } else {\n      timeSignatures[0].setTimeSig(timeSpec);\n    }\n    return this;\n  }\n  setEndTimeSignature(timeSpec, customPadding) {\n    this.setTimeSignature(timeSpec, customPadding, StaveModifierPosition.END);\n    return this;\n  }\n  addKeySignature(keySpec, cancelKeySpec, position) {\n    if (position === undefined) {\n      position = StaveModifierPosition.BEGIN;\n    }\n    this.addModifier(new KeySignature(keySpec, cancelKeySpec).setPosition(position), position);\n    return this;\n  }\n  addClef(clef, size, annotation, position) {\n    if (position === undefined || position === StaveModifierPosition.BEGIN) {\n      this.clef = clef;\n    } else if (position === StaveModifierPosition.END) {\n      this.endClef = clef;\n    }\n    this.addModifier(new Clef(clef, size, annotation), position);\n    return this;\n  }\n  addEndClef(clef, size, annotation) {\n    this.addClef(clef, size, annotation, StaveModifierPosition.END);\n    return this;\n  }\n  addTimeSignature(timeSpec, customPadding, position) {\n    this.addModifier(new TimeSignature(timeSpec, customPadding), position);\n    return this;\n  }\n  addEndTimeSignature(timeSpec, customPadding) {\n    this.addTimeSignature(timeSpec, customPadding, StaveModifierPosition.END);\n    return this;\n  }\n  addTrebleGlyph() {\n    this.addClef('treble');\n    return this;\n  }\n  getModifiers(position, category) {\n    const noPosition = position === undefined;\n    const noCategory = category === undefined;\n    if (noPosition && noCategory) {\n      return this.modifiers;\n    } else if (noPosition) {\n      return this.modifiers.filter(m => category === m.getCategory());\n    } else if (noCategory) {\n      return this.modifiers.filter(m => position === m.getPosition());\n    } else {\n      return this.modifiers.filter(m => position === m.getPosition() && category === m.getCategory());\n    }\n  }\n  sortByCategory(items, order) {\n    for (let i = items.length - 1; i >= 0; i--) {\n      for (let j = 0; j < i; j++) {\n        if (order[items[j].getCategory()] > order[items[j + 1].getCategory()]) {\n          const temp = items[j];\n          items[j] = items[j + 1];\n          items[j + 1] = temp;\n        }\n      }\n    }\n  }\n  format() {\n    var _a, _b, _c, _d;\n    const begBarline = this.modifiers[0];\n    const endBarline = this.modifiers[1];\n    const begModifiers = this.getModifiers(StaveModifierPosition.BEGIN);\n    const endModifiers = this.getModifiers(StaveModifierPosition.END);\n    this.sortByCategory(begModifiers, SORT_ORDER_BEG_MODIFIERS);\n    this.sortByCategory(endModifiers, SORT_ORDER_END_MODIFIERS);\n    if (begModifiers.length > 1 && begBarline.getType() === BarlineType.REPEAT_BEGIN) {\n      begModifiers.push(begModifiers.splice(0, 1)[0]);\n      begModifiers.splice(0, 0, new Barline(BarlineType.SINGLE));\n    }\n    if (endModifiers.indexOf(endBarline) > 0) {\n      endModifiers.splice(0, 0, new Barline(BarlineType.NONE));\n    }\n    let width;\n    let padding;\n    let modifier;\n    let offset = 0;\n    let x = this.x;\n    for (let i = 0; i < begModifiers.length; i++) {\n      modifier = begModifiers[i];\n      padding = modifier.getPadding(i + offset);\n      width = modifier.getWidth();\n      x += padding;\n      modifier.setX(x);\n      x += width;\n      if (padding + width === 0) offset--;\n    }\n    this.startX = x;\n    x = this.x + this.width;\n    const widths = {\n      left: 0,\n      right: 0,\n      paddingRight: 0,\n      paddingLeft: 0\n    };\n    let lastBarlineIdx = 0;\n    for (let i = 0; i < endModifiers.length; i++) {\n      modifier = endModifiers[i];\n      lastBarlineIdx = isBarline(modifier) ? i : lastBarlineIdx;\n      widths.right = 0;\n      widths.left = 0;\n      widths.paddingRight = 0;\n      widths.paddingLeft = 0;\n      const layoutMetrics = modifier.getLayoutMetrics();\n      if (layoutMetrics) {\n        if (i !== 0) {\n          widths.right = (_a = layoutMetrics.xMax) !== null && _a !== void 0 ? _a : 0;\n          widths.paddingRight = (_b = layoutMetrics.paddingRight) !== null && _b !== void 0 ? _b : 0;\n        }\n        widths.left = -((_c = layoutMetrics.xMin) !== null && _c !== void 0 ? _c : 0);\n        widths.paddingLeft = (_d = layoutMetrics.paddingLeft) !== null && _d !== void 0 ? _d : 0;\n        if (i === endModifiers.length - 1) {\n          widths.paddingLeft = 0;\n        }\n      } else {\n        widths.paddingRight = modifier.getPadding(i - lastBarlineIdx);\n        if (i !== 0) {\n          widths.right = modifier.getWidth();\n        }\n        if (i === 0) {\n          widths.left = modifier.getWidth();\n        }\n      }\n      x -= widths.paddingRight;\n      x -= widths.right;\n      modifier.setX(x);\n      x -= widths.left;\n      x -= widths.paddingLeft;\n    }\n    this.endX = endModifiers.length === 1 ? this.x + this.width : x;\n    this.formatted = true;\n  }\n  draw() {\n    var _a;\n    const ctx = this.checkContext();\n    this.setRendered();\n    ctx.openGroup('stave', this.getAttribute('id'));\n    if (!this.formatted) this.format();\n    const numLines = this.options.numLines;\n    const width = this.width;\n    const x = this.x;\n    let y;\n    const lineWidth = (_a = this.getStyle().lineWidth) !== null && _a !== void 0 ? _a : 1;\n    const lineWidthCorrection = lineWidth % 2 === 0 ? 0 : 0.5;\n    for (let line = 0; line < numLines; line++) {\n      y = this.getYForLine(line);\n      if (this.options.lineConfig[line].visible) {\n        ctx.beginPath();\n        ctx.moveTo(x, y + lineWidthCorrection);\n        ctx.lineTo(x + width, y + lineWidthCorrection);\n        ctx.stroke();\n      }\n    }\n    ctx.closeGroup();\n    for (let i = 0; i < this.modifiers.length; i++) {\n      const modifier = this.modifiers[i];\n      modifier.setContext(ctx);\n      modifier.setStave(this);\n      modifier.drawWithStyle();\n    }\n    if (this.measure > 0) {\n      ctx.setFont(this.fontInfo);\n      const textWidth = ctx.measureText('' + this.measure).width;\n      y = this.getYForTopText(0) + 3;\n      ctx.fillText('' + this.measure, this.x - textWidth / 2, y);\n    }\n  }\n  getVerticalBarWidth() {\n    return this.options.verticalBarWidth;\n  }\n  getConfigForLines() {\n    return this.options.lineConfig;\n  }\n  setConfigForLine(lineNumber, lineConfig) {\n    if (lineNumber >= this.options.numLines || lineNumber < 0) {\n      throw new RuntimeError('StaveConfigError', 'The line number must be within the range of the number of lines in the Stave.');\n    }\n    if (lineConfig.visible === undefined) {\n      throw new RuntimeError('StaveConfigError', \"The line configuration object is missing the 'visible' property.\");\n    }\n    if (typeof lineConfig.visible !== 'boolean') {\n      throw new RuntimeError('StaveConfigError', \"The line configuration objects 'visible' property must be true or false.\");\n    }\n    this.options.lineConfig[lineNumber] = lineConfig;\n    return this;\n  }\n  setConfigForLines(linesConfiguration) {\n    if (linesConfiguration.length !== this.options.numLines) {\n      throw new RuntimeError('StaveConfigError', 'The length of the lines configuration array must match the number of lines in the Stave');\n    }\n    for (const lineConfig in linesConfiguration) {\n      if (linesConfiguration[lineConfig].visible === undefined) {\n        linesConfiguration[lineConfig] = this.options.lineConfig[lineConfig];\n      }\n      this.options.lineConfig[lineConfig] = Object.assign(Object.assign({}, this.options.lineConfig[lineConfig]), linesConfiguration[lineConfig]);\n    }\n    this.options.lineConfig = linesConfiguration;\n    return this;\n  }\n  static formatBegModifiers(staves) {\n    const adjustCategoryStartX = category => {\n      let minStartX = 0;\n      staves.forEach(stave => {\n        const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, category);\n        if (modifiers.length > 0 && modifiers[0].getX() > minStartX) minStartX = modifiers[0].getX();\n      });\n      let adjustX = 0;\n      staves.forEach(stave => {\n        adjustX = 0;\n        const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, category);\n        modifiers.forEach(modifier => {\n          if (minStartX - modifier.getX() > adjustX) adjustX = minStartX - modifier.getX();\n        });\n        const allModifiers = stave.getModifiers(StaveModifierPosition.BEGIN);\n        let bAdjust = false;\n        allModifiers.forEach(modifier => {\n          if (modifier.getCategory() === category) bAdjust = true;\n          if (bAdjust && adjustX > 0) modifier.setX(modifier.getX() + adjustX);\n        });\n        stave.setNoteStartX(stave.getNoteStartX() + adjustX);\n      });\n    };\n    staves.forEach(stave => {\n      if (!stave.formatted) stave.format();\n    });\n    adjustCategoryStartX(\"Clef\");\n    adjustCategoryStartX(\"KeySignature\");\n    adjustCategoryStartX(\"TimeSignature\");\n    let maxX = 0;\n    staves.forEach(stave => {\n      if (stave.getNoteStartX() > maxX) maxX = stave.getNoteStartX();\n    });\n    staves.forEach(stave => {\n      stave.setNoteStartX(maxX);\n    });\n    maxX = 0;\n    staves.forEach(stave => {\n      const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, \"Barline\");\n      modifiers.forEach(modifier => {\n        if (modifier.getType() === BarlineType.REPEAT_BEGIN) if (modifier.getX() > maxX) maxX = modifier.getX();\n      });\n    });\n    staves.forEach(stave => {\n      const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, \"Barline\");\n      modifiers.forEach(modifier => {\n        if (modifier.getType() === BarlineType.REPEAT_BEGIN) modifier.setX(maxX);\n      });\n    });\n  }\n}", "map": {"version": 3, "names": ["BoundingBox", "<PERSON><PERSON><PERSON>", "Element", "KeySignature", "Metrics", "Barline", "BarlineType", "StaveModifierPosition", "Repetition", "StaveSection", "StaveTempo", "StaveText", "Volta", "Tables", "TimeSignature", "isBarline", "RuntimeError", "SORT_ORDER_BEG_MODIFIERS", "CATEGORY", "SORT_ORDER_END_MODIFIERS", "Stave", "defaultPadding", "get", "rightPadding", "constructor", "x", "y", "width", "options", "formatted", "startX", "endX", "modifiers", "measure", "clef", "endClef", "undefined", "Object", "assign", "verticalBar<PERSON>idth", "numLines", "leftBar", "rightBar", "spacingBetweenLinesPx", "STAVE_LINE_DISTANCE", "spaceAboveStaffLn", "spaceBelowStaffLn", "topTextPosition", "bottomTextPosition", "lineConfig", "bounds", "w", "h", "defaultLedgerLineStyle", "strokeStyle", "lineWidth", "resetLines", "addModifier", "SINGLE", "NONE", "addEndModifier", "setDefaultLedgerLineStyle", "style", "getDefaultLedgerLineStyle", "getStyle", "space", "spacing", "i", "push", "visible", "height", "setNoteStartX", "format", "getNoteStartX", "getNoteEndX", "getTieStartX", "getTieEndX", "getNumLines", "setNumLines", "n", "getTopLineTopY", "getYForLine", "getBottomLineBottomY", "_a", "setX", "shift", "length", "mod", "getX", "<PERSON><PERSON><PERSON><PERSON>", "setMeasure", "getMeasure", "getModifierXShift", "index", "getModifiers", "BEGIN", "getPosition", "RIGHT", "beg<PERSON><PERSON><PERSON>", "getType", "REPEAT_BEGIN", "getWidth", "setRepetitionType", "type", "yShift", "setVoltaType", "label", "setSection", "section", "xOffset", "fontSize", "drawRect", "staveSection", "setYShift", "setXShift", "setDrawRect", "setFontSize", "set<PERSON><PERSON><PERSON>", "tempo", "setStaveText", "text", "position", "getSpacingBetweenLines", "getBoundingBox", "getBottomY", "scoreBottom", "getBottomLineY", "line", "headroom", "getLineForY", "getYForTopText", "getYForBottomText", "getYForNote", "getYForGlyphs", "modifier", "setPosition", "setStave", "END", "setBegBarType", "setType", "setEndBarType", "set<PERSON>lef", "clefSpec", "size", "annotation", "clefs", "addClef", "getClef", "setEndClef", "getEndClef", "setKeySignature", "keySpec", "cancelKeySpec", "keySignatures", "addKeySignature", "setKeySig", "setEndKeySignature", "setTimeSignature", "timeSpec", "customPadding", "timeSignatures", "addTimeSignature", "setTimeSig", "setEndTimeSignature", "addEndClef", "addEndTimeSignature", "addTrebleGlyph", "category", "noPosition", "noCategory", "filter", "m", "getCategory", "sortByCategory", "items", "order", "j", "temp", "_b", "_c", "_d", "endBarline", "begModifiers", "endModifiers", "splice", "indexOf", "padding", "offset", "getPadding", "widths", "left", "right", "paddingRight", "paddingLeft", "lastBarlineIdx", "layoutMetrics", "getLayoutMetrics", "xMax", "xMin", "draw", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "lineWidthCorrection", "beginPath", "moveTo", "lineTo", "stroke", "closeGroup", "setContext", "drawWithStyle", "setFont", "fontInfo", "textWidth", "measureText", "fillText", "getVerticalBarWidth", "getConfigForLines", "setConfigForLine", "lineNumber", "setConfigForLines", "linesConfiguration", "formatBegModifiers", "staves", "adjustCategoryStartX", "minStartX", "for<PERSON>ach", "stave", "adjustX", "allModifiers", "bAdju<PERSON>", "maxX"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stave.js"], "sourcesContent": ["import { BoundingBox } from './boundingbox.js';\nimport { Clef } from './clef.js';\nimport { Element } from './element.js';\nimport { KeySignature } from './keysignature.js';\nimport { Metrics } from './metrics.js';\nimport { Barline, BarlineType } from './stavebarline.js';\nimport { StaveModifierPosition } from './stavemodifier.js';\nimport { Repetition } from './staverepetition.js';\nimport { StaveSection } from './stavesection.js';\nimport { StaveTempo } from './stavetempo.js';\nimport { StaveText } from './stavetext.js';\nimport { Volta } from './stavevolta.js';\nimport { Tables } from './tables.js';\nimport { TimeSignature } from './timesignature.js';\nimport { isBarline } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nconst SORT_ORDER_BEG_MODIFIERS = {\n    [Barline.CATEGORY]: 0,\n    [Clef.CATEGORY]: 1,\n    [KeySignature.CATEGORY]: 2,\n    [TimeSignature.CATEGORY]: 3,\n};\nconst SORT_ORDER_END_MODIFIERS = {\n    [TimeSignature.CATEGORY]: 0,\n    [KeySignature.CATEGORY]: 1,\n    [Barline.CATEGORY]: 2,\n    [Clef.CATEGORY]: 3,\n};\nexport class Stave extends Element {\n    static get CATEGORY() {\n        return \"Stave\";\n    }\n    static get defaultPadding() {\n        return Metrics.get('Stave.padding') + Metrics.get('Stave.endPaddingMax');\n    }\n    static get rightPadding() {\n        return Metrics.get('Stave.endPaddingMax');\n    }\n    constructor(x, y, width, options) {\n        super();\n        this.x = x;\n        this.y = y;\n        this.width = width;\n        this.formatted = false;\n        this.startX = x + 5;\n        this.endX = x + width;\n        this.modifiers = [];\n        this.measure = 0;\n        this.clef = 'treble';\n        this.endClef = undefined;\n        this.options = Object.assign({ verticalBarWidth: 10, numLines: 5, leftBar: true, rightBar: true, spacingBetweenLinesPx: Tables.STAVE_LINE_DISTANCE, spaceAboveStaffLn: 4, spaceBelowStaffLn: 4, topTextPosition: 1, bottomTextPosition: 4, lineConfig: [] }, options);\n        this.bounds = { x: this.x, y: this.y, w: this.width, h: 0 };\n        this.defaultLedgerLineStyle = { strokeStyle: '#444', lineWidth: 2 };\n        this.resetLines();\n        this.addModifier(new Barline(this.options.leftBar ? BarlineType.SINGLE : BarlineType.NONE));\n        this.addEndModifier(new Barline(this.options.rightBar ? BarlineType.SINGLE : BarlineType.NONE));\n    }\n    setDefaultLedgerLineStyle(style) {\n        this.defaultLedgerLineStyle = style;\n    }\n    getDefaultLedgerLineStyle() {\n        return Object.assign(Object.assign({}, this.getStyle()), this.defaultLedgerLineStyle);\n    }\n    space(spacing) {\n        return this.options.spacingBetweenLinesPx * spacing;\n    }\n    resetLines() {\n        this.options.lineConfig = [];\n        for (let i = 0; i < this.options.numLines; i++) {\n            this.options.lineConfig.push({ visible: true });\n        }\n        this.height = (this.options.numLines + this.options.spaceAboveStaffLn) * this.options.spacingBetweenLinesPx;\n        this.options.bottomTextPosition = this.options.numLines;\n    }\n    setNoteStartX(x) {\n        if (!this.formatted)\n            this.format();\n        this.startX = x;\n        return this;\n    }\n    getNoteStartX() {\n        if (!this.formatted)\n            this.format();\n        return this.startX;\n    }\n    getNoteEndX() {\n        if (!this.formatted)\n            this.format();\n        return this.endX;\n    }\n    getTieStartX() {\n        return this.startX;\n    }\n    getTieEndX() {\n        return this.endX;\n    }\n    getNumLines() {\n        return this.options.numLines;\n    }\n    setNumLines(n) {\n        this.options.numLines = n;\n        this.resetLines();\n        return this;\n    }\n    getTopLineTopY() {\n        return this.getYForLine(0);\n    }\n    getBottomLineBottomY() {\n        var _a;\n        return this.getYForLine(this.getNumLines() - 1) + ((_a = this.getStyle().lineWidth) !== null && _a !== void 0 ? _a : 1);\n    }\n    setX(x) {\n        const shift = x - this.x;\n        this.formatted = false;\n        this.x = x;\n        this.startX += shift;\n        this.endX += shift;\n        for (let i = 0; i < this.modifiers.length; i++) {\n            const mod = this.modifiers[i];\n            mod.setX(mod.getX() + shift);\n        }\n        return this;\n    }\n    setWidth(width) {\n        this.formatted = false;\n        this.width = width;\n        this.endX = this.x + width;\n        return this;\n    }\n    setMeasure(measure) {\n        this.measure = measure;\n        return this;\n    }\n    getMeasure() {\n        return this.measure;\n    }\n    getModifierXShift(index = 0) {\n        if (typeof index !== 'number') {\n            throw new RuntimeError('InvalidIndex', 'Must be of number type');\n        }\n        if (!this.formatted)\n            this.format();\n        if (this.getModifiers(StaveModifierPosition.BEGIN).length === 1) {\n            return 0;\n        }\n        if (this.modifiers[index].getPosition() === StaveModifierPosition.RIGHT) {\n            return 0;\n        }\n        let startX = this.startX - this.x;\n        const begBarline = this.modifiers[0];\n        if (begBarline.getType() === BarlineType.REPEAT_BEGIN && startX > begBarline.getWidth()) {\n            startX -= begBarline.getWidth();\n        }\n        return startX;\n    }\n    setRepetitionType(type, yShift = 0) {\n        this.modifiers.push(new Repetition(type, this.x, yShift));\n        return this;\n    }\n    setVoltaType(type, label, y) {\n        this.modifiers.push(new Volta(type, label, this.x, y));\n        return this;\n    }\n    setSection(section, y, xOffset = 0, fontSize, drawRect = true) {\n        const staveSection = new StaveSection(section).setYShift(y).setXShift(xOffset).setDrawRect(drawRect);\n        if (fontSize) {\n            staveSection.setFontSize(fontSize);\n        }\n        this.addModifier(staveSection);\n        return this;\n    }\n    setTempo(tempo, y) {\n        this.modifiers.push(new StaveTempo(tempo, this.x, y));\n        return this;\n    }\n    setStaveText(text, position, options = {}) {\n        this.modifiers.push(new StaveText(text, position, options));\n        return this;\n    }\n    getSpacingBetweenLines() {\n        return this.options.spacingBetweenLinesPx;\n    }\n    getBoundingBox() {\n        return new BoundingBox(this.x, this.y, this.width, this.getBottomY() - this.y);\n    }\n    getBottomY() {\n        const options = this.options;\n        const spacing = options.spacingBetweenLinesPx;\n        const scoreBottom = this.getYForLine(options.numLines) + options.spaceBelowStaffLn * spacing;\n        return scoreBottom;\n    }\n    getBottomLineY() {\n        return this.getYForLine(this.options.numLines);\n    }\n    getYForLine(line) {\n        const options = this.options;\n        const spacing = options.spacingBetweenLinesPx;\n        const headroom = options.spaceAboveStaffLn;\n        const y = this.y + line * spacing + headroom * spacing;\n        return y;\n    }\n    getLineForY(y) {\n        const options = this.options;\n        const spacing = options.spacingBetweenLinesPx;\n        const headroom = options.spaceAboveStaffLn;\n        return (y - this.y) / spacing - headroom;\n    }\n    getYForTopText(line = 0) {\n        return this.getYForLine(-line - this.options.topTextPosition);\n    }\n    getYForBottomText(line = 0) {\n        return this.getYForLine(this.options.bottomTextPosition + line);\n    }\n    getYForNote(line) {\n        const options = this.options;\n        const spacing = options.spacingBetweenLinesPx;\n        const headroom = options.spaceAboveStaffLn;\n        return this.y + headroom * spacing + 5 * spacing - line * spacing;\n    }\n    getYForGlyphs() {\n        return this.getYForLine(3);\n    }\n    addModifier(modifier, position) {\n        if (position !== undefined) {\n            modifier.setPosition(position);\n        }\n        modifier.setStave(this);\n        this.formatted = false;\n        this.modifiers.push(modifier);\n        return this;\n    }\n    addEndModifier(modifier) {\n        this.addModifier(modifier, StaveModifierPosition.END);\n        return this;\n    }\n    setBegBarType(type) {\n        const { SINGLE, REPEAT_BEGIN, NONE } = BarlineType;\n        if (type === SINGLE || type === REPEAT_BEGIN || type === NONE) {\n            this.modifiers[0].setType(type);\n            this.formatted = false;\n        }\n        return this;\n    }\n    setEndBarType(type) {\n        if (type !== BarlineType.REPEAT_BEGIN) {\n            this.modifiers[1].setType(type);\n            this.formatted = false;\n        }\n        return this;\n    }\n    setClef(clefSpec, size, annotation, position) {\n        if (position === undefined) {\n            position = StaveModifierPosition.BEGIN;\n        }\n        if (position === StaveModifierPosition.END) {\n            this.endClef = clefSpec;\n        }\n        else {\n            this.clef = clefSpec;\n        }\n        const clefs = this.getModifiers(position, Clef.CATEGORY);\n        if (clefs.length === 0) {\n            this.addClef(clefSpec, size, annotation, position);\n        }\n        else {\n            clefs[0].setType(clefSpec, size, annotation);\n        }\n        return this;\n    }\n    getClef() {\n        return this.clef;\n    }\n    setEndClef(clefSpec, size, annotation) {\n        this.setClef(clefSpec, size, annotation, StaveModifierPosition.END);\n        return this;\n    }\n    getEndClef() {\n        return this.endClef;\n    }\n    setKeySignature(keySpec, cancelKeySpec, position) {\n        if (position === undefined) {\n            position = StaveModifierPosition.BEGIN;\n        }\n        const keySignatures = this.getModifiers(position, KeySignature.CATEGORY);\n        if (keySignatures.length === 0) {\n            this.addKeySignature(keySpec, cancelKeySpec, position);\n        }\n        else {\n            keySignatures[0].setKeySig(keySpec, cancelKeySpec);\n        }\n        return this;\n    }\n    setEndKeySignature(keySpec, cancelKeySpec) {\n        this.setKeySignature(keySpec, cancelKeySpec, StaveModifierPosition.END);\n        return this;\n    }\n    setTimeSignature(timeSpec, customPadding, position) {\n        if (position === undefined) {\n            position = StaveModifierPosition.BEGIN;\n        }\n        const timeSignatures = this.getModifiers(position, TimeSignature.CATEGORY);\n        if (timeSignatures.length === 0) {\n            this.addTimeSignature(timeSpec, customPadding, position);\n        }\n        else {\n            timeSignatures[0].setTimeSig(timeSpec);\n        }\n        return this;\n    }\n    setEndTimeSignature(timeSpec, customPadding) {\n        this.setTimeSignature(timeSpec, customPadding, StaveModifierPosition.END);\n        return this;\n    }\n    addKeySignature(keySpec, cancelKeySpec, position) {\n        if (position === undefined) {\n            position = StaveModifierPosition.BEGIN;\n        }\n        this.addModifier(new KeySignature(keySpec, cancelKeySpec).setPosition(position), position);\n        return this;\n    }\n    addClef(clef, size, annotation, position) {\n        if (position === undefined || position === StaveModifierPosition.BEGIN) {\n            this.clef = clef;\n        }\n        else if (position === StaveModifierPosition.END) {\n            this.endClef = clef;\n        }\n        this.addModifier(new Clef(clef, size, annotation), position);\n        return this;\n    }\n    addEndClef(clef, size, annotation) {\n        this.addClef(clef, size, annotation, StaveModifierPosition.END);\n        return this;\n    }\n    addTimeSignature(timeSpec, customPadding, position) {\n        this.addModifier(new TimeSignature(timeSpec, customPadding), position);\n        return this;\n    }\n    addEndTimeSignature(timeSpec, customPadding) {\n        this.addTimeSignature(timeSpec, customPadding, StaveModifierPosition.END);\n        return this;\n    }\n    addTrebleGlyph() {\n        this.addClef('treble');\n        return this;\n    }\n    getModifiers(position, category) {\n        const noPosition = position === undefined;\n        const noCategory = category === undefined;\n        if (noPosition && noCategory) {\n            return this.modifiers;\n        }\n        else if (noPosition) {\n            return this.modifiers.filter((m) => category === m.getCategory());\n        }\n        else if (noCategory) {\n            return this.modifiers.filter((m) => position === m.getPosition());\n        }\n        else {\n            return this.modifiers.filter((m) => position === m.getPosition() && category === m.getCategory());\n        }\n    }\n    sortByCategory(items, order) {\n        for (let i = items.length - 1; i >= 0; i--) {\n            for (let j = 0; j < i; j++) {\n                if (order[items[j].getCategory()] > order[items[j + 1].getCategory()]) {\n                    const temp = items[j];\n                    items[j] = items[j + 1];\n                    items[j + 1] = temp;\n                }\n            }\n        }\n    }\n    format() {\n        var _a, _b, _c, _d;\n        const begBarline = this.modifiers[0];\n        const endBarline = this.modifiers[1];\n        const begModifiers = this.getModifiers(StaveModifierPosition.BEGIN);\n        const endModifiers = this.getModifiers(StaveModifierPosition.END);\n        this.sortByCategory(begModifiers, SORT_ORDER_BEG_MODIFIERS);\n        this.sortByCategory(endModifiers, SORT_ORDER_END_MODIFIERS);\n        if (begModifiers.length > 1 && begBarline.getType() === BarlineType.REPEAT_BEGIN) {\n            begModifiers.push(begModifiers.splice(0, 1)[0]);\n            begModifiers.splice(0, 0, new Barline(BarlineType.SINGLE));\n        }\n        if (endModifiers.indexOf(endBarline) > 0) {\n            endModifiers.splice(0, 0, new Barline(BarlineType.NONE));\n        }\n        let width;\n        let padding;\n        let modifier;\n        let offset = 0;\n        let x = this.x;\n        for (let i = 0; i < begModifiers.length; i++) {\n            modifier = begModifiers[i];\n            padding = modifier.getPadding(i + offset);\n            width = modifier.getWidth();\n            x += padding;\n            modifier.setX(x);\n            x += width;\n            if (padding + width === 0)\n                offset--;\n        }\n        this.startX = x;\n        x = this.x + this.width;\n        const widths = {\n            left: 0,\n            right: 0,\n            paddingRight: 0,\n            paddingLeft: 0,\n        };\n        let lastBarlineIdx = 0;\n        for (let i = 0; i < endModifiers.length; i++) {\n            modifier = endModifiers[i];\n            lastBarlineIdx = isBarline(modifier) ? i : lastBarlineIdx;\n            widths.right = 0;\n            widths.left = 0;\n            widths.paddingRight = 0;\n            widths.paddingLeft = 0;\n            const layoutMetrics = modifier.getLayoutMetrics();\n            if (layoutMetrics) {\n                if (i !== 0) {\n                    widths.right = (_a = layoutMetrics.xMax) !== null && _a !== void 0 ? _a : 0;\n                    widths.paddingRight = (_b = layoutMetrics.paddingRight) !== null && _b !== void 0 ? _b : 0;\n                }\n                widths.left = -((_c = layoutMetrics.xMin) !== null && _c !== void 0 ? _c : 0);\n                widths.paddingLeft = (_d = layoutMetrics.paddingLeft) !== null && _d !== void 0 ? _d : 0;\n                if (i === endModifiers.length - 1) {\n                    widths.paddingLeft = 0;\n                }\n            }\n            else {\n                widths.paddingRight = modifier.getPadding(i - lastBarlineIdx);\n                if (i !== 0) {\n                    widths.right = modifier.getWidth();\n                }\n                if (i === 0) {\n                    widths.left = modifier.getWidth();\n                }\n            }\n            x -= widths.paddingRight;\n            x -= widths.right;\n            modifier.setX(x);\n            x -= widths.left;\n            x -= widths.paddingLeft;\n        }\n        this.endX = endModifiers.length === 1 ? this.x + this.width : x;\n        this.formatted = true;\n    }\n    draw() {\n        var _a;\n        const ctx = this.checkContext();\n        this.setRendered();\n        ctx.openGroup('stave', this.getAttribute('id'));\n        if (!this.formatted)\n            this.format();\n        const numLines = this.options.numLines;\n        const width = this.width;\n        const x = this.x;\n        let y;\n        const lineWidth = (_a = this.getStyle().lineWidth) !== null && _a !== void 0 ? _a : 1;\n        const lineWidthCorrection = lineWidth % 2 === 0 ? 0 : 0.5;\n        for (let line = 0; line < numLines; line++) {\n            y = this.getYForLine(line);\n            if (this.options.lineConfig[line].visible) {\n                ctx.beginPath();\n                ctx.moveTo(x, y + lineWidthCorrection);\n                ctx.lineTo(x + width, y + lineWidthCorrection);\n                ctx.stroke();\n            }\n        }\n        ctx.closeGroup();\n        for (let i = 0; i < this.modifiers.length; i++) {\n            const modifier = this.modifiers[i];\n            modifier.setContext(ctx);\n            modifier.setStave(this);\n            modifier.drawWithStyle();\n        }\n        if (this.measure > 0) {\n            ctx.setFont(this.fontInfo);\n            const textWidth = ctx.measureText('' + this.measure).width;\n            y = this.getYForTopText(0) + 3;\n            ctx.fillText('' + this.measure, this.x - textWidth / 2, y);\n        }\n    }\n    getVerticalBarWidth() {\n        return this.options.verticalBarWidth;\n    }\n    getConfigForLines() {\n        return this.options.lineConfig;\n    }\n    setConfigForLine(lineNumber, lineConfig) {\n        if (lineNumber >= this.options.numLines || lineNumber < 0) {\n            throw new RuntimeError('StaveConfigError', 'The line number must be within the range of the number of lines in the Stave.');\n        }\n        if (lineConfig.visible === undefined) {\n            throw new RuntimeError('StaveConfigError', \"The line configuration object is missing the 'visible' property.\");\n        }\n        if (typeof lineConfig.visible !== 'boolean') {\n            throw new RuntimeError('StaveConfigError', \"The line configuration objects 'visible' property must be true or false.\");\n        }\n        this.options.lineConfig[lineNumber] = lineConfig;\n        return this;\n    }\n    setConfigForLines(linesConfiguration) {\n        if (linesConfiguration.length !== this.options.numLines) {\n            throw new RuntimeError('StaveConfigError', 'The length of the lines configuration array must match the number of lines in the Stave');\n        }\n        for (const lineConfig in linesConfiguration) {\n            if (linesConfiguration[lineConfig].visible === undefined) {\n                linesConfiguration[lineConfig] = this.options.lineConfig[lineConfig];\n            }\n            this.options.lineConfig[lineConfig] = Object.assign(Object.assign({}, this.options.lineConfig[lineConfig]), linesConfiguration[lineConfig]);\n        }\n        this.options.lineConfig = linesConfiguration;\n        return this;\n    }\n    static formatBegModifiers(staves) {\n        const adjustCategoryStartX = (category) => {\n            let minStartX = 0;\n            staves.forEach((stave) => {\n                const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, category);\n                if (modifiers.length > 0 && modifiers[0].getX() > minStartX)\n                    minStartX = modifiers[0].getX();\n            });\n            let adjustX = 0;\n            staves.forEach((stave) => {\n                adjustX = 0;\n                const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, category);\n                modifiers.forEach((modifier) => {\n                    if (minStartX - modifier.getX() > adjustX)\n                        adjustX = minStartX - modifier.getX();\n                });\n                const allModifiers = stave.getModifiers(StaveModifierPosition.BEGIN);\n                let bAdjust = false;\n                allModifiers.forEach((modifier) => {\n                    if (modifier.getCategory() === category)\n                        bAdjust = true;\n                    if (bAdjust && adjustX > 0)\n                        modifier.setX(modifier.getX() + adjustX);\n                });\n                stave.setNoteStartX(stave.getNoteStartX() + adjustX);\n            });\n        };\n        staves.forEach((stave) => {\n            if (!stave.formatted)\n                stave.format();\n        });\n        adjustCategoryStartX(\"Clef\");\n        adjustCategoryStartX(\"KeySignature\");\n        adjustCategoryStartX(\"TimeSignature\");\n        let maxX = 0;\n        staves.forEach((stave) => {\n            if (stave.getNoteStartX() > maxX)\n                maxX = stave.getNoteStartX();\n        });\n        staves.forEach((stave) => {\n            stave.setNoteStartX(maxX);\n        });\n        maxX = 0;\n        staves.forEach((stave) => {\n            const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, \"Barline\");\n            modifiers.forEach((modifier) => {\n                if (modifier.getType() === BarlineType.REPEAT_BEGIN)\n                    if (modifier.getX() > maxX)\n                        maxX = modifier.getX();\n            });\n        });\n        staves.forEach((stave) => {\n            const modifiers = stave.getModifiers(StaveModifierPosition.BEGIN, \"Barline\");\n            modifiers.forEach((modifier) => {\n                if (modifier.getType() === BarlineType.REPEAT_BEGIN)\n                    modifier.setX(maxX);\n            });\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,EAAEC,WAAW,QAAQ,mBAAmB;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,wBAAwB,GAAG;EAC7B,CAACZ,OAAO,CAACa,QAAQ,GAAG,CAAC;EACrB,CAACjB,IAAI,CAACiB,QAAQ,GAAG,CAAC;EAClB,CAACf,YAAY,CAACe,QAAQ,GAAG,CAAC;EAC1B,CAACJ,aAAa,CAACI,QAAQ,GAAG;AAC9B,CAAC;AACD,MAAMC,wBAAwB,GAAG;EAC7B,CAACL,aAAa,CAACI,QAAQ,GAAG,CAAC;EAC3B,CAACf,YAAY,CAACe,QAAQ,GAAG,CAAC;EAC1B,CAACb,OAAO,CAACa,QAAQ,GAAG,CAAC;EACrB,CAACjB,IAAI,CAACiB,QAAQ,GAAG;AACrB,CAAC;AACD,OAAO,MAAME,KAAK,SAASlB,OAAO,CAAC;EAC/B,WAAWgB,QAAQA,CAAA,EAAG;IAClB,OAAO,OAAO;EAClB;EACA,WAAWG,cAAcA,CAAA,EAAG;IACxB,OAAOjB,OAAO,CAACkB,GAAG,CAAC,eAAe,CAAC,GAAGlB,OAAO,CAACkB,GAAG,CAAC,qBAAqB,CAAC;EAC5E;EACA,WAAWC,YAAYA,CAAA,EAAG;IACtB,OAAOnB,OAAO,CAACkB,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACAE,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,MAAM,GAAGL,CAAC,GAAG,CAAC;IACnB,IAAI,CAACM,IAAI,GAAGN,CAAC,GAAGE,KAAK;IACrB,IAAI,CAACK,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,OAAO,GAAGC,SAAS;IACxB,IAAI,CAACR,OAAO,GAAGS,MAAM,CAACC,MAAM,CAAC;MAAEC,gBAAgB,EAAE,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,OAAO,EAAE,IAAI;MAAEC,QAAQ,EAAE,IAAI;MAAEC,qBAAqB,EAAE9B,MAAM,CAAC+B,mBAAmB;MAAEC,iBAAiB,EAAE,CAAC;MAAEC,iBAAiB,EAAE,CAAC;MAAEC,eAAe,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAG,CAAC,EAAErB,OAAO,CAAC;IACrQ,IAAI,CAACsB,MAAM,GAAG;MAAEzB,CAAC,EAAE,IAAI,CAACA,CAAC;MAAEC,CAAC,EAAE,IAAI,CAACA,CAAC;MAAEyB,CAAC,EAAE,IAAI,CAACxB,KAAK;MAAEyB,CAAC,EAAE;IAAE,CAAC;IAC3D,IAAI,CAACC,sBAAsB,GAAG;MAAEC,WAAW,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAE,CAAC;IACnE,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,WAAW,CAAC,IAAIpD,OAAO,CAAC,IAAI,CAACuB,OAAO,CAACa,OAAO,GAAGnC,WAAW,CAACoD,MAAM,GAAGpD,WAAW,CAACqD,IAAI,CAAC,CAAC;IAC3F,IAAI,CAACC,cAAc,CAAC,IAAIvD,OAAO,CAAC,IAAI,CAACuB,OAAO,CAACc,QAAQ,GAAGpC,WAAW,CAACoD,MAAM,GAAGpD,WAAW,CAACqD,IAAI,CAAC,CAAC;EACnG;EACAE,yBAAyBA,CAACC,KAAK,EAAE;IAC7B,IAAI,CAACT,sBAAsB,GAAGS,KAAK;EACvC;EACAC,yBAAyBA,CAAA,EAAG;IACxB,OAAO1B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC0B,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,sBAAsB,CAAC;EACzF;EACAY,KAAKA,CAACC,OAAO,EAAE;IACX,OAAO,IAAI,CAACtC,OAAO,CAACe,qBAAqB,GAAGuB,OAAO;EACvD;EACAV,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC5B,OAAO,CAACqB,UAAU,GAAG,EAAE;IAC5B,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvC,OAAO,CAACY,QAAQ,EAAE2B,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACvC,OAAO,CAACqB,UAAU,CAACmB,IAAI,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACnD;IACA,IAAI,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC1C,OAAO,CAACY,QAAQ,GAAG,IAAI,CAACZ,OAAO,CAACiB,iBAAiB,IAAI,IAAI,CAACjB,OAAO,CAACe,qBAAqB;IAC3G,IAAI,CAACf,OAAO,CAACoB,kBAAkB,GAAG,IAAI,CAACpB,OAAO,CAACY,QAAQ;EAC3D;EACA+B,aAAaA,CAAC9C,CAAC,EAAE;IACb,IAAI,CAAC,IAAI,CAACI,SAAS,EACf,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB,IAAI,CAAC1C,MAAM,GAAGL,CAAC;IACf,OAAO,IAAI;EACf;EACAgD,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAC5C,SAAS,EACf,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI,CAAC1C,MAAM;EACtB;EACA4C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC7C,SAAS,EACf,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI,CAACzC,IAAI;EACpB;EACA4C,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7C,MAAM;EACtB;EACA8C,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7C,IAAI;EACpB;EACA8C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjD,OAAO,CAACY,QAAQ;EAChC;EACAsC,WAAWA,CAACC,CAAC,EAAE;IACX,IAAI,CAACnD,OAAO,CAACY,QAAQ,GAAGuC,CAAC;IACzB,IAAI,CAACvB,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACf;EACAwB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;EAC9B;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAIC,EAAE;IACN,OAAO,IAAI,CAACF,WAAW,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAACM,EAAE,GAAG,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACT,SAAS,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;EAC3H;EACAC,IAAIA,CAAC3D,CAAC,EAAE;IACJ,MAAM4D,KAAK,GAAG5D,CAAC,GAAG,IAAI,CAACA,CAAC;IACxB,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACJ,CAAC,GAAGA,CAAC;IACV,IAAI,CAACK,MAAM,IAAIuD,KAAK;IACpB,IAAI,CAACtD,IAAI,IAAIsD,KAAK;IAClB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnC,SAAS,CAACsD,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC5C,MAAMoB,GAAG,GAAG,IAAI,CAACvD,SAAS,CAACmC,CAAC,CAAC;MAC7BoB,GAAG,CAACH,IAAI,CAACG,GAAG,CAACC,IAAI,CAAC,CAAC,GAAGH,KAAK,CAAC;IAChC;IACA,OAAO,IAAI;EACf;EACAI,QAAQA,CAAC9D,KAAK,EAAE;IACZ,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,IAAI,GAAG,IAAI,CAACN,CAAC,GAAGE,KAAK;IAC1B,OAAO,IAAI;EACf;EACA+D,UAAUA,CAACzD,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACA0D,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1D,OAAO;EACvB;EACA2D,iBAAiBA,CAACC,KAAK,GAAG,CAAC,EAAE;IACzB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAI7E,YAAY,CAAC,cAAc,EAAE,wBAAwB,CAAC;IACpE;IACA,IAAI,CAAC,IAAI,CAACa,SAAS,EACf,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB,IAAI,IAAI,CAACsB,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,CAAC,CAACT,MAAM,KAAK,CAAC,EAAE;MAC7D,OAAO,CAAC;IACZ;IACA,IAAI,IAAI,CAACtD,SAAS,CAAC6D,KAAK,CAAC,CAACG,WAAW,CAAC,CAAC,KAAKzF,qBAAqB,CAAC0F,KAAK,EAAE;MACrE,OAAO,CAAC;IACZ;IACA,IAAInE,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,IAAI,CAACL,CAAC;IACjC,MAAMyE,UAAU,GAAG,IAAI,CAAClE,SAAS,CAAC,CAAC,CAAC;IACpC,IAAIkE,UAAU,CAACC,OAAO,CAAC,CAAC,KAAK7F,WAAW,CAAC8F,YAAY,IAAItE,MAAM,GAAGoE,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAE;MACrFvE,MAAM,IAAIoE,UAAU,CAACG,QAAQ,CAAC,CAAC;IACnC;IACA,OAAOvE,MAAM;EACjB;EACAwE,iBAAiBA,CAACC,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAE;IAChC,IAAI,CAACxE,SAAS,CAACoC,IAAI,CAAC,IAAI5D,UAAU,CAAC+F,IAAI,EAAE,IAAI,CAAC9E,CAAC,EAAE+E,MAAM,CAAC,CAAC;IACzD,OAAO,IAAI;EACf;EACAC,YAAYA,CAACF,IAAI,EAAEG,KAAK,EAAEhF,CAAC,EAAE;IACzB,IAAI,CAACM,SAAS,CAACoC,IAAI,CAAC,IAAIxD,KAAK,CAAC2F,IAAI,EAAEG,KAAK,EAAE,IAAI,CAACjF,CAAC,EAAEC,CAAC,CAAC,CAAC;IACtD,OAAO,IAAI;EACf;EACAiF,UAAUA,CAACC,OAAO,EAAElF,CAAC,EAAEmF,OAAO,GAAG,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,IAAI,EAAE;IAC3D,MAAMC,YAAY,GAAG,IAAIvG,YAAY,CAACmG,OAAO,CAAC,CAACK,SAAS,CAACvF,CAAC,CAAC,CAACwF,SAAS,CAACL,OAAO,CAAC,CAACM,WAAW,CAACJ,QAAQ,CAAC;IACpG,IAAID,QAAQ,EAAE;MACVE,YAAY,CAACI,WAAW,CAACN,QAAQ,CAAC;IACtC;IACA,IAAI,CAACrD,WAAW,CAACuD,YAAY,CAAC;IAC9B,OAAO,IAAI;EACf;EACAK,QAAQA,CAACC,KAAK,EAAE5F,CAAC,EAAE;IACf,IAAI,CAACM,SAAS,CAACoC,IAAI,CAAC,IAAI1D,UAAU,CAAC4G,KAAK,EAAE,IAAI,CAAC7F,CAAC,EAAEC,CAAC,CAAC,CAAC;IACrD,OAAO,IAAI;EACf;EACA6F,YAAYA,CAACC,IAAI,EAAEC,QAAQ,EAAE7F,OAAO,GAAG,CAAC,CAAC,EAAE;IACvC,IAAI,CAACI,SAAS,CAACoC,IAAI,CAAC,IAAIzD,SAAS,CAAC6G,IAAI,EAAEC,QAAQ,EAAE7F,OAAO,CAAC,CAAC;IAC3D,OAAO,IAAI;EACf;EACA8F,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC9F,OAAO,CAACe,qBAAqB;EAC7C;EACAgF,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI3H,WAAW,CAAC,IAAI,CAACyB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACiG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAClG,CAAC,CAAC;EAClF;EACAkG,UAAUA,CAAA,EAAG;IACT,MAAMhG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMsC,OAAO,GAAGtC,OAAO,CAACe,qBAAqB;IAC7C,MAAMkF,WAAW,GAAG,IAAI,CAAC5C,WAAW,CAACrD,OAAO,CAACY,QAAQ,CAAC,GAAGZ,OAAO,CAACkB,iBAAiB,GAAGoB,OAAO;IAC5F,OAAO2D,WAAW;EACtB;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC7C,WAAW,CAAC,IAAI,CAACrD,OAAO,CAACY,QAAQ,CAAC;EAClD;EACAyC,WAAWA,CAAC8C,IAAI,EAAE;IACd,MAAMnG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMsC,OAAO,GAAGtC,OAAO,CAACe,qBAAqB;IAC7C,MAAMqF,QAAQ,GAAGpG,OAAO,CAACiB,iBAAiB;IAC1C,MAAMnB,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGqG,IAAI,GAAG7D,OAAO,GAAG8D,QAAQ,GAAG9D,OAAO;IACtD,OAAOxC,CAAC;EACZ;EACAuG,WAAWA,CAACvG,CAAC,EAAE;IACX,MAAME,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMsC,OAAO,GAAGtC,OAAO,CAACe,qBAAqB;IAC7C,MAAMqF,QAAQ,GAAGpG,OAAO,CAACiB,iBAAiB;IAC1C,OAAO,CAACnB,CAAC,GAAG,IAAI,CAACA,CAAC,IAAIwC,OAAO,GAAG8D,QAAQ;EAC5C;EACAE,cAAcA,CAACH,IAAI,GAAG,CAAC,EAAE;IACrB,OAAO,IAAI,CAAC9C,WAAW,CAAC,CAAC8C,IAAI,GAAG,IAAI,CAACnG,OAAO,CAACmB,eAAe,CAAC;EACjE;EACAoF,iBAAiBA,CAACJ,IAAI,GAAG,CAAC,EAAE;IACxB,OAAO,IAAI,CAAC9C,WAAW,CAAC,IAAI,CAACrD,OAAO,CAACoB,kBAAkB,GAAG+E,IAAI,CAAC;EACnE;EACAK,WAAWA,CAACL,IAAI,EAAE;IACd,MAAMnG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMsC,OAAO,GAAGtC,OAAO,CAACe,qBAAqB;IAC7C,MAAMqF,QAAQ,GAAGpG,OAAO,CAACiB,iBAAiB;IAC1C,OAAO,IAAI,CAACnB,CAAC,GAAGsG,QAAQ,GAAG9D,OAAO,GAAG,CAAC,GAAGA,OAAO,GAAG6D,IAAI,GAAG7D,OAAO;EACrE;EACAmE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpD,WAAW,CAAC,CAAC,CAAC;EAC9B;EACAxB,WAAWA,CAAC6E,QAAQ,EAAEb,QAAQ,EAAE;IAC5B,IAAIA,QAAQ,KAAKrF,SAAS,EAAE;MACxBkG,QAAQ,CAACC,WAAW,CAACd,QAAQ,CAAC;IAClC;IACAa,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC;IACvB,IAAI,CAAC3G,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,SAAS,CAACoC,IAAI,CAACkE,QAAQ,CAAC;IAC7B,OAAO,IAAI;EACf;EACA1E,cAAcA,CAAC0E,QAAQ,EAAE;IACrB,IAAI,CAAC7E,WAAW,CAAC6E,QAAQ,EAAE/H,qBAAqB,CAACkI,GAAG,CAAC;IACrD,OAAO,IAAI;EACf;EACAC,aAAaA,CAACnC,IAAI,EAAE;IAChB,MAAM;MAAE7C,MAAM;MAAE0C,YAAY;MAAEzC;IAAK,CAAC,GAAGrD,WAAW;IAClD,IAAIiG,IAAI,KAAK7C,MAAM,IAAI6C,IAAI,KAAKH,YAAY,IAAIG,IAAI,KAAK5C,IAAI,EAAE;MAC3D,IAAI,CAAC3B,SAAS,CAAC,CAAC,CAAC,CAAC2G,OAAO,CAACpC,IAAI,CAAC;MAC/B,IAAI,CAAC1E,SAAS,GAAG,KAAK;IAC1B;IACA,OAAO,IAAI;EACf;EACA+G,aAAaA,CAACrC,IAAI,EAAE;IAChB,IAAIA,IAAI,KAAKjG,WAAW,CAAC8F,YAAY,EAAE;MACnC,IAAI,CAACpE,SAAS,CAAC,CAAC,CAAC,CAAC2G,OAAO,CAACpC,IAAI,CAAC;MAC/B,IAAI,CAAC1E,SAAS,GAAG,KAAK;IAC1B;IACA,OAAO,IAAI;EACf;EACAgH,OAAOA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEvB,QAAQ,EAAE;IAC1C,IAAIA,QAAQ,KAAKrF,SAAS,EAAE;MACxBqF,QAAQ,GAAGlH,qBAAqB,CAACwF,KAAK;IAC1C;IACA,IAAI0B,QAAQ,KAAKlH,qBAAqB,CAACkI,GAAG,EAAE;MACxC,IAAI,CAACtG,OAAO,GAAG2G,QAAQ;IAC3B,CAAC,MACI;MACD,IAAI,CAAC5G,IAAI,GAAG4G,QAAQ;IACxB;IACA,MAAMG,KAAK,GAAG,IAAI,CAACnD,YAAY,CAAC2B,QAAQ,EAAExH,IAAI,CAACiB,QAAQ,CAAC;IACxD,IAAI+H,KAAK,CAAC3D,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAAC4D,OAAO,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEvB,QAAQ,CAAC;IACtD,CAAC,MACI;MACDwB,KAAK,CAAC,CAAC,CAAC,CAACN,OAAO,CAACG,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAAC;IAChD;IACA,OAAO,IAAI;EACf;EACAG,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjH,IAAI;EACpB;EACAkH,UAAUA,CAACN,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAE;IACnC,IAAI,CAACH,OAAO,CAACC,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEzI,qBAAqB,CAACkI,GAAG,CAAC;IACnE,OAAO,IAAI;EACf;EACAY,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClH,OAAO;EACvB;EACAmH,eAAeA,CAACC,OAAO,EAAEC,aAAa,EAAE/B,QAAQ,EAAE;IAC9C,IAAIA,QAAQ,KAAKrF,SAAS,EAAE;MACxBqF,QAAQ,GAAGlH,qBAAqB,CAACwF,KAAK;IAC1C;IACA,MAAM0D,aAAa,GAAG,IAAI,CAAC3D,YAAY,CAAC2B,QAAQ,EAAEtH,YAAY,CAACe,QAAQ,CAAC;IACxE,IAAIuI,aAAa,CAACnE,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACoE,eAAe,CAACH,OAAO,EAAEC,aAAa,EAAE/B,QAAQ,CAAC;IAC1D,CAAC,MACI;MACDgC,aAAa,CAAC,CAAC,CAAC,CAACE,SAAS,CAACJ,OAAO,EAAEC,aAAa,CAAC;IACtD;IACA,OAAO,IAAI;EACf;EACAI,kBAAkBA,CAACL,OAAO,EAAEC,aAAa,EAAE;IACvC,IAAI,CAACF,eAAe,CAACC,OAAO,EAAEC,aAAa,EAAEjJ,qBAAqB,CAACkI,GAAG,CAAC;IACvE,OAAO,IAAI;EACf;EACAoB,gBAAgBA,CAACC,QAAQ,EAAEC,aAAa,EAAEtC,QAAQ,EAAE;IAChD,IAAIA,QAAQ,KAAKrF,SAAS,EAAE;MACxBqF,QAAQ,GAAGlH,qBAAqB,CAACwF,KAAK;IAC1C;IACA,MAAMiE,cAAc,GAAG,IAAI,CAAClE,YAAY,CAAC2B,QAAQ,EAAE3G,aAAa,CAACI,QAAQ,CAAC;IAC1E,IAAI8I,cAAc,CAAC1E,MAAM,KAAK,CAAC,EAAE;MAC7B,IAAI,CAAC2E,gBAAgB,CAACH,QAAQ,EAAEC,aAAa,EAAEtC,QAAQ,CAAC;IAC5D,CAAC,MACI;MACDuC,cAAc,CAAC,CAAC,CAAC,CAACE,UAAU,CAACJ,QAAQ,CAAC;IAC1C;IACA,OAAO,IAAI;EACf;EACAK,mBAAmBA,CAACL,QAAQ,EAAEC,aAAa,EAAE;IACzC,IAAI,CAACF,gBAAgB,CAACC,QAAQ,EAAEC,aAAa,EAAExJ,qBAAqB,CAACkI,GAAG,CAAC;IACzE,OAAO,IAAI;EACf;EACAiB,eAAeA,CAACH,OAAO,EAAEC,aAAa,EAAE/B,QAAQ,EAAE;IAC9C,IAAIA,QAAQ,KAAKrF,SAAS,EAAE;MACxBqF,QAAQ,GAAGlH,qBAAqB,CAACwF,KAAK;IAC1C;IACA,IAAI,CAACtC,WAAW,CAAC,IAAItD,YAAY,CAACoJ,OAAO,EAAEC,aAAa,CAAC,CAACjB,WAAW,CAACd,QAAQ,CAAC,EAAEA,QAAQ,CAAC;IAC1F,OAAO,IAAI;EACf;EACAyB,OAAOA,CAAChH,IAAI,EAAE6G,IAAI,EAAEC,UAAU,EAAEvB,QAAQ,EAAE;IACtC,IAAIA,QAAQ,KAAKrF,SAAS,IAAIqF,QAAQ,KAAKlH,qBAAqB,CAACwF,KAAK,EAAE;MACpE,IAAI,CAAC7D,IAAI,GAAGA,IAAI;IACpB,CAAC,MACI,IAAIuF,QAAQ,KAAKlH,qBAAqB,CAACkI,GAAG,EAAE;MAC7C,IAAI,CAACtG,OAAO,GAAGD,IAAI;IACvB;IACA,IAAI,CAACuB,WAAW,CAAC,IAAIxD,IAAI,CAACiC,IAAI,EAAE6G,IAAI,EAAEC,UAAU,CAAC,EAAEvB,QAAQ,CAAC;IAC5D,OAAO,IAAI;EACf;EACA2C,UAAUA,CAAClI,IAAI,EAAE6G,IAAI,EAAEC,UAAU,EAAE;IAC/B,IAAI,CAACE,OAAO,CAAChH,IAAI,EAAE6G,IAAI,EAAEC,UAAU,EAAEzI,qBAAqB,CAACkI,GAAG,CAAC;IAC/D,OAAO,IAAI;EACf;EACAwB,gBAAgBA,CAACH,QAAQ,EAAEC,aAAa,EAAEtC,QAAQ,EAAE;IAChD,IAAI,CAAChE,WAAW,CAAC,IAAI3C,aAAa,CAACgJ,QAAQ,EAAEC,aAAa,CAAC,EAAEtC,QAAQ,CAAC;IACtE,OAAO,IAAI;EACf;EACA4C,mBAAmBA,CAACP,QAAQ,EAAEC,aAAa,EAAE;IACzC,IAAI,CAACE,gBAAgB,CAACH,QAAQ,EAAEC,aAAa,EAAExJ,qBAAqB,CAACkI,GAAG,CAAC;IACzE,OAAO,IAAI;EACf;EACA6B,cAAcA,CAAA,EAAG;IACb,IAAI,CAACpB,OAAO,CAAC,QAAQ,CAAC;IACtB,OAAO,IAAI;EACf;EACApD,YAAYA,CAAC2B,QAAQ,EAAE8C,QAAQ,EAAE;IAC7B,MAAMC,UAAU,GAAG/C,QAAQ,KAAKrF,SAAS;IACzC,MAAMqI,UAAU,GAAGF,QAAQ,KAAKnI,SAAS;IACzC,IAAIoI,UAAU,IAAIC,UAAU,EAAE;MAC1B,OAAO,IAAI,CAACzI,SAAS;IACzB,CAAC,MACI,IAAIwI,UAAU,EAAE;MACjB,OAAO,IAAI,CAACxI,SAAS,CAAC0I,MAAM,CAAEC,CAAC,IAAKJ,QAAQ,KAAKI,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IACrE,CAAC,MACI,IAAIH,UAAU,EAAE;MACjB,OAAO,IAAI,CAACzI,SAAS,CAAC0I,MAAM,CAAEC,CAAC,IAAKlD,QAAQ,KAAKkD,CAAC,CAAC3E,WAAW,CAAC,CAAC,CAAC;IACrE,CAAC,MACI;MACD,OAAO,IAAI,CAAChE,SAAS,CAAC0I,MAAM,CAAEC,CAAC,IAAKlD,QAAQ,KAAKkD,CAAC,CAAC3E,WAAW,CAAC,CAAC,IAAIuE,QAAQ,KAAKI,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;IACrG;EACJ;EACAC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACzB,KAAK,IAAI5G,CAAC,GAAG2G,KAAK,CAACxF,MAAM,GAAG,CAAC,EAAEnB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxC,KAAK,IAAI6G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7G,CAAC,EAAE6G,CAAC,EAAE,EAAE;QACxB,IAAID,KAAK,CAACD,KAAK,CAACE,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAC,GAAGG,KAAK,CAACD,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,CAACJ,WAAW,CAAC,CAAC,CAAC,EAAE;UACnE,MAAMK,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC;UACrBF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC;UACvBF,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGC,IAAI;QACvB;MACJ;IACJ;EACJ;EACAzG,MAAMA,CAAA,EAAG;IACL,IAAIW,EAAE,EAAE+F,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAClB,MAAMlF,UAAU,GAAG,IAAI,CAAClE,SAAS,CAAC,CAAC,CAAC;IACpC,MAAMqJ,UAAU,GAAG,IAAI,CAACrJ,SAAS,CAAC,CAAC,CAAC;IACpC,MAAMsJ,YAAY,GAAG,IAAI,CAACxF,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,CAAC;IACnE,MAAMwF,YAAY,GAAG,IAAI,CAACzF,YAAY,CAACvF,qBAAqB,CAACkI,GAAG,CAAC;IACjE,IAAI,CAACoC,cAAc,CAACS,YAAY,EAAErK,wBAAwB,CAAC;IAC3D,IAAI,CAAC4J,cAAc,CAACU,YAAY,EAAEpK,wBAAwB,CAAC;IAC3D,IAAImK,YAAY,CAAChG,MAAM,GAAG,CAAC,IAAIY,UAAU,CAACC,OAAO,CAAC,CAAC,KAAK7F,WAAW,CAAC8F,YAAY,EAAE;MAC9EkF,YAAY,CAAClH,IAAI,CAACkH,YAAY,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/CF,YAAY,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAInL,OAAO,CAACC,WAAW,CAACoD,MAAM,CAAC,CAAC;IAC9D;IACA,IAAI6H,YAAY,CAACE,OAAO,CAACJ,UAAU,CAAC,GAAG,CAAC,EAAE;MACtCE,YAAY,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAInL,OAAO,CAACC,WAAW,CAACqD,IAAI,CAAC,CAAC;IAC5D;IACA,IAAIhC,KAAK;IACT,IAAI+J,OAAO;IACX,IAAIpD,QAAQ;IACZ,IAAIqD,MAAM,GAAG,CAAC;IACd,IAAIlK,CAAC,GAAG,IAAI,CAACA,CAAC;IACd,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmH,YAAY,CAAChG,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC1CmE,QAAQ,GAAGgD,YAAY,CAACnH,CAAC,CAAC;MAC1BuH,OAAO,GAAGpD,QAAQ,CAACsD,UAAU,CAACzH,CAAC,GAAGwH,MAAM,CAAC;MACzChK,KAAK,GAAG2G,QAAQ,CAACjC,QAAQ,CAAC,CAAC;MAC3B5E,CAAC,IAAIiK,OAAO;MACZpD,QAAQ,CAAClD,IAAI,CAAC3D,CAAC,CAAC;MAChBA,CAAC,IAAIE,KAAK;MACV,IAAI+J,OAAO,GAAG/J,KAAK,KAAK,CAAC,EACrBgK,MAAM,EAAE;IAChB;IACA,IAAI,CAAC7J,MAAM,GAAGL,CAAC;IACfA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACE,KAAK;IACvB,MAAMkK,MAAM,GAAG;MACXC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE;IACjB,CAAC;IACD,IAAIC,cAAc,GAAG,CAAC;IACtB,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoH,YAAY,CAACjG,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC1CmE,QAAQ,GAAGiD,YAAY,CAACpH,CAAC,CAAC;MAC1B+H,cAAc,GAAGnL,SAAS,CAACuH,QAAQ,CAAC,GAAGnE,CAAC,GAAG+H,cAAc;MACzDL,MAAM,CAACE,KAAK,GAAG,CAAC;MAChBF,MAAM,CAACC,IAAI,GAAG,CAAC;MACfD,MAAM,CAACG,YAAY,GAAG,CAAC;MACvBH,MAAM,CAACI,WAAW,GAAG,CAAC;MACtB,MAAME,aAAa,GAAG7D,QAAQ,CAAC8D,gBAAgB,CAAC,CAAC;MACjD,IAAID,aAAa,EAAE;QACf,IAAIhI,CAAC,KAAK,CAAC,EAAE;UACT0H,MAAM,CAACE,KAAK,GAAG,CAAC5G,EAAE,GAAGgH,aAAa,CAACE,IAAI,MAAM,IAAI,IAAIlH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;UAC3E0G,MAAM,CAACG,YAAY,GAAG,CAACd,EAAE,GAAGiB,aAAa,CAACH,YAAY,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;QAC9F;QACAW,MAAM,CAACC,IAAI,GAAG,EAAE,CAACX,EAAE,GAAGgB,aAAa,CAACG,IAAI,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;QAC7EU,MAAM,CAACI,WAAW,GAAG,CAACb,EAAE,GAAGe,aAAa,CAACF,WAAW,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;QACxF,IAAIjH,CAAC,KAAKoH,YAAY,CAACjG,MAAM,GAAG,CAAC,EAAE;UAC/BuG,MAAM,CAACI,WAAW,GAAG,CAAC;QAC1B;MACJ,CAAC,MACI;QACDJ,MAAM,CAACG,YAAY,GAAG1D,QAAQ,CAACsD,UAAU,CAACzH,CAAC,GAAG+H,cAAc,CAAC;QAC7D,IAAI/H,CAAC,KAAK,CAAC,EAAE;UACT0H,MAAM,CAACE,KAAK,GAAGzD,QAAQ,CAACjC,QAAQ,CAAC,CAAC;QACtC;QACA,IAAIlC,CAAC,KAAK,CAAC,EAAE;UACT0H,MAAM,CAACC,IAAI,GAAGxD,QAAQ,CAACjC,QAAQ,CAAC,CAAC;QACrC;MACJ;MACA5E,CAAC,IAAIoK,MAAM,CAACG,YAAY;MACxBvK,CAAC,IAAIoK,MAAM,CAACE,KAAK;MACjBzD,QAAQ,CAAClD,IAAI,CAAC3D,CAAC,CAAC;MAChBA,CAAC,IAAIoK,MAAM,CAACC,IAAI;MAChBrK,CAAC,IAAIoK,MAAM,CAACI,WAAW;IAC3B;IACA,IAAI,CAAClK,IAAI,GAAGwJ,YAAY,CAACjG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC7D,CAAC,GAAG,IAAI,CAACE,KAAK,GAAGF,CAAC;IAC/D,IAAI,CAACI,SAAS,GAAG,IAAI;EACzB;EACA0K,IAAIA,CAAA,EAAG;IACH,IAAIpH,EAAE;IACN,MAAMqH,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,OAAO,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC,IAAI,CAAC/K,SAAS,EACf,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB,MAAMhC,QAAQ,GAAG,IAAI,CAACZ,OAAO,CAACY,QAAQ;IACtC,MAAMb,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMF,CAAC,GAAG,IAAI,CAACA,CAAC;IAChB,IAAIC,CAAC;IACL,MAAM6B,SAAS,GAAG,CAAC4B,EAAE,GAAG,IAAI,CAACnB,QAAQ,CAAC,CAAC,CAACT,SAAS,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACrF,MAAM0H,mBAAmB,GAAGtJ,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;IACzD,KAAK,IAAIwE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGvF,QAAQ,EAAEuF,IAAI,EAAE,EAAE;MACxCrG,CAAC,GAAG,IAAI,CAACuD,WAAW,CAAC8C,IAAI,CAAC;MAC1B,IAAI,IAAI,CAACnG,OAAO,CAACqB,UAAU,CAAC8E,IAAI,CAAC,CAAC1D,OAAO,EAAE;QACvCmI,GAAG,CAACM,SAAS,CAAC,CAAC;QACfN,GAAG,CAACO,MAAM,CAACtL,CAAC,EAAEC,CAAC,GAAGmL,mBAAmB,CAAC;QACtCL,GAAG,CAACQ,MAAM,CAACvL,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGmL,mBAAmB,CAAC;QAC9CL,GAAG,CAACS,MAAM,CAAC,CAAC;MAChB;IACJ;IACAT,GAAG,CAACU,UAAU,CAAC,CAAC;IAChB,KAAK,IAAI/I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnC,SAAS,CAACsD,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC5C,MAAMmE,QAAQ,GAAG,IAAI,CAACtG,SAAS,CAACmC,CAAC,CAAC;MAClCmE,QAAQ,CAAC6E,UAAU,CAACX,GAAG,CAAC;MACxBlE,QAAQ,CAACE,QAAQ,CAAC,IAAI,CAAC;MACvBF,QAAQ,CAAC8E,aAAa,CAAC,CAAC;IAC5B;IACA,IAAI,IAAI,CAACnL,OAAO,GAAG,CAAC,EAAE;MAClBuK,GAAG,CAACa,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC;MAC1B,MAAMC,SAAS,GAAGf,GAAG,CAACgB,WAAW,CAAC,EAAE,GAAG,IAAI,CAACvL,OAAO,CAAC,CAACN,KAAK;MAC1DD,CAAC,GAAG,IAAI,CAACwG,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC;MAC9BsE,GAAG,CAACiB,QAAQ,CAAC,EAAE,GAAG,IAAI,CAACxL,OAAO,EAAE,IAAI,CAACR,CAAC,GAAG8L,SAAS,GAAG,CAAC,EAAE7L,CAAC,CAAC;IAC9D;EACJ;EACAgM,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9L,OAAO,CAACW,gBAAgB;EACxC;EACAoL,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/L,OAAO,CAACqB,UAAU;EAClC;EACA2K,gBAAgBA,CAACC,UAAU,EAAE5K,UAAU,EAAE;IACrC,IAAI4K,UAAU,IAAI,IAAI,CAACjM,OAAO,CAACY,QAAQ,IAAIqL,UAAU,GAAG,CAAC,EAAE;MACvD,MAAM,IAAI7M,YAAY,CAAC,kBAAkB,EAAE,+EAA+E,CAAC;IAC/H;IACA,IAAIiC,UAAU,CAACoB,OAAO,KAAKjC,SAAS,EAAE;MAClC,MAAM,IAAIpB,YAAY,CAAC,kBAAkB,EAAE,kEAAkE,CAAC;IAClH;IACA,IAAI,OAAOiC,UAAU,CAACoB,OAAO,KAAK,SAAS,EAAE;MACzC,MAAM,IAAIrD,YAAY,CAAC,kBAAkB,EAAE,0EAA0E,CAAC;IAC1H;IACA,IAAI,CAACY,OAAO,CAACqB,UAAU,CAAC4K,UAAU,CAAC,GAAG5K,UAAU;IAChD,OAAO,IAAI;EACf;EACA6K,iBAAiBA,CAACC,kBAAkB,EAAE;IAClC,IAAIA,kBAAkB,CAACzI,MAAM,KAAK,IAAI,CAAC1D,OAAO,CAACY,QAAQ,EAAE;MACrD,MAAM,IAAIxB,YAAY,CAAC,kBAAkB,EAAE,yFAAyF,CAAC;IACzI;IACA,KAAK,MAAMiC,UAAU,IAAI8K,kBAAkB,EAAE;MACzC,IAAIA,kBAAkB,CAAC9K,UAAU,CAAC,CAACoB,OAAO,KAAKjC,SAAS,EAAE;QACtD2L,kBAAkB,CAAC9K,UAAU,CAAC,GAAG,IAAI,CAACrB,OAAO,CAACqB,UAAU,CAACA,UAAU,CAAC;MACxE;MACA,IAAI,CAACrB,OAAO,CAACqB,UAAU,CAACA,UAAU,CAAC,GAAGZ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACV,OAAO,CAACqB,UAAU,CAACA,UAAU,CAAC,CAAC,EAAE8K,kBAAkB,CAAC9K,UAAU,CAAC,CAAC;IAC/I;IACA,IAAI,CAACrB,OAAO,CAACqB,UAAU,GAAG8K,kBAAkB;IAC5C,OAAO,IAAI;EACf;EACA,OAAOC,kBAAkBA,CAACC,MAAM,EAAE;IAC9B,MAAMC,oBAAoB,GAAI3D,QAAQ,IAAK;MACvC,IAAI4D,SAAS,GAAG,CAAC;MACjBF,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;QACtB,MAAMrM,SAAS,GAAGqM,KAAK,CAACvI,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,EAAEwE,QAAQ,CAAC;QAC3E,IAAIvI,SAAS,CAACsD,MAAM,GAAG,CAAC,IAAItD,SAAS,CAAC,CAAC,CAAC,CAACwD,IAAI,CAAC,CAAC,GAAG2I,SAAS,EACvDA,SAAS,GAAGnM,SAAS,CAAC,CAAC,CAAC,CAACwD,IAAI,CAAC,CAAC;MACvC,CAAC,CAAC;MACF,IAAI8I,OAAO,GAAG,CAAC;MACfL,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;QACtBC,OAAO,GAAG,CAAC;QACX,MAAMtM,SAAS,GAAGqM,KAAK,CAACvI,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,EAAEwE,QAAQ,CAAC;QAC3EvI,SAAS,CAACoM,OAAO,CAAE9F,QAAQ,IAAK;UAC5B,IAAI6F,SAAS,GAAG7F,QAAQ,CAAC9C,IAAI,CAAC,CAAC,GAAG8I,OAAO,EACrCA,OAAO,GAAGH,SAAS,GAAG7F,QAAQ,CAAC9C,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC;QACF,MAAM+I,YAAY,GAAGF,KAAK,CAACvI,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,CAAC;QACpE,IAAIyI,OAAO,GAAG,KAAK;QACnBD,YAAY,CAACH,OAAO,CAAE9F,QAAQ,IAAK;UAC/B,IAAIA,QAAQ,CAACsC,WAAW,CAAC,CAAC,KAAKL,QAAQ,EACnCiE,OAAO,GAAG,IAAI;UAClB,IAAIA,OAAO,IAAIF,OAAO,GAAG,CAAC,EACtBhG,QAAQ,CAAClD,IAAI,CAACkD,QAAQ,CAAC9C,IAAI,CAAC,CAAC,GAAG8I,OAAO,CAAC;QAChD,CAAC,CAAC;QACFD,KAAK,CAAC9J,aAAa,CAAC8J,KAAK,CAAC5J,aAAa,CAAC,CAAC,GAAG6J,OAAO,CAAC;MACxD,CAAC,CAAC;IACN,CAAC;IACDL,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACtB,IAAI,CAACA,KAAK,CAACxM,SAAS,EAChBwM,KAAK,CAAC7J,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC;IACF0J,oBAAoB,CAAC,MAAM,CAAC;IAC5BA,oBAAoB,CAAC,cAAc,CAAC;IACpCA,oBAAoB,CAAC,eAAe,CAAC;IACrC,IAAIO,IAAI,GAAG,CAAC;IACZR,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACtB,IAAIA,KAAK,CAAC5J,aAAa,CAAC,CAAC,GAAGgK,IAAI,EAC5BA,IAAI,GAAGJ,KAAK,CAAC5J,aAAa,CAAC,CAAC;IACpC,CAAC,CAAC;IACFwJ,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACtBA,KAAK,CAAC9J,aAAa,CAACkK,IAAI,CAAC;IAC7B,CAAC,CAAC;IACFA,IAAI,GAAG,CAAC;IACRR,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACtB,MAAMrM,SAAS,GAAGqM,KAAK,CAACvI,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,EAAE,SAAS,CAAC;MAC5E/D,SAAS,CAACoM,OAAO,CAAE9F,QAAQ,IAAK;QAC5B,IAAIA,QAAQ,CAACnC,OAAO,CAAC,CAAC,KAAK7F,WAAW,CAAC8F,YAAY,EAC/C,IAAIkC,QAAQ,CAAC9C,IAAI,CAAC,CAAC,GAAGiJ,IAAI,EACtBA,IAAI,GAAGnG,QAAQ,CAAC9C,IAAI,CAAC,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,CAAC;IACFyI,MAAM,CAACG,OAAO,CAAEC,KAAK,IAAK;MACtB,MAAMrM,SAAS,GAAGqM,KAAK,CAACvI,YAAY,CAACvF,qBAAqB,CAACwF,KAAK,EAAE,SAAS,CAAC;MAC5E/D,SAAS,CAACoM,OAAO,CAAE9F,QAAQ,IAAK;QAC5B,IAAIA,QAAQ,CAACnC,OAAO,CAAC,CAAC,KAAK7F,WAAW,CAAC8F,YAAY,EAC/CkC,QAAQ,CAAClD,IAAI,CAACqJ,IAAI,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}