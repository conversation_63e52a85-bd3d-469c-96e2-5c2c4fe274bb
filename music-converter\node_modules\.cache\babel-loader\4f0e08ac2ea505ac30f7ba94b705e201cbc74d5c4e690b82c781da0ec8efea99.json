{"ast": null, "code": "import { Fraction } from './fraction.js';\nimport { RuntimeError, sumArray } from './util.js';\nexport class TickContext {\n  static getNextContext(tContext) {\n    const contexts = tContext.tContexts;\n    const index = contexts.indexOf(tContext);\n    if (index + 1 < contexts.length) return contexts[index + 1];\n  }\n  constructor(options) {\n    var _a;\n    this.preFormatted = false;\n    this.postFormatted = false;\n    this.tickID = (_a = options === null || options === void 0 ? void 0 : options.tickID) !== null && _a !== void 0 ? _a : 0;\n    this.currentTick = new Fraction(0, 1);\n    this.maxTicks = new Fraction(0, 1);\n    this.maxTickable = undefined;\n    this.minTicks = undefined;\n    this.minTickable = undefined;\n    this.padding = 1;\n    this.x = 0;\n    this.xBase = 0;\n    this.xOffset = 0;\n    this.tickables = [];\n    this.tickablesByVoice = {};\n    this.notePx = 0;\n    this.glyphPx = 0;\n    this.leftDisplacedHeadPx = 0;\n    this.rightDisplacedHeadPx = 0;\n    this.modLeftPx = 0;\n    this.modRightPx = 0;\n    this.totalLeftPx = 0;\n    this.totalRightPx = 0;\n    this.tContexts = [];\n    this.width = 0;\n    this.formatterMetrics = {\n      freedom: {\n        left: 0,\n        right: 0\n      }\n    };\n  }\n  getTickID() {\n    return this.tickID;\n  }\n  getX() {\n    return this.x;\n  }\n  setX(x) {\n    this.x = x;\n    this.xBase = x;\n    this.xOffset = 0;\n    return this;\n  }\n  getXBase() {\n    return this.xBase;\n  }\n  setXBase(xBase) {\n    this.xBase = xBase;\n    this.x = xBase + this.xOffset;\n  }\n  getXOffset() {\n    return this.xOffset;\n  }\n  setXOffset(xOffset) {\n    this.xOffset = xOffset;\n    this.x = this.xBase + xOffset;\n  }\n  getWidth() {\n    return this.width + this.padding * 2;\n  }\n  setPadding(padding) {\n    this.padding = padding;\n    return this;\n  }\n  getMaxTicks() {\n    return this.maxTicks;\n  }\n  getMinTicks() {\n    return this.minTicks;\n  }\n  getMaxTickable() {\n    return this.maxTickable;\n  }\n  getMinTickable() {\n    return this.minTickable;\n  }\n  getTickables() {\n    return this.tickables;\n  }\n  getTickableForVoice(voiceIndex) {\n    return this.tickablesByVoice[voiceIndex];\n  }\n  getTickablesByVoice() {\n    return this.tickablesByVoice;\n  }\n  getCenterAlignedTickables() {\n    return this.tickables.filter(tickable => tickable.isCenterAligned());\n  }\n  getMetrics() {\n    const {\n      width,\n      glyphPx,\n      notePx,\n      leftDisplacedHeadPx,\n      rightDisplacedHeadPx,\n      modLeftPx,\n      modRightPx,\n      totalLeftPx,\n      totalRightPx\n    } = this;\n    return {\n      width,\n      glyphPx,\n      notePx,\n      leftDisplacedHeadPx,\n      rightDisplacedHeadPx,\n      modLeftPx,\n      modRightPx,\n      totalLeftPx,\n      totalRightPx\n    };\n  }\n  getCurrentTick() {\n    return this.currentTick;\n  }\n  setCurrentTick(tick) {\n    this.currentTick = tick;\n    this.preFormatted = false;\n  }\n  addTickable(tickable, voiceIndex) {\n    if (!tickable) {\n      throw new RuntimeError('BadArgument', 'Invalid tickable added.');\n    }\n    if (!tickable.shouldIgnoreTicks()) {\n      const ticks = tickable.getTicks();\n      if (ticks.greaterThan(this.maxTicks)) {\n        this.maxTicks = ticks.clone();\n        this.maxTickable = tickable;\n      }\n      if (this.minTicks === undefined) {\n        this.minTicks = ticks.clone();\n        this.minTickable = tickable;\n      } else if (ticks.lessThan(this.minTicks)) {\n        this.minTicks = ticks.clone();\n        this.minTickable = tickable;\n      }\n    }\n    tickable.setTickContext(this);\n    this.tickables.push(tickable);\n    this.tickablesByVoice[voiceIndex !== null && voiceIndex !== void 0 ? voiceIndex : 0] = tickable;\n    this.preFormatted = false;\n    return this;\n  }\n  preFormat() {\n    var _a;\n    if (this.preFormatted) return this;\n    for (let i = 0; i < this.tickables.length; ++i) {\n      const tickable = this.tickables[i];\n      tickable.preFormat();\n      const metrics = tickable.getMetrics();\n      this.leftDisplacedHeadPx = Math.max(this.leftDisplacedHeadPx, metrics.leftDisplacedHeadPx);\n      this.rightDisplacedHeadPx = Math.max(this.rightDisplacedHeadPx, metrics.rightDisplacedHeadPx);\n      this.notePx = Math.max(this.notePx, metrics.notePx);\n      this.glyphPx = Math.max(this.glyphPx, (_a = metrics.glyphWidth) !== null && _a !== void 0 ? _a : 0);\n      this.modLeftPx = Math.max(this.modLeftPx, metrics.modLeftPx);\n      this.modRightPx = Math.max(this.modRightPx, metrics.modRightPx);\n      this.totalLeftPx = Math.max(this.totalLeftPx, metrics.modLeftPx + metrics.leftDisplacedHeadPx);\n      this.totalRightPx = Math.max(this.totalRightPx, metrics.modRightPx + metrics.rightDisplacedHeadPx);\n      this.width = this.notePx + this.totalLeftPx + this.totalRightPx;\n    }\n    return this;\n  }\n  postFormat() {\n    if (this.postFormatted) return this;\n    this.postFormatted = true;\n    return this;\n  }\n  getFormatterMetrics() {\n    return this.formatterMetrics;\n  }\n  move(shift, prev, next) {\n    this.setX(this.getX() + shift);\n    this.getFormatterMetrics().freedom.left += shift;\n    this.getFormatterMetrics().freedom.right -= shift;\n    if (prev) prev.getFormatterMetrics().freedom.right += shift;\n    if (next) next.getFormatterMetrics().freedom.left -= shift;\n  }\n  getDeviationCost() {\n    return sumArray(this.tickables.map(t => t.getFormatterMetrics().space.deviation));\n  }\n  getAverageDeviationCost() {\n    if (!this.tickables.length) return 0;\n    return this.getDeviationCost() / this.tickables.length;\n  }\n}", "map": {"version": 3, "names": ["Fraction", "RuntimeError", "sumArray", "TickContext", "getNextContext", "tContext", "contexts", "tContexts", "index", "indexOf", "length", "constructor", "options", "_a", "preFormatted", "postFormatted", "tickID", "currentTick", "maxTicks", "maxTickable", "undefined", "minTicks", "minTickable", "padding", "x", "xBase", "xOffset", "tickables", "tickablesByVoice", "notePx", "glyphPx", "leftDisplacedHeadPx", "rightDisplacedHeadPx", "modLeftPx", "modRightPx", "totalLeftPx", "totalRightPx", "width", "formatterMetrics", "freedom", "left", "right", "getTickID", "getX", "setX", "getXBase", "setXBase", "getXOffset", "setXOffset", "getWidth", "setPadding", "getMaxTicks", "getMinTicks", "getMaxTickable", "getMinTickable", "getTickables", "getTickableForVoice", "voiceIndex", "getTickablesByVoice", "getCenterAlignedTickables", "filter", "tickable", "isCenterAligned", "getMetrics", "getCurrentTick", "setCurrentTick", "tick", "addTickable", "shouldIgnoreTicks", "ticks", "getTicks", "greaterThan", "clone", "lessThan", "setTickContext", "push", "preFormat", "i", "metrics", "Math", "max", "glyphWidth", "postFormat", "getFormatterMetrics", "move", "shift", "prev", "next", "getDeviationCost", "map", "t", "space", "deviation", "getAverageDeviationCost"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tickcontext.js"], "sourcesContent": ["import { Fraction } from './fraction.js';\nimport { RuntimeError, sumArray } from './util.js';\nexport class TickContext {\n    static getNextContext(tContext) {\n        const contexts = tContext.tContexts;\n        const index = contexts.indexOf(tContext);\n        if (index + 1 < contexts.length)\n            return contexts[index + 1];\n    }\n    constructor(options) {\n        var _a;\n        this.preFormatted = false;\n        this.postFormatted = false;\n        this.tickID = (_a = options === null || options === void 0 ? void 0 : options.tickID) !== null && _a !== void 0 ? _a : 0;\n        this.currentTick = new Fraction(0, 1);\n        this.maxTicks = new Fraction(0, 1);\n        this.maxTickable = undefined;\n        this.minTicks = undefined;\n        this.minTickable = undefined;\n        this.padding = 1;\n        this.x = 0;\n        this.xBase = 0;\n        this.xOffset = 0;\n        this.tickables = [];\n        this.tickablesByVoice = {};\n        this.notePx = 0;\n        this.glyphPx = 0;\n        this.leftDisplacedHeadPx = 0;\n        this.rightDisplacedHeadPx = 0;\n        this.modLeftPx = 0;\n        this.modRightPx = 0;\n        this.totalLeftPx = 0;\n        this.totalRightPx = 0;\n        this.tContexts = [];\n        this.width = 0;\n        this.formatterMetrics = {\n            freedom: { left: 0, right: 0 },\n        };\n    }\n    getTickID() {\n        return this.tickID;\n    }\n    getX() {\n        return this.x;\n    }\n    setX(x) {\n        this.x = x;\n        this.xBase = x;\n        this.xOffset = 0;\n        return this;\n    }\n    getXBase() {\n        return this.xBase;\n    }\n    setXBase(xBase) {\n        this.xBase = xBase;\n        this.x = xBase + this.xOffset;\n    }\n    getXOffset() {\n        return this.xOffset;\n    }\n    setXOffset(xOffset) {\n        this.xOffset = xOffset;\n        this.x = this.xBase + xOffset;\n    }\n    getWidth() {\n        return this.width + this.padding * 2;\n    }\n    setPadding(padding) {\n        this.padding = padding;\n        return this;\n    }\n    getMaxTicks() {\n        return this.maxTicks;\n    }\n    getMinTicks() {\n        return this.minTicks;\n    }\n    getMaxTickable() {\n        return this.maxTickable;\n    }\n    getMinTickable() {\n        return this.minTickable;\n    }\n    getTickables() {\n        return this.tickables;\n    }\n    getTickableForVoice(voiceIndex) {\n        return this.tickablesByVoice[voiceIndex];\n    }\n    getTickablesByVoice() {\n        return this.tickablesByVoice;\n    }\n    getCenterAlignedTickables() {\n        return this.tickables.filter((tickable) => tickable.isCenterAligned());\n    }\n    getMetrics() {\n        const { width, glyphPx, notePx, leftDisplacedHeadPx, rightDisplacedHeadPx, modLeftPx, modRightPx, totalLeftPx, totalRightPx, } = this;\n        return {\n            width,\n            glyphPx,\n            notePx,\n            leftDisplacedHeadPx,\n            rightDisplacedHeadPx,\n            modLeftPx,\n            modRightPx,\n            totalLeftPx,\n            totalRightPx,\n        };\n    }\n    getCurrentTick() {\n        return this.currentTick;\n    }\n    setCurrentTick(tick) {\n        this.currentTick = tick;\n        this.preFormatted = false;\n    }\n    addTickable(tickable, voiceIndex) {\n        if (!tickable) {\n            throw new RuntimeError('BadArgument', 'Invalid tickable added.');\n        }\n        if (!tickable.shouldIgnoreTicks()) {\n            const ticks = tickable.getTicks();\n            if (ticks.greaterThan(this.maxTicks)) {\n                this.maxTicks = ticks.clone();\n                this.maxTickable = tickable;\n            }\n            if (this.minTicks === undefined) {\n                this.minTicks = ticks.clone();\n                this.minTickable = tickable;\n            }\n            else if (ticks.lessThan(this.minTicks)) {\n                this.minTicks = ticks.clone();\n                this.minTickable = tickable;\n            }\n        }\n        tickable.setTickContext(this);\n        this.tickables.push(tickable);\n        this.tickablesByVoice[voiceIndex !== null && voiceIndex !== void 0 ? voiceIndex : 0] = tickable;\n        this.preFormatted = false;\n        return this;\n    }\n    preFormat() {\n        var _a;\n        if (this.preFormatted)\n            return this;\n        for (let i = 0; i < this.tickables.length; ++i) {\n            const tickable = this.tickables[i];\n            tickable.preFormat();\n            const metrics = tickable.getMetrics();\n            this.leftDisplacedHeadPx = Math.max(this.leftDisplacedHeadPx, metrics.leftDisplacedHeadPx);\n            this.rightDisplacedHeadPx = Math.max(this.rightDisplacedHeadPx, metrics.rightDisplacedHeadPx);\n            this.notePx = Math.max(this.notePx, metrics.notePx);\n            this.glyphPx = Math.max(this.glyphPx, (_a = metrics.glyphWidth) !== null && _a !== void 0 ? _a : 0);\n            this.modLeftPx = Math.max(this.modLeftPx, metrics.modLeftPx);\n            this.modRightPx = Math.max(this.modRightPx, metrics.modRightPx);\n            this.totalLeftPx = Math.max(this.totalLeftPx, metrics.modLeftPx + metrics.leftDisplacedHeadPx);\n            this.totalRightPx = Math.max(this.totalRightPx, metrics.modRightPx + metrics.rightDisplacedHeadPx);\n            this.width = this.notePx + this.totalLeftPx + this.totalRightPx;\n        }\n        return this;\n    }\n    postFormat() {\n        if (this.postFormatted)\n            return this;\n        this.postFormatted = true;\n        return this;\n    }\n    getFormatterMetrics() {\n        return this.formatterMetrics;\n    }\n    move(shift, prev, next) {\n        this.setX(this.getX() + shift);\n        this.getFormatterMetrics().freedom.left += shift;\n        this.getFormatterMetrics().freedom.right -= shift;\n        if (prev)\n            prev.getFormatterMetrics().freedom.right += shift;\n        if (next)\n            next.getFormatterMetrics().freedom.left -= shift;\n    }\n    getDeviationCost() {\n        return sumArray(this.tickables.map((t) => t.getFormatterMetrics().space.deviation));\n    }\n    getAverageDeviationCost() {\n        if (!this.tickables.length)\n            return 0;\n        return this.getDeviationCost() / this.tickables.length;\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,WAAW;AAClD,OAAO,MAAMC,WAAW,CAAC;EACrB,OAAOC,cAAcA,CAACC,QAAQ,EAAE;IAC5B,MAAMC,QAAQ,GAAGD,QAAQ,CAACE,SAAS;IACnC,MAAMC,KAAK,GAAGF,QAAQ,CAACG,OAAO,CAACJ,QAAQ,CAAC;IACxC,IAAIG,KAAK,GAAG,CAAC,GAAGF,QAAQ,CAACI,MAAM,EAC3B,OAAOJ,QAAQ,CAACE,KAAK,GAAG,CAAC,CAAC;EAClC;EACAG,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIC,EAAE;IACN,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,MAAM,GAAG,CAACH,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,MAAM,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACxH,IAAI,CAACI,WAAW,GAAG,IAAIjB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,IAAI,CAACkB,QAAQ,GAAG,IAAIlB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC,IAAI,CAACmB,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACC,QAAQ,GAAGD,SAAS;IACzB,IAAI,CAACE,WAAW,GAAGF,SAAS;IAC5B,IAAI,CAACG,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC7B,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC8B,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,gBAAgB,GAAG;MACpBC,OAAO,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE;IACjC,CAAC;EACL;EACAC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC1B,MAAM;EACtB;EACA2B,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACnB,CAAC;EACjB;EACAoB,IAAIA,CAACpB,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,KAAK,GAAGD,CAAC;IACd,IAAI,CAACE,OAAO,GAAG,CAAC;IAChB,OAAO,IAAI;EACf;EACAmB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpB,KAAK;EACrB;EACAqB,QAAQA,CAACrB,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,CAAC,GAAGC,KAAK,GAAG,IAAI,CAACC,OAAO;EACjC;EACAqB,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrB,OAAO;EACvB;EACAsB,UAAUA,CAACtB,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,CAAC,GAAG,IAAI,CAACC,KAAK,GAAGC,OAAO;EACjC;EACAuB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACd,OAAO,GAAG,CAAC;EACxC;EACA2B,UAAUA,CAAC3B,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACA4B,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjC,QAAQ;EACxB;EACAkC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/B,QAAQ;EACxB;EACAgC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClC,WAAW;EAC3B;EACAmC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChC,WAAW;EAC3B;EACAiC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5B,SAAS;EACzB;EACA6B,mBAAmBA,CAACC,UAAU,EAAE;IAC5B,OAAO,IAAI,CAAC7B,gBAAgB,CAAC6B,UAAU,CAAC;EAC5C;EACAC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9B,gBAAgB;EAChC;EACA+B,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAAChC,SAAS,CAACiC,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,eAAe,CAAC,CAAC,CAAC;EAC1E;EACAC,UAAUA,CAAA,EAAG;IACT,MAAM;MAAE1B,KAAK;MAAEP,OAAO;MAAED,MAAM;MAAEE,mBAAmB;MAAEC,oBAAoB;MAAEC,SAAS;MAAEC,UAAU;MAAEC,WAAW;MAAEC;IAAc,CAAC,GAAG,IAAI;IACrI,OAAO;MACHC,KAAK;MACLP,OAAO;MACPD,MAAM;MACNE,mBAAmB;MACnBC,oBAAoB;MACpBC,SAAS;MACTC,UAAU;MACVC,WAAW;MACXC;IACJ,CAAC;EACL;EACA4B,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/C,WAAW;EAC3B;EACAgD,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACjD,WAAW,GAAGiD,IAAI;IACvB,IAAI,CAACpD,YAAY,GAAG,KAAK;EAC7B;EACAqD,WAAWA,CAACN,QAAQ,EAAEJ,UAAU,EAAE;IAC9B,IAAI,CAACI,QAAQ,EAAE;MACX,MAAM,IAAI5D,YAAY,CAAC,aAAa,EAAE,yBAAyB,CAAC;IACpE;IACA,IAAI,CAAC4D,QAAQ,CAACO,iBAAiB,CAAC,CAAC,EAAE;MAC/B,MAAMC,KAAK,GAAGR,QAAQ,CAACS,QAAQ,CAAC,CAAC;MACjC,IAAID,KAAK,CAACE,WAAW,CAAC,IAAI,CAACrD,QAAQ,CAAC,EAAE;QAClC,IAAI,CAACA,QAAQ,GAAGmD,KAAK,CAACG,KAAK,CAAC,CAAC;QAC7B,IAAI,CAACrD,WAAW,GAAG0C,QAAQ;MAC/B;MACA,IAAI,IAAI,CAACxC,QAAQ,KAAKD,SAAS,EAAE;QAC7B,IAAI,CAACC,QAAQ,GAAGgD,KAAK,CAACG,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAClD,WAAW,GAAGuC,QAAQ;MAC/B,CAAC,MACI,IAAIQ,KAAK,CAACI,QAAQ,CAAC,IAAI,CAACpD,QAAQ,CAAC,EAAE;QACpC,IAAI,CAACA,QAAQ,GAAGgD,KAAK,CAACG,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAClD,WAAW,GAAGuC,QAAQ;MAC/B;IACJ;IACAA,QAAQ,CAACa,cAAc,CAAC,IAAI,CAAC;IAC7B,IAAI,CAAC/C,SAAS,CAACgD,IAAI,CAACd,QAAQ,CAAC;IAC7B,IAAI,CAACjC,gBAAgB,CAAC6B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,CAAC,GAAGI,QAAQ;IAC/F,IAAI,CAAC/C,YAAY,GAAG,KAAK;IACzB,OAAO,IAAI;EACf;EACA8D,SAASA,CAAA,EAAG;IACR,IAAI/D,EAAE;IACN,IAAI,IAAI,CAACC,YAAY,EACjB,OAAO,IAAI;IACf,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAClD,SAAS,CAACjB,MAAM,EAAE,EAAEmE,CAAC,EAAE;MAC5C,MAAMhB,QAAQ,GAAG,IAAI,CAAClC,SAAS,CAACkD,CAAC,CAAC;MAClChB,QAAQ,CAACe,SAAS,CAAC,CAAC;MACpB,MAAME,OAAO,GAAGjB,QAAQ,CAACE,UAAU,CAAC,CAAC;MACrC,IAAI,CAAChC,mBAAmB,GAAGgD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACjD,mBAAmB,EAAE+C,OAAO,CAAC/C,mBAAmB,CAAC;MAC1F,IAAI,CAACC,oBAAoB,GAAG+C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAChD,oBAAoB,EAAE8C,OAAO,CAAC9C,oBAAoB,CAAC;MAC7F,IAAI,CAACH,MAAM,GAAGkD,IAAI,CAACC,GAAG,CAAC,IAAI,CAACnD,MAAM,EAAEiD,OAAO,CAACjD,MAAM,CAAC;MACnD,IAAI,CAACC,OAAO,GAAGiD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,OAAO,EAAE,CAACjB,EAAE,GAAGiE,OAAO,CAACG,UAAU,MAAM,IAAI,IAAIpE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;MACnG,IAAI,CAACoB,SAAS,GAAG8C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,SAAS,EAAE6C,OAAO,CAAC7C,SAAS,CAAC;MAC5D,IAAI,CAACC,UAAU,GAAG6C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9C,UAAU,EAAE4C,OAAO,CAAC5C,UAAU,CAAC;MAC/D,IAAI,CAACC,WAAW,GAAG4C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7C,WAAW,EAAE2C,OAAO,CAAC7C,SAAS,GAAG6C,OAAO,CAAC/C,mBAAmB,CAAC;MAC9F,IAAI,CAACK,YAAY,GAAG2C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5C,YAAY,EAAE0C,OAAO,CAAC5C,UAAU,GAAG4C,OAAO,CAAC9C,oBAAoB,CAAC;MAClG,IAAI,CAACK,KAAK,GAAG,IAAI,CAACR,MAAM,GAAG,IAAI,CAACM,WAAW,GAAG,IAAI,CAACC,YAAY;IACnE;IACA,OAAO,IAAI;EACf;EACA8C,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACnE,aAAa,EAClB,OAAO,IAAI;IACf,IAAI,CAACA,aAAa,GAAG,IAAI;IACzB,OAAO,IAAI;EACf;EACAoE,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7C,gBAAgB;EAChC;EACA8C,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAE;IACpB,IAAI,CAAC3C,IAAI,CAAC,IAAI,CAACD,IAAI,CAAC,CAAC,GAAG0C,KAAK,CAAC;IAC9B,IAAI,CAACF,mBAAmB,CAAC,CAAC,CAAC5C,OAAO,CAACC,IAAI,IAAI6C,KAAK;IAChD,IAAI,CAACF,mBAAmB,CAAC,CAAC,CAAC5C,OAAO,CAACE,KAAK,IAAI4C,KAAK;IACjD,IAAIC,IAAI,EACJA,IAAI,CAACH,mBAAmB,CAAC,CAAC,CAAC5C,OAAO,CAACE,KAAK,IAAI4C,KAAK;IACrD,IAAIE,IAAI,EACJA,IAAI,CAACJ,mBAAmB,CAAC,CAAC,CAAC5C,OAAO,CAACC,IAAI,IAAI6C,KAAK;EACxD;EACAG,gBAAgBA,CAAA,EAAG;IACf,OAAOtF,QAAQ,CAAC,IAAI,CAACyB,SAAS,CAAC8D,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACP,mBAAmB,CAAC,CAAC,CAACQ,KAAK,CAACC,SAAS,CAAC,CAAC;EACvF;EACAC,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAClE,SAAS,CAACjB,MAAM,EACtB,OAAO,CAAC;IACZ,OAAO,IAAI,CAAC8E,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC7D,SAAS,CAACjB,MAAM;EAC1D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}