{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nfunction drawBoldDoubleLine(ctx, type, topX, topY, botY) {\n  if (type !== StaveConnector.type.BOLD_DOUBLE_LEFT && type !== StaveConnector.type.BOLD_DOUBLE_RIGHT) {\n    throw new RuntimeError('InvalidConnector', 'A REPEAT_BEGIN or REPEAT_END type must be provided.');\n  }\n  let xShift = 3;\n  let variableWidth = 3.5;\n  const thickLineOffset = 2;\n  if (type === StaveConnector.type.BOLD_DOUBLE_RIGHT) {\n    xShift = -5;\n    variableWidth = 3;\n  }\n  ctx.fillRect(topX + xShift, topY, 1, botY - topY);\n  ctx.fillRect(topX - thickLineOffset, topY, variableWidth, botY - topY);\n}\nexport class StaveConnector extends Element {\n  static get CATEGORY() {\n    return \"StaveConnector\";\n  }\n  constructor(topStave, bottomStave) {\n    super();\n    this.thickness = Tables.STAVE_LINE_THICKNESS;\n    this.topStave = topStave;\n    this.bottomStave = bottomStave;\n    this.type = StaveConnector.type.DOUBLE;\n    this.xShift = 0;\n    this.texts = [];\n  }\n  setType(type) {\n    const newType = typeof type === 'string' ? StaveConnector.typeString[type] : type;\n    if (Object.values(StaveConnector.type).includes(newType)) {\n      this.type = newType;\n    }\n    return this;\n  }\n  getType() {\n    return this.type;\n  }\n  setText(text, options = {}) {\n    var _a, _b;\n    const textElement = new Element('StaveConnector.text');\n    textElement.setText(text);\n    textElement.setXShift((_a = options.shiftX) !== null && _a !== void 0 ? _a : 0);\n    textElement.setYShift((_b = options.shiftY) !== null && _b !== void 0 ? _b : 0);\n    this.texts.push(textElement);\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    let topY = this.topStave.getYForLine(0);\n    let botY = this.bottomStave.getYForLine(this.bottomStave.getNumLines() - 1) + this.thickness;\n    let width = 3;\n    let topX = this.topStave.getX();\n    const isRightSidedConnector = this.type === StaveConnector.type.SINGLE_RIGHT || this.type === StaveConnector.type.BOLD_DOUBLE_RIGHT || this.type === StaveConnector.type.THIN_DOUBLE;\n    if (isRightSidedConnector) {\n      topX = this.topStave.getX() + this.topStave.getWidth();\n    }\n    let attachmentHeight = botY - topY;\n    const element = new Element();\n    switch (this.type) {\n      case StaveConnector.type.SINGLE:\n        width = 1;\n        break;\n      case StaveConnector.type.SINGLE_LEFT:\n        width = 1;\n        break;\n      case StaveConnector.type.SINGLE_RIGHT:\n        width = 1;\n        break;\n      case StaveConnector.type.DOUBLE:\n        topX -= 5;\n        topY -= this.thickness;\n        attachmentHeight += 0.5;\n        break;\n      case StaveConnector.type.BRACE:\n        {\n          width = 12;\n          const x1 = this.topStave.getX() - 2 + this.xShift;\n          const y1 = topY;\n          const x3 = x1;\n          const y3 = botY;\n          const x2 = x1 - width;\n          const y2 = y1 + attachmentHeight / 2.0;\n          const cpx1 = x2 - 0.9 * width;\n          const cpy1 = y1 + 0.2 * attachmentHeight;\n          const cpx2 = x1 + 1.1 * width;\n          const cpy2 = y2 - 0.135 * attachmentHeight;\n          const cpx3 = cpx2;\n          const cpy3 = y2 + 0.135 * attachmentHeight;\n          const cpx4 = cpx1;\n          const cpy4 = y3 - 0.2 * attachmentHeight;\n          const cpx5 = x2 - width;\n          const cpy5 = cpy4;\n          const cpx6 = x1 + 0.4 * width;\n          const cpy6 = y2 + 0.135 * attachmentHeight;\n          const cpx7 = cpx6;\n          const cpy7 = y2 - 0.135 * attachmentHeight;\n          const cpx8 = cpx5;\n          const cpy8 = cpy1;\n          ctx.beginPath();\n          ctx.moveTo(x1, y1);\n          ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, x2, y2);\n          ctx.bezierCurveTo(cpx3, cpy3, cpx4, cpy4, x3, y3);\n          ctx.bezierCurveTo(cpx5, cpy5, cpx6, cpy6, x2, y2);\n          ctx.bezierCurveTo(cpx7, cpy7, cpx8, cpy8, x1, y1);\n          ctx.fill();\n          ctx.stroke();\n          break;\n        }\n      case StaveConnector.type.BRACKET:\n        topY -= 6;\n        botY += 6;\n        attachmentHeight = botY - topY;\n        element.setText(Glyphs.bracketTop);\n        element.renderText(ctx, topX - 5, topY);\n        element.setText(Glyphs.bracketBottom);\n        element.renderText(ctx, topX - 5, botY);\n        topX -= 5;\n        break;\n      case StaveConnector.type.BOLD_DOUBLE_LEFT:\n        drawBoldDoubleLine(ctx, this.type, topX + this.xShift, topY, botY - this.thickness);\n        break;\n      case StaveConnector.type.BOLD_DOUBLE_RIGHT:\n        drawBoldDoubleLine(ctx, this.type, topX, topY, botY - this.thickness);\n        break;\n      case StaveConnector.type.THIN_DOUBLE:\n        width = 1;\n        attachmentHeight -= this.thickness;\n        break;\n      case StaveConnector.type.NONE:\n        break;\n      default:\n        throw new RuntimeError('InvalidType', `The provided StaveConnector.type (${this.type}) is invalid.`);\n    }\n    if (this.type !== StaveConnector.type.BRACE && this.type !== StaveConnector.type.BOLD_DOUBLE_LEFT && this.type !== StaveConnector.type.BOLD_DOUBLE_RIGHT && this.type !== StaveConnector.type.NONE) {\n      ctx.fillRect(topX, topY, width, attachmentHeight);\n    }\n    if (this.type === StaveConnector.type.THIN_DOUBLE) {\n      ctx.fillRect(topX - 3, topY, width, attachmentHeight);\n    }\n    for (let i = 0; i < this.texts.length; i++) {\n      const textElement = this.texts[i];\n      const x = this.topStave.getX() - textElement.getWidth() - 24;\n      const y = (this.topStave.getYForLine(0) + this.bottomStave.getBottomLineY()) / 2;\n      textElement.renderText(ctx, x, y + 4);\n    }\n  }\n}\nStaveConnector.type = {\n  SINGLE_RIGHT: 0,\n  SINGLE_LEFT: 1,\n  SINGLE: 1,\n  DOUBLE: 2,\n  BRACE: 3,\n  BRACKET: 4,\n  BOLD_DOUBLE_LEFT: 5,\n  BOLD_DOUBLE_RIGHT: 6,\n  THIN_DOUBLE: 7,\n  NONE: 8\n};\nStaveConnector.typeString = {\n  singleRight: StaveConnector.type.SINGLE_RIGHT,\n  singleLeft: StaveConnector.type.SINGLE_LEFT,\n  single: StaveConnector.type.SINGLE,\n  double: StaveConnector.type.DOUBLE,\n  brace: StaveConnector.type.BRACE,\n  bracket: StaveConnector.type.BRACKET,\n  boldDoubleLeft: StaveConnector.type.BOLD_DOUBLE_LEFT,\n  boldDoubleRight: StaveConnector.type.BOLD_DOUBLE_RIGHT,\n  thinDouble: StaveConnector.type.THIN_DOUBLE,\n  none: StaveConnector.type.NONE\n};", "map": {"version": 3, "names": ["Element", "Glyphs", "Tables", "RuntimeError", "drawBoldDoubleLine", "ctx", "type", "topX", "topY", "botY", "StaveConnector", "BOLD_DOUBLE_LEFT", "BOLD_DOUBLE_RIGHT", "xShift", "variableWidth", "thickLineOffset", "fillRect", "CATEGORY", "constructor", "topStave", "bottomStave", "thickness", "STAVE_LINE_THICKNESS", "DOUBLE", "texts", "setType", "newType", "typeString", "Object", "values", "includes", "getType", "setText", "text", "options", "_a", "_b", "textElement", "setXShift", "shiftX", "setYShift", "shiftY", "push", "draw", "checkContext", "setRendered", "getYForLine", "getNumLines", "width", "getX", "isRightSidedConnector", "SINGLE_RIGHT", "THIN_DOUBLE", "getWidth", "attachmentHeight", "element", "SINGLE", "SINGLE_LEFT", "BRACE", "x1", "y1", "x3", "y3", "x2", "y2", "cpx1", "cpy1", "cpx2", "cpy2", "cpx3", "cpy3", "cpx4", "cpy4", "cpx5", "cpy5", "cpx6", "cpy6", "cpx7", "cpy7", "cpx8", "cpy8", "beginPath", "moveTo", "bezierCurveTo", "fill", "stroke", "BRACKET", "bracketTop", "renderText", "bracketBottom", "NONE", "i", "length", "x", "y", "getBottomLineY", "singleRight", "singleLeft", "single", "double", "brace", "bracket", "boldDoubleLeft", "boldDoubleRight", "thinDouble", "none"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/staveconnector.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nfunction drawBoldDoubleLine(ctx, type, topX, topY, botY) {\n    if (type !== StaveConnector.type.BOLD_DOUBLE_LEFT && type !== StaveConnector.type.BOLD_DOUBLE_RIGHT) {\n        throw new RuntimeError('InvalidConnector', 'A REPEAT_BEGIN or REPEAT_END type must be provided.');\n    }\n    let xShift = 3;\n    let variableWidth = 3.5;\n    const thickLineOffset = 2;\n    if (type === StaveConnector.type.BOLD_DOUBLE_RIGHT) {\n        xShift = -5;\n        variableWidth = 3;\n    }\n    ctx.fillRect(topX + xShift, topY, 1, botY - topY);\n    ctx.fillRect(topX - thickLineOffset, topY, variableWidth, botY - topY);\n}\nexport class StaveConnector extends Element {\n    static get CATEGORY() {\n        return \"StaveConnector\";\n    }\n    constructor(topStave, bottomStave) {\n        super();\n        this.thickness = Tables.STAVE_LINE_THICKNESS;\n        this.topStave = topStave;\n        this.bottomStave = bottomStave;\n        this.type = StaveConnector.type.DOUBLE;\n        this.xShift = 0;\n        this.texts = [];\n    }\n    setType(type) {\n        const newType = typeof type === 'string' ? StaveConnector.typeString[type] : type;\n        if (Object.values(StaveConnector.type).includes(newType)) {\n            this.type = newType;\n        }\n        return this;\n    }\n    getType() {\n        return this.type;\n    }\n    setText(text, options = {}) {\n        var _a, _b;\n        const textElement = new Element('StaveConnector.text');\n        textElement.setText(text);\n        textElement.setXShift((_a = options.shiftX) !== null && _a !== void 0 ? _a : 0);\n        textElement.setYShift((_b = options.shiftY) !== null && _b !== void 0 ? _b : 0);\n        this.texts.push(textElement);\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        let topY = this.topStave.getYForLine(0);\n        let botY = this.bottomStave.getYForLine(this.bottomStave.getNumLines() - 1) + this.thickness;\n        let width = 3;\n        let topX = this.topStave.getX();\n        const isRightSidedConnector = this.type === StaveConnector.type.SINGLE_RIGHT ||\n            this.type === StaveConnector.type.BOLD_DOUBLE_RIGHT ||\n            this.type === StaveConnector.type.THIN_DOUBLE;\n        if (isRightSidedConnector) {\n            topX = this.topStave.getX() + this.topStave.getWidth();\n        }\n        let attachmentHeight = botY - topY;\n        const element = new Element();\n        switch (this.type) {\n            case StaveConnector.type.SINGLE:\n                width = 1;\n                break;\n            case StaveConnector.type.SINGLE_LEFT:\n                width = 1;\n                break;\n            case StaveConnector.type.SINGLE_RIGHT:\n                width = 1;\n                break;\n            case StaveConnector.type.DOUBLE:\n                topX -= 5;\n                topY -= this.thickness;\n                attachmentHeight += 0.5;\n                break;\n            case StaveConnector.type.BRACE: {\n                width = 12;\n                const x1 = this.topStave.getX() - 2 + this.xShift;\n                const y1 = topY;\n                const x3 = x1;\n                const y3 = botY;\n                const x2 = x1 - width;\n                const y2 = y1 + attachmentHeight / 2.0;\n                const cpx1 = x2 - 0.9 * width;\n                const cpy1 = y1 + 0.2 * attachmentHeight;\n                const cpx2 = x1 + 1.1 * width;\n                const cpy2 = y2 - 0.135 * attachmentHeight;\n                const cpx3 = cpx2;\n                const cpy3 = y2 + 0.135 * attachmentHeight;\n                const cpx4 = cpx1;\n                const cpy4 = y3 - 0.2 * attachmentHeight;\n                const cpx5 = x2 - width;\n                const cpy5 = cpy4;\n                const cpx6 = x1 + 0.4 * width;\n                const cpy6 = y2 + 0.135 * attachmentHeight;\n                const cpx7 = cpx6;\n                const cpy7 = y2 - 0.135 * attachmentHeight;\n                const cpx8 = cpx5;\n                const cpy8 = cpy1;\n                ctx.beginPath();\n                ctx.moveTo(x1, y1);\n                ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, x2, y2);\n                ctx.bezierCurveTo(cpx3, cpy3, cpx4, cpy4, x3, y3);\n                ctx.bezierCurveTo(cpx5, cpy5, cpx6, cpy6, x2, y2);\n                ctx.bezierCurveTo(cpx7, cpy7, cpx8, cpy8, x1, y1);\n                ctx.fill();\n                ctx.stroke();\n                break;\n            }\n            case StaveConnector.type.BRACKET:\n                topY -= 6;\n                botY += 6;\n                attachmentHeight = botY - topY;\n                element.setText(Glyphs.bracketTop);\n                element.renderText(ctx, topX - 5, topY);\n                element.setText(Glyphs.bracketBottom);\n                element.renderText(ctx, topX - 5, botY);\n                topX -= 5;\n                break;\n            case StaveConnector.type.BOLD_DOUBLE_LEFT:\n                drawBoldDoubleLine(ctx, this.type, topX + this.xShift, topY, botY - this.thickness);\n                break;\n            case StaveConnector.type.BOLD_DOUBLE_RIGHT:\n                drawBoldDoubleLine(ctx, this.type, topX, topY, botY - this.thickness);\n                break;\n            case StaveConnector.type.THIN_DOUBLE:\n                width = 1;\n                attachmentHeight -= this.thickness;\n                break;\n            case StaveConnector.type.NONE:\n                break;\n            default:\n                throw new RuntimeError('InvalidType', `The provided StaveConnector.type (${this.type}) is invalid.`);\n        }\n        if (this.type !== StaveConnector.type.BRACE &&\n            this.type !== StaveConnector.type.BOLD_DOUBLE_LEFT &&\n            this.type !== StaveConnector.type.BOLD_DOUBLE_RIGHT &&\n            this.type !== StaveConnector.type.NONE) {\n            ctx.fillRect(topX, topY, width, attachmentHeight);\n        }\n        if (this.type === StaveConnector.type.THIN_DOUBLE) {\n            ctx.fillRect(topX - 3, topY, width, attachmentHeight);\n        }\n        for (let i = 0; i < this.texts.length; i++) {\n            const textElement = this.texts[i];\n            const x = this.topStave.getX() - textElement.getWidth() - 24;\n            const y = (this.topStave.getYForLine(0) + this.bottomStave.getBottomLineY()) / 2;\n            textElement.renderText(ctx, x, y + 4);\n        }\n    }\n}\nStaveConnector.type = {\n    SINGLE_RIGHT: 0,\n    SINGLE_LEFT: 1,\n    SINGLE: 1,\n    DOUBLE: 2,\n    BRACE: 3,\n    BRACKET: 4,\n    BOLD_DOUBLE_LEFT: 5,\n    BOLD_DOUBLE_RIGHT: 6,\n    THIN_DOUBLE: 7,\n    NONE: 8,\n};\nStaveConnector.typeString = {\n    singleRight: StaveConnector.type.SINGLE_RIGHT,\n    singleLeft: StaveConnector.type.SINGLE_LEFT,\n    single: StaveConnector.type.SINGLE,\n    double: StaveConnector.type.DOUBLE,\n    brace: StaveConnector.type.BRACE,\n    bracket: StaveConnector.type.BRACKET,\n    boldDoubleLeft: StaveConnector.type.BOLD_DOUBLE_LEFT,\n    boldDoubleRight: StaveConnector.type.BOLD_DOUBLE_RIGHT,\n    thinDouble: StaveConnector.type.THIN_DOUBLE,\n    none: StaveConnector.type.NONE,\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,kBAAkBA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACrD,IAAIH,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACK,gBAAgB,IAAIL,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACM,iBAAiB,EAAE;IACjG,MAAM,IAAIT,YAAY,CAAC,kBAAkB,EAAE,qDAAqD,CAAC;EACrG;EACA,IAAIU,MAAM,GAAG,CAAC;EACd,IAAIC,aAAa,GAAG,GAAG;EACvB,MAAMC,eAAe,GAAG,CAAC;EACzB,IAAIT,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACM,iBAAiB,EAAE;IAChDC,MAAM,GAAG,CAAC,CAAC;IACXC,aAAa,GAAG,CAAC;EACrB;EACAT,GAAG,CAACW,QAAQ,CAACT,IAAI,GAAGM,MAAM,EAAEL,IAAI,EAAE,CAAC,EAAEC,IAAI,GAAGD,IAAI,CAAC;EACjDH,GAAG,CAACW,QAAQ,CAACT,IAAI,GAAGQ,eAAe,EAAEP,IAAI,EAAEM,aAAa,EAAEL,IAAI,GAAGD,IAAI,CAAC;AAC1E;AACA,OAAO,MAAME,cAAc,SAASV,OAAO,CAAC;EACxC,WAAWiB,QAAQA,CAAA,EAAG;IAClB,OAAO,gBAAgB;EAC3B;EACAC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAE;IAC/B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,SAAS,GAAGnB,MAAM,CAACoB,oBAAoB;IAC5C,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACd,IAAI,GAAGI,cAAc,CAACJ,IAAI,CAACiB,MAAM;IACtC,IAAI,CAACV,MAAM,GAAG,CAAC;IACf,IAAI,CAACW,KAAK,GAAG,EAAE;EACnB;EACAC,OAAOA,CAACnB,IAAI,EAAE;IACV,MAAMoB,OAAO,GAAG,OAAOpB,IAAI,KAAK,QAAQ,GAAGI,cAAc,CAACiB,UAAU,CAACrB,IAAI,CAAC,GAAGA,IAAI;IACjF,IAAIsB,MAAM,CAACC,MAAM,CAACnB,cAAc,CAACJ,IAAI,CAAC,CAACwB,QAAQ,CAACJ,OAAO,CAAC,EAAE;MACtD,IAAI,CAACpB,IAAI,GAAGoB,OAAO;IACvB;IACA,OAAO,IAAI;EACf;EACAK,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACzB,IAAI;EACpB;EACA0B,OAAOA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,WAAW,GAAG,IAAIrC,OAAO,CAAC,qBAAqB,CAAC;IACtDqC,WAAW,CAACL,OAAO,CAACC,IAAI,CAAC;IACzBI,WAAW,CAACC,SAAS,CAAC,CAACH,EAAE,GAAGD,OAAO,CAACK,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAC/EE,WAAW,CAACG,SAAS,CAAC,CAACJ,EAAE,GAAGF,OAAO,CAACO,MAAM,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAC/E,IAAI,CAACZ,KAAK,CAACkB,IAAI,CAACL,WAAW,CAAC;IAC5B,OAAO,IAAI;EACf;EACAM,IAAIA,CAAA,EAAG;IACH,MAAMtC,GAAG,GAAG,IAAI,CAACuC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIrC,IAAI,GAAG,IAAI,CAACW,QAAQ,CAAC2B,WAAW,CAAC,CAAC,CAAC;IACvC,IAAIrC,IAAI,GAAG,IAAI,CAACW,WAAW,CAAC0B,WAAW,CAAC,IAAI,CAAC1B,WAAW,CAAC2B,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC1B,SAAS;IAC5F,IAAI2B,KAAK,GAAG,CAAC;IACb,IAAIzC,IAAI,GAAG,IAAI,CAACY,QAAQ,CAAC8B,IAAI,CAAC,CAAC;IAC/B,MAAMC,qBAAqB,GAAG,IAAI,CAAC5C,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAAC6C,YAAY,IACxE,IAAI,CAAC7C,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACM,iBAAiB,IACnD,IAAI,CAACN,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAAC8C,WAAW;IACjD,IAAIF,qBAAqB,EAAE;MACvB3C,IAAI,GAAG,IAAI,CAACY,QAAQ,CAAC8B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC9B,QAAQ,CAACkC,QAAQ,CAAC,CAAC;IAC1D;IACA,IAAIC,gBAAgB,GAAG7C,IAAI,GAAGD,IAAI;IAClC,MAAM+C,OAAO,GAAG,IAAIvD,OAAO,CAAC,CAAC;IAC7B,QAAQ,IAAI,CAACM,IAAI;MACb,KAAKI,cAAc,CAACJ,IAAI,CAACkD,MAAM;QAC3BR,KAAK,GAAG,CAAC;QACT;MACJ,KAAKtC,cAAc,CAACJ,IAAI,CAACmD,WAAW;QAChCT,KAAK,GAAG,CAAC;QACT;MACJ,KAAKtC,cAAc,CAACJ,IAAI,CAAC6C,YAAY;QACjCH,KAAK,GAAG,CAAC;QACT;MACJ,KAAKtC,cAAc,CAACJ,IAAI,CAACiB,MAAM;QAC3BhB,IAAI,IAAI,CAAC;QACTC,IAAI,IAAI,IAAI,CAACa,SAAS;QACtBiC,gBAAgB,IAAI,GAAG;QACvB;MACJ,KAAK5C,cAAc,CAACJ,IAAI,CAACoD,KAAK;QAAE;UAC5BV,KAAK,GAAG,EAAE;UACV,MAAMW,EAAE,GAAG,IAAI,CAACxC,QAAQ,CAAC8B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACpC,MAAM;UACjD,MAAM+C,EAAE,GAAGpD,IAAI;UACf,MAAMqD,EAAE,GAAGF,EAAE;UACb,MAAMG,EAAE,GAAGrD,IAAI;UACf,MAAMsD,EAAE,GAAGJ,EAAE,GAAGX,KAAK;UACrB,MAAMgB,EAAE,GAAGJ,EAAE,GAAGN,gBAAgB,GAAG,GAAG;UACtC,MAAMW,IAAI,GAAGF,EAAE,GAAG,GAAG,GAAGf,KAAK;UAC7B,MAAMkB,IAAI,GAAGN,EAAE,GAAG,GAAG,GAAGN,gBAAgB;UACxC,MAAMa,IAAI,GAAGR,EAAE,GAAG,GAAG,GAAGX,KAAK;UAC7B,MAAMoB,IAAI,GAAGJ,EAAE,GAAG,KAAK,GAAGV,gBAAgB;UAC1C,MAAMe,IAAI,GAAGF,IAAI;UACjB,MAAMG,IAAI,GAAGN,EAAE,GAAG,KAAK,GAAGV,gBAAgB;UAC1C,MAAMiB,IAAI,GAAGN,IAAI;UACjB,MAAMO,IAAI,GAAGV,EAAE,GAAG,GAAG,GAAGR,gBAAgB;UACxC,MAAMmB,IAAI,GAAGV,EAAE,GAAGf,KAAK;UACvB,MAAM0B,IAAI,GAAGF,IAAI;UACjB,MAAMG,IAAI,GAAGhB,EAAE,GAAG,GAAG,GAAGX,KAAK;UAC7B,MAAM4B,IAAI,GAAGZ,EAAE,GAAG,KAAK,GAAGV,gBAAgB;UAC1C,MAAMuB,IAAI,GAAGF,IAAI;UACjB,MAAMG,IAAI,GAAGd,EAAE,GAAG,KAAK,GAAGV,gBAAgB;UAC1C,MAAMyB,IAAI,GAAGN,IAAI;UACjB,MAAMO,IAAI,GAAGd,IAAI;UACjB7D,GAAG,CAAC4E,SAAS,CAAC,CAAC;UACf5E,GAAG,CAAC6E,MAAM,CAACvB,EAAE,EAAEC,EAAE,CAAC;UAClBvD,GAAG,CAAC8E,aAAa,CAAClB,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEL,EAAE,EAAEC,EAAE,CAAC;UACjD3D,GAAG,CAAC8E,aAAa,CAACd,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEX,EAAE,EAAEC,EAAE,CAAC;UACjDzD,GAAG,CAAC8E,aAAa,CAACV,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEb,EAAE,EAAEC,EAAE,CAAC;UACjD3D,GAAG,CAAC8E,aAAa,CAACN,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAErB,EAAE,EAAEC,EAAE,CAAC;UACjDvD,GAAG,CAAC+E,IAAI,CAAC,CAAC;UACV/E,GAAG,CAACgF,MAAM,CAAC,CAAC;UACZ;QACJ;MACA,KAAK3E,cAAc,CAACJ,IAAI,CAACgF,OAAO;QAC5B9E,IAAI,IAAI,CAAC;QACTC,IAAI,IAAI,CAAC;QACT6C,gBAAgB,GAAG7C,IAAI,GAAGD,IAAI;QAC9B+C,OAAO,CAACvB,OAAO,CAAC/B,MAAM,CAACsF,UAAU,CAAC;QAClChC,OAAO,CAACiC,UAAU,CAACnF,GAAG,EAAEE,IAAI,GAAG,CAAC,EAAEC,IAAI,CAAC;QACvC+C,OAAO,CAACvB,OAAO,CAAC/B,MAAM,CAACwF,aAAa,CAAC;QACrClC,OAAO,CAACiC,UAAU,CAACnF,GAAG,EAAEE,IAAI,GAAG,CAAC,EAAEE,IAAI,CAAC;QACvCF,IAAI,IAAI,CAAC;QACT;MACJ,KAAKG,cAAc,CAACJ,IAAI,CAACK,gBAAgB;QACrCP,kBAAkB,CAACC,GAAG,EAAE,IAAI,CAACC,IAAI,EAAEC,IAAI,GAAG,IAAI,CAACM,MAAM,EAAEL,IAAI,EAAEC,IAAI,GAAG,IAAI,CAACY,SAAS,CAAC;QACnF;MACJ,KAAKX,cAAc,CAACJ,IAAI,CAACM,iBAAiB;QACtCR,kBAAkB,CAACC,GAAG,EAAE,IAAI,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,GAAG,IAAI,CAACY,SAAS,CAAC;QACrE;MACJ,KAAKX,cAAc,CAACJ,IAAI,CAAC8C,WAAW;QAChCJ,KAAK,GAAG,CAAC;QACTM,gBAAgB,IAAI,IAAI,CAACjC,SAAS;QAClC;MACJ,KAAKX,cAAc,CAACJ,IAAI,CAACoF,IAAI;QACzB;MACJ;QACI,MAAM,IAAIvF,YAAY,CAAC,aAAa,EAAE,qCAAqC,IAAI,CAACG,IAAI,eAAe,CAAC;IAC5G;IACA,IAAI,IAAI,CAACA,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACoD,KAAK,IACvC,IAAI,CAACpD,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACK,gBAAgB,IAClD,IAAI,CAACL,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACM,iBAAiB,IACnD,IAAI,CAACN,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAACoF,IAAI,EAAE;MACxCrF,GAAG,CAACW,QAAQ,CAACT,IAAI,EAAEC,IAAI,EAAEwC,KAAK,EAAEM,gBAAgB,CAAC;IACrD;IACA,IAAI,IAAI,CAAChD,IAAI,KAAKI,cAAc,CAACJ,IAAI,CAAC8C,WAAW,EAAE;MAC/C/C,GAAG,CAACW,QAAQ,CAACT,IAAI,GAAG,CAAC,EAAEC,IAAI,EAAEwC,KAAK,EAAEM,gBAAgB,CAAC;IACzD;IACA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnE,KAAK,CAACoE,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAMtD,WAAW,GAAG,IAAI,CAACb,KAAK,CAACmE,CAAC,CAAC;MACjC,MAAME,CAAC,GAAG,IAAI,CAAC1E,QAAQ,CAAC8B,IAAI,CAAC,CAAC,GAAGZ,WAAW,CAACgB,QAAQ,CAAC,CAAC,GAAG,EAAE;MAC5D,MAAMyC,CAAC,GAAG,CAAC,IAAI,CAAC3E,QAAQ,CAAC2B,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC1B,WAAW,CAAC2E,cAAc,CAAC,CAAC,IAAI,CAAC;MAChF1D,WAAW,CAACmD,UAAU,CAACnF,GAAG,EAAEwF,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;IACzC;EACJ;AACJ;AACApF,cAAc,CAACJ,IAAI,GAAG;EAClB6C,YAAY,EAAE,CAAC;EACfM,WAAW,EAAE,CAAC;EACdD,MAAM,EAAE,CAAC;EACTjC,MAAM,EAAE,CAAC;EACTmC,KAAK,EAAE,CAAC;EACR4B,OAAO,EAAE,CAAC;EACV3E,gBAAgB,EAAE,CAAC;EACnBC,iBAAiB,EAAE,CAAC;EACpBwC,WAAW,EAAE,CAAC;EACdsC,IAAI,EAAE;AACV,CAAC;AACDhF,cAAc,CAACiB,UAAU,GAAG;EACxBqE,WAAW,EAAEtF,cAAc,CAACJ,IAAI,CAAC6C,YAAY;EAC7C8C,UAAU,EAAEvF,cAAc,CAACJ,IAAI,CAACmD,WAAW;EAC3CyC,MAAM,EAAExF,cAAc,CAACJ,IAAI,CAACkD,MAAM;EAClC2C,MAAM,EAAEzF,cAAc,CAACJ,IAAI,CAACiB,MAAM;EAClC6E,KAAK,EAAE1F,cAAc,CAACJ,IAAI,CAACoD,KAAK;EAChC2C,OAAO,EAAE3F,cAAc,CAACJ,IAAI,CAACgF,OAAO;EACpCgB,cAAc,EAAE5F,cAAc,CAACJ,IAAI,CAACK,gBAAgB;EACpD4F,eAAe,EAAE7F,cAAc,CAACJ,IAAI,CAACM,iBAAiB;EACtD4F,UAAU,EAAE9F,cAAc,CAACJ,IAAI,CAAC8C,WAAW;EAC3CqD,IAAI,EAAE/F,cAAc,CAACJ,IAAI,CAACoF;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}