{"ast": null, "code": "export class RenderContext {\n  static get CATEGORY() {\n    return \"RenderContext\";\n  }\n  set font(f) {\n    this.setFont(f);\n  }\n  get font() {\n    return this.getFont();\n  }\n}\nexport function drawDot(ctx, x, y, color = '#F55') {\n  ctx.save();\n  ctx.setFillStyle(color);\n  ctx.beginPath();\n  ctx.arc(x, y, 3, 0, Math.PI * 2, false);\n  ctx.closePath();\n  ctx.fill();\n  ctx.restore();\n}", "map": {"version": 3, "names": ["RenderContext", "CATEGORY", "font", "f", "setFont", "getFont", "drawDot", "ctx", "x", "y", "color", "save", "setFillStyle", "beginPath", "arc", "Math", "PI", "closePath", "fill", "restore"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/rendercontext.js"], "sourcesContent": ["export class RenderContext {\n    static get CATEGORY() {\n        return \"RenderContext\";\n    }\n    set font(f) {\n        this.setFont(f);\n    }\n    get font() {\n        return this.getFont();\n    }\n}\nexport function drawDot(ctx, x, y, color = '#F55') {\n    ctx.save();\n    ctx.setFillStyle(color);\n    ctx.beginPath();\n    ctx.arc(x, y, 3, 0, Math.PI * 2, false);\n    ctx.closePath();\n    ctx.fill();\n    ctx.restore();\n}\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,CAAC;EACvB,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAO,eAAe;EAC1B;EACA,IAAIC,IAAIA,CAACC,CAAC,EAAE;IACR,IAAI,CAACC,OAAO,CAACD,CAAC,CAAC;EACnB;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACG,OAAO,CAAC,CAAC;EACzB;AACJ;AACA,OAAO,SAASC,OAAOA,CAACC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,GAAG,MAAM,EAAE;EAC/CH,GAAG,CAACI,IAAI,CAAC,CAAC;EACVJ,GAAG,CAACK,YAAY,CAACF,KAAK,CAAC;EACvBH,GAAG,CAACM,SAAS,CAAC,CAAC;EACfN,GAAG,CAACO,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEM,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;EACvCT,GAAG,CAACU,SAAS,CAAC,CAAC;EACfV,GAAG,CAACW,IAAI,CAAC,CAAC;EACVX,GAAG,CAACY,OAAO,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}