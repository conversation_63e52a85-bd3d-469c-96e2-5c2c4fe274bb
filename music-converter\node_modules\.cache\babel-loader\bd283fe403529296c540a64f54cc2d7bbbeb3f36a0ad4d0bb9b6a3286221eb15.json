{"ast": null, "code": "import { RuntimeError } from './util.js';\nexport class Music {\n  static get NUM_TONES() {\n    return this.canonicalNotes.length;\n  }\n  static get roots() {\n    return ['c', 'd', 'e', 'f', 'g', 'a', 'b'];\n  }\n  static get rootValues() {\n    return [0, 2, 4, 5, 7, 9, 11];\n  }\n  static get rootIndexes() {\n    return {\n      c: 0,\n      d: 1,\n      e: 2,\n      f: 3,\n      g: 4,\n      a: 5,\n      b: 6\n    };\n  }\n  static get canonicalNotes() {\n    return ['c', 'c#', 'd', 'd#', 'e', 'f', 'f#', 'g', 'g#', 'a', 'a#', 'b'];\n  }\n  static get diatonicIntervals() {\n    return ['unison', 'm2', 'M2', 'm3', 'M3', 'p4', 'dim5', 'p5', 'm6', 'M6', 'b7', 'M7', 'octave'];\n  }\n  static get diatonicAccidentals() {\n    return {\n      unison: {\n        note: 0,\n        accidental: 0\n      },\n      m2: {\n        note: 1,\n        accidental: -1\n      },\n      M2: {\n        note: 1,\n        accidental: 0\n      },\n      m3: {\n        note: 2,\n        accidental: -1\n      },\n      M3: {\n        note: 2,\n        accidental: 0\n      },\n      p4: {\n        note: 3,\n        accidental: 0\n      },\n      dim5: {\n        note: 4,\n        accidental: -1\n      },\n      p5: {\n        note: 4,\n        accidental: 0\n      },\n      m6: {\n        note: 5,\n        accidental: -1\n      },\n      M6: {\n        note: 5,\n        accidental: 0\n      },\n      b7: {\n        note: 6,\n        accidental: -1\n      },\n      M7: {\n        note: 6,\n        accidental: 0\n      },\n      octave: {\n        note: 7,\n        accidental: 0\n      }\n    };\n  }\n  static get intervals() {\n    return {\n      u: 0,\n      unison: 0,\n      m2: 1,\n      b2: 1,\n      min2: 1,\n      S: 1,\n      H: 1,\n      2: 2,\n      M2: 2,\n      maj2: 2,\n      T: 2,\n      W: 2,\n      m3: 3,\n      b3: 3,\n      min3: 3,\n      M3: 4,\n      3: 4,\n      maj3: 4,\n      4: 5,\n      p4: 5,\n      '#4': 6,\n      b5: 6,\n      aug4: 6,\n      dim5: 6,\n      5: 7,\n      p5: 7,\n      '#5': 8,\n      b6: 8,\n      aug5: 8,\n      6: 9,\n      M6: 9,\n      maj6: 9,\n      b7: 10,\n      m7: 10,\n      min7: 10,\n      dom7: 10,\n      M7: 11,\n      maj7: 11,\n      8: 12,\n      octave: 12\n    };\n  }\n  static get scales() {\n    return {\n      major: [2, 2, 1, 2, 2, 2, 1],\n      minor: [2, 1, 2, 2, 1, 2, 2],\n      ionian: [2, 2, 1, 2, 2, 2, 1],\n      dorian: [2, 1, 2, 2, 2, 1, 2],\n      phyrgian: [1, 2, 2, 2, 1, 2, 2],\n      lydian: [2, 2, 2, 1, 2, 2, 1],\n      mixolydian: [2, 2, 1, 2, 2, 1, 2],\n      aeolian: [2, 1, 2, 2, 1, 2, 2],\n      locrian: [1, 2, 2, 1, 2, 2, 2]\n    };\n  }\n  static get scaleTypes() {\n    return {\n      M: Music.scales.major,\n      m: Music.scales.minor\n    };\n  }\n  static get accidentals() {\n    return ['bb', 'b', 'n', '#', '##'];\n  }\n  static get noteValues() {\n    return {\n      c: {\n        rootIndex: 0,\n        intVal: 0\n      },\n      cn: {\n        rootIndex: 0,\n        intVal: 0\n      },\n      'c#': {\n        rootIndex: 0,\n        intVal: 1\n      },\n      'c##': {\n        rootIndex: 0,\n        intVal: 2\n      },\n      cb: {\n        rootIndex: 0,\n        intVal: 11\n      },\n      cbb: {\n        rootIndex: 0,\n        intVal: 10\n      },\n      d: {\n        rootIndex: 1,\n        intVal: 2\n      },\n      dn: {\n        rootIndex: 1,\n        intVal: 2\n      },\n      'd#': {\n        rootIndex: 1,\n        intVal: 3\n      },\n      'd##': {\n        rootIndex: 1,\n        intVal: 4\n      },\n      db: {\n        rootIndex: 1,\n        intVal: 1\n      },\n      dbb: {\n        rootIndex: 1,\n        intVal: 0\n      },\n      e: {\n        rootIndex: 2,\n        intVal: 4\n      },\n      en: {\n        rootIndex: 2,\n        intVal: 4\n      },\n      'e#': {\n        rootIndex: 2,\n        intVal: 5\n      },\n      'e##': {\n        rootIndex: 2,\n        intVal: 6\n      },\n      eb: {\n        rootIndex: 2,\n        intVal: 3\n      },\n      ebb: {\n        rootIndex: 2,\n        intVal: 2\n      },\n      f: {\n        rootIndex: 3,\n        intVal: 5\n      },\n      fn: {\n        rootIndex: 3,\n        intVal: 5\n      },\n      'f#': {\n        rootIndex: 3,\n        intVal: 6\n      },\n      'f##': {\n        rootIndex: 3,\n        intVal: 7\n      },\n      fb: {\n        rootIndex: 3,\n        intVal: 4\n      },\n      fbb: {\n        rootIndex: 3,\n        intVal: 3\n      },\n      g: {\n        rootIndex: 4,\n        intVal: 7\n      },\n      gn: {\n        rootIndex: 4,\n        intVal: 7\n      },\n      'g#': {\n        rootIndex: 4,\n        intVal: 8\n      },\n      'g##': {\n        rootIndex: 4,\n        intVal: 9\n      },\n      gb: {\n        rootIndex: 4,\n        intVal: 6\n      },\n      gbb: {\n        rootIndex: 4,\n        intVal: 5\n      },\n      a: {\n        rootIndex: 5,\n        intVal: 9\n      },\n      an: {\n        rootIndex: 5,\n        intVal: 9\n      },\n      'a#': {\n        rootIndex: 5,\n        intVal: 10\n      },\n      'a##': {\n        rootIndex: 5,\n        intVal: 11\n      },\n      ab: {\n        rootIndex: 5,\n        intVal: 8\n      },\n      abb: {\n        rootIndex: 5,\n        intVal: 7\n      },\n      b: {\n        rootIndex: 6,\n        intVal: 11\n      },\n      bn: {\n        rootIndex: 6,\n        intVal: 11\n      },\n      'b#': {\n        rootIndex: 6,\n        intVal: 0\n      },\n      'b##': {\n        rootIndex: 6,\n        intVal: 1\n      },\n      bb: {\n        rootIndex: 6,\n        intVal: 10\n      },\n      bbb: {\n        rootIndex: 6,\n        intVal: 9\n      }\n    };\n  }\n  isValidNoteValue(note) {\n    return note >= 0 && note < Music.canonicalNotes.length;\n  }\n  isValidIntervalValue(interval) {\n    return interval >= 0 && interval < Music.diatonicIntervals.length;\n  }\n  getNoteParts(noteString) {\n    if (!noteString || noteString.length < 1) {\n      throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n    }\n    if (noteString.length > 3) {\n      throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n    }\n    const note = noteString.toLowerCase();\n    const regex = /^([cdefgab])(b|bb|n|#|##)?$/;\n    const match = regex.exec(note);\n    if (match !== null) {\n      const root = match[1];\n      const accidental = match[2];\n      return {\n        root,\n        accidental\n      };\n    } else {\n      throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n    }\n  }\n  getKeyParts(keyString) {\n    if (!keyString || keyString.length < 1) {\n      throw new RuntimeError('BadArguments', 'Invalid key: ' + keyString);\n    }\n    const key = keyString.toLowerCase();\n    const regex = /^([cdefgab])(b|#)?(mel|harm|m|M)?$/;\n    const match = regex.exec(key);\n    if (match !== null) {\n      const root = match[1];\n      const accidental = match[2];\n      let type = match[3];\n      if (!type) type = 'M';\n      return {\n        root,\n        accidental,\n        type\n      };\n    } else {\n      throw new RuntimeError('BadArguments', `Invalid key: ${keyString}`);\n    }\n  }\n  getNoteValue(noteString) {\n    const value = Music.noteValues[noteString];\n    if (value === undefined) {\n      throw new RuntimeError('BadArguments', `Invalid note name: ${noteString}`);\n    }\n    return value.intVal;\n  }\n  getIntervalValue(intervalString) {\n    const value = Music.intervals[intervalString];\n    if (value === undefined) {\n      throw new RuntimeError('BadArguments', `Invalid interval name: ${intervalString}`);\n    }\n    return value;\n  }\n  getCanonicalNoteName(noteValue) {\n    if (!this.isValidNoteValue(noteValue)) {\n      throw new RuntimeError('BadArguments', `Invalid note value: ${noteValue}`);\n    }\n    return Music.canonicalNotes[noteValue];\n  }\n  getCanonicalIntervalName(intervalValue) {\n    if (!this.isValidIntervalValue(intervalValue)) {\n      throw new RuntimeError('BadArguments', `Invalid interval value: ${intervalValue}`);\n    }\n    return Music.diatonicIntervals[intervalValue];\n  }\n  getRelativeNoteValue(noteValue, intervalValue, direction = 1) {\n    if (direction !== 1 && direction !== -1) {\n      throw new RuntimeError('BadArguments', `Invalid direction: ${direction}`);\n    }\n    let sum = (noteValue + direction * intervalValue) % Music.NUM_TONES;\n    if (sum < 0) sum += Music.NUM_TONES;\n    return sum;\n  }\n  getRelativeNoteName(root, noteValue) {\n    const parts = this.getNoteParts(root);\n    const rootValue = this.getNoteValue(parts.root);\n    let interval = noteValue - rootValue;\n    if (Math.abs(interval) > Music.NUM_TONES - 3) {\n      let multiplier = 1;\n      if (interval > 0) multiplier = -1;\n      const reverseInterval = (noteValue + 1 + (rootValue + 1)) % Music.NUM_TONES * multiplier;\n      if (Math.abs(reverseInterval) > 2) {\n        throw new RuntimeError('BadArguments', `Notes not related: ${root}, ${noteValue})`);\n      } else {\n        interval = reverseInterval;\n      }\n    }\n    if (Math.abs(interval) > 2) {\n      throw new RuntimeError('BadArguments', `Notes not related: ${root}, ${noteValue})`);\n    }\n    let relativeNoteName = parts.root;\n    if (interval > 0) {\n      for (let i = 1; i <= interval; ++i) {\n        relativeNoteName += '#';\n      }\n    } else if (interval < 0) {\n      for (let i = -1; i >= interval; --i) {\n        relativeNoteName += 'b';\n      }\n    }\n    return relativeNoteName;\n  }\n  getScaleTones(key, intervals) {\n    const tones = [key];\n    let nextNote = key;\n    for (let i = 0; i < intervals.length; i++) {\n      nextNote = this.getRelativeNoteValue(nextNote, intervals[i]);\n      if (nextNote !== key) tones.push(nextNote);\n    }\n    return tones;\n  }\n  getIntervalBetween(note1, note2, direction = 1) {\n    if (direction !== 1 && direction !== -1) {\n      throw new RuntimeError('BadArguments', `Invalid direction: ${direction}`);\n    }\n    if (!this.isValidNoteValue(note1) || !this.isValidNoteValue(note2)) {\n      throw new RuntimeError('BadArguments', `Invalid notes: ${note1}, ${note2}`);\n    }\n    let difference = direction === 1 ? note2 - note1 : note1 - note2;\n    if (difference < 0) difference += Music.NUM_TONES;\n    return difference;\n  }\n  createScaleMap(keySignature) {\n    const keySigParts = this.getKeyParts(keySignature);\n    if (!keySigParts.type) throw new RuntimeError('BadArguments', 'Unsupported key type: undefined');\n    const scaleName = Music.scaleTypes[keySigParts.type];\n    let keySigString = keySigParts.root;\n    if (keySigParts.accidental) keySigString += keySigParts.accidental;\n    if (!scaleName) throw new RuntimeError('BadArguments', 'Unsupported key type: ' + keySignature);\n    const scale = this.getScaleTones(this.getNoteValue(keySigString), scaleName);\n    const noteLocation = Music.rootIndexes[keySigParts.root];\n    const scaleMap = {};\n    for (let i = 0; i < Music.roots.length; ++i) {\n      const index = (noteLocation + i) % Music.roots.length;\n      const rootName = Music.roots[index];\n      let noteName = this.getRelativeNoteName(rootName, scale[i]);\n      if (noteName.length === 1) {\n        noteName += 'n';\n      }\n      scaleMap[rootName] = noteName;\n    }\n    return scaleMap;\n  }\n}", "map": {"version": 3, "names": ["RuntimeError", "Music", "NUM_TONES", "canonicalNotes", "length", "roots", "rootValues", "rootIndexes", "c", "d", "e", "f", "g", "a", "b", "diatonicIntervals", "diatonicAccidentals", "unison", "note", "accidental", "m2", "M2", "m3", "M3", "p4", "dim5", "p5", "m6", "M6", "b7", "M7", "octave", "intervals", "u", "b2", "min2", "S", "H", "maj2", "T", "W", "b3", "min3", "maj3", "b5", "aug4", "b6", "aug5", "maj6", "m7", "min7", "dom7", "maj7", "scales", "major", "minor", "ionian", "dorian", "phyrgian", "lydian", "mixolydian", "aeolian", "locrian", "scaleTypes", "M", "m", "accidentals", "noteValues", "rootIndex", "intVal", "cn", "cb", "cbb", "dn", "db", "dbb", "en", "eb", "ebb", "fn", "fb", "fbb", "gn", "gb", "gbb", "an", "ab", "abb", "bn", "bb", "bbb", "isValidNoteValue", "isValidIntervalValue", "interval", "getNoteParts", "noteString", "toLowerCase", "regex", "match", "exec", "root", "getKeyParts", "keyString", "key", "type", "getNoteValue", "value", "undefined", "getIntervalValue", "intervalString", "getCanonicalNoteName", "noteValue", "getCanonicalIntervalName", "intervalValue", "getRelativeNoteValue", "direction", "sum", "getRelativeNoteName", "parts", "rootValue", "Math", "abs", "multiplier", "reverseInterval", "relativeNoteName", "i", "getScaleTones", "tones", "nextNote", "push", "getIntervalBetween", "note1", "note2", "difference", "createScaleMap", "keySignature", "keySigParts", "scaleName", "keySigString", "scale", "noteLocation", "scaleMap", "index", "rootName", "noteName"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/music.js"], "sourcesContent": ["import { RuntimeError } from './util.js';\nexport class Music {\n    static get NUM_TONES() {\n        return this.canonicalNotes.length;\n    }\n    static get roots() {\n        return ['c', 'd', 'e', 'f', 'g', 'a', 'b'];\n    }\n    static get rootValues() {\n        return [0, 2, 4, 5, 7, 9, 11];\n    }\n    static get rootIndexes() {\n        return {\n            c: 0,\n            d: 1,\n            e: 2,\n            f: 3,\n            g: 4,\n            a: 5,\n            b: 6,\n        };\n    }\n    static get canonicalNotes() {\n        return ['c', 'c#', 'd', 'd#', 'e', 'f', 'f#', 'g', 'g#', 'a', 'a#', 'b'];\n    }\n    static get diatonicIntervals() {\n        return ['unison', 'm2', 'M2', 'm3', 'M3', 'p4', 'dim5', 'p5', 'm6', 'M6', 'b7', 'M7', 'octave'];\n    }\n    static get diatonicAccidentals() {\n        return {\n            unison: { note: 0, accidental: 0 },\n            m2: { note: 1, accidental: -1 },\n            M2: { note: 1, accidental: 0 },\n            m3: { note: 2, accidental: -1 },\n            M3: { note: 2, accidental: 0 },\n            p4: { note: 3, accidental: 0 },\n            dim5: { note: 4, accidental: -1 },\n            p5: { note: 4, accidental: 0 },\n            m6: { note: 5, accidental: -1 },\n            M6: { note: 5, accidental: 0 },\n            b7: { note: 6, accidental: -1 },\n            M7: { note: 6, accidental: 0 },\n            octave: { note: 7, accidental: 0 },\n        };\n    }\n    static get intervals() {\n        return {\n            u: 0,\n            unison: 0,\n            m2: 1,\n            b2: 1,\n            min2: 1,\n            S: 1,\n            H: 1,\n            2: 2,\n            M2: 2,\n            maj2: 2,\n            T: 2,\n            W: 2,\n            m3: 3,\n            b3: 3,\n            min3: 3,\n            M3: 4,\n            3: 4,\n            maj3: 4,\n            4: 5,\n            p4: 5,\n            '#4': 6,\n            b5: 6,\n            aug4: 6,\n            dim5: 6,\n            5: 7,\n            p5: 7,\n            '#5': 8,\n            b6: 8,\n            aug5: 8,\n            6: 9,\n            M6: 9,\n            maj6: 9,\n            b7: 10,\n            m7: 10,\n            min7: 10,\n            dom7: 10,\n            M7: 11,\n            maj7: 11,\n            8: 12,\n            octave: 12,\n        };\n    }\n    static get scales() {\n        return {\n            major: [2, 2, 1, 2, 2, 2, 1],\n            minor: [2, 1, 2, 2, 1, 2, 2],\n            ionian: [2, 2, 1, 2, 2, 2, 1],\n            dorian: [2, 1, 2, 2, 2, 1, 2],\n            phyrgian: [1, 2, 2, 2, 1, 2, 2],\n            lydian: [2, 2, 2, 1, 2, 2, 1],\n            mixolydian: [2, 2, 1, 2, 2, 1, 2],\n            aeolian: [2, 1, 2, 2, 1, 2, 2],\n            locrian: [1, 2, 2, 1, 2, 2, 2],\n        };\n    }\n    static get scaleTypes() {\n        return {\n            M: Music.scales.major,\n            m: Music.scales.minor,\n        };\n    }\n    static get accidentals() {\n        return ['bb', 'b', 'n', '#', '##'];\n    }\n    static get noteValues() {\n        return {\n            c: { rootIndex: 0, intVal: 0 },\n            cn: { rootIndex: 0, intVal: 0 },\n            'c#': { rootIndex: 0, intVal: 1 },\n            'c##': { rootIndex: 0, intVal: 2 },\n            cb: { rootIndex: 0, intVal: 11 },\n            cbb: { rootIndex: 0, intVal: 10 },\n            d: { rootIndex: 1, intVal: 2 },\n            dn: { rootIndex: 1, intVal: 2 },\n            'd#': { rootIndex: 1, intVal: 3 },\n            'd##': { rootIndex: 1, intVal: 4 },\n            db: { rootIndex: 1, intVal: 1 },\n            dbb: { rootIndex: 1, intVal: 0 },\n            e: { rootIndex: 2, intVal: 4 },\n            en: { rootIndex: 2, intVal: 4 },\n            'e#': { rootIndex: 2, intVal: 5 },\n            'e##': { rootIndex: 2, intVal: 6 },\n            eb: { rootIndex: 2, intVal: 3 },\n            ebb: { rootIndex: 2, intVal: 2 },\n            f: { rootIndex: 3, intVal: 5 },\n            fn: { rootIndex: 3, intVal: 5 },\n            'f#': { rootIndex: 3, intVal: 6 },\n            'f##': { rootIndex: 3, intVal: 7 },\n            fb: { rootIndex: 3, intVal: 4 },\n            fbb: { rootIndex: 3, intVal: 3 },\n            g: { rootIndex: 4, intVal: 7 },\n            gn: { rootIndex: 4, intVal: 7 },\n            'g#': { rootIndex: 4, intVal: 8 },\n            'g##': { rootIndex: 4, intVal: 9 },\n            gb: { rootIndex: 4, intVal: 6 },\n            gbb: { rootIndex: 4, intVal: 5 },\n            a: { rootIndex: 5, intVal: 9 },\n            an: { rootIndex: 5, intVal: 9 },\n            'a#': { rootIndex: 5, intVal: 10 },\n            'a##': { rootIndex: 5, intVal: 11 },\n            ab: { rootIndex: 5, intVal: 8 },\n            abb: { rootIndex: 5, intVal: 7 },\n            b: { rootIndex: 6, intVal: 11 },\n            bn: { rootIndex: 6, intVal: 11 },\n            'b#': { rootIndex: 6, intVal: 0 },\n            'b##': { rootIndex: 6, intVal: 1 },\n            bb: { rootIndex: 6, intVal: 10 },\n            bbb: { rootIndex: 6, intVal: 9 },\n        };\n    }\n    isValidNoteValue(note) {\n        return note >= 0 && note < Music.canonicalNotes.length;\n    }\n    isValidIntervalValue(interval) {\n        return interval >= 0 && interval < Music.diatonicIntervals.length;\n    }\n    getNoteParts(noteString) {\n        if (!noteString || noteString.length < 1) {\n            throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n        }\n        if (noteString.length > 3) {\n            throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n        }\n        const note = noteString.toLowerCase();\n        const regex = /^([cdefgab])(b|bb|n|#|##)?$/;\n        const match = regex.exec(note);\n        if (match !== null) {\n            const root = match[1];\n            const accidental = match[2];\n            return {\n                root,\n                accidental,\n            };\n        }\n        else {\n            throw new RuntimeError('BadArguments', 'Invalid note name: ' + noteString);\n        }\n    }\n    getKeyParts(keyString) {\n        if (!keyString || keyString.length < 1) {\n            throw new RuntimeError('BadArguments', 'Invalid key: ' + keyString);\n        }\n        const key = keyString.toLowerCase();\n        const regex = /^([cdefgab])(b|#)?(mel|harm|m|M)?$/;\n        const match = regex.exec(key);\n        if (match !== null) {\n            const root = match[1];\n            const accidental = match[2];\n            let type = match[3];\n            if (!type)\n                type = 'M';\n            return {\n                root,\n                accidental,\n                type,\n            };\n        }\n        else {\n            throw new RuntimeError('BadArguments', `Invalid key: ${keyString}`);\n        }\n    }\n    getNoteValue(noteString) {\n        const value = Music.noteValues[noteString];\n        if (value === undefined) {\n            throw new RuntimeError('BadArguments', `Invalid note name: ${noteString}`);\n        }\n        return value.intVal;\n    }\n    getIntervalValue(intervalString) {\n        const value = Music.intervals[intervalString];\n        if (value === undefined) {\n            throw new RuntimeError('BadArguments', `Invalid interval name: ${intervalString}`);\n        }\n        return value;\n    }\n    getCanonicalNoteName(noteValue) {\n        if (!this.isValidNoteValue(noteValue)) {\n            throw new RuntimeError('BadArguments', `Invalid note value: ${noteValue}`);\n        }\n        return Music.canonicalNotes[noteValue];\n    }\n    getCanonicalIntervalName(intervalValue) {\n        if (!this.isValidIntervalValue(intervalValue)) {\n            throw new RuntimeError('BadArguments', `Invalid interval value: ${intervalValue}`);\n        }\n        return Music.diatonicIntervals[intervalValue];\n    }\n    getRelativeNoteValue(noteValue, intervalValue, direction = 1) {\n        if (direction !== 1 && direction !== -1) {\n            throw new RuntimeError('BadArguments', `Invalid direction: ${direction}`);\n        }\n        let sum = (noteValue + direction * intervalValue) % Music.NUM_TONES;\n        if (sum < 0)\n            sum += Music.NUM_TONES;\n        return sum;\n    }\n    getRelativeNoteName(root, noteValue) {\n        const parts = this.getNoteParts(root);\n        const rootValue = this.getNoteValue(parts.root);\n        let interval = noteValue - rootValue;\n        if (Math.abs(interval) > Music.NUM_TONES - 3) {\n            let multiplier = 1;\n            if (interval > 0)\n                multiplier = -1;\n            const reverseInterval = ((noteValue + 1 + (rootValue + 1)) % Music.NUM_TONES) * multiplier;\n            if (Math.abs(reverseInterval) > 2) {\n                throw new RuntimeError('BadArguments', `Notes not related: ${root}, ${noteValue})`);\n            }\n            else {\n                interval = reverseInterval;\n            }\n        }\n        if (Math.abs(interval) > 2) {\n            throw new RuntimeError('BadArguments', `Notes not related: ${root}, ${noteValue})`);\n        }\n        let relativeNoteName = parts.root;\n        if (interval > 0) {\n            for (let i = 1; i <= interval; ++i) {\n                relativeNoteName += '#';\n            }\n        }\n        else if (interval < 0) {\n            for (let i = -1; i >= interval; --i) {\n                relativeNoteName += 'b';\n            }\n        }\n        return relativeNoteName;\n    }\n    getScaleTones(key, intervals) {\n        const tones = [key];\n        let nextNote = key;\n        for (let i = 0; i < intervals.length; i++) {\n            nextNote = this.getRelativeNoteValue(nextNote, intervals[i]);\n            if (nextNote !== key)\n                tones.push(nextNote);\n        }\n        return tones;\n    }\n    getIntervalBetween(note1, note2, direction = 1) {\n        if (direction !== 1 && direction !== -1) {\n            throw new RuntimeError('BadArguments', `Invalid direction: ${direction}`);\n        }\n        if (!this.isValidNoteValue(note1) || !this.isValidNoteValue(note2)) {\n            throw new RuntimeError('BadArguments', `Invalid notes: ${note1}, ${note2}`);\n        }\n        let difference = direction === 1 ? note2 - note1 : note1 - note2;\n        if (difference < 0)\n            difference += Music.NUM_TONES;\n        return difference;\n    }\n    createScaleMap(keySignature) {\n        const keySigParts = this.getKeyParts(keySignature);\n        if (!keySigParts.type)\n            throw new RuntimeError('BadArguments', 'Unsupported key type: undefined');\n        const scaleName = Music.scaleTypes[keySigParts.type];\n        let keySigString = keySigParts.root;\n        if (keySigParts.accidental)\n            keySigString += keySigParts.accidental;\n        if (!scaleName)\n            throw new RuntimeError('BadArguments', 'Unsupported key type: ' + keySignature);\n        const scale = this.getScaleTones(this.getNoteValue(keySigString), scaleName);\n        const noteLocation = Music.rootIndexes[keySigParts.root];\n        const scaleMap = {};\n        for (let i = 0; i < Music.roots.length; ++i) {\n            const index = (noteLocation + i) % Music.roots.length;\n            const rootName = Music.roots[index];\n            let noteName = this.getRelativeNoteName(rootName, scale[i]);\n            if (noteName.length === 1) {\n                noteName += 'n';\n            }\n            scaleMap[rootName] = noteName;\n        }\n        return scaleMap;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,KAAK,CAAC;EACf,WAAWC,SAASA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,cAAc,CAACC,MAAM;EACrC;EACA,WAAWC,KAAKA,CAAA,EAAG;IACf,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9C;EACA,WAAWC,UAAUA,CAAA,EAAG;IACpB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACjC;EACA,WAAWC,WAAWA,CAAA,EAAG;IACrB,OAAO;MACHC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACP,CAAC;EACL;EACA,WAAWX,cAAcA,CAAA,EAAG;IACxB,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAC5E;EACA,WAAWY,iBAAiBA,CAAA,EAAG;IAC3B,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;EACnG;EACA,WAAWC,mBAAmBA,CAAA,EAAG;IAC7B,OAAO;MACHC,MAAM,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAClCC,EAAE,EAAE;QAAEF,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;MAC/BE,EAAE,EAAE;QAAEH,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BG,EAAE,EAAE;QAAEJ,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;MAC/BI,EAAE,EAAE;QAAEL,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BK,EAAE,EAAE;QAAEN,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BM,IAAI,EAAE;QAAEP,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;MACjCO,EAAE,EAAE;QAAER,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BQ,EAAE,EAAE;QAAET,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;MAC/BS,EAAE,EAAE;QAAEV,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BU,EAAE,EAAE;QAAEX,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE,CAAC;MAAE,CAAC;MAC/BW,EAAE,EAAE;QAAEZ,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;MAC9BY,MAAM,EAAE;QAAEb,IAAI,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE;IACrC,CAAC;EACL;EACA,WAAWa,SAASA,CAAA,EAAG;IACnB,OAAO;MACHC,CAAC,EAAE,CAAC;MACJhB,MAAM,EAAE,CAAC;MACTG,EAAE,EAAE,CAAC;MACLc,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,CAAC;MACPC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJ,CAAC,EAAE,CAAC;MACJhB,EAAE,EAAE,CAAC;MACLiB,IAAI,EAAE,CAAC;MACPC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJlB,EAAE,EAAE,CAAC;MACLmB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,CAAC;MACPnB,EAAE,EAAE,CAAC;MACL,CAAC,EAAE,CAAC;MACJoB,IAAI,EAAE,CAAC;MACP,CAAC,EAAE,CAAC;MACJnB,EAAE,EAAE,CAAC;MACL,IAAI,EAAE,CAAC;MACPoB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,CAAC;MACPpB,IAAI,EAAE,CAAC;MACP,CAAC,EAAE,CAAC;MACJC,EAAE,EAAE,CAAC;MACL,IAAI,EAAE,CAAC;MACPoB,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,CAAC;MACP,CAAC,EAAE,CAAC;MACJnB,EAAE,EAAE,CAAC;MACLoB,IAAI,EAAE,CAAC;MACPnB,EAAE,EAAE,EAAE;MACNoB,EAAE,EAAE,EAAE;MACNC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,EAAE;MACRrB,EAAE,EAAE,EAAE;MACNsB,IAAI,EAAE,EAAE;MACR,CAAC,EAAE,EAAE;MACLrB,MAAM,EAAE;IACZ,CAAC;EACL;EACA,WAAWsB,MAAMA,CAAA,EAAG;IAChB,OAAO;MACHC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5BC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5BC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7BC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7BC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC/BC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjCC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9BC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACjC,CAAC;EACL;EACA,WAAWC,UAAUA,CAAA,EAAG;IACpB,OAAO;MACHC,CAAC,EAAE/D,KAAK,CAACoD,MAAM,CAACC,KAAK;MACrBW,CAAC,EAAEhE,KAAK,CAACoD,MAAM,CAACE;IACpB,CAAC;EACL;EACA,WAAWW,WAAWA,CAAA,EAAG;IACrB,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EACtC;EACA,WAAWC,UAAUA,CAAA,EAAG;IACpB,OAAO;MACH3D,CAAC,EAAE;QAAE4D,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BC,EAAE,EAAE;QAAEF,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCE,EAAE,EAAE;QAAEH,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MAChCG,GAAG,EAAE;QAAEJ,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MACjC5D,CAAC,EAAE;QAAE2D,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BI,EAAE,EAAE;QAAEL,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCK,EAAE,EAAE;QAAEN,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/BM,GAAG,EAAE;QAAEP,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChC3D,CAAC,EAAE;QAAE0D,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BO,EAAE,EAAE;QAAER,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCQ,EAAE,EAAE;QAAET,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/BS,GAAG,EAAE;QAAEV,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChC1D,CAAC,EAAE;QAAEyD,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BU,EAAE,EAAE;QAAEX,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCW,EAAE,EAAE;QAAEZ,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/BY,GAAG,EAAE;QAAEb,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChCzD,CAAC,EAAE;QAAEwD,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9Ba,EAAE,EAAE;QAAEd,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCc,EAAE,EAAE;QAAEf,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/Be,GAAG,EAAE;QAAEhB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChCxD,CAAC,EAAE;QAAEuD,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC9BgB,EAAE,EAAE;QAAEjB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/B,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MAClC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MACnCiB,EAAE,EAAE;QAAElB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC/BkB,GAAG,EAAE;QAAEnB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAChCvD,CAAC,EAAE;QAAEsD,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MAC/BmB,EAAE,EAAE;QAAEpB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MAChC,IAAI,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MACjC,KAAK,EAAE;QAAED,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAClCoB,EAAE,EAAE;QAAErB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC;MAChCqB,GAAG,EAAE;QAAEtB,SAAS,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE;IACnC,CAAC;EACL;EACAsB,gBAAgBA,CAACzE,IAAI,EAAE;IACnB,OAAOA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGjB,KAAK,CAACE,cAAc,CAACC,MAAM;EAC1D;EACAwF,oBAAoBA,CAACC,QAAQ,EAAE;IAC3B,OAAOA,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG5F,KAAK,CAACc,iBAAiB,CAACX,MAAM;EACrE;EACA0F,YAAYA,CAACC,UAAU,EAAE;IACrB,IAAI,CAACA,UAAU,IAAIA,UAAU,CAAC3F,MAAM,GAAG,CAAC,EAAE;MACtC,MAAM,IAAIJ,YAAY,CAAC,cAAc,EAAE,qBAAqB,GAAG+F,UAAU,CAAC;IAC9E;IACA,IAAIA,UAAU,CAAC3F,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIJ,YAAY,CAAC,cAAc,EAAE,qBAAqB,GAAG+F,UAAU,CAAC;IAC9E;IACA,MAAM7E,IAAI,GAAG6E,UAAU,CAACC,WAAW,CAAC,CAAC;IACrC,MAAMC,KAAK,GAAG,6BAA6B;IAC3C,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAACjF,IAAI,CAAC;IAC9B,IAAIgF,KAAK,KAAK,IAAI,EAAE;MAChB,MAAME,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC;MACrB,MAAM/E,UAAU,GAAG+E,KAAK,CAAC,CAAC,CAAC;MAC3B,OAAO;QACHE,IAAI;QACJjF;MACJ,CAAC;IACL,CAAC,MACI;MACD,MAAM,IAAInB,YAAY,CAAC,cAAc,EAAE,qBAAqB,GAAG+F,UAAU,CAAC;IAC9E;EACJ;EACAM,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,IAAIA,SAAS,CAAClG,MAAM,GAAG,CAAC,EAAE;MACpC,MAAM,IAAIJ,YAAY,CAAC,cAAc,EAAE,eAAe,GAAGsG,SAAS,CAAC;IACvE;IACA,MAAMC,GAAG,GAAGD,SAAS,CAACN,WAAW,CAAC,CAAC;IACnC,MAAMC,KAAK,GAAG,oCAAoC;IAClD,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAACI,GAAG,CAAC;IAC7B,IAAIL,KAAK,KAAK,IAAI,EAAE;MAChB,MAAME,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC;MACrB,MAAM/E,UAAU,GAAG+E,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAIM,IAAI,GAAGN,KAAK,CAAC,CAAC,CAAC;MACnB,IAAI,CAACM,IAAI,EACLA,IAAI,GAAG,GAAG;MACd,OAAO;QACHJ,IAAI;QACJjF,UAAU;QACVqF;MACJ,CAAC;IACL,CAAC,MACI;MACD,MAAM,IAAIxG,YAAY,CAAC,cAAc,EAAE,gBAAgBsG,SAAS,EAAE,CAAC;IACvE;EACJ;EACAG,YAAYA,CAACV,UAAU,EAAE;IACrB,MAAMW,KAAK,GAAGzG,KAAK,CAACkE,UAAU,CAAC4B,UAAU,CAAC;IAC1C,IAAIW,KAAK,KAAKC,SAAS,EAAE;MACrB,MAAM,IAAI3G,YAAY,CAAC,cAAc,EAAE,sBAAsB+F,UAAU,EAAE,CAAC;IAC9E;IACA,OAAOW,KAAK,CAACrC,MAAM;EACvB;EACAuC,gBAAgBA,CAACC,cAAc,EAAE;IAC7B,MAAMH,KAAK,GAAGzG,KAAK,CAAC+B,SAAS,CAAC6E,cAAc,CAAC;IAC7C,IAAIH,KAAK,KAAKC,SAAS,EAAE;MACrB,MAAM,IAAI3G,YAAY,CAAC,cAAc,EAAE,0BAA0B6G,cAAc,EAAE,CAAC;IACtF;IACA,OAAOH,KAAK;EAChB;EACAI,oBAAoBA,CAACC,SAAS,EAAE;IAC5B,IAAI,CAAC,IAAI,CAACpB,gBAAgB,CAACoB,SAAS,CAAC,EAAE;MACnC,MAAM,IAAI/G,YAAY,CAAC,cAAc,EAAE,uBAAuB+G,SAAS,EAAE,CAAC;IAC9E;IACA,OAAO9G,KAAK,CAACE,cAAc,CAAC4G,SAAS,CAAC;EAC1C;EACAC,wBAAwBA,CAACC,aAAa,EAAE;IACpC,IAAI,CAAC,IAAI,CAACrB,oBAAoB,CAACqB,aAAa,CAAC,EAAE;MAC3C,MAAM,IAAIjH,YAAY,CAAC,cAAc,EAAE,2BAA2BiH,aAAa,EAAE,CAAC;IACtF;IACA,OAAOhH,KAAK,CAACc,iBAAiB,CAACkG,aAAa,CAAC;EACjD;EACAC,oBAAoBA,CAACH,SAAS,EAAEE,aAAa,EAAEE,SAAS,GAAG,CAAC,EAAE;IAC1D,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC,CAAC,EAAE;MACrC,MAAM,IAAInH,YAAY,CAAC,cAAc,EAAE,sBAAsBmH,SAAS,EAAE,CAAC;IAC7E;IACA,IAAIC,GAAG,GAAG,CAACL,SAAS,GAAGI,SAAS,GAAGF,aAAa,IAAIhH,KAAK,CAACC,SAAS;IACnE,IAAIkH,GAAG,GAAG,CAAC,EACPA,GAAG,IAAInH,KAAK,CAACC,SAAS;IAC1B,OAAOkH,GAAG;EACd;EACAC,mBAAmBA,CAACjB,IAAI,EAAEW,SAAS,EAAE;IACjC,MAAMO,KAAK,GAAG,IAAI,CAACxB,YAAY,CAACM,IAAI,CAAC;IACrC,MAAMmB,SAAS,GAAG,IAAI,CAACd,YAAY,CAACa,KAAK,CAAClB,IAAI,CAAC;IAC/C,IAAIP,QAAQ,GAAGkB,SAAS,GAAGQ,SAAS;IACpC,IAAIC,IAAI,CAACC,GAAG,CAAC5B,QAAQ,CAAC,GAAG5F,KAAK,CAACC,SAAS,GAAG,CAAC,EAAE;MAC1C,IAAIwH,UAAU,GAAG,CAAC;MAClB,IAAI7B,QAAQ,GAAG,CAAC,EACZ6B,UAAU,GAAG,CAAC,CAAC;MACnB,MAAMC,eAAe,GAAI,CAACZ,SAAS,GAAG,CAAC,IAAIQ,SAAS,GAAG,CAAC,CAAC,IAAItH,KAAK,CAACC,SAAS,GAAIwH,UAAU;MAC1F,IAAIF,IAAI,CAACC,GAAG,CAACE,eAAe,CAAC,GAAG,CAAC,EAAE;QAC/B,MAAM,IAAI3H,YAAY,CAAC,cAAc,EAAE,sBAAsBoG,IAAI,KAAKW,SAAS,GAAG,CAAC;MACvF,CAAC,MACI;QACDlB,QAAQ,GAAG8B,eAAe;MAC9B;IACJ;IACA,IAAIH,IAAI,CAACC,GAAG,CAAC5B,QAAQ,CAAC,GAAG,CAAC,EAAE;MACxB,MAAM,IAAI7F,YAAY,CAAC,cAAc,EAAE,sBAAsBoG,IAAI,KAAKW,SAAS,GAAG,CAAC;IACvF;IACA,IAAIa,gBAAgB,GAAGN,KAAK,CAAClB,IAAI;IACjC,IAAIP,QAAQ,GAAG,CAAC,EAAE;MACd,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIhC,QAAQ,EAAE,EAAEgC,CAAC,EAAE;QAChCD,gBAAgB,IAAI,GAAG;MAC3B;IACJ,CAAC,MACI,IAAI/B,QAAQ,GAAG,CAAC,EAAE;MACnB,KAAK,IAAIgC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAIhC,QAAQ,EAAE,EAAEgC,CAAC,EAAE;QACjCD,gBAAgB,IAAI,GAAG;MAC3B;IACJ;IACA,OAAOA,gBAAgB;EAC3B;EACAE,aAAaA,CAACvB,GAAG,EAAEvE,SAAS,EAAE;IAC1B,MAAM+F,KAAK,GAAG,CAACxB,GAAG,CAAC;IACnB,IAAIyB,QAAQ,GAAGzB,GAAG;IAClB,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,SAAS,CAAC5B,MAAM,EAAEyH,CAAC,EAAE,EAAE;MACvCG,QAAQ,GAAG,IAAI,CAACd,oBAAoB,CAACc,QAAQ,EAAEhG,SAAS,CAAC6F,CAAC,CAAC,CAAC;MAC5D,IAAIG,QAAQ,KAAKzB,GAAG,EAChBwB,KAAK,CAACE,IAAI,CAACD,QAAQ,CAAC;IAC5B;IACA,OAAOD,KAAK;EAChB;EACAG,kBAAkBA,CAACC,KAAK,EAAEC,KAAK,EAAEjB,SAAS,GAAG,CAAC,EAAE;IAC5C,IAAIA,SAAS,KAAK,CAAC,IAAIA,SAAS,KAAK,CAAC,CAAC,EAAE;MACrC,MAAM,IAAInH,YAAY,CAAC,cAAc,EAAE,sBAAsBmH,SAAS,EAAE,CAAC;IAC7E;IACA,IAAI,CAAC,IAAI,CAACxB,gBAAgB,CAACwC,KAAK,CAAC,IAAI,CAAC,IAAI,CAACxC,gBAAgB,CAACyC,KAAK,CAAC,EAAE;MAChE,MAAM,IAAIpI,YAAY,CAAC,cAAc,EAAE,kBAAkBmI,KAAK,KAAKC,KAAK,EAAE,CAAC;IAC/E;IACA,IAAIC,UAAU,GAAGlB,SAAS,KAAK,CAAC,GAAGiB,KAAK,GAAGD,KAAK,GAAGA,KAAK,GAAGC,KAAK;IAChE,IAAIC,UAAU,GAAG,CAAC,EACdA,UAAU,IAAIpI,KAAK,CAACC,SAAS;IACjC,OAAOmI,UAAU;EACrB;EACAC,cAAcA,CAACC,YAAY,EAAE;IACzB,MAAMC,WAAW,GAAG,IAAI,CAACnC,WAAW,CAACkC,YAAY,CAAC;IAClD,IAAI,CAACC,WAAW,CAAChC,IAAI,EACjB,MAAM,IAAIxG,YAAY,CAAC,cAAc,EAAE,iCAAiC,CAAC;IAC7E,MAAMyI,SAAS,GAAGxI,KAAK,CAAC8D,UAAU,CAACyE,WAAW,CAAChC,IAAI,CAAC;IACpD,IAAIkC,YAAY,GAAGF,WAAW,CAACpC,IAAI;IACnC,IAAIoC,WAAW,CAACrH,UAAU,EACtBuH,YAAY,IAAIF,WAAW,CAACrH,UAAU;IAC1C,IAAI,CAACsH,SAAS,EACV,MAAM,IAAIzI,YAAY,CAAC,cAAc,EAAE,wBAAwB,GAAGuI,YAAY,CAAC;IACnF,MAAMI,KAAK,GAAG,IAAI,CAACb,aAAa,CAAC,IAAI,CAACrB,YAAY,CAACiC,YAAY,CAAC,EAAED,SAAS,CAAC;IAC5E,MAAMG,YAAY,GAAG3I,KAAK,CAACM,WAAW,CAACiI,WAAW,CAACpC,IAAI,CAAC;IACxD,MAAMyC,QAAQ,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5H,KAAK,CAACI,KAAK,CAACD,MAAM,EAAE,EAAEyH,CAAC,EAAE;MACzC,MAAMiB,KAAK,GAAG,CAACF,YAAY,GAAGf,CAAC,IAAI5H,KAAK,CAACI,KAAK,CAACD,MAAM;MACrD,MAAM2I,QAAQ,GAAG9I,KAAK,CAACI,KAAK,CAACyI,KAAK,CAAC;MACnC,IAAIE,QAAQ,GAAG,IAAI,CAAC3B,mBAAmB,CAAC0B,QAAQ,EAAEJ,KAAK,CAACd,CAAC,CAAC,CAAC;MAC3D,IAAImB,QAAQ,CAAC5I,MAAM,KAAK,CAAC,EAAE;QACvB4I,QAAQ,IAAI,GAAG;MACnB;MACAH,QAAQ,CAACE,QAAQ,CAAC,GAAGC,QAAQ;IACjC;IACA,OAAOH,QAAQ;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}