{"ast": null, "code": "import { CanvasContext } from './canvascontext.js';\nimport { SVGContext } from './svgcontext.js';\nimport { isRenderContext } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nimport { isHTMLCanvas, isHTMLDiv } from './web.js';\nexport var RendererBackends;\n(function (RendererBackends) {\n  RendererBackends[RendererBackends[\"CANVAS\"] = 1] = \"CANVAS\";\n  RendererBackends[RendererBackends[\"SVG\"] = 2] = \"SVG\";\n})(RendererBackends || (RendererBackends = {}));\nexport var RendererLineEndType;\n(function (RendererLineEndType) {\n  RendererLineEndType[RendererLineEndType[\"NONE\"] = 1] = \"NONE\";\n  RendererLineEndType[RendererLineEndType[\"UP\"] = 2] = \"UP\";\n  RendererLineEndType[RendererLineEndType[\"DOWN\"] = 3] = \"DOWN\";\n})(RendererLineEndType || (RendererLineEndType = {}));\nexport class Renderer {\n  static buildContext(elementId, backend, width, height, background = '#FFF') {\n    const renderer = new Renderer(elementId, backend);\n    if (width && height) {\n      renderer.resize(width, height);\n    }\n    const ctx = renderer.getContext();\n    ctx.setBackgroundFillStyle(background);\n    Renderer.lastContext = ctx;\n    return ctx;\n  }\n  static getCanvasContext(elementId, width, height, background) {\n    return Renderer.buildContext(elementId, Renderer.Backends.CANVAS, width, height, background);\n  }\n  static getSVGContext(elementId, width, height, background) {\n    return Renderer.buildContext(elementId, Renderer.Backends.SVG, width, height, background);\n  }\n  static drawDashedLine(context, fromX, fromY, toX, toY, dashPattern) {\n    context.beginPath();\n    const dx = toX - fromX;\n    const dy = toY - fromY;\n    const angle = Math.atan2(dy, dx);\n    let x = fromX;\n    let y = fromY;\n    context.moveTo(fromX, fromY);\n    let idx = 0;\n    let draw = true;\n    while (!((dx < 0 ? x <= toX : x >= toX) && (dy < 0 ? y <= toY : y >= toY))) {\n      const dashLength = dashPattern[idx++ % dashPattern.length];\n      const nx = x + Math.cos(angle) * dashLength;\n      x = dx < 0 ? Math.max(toX, nx) : Math.min(toX, nx);\n      const ny = y + Math.sin(angle) * dashLength;\n      y = dy < 0 ? Math.max(toY, ny) : Math.min(toY, ny);\n      if (draw) {\n        context.lineTo(x, y);\n      } else {\n        context.moveTo(x, y);\n      }\n      draw = !draw;\n    }\n    context.closePath();\n    context.stroke();\n  }\n  constructor(arg0, arg1) {\n    if (isRenderContext(arg0)) {\n      this.ctx = arg0;\n    } else {\n      if (arg1 === undefined) {\n        throw new RuntimeError('InvalidArgument', 'Missing backend argument');\n      }\n      const backend = arg1;\n      let element;\n      if (typeof arg0 === 'string') {\n        const maybeElement = document.getElementById(arg0);\n        if (!maybeElement) {\n          throw new RuntimeError('BadElementId', `Can't find element with ID \"${maybeElement}\"`);\n        }\n        element = maybeElement;\n      } else {\n        element = arg0;\n      }\n      if (backend === Renderer.Backends.CANVAS) {\n        if (!isHTMLCanvas(element)) {\n          throw new RuntimeError('BadElement', 'CANVAS context requires an HTMLCanvasElement.');\n        }\n        const context = element.getContext('2d', {\n          willReadFrequently: true\n        });\n        if (!context) {\n          throw new RuntimeError('BadElement', \"Can't get canvas context\");\n        }\n        this.ctx = new CanvasContext(context);\n      } else if (backend === Renderer.Backends.SVG) {\n        if (!isHTMLDiv(element)) {\n          throw new RuntimeError('BadElement', 'SVG context requires an HTMLDivElement.');\n        }\n        this.ctx = new SVGContext(element);\n      } else {\n        throw new RuntimeError('InvalidBackend', `No support for backend: ${backend}`);\n      }\n    }\n  }\n  resize(width, height) {\n    this.ctx.resize(width, height);\n    return this;\n  }\n  getContext() {\n    return this.ctx;\n  }\n}\nRenderer.Backends = RendererBackends;\nRenderer.LineEndType = RendererLineEndType;\nRenderer.lastContext = undefined;", "map": {"version": 3, "names": ["CanvasContext", "SVGContext", "isRenderContext", "RuntimeError", "isHTMLCanvas", "isHTMLDiv", "RendererBackends", "RendererLineEndType", "<PERSON><PERSON><PERSON>", "buildContext", "elementId", "backend", "width", "height", "background", "renderer", "resize", "ctx", "getContext", "setBackgroundFillStyle", "lastContext", "getCanvasContext", "Backends", "CANVAS", "getSVGContext", "SVG", "drawDashedLine", "context", "fromX", "fromY", "toX", "toY", "dashPattern", "beginPath", "dx", "dy", "angle", "Math", "atan2", "x", "y", "moveTo", "idx", "draw", "<PERSON><PERSON><PERSON><PERSON>", "length", "nx", "cos", "max", "min", "ny", "sin", "lineTo", "closePath", "stroke", "constructor", "arg0", "arg1", "undefined", "element", "maybeElement", "document", "getElementById", "willReadFrequently", "LineEndType"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/renderer.js"], "sourcesContent": ["import { CanvasContext } from './canvascontext.js';\nimport { SVGContext } from './svgcontext.js';\nimport { isRenderContext } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nimport { isHTMLCanvas, isHTMLDiv } from './web.js';\nexport var RendererBackends;\n(function (RendererBackends) {\n    RendererBackends[RendererBackends[\"CANVAS\"] = 1] = \"CANVAS\";\n    RendererBackends[RendererBackends[\"SVG\"] = 2] = \"SVG\";\n})(RendererBackends || (RendererBackends = {}));\nexport var RendererLineEndType;\n(function (RendererLineEndType) {\n    RendererLineEndType[RendererLineEndType[\"NONE\"] = 1] = \"NONE\";\n    RendererLineEndType[RendererLineEndType[\"UP\"] = 2] = \"UP\";\n    RendererLineEndType[RendererLineEndType[\"DOWN\"] = 3] = \"DOWN\";\n})(RendererLineEndType || (RendererLineEndType = {}));\nexport class Renderer {\n    static buildContext(elementId, backend, width, height, background = '#FFF') {\n        const renderer = new Renderer(elementId, backend);\n        if (width && height) {\n            renderer.resize(width, height);\n        }\n        const ctx = renderer.getContext();\n        ctx.setBackgroundFillStyle(background);\n        Renderer.lastContext = ctx;\n        return ctx;\n    }\n    static getCanvasContext(elementId, width, height, background) {\n        return Renderer.buildContext(elementId, Renderer.Backends.CANVAS, width, height, background);\n    }\n    static getSVGContext(elementId, width, height, background) {\n        return Renderer.buildContext(elementId, Renderer.Backends.SVG, width, height, background);\n    }\n    static drawDashedLine(context, fromX, fromY, toX, toY, dashPattern) {\n        context.beginPath();\n        const dx = toX - fromX;\n        const dy = toY - fromY;\n        const angle = Math.atan2(dy, dx);\n        let x = fromX;\n        let y = fromY;\n        context.moveTo(fromX, fromY);\n        let idx = 0;\n        let draw = true;\n        while (!((dx < 0 ? x <= toX : x >= toX) && (dy < 0 ? y <= toY : y >= toY))) {\n            const dashLength = dashPattern[idx++ % dashPattern.length];\n            const nx = x + Math.cos(angle) * dashLength;\n            x = dx < 0 ? Math.max(toX, nx) : Math.min(toX, nx);\n            const ny = y + Math.sin(angle) * dashLength;\n            y = dy < 0 ? Math.max(toY, ny) : Math.min(toY, ny);\n            if (draw) {\n                context.lineTo(x, y);\n            }\n            else {\n                context.moveTo(x, y);\n            }\n            draw = !draw;\n        }\n        context.closePath();\n        context.stroke();\n    }\n    constructor(arg0, arg1) {\n        if (isRenderContext(arg0)) {\n            this.ctx = arg0;\n        }\n        else {\n            if (arg1 === undefined) {\n                throw new RuntimeError('InvalidArgument', 'Missing backend argument');\n            }\n            const backend = arg1;\n            let element;\n            if (typeof arg0 === 'string') {\n                const maybeElement = document.getElementById(arg0);\n                if (!maybeElement) {\n                    throw new RuntimeError('BadElementId', `Can't find element with ID \"${maybeElement}\"`);\n                }\n                element = maybeElement;\n            }\n            else {\n                element = arg0;\n            }\n            if (backend === Renderer.Backends.CANVAS) {\n                if (!isHTMLCanvas(element)) {\n                    throw new RuntimeError('BadElement', 'CANVAS context requires an HTMLCanvasElement.');\n                }\n                const context = element.getContext('2d', { willReadFrequently: true });\n                if (!context) {\n                    throw new RuntimeError('BadElement', \"Can't get canvas context\");\n                }\n                this.ctx = new CanvasContext(context);\n            }\n            else if (backend === Renderer.Backends.SVG) {\n                if (!isHTMLDiv(element)) {\n                    throw new RuntimeError('BadElement', 'SVG context requires an HTMLDivElement.');\n                }\n                this.ctx = new SVGContext(element);\n            }\n            else {\n                throw new RuntimeError('InvalidBackend', `No support for backend: ${backend}`);\n            }\n        }\n    }\n    resize(width, height) {\n        this.ctx.resize(width, height);\n        return this;\n    }\n    getContext() {\n        return this.ctx;\n    }\n}\nRenderer.Backends = RendererBackends;\nRenderer.LineEndType = RendererLineEndType;\nRenderer.lastContext = undefined;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,YAAY,EAAEC,SAAS,QAAQ,UAAU;AAClD,OAAO,IAAIC,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC3DA,gBAAgB,CAACA,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACzD,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO,IAAIC,mBAAmB;AAC9B,CAAC,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAACA,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7DA,mBAAmB,CAACA,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACzDA,mBAAmB,CAACA,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACjE,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,OAAO,MAAMC,QAAQ,CAAC;EAClB,OAAOC,YAAYA,CAACC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,GAAG,MAAM,EAAE;IACxE,MAAMC,QAAQ,GAAG,IAAIP,QAAQ,CAACE,SAAS,EAAEC,OAAO,CAAC;IACjD,IAAIC,KAAK,IAAIC,MAAM,EAAE;MACjBE,QAAQ,CAACC,MAAM,CAACJ,KAAK,EAAEC,MAAM,CAAC;IAClC;IACA,MAAMI,GAAG,GAAGF,QAAQ,CAACG,UAAU,CAAC,CAAC;IACjCD,GAAG,CAACE,sBAAsB,CAACL,UAAU,CAAC;IACtCN,QAAQ,CAACY,WAAW,GAAGH,GAAG;IAC1B,OAAOA,GAAG;EACd;EACA,OAAOI,gBAAgBA,CAACX,SAAS,EAAEE,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;IAC1D,OAAON,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAEF,QAAQ,CAACc,QAAQ,CAACC,MAAM,EAAEX,KAAK,EAAEC,MAAM,EAAEC,UAAU,CAAC;EAChG;EACA,OAAOU,aAAaA,CAACd,SAAS,EAAEE,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACvD,OAAON,QAAQ,CAACC,YAAY,CAACC,SAAS,EAAEF,QAAQ,CAACc,QAAQ,CAACG,GAAG,EAAEb,KAAK,EAAEC,MAAM,EAAEC,UAAU,CAAC;EAC7F;EACA,OAAOY,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,WAAW,EAAE;IAChEL,OAAO,CAACM,SAAS,CAAC,CAAC;IACnB,MAAMC,EAAE,GAAGJ,GAAG,GAAGF,KAAK;IACtB,MAAMO,EAAE,GAAGJ,GAAG,GAAGF,KAAK;IACtB,MAAMO,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,EAAE,EAAED,EAAE,CAAC;IAChC,IAAIK,CAAC,GAAGX,KAAK;IACb,IAAIY,CAAC,GAAGX,KAAK;IACbF,OAAO,CAACc,MAAM,CAACb,KAAK,EAAEC,KAAK,CAAC;IAC5B,IAAIa,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAG,IAAI;IACf,OAAO,EAAE,CAACT,EAAE,GAAG,CAAC,GAAGK,CAAC,IAAIT,GAAG,GAAGS,CAAC,IAAIT,GAAG,MAAMK,EAAE,GAAG,CAAC,GAAGK,CAAC,IAAIT,GAAG,GAAGS,CAAC,IAAIT,GAAG,CAAC,CAAC,EAAE;MACxE,MAAMa,UAAU,GAAGZ,WAAW,CAACU,GAAG,EAAE,GAAGV,WAAW,CAACa,MAAM,CAAC;MAC1D,MAAMC,EAAE,GAAGP,CAAC,GAAGF,IAAI,CAACU,GAAG,CAACX,KAAK,CAAC,GAAGQ,UAAU;MAC3CL,CAAC,GAAGL,EAAE,GAAG,CAAC,GAAGG,IAAI,CAACW,GAAG,CAAClB,GAAG,EAAEgB,EAAE,CAAC,GAAGT,IAAI,CAACY,GAAG,CAACnB,GAAG,EAAEgB,EAAE,CAAC;MAClD,MAAMI,EAAE,GAAGV,CAAC,GAAGH,IAAI,CAACc,GAAG,CAACf,KAAK,CAAC,GAAGQ,UAAU;MAC3CJ,CAAC,GAAGL,EAAE,GAAG,CAAC,GAAGE,IAAI,CAACW,GAAG,CAACjB,GAAG,EAAEmB,EAAE,CAAC,GAAGb,IAAI,CAACY,GAAG,CAAClB,GAAG,EAAEmB,EAAE,CAAC;MAClD,IAAIP,IAAI,EAAE;QACNhB,OAAO,CAACyB,MAAM,CAACb,CAAC,EAAEC,CAAC,CAAC;MACxB,CAAC,MACI;QACDb,OAAO,CAACc,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC;MACxB;MACAG,IAAI,GAAG,CAACA,IAAI;IAChB;IACAhB,OAAO,CAAC0B,SAAS,CAAC,CAAC;IACnB1B,OAAO,CAAC2B,MAAM,CAAC,CAAC;EACpB;EACAC,WAAWA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACpB,IAAIvD,eAAe,CAACsD,IAAI,CAAC,EAAE;MACvB,IAAI,CAACvC,GAAG,GAAGuC,IAAI;IACnB,CAAC,MACI;MACD,IAAIC,IAAI,KAAKC,SAAS,EAAE;QACpB,MAAM,IAAIvD,YAAY,CAAC,iBAAiB,EAAE,0BAA0B,CAAC;MACzE;MACA,MAAMQ,OAAO,GAAG8C,IAAI;MACpB,IAAIE,OAAO;MACX,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;QAC1B,MAAMI,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACN,IAAI,CAAC;QAClD,IAAI,CAACI,YAAY,EAAE;UACf,MAAM,IAAIzD,YAAY,CAAC,cAAc,EAAE,+BAA+ByD,YAAY,GAAG,CAAC;QAC1F;QACAD,OAAO,GAAGC,YAAY;MAC1B,CAAC,MACI;QACDD,OAAO,GAAGH,IAAI;MAClB;MACA,IAAI7C,OAAO,KAAKH,QAAQ,CAACc,QAAQ,CAACC,MAAM,EAAE;QACtC,IAAI,CAACnB,YAAY,CAACuD,OAAO,CAAC,EAAE;UACxB,MAAM,IAAIxD,YAAY,CAAC,YAAY,EAAE,+CAA+C,CAAC;QACzF;QACA,MAAMwB,OAAO,GAAGgC,OAAO,CAACzC,UAAU,CAAC,IAAI,EAAE;UAAE6C,kBAAkB,EAAE;QAAK,CAAC,CAAC;QACtE,IAAI,CAACpC,OAAO,EAAE;UACV,MAAM,IAAIxB,YAAY,CAAC,YAAY,EAAE,0BAA0B,CAAC;QACpE;QACA,IAAI,CAACc,GAAG,GAAG,IAAIjB,aAAa,CAAC2B,OAAO,CAAC;MACzC,CAAC,MACI,IAAIhB,OAAO,KAAKH,QAAQ,CAACc,QAAQ,CAACG,GAAG,EAAE;QACxC,IAAI,CAACpB,SAAS,CAACsD,OAAO,CAAC,EAAE;UACrB,MAAM,IAAIxD,YAAY,CAAC,YAAY,EAAE,yCAAyC,CAAC;QACnF;QACA,IAAI,CAACc,GAAG,GAAG,IAAIhB,UAAU,CAAC0D,OAAO,CAAC;MACtC,CAAC,MACI;QACD,MAAM,IAAIxD,YAAY,CAAC,gBAAgB,EAAE,2BAA2BQ,OAAO,EAAE,CAAC;MAClF;IACJ;EACJ;EACAK,MAAMA,CAACJ,KAAK,EAAEC,MAAM,EAAE;IAClB,IAAI,CAACI,GAAG,CAACD,MAAM,CAACJ,KAAK,EAAEC,MAAM,CAAC;IAC9B,OAAO,IAAI;EACf;EACAK,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,GAAG;EACnB;AACJ;AACAT,QAAQ,CAACc,QAAQ,GAAGhB,gBAAgB;AACpCE,QAAQ,CAACwD,WAAW,GAAGzD,mBAAmB;AAC1CC,QAAQ,CAACY,WAAW,GAAGsC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}