{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\VexFlowComponent.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Renderer, Stave, StaveNote, Voice, Formatter } from 'vexflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VexFlowComponent = ({\n  notes = [],\n  width = 500,\n  height = 200\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  useEffect(() => {\n    if (containerRef.current) {\n      // Clear previous content\n      containerRef.current.innerHTML = '';\n\n      // Create VexFlow renderer\n      const renderer = new Renderer(containerRef.current, Renderer.Backends.SVG);\n      renderer.resize(width, height);\n      const context = renderer.getContext();\n\n      // Create a stave\n      const stave = new Stave(10, 40, width - 20);\n      stave.addClef('treble').addTimeSignature('4/4');\n      stave.setContext(context).draw();\n\n      // Create notes (default example if no notes provided)\n      const defaultNotes = [new StaveNote({\n        keys: ['c/4'],\n        duration: 'q'\n      }), new StaveNote({\n        keys: ['d/4'],\n        duration: 'q'\n      }), new StaveNote({\n        keys: ['e/4'],\n        duration: 'q'\n      }), new StaveNote({\n        keys: ['f/4'],\n        duration: 'q'\n      })];\n\n      // Use provided notes or default notes\n      const staveNotes = notes.length > 0 ? notes : defaultNotes;\n\n      // Create a voice and add notes\n      const voice = new Voice({\n        num_beats: 4,\n        beat_value: 4\n      });\n      voice.addTickables(staveNotes);\n\n      // Format and draw\n      new Formatter().joinVoices([voice]).format([voice], width - 40);\n      voice.draw(context, stave);\n    }\n  }, [notes, width, height]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"Music Notation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        border: '1px solid #ccc',\n        borderRadius: '4px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(VexFlowComponent, \"8puyVO4ts1RhCfXUmci3vLI3Njw=\");\n_c = VexFlowComponent;\nexport default VexFlowComponent;\nvar _c;\n$RefreshReg$(_c, \"VexFlowComponent\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "<PERSON><PERSON><PERSON>", "Stave", "StaveNote", "Voice", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "VexFlowComponent", "notes", "width", "height", "_s", "containerRef", "current", "innerHTML", "renderer", "Backends", "SVG", "resize", "context", "getContext", "stave", "addClef", "addTimeSignature", "setContext", "draw", "defaultNotes", "keys", "duration", "staveNotes", "length", "voice", "num_beats", "beat_value", "addTickables", "joinVoices", "format", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "style", "border", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/VexFlowComponent.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Renderer, Stave, StaveNote, Voice, Formatter } from 'vexflow';\n\nconst VexFlowComponent = ({ notes = [], width = 500, height = 200 }) => {\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    if (containerRef.current) {\n      // Clear previous content\n      containerRef.current.innerHTML = '';\n\n      // Create VexFlow renderer\n      const renderer = new Renderer(containerRef.current, Renderer.Backends.SVG);\n      renderer.resize(width, height);\n      const context = renderer.getContext();\n\n      // Create a stave\n      const stave = new Stave(10, 40, width - 20);\n      stave.addClef('treble').addTimeSignature('4/4');\n      stave.setContext(context).draw();\n\n      // Create notes (default example if no notes provided)\n      const defaultNotes = [\n        new StaveNote({ keys: ['c/4'], duration: 'q' }),\n        new StaveNote({ keys: ['d/4'], duration: 'q' }),\n        new StaveNote({ keys: ['e/4'], duration: 'q' }),\n        new StaveNote({ keys: ['f/4'], duration: 'q' })\n      ];\n\n      // Use provided notes or default notes\n      const staveNotes = notes.length > 0 ? notes : defaultNotes;\n\n      // Create a voice and add notes\n      const voice = new Voice({ num_beats: 4, beat_value: 4 });\n      voice.addTickables(staveNotes);\n\n      // Format and draw\n      new Formatter().joinVoices([voice]).format([voice], width - 40);\n      voice.draw(context, stave);\n    }\n  }, [notes, width, height]);\n\n  return (\n    <div>\n      <h4>Music Notation</h4>\n      <div ref={containerRef} style={{ border: '1px solid #ccc', borderRadius: '4px' }}></div>\n    </div>\n  );\n};\n\nexport default VexFlowComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,SAAS,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,KAAK,GAAG,EAAE;EAAEC,KAAK,GAAG,GAAG;EAAEC,MAAM,GAAG;AAAI,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAMC,YAAY,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,IAAIc,YAAY,CAACC,OAAO,EAAE;MACxB;MACAD,YAAY,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;;MAEnC;MACA,MAAMC,QAAQ,GAAG,IAAIf,QAAQ,CAACY,YAAY,CAACC,OAAO,EAAEb,QAAQ,CAACgB,QAAQ,CAACC,GAAG,CAAC;MAC1EF,QAAQ,CAACG,MAAM,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC9B,MAAMS,OAAO,GAAGJ,QAAQ,CAACK,UAAU,CAAC,CAAC;;MAErC;MACA,MAAMC,KAAK,GAAG,IAAIpB,KAAK,CAAC,EAAE,EAAE,EAAE,EAAEQ,KAAK,GAAG,EAAE,CAAC;MAC3CY,KAAK,CAACC,OAAO,CAAC,QAAQ,CAAC,CAACC,gBAAgB,CAAC,KAAK,CAAC;MAC/CF,KAAK,CAACG,UAAU,CAACL,OAAO,CAAC,CAACM,IAAI,CAAC,CAAC;;MAEhC;MACA,MAAMC,YAAY,GAAG,CACnB,IAAIxB,SAAS,CAAC;QAAEyB,IAAI,EAAE,CAAC,KAAK,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC,EAC/C,IAAI1B,SAAS,CAAC;QAAEyB,IAAI,EAAE,CAAC,KAAK,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC,EAC/C,IAAI1B,SAAS,CAAC;QAAEyB,IAAI,EAAE,CAAC,KAAK,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC,EAC/C,IAAI1B,SAAS,CAAC;QAAEyB,IAAI,EAAE,CAAC,KAAK,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAC,CAAC,CAChD;;MAED;MACA,MAAMC,UAAU,GAAGrB,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,GAAGkB,YAAY;;MAE1D;MACA,MAAMK,KAAK,GAAG,IAAI5B,KAAK,CAAC;QAAE6B,SAAS,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;MACxDF,KAAK,CAACG,YAAY,CAACL,UAAU,CAAC;;MAE9B;MACA,IAAIzB,SAAS,CAAC,CAAC,CAAC+B,UAAU,CAAC,CAACJ,KAAK,CAAC,CAAC,CAACK,MAAM,CAAC,CAACL,KAAK,CAAC,EAAEtB,KAAK,GAAG,EAAE,CAAC;MAC/DsB,KAAK,CAACN,IAAI,CAACN,OAAO,EAAEE,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACb,KAAK,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAAC;EAE1B,oBACEJ,OAAA;IAAA+B,QAAA,gBACE/B,OAAA;MAAA+B,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvBnC,OAAA;MAAKoC,GAAG,EAAE9B,YAAa;MAAC+B,KAAK,EAAE;QAAEC,MAAM,EAAE,gBAAgB;QAAEC,YAAY,EAAE;MAAM;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrF,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA7CIJ,gBAAgB;AAAAuC,EAAA,GAAhBvC,gBAAgB;AA+CtB,eAAeA,gBAAgB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}