{"ast": null, "code": "import { StaveTie } from './stavetie.js';\nexport class TabTie extends StaveTie {\n  static get CATEGORY() {\n    return \"TabTie\";\n  }\n  static createHammeron(notes) {\n    return new TabTie(notes, 'H');\n  }\n  static createPulloff(notes) {\n    return new TabTie(notes, 'P');\n  }\n  constructor(notes, text) {\n    super(notes, text);\n    this.renderOptions.cp1 = 9;\n    this.renderOptions.cp2 = 11;\n    this.renderOptions.yShift = 3;\n    this.direction = -1;\n  }\n}", "map": {"version": 3, "names": ["StaveTie", "<PERSON><PERSON><PERSON><PERSON>", "CATEGORY", "createHammeron", "notes", "create<PERSON><PERSON>off", "constructor", "text", "renderOptions", "cp1", "cp2", "yShift", "direction"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tabtie.js"], "sourcesContent": ["import { StaveTie } from './stavetie.js';\nexport class TabTie extends StaveTie {\n    static get CATEGORY() {\n        return \"TabTie\";\n    }\n    static createHammeron(notes) {\n        return new TabTie(notes, 'H');\n    }\n    static createPulloff(notes) {\n        return new TabTie(notes, 'P');\n    }\n    constructor(notes, text) {\n        super(notes, text);\n        this.renderOptions.cp1 = 9;\n        this.renderOptions.cp2 = 11;\n        this.renderOptions.yShift = 3;\n        this.direction = -1;\n    }\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,MAAMC,MAAM,SAASD,QAAQ,CAAC;EACjC,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,QAAQ;EACnB;EACA,OAAOC,cAAcA,CAACC,KAAK,EAAE;IACzB,OAAO,IAAIH,MAAM,CAACG,KAAK,EAAE,GAAG,CAAC;EACjC;EACA,OAAOC,aAAaA,CAACD,KAAK,EAAE;IACxB,OAAO,IAAIH,MAAM,CAACG,KAAK,EAAE,GAAG,CAAC;EACjC;EACAE,WAAWA,CAACF,KAAK,EAAEG,IAAI,EAAE;IACrB,KAAK,CAACH,KAAK,EAAEG,IAAI,CAAC;IAClB,IAAI,CAACC,aAAa,CAACC,GAAG,GAAG,CAAC;IAC1B,IAAI,CAACD,aAAa,CAACE,GAAG,GAAG,EAAE;IAC3B,IAAI,CAACF,aAAa,CAACG,MAAM,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}