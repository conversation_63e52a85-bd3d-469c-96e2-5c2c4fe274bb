{"ast": null, "code": "import { Element } from './element.js';\nimport { Formatter } from './formatter.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Tuplet extends Element {\n  static get CATEGORY() {\n    return \"Tuplet\";\n  }\n  static get LOCATION_TOP() {\n    return 1;\n  }\n  static get LOCATION_BOTTOM() {\n    return -1;\n  }\n  static get NESTING_OFFSET() {\n    return 15;\n  }\n  constructor(notes, options = {}) {\n    super();\n    if (!notes || !notes.length) {\n      throw new RuntimeError('BadArguments', 'No notes provided for tuplet.');\n    }\n    this.notes = notes;\n    const numNotes = options.numNotes !== undefined ? options.numNotes : notes.length;\n    const notesOccupied = options.notesOccupied || 2;\n    const bracketed = options.bracketed !== undefined ? options.bracketed : notes.some(note => !note.hasBeam());\n    const ratioed = options.ratioed !== undefined ? options.ratioed : Math.abs(notesOccupied - numNotes) > 1;\n    const location = options.location || Tuplet.LOCATION_TOP;\n    const yOffset = options.yOffset || Metrics.get('Tuplet.yOffset');\n    const textYOffset = options.textYOffset || Metrics.get('Tuplet.textYOffset');\n    this.options = {\n      bracketed,\n      location,\n      notesOccupied,\n      numNotes,\n      ratioed,\n      yOffset,\n      textYOffset\n    };\n    this.textElement = new Element('Tuplet');\n    this.setTupletLocation(location || Tuplet.LOCATION_TOP);\n    Formatter.AlignRestsToNotes(notes, true, true);\n    this.resolveGlyphs();\n    this.attach();\n  }\n  attach() {\n    for (let i = 0; i < this.notes.length; i++) {\n      const note = this.notes[i];\n      note.setTuplet(this);\n    }\n  }\n  detach() {\n    for (let i = 0; i < this.notes.length; i++) {\n      const note = this.notes[i];\n      note.resetTuplet(this);\n    }\n  }\n  setBracketed(bracketed) {\n    this.options.bracketed = bracketed;\n    return this;\n  }\n  setRatioed(ratioed) {\n    this.options.ratioed = ratioed;\n    return this;\n  }\n  setTupletLocation(location) {\n    if (location !== Tuplet.LOCATION_TOP && location !== Tuplet.LOCATION_BOTTOM) {\n      console.warn(`Invalid tuplet location [${location}]. Using Tuplet.LOCATION_TOP.`);\n      location = Tuplet.LOCATION_TOP;\n    }\n    this.options.location = location;\n    return this;\n  }\n  getNotes() {\n    return this.notes;\n  }\n  getNoteCount() {\n    return this.options.numNotes;\n  }\n  getNotesOccupied() {\n    return this.options.notesOccupied;\n  }\n  setNotesOccupied(notes) {\n    this.detach();\n    this.options.notesOccupied = notes;\n    this.resolveGlyphs();\n    this.attach();\n  }\n  resolveGlyphs() {\n    let numerator = '';\n    let denominator = '';\n    let n = this.options.numNotes;\n    while (n >= 1) {\n      numerator = String.fromCharCode(0xe880 + n % 10) + numerator;\n      n = Math.floor(n / 10);\n    }\n    if (this.options.ratioed) {\n      n = this.options.notesOccupied;\n      while (n >= 1) {\n        denominator = String.fromCharCode(0xe880 + n % 10) + denominator;\n        n = Math.floor(n / 10);\n      }\n      denominator = Glyphs.tupletColon + denominator;\n    }\n    this.textElement.setText(numerator + denominator);\n  }\n  getNestedTupletCount() {\n    const {\n      location\n    } = this.options;\n    const firstNote = this.notes[0];\n    let maxTupletCount = countTuplets(firstNote, location);\n    let minTupletCount = countTuplets(firstNote, location);\n    function countTuplets(note, location) {\n      return note.getTupletStack().filter(tuplet => tuplet.options.location === location).length;\n    }\n    this.notes.forEach(note => {\n      const tupletCount = countTuplets(note, location);\n      maxTupletCount = tupletCount > maxTupletCount ? tupletCount : maxTupletCount;\n      minTupletCount = tupletCount < minTupletCount ? tupletCount : minTupletCount;\n    });\n    return maxTupletCount - minTupletCount;\n  }\n  getYPosition() {\n    var _a;\n    const nestedTupletYOffset = this.getNestedTupletCount() * Tuplet.NESTING_OFFSET * -this.options.location;\n    const yOffset = (_a = this.options.yOffset) !== null && _a !== void 0 ? _a : 0;\n    const firstNote = this.notes[0];\n    let yPosition;\n    if (this.options.location === Tuplet.LOCATION_TOP) {\n      yPosition = firstNote.checkStave().getYForLine(0) - 1.5 * Tables.STAVE_LINE_DISTANCE;\n      for (let i = 0; i < this.notes.length; ++i) {\n        const note = this.notes[i];\n        let modLines = 0;\n        const mc = note.getModifierContext();\n        if (mc) {\n          modLines = Math.max(modLines, mc.getState().topTextLine);\n        }\n        const modY = note.getYForTopText(modLines) - 2 * Tables.STAVE_LINE_DISTANCE;\n        if (note.hasStem() || note.isRest()) {\n          const topY = note.getStemDirection() === Stem.UP ? note.getStemExtents().topY - Tables.STAVE_LINE_DISTANCE : note.getStemExtents().baseY - 2 * Tables.STAVE_LINE_DISTANCE;\n          yPosition = Math.min(topY, yPosition);\n          if (modLines > 0) {\n            yPosition = Math.min(modY, yPosition);\n          }\n        }\n      }\n    } else {\n      let lineCheck = 4;\n      this.notes.forEach(nn => {\n        const mc = nn.getModifierContext();\n        if (mc) {\n          lineCheck = Math.max(lineCheck, mc.getState().textLine + 1);\n        }\n      });\n      yPosition = firstNote.checkStave().getYForLine(lineCheck) + 2 * Tables.STAVE_LINE_DISTANCE;\n      for (let i = 0; i < this.notes.length; ++i) {\n        if (this.notes[i].hasStem() || this.notes[i].isRest()) {\n          const bottomY = this.notes[i].getStemDirection() === Stem.UP ? this.notes[i].getStemExtents().baseY + 2 * Tables.STAVE_LINE_DISTANCE : this.notes[i].getStemExtents().topY + Tables.STAVE_LINE_DISTANCE;\n          if (bottomY > yPosition) {\n            yPosition = bottomY;\n          }\n        }\n      }\n    }\n    return yPosition + nestedTupletYOffset + yOffset;\n  }\n  draw() {\n    const {\n      location,\n      bracketed,\n      textYOffset\n    } = this.options;\n    const ctx = this.checkContext();\n    let xPos = 0;\n    let yPos = 0;\n    const firstNote = this.notes[0];\n    const lastNote = this.notes[this.notes.length - 1];\n    if (!bracketed) {\n      xPos = firstNote.getStemX();\n      this.width = lastNote.getStemX() - xPos;\n    } else {\n      xPos = firstNote.getTieLeftX() - 5;\n      this.width = lastNote.getTieRightX() - xPos + 5;\n    }\n    yPos = this.getYPosition();\n    const notationCenterX = xPos + this.width / 2;\n    const notationStartX = notationCenterX - this.textElement.getWidth() / 2;\n    ctx.openGroup('tuplet', this.getAttribute('id'));\n    if (bracketed) {\n      const lineWidth = this.width / 2 - this.textElement.getWidth() / 2 - 5;\n      if (lineWidth > 0) {\n        ctx.fillRect(xPos, yPos, lineWidth, 1);\n        ctx.fillRect(xPos + this.width / 2 + this.textElement.getWidth() / 2 + 5, yPos, lineWidth, 1);\n        ctx.fillRect(xPos, yPos + (location === Tuplet.LOCATION_BOTTOM ? 1 : 0), 1, location * 10);\n        ctx.fillRect(xPos + this.width, yPos + (location === Tuplet.LOCATION_BOTTOM ? 1 : 0), 1, location * 10);\n      }\n    }\n    this.textElement.renderText(ctx, notationStartX, yPos + this.textElement.getHeight() / 2 + (location === Tuplet.LOCATION_TOP ? -1 : 1) * textYOffset);\n    const bb = this.getBoundingBox();\n    ctx.pointerRect(bb.getX(), bb.getY(), bb.getW(), bb.getH());\n    ctx.closeGroup();\n    this.setRendered();\n  }\n}", "map": {"version": 3, "names": ["Element", "<PERSON><PERSON><PERSON>", "Glyphs", "Metrics", "<PERSON><PERSON>", "Tables", "RuntimeError", "Tuplet", "CATEGORY", "LOCATION_TOP", "LOCATION_BOTTOM", "NESTING_OFFSET", "constructor", "notes", "options", "length", "numNotes", "undefined", "notesOccupied", "bracketed", "some", "note", "hasBeam", "ratioed", "Math", "abs", "location", "yOffset", "get", "textYOffset", "textElement", "setTupletLocation", "AlignRestsToNotes", "resolveGlyphs", "attach", "i", "setTuplet", "detach", "resetTuplet", "setBracketed", "setRatioed", "console", "warn", "getNotes", "getNoteCount", "getNotesOccupied", "set<PERSON><PERSON><PERSON>ccupied", "numerator", "denominator", "n", "String", "fromCharCode", "floor", "tupletColon", "setText", "getNestedTupletCount", "firstNote", "maxTupletCount", "countTuplets", "minTupletCount", "getTupletStack", "filter", "tuplet", "for<PERSON>ach", "tupletCount", "getYPosition", "_a", "nestedTupletYOffset", "yPosition", "checkStave", "getYForLine", "STAVE_LINE_DISTANCE", "modLines", "mc", "getModifierContext", "max", "getState", "topTextLine", "modY", "getYForTopText", "hasStem", "isRest", "topY", "getStemDirection", "UP", "getStemExtents", "baseY", "min", "lineCheck", "nn", "textLine", "bottomY", "draw", "ctx", "checkContext", "xPos", "yPos", "lastNote", "getStemX", "width", "getTieLeftX", "getTieRightX", "notationCenterX", "notationStartX", "getWidth", "openGroup", "getAttribute", "lineWidth", "fillRect", "renderText", "getHeight", "bb", "getBoundingBox", "pointerRect", "getX", "getY", "getW", "getH", "closeGroup", "setRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tuplet.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Formatter } from './formatter.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class Tuplet extends Element {\n    static get CATEGORY() {\n        return \"Tuplet\";\n    }\n    static get LOCATION_TOP() {\n        return 1;\n    }\n    static get LOCATION_BOTTOM() {\n        return -1;\n    }\n    static get NESTING_OFFSET() {\n        return 15;\n    }\n    constructor(notes, options = {}) {\n        super();\n        if (!notes || !notes.length) {\n            throw new RuntimeError('BadArguments', 'No notes provided for tuplet.');\n        }\n        this.notes = notes;\n        const numNotes = options.numNotes !== undefined ? options.numNotes : notes.length;\n        const notesOccupied = options.notesOccupied || 2;\n        const bracketed = options.bracketed !== undefined ? options.bracketed : notes.some((note) => !note.hasBeam());\n        const ratioed = options.ratioed !== undefined ? options.ratioed : Math.abs(notesOccupied - numNotes) > 1;\n        const location = options.location || Tuplet.LOCATION_TOP;\n        const yOffset = options.yOffset || Metrics.get('Tuplet.yOffset');\n        const textYOffset = options.textYOffset || Metrics.get('Tuplet.textYOffset');\n        this.options = {\n            bracketed,\n            location,\n            notesOccupied,\n            numNotes,\n            ratioed,\n            yOffset,\n            textYOffset,\n        };\n        this.textElement = new Element('Tuplet');\n        this.setTupletLocation(location || Tuplet.LOCATION_TOP);\n        Formatter.AlignRestsToNotes(notes, true, true);\n        this.resolveGlyphs();\n        this.attach();\n    }\n    attach() {\n        for (let i = 0; i < this.notes.length; i++) {\n            const note = this.notes[i];\n            note.setTuplet(this);\n        }\n    }\n    detach() {\n        for (let i = 0; i < this.notes.length; i++) {\n            const note = this.notes[i];\n            note.resetTuplet(this);\n        }\n    }\n    setBracketed(bracketed) {\n        this.options.bracketed = bracketed;\n        return this;\n    }\n    setRatioed(ratioed) {\n        this.options.ratioed = ratioed;\n        return this;\n    }\n    setTupletLocation(location) {\n        if (location !== Tuplet.LOCATION_TOP && location !== Tuplet.LOCATION_BOTTOM) {\n            console.warn(`Invalid tuplet location [${location}]. Using Tuplet.LOCATION_TOP.`);\n            location = Tuplet.LOCATION_TOP;\n        }\n        this.options.location = location;\n        return this;\n    }\n    getNotes() {\n        return this.notes;\n    }\n    getNoteCount() {\n        return this.options.numNotes;\n    }\n    getNotesOccupied() {\n        return this.options.notesOccupied;\n    }\n    setNotesOccupied(notes) {\n        this.detach();\n        this.options.notesOccupied = notes;\n        this.resolveGlyphs();\n        this.attach();\n    }\n    resolveGlyphs() {\n        let numerator = '';\n        let denominator = '';\n        let n = this.options.numNotes;\n        while (n >= 1) {\n            numerator = String.fromCharCode(0xe880 + (n % 10)) + numerator;\n            n = Math.floor(n / 10);\n        }\n        if (this.options.ratioed) {\n            n = this.options.notesOccupied;\n            while (n >= 1) {\n                denominator = String.fromCharCode(0xe880 + (n % 10)) + denominator;\n                n = Math.floor(n / 10);\n            }\n            denominator = Glyphs.tupletColon + denominator;\n        }\n        this.textElement.setText(numerator + denominator);\n    }\n    getNestedTupletCount() {\n        const { location } = this.options;\n        const firstNote = this.notes[0];\n        let maxTupletCount = countTuplets(firstNote, location);\n        let minTupletCount = countTuplets(firstNote, location);\n        function countTuplets(note, location) {\n            return note.getTupletStack().filter((tuplet) => tuplet.options.location === location).length;\n        }\n        this.notes.forEach((note) => {\n            const tupletCount = countTuplets(note, location);\n            maxTupletCount = tupletCount > maxTupletCount ? tupletCount : maxTupletCount;\n            minTupletCount = tupletCount < minTupletCount ? tupletCount : minTupletCount;\n        });\n        return maxTupletCount - minTupletCount;\n    }\n    getYPosition() {\n        var _a;\n        const nestedTupletYOffset = this.getNestedTupletCount() * Tuplet.NESTING_OFFSET * -this.options.location;\n        const yOffset = (_a = this.options.yOffset) !== null && _a !== void 0 ? _a : 0;\n        const firstNote = this.notes[0];\n        let yPosition;\n        if (this.options.location === Tuplet.LOCATION_TOP) {\n            yPosition = firstNote.checkStave().getYForLine(0) - 1.5 * Tables.STAVE_LINE_DISTANCE;\n            for (let i = 0; i < this.notes.length; ++i) {\n                const note = this.notes[i];\n                let modLines = 0;\n                const mc = note.getModifierContext();\n                if (mc) {\n                    modLines = Math.max(modLines, mc.getState().topTextLine);\n                }\n                const modY = note.getYForTopText(modLines) - 2 * Tables.STAVE_LINE_DISTANCE;\n                if (note.hasStem() || note.isRest()) {\n                    const topY = note.getStemDirection() === Stem.UP\n                        ? note.getStemExtents().topY - Tables.STAVE_LINE_DISTANCE\n                        : note.getStemExtents().baseY - 2 * Tables.STAVE_LINE_DISTANCE;\n                    yPosition = Math.min(topY, yPosition);\n                    if (modLines > 0) {\n                        yPosition = Math.min(modY, yPosition);\n                    }\n                }\n            }\n        }\n        else {\n            let lineCheck = 4;\n            this.notes.forEach((nn) => {\n                const mc = nn.getModifierContext();\n                if (mc) {\n                    lineCheck = Math.max(lineCheck, mc.getState().textLine + 1);\n                }\n            });\n            yPosition = firstNote.checkStave().getYForLine(lineCheck) + 2 * Tables.STAVE_LINE_DISTANCE;\n            for (let i = 0; i < this.notes.length; ++i) {\n                if (this.notes[i].hasStem() || this.notes[i].isRest()) {\n                    const bottomY = this.notes[i].getStemDirection() === Stem.UP\n                        ? this.notes[i].getStemExtents().baseY + 2 * Tables.STAVE_LINE_DISTANCE\n                        : this.notes[i].getStemExtents().topY + Tables.STAVE_LINE_DISTANCE;\n                    if (bottomY > yPosition) {\n                        yPosition = bottomY;\n                    }\n                }\n            }\n        }\n        return yPosition + nestedTupletYOffset + yOffset;\n    }\n    draw() {\n        const { location, bracketed, textYOffset } = this.options;\n        const ctx = this.checkContext();\n        let xPos = 0;\n        let yPos = 0;\n        const firstNote = this.notes[0];\n        const lastNote = this.notes[this.notes.length - 1];\n        if (!bracketed) {\n            xPos = firstNote.getStemX();\n            this.width = lastNote.getStemX() - xPos;\n        }\n        else {\n            xPos = firstNote.getTieLeftX() - 5;\n            this.width = lastNote.getTieRightX() - xPos + 5;\n        }\n        yPos = this.getYPosition();\n        const notationCenterX = xPos + this.width / 2;\n        const notationStartX = notationCenterX - this.textElement.getWidth() / 2;\n        ctx.openGroup('tuplet', this.getAttribute('id'));\n        if (bracketed) {\n            const lineWidth = this.width / 2 - this.textElement.getWidth() / 2 - 5;\n            if (lineWidth > 0) {\n                ctx.fillRect(xPos, yPos, lineWidth, 1);\n                ctx.fillRect(xPos + this.width / 2 + this.textElement.getWidth() / 2 + 5, yPos, lineWidth, 1);\n                ctx.fillRect(xPos, yPos + (location === Tuplet.LOCATION_BOTTOM ? 1 : 0), 1, location * 10);\n                ctx.fillRect(xPos + this.width, yPos + (location === Tuplet.LOCATION_BOTTOM ? 1 : 0), 1, location * 10);\n            }\n        }\n        this.textElement.renderText(ctx, notationStartX, yPos + this.textElement.getHeight() / 2 + (location === Tuplet.LOCATION_TOP ? -1 : 1) * textYOffset);\n        const bb = this.getBoundingBox();\n        ctx.pointerRect(bb.getX(), bb.getY(), bb.getW(), bb.getH());\n        ctx.closeGroup();\n        this.setRendered();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,MAAM,SAASP,OAAO,CAAC;EAChC,WAAWQ,QAAQA,CAAA,EAAG;IAClB,OAAO,QAAQ;EACnB;EACA,WAAWC,YAAYA,CAAA,EAAG;IACtB,OAAO,CAAC;EACZ;EACA,WAAWC,eAAeA,CAAA,EAAG;IACzB,OAAO,CAAC,CAAC;EACb;EACA,WAAWC,cAAcA,CAAA,EAAG;IACxB,OAAO,EAAE;EACb;EACAC,WAAWA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,EAAE;MACzB,MAAM,IAAIT,YAAY,CAAC,cAAc,EAAE,+BAA+B,CAAC;IAC3E;IACA,IAAI,CAACO,KAAK,GAAGA,KAAK;IAClB,MAAMG,QAAQ,GAAGF,OAAO,CAACE,QAAQ,KAAKC,SAAS,GAAGH,OAAO,CAACE,QAAQ,GAAGH,KAAK,CAACE,MAAM;IACjF,MAAMG,aAAa,GAAGJ,OAAO,CAACI,aAAa,IAAI,CAAC;IAChD,MAAMC,SAAS,GAAGL,OAAO,CAACK,SAAS,KAAKF,SAAS,GAAGH,OAAO,CAACK,SAAS,GAAGN,KAAK,CAACO,IAAI,CAAEC,IAAI,IAAK,CAACA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IAC7G,MAAMC,OAAO,GAAGT,OAAO,CAACS,OAAO,KAAKN,SAAS,GAAGH,OAAO,CAACS,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACP,aAAa,GAAGF,QAAQ,CAAC,GAAG,CAAC;IACxG,MAAMU,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,IAAInB,MAAM,CAACE,YAAY;IACxD,MAAMkB,OAAO,GAAGb,OAAO,CAACa,OAAO,IAAIxB,OAAO,CAACyB,GAAG,CAAC,gBAAgB,CAAC;IAChE,MAAMC,WAAW,GAAGf,OAAO,CAACe,WAAW,IAAI1B,OAAO,CAACyB,GAAG,CAAC,oBAAoB,CAAC;IAC5E,IAAI,CAACd,OAAO,GAAG;MACXK,SAAS;MACTO,QAAQ;MACRR,aAAa;MACbF,QAAQ;MACRO,OAAO;MACPI,OAAO;MACPE;IACJ,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,IAAI9B,OAAO,CAAC,QAAQ,CAAC;IACxC,IAAI,CAAC+B,iBAAiB,CAACL,QAAQ,IAAInB,MAAM,CAACE,YAAY,CAAC;IACvDR,SAAS,CAAC+B,iBAAiB,CAACnB,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACoB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EACAA,MAAMA,CAAA,EAAG;IACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACE,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACxC,MAAMd,IAAI,GAAG,IAAI,CAACR,KAAK,CAACsB,CAAC,CAAC;MAC1Bd,IAAI,CAACe,SAAS,CAAC,IAAI,CAAC;IACxB;EACJ;EACAC,MAAMA,CAAA,EAAG;IACL,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACE,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACxC,MAAMd,IAAI,GAAG,IAAI,CAACR,KAAK,CAACsB,CAAC,CAAC;MAC1Bd,IAAI,CAACiB,WAAW,CAAC,IAAI,CAAC;IAC1B;EACJ;EACAC,YAAYA,CAACpB,SAAS,EAAE;IACpB,IAAI,CAACL,OAAO,CAACK,SAAS,GAAGA,SAAS;IAClC,OAAO,IAAI;EACf;EACAqB,UAAUA,CAACjB,OAAO,EAAE;IAChB,IAAI,CAACT,OAAO,CAACS,OAAO,GAAGA,OAAO;IAC9B,OAAO,IAAI;EACf;EACAQ,iBAAiBA,CAACL,QAAQ,EAAE;IACxB,IAAIA,QAAQ,KAAKnB,MAAM,CAACE,YAAY,IAAIiB,QAAQ,KAAKnB,MAAM,CAACG,eAAe,EAAE;MACzE+B,OAAO,CAACC,IAAI,CAAC,4BAA4BhB,QAAQ,+BAA+B,CAAC;MACjFA,QAAQ,GAAGnB,MAAM,CAACE,YAAY;IAClC;IACA,IAAI,CAACK,OAAO,CAACY,QAAQ,GAAGA,QAAQ;IAChC,OAAO,IAAI;EACf;EACAiB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC9B,KAAK;EACrB;EACA+B,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC9B,OAAO,CAACE,QAAQ;EAChC;EACA6B,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC/B,OAAO,CAACI,aAAa;EACrC;EACA4B,gBAAgBA,CAACjC,KAAK,EAAE;IACpB,IAAI,CAACwB,MAAM,CAAC,CAAC;IACb,IAAI,CAACvB,OAAO,CAACI,aAAa,GAAGL,KAAK;IAClC,IAAI,CAACoB,aAAa,CAAC,CAAC;IACpB,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EACAD,aAAaA,CAAA,EAAG;IACZ,IAAIc,SAAS,GAAG,EAAE;IAClB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,CAAC,GAAG,IAAI,CAACnC,OAAO,CAACE,QAAQ;IAC7B,OAAOiC,CAAC,IAAI,CAAC,EAAE;MACXF,SAAS,GAAGG,MAAM,CAACC,YAAY,CAAC,MAAM,GAAIF,CAAC,GAAG,EAAG,CAAC,GAAGF,SAAS;MAC9DE,CAAC,GAAGzB,IAAI,CAAC4B,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC;IAC1B;IACA,IAAI,IAAI,CAACnC,OAAO,CAACS,OAAO,EAAE;MACtB0B,CAAC,GAAG,IAAI,CAACnC,OAAO,CAACI,aAAa;MAC9B,OAAO+B,CAAC,IAAI,CAAC,EAAE;QACXD,WAAW,GAAGE,MAAM,CAACC,YAAY,CAAC,MAAM,GAAIF,CAAC,GAAG,EAAG,CAAC,GAAGD,WAAW;QAClEC,CAAC,GAAGzB,IAAI,CAAC4B,KAAK,CAACH,CAAC,GAAG,EAAE,CAAC;MAC1B;MACAD,WAAW,GAAG9C,MAAM,CAACmD,WAAW,GAAGL,WAAW;IAClD;IACA,IAAI,CAAClB,WAAW,CAACwB,OAAO,CAACP,SAAS,GAAGC,WAAW,CAAC;EACrD;EACAO,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAE7B;IAAS,CAAC,GAAG,IAAI,CAACZ,OAAO;IACjC,MAAM0C,SAAS,GAAG,IAAI,CAAC3C,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAI4C,cAAc,GAAGC,YAAY,CAACF,SAAS,EAAE9B,QAAQ,CAAC;IACtD,IAAIiC,cAAc,GAAGD,YAAY,CAACF,SAAS,EAAE9B,QAAQ,CAAC;IACtD,SAASgC,YAAYA,CAACrC,IAAI,EAAEK,QAAQ,EAAE;MAClC,OAAOL,IAAI,CAACuC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAChD,OAAO,CAACY,QAAQ,KAAKA,QAAQ,CAAC,CAACX,MAAM;IAChG;IACA,IAAI,CAACF,KAAK,CAACkD,OAAO,CAAE1C,IAAI,IAAK;MACzB,MAAM2C,WAAW,GAAGN,YAAY,CAACrC,IAAI,EAAEK,QAAQ,CAAC;MAChD+B,cAAc,GAAGO,WAAW,GAAGP,cAAc,GAAGO,WAAW,GAAGP,cAAc;MAC5EE,cAAc,GAAGK,WAAW,GAAGL,cAAc,GAAGK,WAAW,GAAGL,cAAc;IAChF,CAAC,CAAC;IACF,OAAOF,cAAc,GAAGE,cAAc;EAC1C;EACAM,YAAYA,CAAA,EAAG;IACX,IAAIC,EAAE;IACN,MAAMC,mBAAmB,GAAG,IAAI,CAACZ,oBAAoB,CAAC,CAAC,GAAGhD,MAAM,CAACI,cAAc,GAAG,CAAC,IAAI,CAACG,OAAO,CAACY,QAAQ;IACxG,MAAMC,OAAO,GAAG,CAACuC,EAAE,GAAG,IAAI,CAACpD,OAAO,CAACa,OAAO,MAAM,IAAI,IAAIuC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC9E,MAAMV,SAAS,GAAG,IAAI,CAAC3C,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAIuD,SAAS;IACb,IAAI,IAAI,CAACtD,OAAO,CAACY,QAAQ,KAAKnB,MAAM,CAACE,YAAY,EAAE;MAC/C2D,SAAS,GAAGZ,SAAS,CAACa,UAAU,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGjE,MAAM,CAACkE,mBAAmB;MACpF,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACE,MAAM,EAAE,EAAEoB,CAAC,EAAE;QACxC,MAAMd,IAAI,GAAG,IAAI,CAACR,KAAK,CAACsB,CAAC,CAAC;QAC1B,IAAIqC,QAAQ,GAAG,CAAC;QAChB,MAAMC,EAAE,GAAGpD,IAAI,CAACqD,kBAAkB,CAAC,CAAC;QACpC,IAAID,EAAE,EAAE;UACJD,QAAQ,GAAGhD,IAAI,CAACmD,GAAG,CAACH,QAAQ,EAAEC,EAAE,CAACG,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC;QAC5D;QACA,MAAMC,IAAI,GAAGzD,IAAI,CAAC0D,cAAc,CAACP,QAAQ,CAAC,GAAG,CAAC,GAAGnE,MAAM,CAACkE,mBAAmB;QAC3E,IAAIlD,IAAI,CAAC2D,OAAO,CAAC,CAAC,IAAI3D,IAAI,CAAC4D,MAAM,CAAC,CAAC,EAAE;UACjC,MAAMC,IAAI,GAAG7D,IAAI,CAAC8D,gBAAgB,CAAC,CAAC,KAAK/E,IAAI,CAACgF,EAAE,GAC1C/D,IAAI,CAACgE,cAAc,CAAC,CAAC,CAACH,IAAI,GAAG7E,MAAM,CAACkE,mBAAmB,GACvDlD,IAAI,CAACgE,cAAc,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC,GAAGjF,MAAM,CAACkE,mBAAmB;UAClEH,SAAS,GAAG5C,IAAI,CAAC+D,GAAG,CAACL,IAAI,EAAEd,SAAS,CAAC;UACrC,IAAII,QAAQ,GAAG,CAAC,EAAE;YACdJ,SAAS,GAAG5C,IAAI,CAAC+D,GAAG,CAACT,IAAI,EAAEV,SAAS,CAAC;UACzC;QACJ;MACJ;IACJ,CAAC,MACI;MACD,IAAIoB,SAAS,GAAG,CAAC;MACjB,IAAI,CAAC3E,KAAK,CAACkD,OAAO,CAAE0B,EAAE,IAAK;QACvB,MAAMhB,EAAE,GAAGgB,EAAE,CAACf,kBAAkB,CAAC,CAAC;QAClC,IAAID,EAAE,EAAE;UACJe,SAAS,GAAGhE,IAAI,CAACmD,GAAG,CAACa,SAAS,EAAEf,EAAE,CAACG,QAAQ,CAAC,CAAC,CAACc,QAAQ,GAAG,CAAC,CAAC;QAC/D;MACJ,CAAC,CAAC;MACFtB,SAAS,GAAGZ,SAAS,CAACa,UAAU,CAAC,CAAC,CAACC,WAAW,CAACkB,SAAS,CAAC,GAAG,CAAC,GAAGnF,MAAM,CAACkE,mBAAmB;MAC1F,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACE,MAAM,EAAE,EAAEoB,CAAC,EAAE;QACxC,IAAI,IAAI,CAACtB,KAAK,CAACsB,CAAC,CAAC,CAAC6C,OAAO,CAAC,CAAC,IAAI,IAAI,CAACnE,KAAK,CAACsB,CAAC,CAAC,CAAC8C,MAAM,CAAC,CAAC,EAAE;UACnD,MAAMU,OAAO,GAAG,IAAI,CAAC9E,KAAK,CAACsB,CAAC,CAAC,CAACgD,gBAAgB,CAAC,CAAC,KAAK/E,IAAI,CAACgF,EAAE,GACtD,IAAI,CAACvE,KAAK,CAACsB,CAAC,CAAC,CAACkD,cAAc,CAAC,CAAC,CAACC,KAAK,GAAG,CAAC,GAAGjF,MAAM,CAACkE,mBAAmB,GACrE,IAAI,CAAC1D,KAAK,CAACsB,CAAC,CAAC,CAACkD,cAAc,CAAC,CAAC,CAACH,IAAI,GAAG7E,MAAM,CAACkE,mBAAmB;UACtE,IAAIoB,OAAO,GAAGvB,SAAS,EAAE;YACrBA,SAAS,GAAGuB,OAAO;UACvB;QACJ;MACJ;IACJ;IACA,OAAOvB,SAAS,GAAGD,mBAAmB,GAAGxC,OAAO;EACpD;EACAiE,IAAIA,CAAA,EAAG;IACH,MAAM;MAAElE,QAAQ;MAAEP,SAAS;MAAEU;IAAY,CAAC,GAAG,IAAI,CAACf,OAAO;IACzD,MAAM+E,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,MAAMxC,SAAS,GAAG,IAAI,CAAC3C,KAAK,CAAC,CAAC,CAAC;IAC/B,MAAMoF,QAAQ,GAAG,IAAI,CAACpF,KAAK,CAAC,IAAI,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;IAClD,IAAI,CAACI,SAAS,EAAE;MACZ4E,IAAI,GAAGvC,SAAS,CAAC0C,QAAQ,CAAC,CAAC;MAC3B,IAAI,CAACC,KAAK,GAAGF,QAAQ,CAACC,QAAQ,CAAC,CAAC,GAAGH,IAAI;IAC3C,CAAC,MACI;MACDA,IAAI,GAAGvC,SAAS,CAAC4C,WAAW,CAAC,CAAC,GAAG,CAAC;MAClC,IAAI,CAACD,KAAK,GAAGF,QAAQ,CAACI,YAAY,CAAC,CAAC,GAAGN,IAAI,GAAG,CAAC;IACnD;IACAC,IAAI,GAAG,IAAI,CAAC/B,YAAY,CAAC,CAAC;IAC1B,MAAMqC,eAAe,GAAGP,IAAI,GAAG,IAAI,CAACI,KAAK,GAAG,CAAC;IAC7C,MAAMI,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACxE,WAAW,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC;IACxEX,GAAG,CAACY,SAAS,CAAC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAChD,IAAIvF,SAAS,EAAE;MACX,MAAMwF,SAAS,GAAG,IAAI,CAACR,KAAK,GAAG,CAAC,GAAG,IAAI,CAACrE,WAAW,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;MACtE,IAAIG,SAAS,GAAG,CAAC,EAAE;QACfd,GAAG,CAACe,QAAQ,CAACb,IAAI,EAAEC,IAAI,EAAEW,SAAS,EAAE,CAAC,CAAC;QACtCd,GAAG,CAACe,QAAQ,CAACb,IAAI,GAAG,IAAI,CAACI,KAAK,GAAG,CAAC,GAAG,IAAI,CAACrE,WAAW,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAER,IAAI,EAAEW,SAAS,EAAE,CAAC,CAAC;QAC7Fd,GAAG,CAACe,QAAQ,CAACb,IAAI,EAAEC,IAAI,IAAItE,QAAQ,KAAKnB,MAAM,CAACG,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEgB,QAAQ,GAAG,EAAE,CAAC;QAC1FmE,GAAG,CAACe,QAAQ,CAACb,IAAI,GAAG,IAAI,CAACI,KAAK,EAAEH,IAAI,IAAItE,QAAQ,KAAKnB,MAAM,CAACG,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEgB,QAAQ,GAAG,EAAE,CAAC;MAC3G;IACJ;IACA,IAAI,CAACI,WAAW,CAAC+E,UAAU,CAAChB,GAAG,EAAEU,cAAc,EAAEP,IAAI,GAAG,IAAI,CAAClE,WAAW,CAACgF,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAACpF,QAAQ,KAAKnB,MAAM,CAACE,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIoB,WAAW,CAAC;IACrJ,MAAMkF,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAChCnB,GAAG,CAACoB,WAAW,CAACF,EAAE,CAACG,IAAI,CAAC,CAAC,EAAEH,EAAE,CAACI,IAAI,CAAC,CAAC,EAAEJ,EAAE,CAACK,IAAI,CAAC,CAAC,EAAEL,EAAE,CAACM,IAAI,CAAC,CAAC,CAAC;IAC3DxB,GAAG,CAACyB,UAAU,CAAC,CAAC;IAChB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}