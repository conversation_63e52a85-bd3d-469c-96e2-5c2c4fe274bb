{"ast": null, "code": "import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nfunction calculateStemDirection(notes) {\n  let lineSum = 0;\n  notes.forEach(note => {\n    if (note.keyProps) {\n      note.keyProps.forEach(keyProp => {\n        lineSum += keyProp.line - 3;\n      });\n    }\n  });\n  if (lineSum >= 0) {\n    return Stem.DOWN;\n  }\n  return Stem.UP;\n}\nfunction getStemSlope(firstNote, lastNote) {\n  const firstStemTipY = firstNote.getStemExtents().topY;\n  const firstStemX = firstNote.getStemX();\n  const lastStemTipY = lastNote.getStemExtents().topY;\n  const lastStemX = lastNote.getStemX();\n  return (lastStemTipY - firstStemTipY) / (lastStemX - firstStemX);\n}\nexport const BEAM_LEFT = 'L';\nexport const BEAM_RIGHT = 'R';\nexport const BEAM_BOTH = 'B';\nexport class Beam extends Element {\n  static get CATEGORY() {\n    return \"Beam\";\n  }\n  getStemDirection() {\n    return this._stemDirection;\n  }\n  static getDefaultBeamGroups(timeSig) {\n    if (!timeSig || timeSig === 'c') {\n      timeSig = '4/4';\n    }\n    const defaults = {\n      '1/2': ['1/2'],\n      '2/2': ['1/2'],\n      '3/2': ['1/2'],\n      '4/2': ['1/2'],\n      '1/4': ['1/4'],\n      '2/4': ['1/4'],\n      '3/4': ['1/4'],\n      '4/4': ['1/4'],\n      '1/8': ['1/8'],\n      '2/8': ['2/8'],\n      '3/8': ['3/8'],\n      '4/8': ['2/8'],\n      '1/16': ['1/16'],\n      '2/16': ['2/16'],\n      '3/16': ['3/16'],\n      '4/16': ['2/16']\n    };\n    const groups = defaults[timeSig];\n    if (groups === undefined) {\n      const beatTotal = parseInt(timeSig.split('/')[0], 10);\n      const beatValue = parseInt(timeSig.split('/')[1], 10);\n      const tripleMeter = beatTotal % 3 === 0;\n      if (tripleMeter) {\n        return [new Fraction(3, beatValue)];\n      } else if (beatValue > 4) {\n        return [new Fraction(2, beatValue)];\n      } else if (beatValue <= 4) {\n        return [new Fraction(1, beatValue)];\n      }\n    } else {\n      return groups.map(group => new Fraction().parse(group));\n    }\n    return [new Fraction(1, 4)];\n  }\n  static applyAndGetBeams(voice, stemDirection, groups) {\n    return Beam.generateBeams(voice.getTickables(), {\n      groups,\n      stemDirection\n    });\n  }\n  static generateBeams(notes, config = {}) {\n    if (!config.groups || !config.groups.length) {\n      config.groups = [new Fraction(2, 8)];\n    }\n    const tickGroups = config.groups.map(group => {\n      if (!group.multiply) {\n        throw new RuntimeError('InvalidBeamGroups', 'The beam groups must be an array of VexFlow.Fractions');\n      }\n      return group.clone().multiply(Tables.RESOLUTION, 1);\n    });\n    const unprocessedNotes = notes;\n    let currentTickGroup = 0;\n    let noteGroups = [];\n    let currentGroup = [];\n    function getTotalTicks(notes) {\n      return notes.reduce((memo, note) => note.getTicks().clone().add(memo), new Fraction(0, 1));\n    }\n    function nextTickGroup() {\n      if (tickGroups.length - 1 > currentTickGroup) {\n        currentTickGroup += 1;\n      } else {\n        currentTickGroup = 0;\n      }\n    }\n    function createGroups() {\n      let nextGroup = [];\n      let currentGroupTotalTicks = new Fraction(0, 1);\n      unprocessedNotes.forEach(unprocessedNote => {\n        nextGroup = [];\n        if (unprocessedNote.shouldIgnoreTicks()) {\n          noteGroups.push(currentGroup);\n          currentGroup = nextGroup;\n          return;\n        }\n        currentGroup.push(unprocessedNote);\n        const ticksPerGroup = tickGroups[currentTickGroup].clone();\n        const totalTicks = getTotalTicks(currentGroup).add(currentGroupTotalTicks);\n        const unbeamable = Tables.durationToNumber(unprocessedNote.getDuration()) < 8;\n        if (unbeamable && unprocessedNote.getTuplet()) {\n          ticksPerGroup.numerator *= 2;\n        }\n        if (totalTicks.greaterThan(ticksPerGroup)) {\n          if (!unbeamable) {\n            const note = currentGroup.pop();\n            if (note) nextGroup.push(note);\n          }\n          noteGroups.push(currentGroup);\n          do {\n            currentGroupTotalTicks = totalTicks.subtract(tickGroups[currentTickGroup]);\n            nextTickGroup();\n          } while (currentGroupTotalTicks.greaterThanEquals(tickGroups[currentTickGroup]));\n          currentGroup = nextGroup;\n        } else if (totalTicks.equals(ticksPerGroup)) {\n          noteGroups.push(currentGroup);\n          currentGroupTotalTicks = new Fraction(0, 1);\n          currentGroup = nextGroup;\n          nextTickGroup();\n        }\n      });\n      if (currentGroup.length > 0) {\n        noteGroups.push(currentGroup);\n      }\n    }\n    function getBeamGroups() {\n      return noteGroups.filter(group => {\n        if (group.length > 1) {\n          let beamable = true;\n          group.forEach(note => {\n            if (note.getIntrinsicTicks() >= Tables.durationToTicks('4')) {\n              beamable = false;\n            }\n          });\n          return beamable;\n        }\n        return false;\n      });\n    }\n    function sanitizeGroups() {\n      const sanitizedGroups = [];\n      noteGroups.forEach(group => {\n        let tempGroup = [];\n        group.forEach((note, index, group) => {\n          const isFirstOrLast = index === 0 || index === group.length - 1;\n          const prevNote = group[index - 1];\n          const breaksOnEachRest = !config.beamRests && note.isRest();\n          const breaksOnFirstOrLastRest = config.beamRests && config.beamMiddleOnly && note.isRest() && isFirstOrLast;\n          let breakOnStemChange = false;\n          if (config.maintainStemDirections && prevNote && !note.isRest() && !prevNote.isRest()) {\n            const prevDirection = prevNote.getStemDirection();\n            const currentDirection = note.getStemDirection();\n            breakOnStemChange = currentDirection !== prevDirection;\n          }\n          const isUnbeamableDuration = parseInt(note.getDuration(), 10) < 8;\n          const shouldBreak = breaksOnEachRest || breaksOnFirstOrLastRest || breakOnStemChange || isUnbeamableDuration;\n          if (shouldBreak) {\n            if (tempGroup.length > 0) {\n              sanitizedGroups.push(tempGroup);\n            }\n            tempGroup = breakOnStemChange ? [note] : [];\n          } else {\n            tempGroup.push(note);\n          }\n        });\n        if (tempGroup.length > 0) {\n          sanitizedGroups.push(tempGroup);\n        }\n      });\n      noteGroups = sanitizedGroups;\n    }\n    function formatStems() {\n      noteGroups.forEach(group => {\n        let stemDirection;\n        if (config.maintainStemDirections) {\n          const note = findFirstNote(group);\n          stemDirection = note ? note.getStemDirection() : Stem.UP;\n        } else {\n          if (config.stemDirection) {\n            stemDirection = config.stemDirection;\n          } else {\n            stemDirection = calculateStemDirection(group);\n          }\n        }\n        applyStemDirection(group, stemDirection);\n      });\n    }\n    function findFirstNote(group) {\n      for (let i = 0; i < group.length; i++) {\n        const note = group[i];\n        if (!note.isRest()) {\n          return note;\n        }\n      }\n      return false;\n    }\n    function applyStemDirection(group, direction) {\n      group.forEach(note => {\n        note.setStemDirection(direction);\n      });\n    }\n    function getTuplets() {\n      const uniqueTuplets = [];\n      noteGroups.forEach(group => {\n        let tuplet;\n        group.forEach(note => {\n          const noteTuplet = note.getTuplet();\n          if (noteTuplet && tuplet !== noteTuplet) {\n            tuplet = noteTuplet;\n            uniqueTuplets.push(tuplet);\n          }\n        });\n      });\n      return uniqueTuplets;\n    }\n    createGroups();\n    sanitizeGroups();\n    formatStems();\n    const beamedNoteGroups = getBeamGroups();\n    const allTuplets = getTuplets();\n    const beams = [];\n    beamedNoteGroups.forEach(group => {\n      const beam = new Beam(group);\n      if (config.showStemlets) {\n        beam.renderOptions.showStemlets = true;\n      }\n      if (config.secondaryBreaks) {\n        beam.renderOptions.secondaryBreakTicks = Tables.durationToTicks(config.secondaryBreaks);\n      }\n      if (config.flatBeams === true) {\n        beam.renderOptions.flatBeams = true;\n        beam.renderOptions.flatBeamOffset = config.flatBeamOffset;\n      }\n      beams.push(beam);\n    });\n    allTuplets.forEach(tuplet => {\n      const direction = tuplet.notes[0].stemDirection === Stem.DOWN ? -1 : 1;\n      tuplet.setTupletLocation(direction);\n      let bracketed = false;\n      for (let i = 0; i < tuplet.notes.length; i++) {\n        const note = tuplet.notes[i];\n        if (!note.hasBeam()) {\n          bracketed = true;\n          break;\n        }\n      }\n      tuplet.setBracketed(bracketed);\n    });\n    return beams;\n  }\n  constructor(notes, autoStem = false) {\n    super();\n    this.slope = 0;\n    this.yShift = 0;\n    this.forcedPartialDirections = {};\n    if (!notes || notes.length === 0) {\n      throw new RuntimeError('BadArguments', 'No notes provided for beam.');\n    }\n    if (notes.length === 1) {\n      throw new RuntimeError('BadArguments', 'Too few notes for beam.');\n    }\n    this._ticks = notes[0].getIntrinsicTicks();\n    if (this._ticks >= Tables.durationToTicks('4')) {\n      throw new RuntimeError('BadArguments', 'Beams can only be applied to notes shorter than a quarter note.');\n    }\n    let i;\n    let note;\n    this._stemDirection = notes[0].getStemDirection();\n    let stemDirection = this._stemDirection;\n    if (autoStem && isStaveNote(notes[0])) {\n      stemDirection = calculateStemDirection(notes);\n    } else if (autoStem && isTabNote(notes[0])) {\n      const stemWeight = notes.reduce((memo, note) => memo + note.getStemDirection(), 0);\n      stemDirection = stemWeight > -1 ? Stem.UP : Stem.DOWN;\n    }\n    for (i = 0; i < notes.length; ++i) {\n      note = notes[i];\n      if (autoStem) {\n        note.setStemDirection(stemDirection);\n        this._stemDirection = stemDirection;\n      }\n      note.setBeam(this);\n    }\n    this.postFormatted = false;\n    this.notes = notes;\n    this._beamCount = this.getBeamCount();\n    this.breakOnIndexes = [];\n    this.renderOptions = {\n      beamWidth: 5,\n      maxSlope: 0.25,\n      minSlope: -0.25,\n      slopeIterations: 20,\n      slopeCost: 100,\n      showStemlets: false,\n      stemletExtension: 7,\n      partialBeamLength: 10,\n      flatBeams: false,\n      minFlatBeamOffset: 15\n    };\n  }\n  getNotes() {\n    return this.notes;\n  }\n  getBeamCount() {\n    const beamCounts = this.notes.map(note => note.getGlyphProps().beamCount);\n    const maxBeamCount = beamCounts.reduce((max, beamCount) => beamCount > max ? beamCount : max);\n    return maxBeamCount;\n  }\n  breakSecondaryAt(indexes) {\n    this.breakOnIndexes = indexes;\n    return this;\n  }\n  setPartialBeamSideAt(noteIndex, side) {\n    this.forcedPartialDirections[noteIndex] = side;\n    return this;\n  }\n  unsetPartialBeamSideAt(noteIndex) {\n    delete this.forcedPartialDirections[noteIndex];\n    return this;\n  }\n  getSlopeY(x, firstX, firstY, slope) {\n    return firstY + (x - firstX) * slope;\n  }\n  calculateSlope() {\n    const {\n      notes,\n      renderOptions: {\n        maxSlope,\n        minSlope,\n        slopeIterations,\n        slopeCost\n      }\n    } = this;\n    const stemDirection = this._stemDirection;\n    const firstNote = notes[0];\n    const initialSlope = getStemSlope(firstNote, notes[notes.length - 1]);\n    const increment = (maxSlope - minSlope) / slopeIterations;\n    let minCost = Number.MAX_VALUE;\n    let bestSlope = 0;\n    let yShift = 0;\n    for (let slope = minSlope; slope <= maxSlope; slope += increment) {\n      let totalStemExtension = 0;\n      let yShiftTemp = 0;\n      for (let i = 1; i < notes.length; ++i) {\n        const note = notes[i];\n        if (note.hasStem() || note.isRest()) {\n          const adjustedStemTipY = this.getSlopeY(note.getStemX(), firstNote.getStemX(), firstNote.getStemExtents().topY, slope) + yShiftTemp;\n          const stemTipY = note.getStemExtents().topY;\n          if (stemTipY * stemDirection < adjustedStemTipY * stemDirection) {\n            const diff = Math.abs(stemTipY - adjustedStemTipY);\n            yShiftTemp += diff * -stemDirection;\n            totalStemExtension += diff * i;\n          } else {\n            totalStemExtension += (stemTipY - adjustedStemTipY) * stemDirection;\n          }\n        }\n      }\n      const idealSlope = initialSlope / 2;\n      const distanceFromIdeal = Math.abs(idealSlope - slope);\n      const cost = slopeCost * distanceFromIdeal + Math.abs(totalStemExtension);\n      if (cost < minCost) {\n        minCost = cost;\n        bestSlope = slope;\n        yShift = yShiftTemp;\n      }\n    }\n    this.slope = bestSlope;\n    this.yShift = yShift;\n  }\n  calculateFlatSlope() {\n    const {\n      notes,\n      renderOptions: {\n        beamWidth,\n        minFlatBeamOffset,\n        flatBeamOffset\n      }\n    } = this;\n    const stemDirection = this._stemDirection;\n    let total = 0;\n    let extremeY = 0;\n    let extremeBeamCount = 0;\n    let currentExtreme = 0;\n    for (let i = 0; i < notes.length; i++) {\n      const note = notes[i];\n      const stemTipY = note.getStemExtents().topY;\n      total += stemTipY;\n      if (stemDirection === Stem.DOWN && currentExtreme < stemTipY) {\n        currentExtreme = stemTipY;\n        extremeY = Math.max(...note.getYs());\n        extremeBeamCount = note.getBeamCount();\n      } else if (stemDirection === Stem.UP && (currentExtreme === 0 || currentExtreme > stemTipY)) {\n        currentExtreme = stemTipY;\n        extremeY = Math.min(...note.getYs());\n        extremeBeamCount = note.getBeamCount();\n      }\n    }\n    let offset = total / notes.length;\n    const extremeTest = minFlatBeamOffset + extremeBeamCount * beamWidth * 1.5;\n    const newOffset = extremeY + extremeTest * -stemDirection;\n    if (stemDirection === Stem.DOWN && offset < newOffset) {\n      offset = extremeY + extremeTest;\n    } else if (stemDirection === Stem.UP && offset > newOffset) {\n      offset = extremeY - extremeTest;\n    }\n    if (!flatBeamOffset) {\n      this.renderOptions.flatBeamOffset = offset;\n    } else if (stemDirection === Stem.DOWN && offset > flatBeamOffset) {\n      this.renderOptions.flatBeamOffset = offset;\n    } else if (stemDirection === Stem.UP && offset < flatBeamOffset) {\n      this.renderOptions.flatBeamOffset = offset;\n    }\n    this.slope = 0;\n    this.yShift = 0;\n  }\n  getBeamYToDraw() {\n    const firstNote = this.notes[0];\n    const firstStemTipY = firstNote.getStemExtents().topY;\n    let beamY = firstStemTipY;\n    if (this.renderOptions.flatBeams && this.renderOptions.flatBeamOffset) {\n      beamY = this.renderOptions.flatBeamOffset;\n    }\n    return beamY;\n  }\n  applyStemExtensions() {\n    const {\n      notes,\n      slope,\n      renderOptions: {\n        showStemlets,\n        stemletExtension,\n        beamWidth\n      }\n    } = this;\n    const yShift = this.yShift;\n    const beamCount = this._beamCount;\n    const firstNote = notes[0];\n    const firstStemTipY = this.getBeamYToDraw();\n    const firstStemX = firstNote.getStemX();\n    for (let i = 0; i < notes.length; ++i) {\n      const note = notes[i];\n      const stem = note.getStem();\n      if (stem) {\n        const stemX = note.getStemX();\n        const {\n          topY: stemTipY\n        } = note.getStemExtents();\n        const beamedStemTipY = this.getSlopeY(stemX, firstStemX, firstStemTipY, slope) + yShift;\n        const preBeamExtension = stem.getExtension();\n        const beamExtension = note.getStemDirection() === Stem.UP ? stemTipY - beamedStemTipY : beamedStemTipY - stemTipY;\n        let crossStemExtension = 0;\n        if (note.getStemDirection() !== this._stemDirection) {\n          const beamCount = note.getGlyphProps().beamCount;\n          crossStemExtension = (1 + (beamCount - 1) * 1.5) * this.renderOptions.beamWidth;\n        }\n        stem.setExtension(preBeamExtension + beamExtension + crossStemExtension);\n        stem.adjustHeightForBeam();\n        if (note.isRest() && showStemlets) {\n          const totalBeamWidth = (beamCount - 1) * beamWidth * 1.5 + beamWidth;\n          stem.setVisibility(true).setStemlet(true, totalBeamWidth + stemletExtension);\n        }\n      }\n    }\n  }\n  lookupBeamDirection(duration, prevTick, tick, nextTick, noteIndex) {\n    if (duration === '4') {\n      return BEAM_LEFT;\n    }\n    const forcedBeamDirection = this.forcedPartialDirections[noteIndex];\n    if (forcedBeamDirection) return forcedBeamDirection;\n    const lookupDuration = `${Tables.durationToNumber(duration) / 2}`;\n    const prevNoteGetsBeam = prevTick < Tables.durationToTicks(lookupDuration);\n    const nextNoteGetsBeam = nextTick < Tables.durationToTicks(lookupDuration);\n    const noteGetsBeam = tick < Tables.durationToTicks(lookupDuration);\n    if (prevNoteGetsBeam && nextNoteGetsBeam && noteGetsBeam) {\n      return BEAM_BOTH;\n    } else if (prevNoteGetsBeam && !nextNoteGetsBeam && noteGetsBeam) {\n      return BEAM_LEFT;\n    } else if (!prevNoteGetsBeam && nextNoteGetsBeam && noteGetsBeam) {\n      return BEAM_RIGHT;\n    }\n    return this.lookupBeamDirection(lookupDuration, prevTick, tick, nextTick, noteIndex);\n  }\n  getBeamLines(duration) {\n    const tickOfDuration = Tables.durationToTicks(duration);\n    let beamStarted = false;\n    const beamLines = [];\n    let currentBeam = undefined;\n    const partialBeamLength = this.renderOptions.partialBeamLength;\n    let previousShouldBreak = false;\n    let tickTally = 0;\n    for (let i = 0; i < this.notes.length; ++i) {\n      const note = this.notes[i];\n      const ticks = note.getTicks().value();\n      tickTally += ticks;\n      let shouldBreak = false;\n      if (parseInt(duration, 10) >= 8) {\n        shouldBreak = this.breakOnIndexes.indexOf(i) !== -1;\n        if (this.renderOptions.secondaryBreakTicks && tickTally >= this.renderOptions.secondaryBreakTicks) {\n          tickTally = 0;\n          shouldBreak = true;\n        }\n      }\n      const noteGetsBeam = note.getIntrinsicTicks() < tickOfDuration;\n      const stemX = note.getStemX() - Stem.WIDTH / 2;\n      const prevNote = this.notes[i - 1];\n      const nextNote = this.notes[i + 1];\n      const nextNoteGetsBeam = nextNote && nextNote.getIntrinsicTicks() < tickOfDuration;\n      const prevNoteGetsBeam = prevNote && prevNote.getIntrinsicTicks() < tickOfDuration;\n      const beamAlone = prevNote && nextNote && noteGetsBeam && !prevNoteGetsBeam && !nextNoteGetsBeam;\n      if (noteGetsBeam) {\n        if (beamStarted) {\n          currentBeam = beamLines[beamLines.length - 1];\n          currentBeam.end = stemX;\n          if (shouldBreak) {\n            beamStarted = false;\n            if (nextNote && !nextNoteGetsBeam && currentBeam.end === undefined) {\n              currentBeam.end = currentBeam.start - partialBeamLength;\n            }\n          }\n        } else {\n          currentBeam = {\n            start: stemX,\n            end: undefined\n          };\n          beamStarted = true;\n          if (beamAlone) {\n            const prevTick = prevNote.getIntrinsicTicks();\n            const nextTick = nextNote.getIntrinsicTicks();\n            const tick = note.getIntrinsicTicks();\n            const beamDirection = this.lookupBeamDirection(duration, prevTick, tick, nextTick, i);\n            if ([BEAM_LEFT, BEAM_BOTH].includes(beamDirection)) {\n              currentBeam.end = currentBeam.start - partialBeamLength;\n            } else {\n              currentBeam.end = currentBeam.start + partialBeamLength;\n            }\n          } else if (!nextNoteGetsBeam) {\n            if ((previousShouldBreak || i === 0) && nextNote) {\n              currentBeam.end = currentBeam.start + partialBeamLength;\n            } else {\n              currentBeam.end = currentBeam.start - partialBeamLength;\n            }\n          } else if (shouldBreak) {\n            currentBeam.end = currentBeam.start - partialBeamLength;\n            beamStarted = false;\n          }\n          beamLines.push(currentBeam);\n        }\n      } else {\n        beamStarted = false;\n      }\n      previousShouldBreak = shouldBreak;\n    }\n    const lastBeam = beamLines[beamLines.length - 1];\n    if (lastBeam && lastBeam.end === undefined) {\n      lastBeam.end = lastBeam.start - partialBeamLength;\n    }\n    return beamLines;\n  }\n  drawStems(ctx) {\n    this.notes.forEach(note => {\n      const stem = note.getStem();\n      if (stem) {\n        const stemX = note.getStemX();\n        stem.setNoteHeadXBounds(stemX, stemX);\n        stem.setContext(ctx).drawWithStyle();\n      }\n    }, this);\n  }\n  drawBeamLines(ctx) {\n    const validBeamDurations = ['4', '8', '16', '32', '64'];\n    const firstNote = this.notes[0];\n    let beamY = this.getBeamYToDraw();\n    const firstStemX = firstNote.getStemX();\n    const beamThickness = this.renderOptions.beamWidth * this._stemDirection;\n    for (let i = 0; i < validBeamDurations.length; ++i) {\n      const duration = validBeamDurations[i];\n      const beamLines = this.getBeamLines(duration);\n      for (let j = 0; j < beamLines.length; ++j) {\n        const beamLine = beamLines[j];\n        const startBeamX = beamLine.start;\n        const startBeamY = this.getSlopeY(startBeamX, firstStemX, beamY, this.slope);\n        const lastBeamX = beamLine.end;\n        if (lastBeamX) {\n          const lastBeamY = this.getSlopeY(lastBeamX, firstStemX, beamY, this.slope);\n          ctx.beginPath();\n          ctx.moveTo(startBeamX, startBeamY);\n          ctx.lineTo(startBeamX, startBeamY + beamThickness);\n          ctx.lineTo(lastBeamX + 1, lastBeamY + beamThickness);\n          ctx.lineTo(lastBeamX + 1, lastBeamY);\n          ctx.closePath();\n          ctx.fill();\n        } else {\n          throw new RuntimeError('NoLastBeamX', 'lastBeamX undefined.');\n        }\n      }\n      beamY += beamThickness * 1.5;\n    }\n  }\n  preFormat() {\n    return this;\n  }\n  postFormat() {\n    if (this.postFormatted) return;\n    if (isTabNote(this.notes[0]) || this.renderOptions.flatBeams) {\n      this.calculateFlatSlope();\n    } else {\n      this.calculateSlope();\n    }\n    this.applyStemExtensions();\n    this.postFormatted = true;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    if (this.unbeamable) return;\n    if (!this.postFormatted) {\n      this.postFormat();\n    }\n    ctx.openGroup('beam', this.getAttribute('id'));\n    this.drawStems(ctx);\n    this.drawBeamLines(ctx);\n    ctx.closeGroup();\n  }\n}", "map": {"version": 3, "names": ["Element", "Fraction", "<PERSON><PERSON>", "Tables", "isStaveNote", "isTabNote", "RuntimeError", "calculateStemDirection", "notes", "lineSum", "for<PERSON>ach", "note", "keyProps", "keyProp", "line", "DOWN", "UP", "getStemSlope", "firstNote", "lastNote", "firstStemTipY", "getStemExtents", "topY", "firstStemX", "getStemX", "lastStemTipY", "lastStemX", "BEAM_LEFT", "BEAM_RIGHT", "BEAM_BOTH", "<PERSON><PERSON>", "CATEGORY", "getStemDirection", "_stemDirection", "getDefaultBeamGroups", "timeSig", "defaults", "groups", "undefined", "beatTotal", "parseInt", "split", "beatValue", "tripleMeter", "map", "group", "parse", "applyAndGetBeams", "voice", "stemDirection", "generateBeams", "getTickables", "config", "length", "tickGroups", "multiply", "clone", "RESOLUTION", "unprocessedNotes", "currentTickGroup", "noteGroups", "currentGroup", "getTotalTicks", "reduce", "memo", "getTicks", "add", "nextTickGroup", "createGroups", "nextGroup", "currentGroupTotalTicks", "unprocessedNote", "shouldIgnoreTicks", "push", "ticksPerGroup", "totalTicks", "unbeamable", "durationToNumber", "getDuration", "getTuplet", "numerator", "greaterThan", "pop", "subtract", "greaterThanEquals", "equals", "getBeamGroups", "filter", "beamable", "getIntrinsicTicks", "durationToTicks", "sanitizeGroups", "sanitizedGroups", "tempGroup", "index", "isFirstOrLast", "prevNote", "breaksOnEachRest", "beamRests", "isRest", "breaksOnFirstOrLastRest", "beamMiddleOnly", "breakOnStemChange", "maintainStemDirections", "prevDirection", "currentDirection", "isUnbeamableDuration", "shouldBreak", "formatStems", "findFirstNote", "applyStemDirection", "i", "direction", "setStemDirection", "getTuplets", "uniqueTuplets", "tuplet", "noteTuplet", "beamedNoteGroups", "allTuplets", "beams", "beam", "showStemlets", "renderOptions", "secondaryBreaks", "secondaryBreakTicks", "flatBeams", "flatBeamOffset", "setTupletLocation", "bracketed", "hasBeam", "setBracketed", "constructor", "autoStem", "slope", "yShift", "forcedPartialDirections", "_ticks", "stemWeight", "setBeam", "postFormatted", "_beamCount", "getBeamCount", "breakOnIndexes", "beamWidth", "maxSlope", "minSlope", "slopeIterations", "slopeCost", "stemletExtension", "partialBeam<PERSON>ength", "minFlatBeamOffset", "getNotes", "beamCounts", "getGlyphProps", "beamCount", "maxBeam<PERSON>ount", "max", "breakSecondaryAt", "indexes", "setPartialBeamSideAt", "noteIndex", "side", "unsetPartialBeamSideAt", "getSlopeY", "x", "firstX", "firstY", "calculateSlope", "initialSlope", "increment", "minCost", "Number", "MAX_VALUE", "bestSlope", "totalStemExtension", "yShiftTemp", "hasStem", "adjustedStemTipY", "stemTipY", "diff", "Math", "abs", "idealSlope", "distanceFromIdeal", "cost", "calculateFlatSlope", "total", "extremeY", "extremeBeamCount", "currentExtreme", "getYs", "min", "offset", "extremeTest", "newOffset", "getBeamYToDraw", "beamY", "applyStemExtensions", "stem", "getStem", "stemX", "beamedStemTipY", "preBeamExtension", "getExtension", "beamExtension", "crossStemExtension", "setExtension", "adjustHeightForBeam", "totalBeamWidth", "setVisibility", "setStemlet", "lookupBeamDirection", "duration", "prevTick", "tick", "nextTick", "forcedBeamDirection", "lookupDuration", "prevNoteGetsBeam", "nextNoteGetsBeam", "noteGetsBeam", "getBeamLines", "tickOfDuration", "beamStarted", "beamLines", "currentBeam", "previousShouldBreak", "tickTally", "ticks", "value", "indexOf", "WIDTH", "nextNote", "beamAlone", "end", "start", "beamDirection", "includes", "lastBeam", "drawStems", "ctx", "setNoteHeadXBounds", "setContext", "drawWithStyle", "drawBeamLines", "validBeamDurations", "beamThickness", "j", "beamLine", "startBeamX", "startBeamY", "lastBeamX", "lastBeamY", "beginPath", "moveTo", "lineTo", "closePath", "fill", "preFormat", "postFormat", "draw", "checkContext", "setRendered", "openGroup", "getAttribute", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/beam.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nfunction calculateStemDirection(notes) {\n    let lineSum = 0;\n    notes.forEach((note) => {\n        if (note.keyProps) {\n            note.keyProps.forEach((keyProp) => {\n                lineSum += keyProp.line - 3;\n            });\n        }\n    });\n    if (lineSum >= 0) {\n        return Stem.DOWN;\n    }\n    return Stem.UP;\n}\nfunction getStemSlope(firstNote, lastNote) {\n    const firstStemTipY = firstNote.getStemExtents().topY;\n    const firstStemX = firstNote.getStemX();\n    const lastStemTipY = lastNote.getStemExtents().topY;\n    const lastStemX = lastNote.getStemX();\n    return (lastStemTipY - firstStemTipY) / (lastStemX - firstStemX);\n}\nexport const BEAM_LEFT = 'L';\nexport const BEAM_RIGHT = 'R';\nexport const BEAM_BOTH = 'B';\nexport class Beam extends Element {\n    static get CATEGORY() {\n        return \"Beam\";\n    }\n    getStemDirection() {\n        return this._stemDirection;\n    }\n    static getDefaultBeamGroups(timeSig) {\n        if (!timeSig || timeSig === 'c') {\n            timeSig = '4/4';\n        }\n        const defaults = {\n            '1/2': ['1/2'],\n            '2/2': ['1/2'],\n            '3/2': ['1/2'],\n            '4/2': ['1/2'],\n            '1/4': ['1/4'],\n            '2/4': ['1/4'],\n            '3/4': ['1/4'],\n            '4/4': ['1/4'],\n            '1/8': ['1/8'],\n            '2/8': ['2/8'],\n            '3/8': ['3/8'],\n            '4/8': ['2/8'],\n            '1/16': ['1/16'],\n            '2/16': ['2/16'],\n            '3/16': ['3/16'],\n            '4/16': ['2/16'],\n        };\n        const groups = defaults[timeSig];\n        if (groups === undefined) {\n            const beatTotal = parseInt(timeSig.split('/')[0], 10);\n            const beatValue = parseInt(timeSig.split('/')[1], 10);\n            const tripleMeter = beatTotal % 3 === 0;\n            if (tripleMeter) {\n                return [new Fraction(3, beatValue)];\n            }\n            else if (beatValue > 4) {\n                return [new Fraction(2, beatValue)];\n            }\n            else if (beatValue <= 4) {\n                return [new Fraction(1, beatValue)];\n            }\n        }\n        else {\n            return groups.map((group) => new Fraction().parse(group));\n        }\n        return [new Fraction(1, 4)];\n    }\n    static applyAndGetBeams(voice, stemDirection, groups) {\n        return Beam.generateBeams(voice.getTickables(), { groups, stemDirection });\n    }\n    static generateBeams(notes, config = {}) {\n        if (!config.groups || !config.groups.length) {\n            config.groups = [new Fraction(2, 8)];\n        }\n        const tickGroups = config.groups.map((group) => {\n            if (!group.multiply) {\n                throw new RuntimeError('InvalidBeamGroups', 'The beam groups must be an array of VexFlow.Fractions');\n            }\n            return group.clone().multiply(Tables.RESOLUTION, 1);\n        });\n        const unprocessedNotes = notes;\n        let currentTickGroup = 0;\n        let noteGroups = [];\n        let currentGroup = [];\n        function getTotalTicks(notes) {\n            return notes.reduce((memo, note) => note.getTicks().clone().add(memo), new Fraction(0, 1));\n        }\n        function nextTickGroup() {\n            if (tickGroups.length - 1 > currentTickGroup) {\n                currentTickGroup += 1;\n            }\n            else {\n                currentTickGroup = 0;\n            }\n        }\n        function createGroups() {\n            let nextGroup = [];\n            let currentGroupTotalTicks = new Fraction(0, 1);\n            unprocessedNotes.forEach((unprocessedNote) => {\n                nextGroup = [];\n                if (unprocessedNote.shouldIgnoreTicks()) {\n                    noteGroups.push(currentGroup);\n                    currentGroup = nextGroup;\n                    return;\n                }\n                currentGroup.push(unprocessedNote);\n                const ticksPerGroup = tickGroups[currentTickGroup].clone();\n                const totalTicks = getTotalTicks(currentGroup).add(currentGroupTotalTicks);\n                const unbeamable = Tables.durationToNumber(unprocessedNote.getDuration()) < 8;\n                if (unbeamable && unprocessedNote.getTuplet()) {\n                    ticksPerGroup.numerator *= 2;\n                }\n                if (totalTicks.greaterThan(ticksPerGroup)) {\n                    if (!unbeamable) {\n                        const note = currentGroup.pop();\n                        if (note)\n                            nextGroup.push(note);\n                    }\n                    noteGroups.push(currentGroup);\n                    do {\n                        currentGroupTotalTicks = totalTicks.subtract(tickGroups[currentTickGroup]);\n                        nextTickGroup();\n                    } while (currentGroupTotalTicks.greaterThanEquals(tickGroups[currentTickGroup]));\n                    currentGroup = nextGroup;\n                }\n                else if (totalTicks.equals(ticksPerGroup)) {\n                    noteGroups.push(currentGroup);\n                    currentGroupTotalTicks = new Fraction(0, 1);\n                    currentGroup = nextGroup;\n                    nextTickGroup();\n                }\n            });\n            if (currentGroup.length > 0) {\n                noteGroups.push(currentGroup);\n            }\n        }\n        function getBeamGroups() {\n            return noteGroups.filter((group) => {\n                if (group.length > 1) {\n                    let beamable = true;\n                    group.forEach((note) => {\n                        if (note.getIntrinsicTicks() >= Tables.durationToTicks('4')) {\n                            beamable = false;\n                        }\n                    });\n                    return beamable;\n                }\n                return false;\n            });\n        }\n        function sanitizeGroups() {\n            const sanitizedGroups = [];\n            noteGroups.forEach((group) => {\n                let tempGroup = [];\n                group.forEach((note, index, group) => {\n                    const isFirstOrLast = index === 0 || index === group.length - 1;\n                    const prevNote = group[index - 1];\n                    const breaksOnEachRest = !config.beamRests && note.isRest();\n                    const breaksOnFirstOrLastRest = config.beamRests && config.beamMiddleOnly && note.isRest() && isFirstOrLast;\n                    let breakOnStemChange = false;\n                    if (config.maintainStemDirections && prevNote && !note.isRest() && !prevNote.isRest()) {\n                        const prevDirection = prevNote.getStemDirection();\n                        const currentDirection = note.getStemDirection();\n                        breakOnStemChange = currentDirection !== prevDirection;\n                    }\n                    const isUnbeamableDuration = parseInt(note.getDuration(), 10) < 8;\n                    const shouldBreak = breaksOnEachRest || breaksOnFirstOrLastRest || breakOnStemChange || isUnbeamableDuration;\n                    if (shouldBreak) {\n                        if (tempGroup.length > 0) {\n                            sanitizedGroups.push(tempGroup);\n                        }\n                        tempGroup = breakOnStemChange ? [note] : [];\n                    }\n                    else {\n                        tempGroup.push(note);\n                    }\n                });\n                if (tempGroup.length > 0) {\n                    sanitizedGroups.push(tempGroup);\n                }\n            });\n            noteGroups = sanitizedGroups;\n        }\n        function formatStems() {\n            noteGroups.forEach((group) => {\n                let stemDirection;\n                if (config.maintainStemDirections) {\n                    const note = findFirstNote(group);\n                    stemDirection = note ? note.getStemDirection() : Stem.UP;\n                }\n                else {\n                    if (config.stemDirection) {\n                        stemDirection = config.stemDirection;\n                    }\n                    else {\n                        stemDirection = calculateStemDirection(group);\n                    }\n                }\n                applyStemDirection(group, stemDirection);\n            });\n        }\n        function findFirstNote(group) {\n            for (let i = 0; i < group.length; i++) {\n                const note = group[i];\n                if (!note.isRest()) {\n                    return note;\n                }\n            }\n            return false;\n        }\n        function applyStemDirection(group, direction) {\n            group.forEach((note) => {\n                note.setStemDirection(direction);\n            });\n        }\n        function getTuplets() {\n            const uniqueTuplets = [];\n            noteGroups.forEach((group) => {\n                let tuplet;\n                group.forEach((note) => {\n                    const noteTuplet = note.getTuplet();\n                    if (noteTuplet && tuplet !== noteTuplet) {\n                        tuplet = noteTuplet;\n                        uniqueTuplets.push(tuplet);\n                    }\n                });\n            });\n            return uniqueTuplets;\n        }\n        createGroups();\n        sanitizeGroups();\n        formatStems();\n        const beamedNoteGroups = getBeamGroups();\n        const allTuplets = getTuplets();\n        const beams = [];\n        beamedNoteGroups.forEach((group) => {\n            const beam = new Beam(group);\n            if (config.showStemlets) {\n                beam.renderOptions.showStemlets = true;\n            }\n            if (config.secondaryBreaks) {\n                beam.renderOptions.secondaryBreakTicks = Tables.durationToTicks(config.secondaryBreaks);\n            }\n            if (config.flatBeams === true) {\n                beam.renderOptions.flatBeams = true;\n                beam.renderOptions.flatBeamOffset = config.flatBeamOffset;\n            }\n            beams.push(beam);\n        });\n        allTuplets.forEach((tuplet) => {\n            const direction = tuplet.notes[0].stemDirection === Stem.DOWN ? -1 : 1;\n            tuplet.setTupletLocation(direction);\n            let bracketed = false;\n            for (let i = 0; i < tuplet.notes.length; i++) {\n                const note = tuplet.notes[i];\n                if (!note.hasBeam()) {\n                    bracketed = true;\n                    break;\n                }\n            }\n            tuplet.setBracketed(bracketed);\n        });\n        return beams;\n    }\n    constructor(notes, autoStem = false) {\n        super();\n        this.slope = 0;\n        this.yShift = 0;\n        this.forcedPartialDirections = {};\n        if (!notes || notes.length === 0) {\n            throw new RuntimeError('BadArguments', 'No notes provided for beam.');\n        }\n        if (notes.length === 1) {\n            throw new RuntimeError('BadArguments', 'Too few notes for beam.');\n        }\n        this._ticks = notes[0].getIntrinsicTicks();\n        if (this._ticks >= Tables.durationToTicks('4')) {\n            throw new RuntimeError('BadArguments', 'Beams can only be applied to notes shorter than a quarter note.');\n        }\n        let i;\n        let note;\n        this._stemDirection = notes[0].getStemDirection();\n        let stemDirection = this._stemDirection;\n        if (autoStem && isStaveNote(notes[0])) {\n            stemDirection = calculateStemDirection(notes);\n        }\n        else if (autoStem && isTabNote(notes[0])) {\n            const stemWeight = notes.reduce((memo, note) => memo + note.getStemDirection(), 0);\n            stemDirection = stemWeight > -1 ? Stem.UP : Stem.DOWN;\n        }\n        for (i = 0; i < notes.length; ++i) {\n            note = notes[i];\n            if (autoStem) {\n                note.setStemDirection(stemDirection);\n                this._stemDirection = stemDirection;\n            }\n            note.setBeam(this);\n        }\n        this.postFormatted = false;\n        this.notes = notes;\n        this._beamCount = this.getBeamCount();\n        this.breakOnIndexes = [];\n        this.renderOptions = {\n            beamWidth: 5,\n            maxSlope: 0.25,\n            minSlope: -0.25,\n            slopeIterations: 20,\n            slopeCost: 100,\n            showStemlets: false,\n            stemletExtension: 7,\n            partialBeamLength: 10,\n            flatBeams: false,\n            minFlatBeamOffset: 15,\n        };\n    }\n    getNotes() {\n        return this.notes;\n    }\n    getBeamCount() {\n        const beamCounts = this.notes.map((note) => note.getGlyphProps().beamCount);\n        const maxBeamCount = beamCounts.reduce((max, beamCount) => (beamCount > max ? beamCount : max));\n        return maxBeamCount;\n    }\n    breakSecondaryAt(indexes) {\n        this.breakOnIndexes = indexes;\n        return this;\n    }\n    setPartialBeamSideAt(noteIndex, side) {\n        this.forcedPartialDirections[noteIndex] = side;\n        return this;\n    }\n    unsetPartialBeamSideAt(noteIndex) {\n        delete this.forcedPartialDirections[noteIndex];\n        return this;\n    }\n    getSlopeY(x, firstX, firstY, slope) {\n        return firstY + (x - firstX) * slope;\n    }\n    calculateSlope() {\n        const { notes, renderOptions: { maxSlope, minSlope, slopeIterations, slopeCost }, } = this;\n        const stemDirection = this._stemDirection;\n        const firstNote = notes[0];\n        const initialSlope = getStemSlope(firstNote, notes[notes.length - 1]);\n        const increment = (maxSlope - minSlope) / slopeIterations;\n        let minCost = Number.MAX_VALUE;\n        let bestSlope = 0;\n        let yShift = 0;\n        for (let slope = minSlope; slope <= maxSlope; slope += increment) {\n            let totalStemExtension = 0;\n            let yShiftTemp = 0;\n            for (let i = 1; i < notes.length; ++i) {\n                const note = notes[i];\n                if (note.hasStem() || note.isRest()) {\n                    const adjustedStemTipY = this.getSlopeY(note.getStemX(), firstNote.getStemX(), firstNote.getStemExtents().topY, slope) + yShiftTemp;\n                    const stemTipY = note.getStemExtents().topY;\n                    if (stemTipY * stemDirection < adjustedStemTipY * stemDirection) {\n                        const diff = Math.abs(stemTipY - adjustedStemTipY);\n                        yShiftTemp += diff * -stemDirection;\n                        totalStemExtension += diff * i;\n                    }\n                    else {\n                        totalStemExtension += (stemTipY - adjustedStemTipY) * stemDirection;\n                    }\n                }\n            }\n            const idealSlope = initialSlope / 2;\n            const distanceFromIdeal = Math.abs(idealSlope - slope);\n            const cost = slopeCost * distanceFromIdeal + Math.abs(totalStemExtension);\n            if (cost < minCost) {\n                minCost = cost;\n                bestSlope = slope;\n                yShift = yShiftTemp;\n            }\n        }\n        this.slope = bestSlope;\n        this.yShift = yShift;\n    }\n    calculateFlatSlope() {\n        const { notes, renderOptions: { beamWidth, minFlatBeamOffset, flatBeamOffset }, } = this;\n        const stemDirection = this._stemDirection;\n        let total = 0;\n        let extremeY = 0;\n        let extremeBeamCount = 0;\n        let currentExtreme = 0;\n        for (let i = 0; i < notes.length; i++) {\n            const note = notes[i];\n            const stemTipY = note.getStemExtents().topY;\n            total += stemTipY;\n            if (stemDirection === Stem.DOWN && currentExtreme < stemTipY) {\n                currentExtreme = stemTipY;\n                extremeY = Math.max(...note.getYs());\n                extremeBeamCount = note.getBeamCount();\n            }\n            else if (stemDirection === Stem.UP && (currentExtreme === 0 || currentExtreme > stemTipY)) {\n                currentExtreme = stemTipY;\n                extremeY = Math.min(...note.getYs());\n                extremeBeamCount = note.getBeamCount();\n            }\n        }\n        let offset = total / notes.length;\n        const extremeTest = minFlatBeamOffset + extremeBeamCount * beamWidth * 1.5;\n        const newOffset = extremeY + extremeTest * -stemDirection;\n        if (stemDirection === Stem.DOWN && offset < newOffset) {\n            offset = extremeY + extremeTest;\n        }\n        else if (stemDirection === Stem.UP && offset > newOffset) {\n            offset = extremeY - extremeTest;\n        }\n        if (!flatBeamOffset) {\n            this.renderOptions.flatBeamOffset = offset;\n        }\n        else if (stemDirection === Stem.DOWN && offset > flatBeamOffset) {\n            this.renderOptions.flatBeamOffset = offset;\n        }\n        else if (stemDirection === Stem.UP && offset < flatBeamOffset) {\n            this.renderOptions.flatBeamOffset = offset;\n        }\n        this.slope = 0;\n        this.yShift = 0;\n    }\n    getBeamYToDraw() {\n        const firstNote = this.notes[0];\n        const firstStemTipY = firstNote.getStemExtents().topY;\n        let beamY = firstStemTipY;\n        if (this.renderOptions.flatBeams && this.renderOptions.flatBeamOffset) {\n            beamY = this.renderOptions.flatBeamOffset;\n        }\n        return beamY;\n    }\n    applyStemExtensions() {\n        const { notes, slope, renderOptions: { showStemlets, stemletExtension, beamWidth }, } = this;\n        const yShift = this.yShift;\n        const beamCount = this._beamCount;\n        const firstNote = notes[0];\n        const firstStemTipY = this.getBeamYToDraw();\n        const firstStemX = firstNote.getStemX();\n        for (let i = 0; i < notes.length; ++i) {\n            const note = notes[i];\n            const stem = note.getStem();\n            if (stem) {\n                const stemX = note.getStemX();\n                const { topY: stemTipY } = note.getStemExtents();\n                const beamedStemTipY = this.getSlopeY(stemX, firstStemX, firstStemTipY, slope) + yShift;\n                const preBeamExtension = stem.getExtension();\n                const beamExtension = note.getStemDirection() === Stem.UP ? stemTipY - beamedStemTipY : beamedStemTipY - stemTipY;\n                let crossStemExtension = 0;\n                if (note.getStemDirection() !== this._stemDirection) {\n                    const beamCount = note.getGlyphProps().beamCount;\n                    crossStemExtension = (1 + (beamCount - 1) * 1.5) * this.renderOptions.beamWidth;\n                }\n                stem.setExtension(preBeamExtension + beamExtension + crossStemExtension);\n                stem.adjustHeightForBeam();\n                if (note.isRest() && showStemlets) {\n                    const totalBeamWidth = (beamCount - 1) * beamWidth * 1.5 + beamWidth;\n                    stem.setVisibility(true).setStemlet(true, totalBeamWidth + stemletExtension);\n                }\n            }\n        }\n    }\n    lookupBeamDirection(duration, prevTick, tick, nextTick, noteIndex) {\n        if (duration === '4') {\n            return BEAM_LEFT;\n        }\n        const forcedBeamDirection = this.forcedPartialDirections[noteIndex];\n        if (forcedBeamDirection)\n            return forcedBeamDirection;\n        const lookupDuration = `${Tables.durationToNumber(duration) / 2}`;\n        const prevNoteGetsBeam = prevTick < Tables.durationToTicks(lookupDuration);\n        const nextNoteGetsBeam = nextTick < Tables.durationToTicks(lookupDuration);\n        const noteGetsBeam = tick < Tables.durationToTicks(lookupDuration);\n        if (prevNoteGetsBeam && nextNoteGetsBeam && noteGetsBeam) {\n            return BEAM_BOTH;\n        }\n        else if (prevNoteGetsBeam && !nextNoteGetsBeam && noteGetsBeam) {\n            return BEAM_LEFT;\n        }\n        else if (!prevNoteGetsBeam && nextNoteGetsBeam && noteGetsBeam) {\n            return BEAM_RIGHT;\n        }\n        return this.lookupBeamDirection(lookupDuration, prevTick, tick, nextTick, noteIndex);\n    }\n    getBeamLines(duration) {\n        const tickOfDuration = Tables.durationToTicks(duration);\n        let beamStarted = false;\n        const beamLines = [];\n        let currentBeam = undefined;\n        const partialBeamLength = this.renderOptions.partialBeamLength;\n        let previousShouldBreak = false;\n        let tickTally = 0;\n        for (let i = 0; i < this.notes.length; ++i) {\n            const note = this.notes[i];\n            const ticks = note.getTicks().value();\n            tickTally += ticks;\n            let shouldBreak = false;\n            if (parseInt(duration, 10) >= 8) {\n                shouldBreak = this.breakOnIndexes.indexOf(i) !== -1;\n                if (this.renderOptions.secondaryBreakTicks && tickTally >= this.renderOptions.secondaryBreakTicks) {\n                    tickTally = 0;\n                    shouldBreak = true;\n                }\n            }\n            const noteGetsBeam = note.getIntrinsicTicks() < tickOfDuration;\n            const stemX = note.getStemX() - Stem.WIDTH / 2;\n            const prevNote = this.notes[i - 1];\n            const nextNote = this.notes[i + 1];\n            const nextNoteGetsBeam = nextNote && nextNote.getIntrinsicTicks() < tickOfDuration;\n            const prevNoteGetsBeam = prevNote && prevNote.getIntrinsicTicks() < tickOfDuration;\n            const beamAlone = prevNote && nextNote && noteGetsBeam && !prevNoteGetsBeam && !nextNoteGetsBeam;\n            if (noteGetsBeam) {\n                if (beamStarted) {\n                    currentBeam = beamLines[beamLines.length - 1];\n                    currentBeam.end = stemX;\n                    if (shouldBreak) {\n                        beamStarted = false;\n                        if (nextNote && !nextNoteGetsBeam && currentBeam.end === undefined) {\n                            currentBeam.end = currentBeam.start - partialBeamLength;\n                        }\n                    }\n                }\n                else {\n                    currentBeam = { start: stemX, end: undefined };\n                    beamStarted = true;\n                    if (beamAlone) {\n                        const prevTick = prevNote.getIntrinsicTicks();\n                        const nextTick = nextNote.getIntrinsicTicks();\n                        const tick = note.getIntrinsicTicks();\n                        const beamDirection = this.lookupBeamDirection(duration, prevTick, tick, nextTick, i);\n                        if ([BEAM_LEFT, BEAM_BOTH].includes(beamDirection)) {\n                            currentBeam.end = currentBeam.start - partialBeamLength;\n                        }\n                        else {\n                            currentBeam.end = currentBeam.start + partialBeamLength;\n                        }\n                    }\n                    else if (!nextNoteGetsBeam) {\n                        if ((previousShouldBreak || i === 0) && nextNote) {\n                            currentBeam.end = currentBeam.start + partialBeamLength;\n                        }\n                        else {\n                            currentBeam.end = currentBeam.start - partialBeamLength;\n                        }\n                    }\n                    else if (shouldBreak) {\n                        currentBeam.end = currentBeam.start - partialBeamLength;\n                        beamStarted = false;\n                    }\n                    beamLines.push(currentBeam);\n                }\n            }\n            else {\n                beamStarted = false;\n            }\n            previousShouldBreak = shouldBreak;\n        }\n        const lastBeam = beamLines[beamLines.length - 1];\n        if (lastBeam && lastBeam.end === undefined) {\n            lastBeam.end = lastBeam.start - partialBeamLength;\n        }\n        return beamLines;\n    }\n    drawStems(ctx) {\n        this.notes.forEach((note) => {\n            const stem = note.getStem();\n            if (stem) {\n                const stemX = note.getStemX();\n                stem.setNoteHeadXBounds(stemX, stemX);\n                stem.setContext(ctx).drawWithStyle();\n            }\n        }, this);\n    }\n    drawBeamLines(ctx) {\n        const validBeamDurations = ['4', '8', '16', '32', '64'];\n        const firstNote = this.notes[0];\n        let beamY = this.getBeamYToDraw();\n        const firstStemX = firstNote.getStemX();\n        const beamThickness = this.renderOptions.beamWidth * this._stemDirection;\n        for (let i = 0; i < validBeamDurations.length; ++i) {\n            const duration = validBeamDurations[i];\n            const beamLines = this.getBeamLines(duration);\n            for (let j = 0; j < beamLines.length; ++j) {\n                const beamLine = beamLines[j];\n                const startBeamX = beamLine.start;\n                const startBeamY = this.getSlopeY(startBeamX, firstStemX, beamY, this.slope);\n                const lastBeamX = beamLine.end;\n                if (lastBeamX) {\n                    const lastBeamY = this.getSlopeY(lastBeamX, firstStemX, beamY, this.slope);\n                    ctx.beginPath();\n                    ctx.moveTo(startBeamX, startBeamY);\n                    ctx.lineTo(startBeamX, startBeamY + beamThickness);\n                    ctx.lineTo(lastBeamX + 1, lastBeamY + beamThickness);\n                    ctx.lineTo(lastBeamX + 1, lastBeamY);\n                    ctx.closePath();\n                    ctx.fill();\n                }\n                else {\n                    throw new RuntimeError('NoLastBeamX', 'lastBeamX undefined.');\n                }\n            }\n            beamY += beamThickness * 1.5;\n        }\n    }\n    preFormat() {\n        return this;\n    }\n    postFormat() {\n        if (this.postFormatted)\n            return;\n        if (isTabNote(this.notes[0]) || this.renderOptions.flatBeams) {\n            this.calculateFlatSlope();\n        }\n        else {\n            this.calculateSlope();\n        }\n        this.applyStemExtensions();\n        this.postFormatted = true;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        if (this.unbeamable)\n            return;\n        if (!this.postFormatted) {\n            this.postFormat();\n        }\n        ctx.openGroup('beam', this.getAttribute('id'));\n        this.drawStems(ctx);\n        this.drawBeamLines(ctx);\n        ctx.closeGroup();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AACvD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACnC,IAAIC,OAAO,GAAG,CAAC;EACfD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAK;IACpB,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACfD,IAAI,CAACC,QAAQ,CAACF,OAAO,CAAEG,OAAO,IAAK;QAC/BJ,OAAO,IAAII,OAAO,CAACC,IAAI,GAAG,CAAC;MAC/B,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,IAAIL,OAAO,IAAI,CAAC,EAAE;IACd,OAAOP,IAAI,CAACa,IAAI;EACpB;EACA,OAAOb,IAAI,CAACc,EAAE;AAClB;AACA,SAASC,YAAYA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACvC,MAAMC,aAAa,GAAGF,SAAS,CAACG,cAAc,CAAC,CAAC,CAACC,IAAI;EACrD,MAAMC,UAAU,GAAGL,SAAS,CAACM,QAAQ,CAAC,CAAC;EACvC,MAAMC,YAAY,GAAGN,QAAQ,CAACE,cAAc,CAAC,CAAC,CAACC,IAAI;EACnD,MAAMI,SAAS,GAAGP,QAAQ,CAACK,QAAQ,CAAC,CAAC;EACrC,OAAO,CAACC,YAAY,GAAGL,aAAa,KAAKM,SAAS,GAAGH,UAAU,CAAC;AACpE;AACA,OAAO,MAAMI,SAAS,GAAG,GAAG;AAC5B,OAAO,MAAMC,UAAU,GAAG,GAAG;AAC7B,OAAO,MAAMC,SAAS,GAAG,GAAG;AAC5B,OAAO,MAAMC,IAAI,SAAS9B,OAAO,CAAC;EAC9B,WAAW+B,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,OAAOC,oBAAoBA,CAACC,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,GAAG,EAAE;MAC7BA,OAAO,GAAG,KAAK;IACnB;IACA,MAAMC,QAAQ,GAAG;MACb,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,KAAK,EAAE,CAAC,KAAK,CAAC;MACd,MAAM,EAAE,CAAC,MAAM,CAAC;MAChB,MAAM,EAAE,CAAC,MAAM,CAAC;MAChB,MAAM,EAAE,CAAC,MAAM,CAAC;MAChB,MAAM,EAAE,CAAC,MAAM;IACnB,CAAC;IACD,MAAMC,MAAM,GAAGD,QAAQ,CAACD,OAAO,CAAC;IAChC,IAAIE,MAAM,KAAKC,SAAS,EAAE;MACtB,MAAMC,SAAS,GAAGC,QAAQ,CAACL,OAAO,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACrD,MAAMC,SAAS,GAAGF,QAAQ,CAACL,OAAO,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACrD,MAAME,WAAW,GAAGJ,SAAS,GAAG,CAAC,KAAK,CAAC;MACvC,IAAII,WAAW,EAAE;QACb,OAAO,CAAC,IAAI1C,QAAQ,CAAC,CAAC,EAAEyC,SAAS,CAAC,CAAC;MACvC,CAAC,MACI,IAAIA,SAAS,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,IAAIzC,QAAQ,CAAC,CAAC,EAAEyC,SAAS,CAAC,CAAC;MACvC,CAAC,MACI,IAAIA,SAAS,IAAI,CAAC,EAAE;QACrB,OAAO,CAAC,IAAIzC,QAAQ,CAAC,CAAC,EAAEyC,SAAS,CAAC,CAAC;MACvC;IACJ,CAAC,MACI;MACD,OAAOL,MAAM,CAACO,GAAG,CAAEC,KAAK,IAAK,IAAI5C,QAAQ,CAAC,CAAC,CAAC6C,KAAK,CAACD,KAAK,CAAC,CAAC;IAC7D;IACA,OAAO,CAAC,IAAI5C,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/B;EACA,OAAO8C,gBAAgBA,CAACC,KAAK,EAAEC,aAAa,EAAEZ,MAAM,EAAE;IAClD,OAAOP,IAAI,CAACoB,aAAa,CAACF,KAAK,CAACG,YAAY,CAAC,CAAC,EAAE;MAAEd,MAAM;MAAEY;IAAc,CAAC,CAAC;EAC9E;EACA,OAAOC,aAAaA,CAAC1C,KAAK,EAAE4C,MAAM,GAAG,CAAC,CAAC,EAAE;IACrC,IAAI,CAACA,MAAM,CAACf,MAAM,IAAI,CAACe,MAAM,CAACf,MAAM,CAACgB,MAAM,EAAE;MACzCD,MAAM,CAACf,MAAM,GAAG,CAAC,IAAIpC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC;IACA,MAAMqD,UAAU,GAAGF,MAAM,CAACf,MAAM,CAACO,GAAG,CAAEC,KAAK,IAAK;MAC5C,IAAI,CAACA,KAAK,CAACU,QAAQ,EAAE;QACjB,MAAM,IAAIjD,YAAY,CAAC,mBAAmB,EAAE,uDAAuD,CAAC;MACxG;MACA,OAAOuC,KAAK,CAACW,KAAK,CAAC,CAAC,CAACD,QAAQ,CAACpD,MAAM,CAACsD,UAAU,EAAE,CAAC,CAAC;IACvD,CAAC,CAAC;IACF,MAAMC,gBAAgB,GAAGlD,KAAK;IAC9B,IAAImD,gBAAgB,GAAG,CAAC;IACxB,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIC,YAAY,GAAG,EAAE;IACrB,SAASC,aAAaA,CAACtD,KAAK,EAAE;MAC1B,OAAOA,KAAK,CAACuD,MAAM,CAAC,CAACC,IAAI,EAAErD,IAAI,KAAKA,IAAI,CAACsD,QAAQ,CAAC,CAAC,CAACT,KAAK,CAAC,CAAC,CAACU,GAAG,CAACF,IAAI,CAAC,EAAE,IAAI/D,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9F;IACA,SAASkE,aAAaA,CAAA,EAAG;MACrB,IAAIb,UAAU,CAACD,MAAM,GAAG,CAAC,GAAGM,gBAAgB,EAAE;QAC1CA,gBAAgB,IAAI,CAAC;MACzB,CAAC,MACI;QACDA,gBAAgB,GAAG,CAAC;MACxB;IACJ;IACA,SAASS,YAAYA,CAAA,EAAG;MACpB,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIC,sBAAsB,GAAG,IAAIrE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/CyD,gBAAgB,CAAChD,OAAO,CAAE6D,eAAe,IAAK;QAC1CF,SAAS,GAAG,EAAE;QACd,IAAIE,eAAe,CAACC,iBAAiB,CAAC,CAAC,EAAE;UACrCZ,UAAU,CAACa,IAAI,CAACZ,YAAY,CAAC;UAC7BA,YAAY,GAAGQ,SAAS;UACxB;QACJ;QACAR,YAAY,CAACY,IAAI,CAACF,eAAe,CAAC;QAClC,MAAMG,aAAa,GAAGpB,UAAU,CAACK,gBAAgB,CAAC,CAACH,KAAK,CAAC,CAAC;QAC1D,MAAMmB,UAAU,GAAGb,aAAa,CAACD,YAAY,CAAC,CAACK,GAAG,CAACI,sBAAsB,CAAC;QAC1E,MAAMM,UAAU,GAAGzE,MAAM,CAAC0E,gBAAgB,CAACN,eAAe,CAACO,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;QAC7E,IAAIF,UAAU,IAAIL,eAAe,CAACQ,SAAS,CAAC,CAAC,EAAE;UAC3CL,aAAa,CAACM,SAAS,IAAI,CAAC;QAChC;QACA,IAAIL,UAAU,CAACM,WAAW,CAACP,aAAa,CAAC,EAAE;UACvC,IAAI,CAACE,UAAU,EAAE;YACb,MAAMjE,IAAI,GAAGkD,YAAY,CAACqB,GAAG,CAAC,CAAC;YAC/B,IAAIvE,IAAI,EACJ0D,SAAS,CAACI,IAAI,CAAC9D,IAAI,CAAC;UAC5B;UACAiD,UAAU,CAACa,IAAI,CAACZ,YAAY,CAAC;UAC7B,GAAG;YACCS,sBAAsB,GAAGK,UAAU,CAACQ,QAAQ,CAAC7B,UAAU,CAACK,gBAAgB,CAAC,CAAC;YAC1EQ,aAAa,CAAC,CAAC;UACnB,CAAC,QAAQG,sBAAsB,CAACc,iBAAiB,CAAC9B,UAAU,CAACK,gBAAgB,CAAC,CAAC;UAC/EE,YAAY,GAAGQ,SAAS;QAC5B,CAAC,MACI,IAAIM,UAAU,CAACU,MAAM,CAACX,aAAa,CAAC,EAAE;UACvCd,UAAU,CAACa,IAAI,CAACZ,YAAY,CAAC;UAC7BS,sBAAsB,GAAG,IAAIrE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3C4D,YAAY,GAAGQ,SAAS;UACxBF,aAAa,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;MACF,IAAIN,YAAY,CAACR,MAAM,GAAG,CAAC,EAAE;QACzBO,UAAU,CAACa,IAAI,CAACZ,YAAY,CAAC;MACjC;IACJ;IACA,SAASyB,aAAaA,CAAA,EAAG;MACrB,OAAO1B,UAAU,CAAC2B,MAAM,CAAE1C,KAAK,IAAK;QAChC,IAAIA,KAAK,CAACQ,MAAM,GAAG,CAAC,EAAE;UAClB,IAAImC,QAAQ,GAAG,IAAI;UACnB3C,KAAK,CAACnC,OAAO,CAAEC,IAAI,IAAK;YACpB,IAAIA,IAAI,CAAC8E,iBAAiB,CAAC,CAAC,IAAItF,MAAM,CAACuF,eAAe,CAAC,GAAG,CAAC,EAAE;cACzDF,QAAQ,GAAG,KAAK;YACpB;UACJ,CAAC,CAAC;UACF,OAAOA,QAAQ;QACnB;QACA,OAAO,KAAK;MAChB,CAAC,CAAC;IACN;IACA,SAASG,cAAcA,CAAA,EAAG;MACtB,MAAMC,eAAe,GAAG,EAAE;MAC1BhC,UAAU,CAAClD,OAAO,CAAEmC,KAAK,IAAK;QAC1B,IAAIgD,SAAS,GAAG,EAAE;QAClBhD,KAAK,CAACnC,OAAO,CAAC,CAACC,IAAI,EAAEmF,KAAK,EAAEjD,KAAK,KAAK;UAClC,MAAMkD,aAAa,GAAGD,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAKjD,KAAK,CAACQ,MAAM,GAAG,CAAC;UAC/D,MAAM2C,QAAQ,GAAGnD,KAAK,CAACiD,KAAK,GAAG,CAAC,CAAC;UACjC,MAAMG,gBAAgB,GAAG,CAAC7C,MAAM,CAAC8C,SAAS,IAAIvF,IAAI,CAACwF,MAAM,CAAC,CAAC;UAC3D,MAAMC,uBAAuB,GAAGhD,MAAM,CAAC8C,SAAS,IAAI9C,MAAM,CAACiD,cAAc,IAAI1F,IAAI,CAACwF,MAAM,CAAC,CAAC,IAAIJ,aAAa;UAC3G,IAAIO,iBAAiB,GAAG,KAAK;UAC7B,IAAIlD,MAAM,CAACmD,sBAAsB,IAAIP,QAAQ,IAAI,CAACrF,IAAI,CAACwF,MAAM,CAAC,CAAC,IAAI,CAACH,QAAQ,CAACG,MAAM,CAAC,CAAC,EAAE;YACnF,MAAMK,aAAa,GAAGR,QAAQ,CAAChE,gBAAgB,CAAC,CAAC;YACjD,MAAMyE,gBAAgB,GAAG9F,IAAI,CAACqB,gBAAgB,CAAC,CAAC;YAChDsE,iBAAiB,GAAGG,gBAAgB,KAAKD,aAAa;UAC1D;UACA,MAAME,oBAAoB,GAAGlE,QAAQ,CAAC7B,IAAI,CAACmE,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UACjE,MAAM6B,WAAW,GAAGV,gBAAgB,IAAIG,uBAAuB,IAAIE,iBAAiB,IAAII,oBAAoB;UAC5G,IAAIC,WAAW,EAAE;YACb,IAAId,SAAS,CAACxC,MAAM,GAAG,CAAC,EAAE;cACtBuC,eAAe,CAACnB,IAAI,CAACoB,SAAS,CAAC;YACnC;YACAA,SAAS,GAAGS,iBAAiB,GAAG,CAAC3F,IAAI,CAAC,GAAG,EAAE;UAC/C,CAAC,MACI;YACDkF,SAAS,CAACpB,IAAI,CAAC9D,IAAI,CAAC;UACxB;QACJ,CAAC,CAAC;QACF,IAAIkF,SAAS,CAACxC,MAAM,GAAG,CAAC,EAAE;UACtBuC,eAAe,CAACnB,IAAI,CAACoB,SAAS,CAAC;QACnC;MACJ,CAAC,CAAC;MACFjC,UAAU,GAAGgC,eAAe;IAChC;IACA,SAASgB,WAAWA,CAAA,EAAG;MACnBhD,UAAU,CAAClD,OAAO,CAAEmC,KAAK,IAAK;QAC1B,IAAII,aAAa;QACjB,IAAIG,MAAM,CAACmD,sBAAsB,EAAE;UAC/B,MAAM5F,IAAI,GAAGkG,aAAa,CAAChE,KAAK,CAAC;UACjCI,aAAa,GAAGtC,IAAI,GAAGA,IAAI,CAACqB,gBAAgB,CAAC,CAAC,GAAG9B,IAAI,CAACc,EAAE;QAC5D,CAAC,MACI;UACD,IAAIoC,MAAM,CAACH,aAAa,EAAE;YACtBA,aAAa,GAAGG,MAAM,CAACH,aAAa;UACxC,CAAC,MACI;YACDA,aAAa,GAAG1C,sBAAsB,CAACsC,KAAK,CAAC;UACjD;QACJ;QACAiE,kBAAkB,CAACjE,KAAK,EAAEI,aAAa,CAAC;MAC5C,CAAC,CAAC;IACN;IACA,SAAS4D,aAAaA,CAAChE,KAAK,EAAE;MAC1B,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlE,KAAK,CAACQ,MAAM,EAAE0D,CAAC,EAAE,EAAE;QACnC,MAAMpG,IAAI,GAAGkC,KAAK,CAACkE,CAAC,CAAC;QACrB,IAAI,CAACpG,IAAI,CAACwF,MAAM,CAAC,CAAC,EAAE;UAChB,OAAOxF,IAAI;QACf;MACJ;MACA,OAAO,KAAK;IAChB;IACA,SAASmG,kBAAkBA,CAACjE,KAAK,EAAEmE,SAAS,EAAE;MAC1CnE,KAAK,CAACnC,OAAO,CAAEC,IAAI,IAAK;QACpBA,IAAI,CAACsG,gBAAgB,CAACD,SAAS,CAAC;MACpC,CAAC,CAAC;IACN;IACA,SAASE,UAAUA,CAAA,EAAG;MAClB,MAAMC,aAAa,GAAG,EAAE;MACxBvD,UAAU,CAAClD,OAAO,CAAEmC,KAAK,IAAK;QAC1B,IAAIuE,MAAM;QACVvE,KAAK,CAACnC,OAAO,CAAEC,IAAI,IAAK;UACpB,MAAM0G,UAAU,GAAG1G,IAAI,CAACoE,SAAS,CAAC,CAAC;UACnC,IAAIsC,UAAU,IAAID,MAAM,KAAKC,UAAU,EAAE;YACrCD,MAAM,GAAGC,UAAU;YACnBF,aAAa,CAAC1C,IAAI,CAAC2C,MAAM,CAAC;UAC9B;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAOD,aAAa;IACxB;IACA/C,YAAY,CAAC,CAAC;IACduB,cAAc,CAAC,CAAC;IAChBiB,WAAW,CAAC,CAAC;IACb,MAAMU,gBAAgB,GAAGhC,aAAa,CAAC,CAAC;IACxC,MAAMiC,UAAU,GAAGL,UAAU,CAAC,CAAC;IAC/B,MAAMM,KAAK,GAAG,EAAE;IAChBF,gBAAgB,CAAC5G,OAAO,CAAEmC,KAAK,IAAK;MAChC,MAAM4E,IAAI,GAAG,IAAI3F,IAAI,CAACe,KAAK,CAAC;MAC5B,IAAIO,MAAM,CAACsE,YAAY,EAAE;QACrBD,IAAI,CAACE,aAAa,CAACD,YAAY,GAAG,IAAI;MAC1C;MACA,IAAItE,MAAM,CAACwE,eAAe,EAAE;QACxBH,IAAI,CAACE,aAAa,CAACE,mBAAmB,GAAG1H,MAAM,CAACuF,eAAe,CAACtC,MAAM,CAACwE,eAAe,CAAC;MAC3F;MACA,IAAIxE,MAAM,CAAC0E,SAAS,KAAK,IAAI,EAAE;QAC3BL,IAAI,CAACE,aAAa,CAACG,SAAS,GAAG,IAAI;QACnCL,IAAI,CAACE,aAAa,CAACI,cAAc,GAAG3E,MAAM,CAAC2E,cAAc;MAC7D;MACAP,KAAK,CAAC/C,IAAI,CAACgD,IAAI,CAAC;IACpB,CAAC,CAAC;IACFF,UAAU,CAAC7G,OAAO,CAAE0G,MAAM,IAAK;MAC3B,MAAMJ,SAAS,GAAGI,MAAM,CAAC5G,KAAK,CAAC,CAAC,CAAC,CAACyC,aAAa,KAAK/C,IAAI,CAACa,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;MACtEqG,MAAM,CAACY,iBAAiB,CAAChB,SAAS,CAAC;MACnC,IAAIiB,SAAS,GAAG,KAAK;MACrB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAAC5G,KAAK,CAAC6C,MAAM,EAAE0D,CAAC,EAAE,EAAE;QAC1C,MAAMpG,IAAI,GAAGyG,MAAM,CAAC5G,KAAK,CAACuG,CAAC,CAAC;QAC5B,IAAI,CAACpG,IAAI,CAACuH,OAAO,CAAC,CAAC,EAAE;UACjBD,SAAS,GAAG,IAAI;UAChB;QACJ;MACJ;MACAb,MAAM,CAACe,YAAY,CAACF,SAAS,CAAC;IAClC,CAAC,CAAC;IACF,OAAOT,KAAK;EAChB;EACAY,WAAWA,CAAC5H,KAAK,EAAE6H,QAAQ,GAAG,KAAK,EAAE;IACjC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,IAAI,CAAChI,KAAK,IAAIA,KAAK,CAAC6C,MAAM,KAAK,CAAC,EAAE;MAC9B,MAAM,IAAI/C,YAAY,CAAC,cAAc,EAAE,6BAA6B,CAAC;IACzE;IACA,IAAIE,KAAK,CAAC6C,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAI/C,YAAY,CAAC,cAAc,EAAE,yBAAyB,CAAC;IACrE;IACA,IAAI,CAACmI,MAAM,GAAGjI,KAAK,CAAC,CAAC,CAAC,CAACiF,iBAAiB,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACgD,MAAM,IAAItI,MAAM,CAACuF,eAAe,CAAC,GAAG,CAAC,EAAE;MAC5C,MAAM,IAAIpF,YAAY,CAAC,cAAc,EAAE,iEAAiE,CAAC;IAC7G;IACA,IAAIyG,CAAC;IACL,IAAIpG,IAAI;IACR,IAAI,CAACsB,cAAc,GAAGzB,KAAK,CAAC,CAAC,CAAC,CAACwB,gBAAgB,CAAC,CAAC;IACjD,IAAIiB,aAAa,GAAG,IAAI,CAAChB,cAAc;IACvC,IAAIoG,QAAQ,IAAIjI,WAAW,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACnCyC,aAAa,GAAG1C,sBAAsB,CAACC,KAAK,CAAC;IACjD,CAAC,MACI,IAAI6H,QAAQ,IAAIhI,SAAS,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACtC,MAAMkI,UAAU,GAAGlI,KAAK,CAACuD,MAAM,CAAC,CAACC,IAAI,EAAErD,IAAI,KAAKqD,IAAI,GAAGrD,IAAI,CAACqB,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;MAClFiB,aAAa,GAAGyF,UAAU,GAAG,CAAC,CAAC,GAAGxI,IAAI,CAACc,EAAE,GAAGd,IAAI,CAACa,IAAI;IACzD;IACA,KAAKgG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvG,KAAK,CAAC6C,MAAM,EAAE,EAAE0D,CAAC,EAAE;MAC/BpG,IAAI,GAAGH,KAAK,CAACuG,CAAC,CAAC;MACf,IAAIsB,QAAQ,EAAE;QACV1H,IAAI,CAACsG,gBAAgB,CAAChE,aAAa,CAAC;QACpC,IAAI,CAAChB,cAAc,GAAGgB,aAAa;MACvC;MACAtC,IAAI,CAACgI,OAAO,CAAC,IAAI,CAAC;IACtB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACpI,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqI,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACrC,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACpB,aAAa,GAAG;MACjBqB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CAAC,IAAI;MACfC,eAAe,EAAE,EAAE;MACnBC,SAAS,EAAE,GAAG;MACd1B,YAAY,EAAE,KAAK;MACnB2B,gBAAgB,EAAE,CAAC;MACnBC,iBAAiB,EAAE,EAAE;MACrBxB,SAAS,EAAE,KAAK;MAChByB,iBAAiB,EAAE;IACvB,CAAC;EACL;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChJ,KAAK;EACrB;EACAsI,YAAYA,CAAA,EAAG;IACX,MAAMW,UAAU,GAAG,IAAI,CAACjJ,KAAK,CAACoC,GAAG,CAAEjC,IAAI,IAAKA,IAAI,CAAC+I,aAAa,CAAC,CAAC,CAACC,SAAS,CAAC;IAC3E,MAAMC,YAAY,GAAGH,UAAU,CAAC1F,MAAM,CAAC,CAAC8F,GAAG,EAAEF,SAAS,KAAMA,SAAS,GAAGE,GAAG,GAAGF,SAAS,GAAGE,GAAI,CAAC;IAC/F,OAAOD,YAAY;EACvB;EACAE,gBAAgBA,CAACC,OAAO,EAAE;IACtB,IAAI,CAAChB,cAAc,GAAGgB,OAAO;IAC7B,OAAO,IAAI;EACf;EACAC,oBAAoBA,CAACC,SAAS,EAAEC,IAAI,EAAE;IAClC,IAAI,CAAC1B,uBAAuB,CAACyB,SAAS,CAAC,GAAGC,IAAI;IAC9C,OAAO,IAAI;EACf;EACAC,sBAAsBA,CAACF,SAAS,EAAE;IAC9B,OAAO,IAAI,CAACzB,uBAAuB,CAACyB,SAAS,CAAC;IAC9C,OAAO,IAAI;EACf;EACAG,SAASA,CAACC,CAAC,EAAEC,MAAM,EAAEC,MAAM,EAAEjC,KAAK,EAAE;IAChC,OAAOiC,MAAM,GAAG,CAACF,CAAC,GAAGC,MAAM,IAAIhC,KAAK;EACxC;EACAkC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEhK,KAAK;MAAEmH,aAAa,EAAE;QAAEsB,QAAQ;QAAEC,QAAQ;QAAEC,eAAe;QAAEC;MAAU;IAAG,CAAC,GAAG,IAAI;IAC1F,MAAMnG,aAAa,GAAG,IAAI,CAAChB,cAAc;IACzC,MAAMf,SAAS,GAAGV,KAAK,CAAC,CAAC,CAAC;IAC1B,MAAMiK,YAAY,GAAGxJ,YAAY,CAACC,SAAS,EAAEV,KAAK,CAACA,KAAK,CAAC6C,MAAM,GAAG,CAAC,CAAC,CAAC;IACrE,MAAMqH,SAAS,GAAG,CAACzB,QAAQ,GAAGC,QAAQ,IAAIC,eAAe;IACzD,IAAIwB,OAAO,GAAGC,MAAM,CAACC,SAAS;IAC9B,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIvC,MAAM,GAAG,CAAC;IACd,KAAK,IAAID,KAAK,GAAGY,QAAQ,EAAEZ,KAAK,IAAIW,QAAQ,EAAEX,KAAK,IAAIoC,SAAS,EAAE;MAC9D,IAAIK,kBAAkB,GAAG,CAAC;MAC1B,IAAIC,UAAU,GAAG,CAAC;MAClB,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvG,KAAK,CAAC6C,MAAM,EAAE,EAAE0D,CAAC,EAAE;QACnC,MAAMpG,IAAI,GAAGH,KAAK,CAACuG,CAAC,CAAC;QACrB,IAAIpG,IAAI,CAACsK,OAAO,CAAC,CAAC,IAAItK,IAAI,CAACwF,MAAM,CAAC,CAAC,EAAE;UACjC,MAAM+E,gBAAgB,GAAG,IAAI,CAACd,SAAS,CAACzJ,IAAI,CAACa,QAAQ,CAAC,CAAC,EAAEN,SAAS,CAACM,QAAQ,CAAC,CAAC,EAAEN,SAAS,CAACG,cAAc,CAAC,CAAC,CAACC,IAAI,EAAEgH,KAAK,CAAC,GAAG0C,UAAU;UACnI,MAAMG,QAAQ,GAAGxK,IAAI,CAACU,cAAc,CAAC,CAAC,CAACC,IAAI;UAC3C,IAAI6J,QAAQ,GAAGlI,aAAa,GAAGiI,gBAAgB,GAAGjI,aAAa,EAAE;YAC7D,MAAMmI,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACH,QAAQ,GAAGD,gBAAgB,CAAC;YAClDF,UAAU,IAAII,IAAI,GAAG,CAACnI,aAAa;YACnC8H,kBAAkB,IAAIK,IAAI,GAAGrE,CAAC;UAClC,CAAC,MACI;YACDgE,kBAAkB,IAAI,CAACI,QAAQ,GAAGD,gBAAgB,IAAIjI,aAAa;UACvE;QACJ;MACJ;MACA,MAAMsI,UAAU,GAAGd,YAAY,GAAG,CAAC;MACnC,MAAMe,iBAAiB,GAAGH,IAAI,CAACC,GAAG,CAACC,UAAU,GAAGjD,KAAK,CAAC;MACtD,MAAMmD,IAAI,GAAGrC,SAAS,GAAGoC,iBAAiB,GAAGH,IAAI,CAACC,GAAG,CAACP,kBAAkB,CAAC;MACzE,IAAIU,IAAI,GAAGd,OAAO,EAAE;QAChBA,OAAO,GAAGc,IAAI;QACdX,SAAS,GAAGxC,KAAK;QACjBC,MAAM,GAAGyC,UAAU;MACvB;IACJ;IACA,IAAI,CAAC1C,KAAK,GAAGwC,SAAS;IACtB,IAAI,CAACvC,MAAM,GAAGA,MAAM;EACxB;EACAmD,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAElL,KAAK;MAAEmH,aAAa,EAAE;QAAEqB,SAAS;QAAEO,iBAAiB;QAAExB;MAAe;IAAG,CAAC,GAAG,IAAI;IACxF,MAAM9E,aAAa,GAAG,IAAI,CAAChB,cAAc;IACzC,IAAI0J,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,IAAIC,cAAc,GAAG,CAAC;IACtB,KAAK,IAAI/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvG,KAAK,CAAC6C,MAAM,EAAE0D,CAAC,EAAE,EAAE;MACnC,MAAMpG,IAAI,GAAGH,KAAK,CAACuG,CAAC,CAAC;MACrB,MAAMoE,QAAQ,GAAGxK,IAAI,CAACU,cAAc,CAAC,CAAC,CAACC,IAAI;MAC3CqK,KAAK,IAAIR,QAAQ;MACjB,IAAIlI,aAAa,KAAK/C,IAAI,CAACa,IAAI,IAAI+K,cAAc,GAAGX,QAAQ,EAAE;QAC1DW,cAAc,GAAGX,QAAQ;QACzBS,QAAQ,GAAGP,IAAI,CAACxB,GAAG,CAAC,GAAGlJ,IAAI,CAACoL,KAAK,CAAC,CAAC,CAAC;QACpCF,gBAAgB,GAAGlL,IAAI,CAACmI,YAAY,CAAC,CAAC;MAC1C,CAAC,MACI,IAAI7F,aAAa,KAAK/C,IAAI,CAACc,EAAE,KAAK8K,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGX,QAAQ,CAAC,EAAE;QACvFW,cAAc,GAAGX,QAAQ;QACzBS,QAAQ,GAAGP,IAAI,CAACW,GAAG,CAAC,GAAGrL,IAAI,CAACoL,KAAK,CAAC,CAAC,CAAC;QACpCF,gBAAgB,GAAGlL,IAAI,CAACmI,YAAY,CAAC,CAAC;MAC1C;IACJ;IACA,IAAImD,MAAM,GAAGN,KAAK,GAAGnL,KAAK,CAAC6C,MAAM;IACjC,MAAM6I,WAAW,GAAG3C,iBAAiB,GAAGsC,gBAAgB,GAAG7C,SAAS,GAAG,GAAG;IAC1E,MAAMmD,SAAS,GAAGP,QAAQ,GAAGM,WAAW,GAAG,CAACjJ,aAAa;IACzD,IAAIA,aAAa,KAAK/C,IAAI,CAACa,IAAI,IAAIkL,MAAM,GAAGE,SAAS,EAAE;MACnDF,MAAM,GAAGL,QAAQ,GAAGM,WAAW;IACnC,CAAC,MACI,IAAIjJ,aAAa,KAAK/C,IAAI,CAACc,EAAE,IAAIiL,MAAM,GAAGE,SAAS,EAAE;MACtDF,MAAM,GAAGL,QAAQ,GAAGM,WAAW;IACnC;IACA,IAAI,CAACnE,cAAc,EAAE;MACjB,IAAI,CAACJ,aAAa,CAACI,cAAc,GAAGkE,MAAM;IAC9C,CAAC,MACI,IAAIhJ,aAAa,KAAK/C,IAAI,CAACa,IAAI,IAAIkL,MAAM,GAAGlE,cAAc,EAAE;MAC7D,IAAI,CAACJ,aAAa,CAACI,cAAc,GAAGkE,MAAM;IAC9C,CAAC,MACI,IAAIhJ,aAAa,KAAK/C,IAAI,CAACc,EAAE,IAAIiL,MAAM,GAAGlE,cAAc,EAAE;MAC3D,IAAI,CAACJ,aAAa,CAACI,cAAc,GAAGkE,MAAM;IAC9C;IACA,IAAI,CAAC3D,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACA6D,cAAcA,CAAA,EAAG;IACb,MAAMlL,SAAS,GAAG,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC;IAC/B,MAAMY,aAAa,GAAGF,SAAS,CAACG,cAAc,CAAC,CAAC,CAACC,IAAI;IACrD,IAAI+K,KAAK,GAAGjL,aAAa;IACzB,IAAI,IAAI,CAACuG,aAAa,CAACG,SAAS,IAAI,IAAI,CAACH,aAAa,CAACI,cAAc,EAAE;MACnEsE,KAAK,GAAG,IAAI,CAAC1E,aAAa,CAACI,cAAc;IAC7C;IACA,OAAOsE,KAAK;EAChB;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAE9L,KAAK;MAAE8H,KAAK;MAAEX,aAAa,EAAE;QAAED,YAAY;QAAE2B,gBAAgB;QAAEL;MAAU;IAAG,CAAC,GAAG,IAAI;IAC5F,MAAMT,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMoB,SAAS,GAAG,IAAI,CAACd,UAAU;IACjC,MAAM3H,SAAS,GAAGV,KAAK,CAAC,CAAC,CAAC;IAC1B,MAAMY,aAAa,GAAG,IAAI,CAACgL,cAAc,CAAC,CAAC;IAC3C,MAAM7K,UAAU,GAAGL,SAAS,CAACM,QAAQ,CAAC,CAAC;IACvC,KAAK,IAAIuF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvG,KAAK,CAAC6C,MAAM,EAAE,EAAE0D,CAAC,EAAE;MACnC,MAAMpG,IAAI,GAAGH,KAAK,CAACuG,CAAC,CAAC;MACrB,MAAMwF,IAAI,GAAG5L,IAAI,CAAC6L,OAAO,CAAC,CAAC;MAC3B,IAAID,IAAI,EAAE;QACN,MAAME,KAAK,GAAG9L,IAAI,CAACa,QAAQ,CAAC,CAAC;QAC7B,MAAM;UAAEF,IAAI,EAAE6J;QAAS,CAAC,GAAGxK,IAAI,CAACU,cAAc,CAAC,CAAC;QAChD,MAAMqL,cAAc,GAAG,IAAI,CAACtC,SAAS,CAACqC,KAAK,EAAElL,UAAU,EAAEH,aAAa,EAAEkH,KAAK,CAAC,GAAGC,MAAM;QACvF,MAAMoE,gBAAgB,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC;QAC5C,MAAMC,aAAa,GAAGlM,IAAI,CAACqB,gBAAgB,CAAC,CAAC,KAAK9B,IAAI,CAACc,EAAE,GAAGmK,QAAQ,GAAGuB,cAAc,GAAGA,cAAc,GAAGvB,QAAQ;QACjH,IAAI2B,kBAAkB,GAAG,CAAC;QAC1B,IAAInM,IAAI,CAACqB,gBAAgB,CAAC,CAAC,KAAK,IAAI,CAACC,cAAc,EAAE;UACjD,MAAM0H,SAAS,GAAGhJ,IAAI,CAAC+I,aAAa,CAAC,CAAC,CAACC,SAAS;UAChDmD,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAACnD,SAAS,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,CAAChC,aAAa,CAACqB,SAAS;QACnF;QACAuD,IAAI,CAACQ,YAAY,CAACJ,gBAAgB,GAAGE,aAAa,GAAGC,kBAAkB,CAAC;QACxEP,IAAI,CAACS,mBAAmB,CAAC,CAAC;QAC1B,IAAIrM,IAAI,CAACwF,MAAM,CAAC,CAAC,IAAIuB,YAAY,EAAE;UAC/B,MAAMuF,cAAc,GAAG,CAACtD,SAAS,GAAG,CAAC,IAAIX,SAAS,GAAG,GAAG,GAAGA,SAAS;UACpEuD,IAAI,CAACW,aAAa,CAAC,IAAI,CAAC,CAACC,UAAU,CAAC,IAAI,EAAEF,cAAc,GAAG5D,gBAAgB,CAAC;QAChF;MACJ;IACJ;EACJ;EACA+D,mBAAmBA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEvD,SAAS,EAAE;IAC/D,IAAIoD,QAAQ,KAAK,GAAG,EAAE;MAClB,OAAO1L,SAAS;IACpB;IACA,MAAM8L,mBAAmB,GAAG,IAAI,CAACjF,uBAAuB,CAACyB,SAAS,CAAC;IACnE,IAAIwD,mBAAmB,EACnB,OAAOA,mBAAmB;IAC9B,MAAMC,cAAc,GAAG,GAAGvN,MAAM,CAAC0E,gBAAgB,CAACwI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACjE,MAAMM,gBAAgB,GAAGL,QAAQ,GAAGnN,MAAM,CAACuF,eAAe,CAACgI,cAAc,CAAC;IAC1E,MAAME,gBAAgB,GAAGJ,QAAQ,GAAGrN,MAAM,CAACuF,eAAe,CAACgI,cAAc,CAAC;IAC1E,MAAMG,YAAY,GAAGN,IAAI,GAAGpN,MAAM,CAACuF,eAAe,CAACgI,cAAc,CAAC;IAClE,IAAIC,gBAAgB,IAAIC,gBAAgB,IAAIC,YAAY,EAAE;MACtD,OAAOhM,SAAS;IACpB,CAAC,MACI,IAAI8L,gBAAgB,IAAI,CAACC,gBAAgB,IAAIC,YAAY,EAAE;MAC5D,OAAOlM,SAAS;IACpB,CAAC,MACI,IAAI,CAACgM,gBAAgB,IAAIC,gBAAgB,IAAIC,YAAY,EAAE;MAC5D,OAAOjM,UAAU;IACrB;IACA,OAAO,IAAI,CAACwL,mBAAmB,CAACM,cAAc,EAAEJ,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEvD,SAAS,CAAC;EACxF;EACA6D,YAAYA,CAACT,QAAQ,EAAE;IACnB,MAAMU,cAAc,GAAG5N,MAAM,CAACuF,eAAe,CAAC2H,QAAQ,CAAC;IACvD,IAAIW,WAAW,GAAG,KAAK;IACvB,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAIC,WAAW,GAAG5L,SAAS;IAC3B,MAAMgH,iBAAiB,GAAG,IAAI,CAAC3B,aAAa,CAAC2B,iBAAiB;IAC9D,IAAI6E,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvG,KAAK,CAAC6C,MAAM,EAAE,EAAE0D,CAAC,EAAE;MACxC,MAAMpG,IAAI,GAAG,IAAI,CAACH,KAAK,CAACuG,CAAC,CAAC;MAC1B,MAAMsH,KAAK,GAAG1N,IAAI,CAACsD,QAAQ,CAAC,CAAC,CAACqK,KAAK,CAAC,CAAC;MACrCF,SAAS,IAAIC,KAAK;MAClB,IAAI1H,WAAW,GAAG,KAAK;MACvB,IAAInE,QAAQ,CAAC6K,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;QAC7B1G,WAAW,GAAG,IAAI,CAACoC,cAAc,CAACwF,OAAO,CAACxH,CAAC,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,IAAI,CAACY,aAAa,CAACE,mBAAmB,IAAIuG,SAAS,IAAI,IAAI,CAACzG,aAAa,CAACE,mBAAmB,EAAE;UAC/FuG,SAAS,GAAG,CAAC;UACbzH,WAAW,GAAG,IAAI;QACtB;MACJ;MACA,MAAMkH,YAAY,GAAGlN,IAAI,CAAC8E,iBAAiB,CAAC,CAAC,GAAGsI,cAAc;MAC9D,MAAMtB,KAAK,GAAG9L,IAAI,CAACa,QAAQ,CAAC,CAAC,GAAGtB,IAAI,CAACsO,KAAK,GAAG,CAAC;MAC9C,MAAMxI,QAAQ,GAAG,IAAI,CAACxF,KAAK,CAACuG,CAAC,GAAG,CAAC,CAAC;MAClC,MAAM0H,QAAQ,GAAG,IAAI,CAACjO,KAAK,CAACuG,CAAC,GAAG,CAAC,CAAC;MAClC,MAAM6G,gBAAgB,GAAGa,QAAQ,IAAIA,QAAQ,CAAChJ,iBAAiB,CAAC,CAAC,GAAGsI,cAAc;MAClF,MAAMJ,gBAAgB,GAAG3H,QAAQ,IAAIA,QAAQ,CAACP,iBAAiB,CAAC,CAAC,GAAGsI,cAAc;MAClF,MAAMW,SAAS,GAAG1I,QAAQ,IAAIyI,QAAQ,IAAIZ,YAAY,IAAI,CAACF,gBAAgB,IAAI,CAACC,gBAAgB;MAChG,IAAIC,YAAY,EAAE;QACd,IAAIG,WAAW,EAAE;UACbE,WAAW,GAAGD,SAAS,CAACA,SAAS,CAAC5K,MAAM,GAAG,CAAC,CAAC;UAC7C6K,WAAW,CAACS,GAAG,GAAGlC,KAAK;UACvB,IAAI9F,WAAW,EAAE;YACbqH,WAAW,GAAG,KAAK;YACnB,IAAIS,QAAQ,IAAI,CAACb,gBAAgB,IAAIM,WAAW,CAACS,GAAG,KAAKrM,SAAS,EAAE;cAChE4L,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YAC3D;UACJ;QACJ,CAAC,MACI;UACD4E,WAAW,GAAG;YAAEU,KAAK,EAAEnC,KAAK;YAAEkC,GAAG,EAAErM;UAAU,CAAC;UAC9C0L,WAAW,GAAG,IAAI;UAClB,IAAIU,SAAS,EAAE;YACX,MAAMpB,QAAQ,GAAGtH,QAAQ,CAACP,iBAAiB,CAAC,CAAC;YAC7C,MAAM+H,QAAQ,GAAGiB,QAAQ,CAAChJ,iBAAiB,CAAC,CAAC;YAC7C,MAAM8H,IAAI,GAAG5M,IAAI,CAAC8E,iBAAiB,CAAC,CAAC;YACrC,MAAMoJ,aAAa,GAAG,IAAI,CAACzB,mBAAmB,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEzG,CAAC,CAAC;YACrF,IAAI,CAACpF,SAAS,EAAEE,SAAS,CAAC,CAACiN,QAAQ,CAACD,aAAa,CAAC,EAAE;cAChDX,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YAC3D,CAAC,MACI;cACD4E,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YAC3D;UACJ,CAAC,MACI,IAAI,CAACsE,gBAAgB,EAAE;YACxB,IAAI,CAACO,mBAAmB,IAAIpH,CAAC,KAAK,CAAC,KAAK0H,QAAQ,EAAE;cAC9CP,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YAC3D,CAAC,MACI;cACD4E,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YAC3D;UACJ,CAAC,MACI,IAAI3C,WAAW,EAAE;YAClBuH,WAAW,CAACS,GAAG,GAAGT,WAAW,CAACU,KAAK,GAAGtF,iBAAiB;YACvD0E,WAAW,GAAG,KAAK;UACvB;UACAC,SAAS,CAACxJ,IAAI,CAACyJ,WAAW,CAAC;QAC/B;MACJ,CAAC,MACI;QACDF,WAAW,GAAG,KAAK;MACvB;MACAG,mBAAmB,GAAGxH,WAAW;IACrC;IACA,MAAMoI,QAAQ,GAAGd,SAAS,CAACA,SAAS,CAAC5K,MAAM,GAAG,CAAC,CAAC;IAChD,IAAI0L,QAAQ,IAAIA,QAAQ,CAACJ,GAAG,KAAKrM,SAAS,EAAE;MACxCyM,QAAQ,CAACJ,GAAG,GAAGI,QAAQ,CAACH,KAAK,GAAGtF,iBAAiB;IACrD;IACA,OAAO2E,SAAS;EACpB;EACAe,SAASA,CAACC,GAAG,EAAE;IACX,IAAI,CAACzO,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAK;MACzB,MAAM4L,IAAI,GAAG5L,IAAI,CAAC6L,OAAO,CAAC,CAAC;MAC3B,IAAID,IAAI,EAAE;QACN,MAAME,KAAK,GAAG9L,IAAI,CAACa,QAAQ,CAAC,CAAC;QAC7B+K,IAAI,CAAC2C,kBAAkB,CAACzC,KAAK,EAAEA,KAAK,CAAC;QACrCF,IAAI,CAAC4C,UAAU,CAACF,GAAG,CAAC,CAACG,aAAa,CAAC,CAAC;MACxC;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ;EACAC,aAAaA,CAACJ,GAAG,EAAE;IACf,MAAMK,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACvD,MAAMpO,SAAS,GAAG,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAI6L,KAAK,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,MAAM7K,UAAU,GAAGL,SAAS,CAACM,QAAQ,CAAC,CAAC;IACvC,MAAM+N,aAAa,GAAG,IAAI,CAAC5H,aAAa,CAACqB,SAAS,GAAG,IAAI,CAAC/G,cAAc;IACxE,KAAK,IAAI8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,kBAAkB,CAACjM,MAAM,EAAE,EAAE0D,CAAC,EAAE;MAChD,MAAMsG,QAAQ,GAAGiC,kBAAkB,CAACvI,CAAC,CAAC;MACtC,MAAMkH,SAAS,GAAG,IAAI,CAACH,YAAY,CAACT,QAAQ,CAAC;MAC7C,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,SAAS,CAAC5K,MAAM,EAAE,EAAEmM,CAAC,EAAE;QACvC,MAAMC,QAAQ,GAAGxB,SAAS,CAACuB,CAAC,CAAC;QAC7B,MAAME,UAAU,GAAGD,QAAQ,CAACb,KAAK;QACjC,MAAMe,UAAU,GAAG,IAAI,CAACvF,SAAS,CAACsF,UAAU,EAAEnO,UAAU,EAAE8K,KAAK,EAAE,IAAI,CAAC/D,KAAK,CAAC;QAC5E,MAAMsH,SAAS,GAAGH,QAAQ,CAACd,GAAG;QAC9B,IAAIiB,SAAS,EAAE;UACX,MAAMC,SAAS,GAAG,IAAI,CAACzF,SAAS,CAACwF,SAAS,EAAErO,UAAU,EAAE8K,KAAK,EAAE,IAAI,CAAC/D,KAAK,CAAC;UAC1E2G,GAAG,CAACa,SAAS,CAAC,CAAC;UACfb,GAAG,CAACc,MAAM,CAACL,UAAU,EAAEC,UAAU,CAAC;UAClCV,GAAG,CAACe,MAAM,CAACN,UAAU,EAAEC,UAAU,GAAGJ,aAAa,CAAC;UAClDN,GAAG,CAACe,MAAM,CAACJ,SAAS,GAAG,CAAC,EAAEC,SAAS,GAAGN,aAAa,CAAC;UACpDN,GAAG,CAACe,MAAM,CAACJ,SAAS,GAAG,CAAC,EAAEC,SAAS,CAAC;UACpCZ,GAAG,CAACgB,SAAS,CAAC,CAAC;UACfhB,GAAG,CAACiB,IAAI,CAAC,CAAC;QACd,CAAC,MACI;UACD,MAAM,IAAI5P,YAAY,CAAC,aAAa,EAAE,sBAAsB,CAAC;QACjE;MACJ;MACA+L,KAAK,IAAIkD,aAAa,GAAG,GAAG;IAChC;EACJ;EACAY,SAASA,CAAA,EAAG;IACR,OAAO,IAAI;EACf;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACxH,aAAa,EAClB;IACJ,IAAIvI,SAAS,CAAC,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAACmH,aAAa,CAACG,SAAS,EAAE;MAC1D,IAAI,CAAC4D,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAAClB,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAAC8B,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC1D,aAAa,GAAG,IAAI;EAC7B;EACAyH,IAAIA,CAAA,EAAG;IACH,MAAMpB,GAAG,GAAG,IAAI,CAACqB,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC3L,UAAU,EACf;IACJ,IAAI,CAAC,IAAI,CAACgE,aAAa,EAAE;MACrB,IAAI,CAACwH,UAAU,CAAC,CAAC;IACrB;IACAnB,GAAG,CAACuB,SAAS,CAAC,MAAM,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,CAACzB,SAAS,CAACC,GAAG,CAAC;IACnB,IAAI,CAACI,aAAa,CAACJ,GAAG,CAAC;IACvBA,GAAG,CAACyB,UAAU,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}