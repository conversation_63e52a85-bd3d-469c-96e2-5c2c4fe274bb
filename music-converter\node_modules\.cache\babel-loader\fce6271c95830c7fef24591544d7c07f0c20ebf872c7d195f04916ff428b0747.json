{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\VexTabBlock.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function updateVexTabBlock(blockId) {\n  const wrapper = document.getElementById(blockId);\n  const curVexText = wrapper.querySelector('textarea.editor');\n  curVexText.dispatchEvent(new KeyboardEvent(\"keyup\", {\n    bubbles: true,\n    cancelable: true,\n    key: \" \",\n    code: \"Space\"\n  }));\n}\nfunction VexTabBlock({\n  source,\n  id,\n  editorHeight\n}) {\n  _s();\n  const divRef = useRef(null);\n  const originalSourceRef = useRef(source);\n  useEffect(() => {\n    const el = divRef.current;\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\n    const handleInput = e => {\n      const curText = e.target.textContent;\n      originalSourceRef.current = curText;\n      setTimeout(() => {\n        new window.VexTab.Div(el);\n      }, 0);\n    };\n    el.addEventListener('input', handleInput);\n    console.log(\"Original Source: \", originalSourceRef.current);\n    originalSourceRef.current = source;\n    el.textContent = source;\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n    new window.VexTab.Div(el);\n  }, [source, id]);\n  const defaultText = `tabstave notation=true tablature=false key=G time=4/4 \\nnotes A/4`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    style: {\n      minHeight: '0rem',\n      display: 'hidden'\n    },\n    editor: \"true\",\n    \"editor-height\": editorHeight,\n    id: id,\n    ref: divRef,\n    children: defaultText\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(VexTabBlock, \"KVlM2XxlLuyxsCbVLLNK4aY8Z9o=\");\n_c = VexTabBlock;\nexport default VexTabBlock;\nvar _c;\n$RefreshReg$(_c, \"VexTabBlock\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "updateVexTabBlock", "blockId", "wrapper", "document", "getElementById", "curVexText", "querySelector", "dispatchEvent", "KeyboardEvent", "bubbles", "cancelable", "key", "code", "VexTabBlock", "source", "id", "<PERSON><PERSON><PERSON><PERSON>", "_s", "divRef", "originalSourceRef", "el", "current", "window", "VexTab", "Div", "handleInput", "e", "curText", "target", "textContent", "setTimeout", "addEventListener", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "defaultText", "className", "style", "minHeight", "display", "editor", "ref", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/VexTabBlock.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\n\r\nexport function updateVexTabBlock(blockId) {\r\n  const wrapper = document.getElementById(blockId);\r\n  const curVexText = wrapper.querySelector('textarea.editor');\r\n  curVexText.dispatchEvent(new KeyboardEvent(\"keyup\", { bubbles: true, cancelable: true, key: \" \" , code: \"Space\" }));\r\n}\r\n\r\nfunction VexTabBlock({ source, id, editorHeight }) {\r\n  const divRef = useRef(null);\r\n  const originalSourceRef = useRef(source);\r\n\r\n  useEffect(() => {\r\n    const el = divRef.current;\r\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\r\n\r\n    const handleInput = (e) => {\r\n      const curText = e.target.textContent;\r\n      originalSourceRef.current = curText;\r\n\r\n      setTimeout(() => {\r\n        new window.VexTab.Div(el);\r\n      }, 0);\r\n    }\r\n\r\n    el.addEventListener('input', handleInput);\r\n    console.log(\"Original Source: \", originalSourceRef.current);\r\n\r\n    originalSourceRef.current = source;\r\n    el.textContent = source;\r\n    while (el.firstChild) {\r\n      el.removeChild(el.firstChild);\r\n    }\r\n    new window.VexTab.Div(el); \r\n  }, [source, id]);\r\n\r\n  const defaultText = `tabstave notation=true tablature=false key=G time=4/4 \\nnotes A/4`;\r\n\r\n  return (\r\n    <div\r\n      className=\"vextab-auto\"\r\n      style={{ minHeight: '0rem', display: 'hidden' }}\r\n      editor=\"true\"\r\n      editor-height={editorHeight}\r\n      id={id}\r\n      ref={divRef}\r\n    >\r\n      {defaultText}\r\n    </div>\r\n  )  \r\n}\r\n\r\nexport default VexTabBlock;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EACzC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAC;EAChD,MAAMI,UAAU,GAAGH,OAAO,CAACI,aAAa,CAAC,iBAAiB,CAAC;EAC3DD,UAAU,CAACE,aAAa,CAAC,IAAIC,aAAa,CAAC,OAAO,EAAE;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,GAAG,EAAE,GAAG;IAAGC,IAAI,EAAE;EAAQ,CAAC,CAAC,CAAC;AACrH;AAEA,SAASC,WAAWA,CAAC;EAAEC,MAAM;EAAEC,EAAE;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACjD,MAAMC,MAAM,GAAGrB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAMsB,iBAAiB,GAAGtB,MAAM,CAACiB,MAAM,CAAC;EAExClB,SAAS,CAAC,MAAM;IACd,MAAMwB,EAAE,GAAGF,MAAM,CAACG,OAAO;IACzB,IAAI,CAACD,EAAE,IAAI,CAACE,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;IAEjD,MAAMC,WAAW,GAAIC,CAAC,IAAK;MACzB,MAAMC,OAAO,GAAGD,CAAC,CAACE,MAAM,CAACC,WAAW;MACpCV,iBAAiB,CAACE,OAAO,GAAGM,OAAO;MAEnCG,UAAU,CAAC,MAAM;QACf,IAAIR,MAAM,CAACC,MAAM,CAACC,GAAG,CAACJ,EAAE,CAAC;MAC3B,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAEDA,EAAE,CAACW,gBAAgB,CAAC,OAAO,EAAEN,WAAW,CAAC;IACzCO,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEd,iBAAiB,CAACE,OAAO,CAAC;IAE3DF,iBAAiB,CAACE,OAAO,GAAGP,MAAM;IAClCM,EAAE,CAACS,WAAW,GAAGf,MAAM;IACvB,OAAOM,EAAE,CAACc,UAAU,EAAE;MACpBd,EAAE,CAACe,WAAW,CAACf,EAAE,CAACc,UAAU,CAAC;IAC/B;IACA,IAAIZ,MAAM,CAACC,MAAM,CAACC,GAAG,CAACJ,EAAE,CAAC;EAC3B,CAAC,EAAE,CAACN,MAAM,EAAEC,EAAE,CAAC,CAAC;EAEhB,MAAMqB,WAAW,GAAG,mEAAmE;EAEvF,oBACErC,OAAA;IACEsC,SAAS,EAAC,aAAa;IACvBC,KAAK,EAAE;MAAEC,SAAS,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAS,CAAE;IAChDC,MAAM,EAAC,MAAM;IACb,iBAAezB,YAAa;IAC5BD,EAAE,EAAEA,EAAG;IACP2B,GAAG,EAAExB,MAAO;IAAAyB,QAAA,EAEXP;EAAW;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAAC9B,EAAA,CA1CQJ,WAAW;AAAAmC,EAAA,GAAXnC,WAAW;AA4CpB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}