{"ast": null, "code": "import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Tables } from './tables.js';\nimport { defined, RuntimeError } from './util.js';\nexport class Tickable extends Element {\n  static get CATEGORY() {\n    return \"Tickable\";\n  }\n  constructor() {\n    super();\n    this._preFormatted = false;\n    this._postFormatted = false;\n    this.ticks = new Fraction(0, 1);\n    this.intrinsicTicks = 0;\n    this.tickMultiplier = new Fraction(1, 1);\n    this.modifiers = [];\n    this.tupletStack = [];\n    this.alignCenter = false;\n    this.centerXShift = 0;\n    this.ignoreTicks = false;\n    this.formatterMetrics = {\n      freedom: {\n        left: 0,\n        right: 0\n      },\n      duration: '',\n      iterations: 0,\n      space: {\n        used: 0,\n        mean: 0,\n        deviation: 0\n      }\n    };\n  }\n  reset() {\n    return this;\n  }\n  getTicks() {\n    return this.ticks;\n  }\n  shouldIgnoreTicks() {\n    return this.ignoreTicks;\n  }\n  setIgnoreTicks(flag) {\n    this.ignoreTicks = flag;\n    return this;\n  }\n  getWidth() {\n    if (!this._preFormatted) {\n      throw new RuntimeError('UnformattedNote', \"Can't call <PERSON>Width on an unformatted note.\");\n    }\n    return this.width + (this.modifierContext ? this.modifierContext.getWidth() : 0);\n  }\n  getX() {\n    const tickContext = this.checkTickContext(`Can't getX() without a TickContext.`);\n    return tickContext.getX() + this.xShift;\n  }\n  getFormatterMetrics() {\n    return this.formatterMetrics;\n  }\n  getCenterXShift() {\n    if (this.isCenterAligned()) {\n      return this.centerXShift;\n    }\n    return 0;\n  }\n  setCenterXShift(centerXShift) {\n    this.centerXShift = centerXShift;\n    return this;\n  }\n  isCenterAligned() {\n    return this.alignCenter;\n  }\n  setCenterAlignment(alignCenter) {\n    this.alignCenter = alignCenter;\n    return this;\n  }\n  getVoice() {\n    return defined(this.voice, 'NoVoice', 'Tickable has no voice.');\n  }\n  setVoice(voice) {\n    this.voice = voice;\n  }\n  getTuplet() {\n    return this.tuplet;\n  }\n  getTupletStack() {\n    return this.tupletStack;\n  }\n  resetTuplet(tuplet) {\n    let noteCount;\n    let notesOccupied;\n    if (tuplet) {\n      const i = this.tupletStack.indexOf(tuplet);\n      if (i !== -1) {\n        this.tupletStack.splice(i, 1);\n        noteCount = tuplet.getNoteCount();\n        notesOccupied = tuplet.getNotesOccupied();\n        this.applyTickMultiplier(noteCount, notesOccupied);\n      }\n      return this;\n    }\n    while (this.tupletStack.length) {\n      tuplet = this.tupletStack.pop();\n      noteCount = tuplet.getNoteCount();\n      notesOccupied = tuplet.getNotesOccupied();\n      this.applyTickMultiplier(noteCount, notesOccupied);\n    }\n    return this;\n  }\n  setTuplet(tuplet) {\n    if (tuplet) {\n      this.tupletStack.push(tuplet);\n      const noteCount = tuplet.getNoteCount();\n      const notesOccupied = tuplet.getNotesOccupied();\n      this.applyTickMultiplier(notesOccupied, noteCount);\n    }\n    this.tuplet = tuplet;\n    return this;\n  }\n  addToModifierContext(mc) {\n    this.modifierContext = mc;\n    for (let i = 0; i < this.modifiers.length; ++i) {\n      this.modifierContext.addMember(this.modifiers[i]);\n    }\n    this.modifierContext.addMember(this);\n    this._preFormatted = false;\n    return this;\n  }\n  addModifier(modifier, index = 0) {\n    this.modifiers.push(modifier);\n    this._preFormatted = false;\n    return this;\n  }\n  getModifiers() {\n    return this.modifiers;\n  }\n  setTickContext(tc) {\n    this.tickContext = tc;\n    this._preFormatted = false;\n  }\n  checkTickContext(message = 'Tickable has no tick context.') {\n    return defined(this.tickContext, 'NoTickContext', message);\n  }\n  preFormat() {\n    if (this._preFormatted) return;\n    this.width = 0;\n    if (this.modifierContext) {\n      this.modifierContext.preFormat();\n      this.width += this.modifierContext.getWidth();\n    }\n  }\n  set preFormatted(value) {\n    this._preFormatted = value;\n  }\n  get preFormatted() {\n    return this._preFormatted;\n  }\n  postFormat() {\n    if (this._postFormatted) return this;\n    this._postFormatted = true;\n    return this;\n  }\n  set postFormatted(value) {\n    this._postFormatted = value;\n  }\n  get postFormatted() {\n    return this._postFormatted;\n  }\n  getIntrinsicTicks() {\n    return this.intrinsicTicks;\n  }\n  setIntrinsicTicks(intrinsicTicks) {\n    this.intrinsicTicks = intrinsicTicks;\n    this.ticks = this.tickMultiplier.clone().multiply(this.intrinsicTicks);\n  }\n  getTickMultiplier() {\n    return this.tickMultiplier;\n  }\n  applyTickMultiplier(numerator, denominator) {\n    this.tickMultiplier.multiply(numerator, denominator);\n    this.ticks = this.tickMultiplier.clone().multiply(this.intrinsicTicks);\n  }\n  setDuration(duration) {\n    const ticks = duration.numerator * (Tables.RESOLUTION / duration.denominator);\n    this.ticks = this.tickMultiplier.clone().multiply(ticks);\n    this.intrinsicTicks = this.ticks.value();\n  }\n  getAbsoluteX() {\n    const tickContext = this.checkTickContext(`Can't getAbsoluteX() without a TickContext.`);\n    return tickContext.getX();\n  }\n  setModifierContext(mc) {\n    this.modifierContext = mc;\n    return this;\n  }\n  getModifierContext() {\n    return this.modifierContext;\n  }\n  checkModifierContext() {\n    return defined(this.modifierContext, 'NoModifierContext', 'No modifier context attached to this tickable.');\n  }\n}", "map": {"version": 3, "names": ["Element", "Fraction", "Tables", "defined", "RuntimeError", "Tickable", "CATEGORY", "constructor", "_preFormatted", "_postFormatted", "ticks", "intrinsicTicks", "tickMultiplier", "modifiers", "tupletStack", "alignCenter", "centerXShift", "ignoreTicks", "formatterMetrics", "freedom", "left", "right", "duration", "iterations", "space", "used", "mean", "deviation", "reset", "getTicks", "shouldIgnoreTicks", "setIgnoreTicks", "flag", "getWidth", "width", "modifierContext", "getX", "tickContext", "checkTickContext", "xShift", "getFormatterMetrics", "getCenterXShift", "isCenterAligned", "setCenterXShift", "setCenterAlignment", "getVoice", "voice", "setVoice", "getTuplet", "tuplet", "getTupletStack", "resetTuplet", "noteCount", "notesOccupied", "i", "indexOf", "splice", "getNoteCount", "getNotesOccupied", "applyTickMultiplier", "length", "pop", "setTuplet", "push", "addToModifierContext", "mc", "addMember", "addModifier", "modifier", "index", "getModifiers", "setTickContext", "tc", "message", "preFormat", "preFormatted", "value", "postFormat", "postFormatted", "getIntrinsicTicks", "setIntrinsicTicks", "clone", "multiply", "getTickMultiplier", "numerator", "denominator", "setDuration", "RESOLUTION", "getAbsoluteX", "setModifierContext", "getModifierContext", "checkModifierContext"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tickable.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Fraction } from './fraction.js';\nimport { Tables } from './tables.js';\nimport { defined, RuntimeError } from './util.js';\nexport class Tickable extends Element {\n    static get CATEGORY() {\n        return \"Tickable\";\n    }\n    constructor() {\n        super();\n        this._preFormatted = false;\n        this._postFormatted = false;\n        this.ticks = new Fraction(0, 1);\n        this.intrinsicTicks = 0;\n        this.tickMultiplier = new Fraction(1, 1);\n        this.modifiers = [];\n        this.tupletStack = [];\n        this.alignCenter = false;\n        this.centerXShift = 0;\n        this.ignoreTicks = false;\n        this.formatterMetrics = {\n            freedom: { left: 0, right: 0 },\n            duration: '',\n            iterations: 0,\n            space: {\n                used: 0,\n                mean: 0,\n                deviation: 0,\n            },\n        };\n    }\n    reset() {\n        return this;\n    }\n    getTicks() {\n        return this.ticks;\n    }\n    shouldIgnoreTicks() {\n        return this.ignoreTicks;\n    }\n    setIgnoreTicks(flag) {\n        this.ignoreTicks = flag;\n        return this;\n    }\n    getWidth() {\n        if (!this._preFormatted) {\n            throw new RuntimeError('UnformattedNote', \"Can't call GetWidth on an unformatted note.\");\n        }\n        return this.width + (this.modifierContext ? this.modifierContext.getWidth() : 0);\n    }\n    getX() {\n        const tickContext = this.checkTickContext(`Can't getX() without a TickContext.`);\n        return tickContext.getX() + this.xShift;\n    }\n    getFormatterMetrics() {\n        return this.formatterMetrics;\n    }\n    getCenterXShift() {\n        if (this.isCenterAligned()) {\n            return this.centerXShift;\n        }\n        return 0;\n    }\n    setCenterXShift(centerXShift) {\n        this.centerXShift = centerXShift;\n        return this;\n    }\n    isCenterAligned() {\n        return this.alignCenter;\n    }\n    setCenterAlignment(alignCenter) {\n        this.alignCenter = alignCenter;\n        return this;\n    }\n    getVoice() {\n        return defined(this.voice, 'NoVoice', 'Tickable has no voice.');\n    }\n    setVoice(voice) {\n        this.voice = voice;\n    }\n    getTuplet() {\n        return this.tuplet;\n    }\n    getTupletStack() {\n        return this.tupletStack;\n    }\n    resetTuplet(tuplet) {\n        let noteCount;\n        let notesOccupied;\n        if (tuplet) {\n            const i = this.tupletStack.indexOf(tuplet);\n            if (i !== -1) {\n                this.tupletStack.splice(i, 1);\n                noteCount = tuplet.getNoteCount();\n                notesOccupied = tuplet.getNotesOccupied();\n                this.applyTickMultiplier(noteCount, notesOccupied);\n            }\n            return this;\n        }\n        while (this.tupletStack.length) {\n            tuplet = this.tupletStack.pop();\n            noteCount = tuplet.getNoteCount();\n            notesOccupied = tuplet.getNotesOccupied();\n            this.applyTickMultiplier(noteCount, notesOccupied);\n        }\n        return this;\n    }\n    setTuplet(tuplet) {\n        if (tuplet) {\n            this.tupletStack.push(tuplet);\n            const noteCount = tuplet.getNoteCount();\n            const notesOccupied = tuplet.getNotesOccupied();\n            this.applyTickMultiplier(notesOccupied, noteCount);\n        }\n        this.tuplet = tuplet;\n        return this;\n    }\n    addToModifierContext(mc) {\n        this.modifierContext = mc;\n        for (let i = 0; i < this.modifiers.length; ++i) {\n            this.modifierContext.addMember(this.modifiers[i]);\n        }\n        this.modifierContext.addMember(this);\n        this._preFormatted = false;\n        return this;\n    }\n    addModifier(modifier, index = 0) {\n        this.modifiers.push(modifier);\n        this._preFormatted = false;\n        return this;\n    }\n    getModifiers() {\n        return this.modifiers;\n    }\n    setTickContext(tc) {\n        this.tickContext = tc;\n        this._preFormatted = false;\n    }\n    checkTickContext(message = 'Tickable has no tick context.') {\n        return defined(this.tickContext, 'NoTickContext', message);\n    }\n    preFormat() {\n        if (this._preFormatted)\n            return;\n        this.width = 0;\n        if (this.modifierContext) {\n            this.modifierContext.preFormat();\n            this.width += this.modifierContext.getWidth();\n        }\n    }\n    set preFormatted(value) {\n        this._preFormatted = value;\n    }\n    get preFormatted() {\n        return this._preFormatted;\n    }\n    postFormat() {\n        if (this._postFormatted)\n            return this;\n        this._postFormatted = true;\n        return this;\n    }\n    set postFormatted(value) {\n        this._postFormatted = value;\n    }\n    get postFormatted() {\n        return this._postFormatted;\n    }\n    getIntrinsicTicks() {\n        return this.intrinsicTicks;\n    }\n    setIntrinsicTicks(intrinsicTicks) {\n        this.intrinsicTicks = intrinsicTicks;\n        this.ticks = this.tickMultiplier.clone().multiply(this.intrinsicTicks);\n    }\n    getTickMultiplier() {\n        return this.tickMultiplier;\n    }\n    applyTickMultiplier(numerator, denominator) {\n        this.tickMultiplier.multiply(numerator, denominator);\n        this.ticks = this.tickMultiplier.clone().multiply(this.intrinsicTicks);\n    }\n    setDuration(duration) {\n        const ticks = duration.numerator * (Tables.RESOLUTION / duration.denominator);\n        this.ticks = this.tickMultiplier.clone().multiply(ticks);\n        this.intrinsicTicks = this.ticks.value();\n    }\n    getAbsoluteX() {\n        const tickContext = this.checkTickContext(`Can't getAbsoluteX() without a TickContext.`);\n        return tickContext.getX();\n    }\n    setModifierContext(mc) {\n        this.modifierContext = mc;\n        return this;\n    }\n    getModifierContext() {\n        return this.modifierContext;\n    }\n    checkModifierContext() {\n        return defined(this.modifierContext, 'NoModifierContext', 'No modifier context attached to this tickable.');\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AACjD,OAAO,MAAMC,QAAQ,SAASL,OAAO,CAAC;EAClC,WAAWM,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,KAAK,GAAG,IAAIT,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,IAAI,CAACU,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,cAAc,GAAG,IAAIX,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACxC,IAAI,CAACY,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG;MACpBC,OAAO,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;MAC9BC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,KAAK,EAAE;QACHC,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE;MACf;IACJ,CAAC;EACL;EACAC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACnB,KAAK;EACrB;EACAoB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACb,WAAW;EAC3B;EACAc,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACf,WAAW,GAAGe,IAAI;IACvB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE;MACrB,MAAM,IAAIJ,YAAY,CAAC,iBAAiB,EAAE,6CAA6C,CAAC;IAC5F;IACA,OAAO,IAAI,CAAC8B,KAAK,IAAI,IAAI,CAACC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACF,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF;EACAG,IAAIA,CAAA,EAAG;IACH,MAAMC,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,qCAAqC,CAAC;IAChF,OAAOD,WAAW,CAACD,IAAI,CAAC,CAAC,GAAG,IAAI,CAACG,MAAM;EAC3C;EACAC,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACtB,gBAAgB;EAChC;EACAuB,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MACxB,OAAO,IAAI,CAAC1B,YAAY;IAC5B;IACA,OAAO,CAAC;EACZ;EACA2B,eAAeA,CAAC3B,YAAY,EAAE;IAC1B,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI;EACf;EACA0B,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3B,WAAW;EAC3B;EACA6B,kBAAkBA,CAAC7B,WAAW,EAAE;IAC5B,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,OAAO,IAAI;EACf;EACA8B,QAAQA,CAAA,EAAG;IACP,OAAO1C,OAAO,CAAC,IAAI,CAAC2C,KAAK,EAAE,SAAS,EAAE,wBAAwB,CAAC;EACnE;EACAC,QAAQA,CAACD,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAE,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACpC,WAAW;EAC3B;EACAqC,WAAWA,CAACF,MAAM,EAAE;IAChB,IAAIG,SAAS;IACb,IAAIC,aAAa;IACjB,IAAIJ,MAAM,EAAE;MACR,MAAMK,CAAC,GAAG,IAAI,CAACxC,WAAW,CAACyC,OAAO,CAACN,MAAM,CAAC;MAC1C,IAAIK,CAAC,KAAK,CAAC,CAAC,EAAE;QACV,IAAI,CAACxC,WAAW,CAAC0C,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;QAC7BF,SAAS,GAAGH,MAAM,CAACQ,YAAY,CAAC,CAAC;QACjCJ,aAAa,GAAGJ,MAAM,CAACS,gBAAgB,CAAC,CAAC;QACzC,IAAI,CAACC,mBAAmB,CAACP,SAAS,EAAEC,aAAa,CAAC;MACtD;MACA,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACvC,WAAW,CAAC8C,MAAM,EAAE;MAC5BX,MAAM,GAAG,IAAI,CAACnC,WAAW,CAAC+C,GAAG,CAAC,CAAC;MAC/BT,SAAS,GAAGH,MAAM,CAACQ,YAAY,CAAC,CAAC;MACjCJ,aAAa,GAAGJ,MAAM,CAACS,gBAAgB,CAAC,CAAC;MACzC,IAAI,CAACC,mBAAmB,CAACP,SAAS,EAAEC,aAAa,CAAC;IACtD;IACA,OAAO,IAAI;EACf;EACAS,SAASA,CAACb,MAAM,EAAE;IACd,IAAIA,MAAM,EAAE;MACR,IAAI,CAACnC,WAAW,CAACiD,IAAI,CAACd,MAAM,CAAC;MAC7B,MAAMG,SAAS,GAAGH,MAAM,CAACQ,YAAY,CAAC,CAAC;MACvC,MAAMJ,aAAa,GAAGJ,MAAM,CAACS,gBAAgB,CAAC,CAAC;MAC/C,IAAI,CAACC,mBAAmB,CAACN,aAAa,EAAED,SAAS,CAAC;IACtD;IACA,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAe,oBAAoBA,CAACC,EAAE,EAAE;IACrB,IAAI,CAAC9B,eAAe,GAAG8B,EAAE;IACzB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,SAAS,CAAC+C,MAAM,EAAE,EAAEN,CAAC,EAAE;MAC5C,IAAI,CAACnB,eAAe,CAAC+B,SAAS,CAAC,IAAI,CAACrD,SAAS,CAACyC,CAAC,CAAC,CAAC;IACrD;IACA,IAAI,CAACnB,eAAe,CAAC+B,SAAS,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC1D,aAAa,GAAG,KAAK;IAC1B,OAAO,IAAI;EACf;EACA2D,WAAWA,CAACC,QAAQ,EAAEC,KAAK,GAAG,CAAC,EAAE;IAC7B,IAAI,CAACxD,SAAS,CAACkD,IAAI,CAACK,QAAQ,CAAC;IAC7B,IAAI,CAAC5D,aAAa,GAAG,KAAK;IAC1B,OAAO,IAAI;EACf;EACA8D,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACzD,SAAS;EACzB;EACA0D,cAAcA,CAACC,EAAE,EAAE;IACf,IAAI,CAACnC,WAAW,GAAGmC,EAAE;IACrB,IAAI,CAAChE,aAAa,GAAG,KAAK;EAC9B;EACA8B,gBAAgBA,CAACmC,OAAO,GAAG,+BAA+B,EAAE;IACxD,OAAOtE,OAAO,CAAC,IAAI,CAACkC,WAAW,EAAE,eAAe,EAAEoC,OAAO,CAAC;EAC9D;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAClE,aAAa,EAClB;IACJ,IAAI,CAAC0B,KAAK,GAAG,CAAC;IACd,IAAI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACuC,SAAS,CAAC,CAAC;MAChC,IAAI,CAACxC,KAAK,IAAI,IAAI,CAACC,eAAe,CAACF,QAAQ,CAAC,CAAC;IACjD;EACJ;EACA,IAAI0C,YAAYA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACpE,aAAa,GAAGoE,KAAK;EAC9B;EACA,IAAID,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnE,aAAa;EAC7B;EACAqE,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACpE,cAAc,EACnB,OAAO,IAAI;IACf,IAAI,CAACA,cAAc,GAAG,IAAI;IAC1B,OAAO,IAAI;EACf;EACA,IAAIqE,aAAaA,CAACF,KAAK,EAAE;IACrB,IAAI,CAACnE,cAAc,GAAGmE,KAAK;EAC/B;EACA,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACrE,cAAc;EAC9B;EACAsE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpE,cAAc;EAC9B;EACAqE,iBAAiBA,CAACrE,cAAc,EAAE;IAC9B,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACD,KAAK,GAAG,IAAI,CAACE,cAAc,CAACqE,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACvE,cAAc,CAAC;EAC1E;EACAwE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACvE,cAAc;EAC9B;EACA+C,mBAAmBA,CAACyB,SAAS,EAAEC,WAAW,EAAE;IACxC,IAAI,CAACzE,cAAc,CAACsE,QAAQ,CAACE,SAAS,EAAEC,WAAW,CAAC;IACpD,IAAI,CAAC3E,KAAK,GAAG,IAAI,CAACE,cAAc,CAACqE,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACvE,cAAc,CAAC;EAC1E;EACA2E,WAAWA,CAAChE,QAAQ,EAAE;IAClB,MAAMZ,KAAK,GAAGY,QAAQ,CAAC8D,SAAS,IAAIlF,MAAM,CAACqF,UAAU,GAAGjE,QAAQ,CAAC+D,WAAW,CAAC;IAC7E,IAAI,CAAC3E,KAAK,GAAG,IAAI,CAACE,cAAc,CAACqE,KAAK,CAAC,CAAC,CAACC,QAAQ,CAACxE,KAAK,CAAC;IACxD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,KAAK,CAACkE,KAAK,CAAC,CAAC;EAC5C;EACAY,YAAYA,CAAA,EAAG;IACX,MAAMnD,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAAC,6CAA6C,CAAC;IACxF,OAAOD,WAAW,CAACD,IAAI,CAAC,CAAC;EAC7B;EACAqD,kBAAkBA,CAACxB,EAAE,EAAE;IACnB,IAAI,CAAC9B,eAAe,GAAG8B,EAAE;IACzB,OAAO,IAAI;EACf;EACAyB,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACvD,eAAe;EAC/B;EACAwD,oBAAoBA,CAAA,EAAG;IACnB,OAAOxF,OAAO,CAAC,IAAI,CAACgC,eAAe,EAAE,mBAAmB,EAAE,gDAAgD,CAAC;EAC/G;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}