
// ----- CONVERSION FUNCTIONS ----- //


// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'
// MIDI FORMAT: 0-127
// PARSE FORMAT: { step, alter, octave }


const SEMI_MAP = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };

export function gigaConvert(key, time, notes) {
  const defaultSetup = 'tabstave notaion=true tablature=false'
  console.log("Key: ", key);
  console.log("Time: ", time);
  console.log("Notes: ", notes);

  const parsedNotes = notes.map(parseNote);
  const midiNotes = parsedNotes.map(toMidi + 9);
  const newNotes = midiNotes.map(fromMidi);
  const formattedNotes = newNotes.map(formatNote);

  return `${defaultSetup} key=${key} time=${time} \nnotes ${formattedNotes.join(' ')}`;
}

export function parseNote(string) {
  console.log("String to parse: ", string);
  const m = string.match(/^([A-G])([#nb]?)[/](\d)$/);
  console.log("Match: ", m);
  if (!m) {
    throw new Error(`Invalid note format: ${string}`);
  }
  const [ step, acc, octave ] = m;
  return { 
    step,
    alter: acc === '#' ? 1 : (acc === 'b' ? -1 : 0),
    octave: parseInt(octave, 10),
  };
}

export function toMidi(parsedNote) {
  const {step, alter, octave} = parsedNote;
  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;
  return midi;
}

export function fromMidi(midiNote) {
  const octave = Math.floor(midiNote / 12) - 1;
  const semi = midiNote % 12;
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val === semi) {
      return { step, alter: 0, octave };
    }
  }
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val + 1 === semi) {
      return { step, alter: 1, octave };
    }
  }
}

export function formatNote(parsedNote) {
  const { step, alter, octave } = parsedNote;
  let acc = '';
  if (alter === 1) {
    acc = '#';
  }
  return `${step}${acc}/${octave}`;
}