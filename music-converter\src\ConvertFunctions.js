
// ----- CONVERSION FUNCTIONS ----- //


// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'
// MIDI FORMAT: 0-127
// PARSE FORMAT: { step, alter, octave }


const SEMI_MAP = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };
// Track what notes should be converted depending on the key
const keys = {
  'G' : {
    'F': 'F#',
  },
  'C' : {
  },
  'F' : {
    'B': 'B@',
  },
  'Bb': {
    'B': 'B@',
    'E': 'E@',
  },
  'Eb': {
    'B': 'B@',
    'E': 'E@',
    'A': 'A@',
  },
  'Ab': {
    'B': 'B@',
    'E': 'E@',
    'A': 'A@',
    'D': 'D@',
  },
  'Db': {
    'B': 'B@',
    'E': 'E@',
    'A': 'A@',
    'D': 'D@',
    'G': 'G@',
  },
  'F#': {
    'F': 'F#',
    'C': 'C#',
    'G': 'G#',
    'D': 'D#',
    'A': 'A#',
    'E': 'E#',
  },
  'B': {
    'F': 'F#',
    'C': 'C#',
    'G': 'G#',
    'D': 'D#',
    'A': 'A#',
  },
  'E': {
    'F': 'F#',
    'C': 'C#',
    'G': 'G#',
    'D': 'D#',
  },
  'A': {
    'F': 'F#',
    'C': 'C#',
    'G': 'G#'
  },
  'D': {
    'F': 'F#', 
    'C': 'C#',
  }
}
// Measure the distance each instrument is from the piano version to do a 2-step conversion
const instruments = {
  piano: 0,
  altoSax: -3,
  tenorSax: -5,
  baritoneSax: -7,
  bassoon: -12,
  clarinet: -2,
  flute: -7,
  trumpet: 2,
  trombone: -5,
  violin: -5,
  viola: -7,
  cello: -12,
  bass: -12,
}

export function gigaConvert(key, time, notes, inputInstrument, outputInstrument) {
  const defaultSetup = 'tabstave notation=true tablature=false';
    // Determine what notes are effected by the input key signature
    
  
  const isNoteStart = (char) => {
    return /[A-G]/.test(char);
  }

  const extractNote = (string, start) => {
    if (/[#n@]/.test(string[start+1])) {
      return { note: string.slice(start, start+4), nextIndex: start+4 };
    } else {
      return { note: string.slice(start, start+3), nextIndex: start+3 };
    }
  }

  const newKey = convertKey(key, inputInstrument, outputInstrument);

  const string = notes.join(' ');
  let res = '';
  let i = 0;

  while (i < string.length) {
    const char = string[i];

    if (isNoteStart(char)) {
      let { note, nextIndex } = extractNote(string, i);
      if (note.length === 3 && keys[key][note[0]]) {
        note = keys[key][note[0]] + note.slice(1);
      }
      const convertedNote = convertNote(note, inputInstrument, outputInstrument);
      res += convertedNote;
      i = nextIndex;
    } else {
      res += char;
      i++;
    }
  }
  return `${defaultSetup} key=${newKey} time=${time} \nnotes ${res}`;
}

function convertKey(key, inputInstrument, outputInstrument) {
  let tempKey = key;
  if (tempKey.length > 1 && tempKey[1] === 'b') {
    tempKey = tempKey[0] + '@';
  }
  tempKey += '/4';
  // Convert key like converting a note
  const parsedKey = parseNote(tempKey);
  const midi = toMidi(parsedKey);
  const newMidi = midi - instruments[inputInstrument] + instruments[outputInstrument];
  const newNote = fromMidi(newMidi);
  let newKey = formatNote(newNote);
  newKey = newKey.slice(0, -2); // Remove the '/4' at the end of the key
  if (newKey.length > 1 && newKey[1] === '@') {
    newKey = newKey[0] + 'b';
  }
  return newKey;
}

function convertNote(note, inputInstrument, outputInstrument) {
  const parsedNote = parseNote(note);
  const midi = toMidi(parsedNote);
  // Go down from the input instrument to piano, then go back up to output instrument
  // Not always "go down" then "go up", just need to reverse the transformation
  const newMidi = midi - instruments[inputInstrument] + instruments[outputInstrument];
  const newNote = fromMidi(newMidi);
  return formatNote(newNote);
}

function parseNote(string) {
  const m = string.match(/^([A-G])([#n@]?)[/](\d)$/);
  if (!m) {
    throw new Error(`Invalid note format: ${string}`);
  }
  const step = m[1];
  const acc = m[2];
  const octave = m[3];
  return { 
    step,
    alter: acc === '#' ? 1 : (acc === '@' ? -1 : 0),
    octave: parseInt(octave, 10),
  };
}

function toMidi(parsedNote) {
  const {step, alter, octave} = parsedNote;
  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;
  return midi;
}

function fromMidi(midiNote) {
  const octave = Math.floor(midiNote / 12) - 1;
  const semi = midiNote % 12;
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val === semi) {
      return { step, alter: 0, octave };
    }
  }
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val + 1 === semi) {
      return { step, alter: 1, octave };
    }
  }
}

function formatNote(parsedNote) {
  const { step, alter, octave } = parsedNote;
  let acc = '';
  if (alter === 1) {
    acc = '#';
  }
  return `${step}${acc}/${octave}`;
}