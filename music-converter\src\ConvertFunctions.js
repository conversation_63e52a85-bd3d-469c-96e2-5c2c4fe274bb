
// ----- CONVERSION FUNCTIONS ----- //


// NOTE FORMAT: '{NOTE}{ACCIDENTAL} / {OCTAVE} , {DURATION} / {DOT} / {ACCENT} / {SLUR} / {BEAMS}'
// MIDI FORMAT: 0-127
// PARSE FORMAT: { step, alter, octave }


const SEMI_MAP = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };

export function parseNote(string) {
  const m = string.match(/^([A-G])([#nb]?)[/](\d)$/);
  if (!m) {
    throw new Error(`Invalid note format: ${string}`);
  }
  const [ step, acc, octave ] = m;
  return { 
    step,
    alter: acc === '#' ? 1 : (acc === 'b' ? -1 : 0),
    octave: parseInt(octave, 10),
  };
}

export function toMidi(parsedNote) {
  const {step, alter, octave} = parsedNote;
  const midi = (octave + 1) * 12 + SEMI_MAP[step] + alter;
  return midi;
}

export function fromMidi(midiNote) {
  const octave = Math.floor(midiNote / 12) - 1;
  const semi = midiNote % 12;
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val === semi) {
      return { step, alter: 0, octave };
    }
  }
  for (const [step, val] of Object.entries(SEMI_MAP)) {
    if (val + 1 === semi) {
      return { step, alter: 1, octave };
    }
  }
}

export function formatNote(parsedNote) {
  const { step, alter, octave } = parsedNote;
  let acc = '';
  if (alter === 1) {
    acc = '#';
  }
  return `${step}${acc}/${octave}`;
}