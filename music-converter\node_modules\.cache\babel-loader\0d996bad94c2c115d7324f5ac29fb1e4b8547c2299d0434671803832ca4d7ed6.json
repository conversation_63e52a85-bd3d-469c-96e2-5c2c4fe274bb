{"ast": null, "code": "import { StemmableNote } from './stemmablenote.js';\nimport { isAnnotation } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nconst ERROR_MSG = 'Ghost note must have valid initialization data to identify duration.';\nexport class GhostNote extends StemmableNote {\n  static get CATEGORY() {\n    return \"GhostNote\";\n  }\n  constructor(parameter) {\n    if (!parameter) {\n      throw new RuntimeError('BadArguments', ERROR_MSG);\n    }\n    let noteStruct;\n    if (typeof parameter === 'string') {\n      noteStruct = {\n        duration: parameter\n      };\n    } else if (typeof parameter === 'object') {\n      noteStruct = parameter;\n    } else {\n      throw new RuntimeError('BadArguments', ERROR_MSG);\n    }\n    super(noteStruct);\n    this.setWidth(0);\n  }\n  isRest() {\n    return true;\n  }\n  setStave(stave) {\n    super.setStave(stave);\n    return this;\n  }\n  addToModifierContext(mc) {\n    return this;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    this.setRendered();\n    for (let i = 0; i < this.modifiers.length; ++i) {\n      const modifier = this.modifiers[i];\n      if (isAnnotation(modifier)) {\n        modifier.setContext(this.getContext());\n        modifier.drawWithStyle();\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["StemmableNote", "isAnnotation", "RuntimeError", "ERROR_MSG", "GhostNote", "CATEGORY", "constructor", "parameter", "noteStruct", "duration", "<PERSON><PERSON><PERSON><PERSON>", "isRest", "setStave", "stave", "addToModifierContext", "mc", "preFormat", "preFormatted", "draw", "setRendered", "i", "modifiers", "length", "modifier", "setContext", "getContext", "drawWithStyle"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/ghostnote.js"], "sourcesContent": ["import { StemmableNote } from './stemmablenote.js';\nimport { isAnnotation } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nconst ERROR_MSG = 'Ghost note must have valid initialization data to identify duration.';\nexport class GhostNote extends StemmableNote {\n    static get CATEGORY() {\n        return \"GhostNote\";\n    }\n    constructor(parameter) {\n        if (!parameter) {\n            throw new RuntimeError('BadArguments', ERROR_MSG);\n        }\n        let noteStruct;\n        if (typeof parameter === 'string') {\n            noteStruct = { duration: parameter };\n        }\n        else if (typeof parameter === 'object') {\n            noteStruct = parameter;\n        }\n        else {\n            throw new RuntimeError('BadArguments', ERROR_MSG);\n        }\n        super(noteStruct);\n        this.setWidth(0);\n    }\n    isRest() {\n        return true;\n    }\n    setStave(stave) {\n        super.setStave(stave);\n        return this;\n    }\n    addToModifierContext(mc) {\n        return this;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        this.setRendered();\n        for (let i = 0; i < this.modifiers.length; ++i) {\n            const modifier = this.modifiers[i];\n            if (isAnnotation(modifier)) {\n                modifier.setContext(this.getContext());\n                modifier.drawWithStyle();\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,SAAS,GAAG,sEAAsE;AACxF,OAAO,MAAMC,SAAS,SAASJ,aAAa,CAAC;EACzC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACAC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,EAAE;MACZ,MAAM,IAAIL,YAAY,CAAC,cAAc,EAAEC,SAAS,CAAC;IACrD;IACA,IAAIK,UAAU;IACd,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;MAC/BC,UAAU,GAAG;QAAEC,QAAQ,EAAEF;MAAU,CAAC;IACxC,CAAC,MACI,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;MACpCC,UAAU,GAAGD,SAAS;IAC1B,CAAC,MACI;MACD,MAAM,IAAIL,YAAY,CAAC,cAAc,EAAEC,SAAS,CAAC;IACrD;IACA,KAAK,CAACK,UAAU,CAAC;IACjB,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;EACpB;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI;EACf;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,KAAK,CAACD,QAAQ,CAACC,KAAK,CAAC;IACrB,OAAO,IAAI;EACf;EACAC,oBAAoBA,CAACC,EAAE,EAAE;IACrB,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE,EAAEF,CAAC,EAAE;MAC5C,MAAMG,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACD,CAAC,CAAC;MAClC,IAAInB,YAAY,CAACsB,QAAQ,CAAC,EAAE;QACxBA,QAAQ,CAACC,UAAU,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC;QACtCF,QAAQ,CAACG,aAAa,CAAC,CAAC;MAC5B;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}