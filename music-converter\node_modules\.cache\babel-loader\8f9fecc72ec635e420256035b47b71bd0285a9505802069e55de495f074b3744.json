{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VexTabBlock({\n  source\n}) {\n  _s();\n  useEffect(() => {\n    console.log('VexTabBlock source: ', source);\n    if (window.vextab && window.vextab.Div) {\n      document.querySelectorAll('vextab-auto').forEach(div => {\n        div.textContent = '';\n        div.textContent = source;\n        new window.vextab.Div(div);\n      });\n    }\n  }, [source]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    children: source\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 10\n  }, this);\n}\n_s(VexTabBlock, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = VexTabBlock;\nfunction Converter() {\n  _s2();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'rgba(68, 25, 240, 0.08)',\n      display: 'grid',\n      gridTemplateRows: '1fr auto auto'\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'white'\n    },\n    sideButton: {\n      backgroundColor: 'white',\n      width: '4rem',\n      height: '4rem',\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#5c14ba',\n      border: '2px solid #9a62e3',\n      borderRadius: '1rem',\n      cursor: 'pointer',\n      margin: 'auto'\n    },\n    sideButtonActive: {\n      backgroundColor: '#9a62e3',\n      color: 'white',\n      border: '2px solid #7f3bd9',\n      boxShadow: '0 4px 8px rgba(127, 59, 217, 0.5)'\n    }\n  };\n\n  // VEXTAB SETUP\n  const defaultSetup = 'tabstave notation=true tablature=false';\n  const defaultNotes = ['G/4'];\n  const [key, setKey] = useState('G');\n  const [time, setTime] = useState('4/4');\n  const [notes, setNotes] = useState(defaultNotes);\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${time}\nnotes ${notes.join(' ')}`);\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  const ACCIDENTALS = ['♯', '♮', '♭'];\n  const VEXTAB_ACCIDENTALS = {\n    '♯': '#',\n    '♮': 'n',\n    '♭': 'b',\n    '': ''\n  };\n\n  // CURRENT NOTE\n  const [curOctave, setCurOctave] = useState(4);\n  const [curStep, setCurStep] = useState('A');\n  const [curAccident, setCurAccident] = useState('♮');\n  const [curNote, setCurNote] = useState();\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyItems: 'center',\n      paddingTop: '4rem',\n      paddingBottom: '4rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vexflow/releases/vexflow-min.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/vextab-core.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: '2px solid #9a62e3',\n          borderRadius: '2rem',\n          background: 'rgba(120, 25, 240, 0.06)',\n          display: 'flex',\n          flexDirection: 'column',\n          margin: '2rem 4rem',\n          padding: '2rem',\n          gap: '2rem',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '6rem 1fr 6rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignContent: 'center',\n                gap: '0.5rem',\n                padding: '1rem 0rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(curOctave + 1),\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                children: curOctave\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(curOctave - 1),\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateRows: 'auto 1fr'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(7, 1fr)',\n                  padding: '1rem'\n                },\n                children: NOTES.map(note => /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    ...styles.sideButton,\n                    ...(curStep === note ? styles.sideButtonActive : {})\n                  },\n                  onClick: () => setCurStep(note),\n                  children: note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  border: '2px solid red',\n                  padding: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(VexTabBlock, {\n                  source: rawVex\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignContent: 'center',\n                gap: '0.5rem',\n                padding: '1rem 0rem'\n              },\n              children: ACCIDENTALS.map(acc => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  ...styles.sideButton,\n                  ...(curAccident === acc ? styles.sideButtonActive : {})\n                },\n                onClick: curAccident === acc ? () => setCurAccident('') : () => setCurAccident(acc),\n                children: acc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Add or remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vextab-auto\",\n            editor: \"true\",\n            onChange: e => setRawVex(e.target.value),\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: rawVex,\n            style: {\n              width: Math.min(window.innerWidth * 0.7, 600),\n              margin: '1rem'\n            },\n            onChange: e => setRawVex(e.target.value),\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vextab-auto\",\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}\n_s2(Converter, \"YWt9exEPjwnY3eYtsPR9CsxEh80=\");\n_c2 = Converter;\nexport default Converter;\nvar _c, _c2;\n$RefreshReg$(_c, \"VexTabBlock\");\n$RefreshReg$(_c2, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "VexTabBlock", "source", "_s", "console", "log", "window", "vextab", "Div", "document", "querySelectorAll", "for<PERSON>ach", "div", "textContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Converter", "_s2", "styles", "graphicDisplay", "width", "Math", "min", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "sideButton", "backgroundColor", "height", "fontSize", "fontWeight", "color", "cursor", "margin", "sideButtonActive", "boxShadow", "defaultSetup", "defaultNotes", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "setRawVex", "join", "NOTES", "ACCIDENTALS", "VEXTAB_ACCIDENTALS", "curOctave", "setCurOctave", "curStep", "setCurStep", "curAccident", "setCurAccident", "cur<PERSON><PERSON>", "setCurNote", "style", "flexDirection", "alignItems", "justifyItems", "paddingTop", "paddingBottom", "src", "padding", "gap", "gridTemplateColumns", "justifyContent", "align<PERSON><PERSON><PERSON>", "onClick", "map", "note", "acc", "editor", "onChange", "e", "target", "value", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["\r\nimport React, { useEffect, useState, useRef } from 'react';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nfunction VexTabBlock({ source }) {\r\n  useEffect(() => {\r\n    console.log('VexTabBlock source: ', source);\r\n    if (window.vextab && window.vextab.Div) {\r\n      document.querySelectorAll('vextab-auto').forEach(div => {\r\n        div.textContent = '';\r\n        div.textContent = source;\r\n        new window.vextab.Div(div);\r\n      });\r\n    }\r\n  }, [source]);\r\n\r\n  return <div className=\"vextab-auto\">{source}</div>;\r\n}\r\n\r\nfunction Converter() {\r\n\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'rgba(68, 25, 240, 0.08)',\r\n      display: 'grid',\r\n      gridTemplateRows: '1fr auto auto',\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'white',\r\n    },\r\n    sideButton: {\r\n      backgroundColor: 'white',\r\n      width: '4rem',\r\n      height: '4rem',\r\n      fontSize: '1.2rem',\r\n      fontWeight: 'bold',\r\n      color: '#5c14ba',\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '1rem',\r\n      cursor: 'pointer',\r\n      margin: 'auto',\r\n    },\r\n    sideButtonActive: {\r\n      backgroundColor: '#9a62e3',\r\n      color: 'white',\r\n      border: '2px solid #7f3bd9',\r\n      boxShadow: '0 4px 8px rgba(127, 59, 217, 0.5)',\r\n    },\r\n  }\r\n\r\n  // VEXTAB SETUP\r\n  const defaultSetup = 'tabstave notation=true tablature=false';\r\n  const defaultNotes = ['G/4'];\r\n  const [key, setKey] = useState('G');\r\n  const [time, setTime] = useState('4/4');\r\n  const [notes, setNotes] = useState(defaultNotes);\r\n  const [rawVex, setRawVex] = useState(\r\n`${defaultSetup} key=${key} time=${time}\r\nnotes ${notes.join(' ')}`\r\n  );\r\n\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n  const ACCIDENTALS = ['♯', '♮', '♭'];\r\n  const VEXTAB_ACCIDENTALS = { '♯': '#', '♮': 'n', '♭': 'b', '': '' };\r\n\r\n  // CURRENT NOTE\r\n  const [curOctave, setCurOctave] = useState(4);\r\n  const [curStep, setCurStep] = useState('A');\r\n  const [curAccident, setCurAccident] = useState('♮');\r\n  const [curNote, setCurNote] = useState();\r\n\r\n\r\n\r\n  return (\r\n    <main\r\n      style={{\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        alignItems: 'center',\r\n        justifyItems: 'center',\r\n        paddingTop: '4rem',\r\n        paddingBottom: '4rem',\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vexflow/releases/vexflow-min.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/vextab-core.prod.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section style={{\r\n          border: '2px solid #9a62e3',\r\n          borderRadius: '2rem',\r\n          background: 'rgba(120, 25, 240, 0.06)',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          margin: '2rem 4rem',\r\n          padding: '2rem',\r\n          gap: '2rem',\r\n          alignItems: 'center',\r\n        }}>\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: 'grid',\r\n                gridTemplateColumns: '6rem 1fr 6rem',\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  justifyContent: 'center',\r\n                  alignContent: 'center',\r\n                  gap: '0.5rem',\r\n                  padding: '1rem 0rem',\r\n                }}>\r\n                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave + 1)}>↑</button>\r\n                <button style={styles.sideButton}>{curOctave}</button>\r\n                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave - 1)}>↓</button>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: 'grid',\r\n                  gridTemplateRows: 'auto 1fr',\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: 'grid',\r\n                    gridTemplateColumns: 'repeat(7, 1fr)',\r\n                    padding: '1rem',\r\n                  }}>\r\n                  {NOTES.map(note => (\r\n                    <button style={{...styles.sideButton, ...(curStep === note ? styles.sideButtonActive : {})}}\r\n                    onClick={() => setCurStep(note)}>\r\n                      {note}\r\n                    </button>\r\n\r\n                  ))}\r\n                </div>\r\n\r\n                <div style={{border: '2px solid red', padding: '1rem'}}>\r\n                  <VexTabBlock source={rawVex} />\r\n                </div>\r\n                \r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  justifyContent: 'center',\r\n                  alignContent: 'center',\r\n                  gap: '0.5rem',\r\n                  padding: '1rem 0rem',\r\n                }}>\r\n                {ACCIDENTALS.map(acc => (\r\n                  <button style={{...styles.sideButton, ...(curAccident === acc ? styles.sideButtonActive : {})}} onClick={(curAccident === acc) ? () => setCurAccident('') : () => setCurAccident(acc)}\r\n                  >\r\n                    {acc}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Add or remove</h3>\r\n            </div>\r\n\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <div className=\"vextab-auto\" editor=\"true\" onChange={(e) => setRawVex(e.target.value)}>{rawVex}</div>\r\n            <textarea\r\n              value={rawVex}\r\n              style={{width: Math.min(window.innerWidth * 0.7, 600), margin: '1rem'}}\r\n              onChange={(e) => setRawVex(e.target.value)}\r\n            >\r\n              {rawVex}\r\n            </textarea>\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>Output</h3>\r\n          <div style={styles.musicDisplay}>\r\n            <div className='vextab-auto'>\r\n              {rawVex}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";;;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,WAAWA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC/BR,SAAS,CAAC,MAAM;IACdS,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,MAAM,CAAC;IAC3C,IAAII,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;MACtCC,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAACC,OAAO,CAACC,GAAG,IAAI;QACtDA,GAAG,CAACC,WAAW,GAAG,EAAE;QACpBD,GAAG,CAACC,WAAW,GAAGX,MAAM;QACxB,IAAII,MAAM,CAACC,MAAM,CAACC,GAAG,CAACI,GAAG,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,oBAAOF,OAAA;IAAKc,SAAS,EAAC,aAAa;IAAAC,QAAA,EAAEb;EAAM;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AACpD;AAAChB,EAAA,CAbQF,WAAW;AAAAmB,EAAA,GAAXnB,WAAW;AAepB,SAASoB,SAASA,CAAA,EAAG;EAAAC,GAAA;EAEnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACrB,MAAM,CAACsB,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZT,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACrB,MAAM,CAACsB,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,UAAU,EAAE;MACVC,eAAe,EAAE,OAAO;MACxBX,KAAK,EAAE,MAAM;MACbY,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBW,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE;MAChBP,eAAe,EAAE,SAAS;MAC1BI,KAAK,EAAE,OAAO;MACdX,MAAM,EAAE,mBAAmB;MAC3Be,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,wCAAwC;EAC7D,MAAMC,YAAY,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGpD,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAACkD,YAAY,CAAC;EAChD,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CACtC,GAAGiD,YAAY,QAAQE,GAAG,SAASE,IAAI;AACvC,QAAQE,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EACrB,CAAC;;EAGD;EACA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,MAAMC,kBAAkB,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,EAAE,EAAE;EAAG,CAAC;;EAEnE;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,CAAC;EAIxC,oBACEI,OAAA;IACEmE,KAAK,EAAE;MACLnC,OAAO,EAAE,MAAM;MACfoC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACjB,CAAE;IAAAzD,QAAA,gBAEFf,OAAA,CAACF,MAAM;MAAAiB,QAAA,gBACLf,OAAA;QAAQyE,GAAG,EAAC;MAAmD;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACzEnB,OAAA;QAAQyE,GAAG,EAAC;MAAuD;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC7EnB,OAAA;QAAQyE,GAAG,EAAC;MAA+C;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGTnB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAASmE,KAAK,EAAE;UACdtC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACfoC,aAAa,EAAE,QAAQ;UACvB1B,MAAM,EAAE,WAAW;UACnBgC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXN,UAAU,EAAE;QACd,CAAE;QAAAtD,QAAA,gBACAf,OAAA;UAAAe,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGdnB,OAAA;UAAKmE,KAAK,EAAE5C,MAAM,CAACC,cAAe;UAAAT,QAAA,gBAChCf,OAAA;YACEmE,KAAK,EAAE;cACLnC,OAAO,EAAE,MAAM;cACf4C,mBAAmB,EAAE;YACvB,CAAE;YAAA7D,QAAA,gBAEFf,OAAA;cACEmE,KAAK,EAAE;gBACLnC,OAAO,EAAE,MAAM;gBACfoC,aAAa,EAAE,QAAQ;gBACvBS,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAA3D,QAAA,gBACFf,OAAA;gBAAQmE,KAAK,EAAE5C,MAAM,CAACY,UAAW;gBAAC4C,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACD,SAAS,GAAG,CAAC,CAAE;gBAAA5C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxFnB,OAAA;gBAAQmE,KAAK,EAAE5C,MAAM,CAACY,UAAW;gBAAApB,QAAA,EAAE4C;cAAS;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACtDnB,OAAA;gBAAQmE,KAAK,EAAE5C,MAAM,CAACY,UAAW;gBAAC4C,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACD,SAAS,GAAG,CAAC,CAAE;gBAAA5C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENnB,OAAA;cACEmE,KAAK,EAAE;gBACLnC,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAAlB,QAAA,gBAEFf,OAAA;gBACEmE,KAAK,EAAE;kBACLnC,OAAO,EAAE,MAAM;kBACf4C,mBAAmB,EAAE,gBAAgB;kBACrCF,OAAO,EAAE;gBACX,CAAE;gBAAA3D,QAAA,EACDyC,KAAK,CAACwB,GAAG,CAACC,IAAI,iBACbjF,OAAA;kBAAQmE,KAAK,EAAE;oBAAC,GAAG5C,MAAM,CAACY,UAAU;oBAAE,IAAI0B,OAAO,KAAKoB,IAAI,GAAG1D,MAAM,CAACoB,gBAAgB,GAAG,CAAC,CAAC;kBAAC,CAAE;kBAC5FoC,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACmB,IAAI,CAAE;kBAAAlE,QAAA,EAC7BkE;gBAAI;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAET;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnB,OAAA;gBAAKmE,KAAK,EAAE;kBAACtC,MAAM,EAAE,eAAe;kBAAE6C,OAAO,EAAE;gBAAM,CAAE;gBAAA3D,QAAA,eACrDf,OAAA,CAACC,WAAW;kBAACC,MAAM,EAAEmD;gBAAO;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH,CAAC,eAENnB,OAAA;cACEmE,KAAK,EAAE;gBACLnC,OAAO,EAAE,MAAM;gBACfoC,aAAa,EAAE,QAAQ;gBACvBS,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAA3D,QAAA,EACD0C,WAAW,CAACuB,GAAG,CAACE,GAAG,iBAClBlF,OAAA;gBAAQmE,KAAK,EAAE;kBAAC,GAAG5C,MAAM,CAACY,UAAU;kBAAE,IAAI4B,WAAW,KAAKmB,GAAG,GAAG3D,MAAM,CAACoB,gBAAgB,GAAG,CAAC,CAAC;gBAAC,CAAE;gBAACoC,OAAO,EAAGhB,WAAW,KAAKmB,GAAG,GAAI,MAAMlB,cAAc,CAAC,EAAE,CAAC,GAAG,MAAMA,cAAc,CAACkB,GAAG,CAAE;gBAAAnE,QAAA,EAEnLmE;cAAG;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,eAENnB,OAAA;YAAAe,QAAA,eACEf,OAAA;cAAAe,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAENnB,OAAA;YAAAe,QAAA,eACEf,OAAA;cAAAe,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,eAINnB,OAAA;UAAKmE,KAAK,EAAE5C,MAAM,CAACW,YAAa;UAAAnB,QAAA,gBAC9Bf,OAAA;YAAKc,SAAS,EAAC,aAAa;YAACqE,MAAM,EAAC,MAAM;YAACC,QAAQ,EAAGC,CAAC,IAAK/B,SAAS,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAAAxE,QAAA,EAAEsC;UAAM;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGnB,OAAA;YACEuF,KAAK,EAAElC,MAAO;YACdc,KAAK,EAAE;cAAC1C,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACrB,MAAM,CAACsB,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;cAAEc,MAAM,EAAE;YAAM,CAAE;YACvE0C,QAAQ,EAAGC,CAAC,IAAK/B,SAAS,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;YAAAxE,QAAA,EAE1CsC;UAAM;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNnB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAAe,QAAA,gBACEf,OAAA;UAAAe,QAAA,EAAI;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfnB,OAAA;UAAKmE,KAAK,EAAE5C,MAAM,CAACW,YAAa;UAAAnB,QAAA,eAC9Bf,OAAA;YAAKc,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBsC;UAAM;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACG,GAAA,CA3MQD,SAAS;AAAAmE,GAAA,GAATnE,SAAS;AA6MlB,eAAeA,SAAS;AAAC,IAAAD,EAAA,EAAAoE,GAAA;AAAAC,YAAA,CAAArE,EAAA;AAAAqE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}