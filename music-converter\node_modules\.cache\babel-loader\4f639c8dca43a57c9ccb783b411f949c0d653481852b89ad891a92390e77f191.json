{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // VEXTAB SETUP\n\n  const defaultSetup = 'tabstave notation=true tablature=false';\n  const defaultNotes = ['G/4'];\n  const [key, setKey] = useState('G');\n  const [time, setTime] = useState('4/4');\n  const [notes, setNotes] = useState(defaultNotes);\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${key} notes=${notes.join(' ')}`);\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"SAY SOMETHING!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: 420,\n            marginLeft: auto,\n            marginRight,\n            auto\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"vextab-auto\",\n            width: \"800px\",\n            scale: \"1.0\",\n            editor: \"true\",\n            \"editor-height\": \"80px\",\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"tFTTIixf0Xce2ns0pcl8O9PprNk=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "Converter", "_s", "defaultSetup", "defaultNotes", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "setRawVex", "join", "NOTES", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "marginLeft", "auto", "marginRight", "class", "scale", "editor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["import React, { useEffect, useState} from 'react';\r\n\r\nfunction Converter() {\r\n\r\n  // VEXTAB SETUP\r\n\r\n  const defaultSetup = 'tabstave notation=true tablature=false';\r\n  const defaultNotes = ['G/4'];\r\n  const [key, setKey] = useState('G');\r\n  const [time, setTime] = useState('4/4');\r\n  const [notes, setNotes] = useState(defaultNotes);\r\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${key} notes=${notes.join(' ')}`);\r\n\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n\r\n  return (\r\n    <main>\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>SAY SOMETHING!</h3>\r\n          <div style={{width:420, marginLeft: auto, marginRight, auto}}>\r\n            <div class=\"vextab-auto\" width=\"800px\" scale=\"1.0\" editor=\"true\" editor-height=\"80px\">{rawVex}</div>\r\n          </div>\r\n          \r\n\r\n\r\n\r\n        </section>\r\n        \r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>Output</h3>\r\n        </section>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAEnB;;EAEA,MAAMC,YAAY,GAAG,wCAAwC;EAC7D,MAAMC,YAAY,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGR,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACS,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAACM,YAAY,CAAC;EAChD,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,GAAGK,YAAY,QAAQE,GAAG,SAASA,GAAG,UAAUI,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;;EAGvG;EACA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAEjD,oBACEd,OAAA;IAAAe,QAAA,gBAEEf,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAAe,QAAA,gBACEf,OAAA;UAAAe,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBnB,OAAA;UAAKoB,KAAK,EAAE;YAACC,KAAK,EAAC,GAAG;YAAEC,UAAU,EAAEC,IAAI;YAAEC,WAAW;YAAED;UAAI,CAAE;UAAAR,QAAA,eAC3Df,OAAA;YAAKyB,KAAK,EAAC,aAAa;YAACJ,KAAK,EAAC,OAAO;YAACK,KAAK,EAAC,KAAK;YAACC,MAAM,EAAC,MAAM;YAAC,iBAAc,MAAM;YAAAZ,QAAA,EAAEJ;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CAAC,eAGNnB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAAe,QAAA,eACEf,OAAA;UAAAe,QAAA,EAAI;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACjB,EAAA,CAzCQD,SAAS;AAAA2B,EAAA,GAAT3B,SAAS;AA2ClB,eAAeA,SAAS;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}