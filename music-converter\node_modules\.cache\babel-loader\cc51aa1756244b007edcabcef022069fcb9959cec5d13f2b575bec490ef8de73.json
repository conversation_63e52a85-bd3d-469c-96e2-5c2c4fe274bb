{"ast": null, "code": "import { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nexport var BarlineType;\n(function (BarlineType) {\n  BarlineType[BarlineType[\"SINGLE\"] = 1] = \"SINGLE\";\n  BarlineType[BarlineType[\"DOUBLE\"] = 2] = \"DOUBLE\";\n  BarlineType[BarlineType[\"END\"] = 3] = \"END\";\n  BarlineType[BarlineType[\"REPEAT_BEGIN\"] = 4] = \"REPEAT_BEGIN\";\n  BarlineType[BarlineType[\"REPEAT_END\"] = 5] = \"REPEAT_END\";\n  BarlineType[BarlineType[\"REPEAT_BOTH\"] = 6] = \"REPEAT_BOTH\";\n  BarlineType[BarlineType[\"NONE\"] = 7] = \"NONE\";\n})(BarlineType || (BarlineType = {}));\nexport class Barline extends StaveModifier {\n  static get CATEGORY() {\n    return \"Barline\";\n  }\n  static get type() {\n    return BarlineType;\n  }\n  static get typeString() {\n    return {\n      single: BarlineType.SINGLE,\n      double: BarlineType.DOUBLE,\n      end: BarlineType.END,\n      repeatBegin: BarlineType.REPEAT_BEGIN,\n      repeatEnd: BarlineType.REPEAT_END,\n      repeatBoth: BarlineType.REPEAT_BOTH,\n      none: BarlineType.NONE\n    };\n  }\n  constructor(type) {\n    super();\n    this.thickness = Tables.STAVE_LINE_THICKNESS;\n    const TYPE = BarlineType;\n    this.widths = {};\n    this.widths[TYPE.SINGLE] = 5;\n    this.widths[TYPE.DOUBLE] = 5;\n    this.widths[TYPE.END] = 5;\n    this.widths[TYPE.REPEAT_BEGIN] = 5;\n    this.widths[TYPE.REPEAT_END] = 5;\n    this.widths[TYPE.REPEAT_BOTH] = 5;\n    this.widths[TYPE.NONE] = 5;\n    this.paddings = {};\n    this.paddings[TYPE.SINGLE] = 0;\n    this.paddings[TYPE.DOUBLE] = 0;\n    this.paddings[TYPE.END] = 0;\n    this.paddings[TYPE.REPEAT_BEGIN] = 15;\n    this.paddings[TYPE.REPEAT_END] = 15;\n    this.paddings[TYPE.REPEAT_BOTH] = 15;\n    this.paddings[TYPE.NONE] = 0;\n    this.layoutMetricsMap = {};\n    this.layoutMetricsMap[TYPE.SINGLE] = {\n      xMin: 0,\n      xMax: 1,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.DOUBLE] = {\n      xMin: -3,\n      xMax: 1,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.END] = {\n      xMin: -5,\n      xMax: 1,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.REPEAT_END] = {\n      xMin: -10,\n      xMax: 1,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.REPEAT_BEGIN] = {\n      xMin: -2,\n      xMax: 10,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.REPEAT_BOTH] = {\n      xMin: -10,\n      xMax: 10,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.layoutMetricsMap[TYPE.NONE] = {\n      xMin: 0,\n      xMax: 0,\n      paddingLeft: 5,\n      paddingRight: 5\n    };\n    this.setPosition(StaveModifierPosition.BEGIN);\n    this.setType(type);\n  }\n  getType() {\n    return this.type;\n  }\n  setType(type) {\n    this.type = typeof type === 'string' ? Barline.typeString[type] : type;\n    this.setWidth(this.widths[this.type]);\n    this.setPadding(this.paddings[this.type]);\n    this.setLayoutMetrics(this.layoutMetricsMap[this.type]);\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    ctx.openGroup('stavebarline', this.getAttribute('id'));\n    switch (this.type) {\n      case BarlineType.SINGLE:\n        this.drawVerticalBar(stave, this.x, false);\n        break;\n      case BarlineType.DOUBLE:\n        this.drawVerticalBar(stave, this.x, true);\n        break;\n      case BarlineType.END:\n        this.drawVerticalEndBar(stave, this.x);\n        break;\n      case BarlineType.REPEAT_BEGIN:\n        this.drawRepeatBar(stave, this.x, true);\n        if (stave.getX() !== this.x) {\n          this.drawVerticalBar(stave, stave.getX());\n        }\n        break;\n      case BarlineType.REPEAT_END:\n        this.drawRepeatBar(stave, this.x, false);\n        break;\n      case BarlineType.REPEAT_BOTH:\n        this.drawRepeatBar(stave, this.x, false);\n        this.drawRepeatBar(stave, this.x, true);\n        break;\n      default:\n        break;\n    }\n    ctx.closeGroup();\n  }\n  drawVerticalBar(stave, x, doubleBar) {\n    const staveCtx = stave.checkContext();\n    const topY = stave.getTopLineTopY();\n    const botY = stave.getBottomLineBottomY();\n    if (doubleBar) {\n      staveCtx.fillRect(x - 3, topY, 1, botY - topY);\n    }\n    staveCtx.fillRect(x, topY, 1, botY - topY);\n  }\n  drawVerticalEndBar(stave, x) {\n    const staveCtx = stave.checkContext();\n    const topY = stave.getTopLineTopY();\n    const botY = stave.getBottomLineBottomY();\n    staveCtx.fillRect(x - 5, topY, 1, botY - topY);\n    staveCtx.fillRect(x - 2, topY, 3, botY - topY);\n  }\n  drawRepeatBar(stave, x, begin) {\n    const staveCtx = stave.checkContext();\n    const topY = stave.getTopLineTopY();\n    const botY = stave.getBottomLineBottomY();\n    let xShift = 3;\n    if (!begin) {\n      xShift = -5;\n    }\n    staveCtx.fillRect(x + xShift, topY, 1, botY - topY);\n    staveCtx.fillRect(x - 2, topY, 3, botY - topY);\n    const dotRadius = 2;\n    if (begin) {\n      xShift += 4;\n    } else {\n      xShift -= 4;\n    }\n    const dotX = x + xShift + dotRadius / 2;\n    let yOffset = (stave.getNumLines() - 1) * stave.getSpacingBetweenLines();\n    yOffset = yOffset / 2 - stave.getSpacingBetweenLines() / 2;\n    let dotY = topY + yOffset + dotRadius / 2;\n    staveCtx.beginPath();\n    staveCtx.arc(dotX, dotY, dotRadius, 0, Math.PI * 2, false);\n    staveCtx.fill();\n    dotY += stave.getSpacingBetweenLines();\n    staveCtx.beginPath();\n    staveCtx.arc(dotX, dotY, dotRadius, 0, Math.PI * 2, false);\n    staveCtx.fill();\n  }\n}", "map": {"version": 3, "names": ["StaveModifier", "StaveModifierPosition", "Tables", "BarlineType", "Barline", "CATEGORY", "type", "typeString", "single", "SINGLE", "double", "DOUBLE", "end", "END", "repeatBegin", "REPEAT_BEGIN", "repeatEnd", "REPEAT_END", "repeatBoth", "REPEAT_BOTH", "none", "NONE", "constructor", "thickness", "STAVE_LINE_THICKNESS", "TYPE", "widths", "paddings", "layoutMetricsMap", "xMin", "xMax", "paddingLeft", "paddingRight", "setPosition", "BEGIN", "setType", "getType", "<PERSON><PERSON><PERSON><PERSON>", "setPadding", "setLayoutMetrics", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "drawVerticalBar", "x", "drawVerticalEndBar", "drawRepeatBar", "getX", "closeGroup", "doubleBar", "staveCtx", "topY", "getTopLineTopY", "botY", "getBottomLineBottomY", "fillRect", "begin", "xShift", "dotRadius", "dotX", "yOffset", "getNumLines", "getSpacingBetweenLines", "dotY", "beginPath", "arc", "Math", "PI", "fill"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavebarline.js"], "sourcesContent": ["import { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nexport var BarlineType;\n(function (BarlineType) {\n    BarlineType[BarlineType[\"SINGLE\"] = 1] = \"SINGLE\";\n    BarlineType[BarlineType[\"DOUBLE\"] = 2] = \"DOUBLE\";\n    BarlineType[BarlineType[\"END\"] = 3] = \"END\";\n    BarlineType[BarlineType[\"REPEAT_BEGIN\"] = 4] = \"REPEAT_BEGIN\";\n    BarlineType[BarlineType[\"REPEAT_END\"] = 5] = \"REPEAT_END\";\n    BarlineType[BarlineType[\"REPEAT_BOTH\"] = 6] = \"REPEAT_BOTH\";\n    BarlineType[BarlineType[\"NONE\"] = 7] = \"NONE\";\n})(BarlineType || (BarlineType = {}));\nexport class Barline extends StaveModifier {\n    static get CATEGORY() {\n        return \"Barline\";\n    }\n    static get type() {\n        return BarlineType;\n    }\n    static get typeString() {\n        return {\n            single: BarlineType.SINGLE,\n            double: BarlineType.DOUBLE,\n            end: BarlineType.END,\n            repeatBegin: BarlineType.REPEAT_BEGIN,\n            repeatEnd: BarlineType.REPEAT_END,\n            repeatBoth: BarlineType.REPEAT_BOTH,\n            none: BarlineType.NONE,\n        };\n    }\n    constructor(type) {\n        super();\n        this.thickness = Tables.STAVE_LINE_THICKNESS;\n        const TYPE = BarlineType;\n        this.widths = {};\n        this.widths[TYPE.SINGLE] = 5;\n        this.widths[TYPE.DOUBLE] = 5;\n        this.widths[TYPE.END] = 5;\n        this.widths[TYPE.REPEAT_BEGIN] = 5;\n        this.widths[TYPE.REPEAT_END] = 5;\n        this.widths[TYPE.REPEAT_BOTH] = 5;\n        this.widths[TYPE.NONE] = 5;\n        this.paddings = {};\n        this.paddings[TYPE.SINGLE] = 0;\n        this.paddings[TYPE.DOUBLE] = 0;\n        this.paddings[TYPE.END] = 0;\n        this.paddings[TYPE.REPEAT_BEGIN] = 15;\n        this.paddings[TYPE.REPEAT_END] = 15;\n        this.paddings[TYPE.REPEAT_BOTH] = 15;\n        this.paddings[TYPE.NONE] = 0;\n        this.layoutMetricsMap = {};\n        this.layoutMetricsMap[TYPE.SINGLE] = {\n            xMin: 0,\n            xMax: 1,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.DOUBLE] = {\n            xMin: -3,\n            xMax: 1,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.END] = {\n            xMin: -5,\n            xMax: 1,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.REPEAT_END] = {\n            xMin: -10,\n            xMax: 1,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.REPEAT_BEGIN] = {\n            xMin: -2,\n            xMax: 10,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.REPEAT_BOTH] = {\n            xMin: -10,\n            xMax: 10,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.layoutMetricsMap[TYPE.NONE] = {\n            xMin: 0,\n            xMax: 0,\n            paddingLeft: 5,\n            paddingRight: 5,\n        };\n        this.setPosition(StaveModifierPosition.BEGIN);\n        this.setType(type);\n    }\n    getType() {\n        return this.type;\n    }\n    setType(type) {\n        this.type = typeof type === 'string' ? Barline.typeString[type] : type;\n        this.setWidth(this.widths[this.type]);\n        this.setPadding(this.paddings[this.type]);\n        this.setLayoutMetrics(this.layoutMetricsMap[this.type]);\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        ctx.openGroup('stavebarline', this.getAttribute('id'));\n        switch (this.type) {\n            case BarlineType.SINGLE:\n                this.drawVerticalBar(stave, this.x, false);\n                break;\n            case BarlineType.DOUBLE:\n                this.drawVerticalBar(stave, this.x, true);\n                break;\n            case BarlineType.END:\n                this.drawVerticalEndBar(stave, this.x);\n                break;\n            case BarlineType.REPEAT_BEGIN:\n                this.drawRepeatBar(stave, this.x, true);\n                if (stave.getX() !== this.x) {\n                    this.drawVerticalBar(stave, stave.getX());\n                }\n                break;\n            case BarlineType.REPEAT_END:\n                this.drawRepeatBar(stave, this.x, false);\n                break;\n            case BarlineType.REPEAT_BOTH:\n                this.drawRepeatBar(stave, this.x, false);\n                this.drawRepeatBar(stave, this.x, true);\n                break;\n            default:\n                break;\n        }\n        ctx.closeGroup();\n    }\n    drawVerticalBar(stave, x, doubleBar) {\n        const staveCtx = stave.checkContext();\n        const topY = stave.getTopLineTopY();\n        const botY = stave.getBottomLineBottomY();\n        if (doubleBar) {\n            staveCtx.fillRect(x - 3, topY, 1, botY - topY);\n        }\n        staveCtx.fillRect(x, topY, 1, botY - topY);\n    }\n    drawVerticalEndBar(stave, x) {\n        const staveCtx = stave.checkContext();\n        const topY = stave.getTopLineTopY();\n        const botY = stave.getBottomLineBottomY();\n        staveCtx.fillRect(x - 5, topY, 1, botY - topY);\n        staveCtx.fillRect(x - 2, topY, 3, botY - topY);\n    }\n    drawRepeatBar(stave, x, begin) {\n        const staveCtx = stave.checkContext();\n        const topY = stave.getTopLineTopY();\n        const botY = stave.getBottomLineBottomY();\n        let xShift = 3;\n        if (!begin) {\n            xShift = -5;\n        }\n        staveCtx.fillRect(x + xShift, topY, 1, botY - topY);\n        staveCtx.fillRect(x - 2, topY, 3, botY - topY);\n        const dotRadius = 2;\n        if (begin) {\n            xShift += 4;\n        }\n        else {\n            xShift -= 4;\n        }\n        const dotX = x + xShift + dotRadius / 2;\n        let yOffset = (stave.getNumLines() - 1) * stave.getSpacingBetweenLines();\n        yOffset = yOffset / 2 - stave.getSpacingBetweenLines() / 2;\n        let dotY = topY + yOffset + dotRadius / 2;\n        staveCtx.beginPath();\n        staveCtx.arc(dotX, dotY, dotRadius, 0, Math.PI * 2, false);\n        staveCtx.fill();\n        dotY += stave.getSpacingBetweenLines();\n        staveCtx.beginPath();\n        staveCtx.arc(dotX, dotY, dotRadius, 0, Math.PI * 2, false);\n        staveCtx.fill();\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAO,IAAIC,WAAW;AACtB,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjDA,WAAW,CAACA,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjDA,WAAW,CAACA,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC3CA,WAAW,CAACA,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC7DA,WAAW,CAACA,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACzDA,WAAW,CAACA,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC3DA,WAAW,CAACA,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACjD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,OAAO,MAAMC,OAAO,SAASJ,aAAa,CAAC;EACvC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACA,WAAWC,IAAIA,CAAA,EAAG;IACd,OAAOH,WAAW;EACtB;EACA,WAAWI,UAAUA,CAAA,EAAG;IACpB,OAAO;MACHC,MAAM,EAAEL,WAAW,CAACM,MAAM;MAC1BC,MAAM,EAAEP,WAAW,CAACQ,MAAM;MAC1BC,GAAG,EAAET,WAAW,CAACU,GAAG;MACpBC,WAAW,EAAEX,WAAW,CAACY,YAAY;MACrCC,SAAS,EAAEb,WAAW,CAACc,UAAU;MACjCC,UAAU,EAAEf,WAAW,CAACgB,WAAW;MACnCC,IAAI,EAAEjB,WAAW,CAACkB;IACtB,CAAC;EACL;EACAC,WAAWA,CAAChB,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACiB,SAAS,GAAGrB,MAAM,CAACsB,oBAAoB;IAC5C,MAAMC,IAAI,GAAGtB,WAAW;IACxB,IAAI,CAACuB,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACA,MAAM,CAACD,IAAI,CAAChB,MAAM,CAAC,GAAG,CAAC;IAC5B,IAAI,CAACiB,MAAM,CAACD,IAAI,CAACd,MAAM,CAAC,GAAG,CAAC;IAC5B,IAAI,CAACe,MAAM,CAACD,IAAI,CAACZ,GAAG,CAAC,GAAG,CAAC;IACzB,IAAI,CAACa,MAAM,CAACD,IAAI,CAACV,YAAY,CAAC,GAAG,CAAC;IAClC,IAAI,CAACW,MAAM,CAACD,IAAI,CAACR,UAAU,CAAC,GAAG,CAAC;IAChC,IAAI,CAACS,MAAM,CAACD,IAAI,CAACN,WAAW,CAAC,GAAG,CAAC;IACjC,IAAI,CAACO,MAAM,CAACD,IAAI,CAACJ,IAAI,CAAC,GAAG,CAAC;IAC1B,IAAI,CAACM,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACA,QAAQ,CAACF,IAAI,CAAChB,MAAM,CAAC,GAAG,CAAC;IAC9B,IAAI,CAACkB,QAAQ,CAACF,IAAI,CAACd,MAAM,CAAC,GAAG,CAAC;IAC9B,IAAI,CAACgB,QAAQ,CAACF,IAAI,CAACZ,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAI,CAACc,QAAQ,CAACF,IAAI,CAACV,YAAY,CAAC,GAAG,EAAE;IACrC,IAAI,CAACY,QAAQ,CAACF,IAAI,CAACR,UAAU,CAAC,GAAG,EAAE;IACnC,IAAI,CAACU,QAAQ,CAACF,IAAI,CAACN,WAAW,CAAC,GAAG,EAAE;IACpC,IAAI,CAACQ,QAAQ,CAACF,IAAI,CAACJ,IAAI,CAAC,GAAG,CAAC;IAC5B,IAAI,CAACO,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACA,gBAAgB,CAACH,IAAI,CAAChB,MAAM,CAAC,GAAG;MACjCoB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACd,MAAM,CAAC,GAAG;MACjCkB,IAAI,EAAE,CAAC,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACZ,GAAG,CAAC,GAAG;MAC9BgB,IAAI,EAAE,CAAC,CAAC;MACRC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACR,UAAU,CAAC,GAAG;MACrCY,IAAI,EAAE,CAAC,EAAE;MACTC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACV,YAAY,CAAC,GAAG;MACvCc,IAAI,EAAE,CAAC,CAAC;MACRC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACN,WAAW,CAAC,GAAG;MACtCU,IAAI,EAAE,CAAC,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACJ,gBAAgB,CAACH,IAAI,CAACJ,IAAI,CAAC,GAAG;MAC/BQ,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,CAAC;MACPC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACC,WAAW,CAAChC,qBAAqB,CAACiC,KAAK,CAAC;IAC7C,IAAI,CAACC,OAAO,CAAC7B,IAAI,CAAC;EACtB;EACA8B,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAAC9B,IAAI;EACpB;EACA6B,OAAOA,CAAC7B,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAG,OAAOA,IAAI,KAAK,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACD,IAAI,CAAC,GAAGA,IAAI;IACtE,IAAI,CAAC+B,QAAQ,CAAC,IAAI,CAACX,MAAM,CAAC,IAAI,CAACpB,IAAI,CAAC,CAAC;IACrC,IAAI,CAACgC,UAAU,CAAC,IAAI,CAACX,QAAQ,CAAC,IAAI,CAACrB,IAAI,CAAC,CAAC;IACzC,IAAI,CAACiC,gBAAgB,CAAC,IAAI,CAACX,gBAAgB,CAAC,IAAI,CAACtB,IAAI,CAAC,CAAC;IACvD,OAAO,IAAI;EACf;EACAkC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGF,KAAK,CAACG,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACtD,QAAQ,IAAI,CAACzC,IAAI;MACb,KAAKH,WAAW,CAACM,MAAM;QACnB,IAAI,CAACuC,eAAe,CAACP,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,KAAK,CAAC;QAC1C;MACJ,KAAK9C,WAAW,CAACQ,MAAM;QACnB,IAAI,CAACqC,eAAe,CAACP,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,IAAI,CAAC;QACzC;MACJ,KAAK9C,WAAW,CAACU,GAAG;QAChB,IAAI,CAACqC,kBAAkB,CAACT,KAAK,EAAE,IAAI,CAACQ,CAAC,CAAC;QACtC;MACJ,KAAK9C,WAAW,CAACY,YAAY;QACzB,IAAI,CAACoC,aAAa,CAACV,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,IAAI,CAAC;QACvC,IAAIR,KAAK,CAACW,IAAI,CAAC,CAAC,KAAK,IAAI,CAACH,CAAC,EAAE;UACzB,IAAI,CAACD,eAAe,CAACP,KAAK,EAAEA,KAAK,CAACW,IAAI,CAAC,CAAC,CAAC;QAC7C;QACA;MACJ,KAAKjD,WAAW,CAACc,UAAU;QACvB,IAAI,CAACkC,aAAa,CAACV,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,KAAK,CAAC;QACxC;MACJ,KAAK9C,WAAW,CAACgB,WAAW;QACxB,IAAI,CAACgC,aAAa,CAACV,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,KAAK,CAAC;QACxC,IAAI,CAACE,aAAa,CAACV,KAAK,EAAE,IAAI,CAACQ,CAAC,EAAE,IAAI,CAAC;QACvC;MACJ;QACI;IACR;IACAN,GAAG,CAACU,UAAU,CAAC,CAAC;EACpB;EACAL,eAAeA,CAACP,KAAK,EAAEQ,CAAC,EAAEK,SAAS,EAAE;IACjC,MAAMC,QAAQ,GAAGd,KAAK,CAACG,YAAY,CAAC,CAAC;IACrC,MAAMY,IAAI,GAAGf,KAAK,CAACgB,cAAc,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC,CAAC;IACzC,IAAIL,SAAS,EAAE;MACXC,QAAQ,CAACK,QAAQ,CAACX,CAAC,GAAG,CAAC,EAAEO,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;IAClD;IACAD,QAAQ,CAACK,QAAQ,CAACX,CAAC,EAAEO,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;EAC9C;EACAN,kBAAkBA,CAACT,KAAK,EAAEQ,CAAC,EAAE;IACzB,MAAMM,QAAQ,GAAGd,KAAK,CAACG,YAAY,CAAC,CAAC;IACrC,MAAMY,IAAI,GAAGf,KAAK,CAACgB,cAAc,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC,CAAC;IACzCJ,QAAQ,CAACK,QAAQ,CAACX,CAAC,GAAG,CAAC,EAAEO,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;IAC9CD,QAAQ,CAACK,QAAQ,CAACX,CAAC,GAAG,CAAC,EAAEO,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;EAClD;EACAL,aAAaA,CAACV,KAAK,EAAEQ,CAAC,EAAEY,KAAK,EAAE;IAC3B,MAAMN,QAAQ,GAAGd,KAAK,CAACG,YAAY,CAAC,CAAC;IACrC,MAAMY,IAAI,GAAGf,KAAK,CAACgB,cAAc,CAAC,CAAC;IACnC,MAAMC,IAAI,GAAGjB,KAAK,CAACkB,oBAAoB,CAAC,CAAC;IACzC,IAAIG,MAAM,GAAG,CAAC;IACd,IAAI,CAACD,KAAK,EAAE;MACRC,MAAM,GAAG,CAAC,CAAC;IACf;IACAP,QAAQ,CAACK,QAAQ,CAACX,CAAC,GAAGa,MAAM,EAAEN,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;IACnDD,QAAQ,CAACK,QAAQ,CAACX,CAAC,GAAG,CAAC,EAAEO,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;IAC9C,MAAMO,SAAS,GAAG,CAAC;IACnB,IAAIF,KAAK,EAAE;MACPC,MAAM,IAAI,CAAC;IACf,CAAC,MACI;MACDA,MAAM,IAAI,CAAC;IACf;IACA,MAAME,IAAI,GAAGf,CAAC,GAAGa,MAAM,GAAGC,SAAS,GAAG,CAAC;IACvC,IAAIE,OAAO,GAAG,CAACxB,KAAK,CAACyB,WAAW,CAAC,CAAC,GAAG,CAAC,IAAIzB,KAAK,CAAC0B,sBAAsB,CAAC,CAAC;IACxEF,OAAO,GAAGA,OAAO,GAAG,CAAC,GAAGxB,KAAK,CAAC0B,sBAAsB,CAAC,CAAC,GAAG,CAAC;IAC1D,IAAIC,IAAI,GAAGZ,IAAI,GAAGS,OAAO,GAAGF,SAAS,GAAG,CAAC;IACzCR,QAAQ,CAACc,SAAS,CAAC,CAAC;IACpBd,QAAQ,CAACe,GAAG,CAACN,IAAI,EAAEI,IAAI,EAAEL,SAAS,EAAE,CAAC,EAAEQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;IAC1DjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;IACfL,IAAI,IAAI3B,KAAK,CAAC0B,sBAAsB,CAAC,CAAC;IACtCZ,QAAQ,CAACc,SAAS,CAAC,CAAC;IACpBd,QAAQ,CAACe,GAAG,CAACN,IAAI,EAAEI,IAAI,EAAEL,SAAS,EAAE,CAAC,EAAEQ,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;IAC1DjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;EACnB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}