{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { RuntimeError } from './util.js';\nconst assertIsValidTimeSig = timeSpec => {\n  const numbers = timeSpec.split('/');\n  numbers.forEach(number => {\n    if (/^[0-9+\\-()]+$/.test(number) === false) {\n      throw new RuntimeError('BadTimeSignature', `Invalid time spec: ${timeSpec}. Must contain valid signatures.`);\n    }\n  });\n};\nexport class TimeSignature extends StaveModifier {\n  static get CATEGORY() {\n    return \"TimeSignature\";\n  }\n  constructor(timeSpec = '4/4', customPadding = 15, validateArgs = true) {\n    super();\n    this.timeSpec = '4/4';\n    this.line = 0;\n    this.isNumeric = true;\n    this.topStartX = 0;\n    this.botStartX = 0;\n    this.lineShift = 0;\n    this.topText = new Element();\n    this.botText = new Element();\n    this.validateArgs = validateArgs;\n    const padding = customPadding;\n    this.topLine = 1;\n    this.bottomLine = 3;\n    this.setPosition(StaveModifierPosition.BEGIN);\n    this.setTimeSig(timeSpec);\n    this.setPadding(padding);\n  }\n  static getTimeSigCode(key, smallSig = false) {\n    let code = Glyphs.null;\n    switch (key) {\n      case 'C':\n        code = Glyphs.timeSigCommon;\n        break;\n      case 'C|':\n        code = Glyphs.timeSigCutCommon;\n        break;\n      case '+':\n        code = smallSig ? Glyphs.timeSigPlusSmall : Glyphs.timeSigPlus;\n        break;\n      case '-':\n        code = Glyphs.timeSigMinus;\n        break;\n      case '(':\n        code = smallSig ? Glyphs.timeSigParensLeftSmall : Glyphs.timeSigParensLeft;\n        break;\n      case ')':\n        code = smallSig ? Glyphs.timeSigParensRightSmall : Glyphs.timeSigParensRight;\n        break;\n      default:\n        code = String.fromCodePoint(0xe080 + Number(key[0]));\n        break;\n    }\n    return code;\n  }\n  makeTimeSignatureGlyph(topDigits, botDigits) {\n    let txt = '';\n    let topWidth = 0;\n    let height = 0;\n    for (let i = 0; i < topDigits.length; ++i) {\n      const code = TimeSignature.getTimeSigCode(topDigits[i], botDigits.length > 0);\n      txt += code;\n    }\n    this.topText.setText(txt);\n    topWidth = this.topText.getWidth();\n    height = this.topText.getHeight();\n    let botWidth = 0;\n    txt = '';\n    for (let i = 0; i < botDigits.length; ++i) {\n      const code = TimeSignature.getTimeSigCode(botDigits[i], true);\n      txt += code;\n    }\n    this.botText.setText(txt);\n    botWidth = this.botText.getWidth();\n    height = Math.max(height, this.botText.getHeight());\n    this.lineShift = height > 30 ? 0.5 : 0;\n    this.width = Math.max(topWidth, botWidth);\n    this.topStartX = (this.width - topWidth) / 2.0;\n    this.botStartX = (this.width - botWidth) / 2.0;\n  }\n  setTimeSig(timeSpec) {\n    var _a, _b;\n    this.timeSpec = timeSpec;\n    if (timeSpec === 'C' || timeSpec === 'C|') {\n      const code = TimeSignature.getTimeSigCode(timeSpec);\n      this.line = 2;\n      this.text = code;\n      this.isNumeric = false;\n    } else {\n      if (this.validateArgs) {\n        assertIsValidTimeSig(timeSpec);\n      }\n      const parts = timeSpec.split('/');\n      this.line = 0;\n      this.isNumeric = true;\n      this.makeTimeSignatureGlyph((_a = parts[0]) !== null && _a !== void 0 ? _a : '', (_b = parts[1]) !== null && _b !== void 0 ? _b : '');\n    }\n    return this;\n  }\n  getTimeSpec() {\n    return this.timeSpec;\n  }\n  getLine() {\n    return this.line;\n  }\n  setLine(line) {\n    this.line = line;\n  }\n  getIsNumeric() {\n    return this.isNumeric;\n  }\n  setIsNumeric(isNumeric) {\n    this.isNumeric = isNumeric;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    ctx.openGroup('timesignature', this.getAttribute('id'));\n    this.drawAt(ctx, stave, this.x);\n    ctx.closeGroup();\n  }\n  drawAt(ctx, stave, x) {\n    this.setRendered();\n    if (this.isNumeric) {\n      let startX = x + this.topStartX;\n      let y = 0;\n      if (this.botText.getText().length > 0) y = stave.getYForLine(this.topLine - this.lineShift);else y = (stave.getYForLine(this.topLine) + stave.getYForLine(this.bottomLine)) / 2;\n      this.topText.renderText(ctx, startX, y);\n      startX = x + this.botStartX;\n      y = stave.getYForLine(this.bottomLine + this.lineShift);\n      this.botText.renderText(ctx, startX, y);\n    } else {\n      this.renderText(ctx, x - this.x, stave.getYForLine(this.line));\n    }\n  }\n}", "map": {"version": 3, "names": ["Element", "Glyphs", "StaveModifier", "StaveModifierPosition", "RuntimeError", "assertIsValidTimeSig", "timeSpec", "numbers", "split", "for<PERSON>ach", "number", "test", "TimeSignature", "CATEGORY", "constructor", "customPadding", "validateArgs", "line", "isNumeric", "topStartX", "botStartX", "lineShift", "topText", "botText", "padding", "topLine", "bottomLine", "setPosition", "BEGIN", "setTimeSig", "setPadding", "getTimeSigCode", "key", "smallSig", "code", "null", "timeSig<PERSON>ommon", "timeSigCutCommon", "timeSigPlusSmall", "timeSigPlus", "timeSigMinus", "timeSigParensLeftSmall", "timeSigParensLeft", "timeSigParensRightSmall", "timeSigParensRight", "String", "fromCodePoint", "Number", "makeTimeSignatureGlyph", "topDigits", "botDigits", "txt", "topWidth", "height", "i", "length", "setText", "getWidth", "getHeight", "botWidth", "Math", "max", "width", "_a", "_b", "text", "parts", "getTimeSpec", "getLine", "setLine", "getIsNumeric", "setIsNumeric", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "drawAt", "x", "closeGroup", "startX", "y", "getText", "getYForLine", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/timesignature.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { RuntimeError } from './util.js';\nconst assertIsValidTimeSig = (timeSpec) => {\n    const numbers = timeSpec.split('/');\n    numbers.forEach((number) => {\n        if (/^[0-9+\\-()]+$/.test(number) === false) {\n            throw new RuntimeError('BadTimeSignature', `Invalid time spec: ${timeSpec}. Must contain valid signatures.`);\n        }\n    });\n};\nexport class TimeSignature extends StaveModifier {\n    static get CATEGORY() {\n        return \"TimeSignature\";\n    }\n    constructor(timeSpec = '4/4', customPadding = 15, validateArgs = true) {\n        super();\n        this.timeSpec = '4/4';\n        this.line = 0;\n        this.isNumeric = true;\n        this.topStartX = 0;\n        this.botStartX = 0;\n        this.lineShift = 0;\n        this.topText = new Element();\n        this.botText = new Element();\n        this.validateArgs = validateArgs;\n        const padding = customPadding;\n        this.topLine = 1;\n        this.bottomLine = 3;\n        this.setPosition(StaveModifierPosition.BEGIN);\n        this.setTimeSig(timeSpec);\n        this.setPadding(padding);\n    }\n    static getTimeSigCode(key, smallSig = false) {\n        let code = Glyphs.null;\n        switch (key) {\n            case 'C':\n                code = Glyphs.timeSigCommon;\n                break;\n            case 'C|':\n                code = Glyphs.timeSigCutCommon;\n                break;\n            case '+':\n                code = smallSig ? Glyphs.timeSigPlusSmall : Glyphs.timeSigPlus;\n                break;\n            case '-':\n                code = Glyphs.timeSigMinus;\n                break;\n            case '(':\n                code = smallSig ? Glyphs.timeSigParensLeftSmall : Glyphs.timeSigParensLeft;\n                break;\n            case ')':\n                code = smallSig ? Glyphs.timeSigParensRightSmall : Glyphs.timeSigParensRight;\n                break;\n            default:\n                code = String.fromCodePoint(0xe080 + Number(key[0]));\n                break;\n        }\n        return code;\n    }\n    makeTimeSignatureGlyph(topDigits, botDigits) {\n        let txt = '';\n        let topWidth = 0;\n        let height = 0;\n        for (let i = 0; i < topDigits.length; ++i) {\n            const code = TimeSignature.getTimeSigCode(topDigits[i], botDigits.length > 0);\n            txt += code;\n        }\n        this.topText.setText(txt);\n        topWidth = this.topText.getWidth();\n        height = this.topText.getHeight();\n        let botWidth = 0;\n        txt = '';\n        for (let i = 0; i < botDigits.length; ++i) {\n            const code = TimeSignature.getTimeSigCode(botDigits[i], true);\n            txt += code;\n        }\n        this.botText.setText(txt);\n        botWidth = this.botText.getWidth();\n        height = Math.max(height, this.botText.getHeight());\n        this.lineShift = height > 30 ? 0.5 : 0;\n        this.width = Math.max(topWidth, botWidth);\n        this.topStartX = (this.width - topWidth) / 2.0;\n        this.botStartX = (this.width - botWidth) / 2.0;\n    }\n    setTimeSig(timeSpec) {\n        var _a, _b;\n        this.timeSpec = timeSpec;\n        if (timeSpec === 'C' || timeSpec === 'C|') {\n            const code = TimeSignature.getTimeSigCode(timeSpec);\n            this.line = 2;\n            this.text = code;\n            this.isNumeric = false;\n        }\n        else {\n            if (this.validateArgs) {\n                assertIsValidTimeSig(timeSpec);\n            }\n            const parts = timeSpec.split('/');\n            this.line = 0;\n            this.isNumeric = true;\n            this.makeTimeSignatureGlyph((_a = parts[0]) !== null && _a !== void 0 ? _a : '', (_b = parts[1]) !== null && _b !== void 0 ? _b : '');\n        }\n        return this;\n    }\n    getTimeSpec() {\n        return this.timeSpec;\n    }\n    getLine() {\n        return this.line;\n    }\n    setLine(line) {\n        this.line = line;\n    }\n    getIsNumeric() {\n        return this.isNumeric;\n    }\n    setIsNumeric(isNumeric) {\n        this.isNumeric = isNumeric;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        ctx.openGroup('timesignature', this.getAttribute('id'));\n        this.drawAt(ctx, stave, this.x);\n        ctx.closeGroup();\n    }\n    drawAt(ctx, stave, x) {\n        this.setRendered();\n        if (this.isNumeric) {\n            let startX = x + this.topStartX;\n            let y = 0;\n            if (this.botText.getText().length > 0)\n                y = stave.getYForLine(this.topLine - this.lineShift);\n            else\n                y = (stave.getYForLine(this.topLine) + stave.getYForLine(this.bottomLine)) / 2;\n            this.topText.renderText(ctx, startX, y);\n            startX = x + this.botStartX;\n            y = stave.getYForLine(this.bottomLine + this.lineShift);\n            this.botText.renderText(ctx, startX, y);\n        }\n        else {\n            this.renderText(ctx, x - this.x, stave.getYForLine(this.line));\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,oBAAoB,GAAIC,QAAQ,IAAK;EACvC,MAAMC,OAAO,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACnCD,OAAO,CAACE,OAAO,CAAEC,MAAM,IAAK;IACxB,IAAI,eAAe,CAACC,IAAI,CAACD,MAAM,CAAC,KAAK,KAAK,EAAE;MACxC,MAAM,IAAIN,YAAY,CAAC,kBAAkB,EAAE,sBAAsBE,QAAQ,kCAAkC,CAAC;IAChH;EACJ,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMM,aAAa,SAASV,aAAa,CAAC;EAC7C,WAAWW,QAAQA,CAAA,EAAG;IAClB,OAAO,eAAe;EAC1B;EACAC,WAAWA,CAACR,QAAQ,GAAG,KAAK,EAAES,aAAa,GAAG,EAAE,EAAEC,YAAY,GAAG,IAAI,EAAE;IACnE,KAAK,CAAC,CAAC;IACP,IAAI,CAACV,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACW,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,IAAItB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACuB,OAAO,GAAG,IAAIvB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACgB,YAAY,GAAGA,YAAY;IAChC,MAAMQ,OAAO,GAAGT,aAAa;IAC7B,IAAI,CAACU,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,WAAW,CAACxB,qBAAqB,CAACyB,KAAK,CAAC;IAC7C,IAAI,CAACC,UAAU,CAACvB,QAAQ,CAAC;IACzB,IAAI,CAACwB,UAAU,CAACN,OAAO,CAAC;EAC5B;EACA,OAAOO,cAAcA,CAACC,GAAG,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACzC,IAAIC,IAAI,GAAGjC,MAAM,CAACkC,IAAI;IACtB,QAAQH,GAAG;MACP,KAAK,GAAG;QACJE,IAAI,GAAGjC,MAAM,CAACmC,aAAa;QAC3B;MACJ,KAAK,IAAI;QACLF,IAAI,GAAGjC,MAAM,CAACoC,gBAAgB;QAC9B;MACJ,KAAK,GAAG;QACJH,IAAI,GAAGD,QAAQ,GAAGhC,MAAM,CAACqC,gBAAgB,GAAGrC,MAAM,CAACsC,WAAW;QAC9D;MACJ,KAAK,GAAG;QACJL,IAAI,GAAGjC,MAAM,CAACuC,YAAY;QAC1B;MACJ,KAAK,GAAG;QACJN,IAAI,GAAGD,QAAQ,GAAGhC,MAAM,CAACwC,sBAAsB,GAAGxC,MAAM,CAACyC,iBAAiB;QAC1E;MACJ,KAAK,GAAG;QACJR,IAAI,GAAGD,QAAQ,GAAGhC,MAAM,CAAC0C,uBAAuB,GAAG1C,MAAM,CAAC2C,kBAAkB;QAC5E;MACJ;QACIV,IAAI,GAAGW,MAAM,CAACC,aAAa,CAAC,MAAM,GAAGC,MAAM,CAACf,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD;IACR;IACA,OAAOE,IAAI;EACf;EACAc,sBAAsBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACzC,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,MAAM,GAAG,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACM,MAAM,EAAE,EAAED,CAAC,EAAE;MACvC,MAAMpB,IAAI,GAAGtB,aAAa,CAACmB,cAAc,CAACkB,SAAS,CAACK,CAAC,CAAC,EAAEJ,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;MAC7EJ,GAAG,IAAIjB,IAAI;IACf;IACA,IAAI,CAACZ,OAAO,CAACkC,OAAO,CAACL,GAAG,CAAC;IACzBC,QAAQ,GAAG,IAAI,CAAC9B,OAAO,CAACmC,QAAQ,CAAC,CAAC;IAClCJ,MAAM,GAAG,IAAI,CAAC/B,OAAO,CAACoC,SAAS,CAAC,CAAC;IACjC,IAAIC,QAAQ,GAAG,CAAC;IAChBR,GAAG,GAAG,EAAE;IACR,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACK,MAAM,EAAE,EAAED,CAAC,EAAE;MACvC,MAAMpB,IAAI,GAAGtB,aAAa,CAACmB,cAAc,CAACmB,SAAS,CAACI,CAAC,CAAC,EAAE,IAAI,CAAC;MAC7DH,GAAG,IAAIjB,IAAI;IACf;IACA,IAAI,CAACX,OAAO,CAACiC,OAAO,CAACL,GAAG,CAAC;IACzBQ,QAAQ,GAAG,IAAI,CAACpC,OAAO,CAACkC,QAAQ,CAAC,CAAC;IAClCJ,MAAM,GAAGO,IAAI,CAACC,GAAG,CAACR,MAAM,EAAE,IAAI,CAAC9B,OAAO,CAACmC,SAAS,CAAC,CAAC,CAAC;IACnD,IAAI,CAACrC,SAAS,GAAGgC,MAAM,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;IACtC,IAAI,CAACS,KAAK,GAAGF,IAAI,CAACC,GAAG,CAACT,QAAQ,EAAEO,QAAQ,CAAC;IACzC,IAAI,CAACxC,SAAS,GAAG,CAAC,IAAI,CAAC2C,KAAK,GAAGV,QAAQ,IAAI,GAAG;IAC9C,IAAI,CAAChC,SAAS,GAAG,CAAC,IAAI,CAAC0C,KAAK,GAAGH,QAAQ,IAAI,GAAG;EAClD;EACA9B,UAAUA,CAACvB,QAAQ,EAAE;IACjB,IAAIyD,EAAE,EAAEC,EAAE;IACV,IAAI,CAAC1D,QAAQ,GAAGA,QAAQ;IACxB,IAAIA,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACvC,MAAM4B,IAAI,GAAGtB,aAAa,CAACmB,cAAc,CAACzB,QAAQ,CAAC;MACnD,IAAI,CAACW,IAAI,GAAG,CAAC;MACb,IAAI,CAACgD,IAAI,GAAG/B,IAAI;MAChB,IAAI,CAAChB,SAAS,GAAG,KAAK;IAC1B,CAAC,MACI;MACD,IAAI,IAAI,CAACF,YAAY,EAAE;QACnBX,oBAAoB,CAACC,QAAQ,CAAC;MAClC;MACA,MAAM4D,KAAK,GAAG5D,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;MACjC,IAAI,CAACS,IAAI,GAAG,CAAC;MACb,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC8B,sBAAsB,CAAC,CAACe,EAAE,GAAGG,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAE,CAACC,EAAE,GAAGE,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;IACzI;IACA,OAAO,IAAI;EACf;EACAG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7D,QAAQ;EACxB;EACA8D,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACnD,IAAI;EACpB;EACAoD,OAAOA,CAACpD,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAqD,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpD,SAAS;EACzB;EACAqD,YAAYA,CAACrD,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAsD,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGF,KAAK,CAACG,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,eAAe,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAACC,MAAM,CAACL,GAAG,EAAEF,KAAK,EAAE,IAAI,CAACQ,CAAC,CAAC;IAC/BN,GAAG,CAACO,UAAU,CAAC,CAAC;EACpB;EACAF,MAAMA,CAACL,GAAG,EAAEF,KAAK,EAAEQ,CAAC,EAAE;IAClB,IAAI,CAACJ,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAChB,IAAIiE,MAAM,GAAGF,CAAC,GAAG,IAAI,CAAC9D,SAAS;MAC/B,IAAIiE,CAAC,GAAG,CAAC;MACT,IAAI,IAAI,CAAC7D,OAAO,CAAC8D,OAAO,CAAC,CAAC,CAAC9B,MAAM,GAAG,CAAC,EACjC6B,CAAC,GAAGX,KAAK,CAACa,WAAW,CAAC,IAAI,CAAC7D,OAAO,GAAG,IAAI,CAACJ,SAAS,CAAC,CAAC,KAErD+D,CAAC,GAAG,CAACX,KAAK,CAACa,WAAW,CAAC,IAAI,CAAC7D,OAAO,CAAC,GAAGgD,KAAK,CAACa,WAAW,CAAC,IAAI,CAAC5D,UAAU,CAAC,IAAI,CAAC;MAClF,IAAI,CAACJ,OAAO,CAACiE,UAAU,CAACZ,GAAG,EAAEQ,MAAM,EAAEC,CAAC,CAAC;MACvCD,MAAM,GAAGF,CAAC,GAAG,IAAI,CAAC7D,SAAS;MAC3BgE,CAAC,GAAGX,KAAK,CAACa,WAAW,CAAC,IAAI,CAAC5D,UAAU,GAAG,IAAI,CAACL,SAAS,CAAC;MACvD,IAAI,CAACE,OAAO,CAACgE,UAAU,CAACZ,GAAG,EAAEQ,MAAM,EAAEC,CAAC,CAAC;IAC3C,CAAC,MACI;MACD,IAAI,CAACG,UAAU,CAACZ,GAAG,EAAEM,CAAC,GAAG,IAAI,CAACA,CAAC,EAAER,KAAK,CAACa,WAAW,CAAC,IAAI,CAACrE,IAAI,CAAC,CAAC;IAClE;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}