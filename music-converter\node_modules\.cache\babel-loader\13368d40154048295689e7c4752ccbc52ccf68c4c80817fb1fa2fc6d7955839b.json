{"ast": null, "code": "import { RuntimeError } from './util.js';\nexport class Fraction {\n  static get CATEGORY() {\n    return \"Fraction\";\n  }\n  static GCD(a, b) {\n    if (typeof a !== 'number' || Number.isNaN(a) || typeof b !== 'number' || Number.isNaN(b)) {\n      throw new RuntimeError('BadArgument', `Invalid numbers: ${a}, ${b}`);\n    }\n    let t;\n    while (b !== 0) {\n      t = b;\n      b = a % b;\n      a = t;\n    }\n    return a;\n  }\n  static LCM(a, b) {\n    return a * b / Fraction.GCD(a, b);\n  }\n  static LCMM(args) {\n    if (args.length === 0) {\n      return 0;\n    } else if (args.length === 1) {\n      return args[0];\n    } else if (args.length === 2) {\n      return Fraction.LCM(args[0], args[1]);\n    } else {\n      return Fraction.LCM(args.shift(), Fraction.LCMM(args));\n    }\n  }\n  constructor(numerator, denominator) {\n    this.numerator = 1;\n    this.denominator = 1;\n    this.set(numerator, denominator);\n  }\n  set(numerator = 1, denominator = 1) {\n    this.numerator = numerator;\n    this.denominator = denominator;\n    return this;\n  }\n  value() {\n    return this.numerator / this.denominator;\n  }\n  simplify() {\n    let u = this.numerator;\n    let d = this.denominator;\n    const gcd = Fraction.GCD(u, d);\n    u /= gcd;\n    d /= gcd;\n    if (d < 0) {\n      d = -d;\n      u = -u;\n    }\n    return this.set(u, d);\n  }\n  add(param1 = 0, param2 = 1) {\n    const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n    const lcm = Fraction.LCM(this.denominator, otherDenominator);\n    const a = lcm / this.denominator;\n    const b = lcm / otherDenominator;\n    const u = this.numerator * a + otherNumerator * b;\n    return this.set(u, lcm);\n  }\n  subtract(param1 = 0, param2 = 1) {\n    const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n    const lcm = Fraction.LCM(this.denominator, otherDenominator);\n    const a = lcm / this.denominator;\n    const b = lcm / otherDenominator;\n    const u = this.numerator * a - otherNumerator * b;\n    return this.set(u, lcm);\n  }\n  multiply(param1 = 1, param2 = 1) {\n    const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n    return this.set(this.numerator * otherNumerator, this.denominator * otherDenominator);\n  }\n  divide(param1 = 1, param2 = 1) {\n    const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n    return this.set(this.numerator * otherDenominator, this.denominator * otherNumerator);\n  }\n  equals(compare) {\n    const a = Fraction.fractionA.copy(compare).simplify();\n    const b = Fraction.fractionB.copy(this).simplify();\n    return a.numerator === b.numerator && a.denominator === b.denominator;\n  }\n  greaterThan(compare) {\n    const a = Fraction.fractionA.copy(this);\n    a.subtract(compare);\n    return a.numerator > 0;\n  }\n  greaterThanEquals(compare) {\n    const a = Fraction.fractionA.copy(this);\n    a.subtract(compare);\n    return a.numerator >= 0;\n  }\n  lessThan(compare) {\n    return !this.greaterThanEquals(compare);\n  }\n  lessThanEquals(compare) {\n    return !this.greaterThan(compare);\n  }\n  clone() {\n    return new Fraction(this.numerator, this.denominator);\n  }\n  copy(other) {\n    if (typeof other === 'number') {\n      return this.set(other);\n    } else {\n      return this.set(other.numerator, other.denominator);\n    }\n  }\n  quotient() {\n    return Math.floor(this.numerator / this.denominator);\n  }\n  remainder() {\n    return this.numerator % this.denominator;\n  }\n  makeAbs() {\n    this.denominator = Math.abs(this.denominator);\n    this.numerator = Math.abs(this.numerator);\n    return this;\n  }\n  toString() {\n    return `${this.numerator}/${this.denominator}`;\n  }\n  toSimplifiedString() {\n    return Fraction.fractionA.copy(this).simplify().toString();\n  }\n  toMixedString() {\n    let s = '';\n    const q = this.quotient();\n    const f = Fraction.fractionA.copy(this);\n    if (q < 0) {\n      f.makeAbs();\n    }\n    if (q !== 0) {\n      s += q;\n      if (f.numerator !== 0) {\n        s += ` ${f.toSimplifiedString()}`;\n      }\n    } else if (f.numerator === 0) {\n      s = '0';\n    } else {\n      s = f.toSimplifiedString();\n    }\n    return s;\n  }\n  parse(str) {\n    const i = str.split('/');\n    const n = parseInt(i[0], 10);\n    const d = i[1] ? parseInt(i[1], 10) : 1;\n    return this.set(n, d);\n  }\n}\nFraction.fractionA = new Fraction();\nFraction.fractionB = new Fraction();\nfunction getNumeratorAndDenominator(n, d = 1) {\n  if (typeof n === 'number') {\n    return [n, d];\n  } else {\n    return [n.numerator, n.denominator];\n  }\n}", "map": {"version": 3, "names": ["RuntimeError", "Fraction", "CATEGORY", "GCD", "a", "b", "Number", "isNaN", "t", "LCM", "LCMM", "args", "length", "shift", "constructor", "numerator", "denominator", "set", "value", "simplify", "u", "d", "gcd", "add", "param1", "param2", "otherNumerator", "otherDenominator", "getNumeratorAndDenominator", "lcm", "subtract", "multiply", "divide", "equals", "compare", "fractionA", "copy", "fractionB", "greaterThan", "greaterThanEquals", "lessThan", "lessThanEquals", "clone", "other", "quotient", "Math", "floor", "remainder", "makeAbs", "abs", "toString", "toSimplifiedString", "toMixedString", "s", "q", "f", "parse", "str", "i", "split", "n", "parseInt"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/fraction.js"], "sourcesContent": ["import { RuntimeError } from './util.js';\nexport class Fraction {\n    static get CATEGORY() {\n        return \"Fraction\";\n    }\n    static GCD(a, b) {\n        if (typeof a !== 'number' || Number.isNaN(a) || typeof b !== 'number' || Number.isNaN(b)) {\n            throw new RuntimeError('BadArgument', `Invalid numbers: ${a}, ${b}`);\n        }\n        let t;\n        while (b !== 0) {\n            t = b;\n            b = a % b;\n            a = t;\n        }\n        return a;\n    }\n    static LCM(a, b) {\n        return (a * b) / Fraction.GCD(a, b);\n    }\n    static LCMM(args) {\n        if (args.length === 0) {\n            return 0;\n        }\n        else if (args.length === 1) {\n            return args[0];\n        }\n        else if (args.length === 2) {\n            return Fraction.LCM(args[0], args[1]);\n        }\n        else {\n            return Fraction.LCM(args.shift(), Fraction.LCMM(args));\n        }\n    }\n    constructor(numerator, denominator) {\n        this.numerator = 1;\n        this.denominator = 1;\n        this.set(numerator, denominator);\n    }\n    set(numerator = 1, denominator = 1) {\n        this.numerator = numerator;\n        this.denominator = denominator;\n        return this;\n    }\n    value() {\n        return this.numerator / this.denominator;\n    }\n    simplify() {\n        let u = this.numerator;\n        let d = this.denominator;\n        const gcd = Fraction.GCD(u, d);\n        u /= gcd;\n        d /= gcd;\n        if (d < 0) {\n            d = -d;\n            u = -u;\n        }\n        return this.set(u, d);\n    }\n    add(param1 = 0, param2 = 1) {\n        const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n        const lcm = Fraction.LCM(this.denominator, otherDenominator);\n        const a = lcm / this.denominator;\n        const b = lcm / otherDenominator;\n        const u = this.numerator * a + otherNumerator * b;\n        return this.set(u, lcm);\n    }\n    subtract(param1 = 0, param2 = 1) {\n        const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n        const lcm = Fraction.LCM(this.denominator, otherDenominator);\n        const a = lcm / this.denominator;\n        const b = lcm / otherDenominator;\n        const u = this.numerator * a - otherNumerator * b;\n        return this.set(u, lcm);\n    }\n    multiply(param1 = 1, param2 = 1) {\n        const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n        return this.set(this.numerator * otherNumerator, this.denominator * otherDenominator);\n    }\n    divide(param1 = 1, param2 = 1) {\n        const [otherNumerator, otherDenominator] = getNumeratorAndDenominator(param1, param2);\n        return this.set(this.numerator * otherDenominator, this.denominator * otherNumerator);\n    }\n    equals(compare) {\n        const a = Fraction.fractionA.copy(compare).simplify();\n        const b = Fraction.fractionB.copy(this).simplify();\n        return a.numerator === b.numerator && a.denominator === b.denominator;\n    }\n    greaterThan(compare) {\n        const a = Fraction.fractionA.copy(this);\n        a.subtract(compare);\n        return a.numerator > 0;\n    }\n    greaterThanEquals(compare) {\n        const a = Fraction.fractionA.copy(this);\n        a.subtract(compare);\n        return a.numerator >= 0;\n    }\n    lessThan(compare) {\n        return !this.greaterThanEquals(compare);\n    }\n    lessThanEquals(compare) {\n        return !this.greaterThan(compare);\n    }\n    clone() {\n        return new Fraction(this.numerator, this.denominator);\n    }\n    copy(other) {\n        if (typeof other === 'number') {\n            return this.set(other);\n        }\n        else {\n            return this.set(other.numerator, other.denominator);\n        }\n    }\n    quotient() {\n        return Math.floor(this.numerator / this.denominator);\n    }\n    remainder() {\n        return this.numerator % this.denominator;\n    }\n    makeAbs() {\n        this.denominator = Math.abs(this.denominator);\n        this.numerator = Math.abs(this.numerator);\n        return this;\n    }\n    toString() {\n        return `${this.numerator}/${this.denominator}`;\n    }\n    toSimplifiedString() {\n        return Fraction.fractionA.copy(this).simplify().toString();\n    }\n    toMixedString() {\n        let s = '';\n        const q = this.quotient();\n        const f = Fraction.fractionA.copy(this);\n        if (q < 0) {\n            f.makeAbs();\n        }\n        if (q !== 0) {\n            s += q;\n            if (f.numerator !== 0) {\n                s += ` ${f.toSimplifiedString()}`;\n            }\n        }\n        else if (f.numerator === 0) {\n            s = '0';\n        }\n        else {\n            s = f.toSimplifiedString();\n        }\n        return s;\n    }\n    parse(str) {\n        const i = str.split('/');\n        const n = parseInt(i[0], 10);\n        const d = i[1] ? parseInt(i[1], 10) : 1;\n        return this.set(n, d);\n    }\n}\nFraction.fractionA = new Fraction();\nFraction.fractionB = new Fraction();\nfunction getNumeratorAndDenominator(n, d = 1) {\n    if (typeof n === 'number') {\n        return [n, d];\n    }\n    else {\n        return [n.numerator, n.denominator];\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,QAAQ,CAAC;EAClB,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACA,OAAOC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACb,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIE,MAAM,CAACC,KAAK,CAACH,CAAC,CAAC,IAAI,OAAOC,CAAC,KAAK,QAAQ,IAAIC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC,EAAE;MACtF,MAAM,IAAIL,YAAY,CAAC,aAAa,EAAE,oBAAoBI,CAAC,KAAKC,CAAC,EAAE,CAAC;IACxE;IACA,IAAIG,CAAC;IACL,OAAOH,CAAC,KAAK,CAAC,EAAE;MACZG,CAAC,GAAGH,CAAC;MACLA,CAAC,GAAGD,CAAC,GAAGC,CAAC;MACTD,CAAC,GAAGI,CAAC;IACT;IACA,OAAOJ,CAAC;EACZ;EACA,OAAOK,GAAGA,CAACL,CAAC,EAAEC,CAAC,EAAE;IACb,OAAQD,CAAC,GAAGC,CAAC,GAAIJ,QAAQ,CAACE,GAAG,CAACC,CAAC,EAAEC,CAAC,CAAC;EACvC;EACA,OAAOK,IAAIA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,CAAC;IACZ,CAAC,MACI,IAAID,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOD,IAAI,CAAC,CAAC,CAAC;IAClB,CAAC,MACI,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOX,QAAQ,CAACQ,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,MACI;MACD,OAAOV,QAAQ,CAACQ,GAAG,CAACE,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEZ,QAAQ,CAACS,IAAI,CAACC,IAAI,CAAC,CAAC;IAC1D;EACJ;EACAG,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAE;IAChC,IAAI,CAACD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,GAAG,CAACF,SAAS,EAAEC,WAAW,CAAC;EACpC;EACAC,GAAGA,CAACF,SAAS,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,EAAE;IAChC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,OAAO,IAAI;EACf;EACAE,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACH,SAAS,GAAG,IAAI,CAACC,WAAW;EAC5C;EACAG,QAAQA,CAAA,EAAG;IACP,IAAIC,CAAC,GAAG,IAAI,CAACL,SAAS;IACtB,IAAIM,CAAC,GAAG,IAAI,CAACL,WAAW;IACxB,MAAMM,GAAG,GAAGrB,QAAQ,CAACE,GAAG,CAACiB,CAAC,EAAEC,CAAC,CAAC;IAC9BD,CAAC,IAAIE,GAAG;IACRD,CAAC,IAAIC,GAAG;IACR,IAAID,CAAC,GAAG,CAAC,EAAE;MACPA,CAAC,GAAG,CAACA,CAAC;MACND,CAAC,GAAG,CAACA,CAAC;IACV;IACA,OAAO,IAAI,CAACH,GAAG,CAACG,CAAC,EAAEC,CAAC,CAAC;EACzB;EACAE,GAAGA,CAACC,MAAM,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,CAACC,cAAc,EAAEC,gBAAgB,CAAC,GAAGC,0BAA0B,CAACJ,MAAM,EAAEC,MAAM,CAAC;IACrF,MAAMI,GAAG,GAAG5B,QAAQ,CAACQ,GAAG,CAAC,IAAI,CAACO,WAAW,EAAEW,gBAAgB,CAAC;IAC5D,MAAMvB,CAAC,GAAGyB,GAAG,GAAG,IAAI,CAACb,WAAW;IAChC,MAAMX,CAAC,GAAGwB,GAAG,GAAGF,gBAAgB;IAChC,MAAMP,CAAC,GAAG,IAAI,CAACL,SAAS,GAAGX,CAAC,GAAGsB,cAAc,GAAGrB,CAAC;IACjD,OAAO,IAAI,CAACY,GAAG,CAACG,CAAC,EAAES,GAAG,CAAC;EAC3B;EACAC,QAAQA,CAACN,MAAM,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAM,CAACC,cAAc,EAAEC,gBAAgB,CAAC,GAAGC,0BAA0B,CAACJ,MAAM,EAAEC,MAAM,CAAC;IACrF,MAAMI,GAAG,GAAG5B,QAAQ,CAACQ,GAAG,CAAC,IAAI,CAACO,WAAW,EAAEW,gBAAgB,CAAC;IAC5D,MAAMvB,CAAC,GAAGyB,GAAG,GAAG,IAAI,CAACb,WAAW;IAChC,MAAMX,CAAC,GAAGwB,GAAG,GAAGF,gBAAgB;IAChC,MAAMP,CAAC,GAAG,IAAI,CAACL,SAAS,GAAGX,CAAC,GAAGsB,cAAc,GAAGrB,CAAC;IACjD,OAAO,IAAI,CAACY,GAAG,CAACG,CAAC,EAAES,GAAG,CAAC;EAC3B;EACAE,QAAQA,CAACP,MAAM,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAM,CAACC,cAAc,EAAEC,gBAAgB,CAAC,GAAGC,0BAA0B,CAACJ,MAAM,EAAEC,MAAM,CAAC;IACrF,OAAO,IAAI,CAACR,GAAG,CAAC,IAAI,CAACF,SAAS,GAAGW,cAAc,EAAE,IAAI,CAACV,WAAW,GAAGW,gBAAgB,CAAC;EACzF;EACAK,MAAMA,CAACR,MAAM,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAM,CAACC,cAAc,EAAEC,gBAAgB,CAAC,GAAGC,0BAA0B,CAACJ,MAAM,EAAEC,MAAM,CAAC;IACrF,OAAO,IAAI,CAACR,GAAG,CAAC,IAAI,CAACF,SAAS,GAAGY,gBAAgB,EAAE,IAAI,CAACX,WAAW,GAAGU,cAAc,CAAC;EACzF;EACAO,MAAMA,CAACC,OAAO,EAAE;IACZ,MAAM9B,CAAC,GAAGH,QAAQ,CAACkC,SAAS,CAACC,IAAI,CAACF,OAAO,CAAC,CAACf,QAAQ,CAAC,CAAC;IACrD,MAAMd,CAAC,GAAGJ,QAAQ,CAACoC,SAAS,CAACD,IAAI,CAAC,IAAI,CAAC,CAACjB,QAAQ,CAAC,CAAC;IAClD,OAAOf,CAAC,CAACW,SAAS,KAAKV,CAAC,CAACU,SAAS,IAAIX,CAAC,CAACY,WAAW,KAAKX,CAAC,CAACW,WAAW;EACzE;EACAsB,WAAWA,CAACJ,OAAO,EAAE;IACjB,MAAM9B,CAAC,GAAGH,QAAQ,CAACkC,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IACvChC,CAAC,CAAC0B,QAAQ,CAACI,OAAO,CAAC;IACnB,OAAO9B,CAAC,CAACW,SAAS,GAAG,CAAC;EAC1B;EACAwB,iBAAiBA,CAACL,OAAO,EAAE;IACvB,MAAM9B,CAAC,GAAGH,QAAQ,CAACkC,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IACvChC,CAAC,CAAC0B,QAAQ,CAACI,OAAO,CAAC;IACnB,OAAO9B,CAAC,CAACW,SAAS,IAAI,CAAC;EAC3B;EACAyB,QAAQA,CAACN,OAAO,EAAE;IACd,OAAO,CAAC,IAAI,CAACK,iBAAiB,CAACL,OAAO,CAAC;EAC3C;EACAO,cAAcA,CAACP,OAAO,EAAE;IACpB,OAAO,CAAC,IAAI,CAACI,WAAW,CAACJ,OAAO,CAAC;EACrC;EACAQ,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIzC,QAAQ,CAAC,IAAI,CAACc,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC;EACzD;EACAoB,IAAIA,CAACO,KAAK,EAAE;IACR,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAAC1B,GAAG,CAAC0B,KAAK,CAAC;IAC1B,CAAC,MACI;MACD,OAAO,IAAI,CAAC1B,GAAG,CAAC0B,KAAK,CAAC5B,SAAS,EAAE4B,KAAK,CAAC3B,WAAW,CAAC;IACvD;EACJ;EACA4B,QAAQA,CAAA,EAAG;IACP,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC/B,SAAS,GAAG,IAAI,CAACC,WAAW,CAAC;EACxD;EACA+B,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChC,SAAS,GAAG,IAAI,CAACC,WAAW;EAC5C;EACAgC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChC,WAAW,GAAG6B,IAAI,CAACI,GAAG,CAAC,IAAI,CAACjC,WAAW,CAAC;IAC7C,IAAI,CAACD,SAAS,GAAG8B,IAAI,CAACI,GAAG,CAAC,IAAI,CAAClC,SAAS,CAAC;IACzC,OAAO,IAAI;EACf;EACAmC,QAAQA,CAAA,EAAG;IACP,OAAO,GAAG,IAAI,CAACnC,SAAS,IAAI,IAAI,CAACC,WAAW,EAAE;EAClD;EACAmC,kBAAkBA,CAAA,EAAG;IACjB,OAAOlD,QAAQ,CAACkC,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC,CAACjB,QAAQ,CAAC,CAAC,CAAC+B,QAAQ,CAAC,CAAC;EAC9D;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAIC,CAAC,GAAG,EAAE;IACV,MAAMC,CAAC,GAAG,IAAI,CAACV,QAAQ,CAAC,CAAC;IACzB,MAAMW,CAAC,GAAGtD,QAAQ,CAACkC,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;IACvC,IAAIkB,CAAC,GAAG,CAAC,EAAE;MACPC,CAAC,CAACP,OAAO,CAAC,CAAC;IACf;IACA,IAAIM,CAAC,KAAK,CAAC,EAAE;MACTD,CAAC,IAAIC,CAAC;MACN,IAAIC,CAAC,CAACxC,SAAS,KAAK,CAAC,EAAE;QACnBsC,CAAC,IAAI,IAAIE,CAAC,CAACJ,kBAAkB,CAAC,CAAC,EAAE;MACrC;IACJ,CAAC,MACI,IAAII,CAAC,CAACxC,SAAS,KAAK,CAAC,EAAE;MACxBsC,CAAC,GAAG,GAAG;IACX,CAAC,MACI;MACDA,CAAC,GAAGE,CAAC,CAACJ,kBAAkB,CAAC,CAAC;IAC9B;IACA,OAAOE,CAAC;EACZ;EACAG,KAAKA,CAACC,GAAG,EAAE;IACP,MAAMC,CAAC,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;IACxB,MAAMC,CAAC,GAAGC,QAAQ,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5B,MAAMrC,CAAC,GAAGqC,CAAC,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACH,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;IACvC,OAAO,IAAI,CAACzC,GAAG,CAAC2C,CAAC,EAAEvC,CAAC,CAAC;EACzB;AACJ;AACApB,QAAQ,CAACkC,SAAS,GAAG,IAAIlC,QAAQ,CAAC,CAAC;AACnCA,QAAQ,CAACoC,SAAS,GAAG,IAAIpC,QAAQ,CAAC,CAAC;AACnC,SAAS2B,0BAA0BA,CAACgC,CAAC,EAAEvC,CAAC,GAAG,CAAC,EAAE;EAC1C,IAAI,OAAOuC,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAO,CAACA,CAAC,EAAEvC,CAAC,CAAC;EACjB,CAAC,MACI;IACD,OAAO,CAACuC,CAAC,CAAC7C,SAAS,EAAE6C,CAAC,CAAC5C,WAAW,CAAC;EACvC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}