{"ast": null, "code": "import { Music } from './music.js';\nimport { RuntimeError } from './util.js';\nexport class KeyManager {\n  constructor(key) {\n    this.music = new Music();\n    this.setKey(key);\n  }\n  set<PERSON>ey(key) {\n    this.key = key;\n    this.reset();\n    return this;\n  }\n  getKey() {\n    return this.key;\n  }\n  reset() {\n    this.keyParts = this.music.getKeyParts(this.key);\n    this.keyString = this.keyParts.root;\n    if (this.keyParts.accidental) this.keyString += this.keyParts.accidental;\n    const isSupportedType = Music.scaleTypes[this.keyParts.type];\n    if (!isSupportedType) {\n      throw new RuntimeError('BadArguments', `Unsupported key type: ${this.key}`);\n    }\n    this.scale = this.music.getScaleTones(this.music.getNoteValue(this.keyString), Music.scaleTypes[this.keyParts.type]);\n    this.scaleMap = {};\n    this.scaleMapByValue = {};\n    this.originalScaleMapByValue = {};\n    const noteLocation = Music.rootIndexes[this.keyParts.root];\n    for (let i = 0; i < Music.roots.length; ++i) {\n      const index = (noteLocation + i) % Music.roots.length;\n      const rootName = Music.roots[index];\n      const noteName = this.music.getRelativeNoteName(rootName, this.scale[i]);\n      this.scaleMap[rootName] = noteName;\n      this.scaleMapByValue[this.scale[i]] = noteName;\n      this.originalScaleMapByValue[this.scale[i]] = noteName;\n    }\n    return this;\n  }\n  getAccidental(key) {\n    const root = this.music.getKeyParts(key).root;\n    const parts = this.music.getNoteParts(this.scaleMap[root]);\n    return {\n      note: this.scaleMap[root],\n      accidental: parts.accidental\n    };\n  }\n  selectNote(note) {\n    note = note.toLowerCase();\n    const parts = this.music.getNoteParts(note);\n    const scaleNote = this.scaleMap[parts.root];\n    const modparts = this.music.getNoteParts(scaleNote);\n    if (scaleNote === note) {\n      return {\n        note: scaleNote,\n        accidental: parts.accidental,\n        change: false\n      };\n    }\n    const valueNote = this.scaleMapByValue[this.music.getNoteValue(note)];\n    if (valueNote !== undefined) {\n      return {\n        note: valueNote,\n        accidental: this.music.getNoteParts(valueNote).accidental,\n        change: false\n      };\n    }\n    const originalValueNote = this.originalScaleMapByValue[this.music.getNoteValue(note)];\n    if (originalValueNote !== undefined) {\n      this.scaleMap[modparts.root] = originalValueNote;\n      delete this.scaleMapByValue[this.music.getNoteValue(scaleNote)];\n      this.scaleMapByValue[this.music.getNoteValue(note)] = originalValueNote;\n      return {\n        note: originalValueNote,\n        accidental: this.music.getNoteParts(originalValueNote).accidental,\n        change: true\n      };\n    }\n    if (modparts.root === note) {\n      delete this.scaleMapByValue[this.music.getNoteValue(this.scaleMap[parts.root])];\n      this.scaleMapByValue[this.music.getNoteValue(modparts.root)] = modparts.root;\n      this.scaleMap[modparts.root] = modparts.root;\n      return {\n        note: modparts.root,\n        accidental: undefined,\n        change: true\n      };\n    }\n    delete this.scaleMapByValue[this.music.getNoteValue(this.scaleMap[parts.root])];\n    this.scaleMapByValue[this.music.getNoteValue(note)] = note;\n    delete this.scaleMap[modparts.root];\n    this.scaleMap[modparts.root] = note;\n    return {\n      note,\n      accidental: parts.accidental,\n      change: true\n    };\n  }\n}", "map": {"version": 3, "names": ["Music", "RuntimeError", "KeyManager", "constructor", "key", "music", "<PERSON><PERSON><PERSON>", "reset", "<PERSON><PERSON><PERSON>", "keyParts", "getKeyParts", "keyString", "root", "accidental", "isSupportedType", "scaleTypes", "type", "scale", "getScaleTones", "getNoteValue", "scaleMap", "scaleMapByValue", "originalScaleMapByValue", "noteLocation", "rootIndexes", "i", "roots", "length", "index", "rootName", "noteName", "getRelativeNoteName", "getAccidental", "parts", "getNoteParts", "note", "selectNote", "toLowerCase", "scaleNote", "modparts", "change", "valueNote", "undefined", "originalValueNote"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/keymanager.js"], "sourcesContent": ["import { Music } from './music.js';\nimport { RuntimeError } from './util.js';\nexport class KeyManager {\n    constructor(key) {\n        this.music = new Music();\n        this.setKey(key);\n    }\n    set<PERSON>ey(key) {\n        this.key = key;\n        this.reset();\n        return this;\n    }\n    getKey() {\n        return this.key;\n    }\n    reset() {\n        this.keyParts = this.music.getKeyParts(this.key);\n        this.keyString = this.keyParts.root;\n        if (this.keyParts.accidental)\n            this.keyString += this.keyParts.accidental;\n        const isSupportedType = Music.scaleTypes[this.keyParts.type];\n        if (!isSupportedType) {\n            throw new RuntimeError('BadArguments', `Unsupported key type: ${this.key}`);\n        }\n        this.scale = this.music.getScaleTones(this.music.getNoteValue(this.keyString), Music.scaleTypes[this.keyParts.type]);\n        this.scaleMap = {};\n        this.scaleMapByValue = {};\n        this.originalScaleMapByValue = {};\n        const noteLocation = Music.rootIndexes[this.keyParts.root];\n        for (let i = 0; i < Music.roots.length; ++i) {\n            const index = (noteLocation + i) % Music.roots.length;\n            const rootName = Music.roots[index];\n            const noteName = this.music.getRelativeNoteName(rootName, this.scale[i]);\n            this.scaleMap[rootName] = noteName;\n            this.scaleMapByValue[this.scale[i]] = noteName;\n            this.originalScaleMapByValue[this.scale[i]] = noteName;\n        }\n        return this;\n    }\n    getAccidental(key) {\n        const root = this.music.getKeyParts(key).root;\n        const parts = this.music.getNoteParts(this.scaleMap[root]);\n        return {\n            note: this.scaleMap[root],\n            accidental: parts.accidental,\n        };\n    }\n    selectNote(note) {\n        note = note.toLowerCase();\n        const parts = this.music.getNoteParts(note);\n        const scaleNote = this.scaleMap[parts.root];\n        const modparts = this.music.getNoteParts(scaleNote);\n        if (scaleNote === note) {\n            return {\n                note: scaleNote,\n                accidental: parts.accidental,\n                change: false,\n            };\n        }\n        const valueNote = this.scaleMapByValue[this.music.getNoteValue(note)];\n        if (valueNote !== undefined) {\n            return {\n                note: valueNote,\n                accidental: this.music.getNoteParts(valueNote).accidental,\n                change: false,\n            };\n        }\n        const originalValueNote = this.originalScaleMapByValue[this.music.getNoteValue(note)];\n        if (originalValueNote !== undefined) {\n            this.scaleMap[modparts.root] = originalValueNote;\n            delete this.scaleMapByValue[this.music.getNoteValue(scaleNote)];\n            this.scaleMapByValue[this.music.getNoteValue(note)] = originalValueNote;\n            return {\n                note: originalValueNote,\n                accidental: this.music.getNoteParts(originalValueNote).accidental,\n                change: true,\n            };\n        }\n        if (modparts.root === note) {\n            delete this.scaleMapByValue[this.music.getNoteValue(this.scaleMap[parts.root])];\n            this.scaleMapByValue[this.music.getNoteValue(modparts.root)] = modparts.root;\n            this.scaleMap[modparts.root] = modparts.root;\n            return {\n                note: modparts.root,\n                accidental: undefined,\n                change: true,\n            };\n        }\n        delete this.scaleMapByValue[this.music.getNoteValue(this.scaleMap[parts.root])];\n        this.scaleMapByValue[this.music.getNoteValue(note)] = note;\n        delete this.scaleMap[modparts.root];\n        this.scaleMap[modparts.root] = note;\n        return {\n            note,\n            accidental: parts.accidental,\n            change: true,\n        };\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,UAAU,CAAC;EACpBC,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,CAACC,KAAK,GAAG,IAAIL,KAAK,CAAC,CAAC;IACxB,IAAI,CAACM,MAAM,CAACF,GAAG,CAAC;EACpB;EACAE,MAAMA,CAACF,GAAG,EAAE;IACR,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACJ,GAAG;EACnB;EACAG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACK,WAAW,CAAC,IAAI,CAACN,GAAG,CAAC;IAChD,IAAI,CAACO,SAAS,GAAG,IAAI,CAACF,QAAQ,CAACG,IAAI;IACnC,IAAI,IAAI,CAACH,QAAQ,CAACI,UAAU,EACxB,IAAI,CAACF,SAAS,IAAI,IAAI,CAACF,QAAQ,CAACI,UAAU;IAC9C,MAAMC,eAAe,GAAGd,KAAK,CAACe,UAAU,CAAC,IAAI,CAACN,QAAQ,CAACO,IAAI,CAAC;IAC5D,IAAI,CAACF,eAAe,EAAE;MAClB,MAAM,IAAIb,YAAY,CAAC,cAAc,EAAE,yBAAyB,IAAI,CAACG,GAAG,EAAE,CAAC;IAC/E;IACA,IAAI,CAACa,KAAK,GAAG,IAAI,CAACZ,KAAK,CAACa,aAAa,CAAC,IAAI,CAACb,KAAK,CAACc,YAAY,CAAC,IAAI,CAACR,SAAS,CAAC,EAAEX,KAAK,CAACe,UAAU,CAAC,IAAI,CAACN,QAAQ,CAACO,IAAI,CAAC,CAAC;IACpH,IAAI,CAACI,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,GAAG,CAAC,CAAC;IACjC,MAAMC,YAAY,GAAGvB,KAAK,CAACwB,WAAW,CAAC,IAAI,CAACf,QAAQ,CAACG,IAAI,CAAC;IAC1D,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,KAAK,CAAC0B,KAAK,CAACC,MAAM,EAAE,EAAEF,CAAC,EAAE;MACzC,MAAMG,KAAK,GAAG,CAACL,YAAY,GAAGE,CAAC,IAAIzB,KAAK,CAAC0B,KAAK,CAACC,MAAM;MACrD,MAAME,QAAQ,GAAG7B,KAAK,CAAC0B,KAAK,CAACE,KAAK,CAAC;MACnC,MAAME,QAAQ,GAAG,IAAI,CAACzB,KAAK,CAAC0B,mBAAmB,CAACF,QAAQ,EAAE,IAAI,CAACZ,KAAK,CAACQ,CAAC,CAAC,CAAC;MACxE,IAAI,CAACL,QAAQ,CAACS,QAAQ,CAAC,GAAGC,QAAQ;MAClC,IAAI,CAACT,eAAe,CAAC,IAAI,CAACJ,KAAK,CAACQ,CAAC,CAAC,CAAC,GAAGK,QAAQ;MAC9C,IAAI,CAACR,uBAAuB,CAAC,IAAI,CAACL,KAAK,CAACQ,CAAC,CAAC,CAAC,GAAGK,QAAQ;IAC1D;IACA,OAAO,IAAI;EACf;EACAE,aAAaA,CAAC5B,GAAG,EAAE;IACf,MAAMQ,IAAI,GAAG,IAAI,CAACP,KAAK,CAACK,WAAW,CAACN,GAAG,CAAC,CAACQ,IAAI;IAC7C,MAAMqB,KAAK,GAAG,IAAI,CAAC5B,KAAK,CAAC6B,YAAY,CAAC,IAAI,CAACd,QAAQ,CAACR,IAAI,CAAC,CAAC;IAC1D,OAAO;MACHuB,IAAI,EAAE,IAAI,CAACf,QAAQ,CAACR,IAAI,CAAC;MACzBC,UAAU,EAAEoB,KAAK,CAACpB;IACtB,CAAC;EACL;EACAuB,UAAUA,CAACD,IAAI,EAAE;IACbA,IAAI,GAAGA,IAAI,CAACE,WAAW,CAAC,CAAC;IACzB,MAAMJ,KAAK,GAAG,IAAI,CAAC5B,KAAK,CAAC6B,YAAY,CAACC,IAAI,CAAC;IAC3C,MAAMG,SAAS,GAAG,IAAI,CAAClB,QAAQ,CAACa,KAAK,CAACrB,IAAI,CAAC;IAC3C,MAAM2B,QAAQ,GAAG,IAAI,CAAClC,KAAK,CAAC6B,YAAY,CAACI,SAAS,CAAC;IACnD,IAAIA,SAAS,KAAKH,IAAI,EAAE;MACpB,OAAO;QACHA,IAAI,EAAEG,SAAS;QACfzB,UAAU,EAAEoB,KAAK,CAACpB,UAAU;QAC5B2B,MAAM,EAAE;MACZ,CAAC;IACL;IACA,MAAMC,SAAS,GAAG,IAAI,CAACpB,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAACgB,IAAI,CAAC,CAAC;IACrE,IAAIM,SAAS,KAAKC,SAAS,EAAE;MACzB,OAAO;QACHP,IAAI,EAAEM,SAAS;QACf5B,UAAU,EAAE,IAAI,CAACR,KAAK,CAAC6B,YAAY,CAACO,SAAS,CAAC,CAAC5B,UAAU;QACzD2B,MAAM,EAAE;MACZ,CAAC;IACL;IACA,MAAMG,iBAAiB,GAAG,IAAI,CAACrB,uBAAuB,CAAC,IAAI,CAACjB,KAAK,CAACc,YAAY,CAACgB,IAAI,CAAC,CAAC;IACrF,IAAIQ,iBAAiB,KAAKD,SAAS,EAAE;MACjC,IAAI,CAACtB,QAAQ,CAACmB,QAAQ,CAAC3B,IAAI,CAAC,GAAG+B,iBAAiB;MAChD,OAAO,IAAI,CAACtB,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAACmB,SAAS,CAAC,CAAC;MAC/D,IAAI,CAACjB,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAACgB,IAAI,CAAC,CAAC,GAAGQ,iBAAiB;MACvE,OAAO;QACHR,IAAI,EAAEQ,iBAAiB;QACvB9B,UAAU,EAAE,IAAI,CAACR,KAAK,CAAC6B,YAAY,CAACS,iBAAiB,CAAC,CAAC9B,UAAU;QACjE2B,MAAM,EAAE;MACZ,CAAC;IACL;IACA,IAAID,QAAQ,CAAC3B,IAAI,KAAKuB,IAAI,EAAE;MACxB,OAAO,IAAI,CAACd,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAAC,IAAI,CAACC,QAAQ,CAACa,KAAK,CAACrB,IAAI,CAAC,CAAC,CAAC;MAC/E,IAAI,CAACS,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAACoB,QAAQ,CAAC3B,IAAI,CAAC,CAAC,GAAG2B,QAAQ,CAAC3B,IAAI;MAC5E,IAAI,CAACQ,QAAQ,CAACmB,QAAQ,CAAC3B,IAAI,CAAC,GAAG2B,QAAQ,CAAC3B,IAAI;MAC5C,OAAO;QACHuB,IAAI,EAAEI,QAAQ,CAAC3B,IAAI;QACnBC,UAAU,EAAE6B,SAAS;QACrBF,MAAM,EAAE;MACZ,CAAC;IACL;IACA,OAAO,IAAI,CAACnB,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAAC,IAAI,CAACC,QAAQ,CAACa,KAAK,CAACrB,IAAI,CAAC,CAAC,CAAC;IAC/E,IAAI,CAACS,eAAe,CAAC,IAAI,CAAChB,KAAK,CAACc,YAAY,CAACgB,IAAI,CAAC,CAAC,GAAGA,IAAI;IAC1D,OAAO,IAAI,CAACf,QAAQ,CAACmB,QAAQ,CAAC3B,IAAI,CAAC;IACnC,IAAI,CAACQ,QAAQ,CAACmB,QAAQ,CAAC3B,IAAI,CAAC,GAAGuB,IAAI;IACnC,OAAO;MACHA,IAAI;MACJtB,UAAU,EAAEoB,KAAK,CAACpB,UAAU;MAC5B2B,MAAM,EAAE;IACZ,CAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}