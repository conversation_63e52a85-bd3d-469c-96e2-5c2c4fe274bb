{"ast": null, "code": "import PropTypes from 'prop-types';\nimport withSideEffect from 'react-side-effect';\nimport isEqual from 'react-fast-compare';\nimport React from 'react';\nimport objectAssign from 'object-assign';\nvar ATTRIBUTE_NAMES = {\n  BODY: \"bodyAttributes\",\n  HTML: \"htmlAttributes\",\n  TITLE: \"titleAttributes\"\n};\nvar TAG_NAMES = {\n  BASE: \"base\",\n  BODY: \"body\",\n  HEAD: \"head\",\n  HTML: \"html\",\n  LINK: \"link\",\n  META: \"meta\",\n  NOSCRIPT: \"noscript\",\n  SCRIPT: \"script\",\n  STYLE: \"style\",\n  TITLE: \"title\"\n};\nvar VALID_TAG_NAMES = Object.keys(TAG_NAMES).map(function (name) {\n  return TAG_NAMES[name];\n});\nvar TAG_PROPERTIES = {\n  CHARSET: \"charset\",\n  CSS_TEXT: \"cssText\",\n  HREF: \"href\",\n  HTTPEQUIV: \"http-equiv\",\n  INNER_HTML: \"innerHTML\",\n  ITEM_PROP: \"itemprop\",\n  NAME: \"name\",\n  PROPERTY: \"property\",\n  REL: \"rel\",\n  SRC: \"src\",\n  TARGET: \"target\"\n};\nvar REACT_TAG_MAP = {\n  accesskey: \"accessKey\",\n  charset: \"charSet\",\n  class: \"className\",\n  contenteditable: \"contentEditable\",\n  contextmenu: \"contextMenu\",\n  \"http-equiv\": \"httpEquiv\",\n  itemprop: \"itemProp\",\n  tabindex: \"tabIndex\"\n};\nvar HELMET_PROPS = {\n  DEFAULT_TITLE: \"defaultTitle\",\n  DEFER: \"defer\",\n  ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n  ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n  TITLE_TEMPLATE: \"titleTemplate\"\n};\nvar HTML_TAG_MAP = Object.keys(REACT_TAG_MAP).reduce(function (obj, key) {\n  obj[REACT_TAG_MAP[key]] = key;\n  return obj;\n}, {});\nvar SELF_CLOSING_TAGS = [TAG_NAMES.NOSCRIPT, TAG_NAMES.SCRIPT, TAG_NAMES.STYLE];\nvar HELMET_ATTRIBUTE = \"data-react-helmet\";\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nvar inherits = function (subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n};\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n  return target;\n};\nvar possibleConstructorReturn = function (self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n};\nvar encodeSpecialCharacters = function encodeSpecialCharacters(str) {\n  var encode = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  if (encode === false) {\n    return String(str);\n  }\n  return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\nvar getTitleFromPropsList = function getTitleFromPropsList(propsList) {\n  var innermostTitle = getInnermostProperty(propsList, TAG_NAMES.TITLE);\n  var innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n  if (innermostTemplate && innermostTitle) {\n    // use function arg to avoid need to escape $ characters\n    return innermostTemplate.replace(/%s/g, function () {\n      return Array.isArray(innermostTitle) ? innermostTitle.join(\"\") : innermostTitle;\n    });\n  }\n  var innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n  return innermostTitle || innermostDefaultTitle || undefined;\n};\nvar getOnChangeClientState = function getOnChangeClientState(propsList) {\n  return getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || function () {};\n};\nvar getAttributesFromPropsList = function getAttributesFromPropsList(tagType, propsList) {\n  return propsList.filter(function (props) {\n    return typeof props[tagType] !== \"undefined\";\n  }).map(function (props) {\n    return props[tagType];\n  }).reduce(function (tagAttrs, current) {\n    return _extends({}, tagAttrs, current);\n  }, {});\n};\nvar getBaseTagFromPropsList = function getBaseTagFromPropsList(primaryAttributes, propsList) {\n  return propsList.filter(function (props) {\n    return typeof props[TAG_NAMES.BASE] !== \"undefined\";\n  }).map(function (props) {\n    return props[TAG_NAMES.BASE];\n  }).reverse().reduce(function (innermostBaseTag, tag) {\n    if (!innermostBaseTag.length) {\n      var keys = Object.keys(tag);\n      for (var i = 0; i < keys.length; i++) {\n        var attributeKey = keys[i];\n        var lowerCaseAttributeKey = attributeKey.toLowerCase();\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n          return innermostBaseTag.concat(tag);\n        }\n      }\n    }\n    return innermostBaseTag;\n  }, []);\n};\nvar getTagsFromPropsList = function getTagsFromPropsList(tagName, primaryAttributes, propsList) {\n  // Calculate list of tags, giving priority innermost component (end of the propslist)\n  var approvedSeenTags = {};\n  return propsList.filter(function (props) {\n    if (Array.isArray(props[tagName])) {\n      return true;\n    }\n    if (typeof props[tagName] !== \"undefined\") {\n      warn(\"Helmet: \" + tagName + \" should be of type \\\"Array\\\". Instead found type \\\"\" + _typeof(props[tagName]) + \"\\\"\");\n    }\n    return false;\n  }).map(function (props) {\n    return props[tagName];\n  }).reverse().reduce(function (approvedTags, instanceTags) {\n    var instanceSeenTags = {};\n    instanceTags.filter(function (tag) {\n      var primaryAttributeKey = void 0;\n      var keys = Object.keys(tag);\n      for (var i = 0; i < keys.length; i++) {\n        var attributeKey = keys[i];\n        var lowerCaseAttributeKey = attributeKey.toLowerCase();\n\n        // Special rule with link tags, since rel and href are both primary tags, rel takes priority\n        if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === TAG_PROPERTIES.REL && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === TAG_PROPERTIES.REL && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n          primaryAttributeKey = lowerCaseAttributeKey;\n        }\n        // Special case for innerHTML which doesn't work lowercased\n        if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === TAG_PROPERTIES.INNER_HTML || attributeKey === TAG_PROPERTIES.CSS_TEXT || attributeKey === TAG_PROPERTIES.ITEM_PROP)) {\n          primaryAttributeKey = attributeKey;\n        }\n      }\n      if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n        return false;\n      }\n      var value = tag[primaryAttributeKey].toLowerCase();\n      if (!approvedSeenTags[primaryAttributeKey]) {\n        approvedSeenTags[primaryAttributeKey] = {};\n      }\n      if (!instanceSeenTags[primaryAttributeKey]) {\n        instanceSeenTags[primaryAttributeKey] = {};\n      }\n      if (!approvedSeenTags[primaryAttributeKey][value]) {\n        instanceSeenTags[primaryAttributeKey][value] = true;\n        return true;\n      }\n      return false;\n    }).reverse().forEach(function (tag) {\n      return approvedTags.push(tag);\n    });\n\n    // Update seen tags with tags from this instance\n    var keys = Object.keys(instanceSeenTags);\n    for (var i = 0; i < keys.length; i++) {\n      var attributeKey = keys[i];\n      var tagUnion = objectAssign({}, approvedSeenTags[attributeKey], instanceSeenTags[attributeKey]);\n      approvedSeenTags[attributeKey] = tagUnion;\n    }\n    return approvedTags;\n  }, []).reverse();\n};\nvar getInnermostProperty = function getInnermostProperty(propsList, property) {\n  for (var i = propsList.length - 1; i >= 0; i--) {\n    var props = propsList[i];\n    if (props.hasOwnProperty(property)) {\n      return props[property];\n    }\n  }\n  return null;\n};\nvar reducePropsToState = function reducePropsToState(propsList) {\n  return {\n    baseTag: getBaseTagFromPropsList([TAG_PROPERTIES.HREF, TAG_PROPERTIES.TARGET], propsList),\n    bodyAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.BODY, propsList),\n    defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n    encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n    htmlAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.HTML, propsList),\n    linkTags: getTagsFromPropsList(TAG_NAMES.LINK, [TAG_PROPERTIES.REL, TAG_PROPERTIES.HREF], propsList),\n    metaTags: getTagsFromPropsList(TAG_NAMES.META, [TAG_PROPERTIES.NAME, TAG_PROPERTIES.CHARSET, TAG_PROPERTIES.HTTPEQUIV, TAG_PROPERTIES.PROPERTY, TAG_PROPERTIES.ITEM_PROP], propsList),\n    noscriptTags: getTagsFromPropsList(TAG_NAMES.NOSCRIPT, [TAG_PROPERTIES.INNER_HTML], propsList),\n    onChangeClientState: getOnChangeClientState(propsList),\n    scriptTags: getTagsFromPropsList(TAG_NAMES.SCRIPT, [TAG_PROPERTIES.SRC, TAG_PROPERTIES.INNER_HTML], propsList),\n    styleTags: getTagsFromPropsList(TAG_NAMES.STYLE, [TAG_PROPERTIES.CSS_TEXT], propsList),\n    title: getTitleFromPropsList(propsList),\n    titleAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.TITLE, propsList)\n  };\n};\nvar rafPolyfill = function () {\n  var clock = Date.now();\n  return function (callback) {\n    var currentTime = Date.now();\n    if (currentTime - clock > 16) {\n      clock = currentTime;\n      callback(currentTime);\n    } else {\n      setTimeout(function () {\n        rafPolyfill(callback);\n      }, 0);\n    }\n  };\n}();\nvar cafPolyfill = function cafPolyfill(id) {\n  return clearTimeout(id);\n};\nvar requestAnimationFrame = typeof window !== \"undefined\" ? window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || rafPolyfill : global.requestAnimationFrame || rafPolyfill;\nvar cancelAnimationFrame = typeof window !== \"undefined\" ? window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || cafPolyfill : global.cancelAnimationFrame || cafPolyfill;\nvar warn = function warn(msg) {\n  return console && typeof console.warn === \"function\" && console.warn(msg);\n};\nvar _helmetCallback = null;\nvar handleClientStateChange = function handleClientStateChange(newState) {\n  if (_helmetCallback) {\n    cancelAnimationFrame(_helmetCallback);\n  }\n  if (newState.defer) {\n    _helmetCallback = requestAnimationFrame(function () {\n      commitTagChanges(newState, function () {\n        _helmetCallback = null;\n      });\n    });\n  } else {\n    commitTagChanges(newState);\n    _helmetCallback = null;\n  }\n};\nvar commitTagChanges = function commitTagChanges(newState, cb) {\n  var baseTag = newState.baseTag,\n    bodyAttributes = newState.bodyAttributes,\n    htmlAttributes = newState.htmlAttributes,\n    linkTags = newState.linkTags,\n    metaTags = newState.metaTags,\n    noscriptTags = newState.noscriptTags,\n    onChangeClientState = newState.onChangeClientState,\n    scriptTags = newState.scriptTags,\n    styleTags = newState.styleTags,\n    title = newState.title,\n    titleAttributes = newState.titleAttributes;\n  updateAttributes(TAG_NAMES.BODY, bodyAttributes);\n  updateAttributes(TAG_NAMES.HTML, htmlAttributes);\n  updateTitle(title, titleAttributes);\n  var tagUpdates = {\n    baseTag: updateTags(TAG_NAMES.BASE, baseTag),\n    linkTags: updateTags(TAG_NAMES.LINK, linkTags),\n    metaTags: updateTags(TAG_NAMES.META, metaTags),\n    noscriptTags: updateTags(TAG_NAMES.NOSCRIPT, noscriptTags),\n    scriptTags: updateTags(TAG_NAMES.SCRIPT, scriptTags),\n    styleTags: updateTags(TAG_NAMES.STYLE, styleTags)\n  };\n  var addedTags = {};\n  var removedTags = {};\n  Object.keys(tagUpdates).forEach(function (tagType) {\n    var _tagUpdates$tagType = tagUpdates[tagType],\n      newTags = _tagUpdates$tagType.newTags,\n      oldTags = _tagUpdates$tagType.oldTags;\n    if (newTags.length) {\n      addedTags[tagType] = newTags;\n    }\n    if (oldTags.length) {\n      removedTags[tagType] = tagUpdates[tagType].oldTags;\n    }\n  });\n  cb && cb();\n  onChangeClientState(newState, addedTags, removedTags);\n};\nvar flattenArray = function flattenArray(possibleArray) {\n  return Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\n};\nvar updateTitle = function updateTitle(title, attributes) {\n  if (typeof title !== \"undefined\" && document.title !== title) {\n    document.title = flattenArray(title);\n  }\n  updateAttributes(TAG_NAMES.TITLE, attributes);\n};\nvar updateAttributes = function updateAttributes(tagName, attributes) {\n  var elementTag = document.getElementsByTagName(tagName)[0];\n  if (!elementTag) {\n    return;\n  }\n  var helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n  var helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n  var attributesToRemove = [].concat(helmetAttributes);\n  var attributeKeys = Object.keys(attributes);\n  for (var i = 0; i < attributeKeys.length; i++) {\n    var attribute = attributeKeys[i];\n    var value = attributes[attribute] || \"\";\n    if (elementTag.getAttribute(attribute) !== value) {\n      elementTag.setAttribute(attribute, value);\n    }\n    if (helmetAttributes.indexOf(attribute) === -1) {\n      helmetAttributes.push(attribute);\n    }\n    var indexToSave = attributesToRemove.indexOf(attribute);\n    if (indexToSave !== -1) {\n      attributesToRemove.splice(indexToSave, 1);\n    }\n  }\n  for (var _i = attributesToRemove.length - 1; _i >= 0; _i--) {\n    elementTag.removeAttribute(attributesToRemove[_i]);\n  }\n  if (helmetAttributes.length === attributesToRemove.length) {\n    elementTag.removeAttribute(HELMET_ATTRIBUTE);\n  } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n    elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n  }\n};\nvar updateTags = function updateTags(type, tags) {\n  var headElement = document.head || document.querySelector(TAG_NAMES.HEAD);\n  var tagNodes = headElement.querySelectorAll(type + \"[\" + HELMET_ATTRIBUTE + \"]\");\n  var oldTags = Array.prototype.slice.call(tagNodes);\n  var newTags = [];\n  var indexToDelete = void 0;\n  if (tags && tags.length) {\n    tags.forEach(function (tag) {\n      var newElement = document.createElement(type);\n      for (var attribute in tag) {\n        if (tag.hasOwnProperty(attribute)) {\n          if (attribute === TAG_PROPERTIES.INNER_HTML) {\n            newElement.innerHTML = tag.innerHTML;\n          } else if (attribute === TAG_PROPERTIES.CSS_TEXT) {\n            if (newElement.styleSheet) {\n              newElement.styleSheet.cssText = tag.cssText;\n            } else {\n              newElement.appendChild(document.createTextNode(tag.cssText));\n            }\n          } else {\n            var value = typeof tag[attribute] === \"undefined\" ? \"\" : tag[attribute];\n            newElement.setAttribute(attribute, value);\n          }\n        }\n      }\n      newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n\n      // Remove a duplicate tag from domTagstoRemove, so it isn't cleared.\n      if (oldTags.some(function (existingTag, index) {\n        indexToDelete = index;\n        return newElement.isEqualNode(existingTag);\n      })) {\n        oldTags.splice(indexToDelete, 1);\n      } else {\n        newTags.push(newElement);\n      }\n    });\n  }\n  oldTags.forEach(function (tag) {\n    return tag.parentNode.removeChild(tag);\n  });\n  newTags.forEach(function (tag) {\n    return headElement.appendChild(tag);\n  });\n  return {\n    oldTags: oldTags,\n    newTags: newTags\n  };\n};\nvar generateElementAttributesAsString = function generateElementAttributesAsString(attributes) {\n  return Object.keys(attributes).reduce(function (str, key) {\n    var attr = typeof attributes[key] !== \"undefined\" ? key + \"=\\\"\" + attributes[key] + \"\\\"\" : \"\" + key;\n    return str ? str + \" \" + attr : attr;\n  }, \"\");\n};\nvar generateTitleAsString = function generateTitleAsString(type, title, attributes, encode) {\n  var attributeString = generateElementAttributesAsString(attributes);\n  var flattenedTitle = flattenArray(title);\n  return attributeString ? \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\" \" + attributeString + \">\" + encodeSpecialCharacters(flattenedTitle, encode) + \"</\" + type + \">\" : \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\">\" + encodeSpecialCharacters(flattenedTitle, encode) + \"</\" + type + \">\";\n};\nvar generateTagsAsString = function generateTagsAsString(type, tags, encode) {\n  return tags.reduce(function (str, tag) {\n    var attributeHtml = Object.keys(tag).filter(function (attribute) {\n      return !(attribute === TAG_PROPERTIES.INNER_HTML || attribute === TAG_PROPERTIES.CSS_TEXT);\n    }).reduce(function (string, attribute) {\n      var attr = typeof tag[attribute] === \"undefined\" ? attribute : attribute + \"=\\\"\" + encodeSpecialCharacters(tag[attribute], encode) + \"\\\"\";\n      return string ? string + \" \" + attr : attr;\n    }, \"\");\n    var tagContent = tag.innerHTML || tag.cssText || \"\";\n    var isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n    return str + \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\" \" + attributeHtml + (isSelfClosing ? \"/>\" : \">\" + tagContent + \"</\" + type + \">\");\n  }, \"\");\n};\nvar convertElementAttributestoReactProps = function convertElementAttributestoReactProps(attributes) {\n  var initProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return Object.keys(attributes).reduce(function (obj, key) {\n    obj[REACT_TAG_MAP[key] || key] = attributes[key];\n    return obj;\n  }, initProps);\n};\nvar convertReactPropstoHtmlAttributes = function convertReactPropstoHtmlAttributes(props) {\n  var initAttributes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return Object.keys(props).reduce(function (obj, key) {\n    obj[HTML_TAG_MAP[key] || key] = props[key];\n    return obj;\n  }, initAttributes);\n};\nvar generateTitleAsReactComponent = function generateTitleAsReactComponent(type, title, attributes) {\n  var _initProps;\n\n  // assigning into an array to define toString function on it\n  var initProps = (_initProps = {\n    key: title\n  }, _initProps[HELMET_ATTRIBUTE] = true, _initProps);\n  var props = convertElementAttributestoReactProps(attributes, initProps);\n  return [React.createElement(TAG_NAMES.TITLE, props, title)];\n};\nvar generateTagsAsReactComponent = function generateTagsAsReactComponent(type, tags) {\n  return tags.map(function (tag, i) {\n    var _mappedTag;\n    var mappedTag = (_mappedTag = {\n      key: i\n    }, _mappedTag[HELMET_ATTRIBUTE] = true, _mappedTag);\n    Object.keys(tag).forEach(function (attribute) {\n      var mappedAttribute = REACT_TAG_MAP[attribute] || attribute;\n      if (mappedAttribute === TAG_PROPERTIES.INNER_HTML || mappedAttribute === TAG_PROPERTIES.CSS_TEXT) {\n        var content = tag.innerHTML || tag.cssText;\n        mappedTag.dangerouslySetInnerHTML = {\n          __html: content\n        };\n      } else {\n        mappedTag[mappedAttribute] = tag[attribute];\n      }\n    });\n    return React.createElement(type, mappedTag);\n  });\n};\nvar getMethodsForTag = function getMethodsForTag(type, tags, encode) {\n  switch (type) {\n    case TAG_NAMES.TITLE:\n      return {\n        toComponent: function toComponent() {\n          return generateTitleAsReactComponent(type, tags.title, tags.titleAttributes, encode);\n        },\n        toString: function toString() {\n          return generateTitleAsString(type, tags.title, tags.titleAttributes, encode);\n        }\n      };\n    case ATTRIBUTE_NAMES.BODY:\n    case ATTRIBUTE_NAMES.HTML:\n      return {\n        toComponent: function toComponent() {\n          return convertElementAttributestoReactProps(tags);\n        },\n        toString: function toString() {\n          return generateElementAttributesAsString(tags);\n        }\n      };\n    default:\n      return {\n        toComponent: function toComponent() {\n          return generateTagsAsReactComponent(type, tags);\n        },\n        toString: function toString() {\n          return generateTagsAsString(type, tags, encode);\n        }\n      };\n  }\n};\nvar mapStateOnServer = function mapStateOnServer(_ref) {\n  var baseTag = _ref.baseTag,\n    bodyAttributes = _ref.bodyAttributes,\n    encode = _ref.encode,\n    htmlAttributes = _ref.htmlAttributes,\n    linkTags = _ref.linkTags,\n    metaTags = _ref.metaTags,\n    noscriptTags = _ref.noscriptTags,\n    scriptTags = _ref.scriptTags,\n    styleTags = _ref.styleTags,\n    _ref$title = _ref.title,\n    title = _ref$title === undefined ? \"\" : _ref$title,\n    titleAttributes = _ref.titleAttributes;\n  return {\n    base: getMethodsForTag(TAG_NAMES.BASE, baseTag, encode),\n    bodyAttributes: getMethodsForTag(ATTRIBUTE_NAMES.BODY, bodyAttributes, encode),\n    htmlAttributes: getMethodsForTag(ATTRIBUTE_NAMES.HTML, htmlAttributes, encode),\n    link: getMethodsForTag(TAG_NAMES.LINK, linkTags, encode),\n    meta: getMethodsForTag(TAG_NAMES.META, metaTags, encode),\n    noscript: getMethodsForTag(TAG_NAMES.NOSCRIPT, noscriptTags, encode),\n    script: getMethodsForTag(TAG_NAMES.SCRIPT, scriptTags, encode),\n    style: getMethodsForTag(TAG_NAMES.STYLE, styleTags, encode),\n    title: getMethodsForTag(TAG_NAMES.TITLE, {\n      title: title,\n      titleAttributes: titleAttributes\n    }, encode)\n  };\n};\nvar Helmet = function Helmet(Component) {\n  var _class, _temp;\n  return _temp = _class = function (_React$Component) {\n    inherits(HelmetWrapper, _React$Component);\n    function HelmetWrapper() {\n      classCallCheck(this, HelmetWrapper);\n      return possibleConstructorReturn(this, _React$Component.apply(this, arguments));\n    }\n    HelmetWrapper.prototype.shouldComponentUpdate = function shouldComponentUpdate(nextProps) {\n      return !isEqual(this.props, nextProps);\n    };\n    HelmetWrapper.prototype.mapNestedChildrenToProps = function mapNestedChildrenToProps(child, nestedChildren) {\n      if (!nestedChildren) {\n        return null;\n      }\n      switch (child.type) {\n        case TAG_NAMES.SCRIPT:\n        case TAG_NAMES.NOSCRIPT:\n          return {\n            innerHTML: nestedChildren\n          };\n        case TAG_NAMES.STYLE:\n          return {\n            cssText: nestedChildren\n          };\n      }\n      throw new Error(\"<\" + child.type + \" /> elements are self-closing and can not contain children. Refer to our API for more information.\");\n    };\n    HelmetWrapper.prototype.flattenArrayTypeChildren = function flattenArrayTypeChildren(_ref) {\n      var _babelHelpers$extends;\n      var child = _ref.child,\n        arrayTypeChildren = _ref.arrayTypeChildren,\n        newChildProps = _ref.newChildProps,\n        nestedChildren = _ref.nestedChildren;\n      return _extends({}, arrayTypeChildren, (_babelHelpers$extends = {}, _babelHelpers$extends[child.type] = [].concat(arrayTypeChildren[child.type] || [], [_extends({}, newChildProps, this.mapNestedChildrenToProps(child, nestedChildren))]), _babelHelpers$extends));\n    };\n    HelmetWrapper.prototype.mapObjectTypeChildren = function mapObjectTypeChildren(_ref2) {\n      var _babelHelpers$extends2, _babelHelpers$extends3;\n      var child = _ref2.child,\n        newProps = _ref2.newProps,\n        newChildProps = _ref2.newChildProps,\n        nestedChildren = _ref2.nestedChildren;\n      switch (child.type) {\n        case TAG_NAMES.TITLE:\n          return _extends({}, newProps, (_babelHelpers$extends2 = {}, _babelHelpers$extends2[child.type] = nestedChildren, _babelHelpers$extends2.titleAttributes = _extends({}, newChildProps), _babelHelpers$extends2));\n        case TAG_NAMES.BODY:\n          return _extends({}, newProps, {\n            bodyAttributes: _extends({}, newChildProps)\n          });\n        case TAG_NAMES.HTML:\n          return _extends({}, newProps, {\n            htmlAttributes: _extends({}, newChildProps)\n          });\n      }\n      return _extends({}, newProps, (_babelHelpers$extends3 = {}, _babelHelpers$extends3[child.type] = _extends({}, newChildProps), _babelHelpers$extends3));\n    };\n    HelmetWrapper.prototype.mapArrayTypeChildrenToProps = function mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n      var newFlattenedProps = _extends({}, newProps);\n      Object.keys(arrayTypeChildren).forEach(function (arrayChildName) {\n        var _babelHelpers$extends4;\n        newFlattenedProps = _extends({}, newFlattenedProps, (_babelHelpers$extends4 = {}, _babelHelpers$extends4[arrayChildName] = arrayTypeChildren[arrayChildName], _babelHelpers$extends4));\n      });\n      return newFlattenedProps;\n    };\n    HelmetWrapper.prototype.warnOnInvalidChildren = function warnOnInvalidChildren(child, nestedChildren) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (!VALID_TAG_NAMES.some(function (name) {\n          return child.type === name;\n        })) {\n          if (typeof child.type === \"function\") {\n            return warn(\"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.\");\n          }\n          return warn(\"Only elements types \" + VALID_TAG_NAMES.join(\", \") + \" are allowed. Helmet does not support rendering <\" + child.type + \"> elements. Refer to our API for more information.\");\n        }\n        if (nestedChildren && typeof nestedChildren !== \"string\" && (!Array.isArray(nestedChildren) || nestedChildren.some(function (nestedChild) {\n          return typeof nestedChild !== \"string\";\n        }))) {\n          throw new Error(\"Helmet expects a string as a child of <\" + child.type + \">. Did you forget to wrap your children in braces? ( <\" + child.type + \">{``}</\" + child.type + \"> ) Refer to our API for more information.\");\n        }\n      }\n      return true;\n    };\n    HelmetWrapper.prototype.mapChildrenToProps = function mapChildrenToProps(children, newProps) {\n      var _this2 = this;\n      var arrayTypeChildren = {};\n      React.Children.forEach(children, function (child) {\n        if (!child || !child.props) {\n          return;\n        }\n        var _child$props = child.props,\n          nestedChildren = _child$props.children,\n          childProps = objectWithoutProperties(_child$props, [\"children\"]);\n        var newChildProps = convertReactPropstoHtmlAttributes(childProps);\n        _this2.warnOnInvalidChildren(child, nestedChildren);\n        switch (child.type) {\n          case TAG_NAMES.LINK:\n          case TAG_NAMES.META:\n          case TAG_NAMES.NOSCRIPT:\n          case TAG_NAMES.SCRIPT:\n          case TAG_NAMES.STYLE:\n            arrayTypeChildren = _this2.flattenArrayTypeChildren({\n              child: child,\n              arrayTypeChildren: arrayTypeChildren,\n              newChildProps: newChildProps,\n              nestedChildren: nestedChildren\n            });\n            break;\n          default:\n            newProps = _this2.mapObjectTypeChildren({\n              child: child,\n              newProps: newProps,\n              newChildProps: newChildProps,\n              nestedChildren: nestedChildren\n            });\n            break;\n        }\n      });\n      newProps = this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n      return newProps;\n    };\n    HelmetWrapper.prototype.render = function render() {\n      var _props = this.props,\n        children = _props.children,\n        props = objectWithoutProperties(_props, [\"children\"]);\n      var newProps = _extends({}, props);\n      if (children) {\n        newProps = this.mapChildrenToProps(children, newProps);\n      }\n      return React.createElement(Component, newProps);\n    };\n    createClass(HelmetWrapper, null, [{\n      key: \"canUseDOM\",\n      // Component.peek comes from react-side-effect:\n      // For testing, you may use a static peek() method available on the returned component.\n      // It lets you get the current state without resetting the mounted instance stack.\n      // Don’t use it for anything other than testing.\n\n      /**\n       * @param {Object} base: {\"target\": \"_blank\", \"href\": \"http://mysite.com/\"}\n       * @param {Object} bodyAttributes: {\"className\": \"root\"}\n       * @param {String} defaultTitle: \"Default Title\"\n       * @param {Boolean} defer: true\n       * @param {Boolean} encodeSpecialCharacters: true\n       * @param {Object} htmlAttributes: {\"lang\": \"en\", \"amp\": undefined}\n       * @param {Array} link: [{\"rel\": \"canonical\", \"href\": \"http://mysite.com/example\"}]\n       * @param {Array} meta: [{\"name\": \"description\", \"content\": \"Test description\"}]\n       * @param {Array} noscript: [{\"innerHTML\": \"<img src='http://mysite.com/js/test.js'\"}]\n       * @param {Function} onChangeClientState: \"(newState) => console.log(newState)\"\n       * @param {Array} script: [{\"type\": \"text/javascript\", \"src\": \"http://mysite.com/js/test.js\"}]\n       * @param {Array} style: [{\"type\": \"text/css\", \"cssText\": \"div { display: block; color: blue; }\"}]\n       * @param {String} title: \"Title\"\n       * @param {Object} titleAttributes: {\"itemprop\": \"name\"}\n       * @param {String} titleTemplate: \"MySite.com - %s\"\n       */\n      set: function set$$1(canUseDOM) {\n        Component.canUseDOM = canUseDOM;\n      }\n    }]);\n    return HelmetWrapper;\n  }(React.Component), _class.propTypes = {\n    base: PropTypes.object,\n    bodyAttributes: PropTypes.object,\n    children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]),\n    defaultTitle: PropTypes.string,\n    defer: PropTypes.bool,\n    encodeSpecialCharacters: PropTypes.bool,\n    htmlAttributes: PropTypes.object,\n    link: PropTypes.arrayOf(PropTypes.object),\n    meta: PropTypes.arrayOf(PropTypes.object),\n    noscript: PropTypes.arrayOf(PropTypes.object),\n    onChangeClientState: PropTypes.func,\n    script: PropTypes.arrayOf(PropTypes.object),\n    style: PropTypes.arrayOf(PropTypes.object),\n    title: PropTypes.string,\n    titleAttributes: PropTypes.object,\n    titleTemplate: PropTypes.string\n  }, _class.defaultProps = {\n    defer: true,\n    encodeSpecialCharacters: true\n  }, _class.peek = Component.peek, _class.rewind = function () {\n    var mappedState = Component.rewind();\n    if (!mappedState) {\n      // provide fallback if mappedState is undefined\n      mappedState = mapStateOnServer({\n        baseTag: [],\n        bodyAttributes: {},\n        encodeSpecialCharacters: true,\n        htmlAttributes: {},\n        linkTags: [],\n        metaTags: [],\n        noscriptTags: [],\n        scriptTags: [],\n        styleTags: [],\n        title: \"\",\n        titleAttributes: {}\n      });\n    }\n    return mappedState;\n  }, _temp;\n};\nvar NullComponent = function NullComponent() {\n  return null;\n};\nvar HelmetSideEffects = withSideEffect(reducePropsToState, handleClientStateChange, mapStateOnServer)(NullComponent);\nvar HelmetExport = Helmet(HelmetSideEffects);\nHelmetExport.renderStatic = HelmetExport.rewind;\nexport default HelmetExport;\nexport { HelmetExport as Helmet };", "map": {"version": 3, "names": ["PropTypes", "withSideEffect", "isEqual", "React", "objectAssign", "ATTRIBUTE_NAMES", "BODY", "HTML", "TITLE", "TAG_NAMES", "BASE", "HEAD", "LINK", "META", "NOSCRIPT", "SCRIPT", "STYLE", "VALID_TAG_NAMES", "Object", "keys", "map", "name", "TAG_PROPERTIES", "CHARSET", "CSS_TEXT", "HREF", "HTTPEQUIV", "INNER_HTML", "ITEM_PROP", "NAME", "PROPERTY", "REL", "SRC", "TARGET", "REACT_TAG_MAP", "accesskey", "charset", "class", "contenteditable", "contextmenu", "itemprop", "tabindex", "HELMET_PROPS", "DEFAULT_TITLE", "DEFER", "ENCODE_SPECIAL_CHARACTERS", "ON_CHANGE_CLIENT_STATE", "TITLE_TEMPLATE", "HTML_TAG_MAP", "reduce", "obj", "key", "SELF_CLOSING_TAGS", "HELMET_ATTRIBUTE", "_typeof", "Symbol", "iterator", "constructor", "prototype", "classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "protoProps", "staticProps", "_extends", "assign", "arguments", "source", "hasOwnProperty", "call", "inherits", "subClass", "superClass", "create", "value", "setPrototypeOf", "__proto__", "objectWithoutProperties", "indexOf", "possibleConstructorReturn", "self", "ReferenceError", "encodeSpecialCharacters", "str", "encode", "undefined", "String", "replace", "getTitleFromPropsList", "propsList", "innermostTitle", "getInnermostProperty", "innermostTemplate", "Array", "isArray", "join", "innermostDefaultTitle", "getOnChangeClientState", "getAttributesFromPropsList", "tagType", "filter", "tagAttrs", "current", "getBaseTagFromPropsList", "primaryAttributes", "reverse", "innermostBaseTag", "tag", "<PERSON><PERSON><PERSON>", "lowerCaseAttributeKey", "toLowerCase", "concat", "getTagsFromPropsList", "tagName", "approvedSeenTags", "warn", "approvedTags", "instanceTags", "instanceSeenTags", "primaryAttributeKey", "for<PERSON>ach", "push", "tagUnion", "property", "reducePropsToState", "baseTag", "bodyAttributes", "defer", "htmlAttributes", "linkTags", "metaTags", "noscriptTags", "onChangeClientState", "scriptTags", "styleTags", "title", "titleAttributes", "rafPolyfill", "clock", "Date", "now", "callback", "currentTime", "setTimeout", "cafPolyfill", "id", "clearTimeout", "requestAnimationFrame", "window", "bind", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "global", "cancelAnimationFrame", "webkitCancelAnimationFrame", "mozCancelAnimationFrame", "msg", "console", "_helmet<PERSON><PERSON><PERSON>", "handleClientStateChange", "newState", "commitTagChanges", "cb", "updateAttributes", "updateTitle", "tagUpdates", "updateTags", "addedTags", "removedTags", "_tagUpdates$tagType", "newTags", "oldTags", "flattenArray", "possible<PERSON><PERSON>y", "attributes", "document", "elementTag", "getElementsByTagName", "helmetAttributeString", "getAttribute", "helmetAttributes", "split", "attributesToRemove", "<PERSON><PERSON><PERSON><PERSON>", "attribute", "setAttribute", "indexToSave", "splice", "_i", "removeAttribute", "type", "tags", "headElement", "head", "querySelector", "tagNodes", "querySelectorAll", "slice", "indexToDelete", "newElement", "createElement", "innerHTML", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "some", "existingTag", "index", "isEqualNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "generateElementAttributesAsString", "attr", "generateTitleAsString", "attributeString", "flattenedTitle", "generateTagsAsString", "attributeHtml", "string", "tagContent", "isSelfClosing", "convertElementAttributestoReactProps", "initProps", "convertReactPropstoHtmlAttributes", "initAttributes", "generateTitleAsReactComponent", "_initProps", "generateTagsAsReactComponent", "_mappedTag", "mappedTag", "mappedAttribute", "content", "dangerouslySetInnerHTML", "__html", "getMethodsForTag", "toComponent", "toString", "mapStateOnServer", "_ref", "_ref$title", "base", "link", "meta", "noscript", "script", "style", "<PERSON><PERSON><PERSON>", "Component", "_class", "_temp", "_React$Component", "HelmetWrapper", "apply", "shouldComponentUpdate", "nextProps", "mapNestedChildrenToProps", "child", "nested<PERSON><PERSON><PERSON><PERSON>", "Error", "flattenArrayTypeChildren", "_babelHelpers$extends", "arrayTypeChildren", "newChildProps", "mapObjectTypeChildren", "_ref2", "_babelHelpers$extends2", "_babelHelpers$extends3", "newProps", "mapArrayTypeChildrenToProps", "newFlattenedProps", "arrayChildName", "_babelHelpers$extends4", "warnOnInvalidChildren", "process", "env", "NODE_ENV", "nested<PERSON><PERSON><PERSON>", "mapChildrenToProps", "children", "_this2", "Children", "_child$props", "childProps", "render", "_props", "set", "set$$1", "canUseDOM", "propTypes", "object", "oneOfType", "arrayOf", "node", "defaultTitle", "bool", "func", "titleTemplate", "defaultProps", "peek", "rewind", "mappedState", "NullComponent", "HelmetSideEffects", "HelmetExport", "renderStatic"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/react-helmet/es/Helmet.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport withSideEffect from 'react-side-effect';\nimport isEqual from 'react-fast-compare';\nimport React from 'react';\nimport objectAssign from 'object-assign';\n\nvar ATTRIBUTE_NAMES = {\n    BODY: \"bodyAttributes\",\n    HTML: \"htmlAttributes\",\n    TITLE: \"titleAttributes\"\n};\n\nvar TAG_NAMES = {\n    BASE: \"base\",\n    BODY: \"body\",\n    HEAD: \"head\",\n    HTML: \"html\",\n    LINK: \"link\",\n    META: \"meta\",\n    NOSCRIPT: \"noscript\",\n    SCRIPT: \"script\",\n    STYLE: \"style\",\n    TITLE: \"title\"\n};\n\nvar VALID_TAG_NAMES = Object.keys(TAG_NAMES).map(function (name) {\n    return TAG_NAMES[name];\n});\n\nvar TAG_PROPERTIES = {\n    CHARSET: \"charset\",\n    CSS_TEXT: \"cssText\",\n    HREF: \"href\",\n    HTTPEQUIV: \"http-equiv\",\n    INNER_HTML: \"innerHTML\",\n    ITEM_PROP: \"itemprop\",\n    NAME: \"name\",\n    PROPERTY: \"property\",\n    REL: \"rel\",\n    SRC: \"src\",\n    TARGET: \"target\"\n};\n\nvar REACT_TAG_MAP = {\n    accesskey: \"accessKey\",\n    charset: \"charSet\",\n    class: \"className\",\n    contenteditable: \"contentEditable\",\n    contextmenu: \"contextMenu\",\n    \"http-equiv\": \"httpEquiv\",\n    itemprop: \"itemProp\",\n    tabindex: \"tabIndex\"\n};\n\nvar HELMET_PROPS = {\n    DEFAULT_TITLE: \"defaultTitle\",\n    DEFER: \"defer\",\n    ENCODE_SPECIAL_CHARACTERS: \"encodeSpecialCharacters\",\n    ON_CHANGE_CLIENT_STATE: \"onChangeClientState\",\n    TITLE_TEMPLATE: \"titleTemplate\"\n};\n\nvar HTML_TAG_MAP = Object.keys(REACT_TAG_MAP).reduce(function (obj, key) {\n    obj[REACT_TAG_MAP[key]] = key;\n    return obj;\n}, {});\n\nvar SELF_CLOSING_TAGS = [TAG_NAMES.NOSCRIPT, TAG_NAMES.SCRIPT, TAG_NAMES.STYLE];\n\nvar HELMET_ATTRIBUTE = \"data-react-helmet\";\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\nvar inherits = function (subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n};\n\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n\n  return target;\n};\n\nvar possibleConstructorReturn = function (self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n};\n\nvar encodeSpecialCharacters = function encodeSpecialCharacters(str) {\n    var encode = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n\n    if (encode === false) {\n        return String(str);\n    }\n\n    return String(str).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#x27;\");\n};\n\nvar getTitleFromPropsList = function getTitleFromPropsList(propsList) {\n    var innermostTitle = getInnermostProperty(propsList, TAG_NAMES.TITLE);\n    var innermostTemplate = getInnermostProperty(propsList, HELMET_PROPS.TITLE_TEMPLATE);\n\n    if (innermostTemplate && innermostTitle) {\n        // use function arg to avoid need to escape $ characters\n        return innermostTemplate.replace(/%s/g, function () {\n            return Array.isArray(innermostTitle) ? innermostTitle.join(\"\") : innermostTitle;\n        });\n    }\n\n    var innermostDefaultTitle = getInnermostProperty(propsList, HELMET_PROPS.DEFAULT_TITLE);\n\n    return innermostTitle || innermostDefaultTitle || undefined;\n};\n\nvar getOnChangeClientState = function getOnChangeClientState(propsList) {\n    return getInnermostProperty(propsList, HELMET_PROPS.ON_CHANGE_CLIENT_STATE) || function () {};\n};\n\nvar getAttributesFromPropsList = function getAttributesFromPropsList(tagType, propsList) {\n    return propsList.filter(function (props) {\n        return typeof props[tagType] !== \"undefined\";\n    }).map(function (props) {\n        return props[tagType];\n    }).reduce(function (tagAttrs, current) {\n        return _extends({}, tagAttrs, current);\n    }, {});\n};\n\nvar getBaseTagFromPropsList = function getBaseTagFromPropsList(primaryAttributes, propsList) {\n    return propsList.filter(function (props) {\n        return typeof props[TAG_NAMES.BASE] !== \"undefined\";\n    }).map(function (props) {\n        return props[TAG_NAMES.BASE];\n    }).reverse().reduce(function (innermostBaseTag, tag) {\n        if (!innermostBaseTag.length) {\n            var keys = Object.keys(tag);\n\n            for (var i = 0; i < keys.length; i++) {\n                var attributeKey = keys[i];\n                var lowerCaseAttributeKey = attributeKey.toLowerCase();\n\n                if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && tag[lowerCaseAttributeKey]) {\n                    return innermostBaseTag.concat(tag);\n                }\n            }\n        }\n\n        return innermostBaseTag;\n    }, []);\n};\n\nvar getTagsFromPropsList = function getTagsFromPropsList(tagName, primaryAttributes, propsList) {\n    // Calculate list of tags, giving priority innermost component (end of the propslist)\n    var approvedSeenTags = {};\n\n    return propsList.filter(function (props) {\n        if (Array.isArray(props[tagName])) {\n            return true;\n        }\n        if (typeof props[tagName] !== \"undefined\") {\n            warn(\"Helmet: \" + tagName + \" should be of type \\\"Array\\\". Instead found type \\\"\" + _typeof(props[tagName]) + \"\\\"\");\n        }\n        return false;\n    }).map(function (props) {\n        return props[tagName];\n    }).reverse().reduce(function (approvedTags, instanceTags) {\n        var instanceSeenTags = {};\n\n        instanceTags.filter(function (tag) {\n            var primaryAttributeKey = void 0;\n            var keys = Object.keys(tag);\n            for (var i = 0; i < keys.length; i++) {\n                var attributeKey = keys[i];\n                var lowerCaseAttributeKey = attributeKey.toLowerCase();\n\n                // Special rule with link tags, since rel and href are both primary tags, rel takes priority\n                if (primaryAttributes.indexOf(lowerCaseAttributeKey) !== -1 && !(primaryAttributeKey === TAG_PROPERTIES.REL && tag[primaryAttributeKey].toLowerCase() === \"canonical\") && !(lowerCaseAttributeKey === TAG_PROPERTIES.REL && tag[lowerCaseAttributeKey].toLowerCase() === \"stylesheet\")) {\n                    primaryAttributeKey = lowerCaseAttributeKey;\n                }\n                // Special case for innerHTML which doesn't work lowercased\n                if (primaryAttributes.indexOf(attributeKey) !== -1 && (attributeKey === TAG_PROPERTIES.INNER_HTML || attributeKey === TAG_PROPERTIES.CSS_TEXT || attributeKey === TAG_PROPERTIES.ITEM_PROP)) {\n                    primaryAttributeKey = attributeKey;\n                }\n            }\n\n            if (!primaryAttributeKey || !tag[primaryAttributeKey]) {\n                return false;\n            }\n\n            var value = tag[primaryAttributeKey].toLowerCase();\n\n            if (!approvedSeenTags[primaryAttributeKey]) {\n                approvedSeenTags[primaryAttributeKey] = {};\n            }\n\n            if (!instanceSeenTags[primaryAttributeKey]) {\n                instanceSeenTags[primaryAttributeKey] = {};\n            }\n\n            if (!approvedSeenTags[primaryAttributeKey][value]) {\n                instanceSeenTags[primaryAttributeKey][value] = true;\n                return true;\n            }\n\n            return false;\n        }).reverse().forEach(function (tag) {\n            return approvedTags.push(tag);\n        });\n\n        // Update seen tags with tags from this instance\n        var keys = Object.keys(instanceSeenTags);\n        for (var i = 0; i < keys.length; i++) {\n            var attributeKey = keys[i];\n            var tagUnion = objectAssign({}, approvedSeenTags[attributeKey], instanceSeenTags[attributeKey]);\n\n            approvedSeenTags[attributeKey] = tagUnion;\n        }\n\n        return approvedTags;\n    }, []).reverse();\n};\n\nvar getInnermostProperty = function getInnermostProperty(propsList, property) {\n    for (var i = propsList.length - 1; i >= 0; i--) {\n        var props = propsList[i];\n\n        if (props.hasOwnProperty(property)) {\n            return props[property];\n        }\n    }\n\n    return null;\n};\n\nvar reducePropsToState = function reducePropsToState(propsList) {\n    return {\n        baseTag: getBaseTagFromPropsList([TAG_PROPERTIES.HREF, TAG_PROPERTIES.TARGET], propsList),\n        bodyAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.BODY, propsList),\n        defer: getInnermostProperty(propsList, HELMET_PROPS.DEFER),\n        encode: getInnermostProperty(propsList, HELMET_PROPS.ENCODE_SPECIAL_CHARACTERS),\n        htmlAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.HTML, propsList),\n        linkTags: getTagsFromPropsList(TAG_NAMES.LINK, [TAG_PROPERTIES.REL, TAG_PROPERTIES.HREF], propsList),\n        metaTags: getTagsFromPropsList(TAG_NAMES.META, [TAG_PROPERTIES.NAME, TAG_PROPERTIES.CHARSET, TAG_PROPERTIES.HTTPEQUIV, TAG_PROPERTIES.PROPERTY, TAG_PROPERTIES.ITEM_PROP], propsList),\n        noscriptTags: getTagsFromPropsList(TAG_NAMES.NOSCRIPT, [TAG_PROPERTIES.INNER_HTML], propsList),\n        onChangeClientState: getOnChangeClientState(propsList),\n        scriptTags: getTagsFromPropsList(TAG_NAMES.SCRIPT, [TAG_PROPERTIES.SRC, TAG_PROPERTIES.INNER_HTML], propsList),\n        styleTags: getTagsFromPropsList(TAG_NAMES.STYLE, [TAG_PROPERTIES.CSS_TEXT], propsList),\n        title: getTitleFromPropsList(propsList),\n        titleAttributes: getAttributesFromPropsList(ATTRIBUTE_NAMES.TITLE, propsList)\n    };\n};\n\nvar rafPolyfill = function () {\n    var clock = Date.now();\n\n    return function (callback) {\n        var currentTime = Date.now();\n\n        if (currentTime - clock > 16) {\n            clock = currentTime;\n            callback(currentTime);\n        } else {\n            setTimeout(function () {\n                rafPolyfill(callback);\n            }, 0);\n        }\n    };\n}();\n\nvar cafPolyfill = function cafPolyfill(id) {\n    return clearTimeout(id);\n};\n\nvar requestAnimationFrame = typeof window !== \"undefined\" ? window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || rafPolyfill : global.requestAnimationFrame || rafPolyfill;\n\nvar cancelAnimationFrame = typeof window !== \"undefined\" ? window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || cafPolyfill : global.cancelAnimationFrame || cafPolyfill;\n\nvar warn = function warn(msg) {\n    return console && typeof console.warn === \"function\" && console.warn(msg);\n};\n\nvar _helmetCallback = null;\n\nvar handleClientStateChange = function handleClientStateChange(newState) {\n    if (_helmetCallback) {\n        cancelAnimationFrame(_helmetCallback);\n    }\n\n    if (newState.defer) {\n        _helmetCallback = requestAnimationFrame(function () {\n            commitTagChanges(newState, function () {\n                _helmetCallback = null;\n            });\n        });\n    } else {\n        commitTagChanges(newState);\n        _helmetCallback = null;\n    }\n};\n\nvar commitTagChanges = function commitTagChanges(newState, cb) {\n    var baseTag = newState.baseTag,\n        bodyAttributes = newState.bodyAttributes,\n        htmlAttributes = newState.htmlAttributes,\n        linkTags = newState.linkTags,\n        metaTags = newState.metaTags,\n        noscriptTags = newState.noscriptTags,\n        onChangeClientState = newState.onChangeClientState,\n        scriptTags = newState.scriptTags,\n        styleTags = newState.styleTags,\n        title = newState.title,\n        titleAttributes = newState.titleAttributes;\n\n    updateAttributes(TAG_NAMES.BODY, bodyAttributes);\n    updateAttributes(TAG_NAMES.HTML, htmlAttributes);\n\n    updateTitle(title, titleAttributes);\n\n    var tagUpdates = {\n        baseTag: updateTags(TAG_NAMES.BASE, baseTag),\n        linkTags: updateTags(TAG_NAMES.LINK, linkTags),\n        metaTags: updateTags(TAG_NAMES.META, metaTags),\n        noscriptTags: updateTags(TAG_NAMES.NOSCRIPT, noscriptTags),\n        scriptTags: updateTags(TAG_NAMES.SCRIPT, scriptTags),\n        styleTags: updateTags(TAG_NAMES.STYLE, styleTags)\n    };\n\n    var addedTags = {};\n    var removedTags = {};\n\n    Object.keys(tagUpdates).forEach(function (tagType) {\n        var _tagUpdates$tagType = tagUpdates[tagType],\n            newTags = _tagUpdates$tagType.newTags,\n            oldTags = _tagUpdates$tagType.oldTags;\n\n\n        if (newTags.length) {\n            addedTags[tagType] = newTags;\n        }\n        if (oldTags.length) {\n            removedTags[tagType] = tagUpdates[tagType].oldTags;\n        }\n    });\n\n    cb && cb();\n\n    onChangeClientState(newState, addedTags, removedTags);\n};\n\nvar flattenArray = function flattenArray(possibleArray) {\n    return Array.isArray(possibleArray) ? possibleArray.join(\"\") : possibleArray;\n};\n\nvar updateTitle = function updateTitle(title, attributes) {\n    if (typeof title !== \"undefined\" && document.title !== title) {\n        document.title = flattenArray(title);\n    }\n\n    updateAttributes(TAG_NAMES.TITLE, attributes);\n};\n\nvar updateAttributes = function updateAttributes(tagName, attributes) {\n    var elementTag = document.getElementsByTagName(tagName)[0];\n\n    if (!elementTag) {\n        return;\n    }\n\n    var helmetAttributeString = elementTag.getAttribute(HELMET_ATTRIBUTE);\n    var helmetAttributes = helmetAttributeString ? helmetAttributeString.split(\",\") : [];\n    var attributesToRemove = [].concat(helmetAttributes);\n    var attributeKeys = Object.keys(attributes);\n\n    for (var i = 0; i < attributeKeys.length; i++) {\n        var attribute = attributeKeys[i];\n        var value = attributes[attribute] || \"\";\n\n        if (elementTag.getAttribute(attribute) !== value) {\n            elementTag.setAttribute(attribute, value);\n        }\n\n        if (helmetAttributes.indexOf(attribute) === -1) {\n            helmetAttributes.push(attribute);\n        }\n\n        var indexToSave = attributesToRemove.indexOf(attribute);\n        if (indexToSave !== -1) {\n            attributesToRemove.splice(indexToSave, 1);\n        }\n    }\n\n    for (var _i = attributesToRemove.length - 1; _i >= 0; _i--) {\n        elementTag.removeAttribute(attributesToRemove[_i]);\n    }\n\n    if (helmetAttributes.length === attributesToRemove.length) {\n        elementTag.removeAttribute(HELMET_ATTRIBUTE);\n    } else if (elementTag.getAttribute(HELMET_ATTRIBUTE) !== attributeKeys.join(\",\")) {\n        elementTag.setAttribute(HELMET_ATTRIBUTE, attributeKeys.join(\",\"));\n    }\n};\n\nvar updateTags = function updateTags(type, tags) {\n    var headElement = document.head || document.querySelector(TAG_NAMES.HEAD);\n    var tagNodes = headElement.querySelectorAll(type + \"[\" + HELMET_ATTRIBUTE + \"]\");\n    var oldTags = Array.prototype.slice.call(tagNodes);\n    var newTags = [];\n    var indexToDelete = void 0;\n\n    if (tags && tags.length) {\n        tags.forEach(function (tag) {\n            var newElement = document.createElement(type);\n\n            for (var attribute in tag) {\n                if (tag.hasOwnProperty(attribute)) {\n                    if (attribute === TAG_PROPERTIES.INNER_HTML) {\n                        newElement.innerHTML = tag.innerHTML;\n                    } else if (attribute === TAG_PROPERTIES.CSS_TEXT) {\n                        if (newElement.styleSheet) {\n                            newElement.styleSheet.cssText = tag.cssText;\n                        } else {\n                            newElement.appendChild(document.createTextNode(tag.cssText));\n                        }\n                    } else {\n                        var value = typeof tag[attribute] === \"undefined\" ? \"\" : tag[attribute];\n                        newElement.setAttribute(attribute, value);\n                    }\n                }\n            }\n\n            newElement.setAttribute(HELMET_ATTRIBUTE, \"true\");\n\n            // Remove a duplicate tag from domTagstoRemove, so it isn't cleared.\n            if (oldTags.some(function (existingTag, index) {\n                indexToDelete = index;\n                return newElement.isEqualNode(existingTag);\n            })) {\n                oldTags.splice(indexToDelete, 1);\n            } else {\n                newTags.push(newElement);\n            }\n        });\n    }\n\n    oldTags.forEach(function (tag) {\n        return tag.parentNode.removeChild(tag);\n    });\n    newTags.forEach(function (tag) {\n        return headElement.appendChild(tag);\n    });\n\n    return {\n        oldTags: oldTags,\n        newTags: newTags\n    };\n};\n\nvar generateElementAttributesAsString = function generateElementAttributesAsString(attributes) {\n    return Object.keys(attributes).reduce(function (str, key) {\n        var attr = typeof attributes[key] !== \"undefined\" ? key + \"=\\\"\" + attributes[key] + \"\\\"\" : \"\" + key;\n        return str ? str + \" \" + attr : attr;\n    }, \"\");\n};\n\nvar generateTitleAsString = function generateTitleAsString(type, title, attributes, encode) {\n    var attributeString = generateElementAttributesAsString(attributes);\n    var flattenedTitle = flattenArray(title);\n    return attributeString ? \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\" \" + attributeString + \">\" + encodeSpecialCharacters(flattenedTitle, encode) + \"</\" + type + \">\" : \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\">\" + encodeSpecialCharacters(flattenedTitle, encode) + \"</\" + type + \">\";\n};\n\nvar generateTagsAsString = function generateTagsAsString(type, tags, encode) {\n    return tags.reduce(function (str, tag) {\n        var attributeHtml = Object.keys(tag).filter(function (attribute) {\n            return !(attribute === TAG_PROPERTIES.INNER_HTML || attribute === TAG_PROPERTIES.CSS_TEXT);\n        }).reduce(function (string, attribute) {\n            var attr = typeof tag[attribute] === \"undefined\" ? attribute : attribute + \"=\\\"\" + encodeSpecialCharacters(tag[attribute], encode) + \"\\\"\";\n            return string ? string + \" \" + attr : attr;\n        }, \"\");\n\n        var tagContent = tag.innerHTML || tag.cssText || \"\";\n\n        var isSelfClosing = SELF_CLOSING_TAGS.indexOf(type) === -1;\n\n        return str + \"<\" + type + \" \" + HELMET_ATTRIBUTE + \"=\\\"true\\\" \" + attributeHtml + (isSelfClosing ? \"/>\" : \">\" + tagContent + \"</\" + type + \">\");\n    }, \"\");\n};\n\nvar convertElementAttributestoReactProps = function convertElementAttributestoReactProps(attributes) {\n    var initProps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    return Object.keys(attributes).reduce(function (obj, key) {\n        obj[REACT_TAG_MAP[key] || key] = attributes[key];\n        return obj;\n    }, initProps);\n};\n\nvar convertReactPropstoHtmlAttributes = function convertReactPropstoHtmlAttributes(props) {\n    var initAttributes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    return Object.keys(props).reduce(function (obj, key) {\n        obj[HTML_TAG_MAP[key] || key] = props[key];\n        return obj;\n    }, initAttributes);\n};\n\nvar generateTitleAsReactComponent = function generateTitleAsReactComponent(type, title, attributes) {\n    var _initProps;\n\n    // assigning into an array to define toString function on it\n    var initProps = (_initProps = {\n        key: title\n    }, _initProps[HELMET_ATTRIBUTE] = true, _initProps);\n    var props = convertElementAttributestoReactProps(attributes, initProps);\n\n    return [React.createElement(TAG_NAMES.TITLE, props, title)];\n};\n\nvar generateTagsAsReactComponent = function generateTagsAsReactComponent(type, tags) {\n    return tags.map(function (tag, i) {\n        var _mappedTag;\n\n        var mappedTag = (_mappedTag = {\n            key: i\n        }, _mappedTag[HELMET_ATTRIBUTE] = true, _mappedTag);\n\n        Object.keys(tag).forEach(function (attribute) {\n            var mappedAttribute = REACT_TAG_MAP[attribute] || attribute;\n\n            if (mappedAttribute === TAG_PROPERTIES.INNER_HTML || mappedAttribute === TAG_PROPERTIES.CSS_TEXT) {\n                var content = tag.innerHTML || tag.cssText;\n                mappedTag.dangerouslySetInnerHTML = { __html: content };\n            } else {\n                mappedTag[mappedAttribute] = tag[attribute];\n            }\n        });\n\n        return React.createElement(type, mappedTag);\n    });\n};\n\nvar getMethodsForTag = function getMethodsForTag(type, tags, encode) {\n    switch (type) {\n        case TAG_NAMES.TITLE:\n            return {\n                toComponent: function toComponent() {\n                    return generateTitleAsReactComponent(type, tags.title, tags.titleAttributes, encode);\n                },\n                toString: function toString() {\n                    return generateTitleAsString(type, tags.title, tags.titleAttributes, encode);\n                }\n            };\n        case ATTRIBUTE_NAMES.BODY:\n        case ATTRIBUTE_NAMES.HTML:\n            return {\n                toComponent: function toComponent() {\n                    return convertElementAttributestoReactProps(tags);\n                },\n                toString: function toString() {\n                    return generateElementAttributesAsString(tags);\n                }\n            };\n        default:\n            return {\n                toComponent: function toComponent() {\n                    return generateTagsAsReactComponent(type, tags);\n                },\n                toString: function toString() {\n                    return generateTagsAsString(type, tags, encode);\n                }\n            };\n    }\n};\n\nvar mapStateOnServer = function mapStateOnServer(_ref) {\n    var baseTag = _ref.baseTag,\n        bodyAttributes = _ref.bodyAttributes,\n        encode = _ref.encode,\n        htmlAttributes = _ref.htmlAttributes,\n        linkTags = _ref.linkTags,\n        metaTags = _ref.metaTags,\n        noscriptTags = _ref.noscriptTags,\n        scriptTags = _ref.scriptTags,\n        styleTags = _ref.styleTags,\n        _ref$title = _ref.title,\n        title = _ref$title === undefined ? \"\" : _ref$title,\n        titleAttributes = _ref.titleAttributes;\n    return {\n        base: getMethodsForTag(TAG_NAMES.BASE, baseTag, encode),\n        bodyAttributes: getMethodsForTag(ATTRIBUTE_NAMES.BODY, bodyAttributes, encode),\n        htmlAttributes: getMethodsForTag(ATTRIBUTE_NAMES.HTML, htmlAttributes, encode),\n        link: getMethodsForTag(TAG_NAMES.LINK, linkTags, encode),\n        meta: getMethodsForTag(TAG_NAMES.META, metaTags, encode),\n        noscript: getMethodsForTag(TAG_NAMES.NOSCRIPT, noscriptTags, encode),\n        script: getMethodsForTag(TAG_NAMES.SCRIPT, scriptTags, encode),\n        style: getMethodsForTag(TAG_NAMES.STYLE, styleTags, encode),\n        title: getMethodsForTag(TAG_NAMES.TITLE, { title: title, titleAttributes: titleAttributes }, encode)\n    };\n};\n\nvar Helmet = function Helmet(Component) {\n    var _class, _temp;\n\n    return _temp = _class = function (_React$Component) {\n        inherits(HelmetWrapper, _React$Component);\n\n        function HelmetWrapper() {\n            classCallCheck(this, HelmetWrapper);\n            return possibleConstructorReturn(this, _React$Component.apply(this, arguments));\n        }\n\n        HelmetWrapper.prototype.shouldComponentUpdate = function shouldComponentUpdate(nextProps) {\n            return !isEqual(this.props, nextProps);\n        };\n\n        HelmetWrapper.prototype.mapNestedChildrenToProps = function mapNestedChildrenToProps(child, nestedChildren) {\n            if (!nestedChildren) {\n                return null;\n            }\n\n            switch (child.type) {\n                case TAG_NAMES.SCRIPT:\n                case TAG_NAMES.NOSCRIPT:\n                    return {\n                        innerHTML: nestedChildren\n                    };\n\n                case TAG_NAMES.STYLE:\n                    return {\n                        cssText: nestedChildren\n                    };\n            }\n\n            throw new Error(\"<\" + child.type + \" /> elements are self-closing and can not contain children. Refer to our API for more information.\");\n        };\n\n        HelmetWrapper.prototype.flattenArrayTypeChildren = function flattenArrayTypeChildren(_ref) {\n            var _babelHelpers$extends;\n\n            var child = _ref.child,\n                arrayTypeChildren = _ref.arrayTypeChildren,\n                newChildProps = _ref.newChildProps,\n                nestedChildren = _ref.nestedChildren;\n\n            return _extends({}, arrayTypeChildren, (_babelHelpers$extends = {}, _babelHelpers$extends[child.type] = [].concat(arrayTypeChildren[child.type] || [], [_extends({}, newChildProps, this.mapNestedChildrenToProps(child, nestedChildren))]), _babelHelpers$extends));\n        };\n\n        HelmetWrapper.prototype.mapObjectTypeChildren = function mapObjectTypeChildren(_ref2) {\n            var _babelHelpers$extends2, _babelHelpers$extends3;\n\n            var child = _ref2.child,\n                newProps = _ref2.newProps,\n                newChildProps = _ref2.newChildProps,\n                nestedChildren = _ref2.nestedChildren;\n\n            switch (child.type) {\n                case TAG_NAMES.TITLE:\n                    return _extends({}, newProps, (_babelHelpers$extends2 = {}, _babelHelpers$extends2[child.type] = nestedChildren, _babelHelpers$extends2.titleAttributes = _extends({}, newChildProps), _babelHelpers$extends2));\n\n                case TAG_NAMES.BODY:\n                    return _extends({}, newProps, {\n                        bodyAttributes: _extends({}, newChildProps)\n                    });\n\n                case TAG_NAMES.HTML:\n                    return _extends({}, newProps, {\n                        htmlAttributes: _extends({}, newChildProps)\n                    });\n            }\n\n            return _extends({}, newProps, (_babelHelpers$extends3 = {}, _babelHelpers$extends3[child.type] = _extends({}, newChildProps), _babelHelpers$extends3));\n        };\n\n        HelmetWrapper.prototype.mapArrayTypeChildrenToProps = function mapArrayTypeChildrenToProps(arrayTypeChildren, newProps) {\n            var newFlattenedProps = _extends({}, newProps);\n\n            Object.keys(arrayTypeChildren).forEach(function (arrayChildName) {\n                var _babelHelpers$extends4;\n\n                newFlattenedProps = _extends({}, newFlattenedProps, (_babelHelpers$extends4 = {}, _babelHelpers$extends4[arrayChildName] = arrayTypeChildren[arrayChildName], _babelHelpers$extends4));\n            });\n\n            return newFlattenedProps;\n        };\n\n        HelmetWrapper.prototype.warnOnInvalidChildren = function warnOnInvalidChildren(child, nestedChildren) {\n            if (process.env.NODE_ENV !== \"production\") {\n                if (!VALID_TAG_NAMES.some(function (name) {\n                    return child.type === name;\n                })) {\n                    if (typeof child.type === \"function\") {\n                        return warn(\"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.\");\n                    }\n\n                    return warn(\"Only elements types \" + VALID_TAG_NAMES.join(\", \") + \" are allowed. Helmet does not support rendering <\" + child.type + \"> elements. Refer to our API for more information.\");\n                }\n\n                if (nestedChildren && typeof nestedChildren !== \"string\" && (!Array.isArray(nestedChildren) || nestedChildren.some(function (nestedChild) {\n                    return typeof nestedChild !== \"string\";\n                }))) {\n                    throw new Error(\"Helmet expects a string as a child of <\" + child.type + \">. Did you forget to wrap your children in braces? ( <\" + child.type + \">{``}</\" + child.type + \"> ) Refer to our API for more information.\");\n                }\n            }\n\n            return true;\n        };\n\n        HelmetWrapper.prototype.mapChildrenToProps = function mapChildrenToProps(children, newProps) {\n            var _this2 = this;\n\n            var arrayTypeChildren = {};\n\n            React.Children.forEach(children, function (child) {\n                if (!child || !child.props) {\n                    return;\n                }\n\n                var _child$props = child.props,\n                    nestedChildren = _child$props.children,\n                    childProps = objectWithoutProperties(_child$props, [\"children\"]);\n\n                var newChildProps = convertReactPropstoHtmlAttributes(childProps);\n\n                _this2.warnOnInvalidChildren(child, nestedChildren);\n\n                switch (child.type) {\n                    case TAG_NAMES.LINK:\n                    case TAG_NAMES.META:\n                    case TAG_NAMES.NOSCRIPT:\n                    case TAG_NAMES.SCRIPT:\n                    case TAG_NAMES.STYLE:\n                        arrayTypeChildren = _this2.flattenArrayTypeChildren({\n                            child: child,\n                            arrayTypeChildren: arrayTypeChildren,\n                            newChildProps: newChildProps,\n                            nestedChildren: nestedChildren\n                        });\n                        break;\n\n                    default:\n                        newProps = _this2.mapObjectTypeChildren({\n                            child: child,\n                            newProps: newProps,\n                            newChildProps: newChildProps,\n                            nestedChildren: nestedChildren\n                        });\n                        break;\n                }\n            });\n\n            newProps = this.mapArrayTypeChildrenToProps(arrayTypeChildren, newProps);\n            return newProps;\n        };\n\n        HelmetWrapper.prototype.render = function render() {\n            var _props = this.props,\n                children = _props.children,\n                props = objectWithoutProperties(_props, [\"children\"]);\n\n            var newProps = _extends({}, props);\n\n            if (children) {\n                newProps = this.mapChildrenToProps(children, newProps);\n            }\n\n            return React.createElement(Component, newProps);\n        };\n\n        createClass(HelmetWrapper, null, [{\n            key: \"canUseDOM\",\n\n\n            // Component.peek comes from react-side-effect:\n            // For testing, you may use a static peek() method available on the returned component.\n            // It lets you get the current state without resetting the mounted instance stack.\n            // Don’t use it for anything other than testing.\n\n            /**\n             * @param {Object} base: {\"target\": \"_blank\", \"href\": \"http://mysite.com/\"}\n             * @param {Object} bodyAttributes: {\"className\": \"root\"}\n             * @param {String} defaultTitle: \"Default Title\"\n             * @param {Boolean} defer: true\n             * @param {Boolean} encodeSpecialCharacters: true\n             * @param {Object} htmlAttributes: {\"lang\": \"en\", \"amp\": undefined}\n             * @param {Array} link: [{\"rel\": \"canonical\", \"href\": \"http://mysite.com/example\"}]\n             * @param {Array} meta: [{\"name\": \"description\", \"content\": \"Test description\"}]\n             * @param {Array} noscript: [{\"innerHTML\": \"<img src='http://mysite.com/js/test.js'\"}]\n             * @param {Function} onChangeClientState: \"(newState) => console.log(newState)\"\n             * @param {Array} script: [{\"type\": \"text/javascript\", \"src\": \"http://mysite.com/js/test.js\"}]\n             * @param {Array} style: [{\"type\": \"text/css\", \"cssText\": \"div { display: block; color: blue; }\"}]\n             * @param {String} title: \"Title\"\n             * @param {Object} titleAttributes: {\"itemprop\": \"name\"}\n             * @param {String} titleTemplate: \"MySite.com - %s\"\n             */\n            set: function set$$1(canUseDOM) {\n                Component.canUseDOM = canUseDOM;\n            }\n        }]);\n        return HelmetWrapper;\n    }(React.Component), _class.propTypes = {\n        base: PropTypes.object,\n        bodyAttributes: PropTypes.object,\n        children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.node]),\n        defaultTitle: PropTypes.string,\n        defer: PropTypes.bool,\n        encodeSpecialCharacters: PropTypes.bool,\n        htmlAttributes: PropTypes.object,\n        link: PropTypes.arrayOf(PropTypes.object),\n        meta: PropTypes.arrayOf(PropTypes.object),\n        noscript: PropTypes.arrayOf(PropTypes.object),\n        onChangeClientState: PropTypes.func,\n        script: PropTypes.arrayOf(PropTypes.object),\n        style: PropTypes.arrayOf(PropTypes.object),\n        title: PropTypes.string,\n        titleAttributes: PropTypes.object,\n        titleTemplate: PropTypes.string\n    }, _class.defaultProps = {\n        defer: true,\n        encodeSpecialCharacters: true\n    }, _class.peek = Component.peek, _class.rewind = function () {\n        var mappedState = Component.rewind();\n        if (!mappedState) {\n            // provide fallback if mappedState is undefined\n            mappedState = mapStateOnServer({\n                baseTag: [],\n                bodyAttributes: {},\n                encodeSpecialCharacters: true,\n                htmlAttributes: {},\n                linkTags: [],\n                metaTags: [],\n                noscriptTags: [],\n                scriptTags: [],\n                styleTags: [],\n                title: \"\",\n                titleAttributes: {}\n            });\n        }\n\n        return mappedState;\n    }, _temp;\n};\n\nvar NullComponent = function NullComponent() {\n    return null;\n};\n\nvar HelmetSideEffects = withSideEffect(reducePropsToState, handleClientStateChange, mapStateOnServer)(NullComponent);\n\nvar HelmetExport = Helmet(HelmetSideEffects);\nHelmetExport.renderStatic = HelmetExport.rewind;\n\nexport default HelmetExport;\nexport { HelmetExport as Helmet };\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,eAAe;AAExC,IAAIC,eAAe,GAAG;EAClBC,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;AACX,CAAC;AAED,IAAIC,SAAS,GAAG;EACZC,IAAI,EAAE,MAAM;EACZJ,IAAI,EAAE,MAAM;EACZK,IAAI,EAAE,MAAM;EACZJ,IAAI,EAAE,MAAM;EACZK,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdR,KAAK,EAAE;AACX,CAAC;AAED,IAAIS,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACV,SAAS,CAAC,CAACW,GAAG,CAAC,UAAUC,IAAI,EAAE;EAC7D,OAAOZ,SAAS,CAACY,IAAI,CAAC;AAC1B,CAAC,CAAC;AAEF,IAAIC,cAAc,GAAG;EACjBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE,WAAW;EACvBC,SAAS,EAAE,UAAU;EACrBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE;AACZ,CAAC;AAED,IAAIC,aAAa,GAAG;EAChBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,WAAW;EAClBC,eAAe,EAAE,iBAAiB;EAClCC,WAAW,EAAE,aAAa;EAC1B,YAAY,EAAE,WAAW;EACzBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACd,CAAC;AAED,IAAIC,YAAY,GAAG;EACfC,aAAa,EAAE,cAAc;EAC7BC,KAAK,EAAE,OAAO;EACdC,yBAAyB,EAAE,yBAAyB;EACpDC,sBAAsB,EAAE,qBAAqB;EAC7CC,cAAc,EAAE;AACpB,CAAC;AAED,IAAIC,YAAY,GAAG9B,MAAM,CAACC,IAAI,CAACe,aAAa,CAAC,CAACe,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;EACrED,GAAG,CAAChB,aAAa,CAACiB,GAAG,CAAC,CAAC,GAAGA,GAAG;EAC7B,OAAOD,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AAEN,IAAIE,iBAAiB,GAAG,CAAC3C,SAAS,CAACK,QAAQ,EAAEL,SAAS,CAACM,MAAM,EAAEN,SAAS,CAACO,KAAK,CAAC;AAE/E,IAAIqC,gBAAgB,GAAG,mBAAmB;AAE1C,IAAIC,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUN,GAAG,EAAE;EACjG,OAAO,OAAOA,GAAG;AACnB,CAAC,GAAG,UAAUA,GAAG,EAAE;EACjB,OAAOA,GAAG,IAAI,OAAOK,MAAM,KAAK,UAAU,IAAIL,GAAG,CAACO,WAAW,KAAKF,MAAM,IAAIL,GAAG,KAAKK,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOR,GAAG;AAC9H,CAAC;AAED,IAAIS,cAAc,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,WAAW,EAAE;EACpD,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF,CAAC;AAED,IAAIC,WAAW,GAAG,YAAY;EAC5B,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACvC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;MAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MACrDtD,MAAM,CAACuD,cAAc,CAACR,MAAM,EAAEI,UAAU,CAAClB,GAAG,EAAEkB,UAAU,CAAC;IAC3D;EACF;EAEA,OAAO,UAAUR,WAAW,EAAEa,UAAU,EAAEC,WAAW,EAAE;IACrD,IAAID,UAAU,EAAEV,gBAAgB,CAACH,WAAW,CAACH,SAAS,EAAEgB,UAAU,CAAC;IACnE,IAAIC,WAAW,EAAEX,gBAAgB,CAACH,WAAW,EAAEc,WAAW,CAAC;IAC3D,OAAOd,WAAW;EACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,IAAIe,QAAQ,GAAG1D,MAAM,CAAC2D,MAAM,IAAI,UAAUZ,MAAM,EAAE;EAChD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACV,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAIY,MAAM,GAAGD,SAAS,CAACX,CAAC,CAAC;IAEzB,KAAK,IAAIhB,GAAG,IAAI4B,MAAM,EAAE;MACtB,IAAI7D,MAAM,CAACwC,SAAS,CAACsB,cAAc,CAACC,IAAI,CAACF,MAAM,EAAE5B,GAAG,CAAC,EAAE;QACrDc,MAAM,CAACd,GAAG,CAAC,GAAG4B,MAAM,CAAC5B,GAAG,CAAC;MAC3B;IACF;EACF;EAEA,OAAOc,MAAM;AACf,CAAC;AAED,IAAIiB,QAAQ,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,UAAU,EAAE;EAC7C,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAC3D,MAAM,IAAItB,SAAS,CAAC,0DAA0D,GAAG,OAAOsB,UAAU,CAAC;EACrG;EAEAD,QAAQ,CAACzB,SAAS,GAAGxC,MAAM,CAACmE,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC1B,SAAS,EAAE;IACrED,WAAW,EAAE;MACX6B,KAAK,EAAEH,QAAQ;MACfb,UAAU,EAAE,KAAK;MACjBE,QAAQ,EAAE,IAAI;MACdD,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,IAAIa,UAAU,EAAElE,MAAM,CAACqE,cAAc,GAAGrE,MAAM,CAACqE,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AACvH,CAAC;AAED,IAAIK,uBAAuB,GAAG,SAAAA,CAAUvC,GAAG,EAAE/B,IAAI,EAAE;EACjD,IAAI8C,MAAM,GAAG,CAAC,CAAC;EAEf,KAAK,IAAIE,CAAC,IAAIjB,GAAG,EAAE;IACjB,IAAI/B,IAAI,CAACuE,OAAO,CAACvB,CAAC,CAAC,IAAI,CAAC,EAAE;IAC1B,IAAI,CAACjD,MAAM,CAACwC,SAAS,CAACsB,cAAc,CAACC,IAAI,CAAC/B,GAAG,EAAEiB,CAAC,CAAC,EAAE;IACnDF,MAAM,CAACE,CAAC,CAAC,GAAGjB,GAAG,CAACiB,CAAC,CAAC;EACpB;EAEA,OAAOF,MAAM;AACf,CAAC;AAED,IAAI0B,yBAAyB,GAAG,SAAAA,CAAUC,IAAI,EAAEX,IAAI,EAAE;EACpD,IAAI,CAACW,IAAI,EAAE;IACT,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvF;EAEA,OAAOZ,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGW,IAAI;AACvF,CAAC;AAED,IAAIE,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,GAAG,EAAE;EAChE,IAAIC,MAAM,GAAGlB,SAAS,CAACV,MAAM,GAAG,CAAC,IAAIU,SAAS,CAAC,CAAC,CAAC,KAAKmB,SAAS,GAAGnB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAErF,IAAIkB,MAAM,KAAK,KAAK,EAAE;IAClB,OAAOE,MAAM,CAACH,GAAG,CAAC;EACtB;EAEA,OAAOG,MAAM,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AACzI,CAAC;AAED,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,SAAS,EAAE;EAClE,IAAIC,cAAc,GAAGC,oBAAoB,CAACF,SAAS,EAAE5F,SAAS,CAACD,KAAK,CAAC;EACrE,IAAIgG,iBAAiB,GAAGD,oBAAoB,CAACF,SAAS,EAAE3D,YAAY,CAACK,cAAc,CAAC;EAEpF,IAAIyD,iBAAiB,IAAIF,cAAc,EAAE;IACrC;IACA,OAAOE,iBAAiB,CAACL,OAAO,CAAC,KAAK,EAAE,YAAY;MAChD,OAAOM,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,GAAGA,cAAc,CAACK,IAAI,CAAC,EAAE,CAAC,GAAGL,cAAc;IACnF,CAAC,CAAC;EACN;EAEA,IAAIM,qBAAqB,GAAGL,oBAAoB,CAACF,SAAS,EAAE3D,YAAY,CAACC,aAAa,CAAC;EAEvF,OAAO2D,cAAc,IAAIM,qBAAqB,IAAIX,SAAS;AAC/D,CAAC;AAED,IAAIY,sBAAsB,GAAG,SAASA,sBAAsBA,CAACR,SAAS,EAAE;EACpE,OAAOE,oBAAoB,CAACF,SAAS,EAAE3D,YAAY,CAACI,sBAAsB,CAAC,IAAI,YAAY,CAAC,CAAC;AACjG,CAAC;AAED,IAAIgE,0BAA0B,GAAG,SAASA,0BAA0BA,CAACC,OAAO,EAAEV,SAAS,EAAE;EACrF,OAAOA,SAAS,CAACW,MAAM,CAAC,UAAU9C,KAAK,EAAE;IACrC,OAAO,OAAOA,KAAK,CAAC6C,OAAO,CAAC,KAAK,WAAW;EAChD,CAAC,CAAC,CAAC3F,GAAG,CAAC,UAAU8C,KAAK,EAAE;IACpB,OAAOA,KAAK,CAAC6C,OAAO,CAAC;EACzB,CAAC,CAAC,CAAC9D,MAAM,CAAC,UAAUgE,QAAQ,EAAEC,OAAO,EAAE;IACnC,OAAOtC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,QAAQ,EAAEC,OAAO,CAAC;EAC1C,CAAC,EAAE,CAAC,CAAC,CAAC;AACV,CAAC;AAED,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,iBAAiB,EAAEf,SAAS,EAAE;EACzF,OAAOA,SAAS,CAACW,MAAM,CAAC,UAAU9C,KAAK,EAAE;IACrC,OAAO,OAAOA,KAAK,CAACzD,SAAS,CAACC,IAAI,CAAC,KAAK,WAAW;EACvD,CAAC,CAAC,CAACU,GAAG,CAAC,UAAU8C,KAAK,EAAE;IACpB,OAAOA,KAAK,CAACzD,SAAS,CAACC,IAAI,CAAC;EAChC,CAAC,CAAC,CAAC2G,OAAO,CAAC,CAAC,CAACpE,MAAM,CAAC,UAAUqE,gBAAgB,EAAEC,GAAG,EAAE;IACjD,IAAI,CAACD,gBAAgB,CAAClD,MAAM,EAAE;MAC1B,IAAIjD,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACoG,GAAG,CAAC;MAE3B,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,IAAI,CAACiD,MAAM,EAAED,CAAC,EAAE,EAAE;QAClC,IAAIqD,YAAY,GAAGrG,IAAI,CAACgD,CAAC,CAAC;QAC1B,IAAIsD,qBAAqB,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC;QAEtD,IAAIN,iBAAiB,CAAC1B,OAAO,CAAC+B,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAAIF,GAAG,CAACE,qBAAqB,CAAC,EAAE;UACvF,OAAOH,gBAAgB,CAACK,MAAM,CAACJ,GAAG,CAAC;QACvC;MACJ;IACJ;IAEA,OAAOD,gBAAgB;EAC3B,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AAED,IAAIM,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,OAAO,EAAET,iBAAiB,EAAEf,SAAS,EAAE;EAC5F;EACA,IAAIyB,gBAAgB,GAAG,CAAC,CAAC;EAEzB,OAAOzB,SAAS,CAACW,MAAM,CAAC,UAAU9C,KAAK,EAAE;IACrC,IAAIuC,KAAK,CAACC,OAAO,CAACxC,KAAK,CAAC2D,OAAO,CAAC,CAAC,EAAE;MAC/B,OAAO,IAAI;IACf;IACA,IAAI,OAAO3D,KAAK,CAAC2D,OAAO,CAAC,KAAK,WAAW,EAAE;MACvCE,IAAI,CAAC,UAAU,GAAGF,OAAO,GAAG,qDAAqD,GAAGvE,OAAO,CAACY,KAAK,CAAC2D,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IACvH;IACA,OAAO,KAAK;EAChB,CAAC,CAAC,CAACzG,GAAG,CAAC,UAAU8C,KAAK,EAAE;IACpB,OAAOA,KAAK,CAAC2D,OAAO,CAAC;EACzB,CAAC,CAAC,CAACR,OAAO,CAAC,CAAC,CAACpE,MAAM,CAAC,UAAU+E,YAAY,EAAEC,YAAY,EAAE;IACtD,IAAIC,gBAAgB,GAAG,CAAC,CAAC;IAEzBD,YAAY,CAACjB,MAAM,CAAC,UAAUO,GAAG,EAAE;MAC/B,IAAIY,mBAAmB,GAAG,KAAK,CAAC;MAChC,IAAIhH,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACoG,GAAG,CAAC;MAC3B,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,IAAI,CAACiD,MAAM,EAAED,CAAC,EAAE,EAAE;QAClC,IAAIqD,YAAY,GAAGrG,IAAI,CAACgD,CAAC,CAAC;QAC1B,IAAIsD,qBAAqB,GAAGD,YAAY,CAACE,WAAW,CAAC,CAAC;;QAEtD;QACA,IAAIN,iBAAiB,CAAC1B,OAAO,CAAC+B,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAAI,EAAEU,mBAAmB,KAAK7G,cAAc,CAACS,GAAG,IAAIwF,GAAG,CAACY,mBAAmB,CAAC,CAACT,WAAW,CAAC,CAAC,KAAK,WAAW,CAAC,IAAI,EAAED,qBAAqB,KAAKnG,cAAc,CAACS,GAAG,IAAIwF,GAAG,CAACE,qBAAqB,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK,YAAY,CAAC,EAAE;UACpRS,mBAAmB,GAAGV,qBAAqB;QAC/C;QACA;QACA,IAAIL,iBAAiB,CAAC1B,OAAO,CAAC8B,YAAY,CAAC,KAAK,CAAC,CAAC,KAAKA,YAAY,KAAKlG,cAAc,CAACK,UAAU,IAAI6F,YAAY,KAAKlG,cAAc,CAACE,QAAQ,IAAIgG,YAAY,KAAKlG,cAAc,CAACM,SAAS,CAAC,EAAE;UACzLuG,mBAAmB,GAAGX,YAAY;QACtC;MACJ;MAEA,IAAI,CAACW,mBAAmB,IAAI,CAACZ,GAAG,CAACY,mBAAmB,CAAC,EAAE;QACnD,OAAO,KAAK;MAChB;MAEA,IAAI7C,KAAK,GAAGiC,GAAG,CAACY,mBAAmB,CAAC,CAACT,WAAW,CAAC,CAAC;MAElD,IAAI,CAACI,gBAAgB,CAACK,mBAAmB,CAAC,EAAE;QACxCL,gBAAgB,CAACK,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAC9C;MAEA,IAAI,CAACD,gBAAgB,CAACC,mBAAmB,CAAC,EAAE;QACxCD,gBAAgB,CAACC,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAC9C;MAEA,IAAI,CAACL,gBAAgB,CAACK,mBAAmB,CAAC,CAAC7C,KAAK,CAAC,EAAE;QAC/C4C,gBAAgB,CAACC,mBAAmB,CAAC,CAAC7C,KAAK,CAAC,GAAG,IAAI;QACnD,OAAO,IAAI;MACf;MAEA,OAAO,KAAK;IAChB,CAAC,CAAC,CAAC+B,OAAO,CAAC,CAAC,CAACe,OAAO,CAAC,UAAUb,GAAG,EAAE;MAChC,OAAOS,YAAY,CAACK,IAAI,CAACd,GAAG,CAAC;IACjC,CAAC,CAAC;;IAEF;IACA,IAAIpG,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC+G,gBAAgB,CAAC;IACxC,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,IAAI,CAACiD,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,IAAIqD,YAAY,GAAGrG,IAAI,CAACgD,CAAC,CAAC;MAC1B,IAAImE,QAAQ,GAAGlI,YAAY,CAAC,CAAC,CAAC,EAAE0H,gBAAgB,CAACN,YAAY,CAAC,EAAEU,gBAAgB,CAACV,YAAY,CAAC,CAAC;MAE/FM,gBAAgB,CAACN,YAAY,CAAC,GAAGc,QAAQ;IAC7C;IAEA,OAAON,YAAY;EACvB,CAAC,EAAE,EAAE,CAAC,CAACX,OAAO,CAAC,CAAC;AACpB,CAAC;AAED,IAAId,oBAAoB,GAAG,SAASA,oBAAoBA,CAACF,SAAS,EAAEkC,QAAQ,EAAE;EAC1E,KAAK,IAAIpE,CAAC,GAAGkC,SAAS,CAACjC,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,IAAID,KAAK,GAAGmC,SAAS,CAAClC,CAAC,CAAC;IAExB,IAAID,KAAK,CAACc,cAAc,CAACuD,QAAQ,CAAC,EAAE;MAChC,OAAOrE,KAAK,CAACqE,QAAQ,CAAC;IAC1B;EACJ;EAEA,OAAO,IAAI;AACf,CAAC;AAED,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACnC,SAAS,EAAE;EAC5D,OAAO;IACHoC,OAAO,EAAEtB,uBAAuB,CAAC,CAAC7F,cAAc,CAACG,IAAI,EAAEH,cAAc,CAACW,MAAM,CAAC,EAAEoE,SAAS,CAAC;IACzFqC,cAAc,EAAE5B,0BAA0B,CAACzG,eAAe,CAACC,IAAI,EAAE+F,SAAS,CAAC;IAC3EsC,KAAK,EAAEpC,oBAAoB,CAACF,SAAS,EAAE3D,YAAY,CAACE,KAAK,CAAC;IAC1DoD,MAAM,EAAEO,oBAAoB,CAACF,SAAS,EAAE3D,YAAY,CAACG,yBAAyB,CAAC;IAC/E+F,cAAc,EAAE9B,0BAA0B,CAACzG,eAAe,CAACE,IAAI,EAAE8F,SAAS,CAAC;IAC3EwC,QAAQ,EAAEjB,oBAAoB,CAACnH,SAAS,CAACG,IAAI,EAAE,CAACU,cAAc,CAACS,GAAG,EAAET,cAAc,CAACG,IAAI,CAAC,EAAE4E,SAAS,CAAC;IACpGyC,QAAQ,EAAElB,oBAAoB,CAACnH,SAAS,CAACI,IAAI,EAAE,CAACS,cAAc,CAACO,IAAI,EAAEP,cAAc,CAACC,OAAO,EAAED,cAAc,CAACI,SAAS,EAAEJ,cAAc,CAACQ,QAAQ,EAAER,cAAc,CAACM,SAAS,CAAC,EAAEyE,SAAS,CAAC;IACrL0C,YAAY,EAAEnB,oBAAoB,CAACnH,SAAS,CAACK,QAAQ,EAAE,CAACQ,cAAc,CAACK,UAAU,CAAC,EAAE0E,SAAS,CAAC;IAC9F2C,mBAAmB,EAAEnC,sBAAsB,CAACR,SAAS,CAAC;IACtD4C,UAAU,EAAErB,oBAAoB,CAACnH,SAAS,CAACM,MAAM,EAAE,CAACO,cAAc,CAACU,GAAG,EAAEV,cAAc,CAACK,UAAU,CAAC,EAAE0E,SAAS,CAAC;IAC9G6C,SAAS,EAAEtB,oBAAoB,CAACnH,SAAS,CAACO,KAAK,EAAE,CAACM,cAAc,CAACE,QAAQ,CAAC,EAAE6E,SAAS,CAAC;IACtF8C,KAAK,EAAE/C,qBAAqB,CAACC,SAAS,CAAC;IACvC+C,eAAe,EAAEtC,0BAA0B,CAACzG,eAAe,CAACG,KAAK,EAAE6F,SAAS;EAChF,CAAC;AACL,CAAC;AAED,IAAIgD,WAAW,GAAG,YAAY;EAC1B,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAEtB,OAAO,UAAUC,QAAQ,EAAE;IACvB,IAAIC,WAAW,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAIE,WAAW,GAAGJ,KAAK,GAAG,EAAE,EAAE;MAC1BA,KAAK,GAAGI,WAAW;MACnBD,QAAQ,CAACC,WAAW,CAAC;IACzB,CAAC,MAAM;MACHC,UAAU,CAAC,YAAY;QACnBN,WAAW,CAACI,QAAQ,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC;IACT;EACJ,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACC,EAAE,EAAE;EACvC,OAAOC,YAAY,CAACD,EAAE,CAAC;AAC3B,CAAC;AAED,IAAIE,qBAAqB,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,qBAAqB,IAAIC,MAAM,CAACD,qBAAqB,CAACE,IAAI,CAACD,MAAM,CAAC,IAAIA,MAAM,CAACE,2BAA2B,IAAIF,MAAM,CAACG,wBAAwB,IAAId,WAAW,GAAGe,MAAM,CAACL,qBAAqB,IAAIV,WAAW;AAE3Q,IAAIgB,oBAAoB,GAAG,OAAOL,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACK,oBAAoB,IAAIL,MAAM,CAACM,0BAA0B,IAAIN,MAAM,CAACO,uBAAuB,IAAIX,WAAW,GAAGQ,MAAM,CAACC,oBAAoB,IAAIT,WAAW;AAEzN,IAAI7B,IAAI,GAAG,SAASA,IAAIA,CAACyC,GAAG,EAAE;EAC1B,OAAOC,OAAO,IAAI,OAAOA,OAAO,CAAC1C,IAAI,KAAK,UAAU,IAAI0C,OAAO,CAAC1C,IAAI,CAACyC,GAAG,CAAC;AAC7E,CAAC;AAED,IAAIE,eAAe,GAAG,IAAI;AAE1B,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,QAAQ,EAAE;EACrE,IAAIF,eAAe,EAAE;IACjBL,oBAAoB,CAACK,eAAe,CAAC;EACzC;EAEA,IAAIE,QAAQ,CAACjC,KAAK,EAAE;IAChB+B,eAAe,GAAGX,qBAAqB,CAAC,YAAY;MAChDc,gBAAgB,CAACD,QAAQ,EAAE,YAAY;QACnCF,eAAe,GAAG,IAAI;MAC1B,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC,MAAM;IACHG,gBAAgB,CAACD,QAAQ,CAAC;IAC1BF,eAAe,GAAG,IAAI;EAC1B;AACJ,CAAC;AAED,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACD,QAAQ,EAAEE,EAAE,EAAE;EAC3D,IAAIrC,OAAO,GAAGmC,QAAQ,CAACnC,OAAO;IAC1BC,cAAc,GAAGkC,QAAQ,CAAClC,cAAc;IACxCE,cAAc,GAAGgC,QAAQ,CAAChC,cAAc;IACxCC,QAAQ,GAAG+B,QAAQ,CAAC/B,QAAQ;IAC5BC,QAAQ,GAAG8B,QAAQ,CAAC9B,QAAQ;IAC5BC,YAAY,GAAG6B,QAAQ,CAAC7B,YAAY;IACpCC,mBAAmB,GAAG4B,QAAQ,CAAC5B,mBAAmB;IAClDC,UAAU,GAAG2B,QAAQ,CAAC3B,UAAU;IAChCC,SAAS,GAAG0B,QAAQ,CAAC1B,SAAS;IAC9BC,KAAK,GAAGyB,QAAQ,CAACzB,KAAK;IACtBC,eAAe,GAAGwB,QAAQ,CAACxB,eAAe;EAE9C2B,gBAAgB,CAACtK,SAAS,CAACH,IAAI,EAAEoI,cAAc,CAAC;EAChDqC,gBAAgB,CAACtK,SAAS,CAACF,IAAI,EAAEqI,cAAc,CAAC;EAEhDoC,WAAW,CAAC7B,KAAK,EAAEC,eAAe,CAAC;EAEnC,IAAI6B,UAAU,GAAG;IACbxC,OAAO,EAAEyC,UAAU,CAACzK,SAAS,CAACC,IAAI,EAAE+H,OAAO,CAAC;IAC5CI,QAAQ,EAAEqC,UAAU,CAACzK,SAAS,CAACG,IAAI,EAAEiI,QAAQ,CAAC;IAC9CC,QAAQ,EAAEoC,UAAU,CAACzK,SAAS,CAACI,IAAI,EAAEiI,QAAQ,CAAC;IAC9CC,YAAY,EAAEmC,UAAU,CAACzK,SAAS,CAACK,QAAQ,EAAEiI,YAAY,CAAC;IAC1DE,UAAU,EAAEiC,UAAU,CAACzK,SAAS,CAACM,MAAM,EAAEkI,UAAU,CAAC;IACpDC,SAAS,EAAEgC,UAAU,CAACzK,SAAS,CAACO,KAAK,EAAEkI,SAAS;EACpD,CAAC;EAED,IAAIiC,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;EAEpBlK,MAAM,CAACC,IAAI,CAAC8J,UAAU,CAAC,CAAC7C,OAAO,CAAC,UAAUrB,OAAO,EAAE;IAC/C,IAAIsE,mBAAmB,GAAGJ,UAAU,CAAClE,OAAO,CAAC;MACzCuE,OAAO,GAAGD,mBAAmB,CAACC,OAAO;MACrCC,OAAO,GAAGF,mBAAmB,CAACE,OAAO;IAGzC,IAAID,OAAO,CAAClH,MAAM,EAAE;MAChB+G,SAAS,CAACpE,OAAO,CAAC,GAAGuE,OAAO;IAChC;IACA,IAAIC,OAAO,CAACnH,MAAM,EAAE;MAChBgH,WAAW,CAACrE,OAAO,CAAC,GAAGkE,UAAU,CAAClE,OAAO,CAAC,CAACwE,OAAO;IACtD;EACJ,CAAC,CAAC;EAEFT,EAAE,IAAIA,EAAE,CAAC,CAAC;EAEV9B,mBAAmB,CAAC4B,QAAQ,EAAEO,SAAS,EAAEC,WAAW,CAAC;AACzD,CAAC;AAED,IAAII,YAAY,GAAG,SAASA,YAAYA,CAACC,aAAa,EAAE;EACpD,OAAOhF,KAAK,CAACC,OAAO,CAAC+E,aAAa,CAAC,GAAGA,aAAa,CAAC9E,IAAI,CAAC,EAAE,CAAC,GAAG8E,aAAa;AAChF,CAAC;AAED,IAAIT,WAAW,GAAG,SAASA,WAAWA,CAAC7B,KAAK,EAAEuC,UAAU,EAAE;EACtD,IAAI,OAAOvC,KAAK,KAAK,WAAW,IAAIwC,QAAQ,CAACxC,KAAK,KAAKA,KAAK,EAAE;IAC1DwC,QAAQ,CAACxC,KAAK,GAAGqC,YAAY,CAACrC,KAAK,CAAC;EACxC;EAEA4B,gBAAgB,CAACtK,SAAS,CAACD,KAAK,EAAEkL,UAAU,CAAC;AACjD,CAAC;AAED,IAAIX,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClD,OAAO,EAAE6D,UAAU,EAAE;EAClE,IAAIE,UAAU,GAAGD,QAAQ,CAACE,oBAAoB,CAAChE,OAAO,CAAC,CAAC,CAAC,CAAC;EAE1D,IAAI,CAAC+D,UAAU,EAAE;IACb;EACJ;EAEA,IAAIE,qBAAqB,GAAGF,UAAU,CAACG,YAAY,CAAC1I,gBAAgB,CAAC;EACrE,IAAI2I,gBAAgB,GAAGF,qBAAqB,GAAGA,qBAAqB,CAACG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EACpF,IAAIC,kBAAkB,GAAG,EAAE,CAACvE,MAAM,CAACqE,gBAAgB,CAAC;EACpD,IAAIG,aAAa,GAAGjL,MAAM,CAACC,IAAI,CAACuK,UAAU,CAAC;EAE3C,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgI,aAAa,CAAC/H,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAIiI,SAAS,GAAGD,aAAa,CAAChI,CAAC,CAAC;IAChC,IAAImB,KAAK,GAAGoG,UAAU,CAACU,SAAS,CAAC,IAAI,EAAE;IAEvC,IAAIR,UAAU,CAACG,YAAY,CAACK,SAAS,CAAC,KAAK9G,KAAK,EAAE;MAC9CsG,UAAU,CAACS,YAAY,CAACD,SAAS,EAAE9G,KAAK,CAAC;IAC7C;IAEA,IAAI0G,gBAAgB,CAACtG,OAAO,CAAC0G,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MAC5CJ,gBAAgB,CAAC3D,IAAI,CAAC+D,SAAS,CAAC;IACpC;IAEA,IAAIE,WAAW,GAAGJ,kBAAkB,CAACxG,OAAO,CAAC0G,SAAS,CAAC;IACvD,IAAIE,WAAW,KAAK,CAAC,CAAC,EAAE;MACpBJ,kBAAkB,CAACK,MAAM,CAACD,WAAW,EAAE,CAAC,CAAC;IAC7C;EACJ;EAEA,KAAK,IAAIE,EAAE,GAAGN,kBAAkB,CAAC9H,MAAM,GAAG,CAAC,EAAEoI,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;IACxDZ,UAAU,CAACa,eAAe,CAACP,kBAAkB,CAACM,EAAE,CAAC,CAAC;EACtD;EAEA,IAAIR,gBAAgB,CAAC5H,MAAM,KAAK8H,kBAAkB,CAAC9H,MAAM,EAAE;IACvDwH,UAAU,CAACa,eAAe,CAACpJ,gBAAgB,CAAC;EAChD,CAAC,MAAM,IAAIuI,UAAU,CAACG,YAAY,CAAC1I,gBAAgB,CAAC,KAAK8I,aAAa,CAACxF,IAAI,CAAC,GAAG,CAAC,EAAE;IAC9EiF,UAAU,CAACS,YAAY,CAAChJ,gBAAgB,EAAE8I,aAAa,CAACxF,IAAI,CAAC,GAAG,CAAC,CAAC;EACtE;AACJ,CAAC;AAED,IAAIuE,UAAU,GAAG,SAASA,UAAUA,CAACwB,IAAI,EAAEC,IAAI,EAAE;EAC7C,IAAIC,WAAW,GAAGjB,QAAQ,CAACkB,IAAI,IAAIlB,QAAQ,CAACmB,aAAa,CAACrM,SAAS,CAACE,IAAI,CAAC;EACzE,IAAIoM,QAAQ,GAAGH,WAAW,CAACI,gBAAgB,CAACN,IAAI,GAAG,GAAG,GAAGrJ,gBAAgB,GAAG,GAAG,CAAC;EAChF,IAAIkI,OAAO,GAAG9E,KAAK,CAAC/C,SAAS,CAACuJ,KAAK,CAAChI,IAAI,CAAC8H,QAAQ,CAAC;EAClD,IAAIzB,OAAO,GAAG,EAAE;EAChB,IAAI4B,aAAa,GAAG,KAAK,CAAC;EAE1B,IAAIP,IAAI,IAAIA,IAAI,CAACvI,MAAM,EAAE;IACrBuI,IAAI,CAACvE,OAAO,CAAC,UAAUb,GAAG,EAAE;MACxB,IAAI4F,UAAU,GAAGxB,QAAQ,CAACyB,aAAa,CAACV,IAAI,CAAC;MAE7C,KAAK,IAAIN,SAAS,IAAI7E,GAAG,EAAE;QACvB,IAAIA,GAAG,CAACvC,cAAc,CAACoH,SAAS,CAAC,EAAE;UAC/B,IAAIA,SAAS,KAAK9K,cAAc,CAACK,UAAU,EAAE;YACzCwL,UAAU,CAACE,SAAS,GAAG9F,GAAG,CAAC8F,SAAS;UACxC,CAAC,MAAM,IAAIjB,SAAS,KAAK9K,cAAc,CAACE,QAAQ,EAAE;YAC9C,IAAI2L,UAAU,CAACG,UAAU,EAAE;cACvBH,UAAU,CAACG,UAAU,CAACC,OAAO,GAAGhG,GAAG,CAACgG,OAAO;YAC/C,CAAC,MAAM;cACHJ,UAAU,CAACK,WAAW,CAAC7B,QAAQ,CAAC8B,cAAc,CAAClG,GAAG,CAACgG,OAAO,CAAC,CAAC;YAChE;UACJ,CAAC,MAAM;YACH,IAAIjI,KAAK,GAAG,OAAOiC,GAAG,CAAC6E,SAAS,CAAC,KAAK,WAAW,GAAG,EAAE,GAAG7E,GAAG,CAAC6E,SAAS,CAAC;YACvEe,UAAU,CAACd,YAAY,CAACD,SAAS,EAAE9G,KAAK,CAAC;UAC7C;QACJ;MACJ;MAEA6H,UAAU,CAACd,YAAY,CAAChJ,gBAAgB,EAAE,MAAM,CAAC;;MAEjD;MACA,IAAIkI,OAAO,CAACmC,IAAI,CAAC,UAAUC,WAAW,EAAEC,KAAK,EAAE;QAC3CV,aAAa,GAAGU,KAAK;QACrB,OAAOT,UAAU,CAACU,WAAW,CAACF,WAAW,CAAC;MAC9C,CAAC,CAAC,EAAE;QACApC,OAAO,CAACgB,MAAM,CAACW,aAAa,EAAE,CAAC,CAAC;MACpC,CAAC,MAAM;QACH5B,OAAO,CAACjD,IAAI,CAAC8E,UAAU,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN;EAEA5B,OAAO,CAACnD,OAAO,CAAC,UAAUb,GAAG,EAAE;IAC3B,OAAOA,GAAG,CAACuG,UAAU,CAACC,WAAW,CAACxG,GAAG,CAAC;EAC1C,CAAC,CAAC;EACF+D,OAAO,CAAClD,OAAO,CAAC,UAAUb,GAAG,EAAE;IAC3B,OAAOqF,WAAW,CAACY,WAAW,CAACjG,GAAG,CAAC;EACvC,CAAC,CAAC;EAEF,OAAO;IACHgE,OAAO,EAAEA,OAAO;IAChBD,OAAO,EAAEA;EACb,CAAC;AACL,CAAC;AAED,IAAI0C,iCAAiC,GAAG,SAASA,iCAAiCA,CAACtC,UAAU,EAAE;EAC3F,OAAOxK,MAAM,CAACC,IAAI,CAACuK,UAAU,CAAC,CAACzI,MAAM,CAAC,UAAU8C,GAAG,EAAE5C,GAAG,EAAE;IACtD,IAAI8K,IAAI,GAAG,OAAOvC,UAAU,CAACvI,GAAG,CAAC,KAAK,WAAW,GAAGA,GAAG,GAAG,KAAK,GAAGuI,UAAU,CAACvI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAGA,GAAG;IACnG,OAAO4C,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGkI,IAAI,GAAGA,IAAI;EACxC,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AAED,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACxB,IAAI,EAAEvD,KAAK,EAAEuC,UAAU,EAAE1F,MAAM,EAAE;EACxF,IAAImI,eAAe,GAAGH,iCAAiC,CAACtC,UAAU,CAAC;EACnE,IAAI0C,cAAc,GAAG5C,YAAY,CAACrC,KAAK,CAAC;EACxC,OAAOgF,eAAe,GAAG,GAAG,GAAGzB,IAAI,GAAG,GAAG,GAAGrJ,gBAAgB,GAAG,YAAY,GAAG8K,eAAe,GAAG,GAAG,GAAGrI,uBAAuB,CAACsI,cAAc,EAAEpI,MAAM,CAAC,GAAG,IAAI,GAAG0G,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG,GAAGrJ,gBAAgB,GAAG,YAAY,GAAGyC,uBAAuB,CAACsI,cAAc,EAAEpI,MAAM,CAAC,GAAG,IAAI,GAAG0G,IAAI,GAAG,GAAG;AACxS,CAAC;AAED,IAAI2B,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC3B,IAAI,EAAEC,IAAI,EAAE3G,MAAM,EAAE;EACzE,OAAO2G,IAAI,CAAC1J,MAAM,CAAC,UAAU8C,GAAG,EAAEwB,GAAG,EAAE;IACnC,IAAI+G,aAAa,GAAGpN,MAAM,CAACC,IAAI,CAACoG,GAAG,CAAC,CAACP,MAAM,CAAC,UAAUoF,SAAS,EAAE;MAC7D,OAAO,EAAEA,SAAS,KAAK9K,cAAc,CAACK,UAAU,IAAIyK,SAAS,KAAK9K,cAAc,CAACE,QAAQ,CAAC;IAC9F,CAAC,CAAC,CAACyB,MAAM,CAAC,UAAUsL,MAAM,EAAEnC,SAAS,EAAE;MACnC,IAAI6B,IAAI,GAAG,OAAO1G,GAAG,CAAC6E,SAAS,CAAC,KAAK,WAAW,GAAGA,SAAS,GAAGA,SAAS,GAAG,KAAK,GAAGtG,uBAAuB,CAACyB,GAAG,CAAC6E,SAAS,CAAC,EAAEpG,MAAM,CAAC,GAAG,IAAI;MACzI,OAAOuI,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAGN,IAAI,GAAGA,IAAI;IAC9C,CAAC,EAAE,EAAE,CAAC;IAEN,IAAIO,UAAU,GAAGjH,GAAG,CAAC8F,SAAS,IAAI9F,GAAG,CAACgG,OAAO,IAAI,EAAE;IAEnD,IAAIkB,aAAa,GAAGrL,iBAAiB,CAACsC,OAAO,CAACgH,IAAI,CAAC,KAAK,CAAC,CAAC;IAE1D,OAAO3G,GAAG,GAAG,GAAG,GAAG2G,IAAI,GAAG,GAAG,GAAGrJ,gBAAgB,GAAG,YAAY,GAAGiL,aAAa,IAAIG,aAAa,GAAG,IAAI,GAAG,GAAG,GAAGD,UAAU,GAAG,IAAI,GAAG9B,IAAI,GAAG,GAAG,CAAC;EACnJ,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AAED,IAAIgC,oCAAoC,GAAG,SAASA,oCAAoCA,CAAChD,UAAU,EAAE;EACjG,IAAIiD,SAAS,GAAG7J,SAAS,CAACV,MAAM,GAAG,CAAC,IAAIU,SAAS,CAAC,CAAC,CAAC,KAAKmB,SAAS,GAAGnB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEtF,OAAO5D,MAAM,CAACC,IAAI,CAACuK,UAAU,CAAC,CAACzI,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACtDD,GAAG,CAAChB,aAAa,CAACiB,GAAG,CAAC,IAAIA,GAAG,CAAC,GAAGuI,UAAU,CAACvI,GAAG,CAAC;IAChD,OAAOD,GAAG;EACd,CAAC,EAAEyL,SAAS,CAAC;AACjB,CAAC;AAED,IAAIC,iCAAiC,GAAG,SAASA,iCAAiCA,CAAC1K,KAAK,EAAE;EACtF,IAAI2K,cAAc,GAAG/J,SAAS,CAACV,MAAM,GAAG,CAAC,IAAIU,SAAS,CAAC,CAAC,CAAC,KAAKmB,SAAS,GAAGnB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAE3F,OAAO5D,MAAM,CAACC,IAAI,CAAC+C,KAAK,CAAC,CAACjB,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACjDD,GAAG,CAACF,YAAY,CAACG,GAAG,CAAC,IAAIA,GAAG,CAAC,GAAGe,KAAK,CAACf,GAAG,CAAC;IAC1C,OAAOD,GAAG;EACd,CAAC,EAAE2L,cAAc,CAAC;AACtB,CAAC;AAED,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAACpC,IAAI,EAAEvD,KAAK,EAAEuC,UAAU,EAAE;EAChG,IAAIqD,UAAU;;EAEd;EACA,IAAIJ,SAAS,IAAII,UAAU,GAAG;IAC1B5L,GAAG,EAAEgG;EACT,CAAC,EAAE4F,UAAU,CAAC1L,gBAAgB,CAAC,GAAG,IAAI,EAAE0L,UAAU,CAAC;EACnD,IAAI7K,KAAK,GAAGwK,oCAAoC,CAAChD,UAAU,EAAEiD,SAAS,CAAC;EAEvE,OAAO,CAACxO,KAAK,CAACiN,aAAa,CAAC3M,SAAS,CAACD,KAAK,EAAE0D,KAAK,EAAEiF,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED,IAAI6F,4BAA4B,GAAG,SAASA,4BAA4BA,CAACtC,IAAI,EAAEC,IAAI,EAAE;EACjF,OAAOA,IAAI,CAACvL,GAAG,CAAC,UAAUmG,GAAG,EAAEpD,CAAC,EAAE;IAC9B,IAAI8K,UAAU;IAEd,IAAIC,SAAS,IAAID,UAAU,GAAG;MAC1B9L,GAAG,EAAEgB;IACT,CAAC,EAAE8K,UAAU,CAAC5L,gBAAgB,CAAC,GAAG,IAAI,EAAE4L,UAAU,CAAC;IAEnD/N,MAAM,CAACC,IAAI,CAACoG,GAAG,CAAC,CAACa,OAAO,CAAC,UAAUgE,SAAS,EAAE;MAC1C,IAAI+C,eAAe,GAAGjN,aAAa,CAACkK,SAAS,CAAC,IAAIA,SAAS;MAE3D,IAAI+C,eAAe,KAAK7N,cAAc,CAACK,UAAU,IAAIwN,eAAe,KAAK7N,cAAc,CAACE,QAAQ,EAAE;QAC9F,IAAI4N,OAAO,GAAG7H,GAAG,CAAC8F,SAAS,IAAI9F,GAAG,CAACgG,OAAO;QAC1C2B,SAAS,CAACG,uBAAuB,GAAG;UAAEC,MAAM,EAAEF;QAAQ,CAAC;MAC3D,CAAC,MAAM;QACHF,SAAS,CAACC,eAAe,CAAC,GAAG5H,GAAG,CAAC6E,SAAS,CAAC;MAC/C;IACJ,CAAC,CAAC;IAEF,OAAOjM,KAAK,CAACiN,aAAa,CAACV,IAAI,EAAEwC,SAAS,CAAC;EAC/C,CAAC,CAAC;AACN,CAAC;AAED,IAAIK,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC7C,IAAI,EAAEC,IAAI,EAAE3G,MAAM,EAAE;EACjE,QAAQ0G,IAAI;IACR,KAAKjM,SAAS,CAACD,KAAK;MAChB,OAAO;QACHgP,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;UAChC,OAAOV,6BAA6B,CAACpC,IAAI,EAAEC,IAAI,CAACxD,KAAK,EAAEwD,IAAI,CAACvD,eAAe,EAAEpD,MAAM,CAAC;QACxF,CAAC;QACDyJ,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC1B,OAAOvB,qBAAqB,CAACxB,IAAI,EAAEC,IAAI,CAACxD,KAAK,EAAEwD,IAAI,CAACvD,eAAe,EAAEpD,MAAM,CAAC;QAChF;MACJ,CAAC;IACL,KAAK3F,eAAe,CAACC,IAAI;IACzB,KAAKD,eAAe,CAACE,IAAI;MACrB,OAAO;QACHiP,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;UAChC,OAAOd,oCAAoC,CAAC/B,IAAI,CAAC;QACrD,CAAC;QACD8C,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC1B,OAAOzB,iCAAiC,CAACrB,IAAI,CAAC;QAClD;MACJ,CAAC;IACL;MACI,OAAO;QACH6C,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;UAChC,OAAOR,4BAA4B,CAACtC,IAAI,EAAEC,IAAI,CAAC;QACnD,CAAC;QACD8C,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC1B,OAAOpB,oBAAoB,CAAC3B,IAAI,EAAEC,IAAI,EAAE3G,MAAM,CAAC;QACnD;MACJ,CAAC;EACT;AACJ,CAAC;AAED,IAAI0J,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAE;EACnD,IAAIlH,OAAO,GAAGkH,IAAI,CAAClH,OAAO;IACtBC,cAAc,GAAGiH,IAAI,CAACjH,cAAc;IACpC1C,MAAM,GAAG2J,IAAI,CAAC3J,MAAM;IACpB4C,cAAc,GAAG+G,IAAI,CAAC/G,cAAc;IACpCC,QAAQ,GAAG8G,IAAI,CAAC9G,QAAQ;IACxBC,QAAQ,GAAG6G,IAAI,CAAC7G,QAAQ;IACxBC,YAAY,GAAG4G,IAAI,CAAC5G,YAAY;IAChCE,UAAU,GAAG0G,IAAI,CAAC1G,UAAU;IAC5BC,SAAS,GAAGyG,IAAI,CAACzG,SAAS;IAC1B0G,UAAU,GAAGD,IAAI,CAACxG,KAAK;IACvBA,KAAK,GAAGyG,UAAU,KAAK3J,SAAS,GAAG,EAAE,GAAG2J,UAAU;IAClDxG,eAAe,GAAGuG,IAAI,CAACvG,eAAe;EAC1C,OAAO;IACHyG,IAAI,EAAEN,gBAAgB,CAAC9O,SAAS,CAACC,IAAI,EAAE+H,OAAO,EAAEzC,MAAM,CAAC;IACvD0C,cAAc,EAAE6G,gBAAgB,CAAClP,eAAe,CAACC,IAAI,EAAEoI,cAAc,EAAE1C,MAAM,CAAC;IAC9E4C,cAAc,EAAE2G,gBAAgB,CAAClP,eAAe,CAACE,IAAI,EAAEqI,cAAc,EAAE5C,MAAM,CAAC;IAC9E8J,IAAI,EAAEP,gBAAgB,CAAC9O,SAAS,CAACG,IAAI,EAAEiI,QAAQ,EAAE7C,MAAM,CAAC;IACxD+J,IAAI,EAAER,gBAAgB,CAAC9O,SAAS,CAACI,IAAI,EAAEiI,QAAQ,EAAE9C,MAAM,CAAC;IACxDgK,QAAQ,EAAET,gBAAgB,CAAC9O,SAAS,CAACK,QAAQ,EAAEiI,YAAY,EAAE/C,MAAM,CAAC;IACpEiK,MAAM,EAAEV,gBAAgB,CAAC9O,SAAS,CAACM,MAAM,EAAEkI,UAAU,EAAEjD,MAAM,CAAC;IAC9DkK,KAAK,EAAEX,gBAAgB,CAAC9O,SAAS,CAACO,KAAK,EAAEkI,SAAS,EAAElD,MAAM,CAAC;IAC3DmD,KAAK,EAAEoG,gBAAgB,CAAC9O,SAAS,CAACD,KAAK,EAAE;MAAE2I,KAAK,EAAEA,KAAK;MAAEC,eAAe,EAAEA;IAAgB,CAAC,EAAEpD,MAAM;EACvG,CAAC;AACL,CAAC;AAED,IAAImK,MAAM,GAAG,SAASA,MAAMA,CAACC,SAAS,EAAE;EACpC,IAAIC,MAAM,EAAEC,KAAK;EAEjB,OAAOA,KAAK,GAAGD,MAAM,GAAG,UAAUE,gBAAgB,EAAE;IAChDrL,QAAQ,CAACsL,aAAa,EAAED,gBAAgB,CAAC;IAEzC,SAASC,aAAaA,CAAA,EAAG;MACrB7M,cAAc,CAAC,IAAI,EAAE6M,aAAa,CAAC;MACnC,OAAO7K,yBAAyB,CAAC,IAAI,EAAE4K,gBAAgB,CAACE,KAAK,CAAC,IAAI,EAAE3L,SAAS,CAAC,CAAC;IACnF;IAEA0L,aAAa,CAAC9M,SAAS,CAACgN,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,SAAS,EAAE;MACtF,OAAO,CAACzQ,OAAO,CAAC,IAAI,CAACgE,KAAK,EAAEyM,SAAS,CAAC;IAC1C,CAAC;IAEDH,aAAa,CAAC9M,SAAS,CAACkN,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,cAAc,EAAE;MACxG,IAAI,CAACA,cAAc,EAAE;QACjB,OAAO,IAAI;MACf;MAEA,QAAQD,KAAK,CAACnE,IAAI;QACd,KAAKjM,SAAS,CAACM,MAAM;QACrB,KAAKN,SAAS,CAACK,QAAQ;UACnB,OAAO;YACHuM,SAAS,EAAEyD;UACf,CAAC;QAEL,KAAKrQ,SAAS,CAACO,KAAK;UAChB,OAAO;YACHuM,OAAO,EAAEuD;UACb,CAAC;MACT;MAEA,MAAM,IAAIC,KAAK,CAAC,GAAG,GAAGF,KAAK,CAACnE,IAAI,GAAG,oGAAoG,CAAC;IAC5I,CAAC;IAED8D,aAAa,CAAC9M,SAAS,CAACsN,wBAAwB,GAAG,SAASA,wBAAwBA,CAACrB,IAAI,EAAE;MACvF,IAAIsB,qBAAqB;MAEzB,IAAIJ,KAAK,GAAGlB,IAAI,CAACkB,KAAK;QAClBK,iBAAiB,GAAGvB,IAAI,CAACuB,iBAAiB;QAC1CC,aAAa,GAAGxB,IAAI,CAACwB,aAAa;QAClCL,cAAc,GAAGnB,IAAI,CAACmB,cAAc;MAExC,OAAOlM,QAAQ,CAAC,CAAC,CAAC,EAAEsM,iBAAiB,GAAGD,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAACJ,KAAK,CAACnE,IAAI,CAAC,GAAG,EAAE,CAAC/E,MAAM,CAACuJ,iBAAiB,CAACL,KAAK,CAACnE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC9H,QAAQ,CAAC,CAAC,CAAC,EAAEuM,aAAa,EAAE,IAAI,CAACP,wBAAwB,CAACC,KAAK,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAAC,CAAC;IACxQ,CAAC;IAEDT,aAAa,CAAC9M,SAAS,CAAC0N,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAE;MAClF,IAAIC,sBAAsB,EAAEC,sBAAsB;MAElD,IAAIV,KAAK,GAAGQ,KAAK,CAACR,KAAK;QACnBW,QAAQ,GAAGH,KAAK,CAACG,QAAQ;QACzBL,aAAa,GAAGE,KAAK,CAACF,aAAa;QACnCL,cAAc,GAAGO,KAAK,CAACP,cAAc;MAEzC,QAAQD,KAAK,CAACnE,IAAI;QACd,KAAKjM,SAAS,CAACD,KAAK;UAChB,OAAOoE,QAAQ,CAAC,CAAC,CAAC,EAAE4M,QAAQ,GAAGF,sBAAsB,GAAG,CAAC,CAAC,EAAEA,sBAAsB,CAACT,KAAK,CAACnE,IAAI,CAAC,GAAGoE,cAAc,EAAEQ,sBAAsB,CAAClI,eAAe,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEuM,aAAa,CAAC,EAAEG,sBAAsB,CAAC,CAAC;QAEnN,KAAK7Q,SAAS,CAACH,IAAI;UACf,OAAOsE,QAAQ,CAAC,CAAC,CAAC,EAAE4M,QAAQ,EAAE;YAC1B9I,cAAc,EAAE9D,QAAQ,CAAC,CAAC,CAAC,EAAEuM,aAAa;UAC9C,CAAC,CAAC;QAEN,KAAK1Q,SAAS,CAACF,IAAI;UACf,OAAOqE,QAAQ,CAAC,CAAC,CAAC,EAAE4M,QAAQ,EAAE;YAC1B5I,cAAc,EAAEhE,QAAQ,CAAC,CAAC,CAAC,EAAEuM,aAAa;UAC9C,CAAC,CAAC;MACV;MAEA,OAAOvM,QAAQ,CAAC,CAAC,CAAC,EAAE4M,QAAQ,GAAGD,sBAAsB,GAAG,CAAC,CAAC,EAAEA,sBAAsB,CAACV,KAAK,CAACnE,IAAI,CAAC,GAAG9H,QAAQ,CAAC,CAAC,CAAC,EAAEuM,aAAa,CAAC,EAAEI,sBAAsB,CAAC,CAAC;IAC1J,CAAC;IAEDf,aAAa,CAAC9M,SAAS,CAAC+N,2BAA2B,GAAG,SAASA,2BAA2BA,CAACP,iBAAiB,EAAEM,QAAQ,EAAE;MACpH,IAAIE,iBAAiB,GAAG9M,QAAQ,CAAC,CAAC,CAAC,EAAE4M,QAAQ,CAAC;MAE9CtQ,MAAM,CAACC,IAAI,CAAC+P,iBAAiB,CAAC,CAAC9I,OAAO,CAAC,UAAUuJ,cAAc,EAAE;QAC7D,IAAIC,sBAAsB;QAE1BF,iBAAiB,GAAG9M,QAAQ,CAAC,CAAC,CAAC,EAAE8M,iBAAiB,GAAGE,sBAAsB,GAAG,CAAC,CAAC,EAAEA,sBAAsB,CAACD,cAAc,CAAC,GAAGT,iBAAiB,CAACS,cAAc,CAAC,EAAEC,sBAAsB,CAAC,CAAC;MAC1L,CAAC,CAAC;MAEF,OAAOF,iBAAiB;IAC5B,CAAC;IAEDlB,aAAa,CAAC9M,SAAS,CAACmO,qBAAqB,GAAG,SAASA,qBAAqBA,CAAChB,KAAK,EAAEC,cAAc,EAAE;MAClG,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACvC,IAAI,CAAC/Q,eAAe,CAACyM,IAAI,CAAC,UAAUrM,IAAI,EAAE;UACtC,OAAOwP,KAAK,CAACnE,IAAI,KAAKrL,IAAI;QAC9B,CAAC,CAAC,EAAE;UACA,IAAI,OAAOwP,KAAK,CAACnE,IAAI,KAAK,UAAU,EAAE;YAClC,OAAO3E,IAAI,CAAC,mIAAmI,CAAC;UACpJ;UAEA,OAAOA,IAAI,CAAC,sBAAsB,GAAG9G,eAAe,CAAC0F,IAAI,CAAC,IAAI,CAAC,GAAG,mDAAmD,GAAGkK,KAAK,CAACnE,IAAI,GAAG,oDAAoD,CAAC;QAC9L;QAEA,IAAIoE,cAAc,IAAI,OAAOA,cAAc,KAAK,QAAQ,KAAK,CAACrK,KAAK,CAACC,OAAO,CAACoK,cAAc,CAAC,IAAIA,cAAc,CAACpD,IAAI,CAAC,UAAUuE,WAAW,EAAE;UACtI,OAAO,OAAOA,WAAW,KAAK,QAAQ;QAC1C,CAAC,CAAC,CAAC,EAAE;UACD,MAAM,IAAIlB,KAAK,CAAC,yCAAyC,GAAGF,KAAK,CAACnE,IAAI,GAAG,wDAAwD,GAAGmE,KAAK,CAACnE,IAAI,GAAG,SAAS,GAAGmE,KAAK,CAACnE,IAAI,GAAG,4CAA4C,CAAC;QAC3N;MACJ;MAEA,OAAO,IAAI;IACf,CAAC;IAED8D,aAAa,CAAC9M,SAAS,CAACwO,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAEX,QAAQ,EAAE;MACzF,IAAIY,MAAM,GAAG,IAAI;MAEjB,IAAIlB,iBAAiB,GAAG,CAAC,CAAC;MAE1B/Q,KAAK,CAACkS,QAAQ,CAACjK,OAAO,CAAC+J,QAAQ,EAAE,UAAUtB,KAAK,EAAE;QAC9C,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC3M,KAAK,EAAE;UACxB;QACJ;QAEA,IAAIoO,YAAY,GAAGzB,KAAK,CAAC3M,KAAK;UAC1B4M,cAAc,GAAGwB,YAAY,CAACH,QAAQ;UACtCI,UAAU,GAAG9M,uBAAuB,CAAC6M,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC;QAEpE,IAAInB,aAAa,GAAGvC,iCAAiC,CAAC2D,UAAU,CAAC;QAEjEH,MAAM,CAACP,qBAAqB,CAAChB,KAAK,EAAEC,cAAc,CAAC;QAEnD,QAAQD,KAAK,CAACnE,IAAI;UACd,KAAKjM,SAAS,CAACG,IAAI;UACnB,KAAKH,SAAS,CAACI,IAAI;UACnB,KAAKJ,SAAS,CAACK,QAAQ;UACvB,KAAKL,SAAS,CAACM,MAAM;UACrB,KAAKN,SAAS,CAACO,KAAK;YAChBkQ,iBAAiB,GAAGkB,MAAM,CAACpB,wBAAwB,CAAC;cAChDH,KAAK,EAAEA,KAAK;cACZK,iBAAiB,EAAEA,iBAAiB;cACpCC,aAAa,EAAEA,aAAa;cAC5BL,cAAc,EAAEA;YACpB,CAAC,CAAC;YACF;UAEJ;YACIU,QAAQ,GAAGY,MAAM,CAAChB,qBAAqB,CAAC;cACpCP,KAAK,EAAEA,KAAK;cACZW,QAAQ,EAAEA,QAAQ;cAClBL,aAAa,EAAEA,aAAa;cAC5BL,cAAc,EAAEA;YACpB,CAAC,CAAC;YACF;QACR;MACJ,CAAC,CAAC;MAEFU,QAAQ,GAAG,IAAI,CAACC,2BAA2B,CAACP,iBAAiB,EAAEM,QAAQ,CAAC;MACxE,OAAOA,QAAQ;IACnB,CAAC;IAEDhB,aAAa,CAAC9M,SAAS,CAAC8O,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MAC/C,IAAIC,MAAM,GAAG,IAAI,CAACvO,KAAK;QACnBiO,QAAQ,GAAGM,MAAM,CAACN,QAAQ;QAC1BjO,KAAK,GAAGuB,uBAAuB,CAACgN,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC;MAEzD,IAAIjB,QAAQ,GAAG5M,QAAQ,CAAC,CAAC,CAAC,EAAEV,KAAK,CAAC;MAElC,IAAIiO,QAAQ,EAAE;QACVX,QAAQ,GAAG,IAAI,CAACU,kBAAkB,CAACC,QAAQ,EAAEX,QAAQ,CAAC;MAC1D;MAEA,OAAOrR,KAAK,CAACiN,aAAa,CAACgD,SAAS,EAAEoB,QAAQ,CAAC;IACnD,CAAC;IAEDzN,WAAW,CAACyM,aAAa,EAAE,IAAI,EAAE,CAAC;MAC9BrN,GAAG,EAAE,WAAW;MAGhB;MACA;MACA;MACA;;MAEA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYuP,GAAG,EAAE,SAASC,MAAMA,CAACC,SAAS,EAAE;QAC5BxC,SAAS,CAACwC,SAAS,GAAGA,SAAS;MACnC;IACJ,CAAC,CAAC,CAAC;IACH,OAAOpC,aAAa;EACxB,CAAC,CAACrQ,KAAK,CAACiQ,SAAS,CAAC,EAAEC,MAAM,CAACwC,SAAS,GAAG;IACnChD,IAAI,EAAE7P,SAAS,CAAC8S,MAAM;IACtBpK,cAAc,EAAE1I,SAAS,CAAC8S,MAAM;IAChCX,QAAQ,EAAEnS,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAACiT,IAAI,CAAC,EAAEjT,SAAS,CAACiT,IAAI,CAAC,CAAC;IAClFC,YAAY,EAAElT,SAAS,CAACuO,MAAM;IAC9B5F,KAAK,EAAE3I,SAAS,CAACmT,IAAI;IACrBrN,uBAAuB,EAAE9F,SAAS,CAACmT,IAAI;IACvCvK,cAAc,EAAE5I,SAAS,CAAC8S,MAAM;IAChChD,IAAI,EAAE9P,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAAC8S,MAAM,CAAC;IACzC/C,IAAI,EAAE/P,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAAC8S,MAAM,CAAC;IACzC9C,QAAQ,EAAEhQ,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAAC8S,MAAM,CAAC;IAC7C9J,mBAAmB,EAAEhJ,SAAS,CAACoT,IAAI;IACnCnD,MAAM,EAAEjQ,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAAC8S,MAAM,CAAC;IAC3C5C,KAAK,EAAElQ,SAAS,CAACgT,OAAO,CAAChT,SAAS,CAAC8S,MAAM,CAAC;IAC1C3J,KAAK,EAAEnJ,SAAS,CAACuO,MAAM;IACvBnF,eAAe,EAAEpJ,SAAS,CAAC8S,MAAM;IACjCO,aAAa,EAAErT,SAAS,CAACuO;EAC7B,CAAC,EAAE8B,MAAM,CAACiD,YAAY,GAAG;IACrB3K,KAAK,EAAE,IAAI;IACX7C,uBAAuB,EAAE;EAC7B,CAAC,EAAEuK,MAAM,CAACkD,IAAI,GAAGnD,SAAS,CAACmD,IAAI,EAAElD,MAAM,CAACmD,MAAM,GAAG,YAAY;IACzD,IAAIC,WAAW,GAAGrD,SAAS,CAACoD,MAAM,CAAC,CAAC;IACpC,IAAI,CAACC,WAAW,EAAE;MACd;MACAA,WAAW,GAAG/D,gBAAgB,CAAC;QAC3BjH,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,CAAC,CAAC;QAClB5C,uBAAuB,EAAE,IAAI;QAC7B8C,cAAc,EAAE,CAAC,CAAC;QAClBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE,EAAE;QAChBE,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,eAAe,EAAE,CAAC;MACtB,CAAC,CAAC;IACN;IAEA,OAAOqK,WAAW;EACtB,CAAC,EAAEnD,KAAK;AACZ,CAAC;AAED,IAAIoD,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EACzC,OAAO,IAAI;AACf,CAAC;AAED,IAAIC,iBAAiB,GAAG1T,cAAc,CAACuI,kBAAkB,EAAEmC,uBAAuB,EAAE+E,gBAAgB,CAAC,CAACgE,aAAa,CAAC;AAEpH,IAAIE,YAAY,GAAGzD,MAAM,CAACwD,iBAAiB,CAAC;AAC5CC,YAAY,CAACC,YAAY,GAAGD,YAAY,CAACJ,MAAM;AAE/C,eAAeI,YAAY;AAC3B,SAASA,YAAY,IAAIzD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}