{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (PedalMarking.DEBUG) log('VexFlow.PedalMarking', args);\n}\nexport class PedalMarking extends Element {\n  static get CATEGORY() {\n    return \"PedalMarking\";\n  }\n  static createSustain(notes) {\n    const pedal = new PedalMarking(notes);\n    return pedal;\n  }\n  static createSostenuto(notes) {\n    const pedal = new PedalMarking(notes);\n    pedal.setType(PedalMarking.type.MIXED);\n    pedal.setCustomText('Sost. Ped.');\n    return pedal;\n  }\n  static createUnaCorda(notes) {\n    const pedal = new PedalMarking(notes);\n    pedal.setType(PedalMarking.type.TEXT);\n    pedal.setCustomText('una corda', 'tre corda');\n    return pedal;\n  }\n  constructor(notes) {\n    super();\n    this.notes = notes;\n    this.type = PedalMarking.type.TEXT;\n    this.line = 0;\n    this.depressText = PedalMarking.GLYPHS.pedalDepress;\n    this.releaseText = PedalMarking.GLYPHS.pedalRelease;\n    this.renderOptions = {\n      bracketHeight: 10,\n      textMarginRight: 6,\n      bracketLineWidth: 1,\n      color: 'black'\n    };\n  }\n  setType(type) {\n    type = typeof type === 'string' ? PedalMarking.typeString[type] : type;\n    if (type >= PedalMarking.type.TEXT && type <= PedalMarking.type.MIXED) {\n      this.type = type;\n    }\n    return this;\n  }\n  setCustomText(depress, release) {\n    this.depressText = depress || '';\n    this.releaseText = release || '';\n    this.setFont(Metrics.getFontInfo('PedalMarking.text'));\n    return this;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  drawBracketed() {\n    const ctx = this.checkContext();\n    let isPedalDepressed = false;\n    let prevX;\n    let prevY;\n    let textWidth = 0;\n    this.notes.forEach((note, index, notes) => {\n      var _a, _b, _c, _d;\n      isPedalDepressed = !isPedalDepressed;\n      const x = note.getAbsoluteX();\n      const y = note.checkStave().getYForBottomText(this.line + 3);\n      if (x < prevX) {\n        throw new RuntimeError('InvalidConfiguration', 'The notes provided must be in order of ascending x positions');\n      }\n      const nextNoteIsSame = notes[index + 1] === note;\n      const prevNoteIsSame = notes[index - 1] === note;\n      let xShift = 0;\n      if (isPedalDepressed) {\n        xShift = prevNoteIsSame ? 5 : 0;\n        if (this.type === PedalMarking.type.MIXED && !prevNoteIsSame) {\n          textWidth = ctx.measureText(this.depressText).width;\n          ctx.fillText(this.depressText, x, y);\n          xShift = textWidth + this.renderOptions.textMarginRight;\n        } else {\n          ctx.beginPath();\n          ctx.moveTo(x, y - this.renderOptions.bracketHeight);\n          ctx.lineTo(x + xShift, y);\n          ctx.stroke();\n          ctx.closePath();\n        }\n      } else {\n        const noteNdx = note.getVoice().getTickables().indexOf(note);\n        const voiceNotes = note.getVoice().getTickables().length;\n        const noteEndX = noteNdx + 1 < voiceNotes ? note.getVoice().getTickables()[noteNdx + 1].getAbsoluteX() : ((_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getX()) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = note.getStave()) === null || _c === void 0 ? void 0 : _c.getWidth()) !== null && _d !== void 0 ? _d : 0);\n        ctx.beginPath();\n        ctx.moveTo(prevX, prevY);\n        ctx.lineTo(nextNoteIsSame ? x - 5 : noteEndX - 5, y);\n        ctx.lineTo(nextNoteIsSame ? x : noteEndX - 5, y - this.renderOptions.bracketHeight);\n        ctx.stroke();\n        ctx.closePath();\n      }\n      prevX = x + xShift;\n      prevY = y;\n    });\n  }\n  drawText() {\n    const ctx = this.checkContext();\n    let isPedalDepressed = false;\n    let textWidth = 0;\n    this.notes.forEach(note => {\n      var _a, _b, _c, _d;\n      isPedalDepressed = !isPedalDepressed;\n      const stave = note.checkStave();\n      const x = note.getAbsoluteX();\n      const y = stave.getYForBottomText(this.line + 3);\n      if (isPedalDepressed) {\n        textWidth = ctx.measureText(this.depressText).width;\n        ctx.fillText(this.depressText, x, y);\n      } else {\n        const noteNdx = note.getVoice().getTickables().indexOf(note);\n        const voiceNotes = note.getVoice().getTickables().length;\n        const noteEndX = noteNdx + 1 < voiceNotes ? note.getVoice().getTickables()[noteNdx + 1].getAbsoluteX() : ((_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getX()) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = note.getStave()) === null || _c === void 0 ? void 0 : _c.getWidth()) !== null && _d !== void 0 ? _d : 0);\n        textWidth = ctx.measureText(this.releaseText).width;\n        ctx.fillText(this.releaseText, noteEndX - textWidth, y);\n      }\n    });\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    ctx.setStrokeStyle(this.renderOptions.color);\n    ctx.setFillStyle(this.renderOptions.color);\n    ctx.setFont(this.font);\n    L('Rendering Pedal Marking');\n    if (this.type === PedalMarking.type.BRACKET || this.type === PedalMarking.type.MIXED) {\n      ctx.setLineWidth(this.renderOptions.bracketLineWidth);\n      this.drawBracketed();\n    } else if (this.type === PedalMarking.type.TEXT) {\n      this.drawText();\n    }\n  }\n}\nPedalMarking.DEBUG = false;\nPedalMarking.GLYPHS = {\n  pedalDepress: Glyphs.keyboardPedalPed,\n  pedalRelease: Glyphs.keyboardPedalUp\n};\nPedalMarking.type = {\n  TEXT: 1,\n  BRACKET: 2,\n  MIXED: 3\n};\nPedalMarking.typeString = {\n  text: PedalMarking.type.TEXT,\n  bracket: PedalMarking.type.BRACKET,\n  mixed: PedalMarking.type.MIXED\n};", "map": {"version": 3, "names": ["Element", "Glyphs", "Metrics", "log", "RuntimeError", "L", "args", "PedalMarking", "DEBUG", "CATEGORY", "createSustain", "notes", "pedal", "createSostenuto", "setType", "type", "MIXED", "setCustomText", "createUnaCorda", "TEXT", "constructor", "line", "depressText", "GLYPHS", "pedalDepress", "releaseText", "pedalRelease", "renderOptions", "bracketHeight", "textMarginRight", "bracketLine<PERSON>id<PERSON>", "color", "typeString", "depress", "release", "setFont", "getFontInfo", "setLine", "drawBracketed", "ctx", "checkContext", "isPedalDepressed", "prevX", "prevY", "textWidth", "for<PERSON>ach", "note", "index", "_a", "_b", "_c", "_d", "x", "getAbsoluteX", "y", "checkStave", "getYForBottomText", "nextNoteIsSame", "prevNoteIsSame", "xShift", "measureText", "width", "fillText", "beginPath", "moveTo", "lineTo", "stroke", "closePath", "noteNdx", "getVoice", "getTickables", "indexOf", "voiceNotes", "length", "noteEndX", "getStave", "getX", "getWidth", "drawText", "stave", "draw", "setRendered", "setStrokeStyle", "setFillStyle", "font", "BRACKET", "setLineWidth", "keyboardPedalPed", "keyboardPedalUp", "text", "bracket", "mixed"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/pedalmarking.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { log, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (PedalMarking.DEBUG)\n        log('VexFlow.PedalMarking', args);\n}\nexport class PedalMarking extends Element {\n    static get CATEGORY() {\n        return \"PedalMarking\";\n    }\n    static createSustain(notes) {\n        const pedal = new PedalMarking(notes);\n        return pedal;\n    }\n    static createSostenuto(notes) {\n        const pedal = new PedalMarking(notes);\n        pedal.setType(PedalMarking.type.MIXED);\n        pedal.setCustomText('Sost. Ped.');\n        return pedal;\n    }\n    static createUnaCorda(notes) {\n        const pedal = new PedalMarking(notes);\n        pedal.setType(PedalMarking.type.TEXT);\n        pedal.setCustomText('una corda', 'tre corda');\n        return pedal;\n    }\n    constructor(notes) {\n        super();\n        this.notes = notes;\n        this.type = PedalMarking.type.TEXT;\n        this.line = 0;\n        this.depressText = PedalMarking.GLYPHS.pedalDepress;\n        this.releaseText = PedalMarking.GLYPHS.pedalRelease;\n        this.renderOptions = {\n            bracketHeight: 10,\n            textMarginRight: 6,\n            bracketLineWidth: 1,\n            color: 'black',\n        };\n    }\n    setType(type) {\n        type = typeof type === 'string' ? PedalMarking.typeString[type] : type;\n        if (type >= PedalMarking.type.TEXT && type <= PedalMarking.type.MIXED) {\n            this.type = type;\n        }\n        return this;\n    }\n    setCustomText(depress, release) {\n        this.depressText = depress || '';\n        this.releaseText = release || '';\n        this.setFont(Metrics.getFontInfo('PedalMarking.text'));\n        return this;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    drawBracketed() {\n        const ctx = this.checkContext();\n        let isPedalDepressed = false;\n        let prevX;\n        let prevY;\n        let textWidth = 0;\n        this.notes.forEach((note, index, notes) => {\n            var _a, _b, _c, _d;\n            isPedalDepressed = !isPedalDepressed;\n            const x = note.getAbsoluteX();\n            const y = note.checkStave().getYForBottomText(this.line + 3);\n            if (x < prevX) {\n                throw new RuntimeError('InvalidConfiguration', 'The notes provided must be in order of ascending x positions');\n            }\n            const nextNoteIsSame = notes[index + 1] === note;\n            const prevNoteIsSame = notes[index - 1] === note;\n            let xShift = 0;\n            if (isPedalDepressed) {\n                xShift = prevNoteIsSame ? 5 : 0;\n                if (this.type === PedalMarking.type.MIXED && !prevNoteIsSame) {\n                    textWidth = ctx.measureText(this.depressText).width;\n                    ctx.fillText(this.depressText, x, y);\n                    xShift = textWidth + this.renderOptions.textMarginRight;\n                }\n                else {\n                    ctx.beginPath();\n                    ctx.moveTo(x, y - this.renderOptions.bracketHeight);\n                    ctx.lineTo(x + xShift, y);\n                    ctx.stroke();\n                    ctx.closePath();\n                }\n            }\n            else {\n                const noteNdx = note.getVoice().getTickables().indexOf(note);\n                const voiceNotes = note.getVoice().getTickables().length;\n                const noteEndX = noteNdx + 1 < voiceNotes\n                    ?\n                        note.getVoice().getTickables()[noteNdx + 1].getAbsoluteX()\n                    :\n                        ((_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getX()) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = note.getStave()) === null || _c === void 0 ? void 0 : _c.getWidth()) !== null && _d !== void 0 ? _d : 0);\n                ctx.beginPath();\n                ctx.moveTo(prevX, prevY);\n                ctx.lineTo(nextNoteIsSame ? x - 5 : noteEndX - 5, y);\n                ctx.lineTo(nextNoteIsSame ? x : noteEndX - 5, y - this.renderOptions.bracketHeight);\n                ctx.stroke();\n                ctx.closePath();\n            }\n            prevX = x + xShift;\n            prevY = y;\n        });\n    }\n    drawText() {\n        const ctx = this.checkContext();\n        let isPedalDepressed = false;\n        let textWidth = 0;\n        this.notes.forEach((note) => {\n            var _a, _b, _c, _d;\n            isPedalDepressed = !isPedalDepressed;\n            const stave = note.checkStave();\n            const x = note.getAbsoluteX();\n            const y = stave.getYForBottomText(this.line + 3);\n            if (isPedalDepressed) {\n                textWidth = ctx.measureText(this.depressText).width;\n                ctx.fillText(this.depressText, x, y);\n            }\n            else {\n                const noteNdx = note.getVoice().getTickables().indexOf(note);\n                const voiceNotes = note.getVoice().getTickables().length;\n                const noteEndX = noteNdx + 1 < voiceNotes\n                    ?\n                        note.getVoice().getTickables()[noteNdx + 1].getAbsoluteX()\n                    :\n                        ((_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getX()) !== null && _b !== void 0 ? _b : 0) + ((_d = (_c = note.getStave()) === null || _c === void 0 ? void 0 : _c.getWidth()) !== null && _d !== void 0 ? _d : 0);\n                textWidth = ctx.measureText(this.releaseText).width;\n                ctx.fillText(this.releaseText, noteEndX - textWidth, y);\n            }\n        });\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        ctx.setStrokeStyle(this.renderOptions.color);\n        ctx.setFillStyle(this.renderOptions.color);\n        ctx.setFont(this.font);\n        L('Rendering Pedal Marking');\n        if (this.type === PedalMarking.type.BRACKET || this.type === PedalMarking.type.MIXED) {\n            ctx.setLineWidth(this.renderOptions.bracketLineWidth);\n            this.drawBracketed();\n        }\n        else if (this.type === PedalMarking.type.TEXT) {\n            this.drawText();\n        }\n    }\n}\nPedalMarking.DEBUG = false;\nPedalMarking.GLYPHS = {\n    pedalDepress: Glyphs.keyboardPedalPed,\n    pedalRelease: Glyphs.keyboardPedalUp,\n};\nPedalMarking.type = {\n    TEXT: 1,\n    BRACKET: 2,\n    MIXED: 3,\n};\nPedalMarking.typeString = {\n    text: PedalMarking.type.TEXT,\n    bracket: PedalMarking.type.BRACKET,\n    mixed: PedalMarking.type.MIXED,\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,YAAY,CAACC,KAAK,EAClBL,GAAG,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AACzC;AACA,OAAO,MAAMC,YAAY,SAASP,OAAO,CAAC;EACtC,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,OAAOC,aAAaA,CAACC,KAAK,EAAE;IACxB,MAAMC,KAAK,GAAG,IAAIL,YAAY,CAACI,KAAK,CAAC;IACrC,OAAOC,KAAK;EAChB;EACA,OAAOC,eAAeA,CAACF,KAAK,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAIL,YAAY,CAACI,KAAK,CAAC;IACrCC,KAAK,CAACE,OAAO,CAACP,YAAY,CAACQ,IAAI,CAACC,KAAK,CAAC;IACtCJ,KAAK,CAACK,aAAa,CAAC,YAAY,CAAC;IACjC,OAAOL,KAAK;EAChB;EACA,OAAOM,cAAcA,CAACP,KAAK,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIL,YAAY,CAACI,KAAK,CAAC;IACrCC,KAAK,CAACE,OAAO,CAACP,YAAY,CAACQ,IAAI,CAACI,IAAI,CAAC;IACrCP,KAAK,CAACK,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC;IAC7C,OAAOL,KAAK;EAChB;EACAQ,WAAWA,CAACT,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,IAAI,GAAGR,YAAY,CAACQ,IAAI,CAACI,IAAI;IAClC,IAAI,CAACE,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,WAAW,GAAGf,YAAY,CAACgB,MAAM,CAACC,YAAY;IACnD,IAAI,CAACC,WAAW,GAAGlB,YAAY,CAACgB,MAAM,CAACG,YAAY;IACnD,IAAI,CAACC,aAAa,GAAG;MACjBC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,CAAC;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,KAAK,EAAE;IACX,CAAC;EACL;EACAjB,OAAOA,CAACC,IAAI,EAAE;IACVA,IAAI,GAAG,OAAOA,IAAI,KAAK,QAAQ,GAAGR,YAAY,CAACyB,UAAU,CAACjB,IAAI,CAAC,GAAGA,IAAI;IACtE,IAAIA,IAAI,IAAIR,YAAY,CAACQ,IAAI,CAACI,IAAI,IAAIJ,IAAI,IAAIR,YAAY,CAACQ,IAAI,CAACC,KAAK,EAAE;MACnE,IAAI,CAACD,IAAI,GAAGA,IAAI;IACpB;IACA,OAAO,IAAI;EACf;EACAE,aAAaA,CAACgB,OAAO,EAAEC,OAAO,EAAE;IAC5B,IAAI,CAACZ,WAAW,GAAGW,OAAO,IAAI,EAAE;IAChC,IAAI,CAACR,WAAW,GAAGS,OAAO,IAAI,EAAE;IAChC,IAAI,CAACC,OAAO,CAACjC,OAAO,CAACkC,WAAW,CAAC,mBAAmB,CAAC,CAAC;IACtD,OAAO,IAAI;EACf;EACAC,OAAOA,CAAChB,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAiB,aAAaA,CAAA,EAAG;IACZ,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIC,KAAK;IACT,IAAIC,KAAK;IACT,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,EAAEpC,KAAK,KAAK;MACvC,IAAIqC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAClBV,gBAAgB,GAAG,CAACA,gBAAgB;MACpC,MAAMW,CAAC,GAAGN,IAAI,CAACO,YAAY,CAAC,CAAC;MAC7B,MAAMC,CAAC,GAAGR,IAAI,CAACS,UAAU,CAAC,CAAC,CAACC,iBAAiB,CAAC,IAAI,CAACnC,IAAI,GAAG,CAAC,CAAC;MAC5D,IAAI+B,CAAC,GAAGV,KAAK,EAAE;QACX,MAAM,IAAItC,YAAY,CAAC,sBAAsB,EAAE,8DAA8D,CAAC;MAClH;MACA,MAAMqD,cAAc,GAAG9C,KAAK,CAACoC,KAAK,GAAG,CAAC,CAAC,KAAKD,IAAI;MAChD,MAAMY,cAAc,GAAG/C,KAAK,CAACoC,KAAK,GAAG,CAAC,CAAC,KAAKD,IAAI;MAChD,IAAIa,MAAM,GAAG,CAAC;MACd,IAAIlB,gBAAgB,EAAE;QAClBkB,MAAM,GAAGD,cAAc,GAAG,CAAC,GAAG,CAAC;QAC/B,IAAI,IAAI,CAAC3C,IAAI,KAAKR,YAAY,CAACQ,IAAI,CAACC,KAAK,IAAI,CAAC0C,cAAc,EAAE;UAC1Dd,SAAS,GAAGL,GAAG,CAACqB,WAAW,CAAC,IAAI,CAACtC,WAAW,CAAC,CAACuC,KAAK;UACnDtB,GAAG,CAACuB,QAAQ,CAAC,IAAI,CAACxC,WAAW,EAAE8B,CAAC,EAAEE,CAAC,CAAC;UACpCK,MAAM,GAAGf,SAAS,GAAG,IAAI,CAACjB,aAAa,CAACE,eAAe;QAC3D,CAAC,MACI;UACDU,GAAG,CAACwB,SAAS,CAAC,CAAC;UACfxB,GAAG,CAACyB,MAAM,CAACZ,CAAC,EAAEE,CAAC,GAAG,IAAI,CAAC3B,aAAa,CAACC,aAAa,CAAC;UACnDW,GAAG,CAAC0B,MAAM,CAACb,CAAC,GAAGO,MAAM,EAAEL,CAAC,CAAC;UACzBf,GAAG,CAAC2B,MAAM,CAAC,CAAC;UACZ3B,GAAG,CAAC4B,SAAS,CAAC,CAAC;QACnB;MACJ,CAAC,MACI;QACD,MAAMC,OAAO,GAAGtB,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAACzB,IAAI,CAAC;QAC5D,MAAM0B,UAAU,GAAG1B,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACG,MAAM;QACxD,MAAMC,QAAQ,GAAGN,OAAO,GAAG,CAAC,GAAGI,UAAU,GAEjC1B,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACF,OAAO,GAAG,CAAC,CAAC,CAACf,YAAY,CAAC,CAAC,GAE1D,CAAC,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGF,IAAI,CAAC6B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;QACjPZ,GAAG,CAACwB,SAAS,CAAC,CAAC;QACfxB,GAAG,CAACyB,MAAM,CAACtB,KAAK,EAAEC,KAAK,CAAC;QACxBJ,GAAG,CAAC0B,MAAM,CAACR,cAAc,GAAGL,CAAC,GAAG,CAAC,GAAGsB,QAAQ,GAAG,CAAC,EAAEpB,CAAC,CAAC;QACpDf,GAAG,CAAC0B,MAAM,CAACR,cAAc,GAAGL,CAAC,GAAGsB,QAAQ,GAAG,CAAC,EAAEpB,CAAC,GAAG,IAAI,CAAC3B,aAAa,CAACC,aAAa,CAAC;QACnFW,GAAG,CAAC2B,MAAM,CAAC,CAAC;QACZ3B,GAAG,CAAC4B,SAAS,CAAC,CAAC;MACnB;MACAzB,KAAK,GAAGU,CAAC,GAAGO,MAAM;MAClBhB,KAAK,GAAGW,CAAC;IACb,CAAC,CAAC;EACN;EACAwB,QAAQA,CAAA,EAAG;IACP,MAAMvC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAIG,SAAS,GAAG,CAAC;IACjB,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAAEC,IAAI,IAAK;MACzB,IAAIE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAClBV,gBAAgB,GAAG,CAACA,gBAAgB;MACpC,MAAMsC,KAAK,GAAGjC,IAAI,CAACS,UAAU,CAAC,CAAC;MAC/B,MAAMH,CAAC,GAAGN,IAAI,CAACO,YAAY,CAAC,CAAC;MAC7B,MAAMC,CAAC,GAAGyB,KAAK,CAACvB,iBAAiB,CAAC,IAAI,CAACnC,IAAI,GAAG,CAAC,CAAC;MAChD,IAAIoB,gBAAgB,EAAE;QAClBG,SAAS,GAAGL,GAAG,CAACqB,WAAW,CAAC,IAAI,CAACtC,WAAW,CAAC,CAACuC,KAAK;QACnDtB,GAAG,CAACuB,QAAQ,CAAC,IAAI,CAACxC,WAAW,EAAE8B,CAAC,EAAEE,CAAC,CAAC;MACxC,CAAC,MACI;QACD,MAAMc,OAAO,GAAGtB,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACC,OAAO,CAACzB,IAAI,CAAC;QAC5D,MAAM0B,UAAU,GAAG1B,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACG,MAAM;QACxD,MAAMC,QAAQ,GAAGN,OAAO,GAAG,CAAC,GAAGI,UAAU,GAEjC1B,IAAI,CAACuB,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAACF,OAAO,GAAG,CAAC,CAAC,CAACf,YAAY,CAAC,CAAC,GAE1D,CAAC,CAACJ,EAAE,GAAG,CAACD,EAAE,GAAGF,IAAI,CAAC6B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,KAAK,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGJ,IAAI,CAAC6B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;QACjPP,SAAS,GAAGL,GAAG,CAACqB,WAAW,CAAC,IAAI,CAACnC,WAAW,CAAC,CAACoC,KAAK;QACnDtB,GAAG,CAACuB,QAAQ,CAAC,IAAI,CAACrC,WAAW,EAAEiD,QAAQ,GAAG9B,SAAS,EAAEU,CAAC,CAAC;MAC3D;IACJ,CAAC,CAAC;EACN;EACA0B,IAAIA,CAAA,EAAG;IACH,MAAMzC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACyC,WAAW,CAAC,CAAC;IAClB1C,GAAG,CAAC2C,cAAc,CAAC,IAAI,CAACvD,aAAa,CAACI,KAAK,CAAC;IAC5CQ,GAAG,CAAC4C,YAAY,CAAC,IAAI,CAACxD,aAAa,CAACI,KAAK,CAAC;IAC1CQ,GAAG,CAACJ,OAAO,CAAC,IAAI,CAACiD,IAAI,CAAC;IACtB/E,CAAC,CAAC,yBAAyB,CAAC;IAC5B,IAAI,IAAI,CAACU,IAAI,KAAKR,YAAY,CAACQ,IAAI,CAACsE,OAAO,IAAI,IAAI,CAACtE,IAAI,KAAKR,YAAY,CAACQ,IAAI,CAACC,KAAK,EAAE;MAClFuB,GAAG,CAAC+C,YAAY,CAAC,IAAI,CAAC3D,aAAa,CAACG,gBAAgB,CAAC;MACrD,IAAI,CAACQ,aAAa,CAAC,CAAC;IACxB,CAAC,MACI,IAAI,IAAI,CAACvB,IAAI,KAAKR,YAAY,CAACQ,IAAI,CAACI,IAAI,EAAE;MAC3C,IAAI,CAAC2D,QAAQ,CAAC,CAAC;IACnB;EACJ;AACJ;AACAvE,YAAY,CAACC,KAAK,GAAG,KAAK;AAC1BD,YAAY,CAACgB,MAAM,GAAG;EAClBC,YAAY,EAAEvB,MAAM,CAACsF,gBAAgB;EACrC7D,YAAY,EAAEzB,MAAM,CAACuF;AACzB,CAAC;AACDjF,YAAY,CAACQ,IAAI,GAAG;EAChBI,IAAI,EAAE,CAAC;EACPkE,OAAO,EAAE,CAAC;EACVrE,KAAK,EAAE;AACX,CAAC;AACDT,YAAY,CAACyB,UAAU,GAAG;EACtByD,IAAI,EAAElF,YAAY,CAACQ,IAAI,CAACI,IAAI;EAC5BuE,OAAO,EAAEnF,YAAY,CAACQ,IAAI,CAACsE,OAAO;EAClCM,KAAK,EAAEpF,YAAY,CAACQ,IAAI,CAACC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}