{"ast": null, "code": "import { Element } from './element.js';\nimport { Modifier } from './modifier.js';\nimport { RuntimeError } from './util.js';\nexport class StaveHairpin extends Element {\n  static get CATEGORY() {\n    return \"StaveHairpin\";\n  }\n  static FormatByTicksAndDraw(ctx, formatter, notes, type, position, options) {\n    var _a, _b;\n    const ppt = formatter.pixelsPerTick;\n    if (ppt === null) {\n      throw new RuntimeError('BadArguments', 'A valid Formatter must be provide to draw offsets by ticks.');\n    }\n    const leftShiftPx = ppt * ((_a = options.leftShiftTicks) !== null && _a !== void 0 ? _a : 0);\n    const rightShiftPx = ppt * ((_b = options.rightShiftTicks) !== null && _b !== void 0 ? _b : 0);\n    const hairpinOptions = {\n      height: options.height,\n      yShift: options.yShift,\n      leftShiftPx,\n      rightShiftPx,\n      rightShiftTicks: 0,\n      leftShiftTicks: 0\n    };\n    new StaveHairpin({\n      firstNote: notes.firstNote,\n      lastNote: notes.lastNote\n    }, type).setContext(ctx).setRenderOptions(hairpinOptions).setPosition(position).drawWithStyle();\n  }\n  constructor(notes, type) {\n    super();\n    this.setNotes(notes);\n    this.hairpin = type;\n    this.position = Modifier.Position.BELOW;\n    this.renderOptions = {\n      height: 10,\n      yShift: 0,\n      leftShiftPx: 0,\n      rightShiftPx: 0,\n      rightShiftTicks: 0,\n      leftShiftTicks: 0\n    };\n  }\n  setPosition(position) {\n    if (position === Modifier.Position.ABOVE || position === Modifier.Position.BELOW) {\n      this.position = position;\n    }\n    return this;\n  }\n  setRenderOptions(options) {\n    this.renderOptions = options;\n    return this;\n  }\n  setNotes(notes) {\n    if (!notes.firstNote && !notes.lastNote) {\n      throw new RuntimeError('BadArguments', 'Hairpin needs to have either firstNote or lastNote set.');\n    }\n    this.notes = notes;\n    this.firstNote = notes.firstNote;\n    this.lastNote = notes.lastNote;\n    return this;\n  }\n  renderHairpin(params) {\n    const ctx = this.checkContext();\n    let dis = this.renderOptions.yShift + 20;\n    let yShift = params.firstY;\n    if (this.position === Modifier.Position.ABOVE) {\n      dis = -dis + 30;\n      yShift = params.firstY - params.staffHeight;\n    }\n    const leftShiftPx = this.renderOptions.leftShiftPx;\n    const rightShiftPx = this.renderOptions.rightShiftPx;\n    ctx.beginPath();\n    switch (this.hairpin) {\n      case StaveHairpin.type.CRESC:\n        ctx.moveTo(params.lastX + rightShiftPx, yShift + dis);\n        ctx.lineTo(params.firstX + leftShiftPx, yShift + this.renderOptions.height / 2 + dis);\n        ctx.lineTo(params.lastX + rightShiftPx, yShift + this.renderOptions.height + dis);\n        break;\n      case StaveHairpin.type.DECRESC:\n        ctx.moveTo(params.firstX + leftShiftPx, yShift + dis);\n        ctx.lineTo(params.lastX + rightShiftPx, yShift + this.renderOptions.height / 2 + dis);\n        ctx.lineTo(params.firstX + leftShiftPx, yShift + this.renderOptions.height + dis);\n        break;\n      default:\n        break;\n    }\n    ctx.stroke();\n    ctx.closePath();\n  }\n  draw() {\n    this.checkContext();\n    this.setRendered();\n    const firstNote = this.firstNote;\n    const lastNote = this.lastNote;\n    if (!firstNote || !lastNote) throw new RuntimeError('NoNote', 'Notes required to draw');\n    const start = firstNote.getModifierStartXY(this.position, 0);\n    const end = lastNote.getModifierStartXY(this.position, 0);\n    this.renderHairpin({\n      firstX: start.x,\n      lastX: end.x,\n      firstY: firstNote.checkStave().getY() + firstNote.checkStave().getHeight(),\n      lastY: lastNote.checkStave().getY() + lastNote.checkStave().getHeight(),\n      staffHeight: firstNote.checkStave().getHeight()\n    });\n  }\n}\nStaveHairpin.type = {\n  CRESC: 1,\n  DECRESC: 2\n};", "map": {"version": 3, "names": ["Element", "Modifier", "RuntimeError", "StaveHairpin", "CATEGORY", "FormatByTicksAndDraw", "ctx", "formatter", "notes", "type", "position", "options", "_a", "_b", "ppt", "pixelsPerTick", "leftShiftPx", "leftShiftTicks", "rightShiftPx", "rightShiftTicks", "hairpinOptions", "height", "yShift", "firstNote", "lastNote", "setContext", "setRenderOptions", "setPosition", "drawWithStyle", "constructor", "setNotes", "hairpin", "Position", "BELOW", "renderOptions", "ABOVE", "<PERSON><PERSON><PERSON><PERSON>", "params", "checkContext", "dis", "firstY", "staffHeight", "beginPath", "CRESC", "moveTo", "lastX", "lineTo", "firstX", "DECRESC", "stroke", "closePath", "draw", "setRendered", "start", "getModifierStartXY", "end", "x", "checkStave", "getY", "getHeight", "lastY"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavehairpin.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Modifier } from './modifier.js';\nimport { RuntimeError } from './util.js';\nexport class StaveHairpin extends Element {\n    static get CATEGORY() {\n        return \"StaveHairpin\";\n    }\n    static FormatByTicksAndDraw(ctx, formatter, notes, type, position, options) {\n        var _a, _b;\n        const ppt = formatter.pixelsPerTick;\n        if (ppt === null) {\n            throw new RuntimeError('BadArguments', 'A valid Formatter must be provide to draw offsets by ticks.');\n        }\n        const leftShiftPx = ppt * ((_a = options.leftShiftTicks) !== null && _a !== void 0 ? _a : 0);\n        const rightShiftPx = ppt * ((_b = options.rightShiftTicks) !== null && _b !== void 0 ? _b : 0);\n        const hairpinOptions = {\n            height: options.height,\n            yShift: options.yShift,\n            leftShiftPx,\n            rightShiftPx,\n            rightShiftTicks: 0,\n            leftShiftTicks: 0,\n        };\n        new StaveHairpin({\n            firstNote: notes.firstNote,\n            lastNote: notes.lastNote,\n        }, type)\n            .setContext(ctx)\n            .setRenderOptions(hairpinOptions)\n            .setPosition(position)\n            .drawWithStyle();\n    }\n    constructor(notes, type) {\n        super();\n        this.setNotes(notes);\n        this.hairpin = type;\n        this.position = Modifier.Position.BELOW;\n        this.renderOptions = {\n            height: 10,\n            yShift: 0,\n            leftShiftPx: 0,\n            rightShiftPx: 0,\n            rightShiftTicks: 0,\n            leftShiftTicks: 0,\n        };\n    }\n    setPosition(position) {\n        if (position === Modifier.Position.ABOVE || position === Modifier.Position.BELOW) {\n            this.position = position;\n        }\n        return this;\n    }\n    setRenderOptions(options) {\n        this.renderOptions = options;\n        return this;\n    }\n    setNotes(notes) {\n        if (!notes.firstNote && !notes.lastNote) {\n            throw new RuntimeError('BadArguments', 'Hairpin needs to have either firstNote or lastNote set.');\n        }\n        this.notes = notes;\n        this.firstNote = notes.firstNote;\n        this.lastNote = notes.lastNote;\n        return this;\n    }\n    renderHairpin(params) {\n        const ctx = this.checkContext();\n        let dis = this.renderOptions.yShift + 20;\n        let yShift = params.firstY;\n        if (this.position === Modifier.Position.ABOVE) {\n            dis = -dis + 30;\n            yShift = params.firstY - params.staffHeight;\n        }\n        const leftShiftPx = this.renderOptions.leftShiftPx;\n        const rightShiftPx = this.renderOptions.rightShiftPx;\n        ctx.beginPath();\n        switch (this.hairpin) {\n            case StaveHairpin.type.CRESC:\n                ctx.moveTo(params.lastX + rightShiftPx, yShift + dis);\n                ctx.lineTo(params.firstX + leftShiftPx, yShift + this.renderOptions.height / 2 + dis);\n                ctx.lineTo(params.lastX + rightShiftPx, yShift + this.renderOptions.height + dis);\n                break;\n            case StaveHairpin.type.DECRESC:\n                ctx.moveTo(params.firstX + leftShiftPx, yShift + dis);\n                ctx.lineTo(params.lastX + rightShiftPx, yShift + this.renderOptions.height / 2 + dis);\n                ctx.lineTo(params.firstX + leftShiftPx, yShift + this.renderOptions.height + dis);\n                break;\n            default:\n                break;\n        }\n        ctx.stroke();\n        ctx.closePath();\n    }\n    draw() {\n        this.checkContext();\n        this.setRendered();\n        const firstNote = this.firstNote;\n        const lastNote = this.lastNote;\n        if (!firstNote || !lastNote)\n            throw new RuntimeError('NoNote', 'Notes required to draw');\n        const start = firstNote.getModifierStartXY(this.position, 0);\n        const end = lastNote.getModifierStartXY(this.position, 0);\n        this.renderHairpin({\n            firstX: start.x,\n            lastX: end.x,\n            firstY: firstNote.checkStave().getY() + firstNote.checkStave().getHeight(),\n            lastY: lastNote.checkStave().getY() + lastNote.checkStave().getHeight(),\n            staffHeight: firstNote.checkStave().getHeight(),\n        });\n    }\n}\nStaveHairpin.type = {\n    CRESC: 1,\n    DECRESC: 2,\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,YAAY,SAASH,OAAO,CAAC;EACtC,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,OAAOC,oBAAoBA,CAACC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACxE,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,GAAG,GAAGP,SAAS,CAACQ,aAAa;IACnC,IAAID,GAAG,KAAK,IAAI,EAAE;MACd,MAAM,IAAIZ,YAAY,CAAC,cAAc,EAAE,6DAA6D,CAAC;IACzG;IACA,MAAMc,WAAW,GAAGF,GAAG,IAAI,CAACF,EAAE,GAAGD,OAAO,CAACM,cAAc,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAC5F,MAAMM,YAAY,GAAGJ,GAAG,IAAI,CAACD,EAAE,GAAGF,OAAO,CAACQ,eAAe,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAC9F,MAAMO,cAAc,GAAG;MACnBC,MAAM,EAAEV,OAAO,CAACU,MAAM;MACtBC,MAAM,EAAEX,OAAO,CAACW,MAAM;MACtBN,WAAW;MACXE,YAAY;MACZC,eAAe,EAAE,CAAC;MAClBF,cAAc,EAAE;IACpB,CAAC;IACD,IAAId,YAAY,CAAC;MACboB,SAAS,EAAEf,KAAK,CAACe,SAAS;MAC1BC,QAAQ,EAAEhB,KAAK,CAACgB;IACpB,CAAC,EAAEf,IAAI,CAAC,CACHgB,UAAU,CAACnB,GAAG,CAAC,CACfoB,gBAAgB,CAACN,cAAc,CAAC,CAChCO,WAAW,CAACjB,QAAQ,CAAC,CACrBkB,aAAa,CAAC,CAAC;EACxB;EACAC,WAAWA,CAACrB,KAAK,EAAEC,IAAI,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACqB,QAAQ,CAACtB,KAAK,CAAC;IACpB,IAAI,CAACuB,OAAO,GAAGtB,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAGT,QAAQ,CAAC+B,QAAQ,CAACC,KAAK;IACvC,IAAI,CAACC,aAAa,GAAG;MACjBb,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,CAAC;MACTN,WAAW,EAAE,CAAC;MACdE,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBF,cAAc,EAAE;IACpB,CAAC;EACL;EACAU,WAAWA,CAACjB,QAAQ,EAAE;IAClB,IAAIA,QAAQ,KAAKT,QAAQ,CAAC+B,QAAQ,CAACG,KAAK,IAAIzB,QAAQ,KAAKT,QAAQ,CAAC+B,QAAQ,CAACC,KAAK,EAAE;MAC9E,IAAI,CAACvB,QAAQ,GAAGA,QAAQ;IAC5B;IACA,OAAO,IAAI;EACf;EACAgB,gBAAgBA,CAACf,OAAO,EAAE;IACtB,IAAI,CAACuB,aAAa,GAAGvB,OAAO;IAC5B,OAAO,IAAI;EACf;EACAmB,QAAQA,CAACtB,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,CAACe,SAAS,IAAI,CAACf,KAAK,CAACgB,QAAQ,EAAE;MACrC,MAAM,IAAItB,YAAY,CAAC,cAAc,EAAE,yDAAyD,CAAC;IACrG;IACA,IAAI,CAACM,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACe,SAAS,GAAGf,KAAK,CAACe,SAAS;IAChC,IAAI,CAACC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IAC9B,OAAO,IAAI;EACf;EACAY,aAAaA,CAACC,MAAM,EAAE;IAClB,MAAM/B,GAAG,GAAG,IAAI,CAACgC,YAAY,CAAC,CAAC;IAC/B,IAAIC,GAAG,GAAG,IAAI,CAACL,aAAa,CAACZ,MAAM,GAAG,EAAE;IACxC,IAAIA,MAAM,GAAGe,MAAM,CAACG,MAAM;IAC1B,IAAI,IAAI,CAAC9B,QAAQ,KAAKT,QAAQ,CAAC+B,QAAQ,CAACG,KAAK,EAAE;MAC3CI,GAAG,GAAG,CAACA,GAAG,GAAG,EAAE;MACfjB,MAAM,GAAGe,MAAM,CAACG,MAAM,GAAGH,MAAM,CAACI,WAAW;IAC/C;IACA,MAAMzB,WAAW,GAAG,IAAI,CAACkB,aAAa,CAAClB,WAAW;IAClD,MAAME,YAAY,GAAG,IAAI,CAACgB,aAAa,CAAChB,YAAY;IACpDZ,GAAG,CAACoC,SAAS,CAAC,CAAC;IACf,QAAQ,IAAI,CAACX,OAAO;MAChB,KAAK5B,YAAY,CAACM,IAAI,CAACkC,KAAK;QACxBrC,GAAG,CAACsC,MAAM,CAACP,MAAM,CAACQ,KAAK,GAAG3B,YAAY,EAAEI,MAAM,GAAGiB,GAAG,CAAC;QACrDjC,GAAG,CAACwC,MAAM,CAACT,MAAM,CAACU,MAAM,GAAG/B,WAAW,EAAEM,MAAM,GAAG,IAAI,CAACY,aAAa,CAACb,MAAM,GAAG,CAAC,GAAGkB,GAAG,CAAC;QACrFjC,GAAG,CAACwC,MAAM,CAACT,MAAM,CAACQ,KAAK,GAAG3B,YAAY,EAAEI,MAAM,GAAG,IAAI,CAACY,aAAa,CAACb,MAAM,GAAGkB,GAAG,CAAC;QACjF;MACJ,KAAKpC,YAAY,CAACM,IAAI,CAACuC,OAAO;QAC1B1C,GAAG,CAACsC,MAAM,CAACP,MAAM,CAACU,MAAM,GAAG/B,WAAW,EAAEM,MAAM,GAAGiB,GAAG,CAAC;QACrDjC,GAAG,CAACwC,MAAM,CAACT,MAAM,CAACQ,KAAK,GAAG3B,YAAY,EAAEI,MAAM,GAAG,IAAI,CAACY,aAAa,CAACb,MAAM,GAAG,CAAC,GAAGkB,GAAG,CAAC;QACrFjC,GAAG,CAACwC,MAAM,CAACT,MAAM,CAACU,MAAM,GAAG/B,WAAW,EAAEM,MAAM,GAAG,IAAI,CAACY,aAAa,CAACb,MAAM,GAAGkB,GAAG,CAAC;QACjF;MACJ;QACI;IACR;IACAjC,GAAG,CAAC2C,MAAM,CAAC,CAAC;IACZ3C,GAAG,CAAC4C,SAAS,CAAC,CAAC;EACnB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACb,YAAY,CAAC,CAAC;IACnB,IAAI,CAACc,WAAW,CAAC,CAAC;IAClB,MAAM7B,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAACD,SAAS,IAAI,CAACC,QAAQ,EACvB,MAAM,IAAItB,YAAY,CAAC,QAAQ,EAAE,wBAAwB,CAAC;IAC9D,MAAMmD,KAAK,GAAG9B,SAAS,CAAC+B,kBAAkB,CAAC,IAAI,CAAC5C,QAAQ,EAAE,CAAC,CAAC;IAC5D,MAAM6C,GAAG,GAAG/B,QAAQ,CAAC8B,kBAAkB,CAAC,IAAI,CAAC5C,QAAQ,EAAE,CAAC,CAAC;IACzD,IAAI,CAAC0B,aAAa,CAAC;MACfW,MAAM,EAAEM,KAAK,CAACG,CAAC;MACfX,KAAK,EAAEU,GAAG,CAACC,CAAC;MACZhB,MAAM,EAAEjB,SAAS,CAACkC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAGnC,SAAS,CAACkC,UAAU,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;MAC1EC,KAAK,EAAEpC,QAAQ,CAACiC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAGlC,QAAQ,CAACiC,UAAU,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC;MACvElB,WAAW,EAAElB,SAAS,CAACkC,UAAU,CAAC,CAAC,CAACE,SAAS,CAAC;IAClD,CAAC,CAAC;EACN;AACJ;AACAxD,YAAY,CAACM,IAAI,GAAG;EAChBkC,KAAK,EAAE,CAAC;EACRK,OAAO,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}