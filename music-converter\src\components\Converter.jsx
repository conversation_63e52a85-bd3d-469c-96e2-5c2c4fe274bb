
import React, { useEffect, useState} from 'react';
import { Helmet } from 'react-helmet';

function Converter() {

  // VEXTAB SETUP

  const defaultSetup = 'tabstave notation=true tablature=false';
  const defaultNotes = ['G/4'];
  const [key, setKey] = useState('G');
  const [time, setTime] = useState('4/4');
  const [notes, setNotes] = useState(defaultNotes);
  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${key} notes=${notes.join(' ')}`);


  // GRAPHIC INPUT CONSTANTS
  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];

  return (
    <main>
      {/* ----- INPUT AREA ----- */}
      <div>
        <Helmet>
          <script src="https://unpkg.com/vextab/releases/div.prod.js"></script>
        </Helmet>
        <section>
          <h3>SAY SOMETHING!</h3>
          <div style={{ width: '100%' }}>
            <div class="vex-tabdiv" width="800px" scale="1.0" editor="true" editor-height="80px">tabstave</div>
          </div>
          



        </section>
        
      </div>

      {/* ----- OUTPUT AREA ----- */}
      <div>
        <section>
          <h3>Output</h3>
        </section>
      </div>

    </main>
  )
}

export default Converter;