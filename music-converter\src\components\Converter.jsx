import React, { useEffect, useState, useRef } from "react";
import { Helmet } from "react-helmet";
import VexTabBlock, { updateVexTabBlock } from "./VexTabBlock.jsx";
import { gigaConvert, parseNote, toMidi, fromMidi, formatNote } from "../ConvertFunctions.js";

function Converter() {

  // STYLES
  const styles = {
    graphicDisplay: {
      width: Math.min(window.innerWidth * 0.8, 900),
      border: "2px solid #9a62e3",
      borderRadius: "2rem",
      background: "rgba(68, 25, 240, 0.08)",
      display: "grid",
      gridTemplateRows: "auto auto 1fr auto auto",
    },
    musicDisplay: {
      width: Math.min(window.innerWidth * 0.8, 900),
      border: "2px solid #9a62e3",
      borderRadius: "2rem",
      background: "white",
    },
    sideButton: {
      backgroundColor: "white",
      width: "4rem",
      height: "4rem",
      fontSize: "1.2rem",
      fontWeight: "bold",
      color: "#5c14ba",
      border: "2px solid #9a62e3",
      borderRadius: "1rem",
      cursor: "pointer",
      margin: "auto",
      transition: 'all 0.2s ease',
    },
    sideButtonActive: {
      backgroundColor: "#9a62e3",
      color: "white",
      border: "2px solid #7f3bd9",
      boxShadow: "0 4px 8px rgba(127, 59, 217, 0.5)",
      transition: 'all 0.2s ease'
    },
  };

  // MUSIC STATES
  const defaultSetup = "tabstave notation=true tablature=false";

  
  const [curStep, setCurStep]         =  useState('A'); 
  const [curAccident, setCurAccident] =  useState('');  
  const [curOctave, setCurOctave]     =  useState(4);   
  const [curAccent, setCurAccent]     =  useState('');  
  const [curSlur, setCurSlur]         =  useState(' '); 
  const curNote = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`


  const [key, setKey] = useState("G");
  const [time, setTime] = useState("4/4");
  const [notes, setNotes] = useState(['A/4 ']);

  const rawVex = [
    defaultSetup + ` key=${key} time=${time}`,
    "notes " + notes.join(" "),
  ].join("\n");

  const [converted, setConverted] = useState("");

  // GRAPHIC INPUT CONSTANTS
  const NOTES = ["A", "B", "C", "D", "E", "F", "G"];
  const ACCIDENTALS = ["♯", "♮", "♭"];
  const VEX_ACC = { "♯": "#", "♮": "n", "♭": "@", "": "" };

  return (
    <main
      style={{
        display: "flex",
        flexDirection: "column",
        paddingTop: "4rem",
        paddingBottom: "4rem",
      }}
    >
      <Helmet>
        <script src="https://unpkg.com/vexflow/releases/vexflow-min.js"></script>
        <script src="https://unpkg.com/vextab/releases/vextab-core.prod.js"></script>
        <script src="https://unpkg.com/vextab/releases/div.prod.js"></script>
      </Helmet>

      {/* ----- INPUT AREA ----- */}
      <div>
        <section
          style={{
            border: "2px solid #9a62e3",
            borderRadius: "2rem",
            background: "rgba(120, 25, 240, 0.06)",
            display: "flex",
            flexDirection: "column",
            margin: "2rem 4rem",
            padding: "2rem",
            gap: "2rem",
          }}
        >
          <h3>Input</h3>

          {/* ----- Start of Graphical Input Compartment ----- */}
          <div style={styles.graphicDisplay}>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "6rem 1fr 6rem",
              }}
            >
              <div
                style={{
                  border: '1px solid red',
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: 'flex-end',
                  padding: "1rem 0rem",
                }}
              >
                <button
                  style={styles.sideButton}
                  onClick={() => {
                    setCurOctave((o) => o + 1)
                    const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');
                    curVexText.value = defaultSetup + ` key=${key} time=${time} \nnotes ` + `${curStep}${curAccident}/${curOctave+1}${curAccent}${curSlur}`;
                    updateVexTabBlock('currentNoteRef');
                  }}
                >
                  ↑
                </button>
                <button style={styles.sideButton}>{curOctave}</button>
                <button
                  style={styles.sideButton}
                  onClick={() => {
                    setCurOctave((o) => o - 1)
                    const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');
                    curVexText.value = defaultSetup + ` key=${key} time=${time} \nnotes ` + `${curStep}${curAccident}/${curOctave-1}${curAccent}${curSlur}`;
                    updateVexTabBlock('currentNoteRef');
                  }}
                >
                  ↓
                </button>
              </div>

              <div
                style={{
                  display: "grid",
                  gridTemplateRows: "auto 1fr",
                }}
              >
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(7, 1fr)",
                    padding: "1rem",
                  }}
                >
                  {NOTES.map((note) => (
                    <button
                      style={{
                        ...styles.sideButton,
                        ...(curStep === note ? styles.sideButtonActive : {}),
                      }}
                      onClick={() => {
                        setCurStep(note);
                        const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');
                        curVexText.value = defaultSetup + ` key=${key} time=${time} \nnotes ` + `${note}${curAccident}/${curOctave}${curAccent}${curSlur}`;
                        updateVexTabBlock('currentNoteRef');
                      }}
                    >
                      {note}
                    </button>
                  ))}
                </div>

                <div style={{ padding: "1rem" }}>
                  <VexTabBlock source={[` key=${key} time=${time}`,
    "notes " + curNote].join('\n')} id={"currentNoteRef"} editorHeight={1}/>
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignContent: "center",
                  gap: "0.5rem",
                  padding: "1rem 0rem",
                }}
              >
                {ACCIDENTALS.map((acc) => (
                  <button
                    style={{
                      ...styles.sideButton,
                      ...(curAccident === VEX_ACC[acc] ? styles.sideButtonActive : {}),
                    }}
                    onClick={() => {
                      const accident = curAccident === VEX_ACC[acc] ? '' : VEX_ACC[acc];
                      setCurAccident(accident);
                      const curVexText = document.getElementById('currentNoteRef').querySelector('textarea.editor');
                      curVexText.value = defaultSetup + ` key=${key} time=${time} \nnotes ` + `${curStep}${accident}/${curOctave}${curAccent}${curSlur}`;
                      updateVexTabBlock('currentNoteRef');
                    }}
                  >
                    {acc}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3>Duration and Expression</h3>
            </div>

            {/* ----- ADD & REMOVE NOTES ----- */}
            <div>
              {/* --- Add --- */}
              <button
                onClick={() => {
                  const note = `${curStep}${curAccident}/${curOctave}${curAccent}${curSlur}`;
                  setNotes((prev) => [...prev, note]);
                  const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');
                  curVexText.value += note;
                  updateVexTabBlock('inputEditorRef');
                  console.log("Adding to notes: ", notes);
                }}
              >
                Add Note
              </button>
              {/* --- Remove --- */}
              <button
                onClick={() => {
                  console.log("Notes before Removal: ", notes);
                  setNotes((prev) => prev.slice(0, -1));
                  const curVexText = document.getElementById('inputEditorRef').querySelector('textarea.editor');
                  curVexText.value = `${defaultSetup} \nnotes ${notes.slice(0, -1).join(" ")}`;
                  updateVexTabBlock('inputEditorRef');
                  console.log("Subtracting from notes: ", notes);
                }}
              >
                Remove Note
              </button>
            </div>
          </div>
          {/* ----- End of Graphical Input Compartment ----- */}

          <button
            onClick={() => {
              setConverted(gigaConvert(key, notes))
            }}
          >
            Convert
          </button>

          {/* ----- Start of VexTab Input Compartment ----- */}
          <div style={styles.musicDisplay}>
            <h4>VexTab Input Editor</h4>
            <VexTabBlock source={rawVex} id={"inputEditorRef"} editorHeight={100} />
          </div>
          {/* ----- End of VexTab Input Compartment ----- */}
        </section>
      </div>

      {/* ----- OUTPUT AREA ----- */}
      <div>
        <section
          style={{
            border: "2px solid #9a62e3",
            borderRadius: "2rem",
            background: "rgba(120, 25, 240, 0.06)",
            display: "flex",
            flexDirection: "column",
            margin: "2rem 4rem",
            padding: "2rem",
            gap: "2rem",
            alignItems: "center",
          }}
        >
          <h3>Output</h3>
          <div style={styles.musicDisplay}>
            <h4>VexTab Output Editor</h4>
            <VexTabBlock source={converted} id={"outputRef"} editorHeight={100}/>
          </div>
        </section>
      </div>
    </main>
  );
}

export default Converter;
