
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';

function Converter() {

  // STYLES
  const styles = {
    graphicDisplay: {
      width: Math.min(window.innerWidth * 0.8, 900),
      border: '2px solid #9a62e3',
      borderRadius: '2rem',
      background: 'rgba(68, 25, 240, 0.08)',
      display: 'grid',
      gridTemplateRows: '1fr auto auto',
    },
    musicDisplay: {
      width: Math.min(window.innerWidth * 0.8, 900),
      border: '2px solid #9a62e3',
      borderRadius: '2rem',
      background: 'white',
    },
    sideButton: {
      backgroundColor: 'white',
      width: '4rem',
      height: '4rem',
      border: '2px solid #9a62e3',
      borderRadius: '1rem',
      cursor: 'pointer',
      
    },
    sideButtonActive: {
      backgroundColor: '#9a62e3',
      color: 'white',
      border: '2px solid #7f3bd9',
      boxShadow: '0 2px 4px ',
    },
  }

  // VEXTAB SETUP

  const defaultSetup = 'tabstave notation=true tablature=false';
  const defaultNotes = ['G/4'];
  const [key, setKey] = useState('G');
  const [time, setTime] = useState('4/4');
  const [notes, setNotes] = useState(defaultNotes);
  const [rawVex, setRawVex] = useState(
`${defaultSetup} key=${key} time=${time}
notes ${notes.join(' ')}`
  );


  // GRAPHIC INPUT CONSTANTS
  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
  const ACCIDENTALS = ['♯', '♮', '♭'];

  // CURRENT NOTE
  const [curOctave, setCurOctave] = useState(4);
  const [curStep, setCurStep] = useState('A');
  const [curAccident, setCurAccident] = useState('♮'); 
  const [curNote, setCurNote] = useState();

  // Effect to process VexTab divs after component mounts
  useEffect(() => {
    // Wait for the script to load and then process VexTab divs
    const timer = setTimeout(() => {
      if (window.VexTab) {
        window.VexTab.Artist.render();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [rawVex]);

  return (
    <main>
      <Helmet>
        <script src="https://unpkg.com/vextab/releases/div.prod.js"></script>
      </Helmet>
      {/* ----- INPUT AREA ----- */}
      <div>
        <section style={{
          border: '2px solid #9a62e3',
          borderRadius: '2rem',
          background: 'rgba(120, 25, 240, 0.06)',
          display: 'flex',
          flexDirection: 'column',
          margin: '2rem 4rem',
          padding: '2rem',
          gap: '2rem',
        }}>
          <h3>Input</h3>

          {/* ----- Start of Graphical Input Compartment ----- */}
          <div style={styles.graphicDisplay}>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '6rem 1fr 6rem',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignContent: 'center',
                  gap: '0.5rem',
                  padding: '1rem 0rem',
                }}>
                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave + 1)}>↑</button>
                <button style={styles.sideButton}>{curOctave}</button>
                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave - 1)}>↓</button>
              </div>

              <div
                style={{
                  display: 'grid',
                  gridTemplateRows: 'auto 1fr',
                }}
              >
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(7, 1fr)',
                    padding: '1rem',
                  }}>
                  {NOTES.map(note => (
                    <button style={{...styles.sideButton, ...(curStep === note ? styles.sideButtonActive : {})}}
                    onClick={() => setCurStep(note)}>
                      {note}
                    </button>

                  ))}
                </div>

                <div style={{border: '2px solid red'}}>
                  <div className='vextab-auto'>
                    {`tabstave notation=true tablature=false key=${key} time=${time}
                    notes ${curStep}${curAccident}/${curOctave}`}
                  </div>
                </div>
                
              </div>

              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignContent: 'center',
                  gap: '0.5rem',
                  padding: '1rem 0rem',
                }}>
                {ACCIDENTALS.map(acc => (
                  <button style={{...styles.sideButton, ...(curAccident === acc ? styles.sideButtonActive : {})}} onClick={(curAccident === acc) ? () => setCurAccident('') : () => setCurAccident(acc)}
                  >
                    {acc}
                  </button>
                ))}
              </div>

            </div>

            <div>
              <h3>Duration and Expression</h3>
            </div>

            <div>
              <h3>Add or remove</h3>
            </div>

          </div>
          {/* ----- End of Graphical Input Compartment ----- */}

          {/* ----- Start of VexTab Input Compartment ----- */}
          <div style={styles.musicDisplay}>
            <div className="vextab-auto" width="800" scale="1.0">
              {rawVex}
            </div>
            <textarea 
              value={rawVex}
              style={{width: Math.min(window.innerWidth * 0.7, 600), margin: '1rem'}}
              onChange={(e) => setRawVex(e.target.value)}
            >
              {rawVex}
            </textarea>
          </div>
          {/* ----- End of VexTab Input Compartment ----- */}
        </section>
      </div>

      {/* ----- OUTPUT AREA ----- */}
      <div>
        <section>
          <h3>Output</h3>
          <div className="vextab-auto" width="800" scale="1.0">
            {rawVex}
          </div>
        </section>
      </div>

    </main>
  )
}

export default Converter;