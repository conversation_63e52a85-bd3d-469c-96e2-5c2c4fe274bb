{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { StemmableNote } from './stemmablenote.js';\nimport { isDot } from './typeguard.js';\nimport { defined, RuntimeError } from './util.js';\nfunction getUnusedStringGroups(numLines, stringsUsed) {\n  const stemThrough = [];\n  let group = [];\n  for (let string = 1; string <= numLines; string++) {\n    const isUsed = stringsUsed.indexOf(string) > -1;\n    if (!isUsed) {\n      group.push(string);\n    } else {\n      stemThrough.push(group);\n      group = [];\n    }\n  }\n  if (group.length > 0) stemThrough.push(group);\n  return stemThrough;\n}\nfunction getPartialStemLines(stemY, unusedStrings, stave, stemDirection) {\n  const upStem = stemDirection !== 1;\n  const downStem = stemDirection !== -1;\n  const lineSpacing = stave.getSpacingBetweenLines();\n  const totalLines = stave.getNumLines();\n  const stemLines = [];\n  unusedStrings.forEach(strings => {\n    const containsLastString = strings.indexOf(totalLines) > -1;\n    const containsFirstString = strings.indexOf(1) > -1;\n    if (upStem && containsFirstString || downStem && containsLastString) {\n      return;\n    }\n    if (strings.length === 1) {\n      strings.push(strings[0]);\n    }\n    const lineYs = [];\n    strings.forEach((string, index, strings) => {\n      const isTopBound = string === 1;\n      const isBottomBound = string === totalLines;\n      let y = stave.getYForLine(string - 1);\n      if (index === 0 && !isTopBound) {\n        y -= lineSpacing / 2 - 1;\n      } else if (index === strings.length - 1 && !isBottomBound) {\n        y += lineSpacing / 2 - 1;\n      }\n      lineYs.push(y);\n      if (stemDirection === 1 && isTopBound) {\n        lineYs.push(stemY - 2);\n      } else if (stemDirection === -1 && isBottomBound) {\n        lineYs.push(stemY + 2);\n      }\n    });\n    stemLines.push(lineYs.sort((a, b) => a - b));\n  });\n  return stemLines;\n}\nexport class TabNote extends StemmableNote {\n  static get CATEGORY() {\n    return \"TabNote\";\n  }\n  constructor(noteStruct, drawStem = false) {\n    super(noteStruct);\n    this.fretElement = [];\n    this.greatestString = () => {\n      return this.positions.map(x => x.str).reduce((a, b) => a > b ? a : b);\n    };\n    this.leastString = () => {\n      return this.positions.map(x => x.str).reduce((a, b) => a < b ? a : b);\n    };\n    this.ghost = false;\n    this.positions = noteStruct.positions || [];\n    this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), {\n      drawStem,\n      drawDots: drawStem,\n      drawStemThroughStave: false,\n      yShift: 0\n    });\n    this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n    defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n    this.buildStem();\n    if (noteStruct.stemDirection) {\n      this.setStemDirection(noteStruct.stemDirection);\n    } else {\n      this.setStemDirection(Stem.UP);\n    }\n    this.ghost = false;\n    this.updateWidth();\n  }\n  reset() {\n    super.reset();\n    if (this.stave) this.setStave(this.stave);\n    return this;\n  }\n  setGhost(ghost) {\n    this.ghost = ghost;\n    this.updateWidth();\n    return this;\n  }\n  hasStem() {\n    if (this.renderOptions.drawStem) return true;\n    return false;\n  }\n  getStemExtension() {\n    if (this.stemExtensionOverride !== undefined) {\n      return this.stemExtensionOverride;\n    }\n    return this.flag.getHeight() > Stem.HEIGHT ? this.flag.getHeight() - Stem.HEIGHT : 0;\n  }\n  static tabToElement(fret) {\n    let el;\n    if (fret.toUpperCase() === 'X') {\n      el = new Element('TabNote');\n      el.setText(Glyphs.accidentalDoubleSharp);\n    } else {\n      el = new Element('TabNote.text');\n      el.setText(fret);\n      el.setYShift(el.getHeight() / 2);\n    }\n    return el;\n  }\n  updateWidth() {\n    this.fretElement = [];\n    this.width = 0;\n    for (let i = 0; i < this.positions.length; ++i) {\n      let fret = this.positions[i].fret;\n      if (this.ghost) fret = '(' + fret + ')';\n      const el = TabNote.tabToElement(fret.toString());\n      this.fretElement.push(el);\n      this.width = Math.max(el.getWidth(), this.width);\n    }\n  }\n  setStave(stave) {\n    super.setStave(stave);\n    const ctx = stave.getContext();\n    this.setContext(ctx);\n    const ys = this.positions.map(({\n      str: line\n    }) => stave.getYForLine(Number(line) - 1));\n    this.setYs(ys);\n    if (this.stem) {\n      this.stem.setYBounds(this.getStemY(), this.getStemY());\n    }\n    return this;\n  }\n  getPositions() {\n    return this.positions;\n  }\n  getModifierStartXY(position, index) {\n    if (!this.preFormatted) {\n      throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n    }\n    if (this.ys.length === 0) {\n      throw new RuntimeError('NoYValues', 'No Y-Values calculated for this note.');\n    }\n    let x = 0;\n    if (position === Modifier.Position.LEFT) {\n      x = -1 * 2;\n    } else if (position === Modifier.Position.RIGHT) {\n      x = this.width + 2;\n    } else if (position === Modifier.Position.BELOW || position === Modifier.Position.ABOVE) {\n      const noteGlyphWidth = this.width;\n      x = noteGlyphWidth / 2;\n    }\n    return {\n      x: this.getAbsoluteX() + x,\n      y: this.ys[index]\n    };\n  }\n  getLineForRest() {\n    return Number(this.positions[0].str);\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    if (this.modifierContext) this.modifierContext.preFormat();\n    this.preFormatted = true;\n  }\n  getStemX() {\n    return this.getCenterGlyphX();\n  }\n  getStemY() {\n    const numLines = this.checkStave().getNumLines();\n    const stemUpLine = -0.5;\n    const stemDownLine = numLines - 0.5;\n    const stemStartLine = Stem.UP === this.stemDirection ? stemUpLine : stemDownLine;\n    return this.checkStave().getYForLine(stemStartLine);\n  }\n  getStemExtents() {\n    return this.checkStem().getExtents();\n  }\n  drawFlag() {\n    const {\n      beam,\n      glyphProps,\n      renderOptions: {\n        drawStem\n      }\n    } = this;\n    const context = this.checkContext();\n    const shouldDrawFlag = beam === undefined && drawStem;\n    if (glyphProps.codeFlagUp && shouldDrawFlag) {\n      const flagX = this.getStemX();\n      const flagY = this.getStemDirection() === Stem.DOWN ? this.getStemY() - this.checkStem().getHeight() - this.getStemExtension() : this.getStemY() - this.checkStem().getHeight() + this.getStemExtension();\n      this.flag.setContext(context).setX(flagX).setY(flagY).drawWithStyle();\n    }\n  }\n  drawModifiers() {\n    this.modifiers.forEach(modifier => {\n      if (isDot(modifier) && !this.renderOptions.drawDots) {\n        return;\n      }\n      modifier.setContext(this.getContext());\n      modifier.drawWithStyle();\n    });\n  }\n  drawStemThrough() {\n    const stemX = this.getStemX();\n    const stemY = this.getStemY();\n    const ctx = this.checkContext();\n    const drawStem = this.renderOptions.drawStem;\n    const stemThrough = this.renderOptions.drawStemThroughStave;\n    if (drawStem && stemThrough) {\n      const numLines = this.checkStave().getNumLines();\n      const stringsUsed = this.positions.map(position => Number(position.str));\n      const unusedStrings = getUnusedStringGroups(numLines, stringsUsed);\n      const stemLines = getPartialStemLines(stemY, unusedStrings, this.checkStave(), this.getStemDirection());\n      ctx.setLineWidth(Stem.WIDTH);\n      stemLines.forEach(bounds => {\n        if (bounds.length === 0) return;\n        ctx.beginPath();\n        ctx.moveTo(stemX, bounds[0]);\n        ctx.lineTo(stemX, bounds[bounds.length - 1]);\n        ctx.stroke();\n        ctx.closePath();\n      });\n    }\n  }\n  drawPositions() {\n    const ctx = this.checkContext();\n    const x = this.getAbsoluteX();\n    const ys = this.ys;\n    for (let i = 0; i < this.positions.length; ++i) {\n      const y = ys[i] + this.renderOptions.yShift;\n      const el = this.fretElement[i];\n      const tabX = x - el.getWidth() / 2;\n      ctx.clearRect(tabX - 2, y - 3, el.getWidth() + 4, 6);\n      el.renderText(ctx, tabX, y);\n    }\n  }\n  draw() {\n    const ctx = this.checkContext();\n    if (this.ys.length === 0) {\n      throw new RuntimeError('NoYValues', \"Can't draw note without Y values.\");\n    }\n    this.setRendered();\n    const renderStem = this.beam === undefined && this.renderOptions.drawStem;\n    ctx.openGroup('tabnote', this.getAttribute('id'));\n    this.drawPositions();\n    this.drawStemThrough();\n    if (this.stem && renderStem) {\n      const stemX = this.getStemX();\n      this.stem.setNoteHeadXBounds(stemX, stemX);\n      this.stem.setContext(ctx).drawWithStyle();\n    }\n    this.drawFlag();\n    this.drawModifiers();\n    ctx.closeGroup();\n  }\n}", "map": {"version": 3, "names": ["Element", "Glyphs", "Modifier", "Note", "<PERSON><PERSON>", "StemmableNote", "isDot", "defined", "RuntimeError", "getUnusedStringGroups", "numLines", "stringsUsed", "stemThrough", "group", "string", "isUsed", "indexOf", "push", "length", "getPartialStemLines", "stemY", "unusedStrings", "stave", "stemDirection", "upStem", "downStem", "lineSpacing", "getSpacingBetweenLines", "totalLines", "getNumLines", "stemLines", "for<PERSON>ach", "strings", "containsLastString", "containsFirstString", "lineYs", "index", "isTopBound", "isBottomBound", "y", "getYForLine", "sort", "a", "b", "TabNote", "CATEGORY", "constructor", "noteStruct", "drawStem", "fretElement", "greatestString", "positions", "map", "x", "str", "reduce", "leastString", "ghost", "renderOptions", "Object", "assign", "drawDots", "drawStemThroughStave", "yShift", "glyphProps", "getGlyphProps", "duration", "noteType", "buildStem", "setStemDirection", "UP", "updateWidth", "reset", "setStave", "setGhost", "hasStem", "getStemExtension", "stemExtensionOverride", "undefined", "flag", "getHeight", "HEIGHT", "tabToElement", "fret", "el", "toUpperCase", "setText", "accidentalDoubleSharp", "setYShift", "width", "i", "toString", "Math", "max", "getWidth", "ctx", "getContext", "setContext", "ys", "line", "Number", "setYs", "stem", "setYBounds", "getStemY", "getPositions", "getModifierStartXY", "position", "preFormatted", "Position", "LEFT", "RIGHT", "BELOW", "ABOVE", "noteGlyphWidth", "getAbsoluteX", "getLineForRest", "preFormat", "modifierContext", "getStemX", "getCenterGlyphX", "checkStave", "stemUpLine", "stemDownLine", "stemStartLine", "getStemExtents", "checkStem", "getExtents", "drawFlag", "beam", "context", "checkContext", "shouldDrawFlag", "codeFlagUp", "flagX", "flagY", "getStemDirection", "DOWN", "setX", "setY", "drawWithStyle", "drawModifiers", "modifiers", "modifier", "drawStemThrough", "stemX", "setLineWidth", "WIDTH", "bounds", "beginPath", "moveTo", "lineTo", "stroke", "closePath", "drawPositions", "tabX", "clearRect", "renderText", "draw", "setRendered", "renderStem", "openGroup", "getAttribute", "setNoteHeadXBounds", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tabnote.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Modifier } from './modifier.js';\nimport { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { StemmableNote } from './stemmablenote.js';\nimport { isDot } from './typeguard.js';\nimport { defined, RuntimeError } from './util.js';\nfunction getUnusedStringGroups(numLines, stringsUsed) {\n    const stemThrough = [];\n    let group = [];\n    for (let string = 1; string <= numLines; string++) {\n        const isUsed = stringsUsed.indexOf(string) > -1;\n        if (!isUsed) {\n            group.push(string);\n        }\n        else {\n            stemThrough.push(group);\n            group = [];\n        }\n    }\n    if (group.length > 0)\n        stemThrough.push(group);\n    return stemThrough;\n}\nfunction getPartialStemLines(stemY, unusedStrings, stave, stemDirection) {\n    const upStem = stemDirection !== 1;\n    const downStem = stemDirection !== -1;\n    const lineSpacing = stave.getSpacingBetweenLines();\n    const totalLines = stave.getNumLines();\n    const stemLines = [];\n    unusedStrings.forEach((strings) => {\n        const containsLastString = strings.indexOf(totalLines) > -1;\n        const containsFirstString = strings.indexOf(1) > -1;\n        if ((upStem && containsFirstString) || (downStem && containsLastString)) {\n            return;\n        }\n        if (strings.length === 1) {\n            strings.push(strings[0]);\n        }\n        const lineYs = [];\n        strings.forEach((string, index, strings) => {\n            const isTopBound = string === 1;\n            const isBottomBound = string === totalLines;\n            let y = stave.getYForLine(string - 1);\n            if (index === 0 && !isTopBound) {\n                y -= lineSpacing / 2 - 1;\n            }\n            else if (index === strings.length - 1 && !isBottomBound) {\n                y += lineSpacing / 2 - 1;\n            }\n            lineYs.push(y);\n            if (stemDirection === 1 && isTopBound) {\n                lineYs.push(stemY - 2);\n            }\n            else if (stemDirection === -1 && isBottomBound) {\n                lineYs.push(stemY + 2);\n            }\n        });\n        stemLines.push(lineYs.sort((a, b) => a - b));\n    });\n    return stemLines;\n}\nexport class TabNote extends StemmableNote {\n    static get CATEGORY() {\n        return \"TabNote\";\n    }\n    constructor(noteStruct, drawStem = false) {\n        super(noteStruct);\n        this.fretElement = [];\n        this.greatestString = () => {\n            return this.positions.map((x) => x.str).reduce((a, b) => (a > b ? a : b));\n        };\n        this.leastString = () => {\n            return this.positions.map((x) => x.str).reduce((a, b) => (a < b ? a : b));\n        };\n        this.ghost = false;\n        this.positions = noteStruct.positions || [];\n        this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), { drawStem, drawDots: drawStem, drawStemThroughStave: false, yShift: 0 });\n        this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n        defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n        this.buildStem();\n        if (noteStruct.stemDirection) {\n            this.setStemDirection(noteStruct.stemDirection);\n        }\n        else {\n            this.setStemDirection(Stem.UP);\n        }\n        this.ghost = false;\n        this.updateWidth();\n    }\n    reset() {\n        super.reset();\n        if (this.stave)\n            this.setStave(this.stave);\n        return this;\n    }\n    setGhost(ghost) {\n        this.ghost = ghost;\n        this.updateWidth();\n        return this;\n    }\n    hasStem() {\n        if (this.renderOptions.drawStem)\n            return true;\n        return false;\n    }\n    getStemExtension() {\n        if (this.stemExtensionOverride !== undefined) {\n            return this.stemExtensionOverride;\n        }\n        return this.flag.getHeight() > Stem.HEIGHT ? this.flag.getHeight() - Stem.HEIGHT : 0;\n    }\n    static tabToElement(fret) {\n        let el;\n        if (fret.toUpperCase() === 'X') {\n            el = new Element('TabNote');\n            el.setText(Glyphs.accidentalDoubleSharp);\n        }\n        else {\n            el = new Element('TabNote.text');\n            el.setText(fret);\n            el.setYShift(el.getHeight() / 2);\n        }\n        return el;\n    }\n    updateWidth() {\n        this.fretElement = [];\n        this.width = 0;\n        for (let i = 0; i < this.positions.length; ++i) {\n            let fret = this.positions[i].fret;\n            if (this.ghost)\n                fret = '(' + fret + ')';\n            const el = TabNote.tabToElement(fret.toString());\n            this.fretElement.push(el);\n            this.width = Math.max(el.getWidth(), this.width);\n        }\n    }\n    setStave(stave) {\n        super.setStave(stave);\n        const ctx = stave.getContext();\n        this.setContext(ctx);\n        const ys = this.positions.map(({ str: line }) => stave.getYForLine(Number(line) - 1));\n        this.setYs(ys);\n        if (this.stem) {\n            this.stem.setYBounds(this.getStemY(), this.getStemY());\n        }\n        return this;\n    }\n    getPositions() {\n        return this.positions;\n    }\n    getModifierStartXY(position, index) {\n        if (!this.preFormatted) {\n            throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n        }\n        if (this.ys.length === 0) {\n            throw new RuntimeError('NoYValues', 'No Y-Values calculated for this note.');\n        }\n        let x = 0;\n        if (position === Modifier.Position.LEFT) {\n            x = -1 * 2;\n        }\n        else if (position === Modifier.Position.RIGHT) {\n            x = this.width + 2;\n        }\n        else if (position === Modifier.Position.BELOW || position === Modifier.Position.ABOVE) {\n            const noteGlyphWidth = this.width;\n            x = noteGlyphWidth / 2;\n        }\n        return {\n            x: this.getAbsoluteX() + x,\n            y: this.ys[index],\n        };\n    }\n    getLineForRest() {\n        return Number(this.positions[0].str);\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        if (this.modifierContext)\n            this.modifierContext.preFormat();\n        this.preFormatted = true;\n    }\n    getStemX() {\n        return this.getCenterGlyphX();\n    }\n    getStemY() {\n        const numLines = this.checkStave().getNumLines();\n        const stemUpLine = -0.5;\n        const stemDownLine = numLines - 0.5;\n        const stemStartLine = Stem.UP === this.stemDirection ? stemUpLine : stemDownLine;\n        return this.checkStave().getYForLine(stemStartLine);\n    }\n    getStemExtents() {\n        return this.checkStem().getExtents();\n    }\n    drawFlag() {\n        const { beam, glyphProps, renderOptions: { drawStem }, } = this;\n        const context = this.checkContext();\n        const shouldDrawFlag = beam === undefined && drawStem;\n        if (glyphProps.codeFlagUp && shouldDrawFlag) {\n            const flagX = this.getStemX();\n            const flagY = this.getStemDirection() === Stem.DOWN\n                ?\n                    this.getStemY() - this.checkStem().getHeight() - this.getStemExtension()\n                :\n                    this.getStemY() - this.checkStem().getHeight() + this.getStemExtension();\n            this.flag.setContext(context).setX(flagX).setY(flagY).drawWithStyle();\n        }\n    }\n    drawModifiers() {\n        this.modifiers.forEach((modifier) => {\n            if (isDot(modifier) && !this.renderOptions.drawDots) {\n                return;\n            }\n            modifier.setContext(this.getContext());\n            modifier.drawWithStyle();\n        });\n    }\n    drawStemThrough() {\n        const stemX = this.getStemX();\n        const stemY = this.getStemY();\n        const ctx = this.checkContext();\n        const drawStem = this.renderOptions.drawStem;\n        const stemThrough = this.renderOptions.drawStemThroughStave;\n        if (drawStem && stemThrough) {\n            const numLines = this.checkStave().getNumLines();\n            const stringsUsed = this.positions.map((position) => Number(position.str));\n            const unusedStrings = getUnusedStringGroups(numLines, stringsUsed);\n            const stemLines = getPartialStemLines(stemY, unusedStrings, this.checkStave(), this.getStemDirection());\n            ctx.setLineWidth(Stem.WIDTH);\n            stemLines.forEach((bounds) => {\n                if (bounds.length === 0)\n                    return;\n                ctx.beginPath();\n                ctx.moveTo(stemX, bounds[0]);\n                ctx.lineTo(stemX, bounds[bounds.length - 1]);\n                ctx.stroke();\n                ctx.closePath();\n            });\n        }\n    }\n    drawPositions() {\n        const ctx = this.checkContext();\n        const x = this.getAbsoluteX();\n        const ys = this.ys;\n        for (let i = 0; i < this.positions.length; ++i) {\n            const y = ys[i] + this.renderOptions.yShift;\n            const el = this.fretElement[i];\n            const tabX = x - el.getWidth() / 2;\n            ctx.clearRect(tabX - 2, y - 3, el.getWidth() + 4, 6);\n            el.renderText(ctx, tabX, y);\n        }\n    }\n    draw() {\n        const ctx = this.checkContext();\n        if (this.ys.length === 0) {\n            throw new RuntimeError('NoYValues', \"Can't draw note without Y values.\");\n        }\n        this.setRendered();\n        const renderStem = this.beam === undefined && this.renderOptions.drawStem;\n        ctx.openGroup('tabnote', this.getAttribute('id'));\n        this.drawPositions();\n        this.drawStemThrough();\n        if (this.stem && renderStem) {\n            const stemX = this.getStemX();\n            this.stem.setNoteHeadXBounds(stemX, stemX);\n            this.stem.setContext(ctx).drawWithStyle();\n        }\n        this.drawFlag();\n        this.drawModifiers();\n        ctx.closeGroup();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AACjD,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAClD,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAIC,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAIJ,QAAQ,EAAEI,MAAM,EAAE,EAAE;IAC/C,MAAMC,MAAM,GAAGJ,WAAW,CAACK,OAAO,CAACF,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/C,IAAI,CAACC,MAAM,EAAE;MACTF,KAAK,CAACI,IAAI,CAACH,MAAM,CAAC;IACtB,CAAC,MACI;MACDF,WAAW,CAACK,IAAI,CAACJ,KAAK,CAAC;MACvBA,KAAK,GAAG,EAAE;IACd;EACJ;EACA,IAAIA,KAAK,CAACK,MAAM,GAAG,CAAC,EAChBN,WAAW,CAACK,IAAI,CAACJ,KAAK,CAAC;EAC3B,OAAOD,WAAW;AACtB;AACA,SAASO,mBAAmBA,CAACC,KAAK,EAAEC,aAAa,EAAEC,KAAK,EAAEC,aAAa,EAAE;EACrE,MAAMC,MAAM,GAAGD,aAAa,KAAK,CAAC;EAClC,MAAME,QAAQ,GAAGF,aAAa,KAAK,CAAC,CAAC;EACrC,MAAMG,WAAW,GAAGJ,KAAK,CAACK,sBAAsB,CAAC,CAAC;EAClD,MAAMC,UAAU,GAAGN,KAAK,CAACO,WAAW,CAAC,CAAC;EACtC,MAAMC,SAAS,GAAG,EAAE;EACpBT,aAAa,CAACU,OAAO,CAAEC,OAAO,IAAK;IAC/B,MAAMC,kBAAkB,GAAGD,OAAO,CAAChB,OAAO,CAACY,UAAU,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAMM,mBAAmB,GAAGF,OAAO,CAAChB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACnD,IAAKQ,MAAM,IAAIU,mBAAmB,IAAMT,QAAQ,IAAIQ,kBAAmB,EAAE;MACrE;IACJ;IACA,IAAID,OAAO,CAACd,MAAM,KAAK,CAAC,EAAE;MACtBc,OAAO,CAACf,IAAI,CAACe,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA,MAAMG,MAAM,GAAG,EAAE;IACjBH,OAAO,CAACD,OAAO,CAAC,CAACjB,MAAM,EAAEsB,KAAK,EAAEJ,OAAO,KAAK;MACxC,MAAMK,UAAU,GAAGvB,MAAM,KAAK,CAAC;MAC/B,MAAMwB,aAAa,GAAGxB,MAAM,KAAKc,UAAU;MAC3C,IAAIW,CAAC,GAAGjB,KAAK,CAACkB,WAAW,CAAC1B,MAAM,GAAG,CAAC,CAAC;MACrC,IAAIsB,KAAK,KAAK,CAAC,IAAI,CAACC,UAAU,EAAE;QAC5BE,CAAC,IAAIb,WAAW,GAAG,CAAC,GAAG,CAAC;MAC5B,CAAC,MACI,IAAIU,KAAK,KAAKJ,OAAO,CAACd,MAAM,GAAG,CAAC,IAAI,CAACoB,aAAa,EAAE;QACrDC,CAAC,IAAIb,WAAW,GAAG,CAAC,GAAG,CAAC;MAC5B;MACAS,MAAM,CAAClB,IAAI,CAACsB,CAAC,CAAC;MACd,IAAIhB,aAAa,KAAK,CAAC,IAAIc,UAAU,EAAE;QACnCF,MAAM,CAAClB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;MAC1B,CAAC,MACI,IAAIG,aAAa,KAAK,CAAC,CAAC,IAAIe,aAAa,EAAE;QAC5CH,MAAM,CAAClB,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;IACFU,SAAS,CAACb,IAAI,CAACkB,MAAM,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EACF,OAAOb,SAAS;AACpB;AACA,OAAO,MAAMc,OAAO,SAASvC,aAAa,CAAC;EACvC,WAAWwC,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACAC,WAAWA,CAACC,UAAU,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACtC,KAAK,CAACD,UAAU,CAAC;IACjB,IAAI,CAACE,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,cAAc,GAAG,MAAM;MACxB,OAAO,IAAI,CAACC,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACb,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;IAC7E,CAAC;IACD,IAAI,CAACa,WAAW,GAAG,MAAM;MACrB,OAAO,IAAI,CAACL,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACb,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;IAC7E,CAAC;IACD,IAAI,CAACc,KAAK,GAAG,KAAK;IAClB,IAAI,CAACN,SAAS,GAAGJ,UAAU,CAACI,SAAS,IAAI,EAAE;IAC3C,IAAI,CAACO,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,aAAa,CAAC,EAAE;MAAEV,QAAQ;MAAEa,QAAQ,EAAEb,QAAQ;MAAEc,oBAAoB,EAAE,KAAK;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IACnJ,IAAI,CAACC,UAAU,GAAG7D,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC;IAClE5D,OAAO,CAAC,IAAI,CAACyD,UAAU,EAAE,cAAc,EAAE,gCAAgC,IAAI,CAACE,QAAQ,eAAe,IAAI,CAACC,QAAQ,GAAG,CAAC;IACtH,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAIrB,UAAU,CAACxB,aAAa,EAAE;MAC1B,IAAI,CAAC8C,gBAAgB,CAACtB,UAAU,CAACxB,aAAa,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC8C,gBAAgB,CAACjE,IAAI,CAACkE,EAAE,CAAC;IAClC;IACA,IAAI,CAACb,KAAK,GAAG,KAAK;IAClB,IAAI,CAACc,WAAW,CAAC,CAAC;EACtB;EACAC,KAAKA,CAAA,EAAG;IACJ,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,IAAI,IAAI,CAAClD,KAAK,EACV,IAAI,CAACmD,QAAQ,CAAC,IAAI,CAACnD,KAAK,CAAC;IAC7B,OAAO,IAAI;EACf;EACAoD,QAAQA,CAACjB,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACc,WAAW,CAAC,CAAC;IAClB,OAAO,IAAI;EACf;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjB,aAAa,CAACV,QAAQ,EAC3B,OAAO,IAAI;IACf,OAAO,KAAK;EAChB;EACA4B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,qBAAqB,KAAKC,SAAS,EAAE;MAC1C,OAAO,IAAI,CAACD,qBAAqB;IACrC;IACA,OAAO,IAAI,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,GAAG5E,IAAI,CAAC6E,MAAM,GAAG,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC,CAAC,GAAG5E,IAAI,CAAC6E,MAAM,GAAG,CAAC;EACxF;EACA,OAAOC,YAAYA,CAACC,IAAI,EAAE;IACtB,IAAIC,EAAE;IACN,IAAID,IAAI,CAACE,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5BD,EAAE,GAAG,IAAIpF,OAAO,CAAC,SAAS,CAAC;MAC3BoF,EAAE,CAACE,OAAO,CAACrF,MAAM,CAACsF,qBAAqB,CAAC;IAC5C,CAAC,MACI;MACDH,EAAE,GAAG,IAAIpF,OAAO,CAAC,cAAc,CAAC;MAChCoF,EAAE,CAACE,OAAO,CAACH,IAAI,CAAC;MAChBC,EAAE,CAACI,SAAS,CAACJ,EAAE,CAACJ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;IACpC;IACA,OAAOI,EAAE;EACb;EACAb,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACwC,KAAK,GAAG,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvC,SAAS,CAACjC,MAAM,EAAE,EAAEwE,CAAC,EAAE;MAC5C,IAAIP,IAAI,GAAG,IAAI,CAAChC,SAAS,CAACuC,CAAC,CAAC,CAACP,IAAI;MACjC,IAAI,IAAI,CAAC1B,KAAK,EACV0B,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG;MAC3B,MAAMC,EAAE,GAAGxC,OAAO,CAACsC,YAAY,CAACC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC;MAChD,IAAI,CAAC1C,WAAW,CAAChC,IAAI,CAACmE,EAAE,CAAC;MACzB,IAAI,CAACK,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACT,EAAE,CAACU,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACL,KAAK,CAAC;IACpD;EACJ;EACAhB,QAAQA,CAACnD,KAAK,EAAE;IACZ,KAAK,CAACmD,QAAQ,CAACnD,KAAK,CAAC;IACrB,MAAMyE,GAAG,GAAGzE,KAAK,CAAC0E,UAAU,CAAC,CAAC;IAC9B,IAAI,CAACC,UAAU,CAACF,GAAG,CAAC;IACpB,MAAMG,EAAE,GAAG,IAAI,CAAC/C,SAAS,CAACC,GAAG,CAAC,CAAC;MAAEE,GAAG,EAAE6C;IAAK,CAAC,KAAK7E,KAAK,CAACkB,WAAW,CAAC4D,MAAM,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACrF,IAAI,CAACE,KAAK,CAACH,EAAE,CAAC;IACd,IAAI,IAAI,CAACI,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACC,UAAU,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;IAC1D;IACA,OAAO,IAAI;EACf;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtD,SAAS;EACzB;EACAuD,kBAAkBA,CAACC,QAAQ,EAAEvE,KAAK,EAAE;IAChC,IAAI,CAAC,IAAI,CAACwE,YAAY,EAAE;MACpB,MAAM,IAAIpG,YAAY,CAAC,iBAAiB,EAAE,sDAAsD,CAAC;IACrG;IACA,IAAI,IAAI,CAAC0F,EAAE,CAAChF,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIV,YAAY,CAAC,WAAW,EAAE,uCAAuC,CAAC;IAChF;IACA,IAAI6C,CAAC,GAAG,CAAC;IACT,IAAIsD,QAAQ,KAAKzG,QAAQ,CAAC2G,QAAQ,CAACC,IAAI,EAAE;MACrCzD,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACd,CAAC,MACI,IAAIsD,QAAQ,KAAKzG,QAAQ,CAAC2G,QAAQ,CAACE,KAAK,EAAE;MAC3C1D,CAAC,GAAG,IAAI,CAACoC,KAAK,GAAG,CAAC;IACtB,CAAC,MACI,IAAIkB,QAAQ,KAAKzG,QAAQ,CAAC2G,QAAQ,CAACG,KAAK,IAAIL,QAAQ,KAAKzG,QAAQ,CAAC2G,QAAQ,CAACI,KAAK,EAAE;MACnF,MAAMC,cAAc,GAAG,IAAI,CAACzB,KAAK;MACjCpC,CAAC,GAAG6D,cAAc,GAAG,CAAC;IAC1B;IACA,OAAO;MACH7D,CAAC,EAAE,IAAI,CAAC8D,YAAY,CAAC,CAAC,GAAG9D,CAAC;MAC1Bd,CAAC,EAAE,IAAI,CAAC2D,EAAE,CAAC9D,KAAK;IACpB,CAAC;EACL;EACAgF,cAAcA,CAAA,EAAG;IACb,OAAOhB,MAAM,CAAC,IAAI,CAACjD,SAAS,CAAC,CAAC,CAAC,CAACG,GAAG,CAAC;EACxC;EACA+D,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,YAAY,EACjB;IACJ,IAAI,IAAI,CAACU,eAAe,EACpB,IAAI,CAACA,eAAe,CAACD,SAAS,CAAC,CAAC;IACpC,IAAI,CAACT,YAAY,GAAG,IAAI;EAC5B;EACAW,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;EACjC;EACAhB,QAAQA,CAAA,EAAG;IACP,MAAM9F,QAAQ,GAAG,IAAI,CAAC+G,UAAU,CAAC,CAAC,CAAC5F,WAAW,CAAC,CAAC;IAChD,MAAM6F,UAAU,GAAG,CAAC,GAAG;IACvB,MAAMC,YAAY,GAAGjH,QAAQ,GAAG,GAAG;IACnC,MAAMkH,aAAa,GAAGxH,IAAI,CAACkE,EAAE,KAAK,IAAI,CAAC/C,aAAa,GAAGmG,UAAU,GAAGC,YAAY;IAChF,OAAO,IAAI,CAACF,UAAU,CAAC,CAAC,CAACjF,WAAW,CAACoF,aAAa,CAAC;EACvD;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;EACxC;EACAC,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEC,IAAI;MAAEjE,UAAU;MAAEN,aAAa,EAAE;QAAEV;MAAS;IAAG,CAAC,GAAG,IAAI;IAC/D,MAAMkF,OAAO,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnC,MAAMC,cAAc,GAAGH,IAAI,KAAKnD,SAAS,IAAI9B,QAAQ;IACrD,IAAIgB,UAAU,CAACqE,UAAU,IAAID,cAAc,EAAE;MACzC,MAAME,KAAK,GAAG,IAAI,CAACf,QAAQ,CAAC,CAAC;MAC7B,MAAMgB,KAAK,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,KAAKpI,IAAI,CAACqI,IAAI,GAE3C,IAAI,CAACjC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC9C,SAAS,CAAC,CAAC,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC,GAExE,IAAI,CAAC4B,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC,CAAC9C,SAAS,CAAC,CAAC,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;MAChF,IAAI,CAACG,IAAI,CAACkB,UAAU,CAACiC,OAAO,CAAC,CAACQ,IAAI,CAACJ,KAAK,CAAC,CAACK,IAAI,CAACJ,KAAK,CAAC,CAACK,aAAa,CAAC,CAAC;IACzE;EACJ;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,SAAS,CAAC/G,OAAO,CAAEgH,QAAQ,IAAK;MACjC,IAAIzI,KAAK,CAACyI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACrF,aAAa,CAACG,QAAQ,EAAE;QACjD;MACJ;MACAkF,QAAQ,CAAC9C,UAAU,CAAC,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC;MACtC+C,QAAQ,CAACH,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAI,eAAeA,CAAA,EAAG;IACd,MAAMC,KAAK,GAAG,IAAI,CAAC1B,QAAQ,CAAC,CAAC;IAC7B,MAAMnG,KAAK,GAAG,IAAI,CAACoF,QAAQ,CAAC,CAAC;IAC7B,MAAMT,GAAG,GAAG,IAAI,CAACoC,YAAY,CAAC,CAAC;IAC/B,MAAMnF,QAAQ,GAAG,IAAI,CAACU,aAAa,CAACV,QAAQ;IAC5C,MAAMpC,WAAW,GAAG,IAAI,CAAC8C,aAAa,CAACI,oBAAoB;IAC3D,IAAId,QAAQ,IAAIpC,WAAW,EAAE;MACzB,MAAMF,QAAQ,GAAG,IAAI,CAAC+G,UAAU,CAAC,CAAC,CAAC5F,WAAW,CAAC,CAAC;MAChD,MAAMlB,WAAW,GAAG,IAAI,CAACwC,SAAS,CAACC,GAAG,CAAEuD,QAAQ,IAAKP,MAAM,CAACO,QAAQ,CAACrD,GAAG,CAAC,CAAC;MAC1E,MAAMjC,aAAa,GAAGZ,qBAAqB,CAACC,QAAQ,EAAEC,WAAW,CAAC;MAClE,MAAMmB,SAAS,GAAGX,mBAAmB,CAACC,KAAK,EAAEC,aAAa,EAAE,IAAI,CAACoG,UAAU,CAAC,CAAC,EAAE,IAAI,CAACe,gBAAgB,CAAC,CAAC,CAAC;MACvGzC,GAAG,CAACmD,YAAY,CAAC9I,IAAI,CAAC+I,KAAK,CAAC;MAC5BrH,SAAS,CAACC,OAAO,CAAEqH,MAAM,IAAK;QAC1B,IAAIA,MAAM,CAAClI,MAAM,KAAK,CAAC,EACnB;QACJ6E,GAAG,CAACsD,SAAS,CAAC,CAAC;QACftD,GAAG,CAACuD,MAAM,CAACL,KAAK,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5BrD,GAAG,CAACwD,MAAM,CAACN,KAAK,EAAEG,MAAM,CAACA,MAAM,CAAClI,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C6E,GAAG,CAACyD,MAAM,CAAC,CAAC;QACZzD,GAAG,CAAC0D,SAAS,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;EACJ;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM3D,GAAG,GAAG,IAAI,CAACoC,YAAY,CAAC,CAAC;IAC/B,MAAM9E,CAAC,GAAG,IAAI,CAAC8D,YAAY,CAAC,CAAC;IAC7B,MAAMjB,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvC,SAAS,CAACjC,MAAM,EAAE,EAAEwE,CAAC,EAAE;MAC5C,MAAMnD,CAAC,GAAG2D,EAAE,CAACR,CAAC,CAAC,GAAG,IAAI,CAAChC,aAAa,CAACK,MAAM;MAC3C,MAAMqB,EAAE,GAAG,IAAI,CAACnC,WAAW,CAACyC,CAAC,CAAC;MAC9B,MAAMiE,IAAI,GAAGtG,CAAC,GAAG+B,EAAE,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC;MAClCC,GAAG,CAAC6D,SAAS,CAACD,IAAI,GAAG,CAAC,EAAEpH,CAAC,GAAG,CAAC,EAAE6C,EAAE,CAACU,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACpDV,EAAE,CAACyE,UAAU,CAAC9D,GAAG,EAAE4D,IAAI,EAAEpH,CAAC,CAAC;IAC/B;EACJ;EACAuH,IAAIA,CAAA,EAAG;IACH,MAAM/D,GAAG,GAAG,IAAI,CAACoC,YAAY,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACjC,EAAE,CAAChF,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIV,YAAY,CAAC,WAAW,EAAE,mCAAmC,CAAC;IAC5E;IACA,IAAI,CAACuJ,WAAW,CAAC,CAAC;IAClB,MAAMC,UAAU,GAAG,IAAI,CAAC/B,IAAI,KAAKnD,SAAS,IAAI,IAAI,CAACpB,aAAa,CAACV,QAAQ;IACzE+C,GAAG,CAACkE,SAAS,CAAC,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACjD,IAAI,CAACR,aAAa,CAAC,CAAC;IACpB,IAAI,CAACV,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAAC1C,IAAI,IAAI0D,UAAU,EAAE;MACzB,MAAMf,KAAK,GAAG,IAAI,CAAC1B,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAACjB,IAAI,CAAC6D,kBAAkB,CAAClB,KAAK,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAAC3C,IAAI,CAACL,UAAU,CAACF,GAAG,CAAC,CAAC6C,aAAa,CAAC,CAAC;IAC7C;IACA,IAAI,CAACZ,QAAQ,CAAC,CAAC;IACf,IAAI,CAACa,aAAa,CAAC,CAAC;IACpB9C,GAAG,CAACqE,UAAU,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}