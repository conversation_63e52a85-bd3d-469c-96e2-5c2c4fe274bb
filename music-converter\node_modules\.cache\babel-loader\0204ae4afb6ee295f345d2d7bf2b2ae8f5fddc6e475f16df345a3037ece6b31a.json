{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\VexTabBlock.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VexTabBlock({\n  source,\n  id\n}) {\n  _s();\n  const divRef = useRef(null);\n  useEffect(() => {\n    const el = divRef.current;\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\n    console.log(`VexTabBlock ${id || 'unnamed'} content:`, source);\n    el.textContent = source;\n    while (el.firstChild) {\n      el.removeChild(el.firstChild);\n    }\n    new window.VexTab.Div(el);\n  }, [source, id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    style: {\n      minHeight: '6rem'\n    },\n    editor: \"true\",\n    contentEditable: \"true\",\n    id: id,\n    ref: divRef\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(VexTabBlock, \"yu+j3H3uMpPkc7UDcPsbvSQj1vE=\");\n_c = VexTabBlock;\nexport default VexTabBlock;\nvar _c;\n$RefreshReg$(_c, \"VexTabBlock\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "VexTabBlock", "source", "id", "_s", "divRef", "el", "current", "window", "VexTab", "Div", "console", "log", "textContent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "className", "style", "minHeight", "editor", "contentEditable", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/VexTabBlock.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\n\r\nfunction VexTabBlock({ source, id }) {\r\n  const divRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const el = divRef.current;\r\n    if (!el || !window.VexTab || !window.VexTab.Div) return;\r\n\r\n    console.log(`VexTabBlock ${id || 'unnamed'} content:`, source);\r\n\r\n    el.textContent = source;\r\n    while (el.firstChild) {\r\n      el.removeChild(el.firstChild);\r\n    }\r\n\r\n    new window.VexTab.Div(el);\r\n  }, [source, id]);\r\n\r\n  return (\r\n    <div\r\n      className=\"vextab-auto\"\r\n      style={{ minHeight: '6rem' }}\r\n      editor=\"true\"\r\n      contentEditable=\"true\"\r\n      id={id}\r\n      ref={divRef}\r\n    />\r\n  )  \r\n}\r\n\r\nexport default VexTabBlock;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,WAAWA,CAAC;EAAEC,MAAM;EAAEC;AAAG,CAAC,EAAE;EAAAC,EAAA;EACnC,MAAMC,MAAM,GAAGP,MAAM,CAAC,IAAI,CAAC;EAE3BD,SAAS,CAAC,MAAM;IACd,MAAMS,EAAE,GAAGD,MAAM,CAACE,OAAO;IACzB,IAAI,CAACD,EAAE,IAAI,CAACE,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;IAEjDC,OAAO,CAACC,GAAG,CAAC,eAAeT,EAAE,IAAI,SAAS,WAAW,EAAED,MAAM,CAAC;IAE9DI,EAAE,CAACO,WAAW,GAAGX,MAAM;IACvB,OAAOI,EAAE,CAACQ,UAAU,EAAE;MACpBR,EAAE,CAACS,WAAW,CAACT,EAAE,CAACQ,UAAU,CAAC;IAC/B;IAEA,IAAIN,MAAM,CAACC,MAAM,CAACC,GAAG,CAACJ,EAAE,CAAC;EAC3B,CAAC,EAAE,CAACJ,MAAM,EAAEC,EAAE,CAAC,CAAC;EAEhB,oBACEH,OAAA;IACEgB,SAAS,EAAC,aAAa;IACvBC,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAO,CAAE;IAC7BC,MAAM,EAAC,MAAM;IACbC,eAAe,EAAC,MAAM;IACtBjB,EAAE,EAAEA,EAAG;IACPkB,GAAG,EAAEhB;EAAO;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEN;AAACrB,EAAA,CA3BQH,WAAW;AAAAyB,EAAA,GAAXzB,WAAW;AA6BpB,eAAeA,WAAW;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}