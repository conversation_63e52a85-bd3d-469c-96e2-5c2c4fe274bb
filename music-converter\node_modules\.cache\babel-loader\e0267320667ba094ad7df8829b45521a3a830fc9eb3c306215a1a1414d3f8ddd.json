{"ast": null, "code": "import { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class FretHandFinger extends Modifier {\n  static get CATEGORY() {\n    return \"FretHandFinger\";\n  }\n  static format(nums, state) {\n    const {\n      leftShift,\n      rightShift\n    } = state;\n    const numSpacing = 1;\n    if (!nums || nums.length === 0) return false;\n    const numsList = [];\n    let prevNote = null;\n    let shiftLeft = 0;\n    let shiftRight = 0;\n    for (let i = 0; i < nums.length; ++i) {\n      const num = nums[i];\n      const note = num.getNote();\n      const pos = num.getPosition();\n      const index = num.checkIndex();\n      const props = note.getKeyProps()[index];\n      const textHeight = Metrics.get('FretHandFinger.fontSize');\n      if (num.position === ModifierPosition.ABOVE) {\n        state.topTextLine += textHeight / Tables.STAVE_LINE_DISTANCE + 0.5;\n      }\n      if (num.position === ModifierPosition.BELOW) {\n        state.textLine += textHeight / Tables.STAVE_LINE_DISTANCE + 0.5;\n      }\n      if (note !== prevNote) {\n        for (let n = 0; n < note.keys.length; ++n) {\n          if (leftShift === 0) {\n            shiftLeft = Math.max(note.getLeftDisplacedHeadPx(), shiftLeft);\n          }\n          if (rightShift === 0) {\n            shiftRight = Math.max(note.getRightDisplacedHeadPx(), shiftRight);\n          }\n        }\n        prevNote = note;\n      }\n      numsList.push({\n        note,\n        num,\n        pos,\n        line: props.line,\n        shiftL: shiftLeft,\n        shiftR: shiftRight\n      });\n    }\n    numsList.sort((a, b) => b.line - a.line);\n    let numShiftL = 0;\n    let numShiftR = 0;\n    let xWidthL = 0;\n    let xWidthR = 0;\n    let lastLine = null;\n    let lastNote = null;\n    for (let i = 0; i < numsList.length; ++i) {\n      let numShift = 0;\n      const {\n        note,\n        pos,\n        num,\n        line,\n        shiftL,\n        shiftR\n      } = numsList[i];\n      if (line !== lastLine || note !== lastNote) {\n        numShiftL = leftShift + shiftL;\n        numShiftR = rightShift + shiftR;\n      }\n      const numWidth = num.getWidth() + numSpacing;\n      if (pos === Modifier.Position.LEFT) {\n        num.setXShift(leftShift + numShiftL);\n        numShift = leftShift + numWidth;\n        xWidthL = numShift > xWidthL ? numShift : xWidthL;\n      } else if (pos === Modifier.Position.RIGHT) {\n        num.setXShift(numShiftR);\n        numShift = shiftRight + numWidth;\n        xWidthR = numShift > xWidthR ? numShift : xWidthR;\n      }\n      lastLine = line;\n      lastNote = note;\n    }\n    state.leftShift += xWidthL;\n    state.rightShift += xWidthR;\n    return true;\n  }\n  static easyScoreHook({\n    fingerings\n  } = {}, note, builder) {\n    fingerings === null || fingerings === void 0 ? void 0 : fingerings.split(',').map(fingeringString => {\n      const split = fingeringString.trim().split('.');\n      const params = {\n        number: split[0]\n      };\n      if (split[1]) params.position = split[1];\n      return builder.getFactory().Fingering(params);\n    }).map((fingering, index) => note.addModifier(fingering, index));\n  }\n  constructor(finger) {\n    super();\n    this.setFretHandFinger(finger);\n    this.position = Modifier.Position.LEFT;\n    this.xOffset = 0;\n    this.yOffset = 0;\n  }\n  setFretHandFinger(finger) {\n    this.text = finger;\n    return this;\n  }\n  getFretHandFinger() {\n    return this.text;\n  }\n  setOffsetX(x) {\n    this.xOffset = x;\n    return this;\n  }\n  setOffsetY(y) {\n    this.yOffset = y;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(this.position, this.index);\n    let dotX = start.x + this.xOffset;\n    let dotY = start.y + this.yOffset + 5;\n    switch (this.position) {\n      case Modifier.Position.ABOVE:\n        dotX -= 4;\n        dotY -= 12;\n        break;\n      case Modifier.Position.BELOW:\n        dotX -= 2;\n        dotY += 10;\n        break;\n      case Modifier.Position.LEFT:\n        dotX -= this.width;\n        break;\n      case Modifier.Position.RIGHT:\n        dotX += 1;\n        break;\n      default:\n        throw new RuntimeError('InvalidPosition', `The position ${this.position} does not exist`);\n    }\n    this.renderText(ctx, dotX, dotY);\n  }\n}", "map": {"version": 3, "names": ["Metrics", "Modifier", "ModifierPosition", "Tables", "RuntimeError", "FretHandFinger", "CATEGORY", "format", "nums", "state", "leftShift", "rightShift", "numSpacing", "length", "numsList", "prevNote", "shiftLeft", "shiftRight", "i", "num", "note", "getNote", "pos", "getPosition", "index", "checkIndex", "props", "getKeyProps", "textHeight", "get", "position", "ABOVE", "topTextLine", "STAVE_LINE_DISTANCE", "BELOW", "textLine", "n", "keys", "Math", "max", "getLeftDisplacedHeadPx", "getRightDisplacedHeadPx", "push", "line", "shiftL", "shiftR", "sort", "a", "b", "numShiftL", "numShiftR", "xWidthL", "xWidthR", "lastLine", "lastNote", "numShift", "numWidth", "getWidth", "Position", "LEFT", "setXShift", "RIGHT", "easyScoreHook", "fingerings", "builder", "split", "map", "fingeringString", "trim", "params", "number", "getFactory", "Fingering", "fingering", "addModifier", "constructor", "finger", "setFretHand<PERSON>inger", "xOffset", "yOffset", "text", "getFretHandFinger", "setOffsetX", "x", "setOffsetY", "y", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "dotX", "dotY", "width", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/frethandfinger.js"], "sourcesContent": ["import { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class FretHandFinger extends Modifier {\n    static get CATEGORY() {\n        return \"FretHandFinger\";\n    }\n    static format(nums, state) {\n        const { leftShift, rightShift } = state;\n        const numSpacing = 1;\n        if (!nums || nums.length === 0)\n            return false;\n        const numsList = [];\n        let prevNote = null;\n        let shiftLeft = 0;\n        let shiftRight = 0;\n        for (let i = 0; i < nums.length; ++i) {\n            const num = nums[i];\n            const note = num.getNote();\n            const pos = num.getPosition();\n            const index = num.checkIndex();\n            const props = note.getKeyProps()[index];\n            const textHeight = Metrics.get('FretHandFinger.fontSize');\n            if (num.position === ModifierPosition.ABOVE) {\n                state.topTextLine += textHeight / Tables.STAVE_LINE_DISTANCE + 0.5;\n            }\n            if (num.position === ModifierPosition.BELOW) {\n                state.textLine += textHeight / Tables.STAVE_LINE_DISTANCE + 0.5;\n            }\n            if (note !== prevNote) {\n                for (let n = 0; n < note.keys.length; ++n) {\n                    if (leftShift === 0) {\n                        shiftLeft = Math.max(note.getLeftDisplacedHeadPx(), shiftLeft);\n                    }\n                    if (rightShift === 0) {\n                        shiftRight = Math.max(note.getRightDisplacedHeadPx(), shiftRight);\n                    }\n                }\n                prevNote = note;\n            }\n            numsList.push({\n                note,\n                num,\n                pos,\n                line: props.line,\n                shiftL: shiftLeft,\n                shiftR: shiftRight,\n            });\n        }\n        numsList.sort((a, b) => b.line - a.line);\n        let numShiftL = 0;\n        let numShiftR = 0;\n        let xWidthL = 0;\n        let xWidthR = 0;\n        let lastLine = null;\n        let lastNote = null;\n        for (let i = 0; i < numsList.length; ++i) {\n            let numShift = 0;\n            const { note, pos, num, line, shiftL, shiftR } = numsList[i];\n            if (line !== lastLine || note !== lastNote) {\n                numShiftL = leftShift + shiftL;\n                numShiftR = rightShift + shiftR;\n            }\n            const numWidth = num.getWidth() + numSpacing;\n            if (pos === Modifier.Position.LEFT) {\n                num.setXShift(leftShift + numShiftL);\n                numShift = leftShift + numWidth;\n                xWidthL = numShift > xWidthL ? numShift : xWidthL;\n            }\n            else if (pos === Modifier.Position.RIGHT) {\n                num.setXShift(numShiftR);\n                numShift = shiftRight + numWidth;\n                xWidthR = numShift > xWidthR ? numShift : xWidthR;\n            }\n            lastLine = line;\n            lastNote = note;\n        }\n        state.leftShift += xWidthL;\n        state.rightShift += xWidthR;\n        return true;\n    }\n    static easyScoreHook({ fingerings } = {}, note, builder) {\n        fingerings === null || fingerings === void 0 ? void 0 : fingerings.split(',').map((fingeringString) => {\n            const split = fingeringString.trim().split('.');\n            const params = { number: split[0] };\n            if (split[1])\n                params.position = split[1];\n            return builder.getFactory().Fingering(params);\n        }).map((fingering, index) => note.addModifier(fingering, index));\n    }\n    constructor(finger) {\n        super();\n        this.setFretHandFinger(finger);\n        this.position = Modifier.Position.LEFT;\n        this.xOffset = 0;\n        this.yOffset = 0;\n    }\n    setFretHandFinger(finger) {\n        this.text = finger;\n        return this;\n    }\n    getFretHandFinger() {\n        return this.text;\n    }\n    setOffsetX(x) {\n        this.xOffset = x;\n        return this;\n    }\n    setOffsetY(y) {\n        this.yOffset = y;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(this.position, this.index);\n        let dotX = start.x + this.xOffset;\n        let dotY = start.y + this.yOffset + 5;\n        switch (this.position) {\n            case Modifier.Position.ABOVE:\n                dotX -= 4;\n                dotY -= 12;\n                break;\n            case Modifier.Position.BELOW:\n                dotX -= 2;\n                dotY += 10;\n                break;\n            case Modifier.Position.LEFT:\n                dotX -= this.width;\n                break;\n            case Modifier.Position.RIGHT:\n                dotX += 1;\n                break;\n            default:\n                throw new RuntimeError('InvalidPosition', `The position ${this.position} does not exist`);\n        }\n        this.renderText(ctx, dotX, dotY);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,cAAc,SAASJ,QAAQ,CAAC;EACzC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,gBAAgB;EAC3B;EACA,OAAOC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACvB,MAAM;MAAEC,SAAS;MAAEC;IAAW,CAAC,GAAGF,KAAK;IACvC,MAAMG,UAAU,GAAG,CAAC;IACpB,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,CAAC,EAC1B,OAAO,KAAK;IAChB,MAAMC,QAAQ,GAAG,EAAE;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACK,MAAM,EAAE,EAAEK,CAAC,EAAE;MAClC,MAAMC,GAAG,GAAGX,IAAI,CAACU,CAAC,CAAC;MACnB,MAAME,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,CAAC;MAC1B,MAAMC,GAAG,GAAGH,GAAG,CAACI,WAAW,CAAC,CAAC;MAC7B,MAAMC,KAAK,GAAGL,GAAG,CAACM,UAAU,CAAC,CAAC;MAC9B,MAAMC,KAAK,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC,CAACH,KAAK,CAAC;MACvC,MAAMI,UAAU,GAAG5B,OAAO,CAAC6B,GAAG,CAAC,yBAAyB,CAAC;MACzD,IAAIV,GAAG,CAACW,QAAQ,KAAK5B,gBAAgB,CAAC6B,KAAK,EAAE;QACzCtB,KAAK,CAACuB,WAAW,IAAIJ,UAAU,GAAGzB,MAAM,CAAC8B,mBAAmB,GAAG,GAAG;MACtE;MACA,IAAId,GAAG,CAACW,QAAQ,KAAK5B,gBAAgB,CAACgC,KAAK,EAAE;QACzCzB,KAAK,CAAC0B,QAAQ,IAAIP,UAAU,GAAGzB,MAAM,CAAC8B,mBAAmB,GAAG,GAAG;MACnE;MACA,IAAIb,IAAI,KAAKL,QAAQ,EAAE;QACnB,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,IAAI,CAACiB,IAAI,CAACxB,MAAM,EAAE,EAAEuB,CAAC,EAAE;UACvC,IAAI1B,SAAS,KAAK,CAAC,EAAE;YACjBM,SAAS,GAAGsB,IAAI,CAACC,GAAG,CAACnB,IAAI,CAACoB,sBAAsB,CAAC,CAAC,EAAExB,SAAS,CAAC;UAClE;UACA,IAAIL,UAAU,KAAK,CAAC,EAAE;YAClBM,UAAU,GAAGqB,IAAI,CAACC,GAAG,CAACnB,IAAI,CAACqB,uBAAuB,CAAC,CAAC,EAAExB,UAAU,CAAC;UACrE;QACJ;QACAF,QAAQ,GAAGK,IAAI;MACnB;MACAN,QAAQ,CAAC4B,IAAI,CAAC;QACVtB,IAAI;QACJD,GAAG;QACHG,GAAG;QACHqB,IAAI,EAAEjB,KAAK,CAACiB,IAAI;QAChBC,MAAM,EAAE5B,SAAS;QACjB6B,MAAM,EAAE5B;MACZ,CAAC,CAAC;IACN;IACAH,QAAQ,CAACgC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,IAAI,GAAGI,CAAC,CAACJ,IAAI,CAAC;IACxC,IAAIM,SAAS,GAAG,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACD,MAAM,EAAE,EAAEK,CAAC,EAAE;MACtC,IAAIqC,QAAQ,GAAG,CAAC;MAChB,MAAM;QAAEnC,IAAI;QAAEE,GAAG;QAAEH,GAAG;QAAEwB,IAAI;QAAEC,MAAM;QAAEC;MAAO,CAAC,GAAG/B,QAAQ,CAACI,CAAC,CAAC;MAC5D,IAAIyB,IAAI,KAAKU,QAAQ,IAAIjC,IAAI,KAAKkC,QAAQ,EAAE;QACxCL,SAAS,GAAGvC,SAAS,GAAGkC,MAAM;QAC9BM,SAAS,GAAGvC,UAAU,GAAGkC,MAAM;MACnC;MACA,MAAMW,QAAQ,GAAGrC,GAAG,CAACsC,QAAQ,CAAC,CAAC,GAAG7C,UAAU;MAC5C,IAAIU,GAAG,KAAKrB,QAAQ,CAACyD,QAAQ,CAACC,IAAI,EAAE;QAChCxC,GAAG,CAACyC,SAAS,CAAClD,SAAS,GAAGuC,SAAS,CAAC;QACpCM,QAAQ,GAAG7C,SAAS,GAAG8C,QAAQ;QAC/BL,OAAO,GAAGI,QAAQ,GAAGJ,OAAO,GAAGI,QAAQ,GAAGJ,OAAO;MACrD,CAAC,MACI,IAAI7B,GAAG,KAAKrB,QAAQ,CAACyD,QAAQ,CAACG,KAAK,EAAE;QACtC1C,GAAG,CAACyC,SAAS,CAACV,SAAS,CAAC;QACxBK,QAAQ,GAAGtC,UAAU,GAAGuC,QAAQ;QAChCJ,OAAO,GAAGG,QAAQ,GAAGH,OAAO,GAAGG,QAAQ,GAAGH,OAAO;MACrD;MACAC,QAAQ,GAAGV,IAAI;MACfW,QAAQ,GAAGlC,IAAI;IACnB;IACAX,KAAK,CAACC,SAAS,IAAIyC,OAAO;IAC1B1C,KAAK,CAACE,UAAU,IAAIyC,OAAO;IAC3B,OAAO,IAAI;EACf;EACA,OAAOU,aAAaA,CAAC;IAAEC;EAAW,CAAC,GAAG,CAAC,CAAC,EAAE3C,IAAI,EAAE4C,OAAO,EAAE;IACrDD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,eAAe,IAAK;MACnG,MAAMF,KAAK,GAAGE,eAAe,CAACC,IAAI,CAAC,CAAC,CAACH,KAAK,CAAC,GAAG,CAAC;MAC/C,MAAMI,MAAM,GAAG;QAAEC,MAAM,EAAEL,KAAK,CAAC,CAAC;MAAE,CAAC;MACnC,IAAIA,KAAK,CAAC,CAAC,CAAC,EACRI,MAAM,CAACvC,QAAQ,GAAGmC,KAAK,CAAC,CAAC,CAAC;MAC9B,OAAOD,OAAO,CAACO,UAAU,CAAC,CAAC,CAACC,SAAS,CAACH,MAAM,CAAC;IACjD,CAAC,CAAC,CAACH,GAAG,CAAC,CAACO,SAAS,EAAEjD,KAAK,KAAKJ,IAAI,CAACsD,WAAW,CAACD,SAAS,EAAEjD,KAAK,CAAC,CAAC;EACpE;EACAmD,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IAC9B,IAAI,CAAC9C,QAAQ,GAAG7B,QAAQ,CAACyD,QAAQ,CAACC,IAAI;IACtC,IAAI,CAACmB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAF,iBAAiBA,CAACD,MAAM,EAAE;IACtB,IAAI,CAACI,IAAI,GAAGJ,MAAM;IAClB,OAAO,IAAI;EACf;EACAK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,IAAI;EACpB;EACAE,UAAUA,CAACC,CAAC,EAAE;IACV,IAAI,CAACL,OAAO,GAAGK,CAAC;IAChB,OAAO,IAAI;EACf;EACAC,UAAUA,CAACC,CAAC,EAAE;IACV,IAAI,CAACN,OAAO,GAAGM,CAAC;IAChB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMpE,IAAI,GAAG,IAAI,CAACqE,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGvE,IAAI,CAACwE,kBAAkB,CAAC,IAAI,CAAC9D,QAAQ,EAAE,IAAI,CAACN,KAAK,CAAC;IAChE,IAAIqE,IAAI,GAAGF,KAAK,CAACR,CAAC,GAAG,IAAI,CAACL,OAAO;IACjC,IAAIgB,IAAI,GAAGH,KAAK,CAACN,CAAC,GAAG,IAAI,CAACN,OAAO,GAAG,CAAC;IACrC,QAAQ,IAAI,CAACjD,QAAQ;MACjB,KAAK7B,QAAQ,CAACyD,QAAQ,CAAC3B,KAAK;QACxB8D,IAAI,IAAI,CAAC;QACTC,IAAI,IAAI,EAAE;QACV;MACJ,KAAK7F,QAAQ,CAACyD,QAAQ,CAACxB,KAAK;QACxB2D,IAAI,IAAI,CAAC;QACTC,IAAI,IAAI,EAAE;QACV;MACJ,KAAK7F,QAAQ,CAACyD,QAAQ,CAACC,IAAI;QACvBkC,IAAI,IAAI,IAAI,CAACE,KAAK;QAClB;MACJ,KAAK9F,QAAQ,CAACyD,QAAQ,CAACG,KAAK;QACxBgC,IAAI,IAAI,CAAC;QACT;MACJ;QACI,MAAM,IAAIzF,YAAY,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC0B,QAAQ,iBAAiB,CAAC;IACjG;IACA,IAAI,CAACkE,UAAU,CAACT,GAAG,EAAEM,IAAI,EAAEC,IAAI,CAAC;EACpC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}