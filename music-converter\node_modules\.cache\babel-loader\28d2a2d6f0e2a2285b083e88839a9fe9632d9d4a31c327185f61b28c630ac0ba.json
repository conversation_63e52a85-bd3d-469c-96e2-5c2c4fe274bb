{"ast": null, "code": "import { Fraction } from './fraction.js';\nimport { Glyphs } from './glyphs.js';\nimport { RuntimeError } from './util.js';\nconst RESOLUTION = 16384;\nconst durations = {\n  '1/2': RESOLUTION * 2,\n  1: RESOLUTION / 1,\n  2: RESOLUTION / 2,\n  4: RESOLUTION / 4,\n  8: RESOLUTION / 8,\n  16: RESOLUTION / 16,\n  32: RESOLUTION / 32,\n  64: RESOLUTION / 64,\n  128: RESOLUTION / 128,\n  256: RESOLUTION / 256\n};\nconst durationAliases = {\n  w: '1',\n  h: '2',\n  q: '4',\n  b: '256'\n};\nconst keySignatures = {\n  C: {\n    num: 0\n  },\n  Am: {\n    num: 0\n  },\n  F: {\n    accidental: 'b',\n    num: 1\n  },\n  Dm: {\n    accidental: 'b',\n    num: 1\n  },\n  Bb: {\n    accidental: 'b',\n    num: 2\n  },\n  Gm: {\n    accidental: 'b',\n    num: 2\n  },\n  Eb: {\n    accidental: 'b',\n    num: 3\n  },\n  Cm: {\n    accidental: 'b',\n    num: 3\n  },\n  Ab: {\n    accidental: 'b',\n    num: 4\n  },\n  Fm: {\n    accidental: 'b',\n    num: 4\n  },\n  Db: {\n    accidental: 'b',\n    num: 5\n  },\n  Bbm: {\n    accidental: 'b',\n    num: 5\n  },\n  Gb: {\n    accidental: 'b',\n    num: 6\n  },\n  Ebm: {\n    accidental: 'b',\n    num: 6\n  },\n  Cb: {\n    accidental: 'b',\n    num: 7\n  },\n  Abm: {\n    accidental: 'b',\n    num: 7\n  },\n  G: {\n    accidental: '#',\n    num: 1\n  },\n  Em: {\n    accidental: '#',\n    num: 1\n  },\n  D: {\n    accidental: '#',\n    num: 2\n  },\n  Bm: {\n    accidental: '#',\n    num: 2\n  },\n  A: {\n    accidental: '#',\n    num: 3\n  },\n  'F#m': {\n    accidental: '#',\n    num: 3\n  },\n  E: {\n    accidental: '#',\n    num: 4\n  },\n  'C#m': {\n    accidental: '#',\n    num: 4\n  },\n  B: {\n    accidental: '#',\n    num: 5\n  },\n  'G#m': {\n    accidental: '#',\n    num: 5\n  },\n  'F#': {\n    accidental: '#',\n    num: 6\n  },\n  'D#m': {\n    accidental: '#',\n    num: 6\n  },\n  'C#': {\n    accidental: '#',\n    num: 7\n  },\n  'A#m': {\n    accidental: '#',\n    num: 7\n  }\n};\nconst clefs = {\n  treble: {\n    lineShift: 0\n  },\n  bass: {\n    lineShift: 6\n  },\n  tenor: {\n    lineShift: 4\n  },\n  alto: {\n    lineShift: 3\n  },\n  soprano: {\n    lineShift: 1\n  },\n  percussion: {\n    lineShift: 0\n  },\n  'mezzo-soprano': {\n    lineShift: 2\n  },\n  'baritone-c': {\n    lineShift: 5\n  },\n  'baritone-f': {\n    lineShift: 5\n  },\n  subbass: {\n    lineShift: 7\n  },\n  french: {\n    lineShift: -1\n  }\n};\nconst notesInfo = {\n  C: {\n    index: 0,\n    intVal: 0\n  },\n  CN: {\n    index: 0,\n    intVal: 0\n  },\n  'C#': {\n    index: 0,\n    intVal: 1\n  },\n  'C##': {\n    index: 0,\n    intVal: 2\n  },\n  CB: {\n    index: 0,\n    intVal: 11\n  },\n  CBB: {\n    index: 0,\n    intVal: 10\n  },\n  D: {\n    index: 1,\n    intVal: 2\n  },\n  DN: {\n    index: 1,\n    intVal: 2\n  },\n  'D#': {\n    index: 1,\n    intVal: 3\n  },\n  'D##': {\n    index: 1,\n    intVal: 4\n  },\n  DB: {\n    index: 1,\n    intVal: 1\n  },\n  DBB: {\n    index: 1,\n    intVal: 0\n  },\n  E: {\n    index: 2,\n    intVal: 4\n  },\n  EN: {\n    index: 2,\n    intVal: 4\n  },\n  'E#': {\n    index: 2,\n    intVal: 5\n  },\n  'E##': {\n    index: 2,\n    intVal: 6\n  },\n  EB: {\n    index: 2,\n    intVal: 3\n  },\n  EBB: {\n    index: 2,\n    intVal: 2\n  },\n  F: {\n    index: 3,\n    intVal: 5\n  },\n  FN: {\n    index: 3,\n    intVal: 5\n  },\n  'F#': {\n    index: 3,\n    intVal: 6\n  },\n  'F##': {\n    index: 3,\n    intVal: 7\n  },\n  FB: {\n    index: 3,\n    intVal: 4\n  },\n  FBB: {\n    index: 3,\n    intVal: 3\n  },\n  G: {\n    index: 4,\n    intVal: 7\n  },\n  GN: {\n    index: 4,\n    intVal: 7\n  },\n  'G#': {\n    index: 4,\n    intVal: 8\n  },\n  'G##': {\n    index: 4,\n    intVal: 9\n  },\n  GB: {\n    index: 4,\n    intVal: 6\n  },\n  GBB: {\n    index: 4,\n    intVal: 5\n  },\n  A: {\n    index: 5,\n    intVal: 9\n  },\n  AN: {\n    index: 5,\n    intVal: 9\n  },\n  'A#': {\n    index: 5,\n    intVal: 10\n  },\n  'A##': {\n    index: 5,\n    intVal: 11\n  },\n  AB: {\n    index: 5,\n    intVal: 8\n  },\n  ABB: {\n    index: 5,\n    intVal: 7\n  },\n  B: {\n    index: 6,\n    intVal: 11\n  },\n  BN: {\n    index: 6,\n    intVal: 11\n  },\n  'B#': {\n    index: 6,\n    intVal: 12\n  },\n  'B##': {\n    index: 6,\n    intVal: 13\n  },\n  BB: {\n    index: 6,\n    intVal: 10\n  },\n  BBB: {\n    index: 6,\n    intVal: 9\n  },\n  R: {\n    index: 6\n  },\n  X: {\n    index: 6\n  }\n};\nconst validNoteTypes = {\n  n: {\n    name: 'note'\n  },\n  r: {\n    name: 'rest'\n  },\n  h: {\n    name: 'harmonic'\n  },\n  m: {\n    name: 'muted'\n  },\n  s: {\n    name: 'slash'\n  },\n  g: {\n    name: 'ghost'\n  },\n  d: {\n    name: 'diamond'\n  },\n  x: {\n    name: 'x'\n  },\n  ci: {\n    name: 'circled'\n  },\n  cx: {\n    name: 'circle x'\n  },\n  sf: {\n    name: 'slashed'\n  },\n  sb: {\n    name: 'slashed backward'\n  },\n  sq: {\n    name: 'square'\n  },\n  tu: {\n    name: 'triangle up'\n  },\n  td: {\n    name: 'triangle down'\n  }\n};\nconst accidentals = {\n  '#': Glyphs.accidentalSharp,\n  '##': Glyphs.accidentalDoubleSharp,\n  b: Glyphs.accidentalFlat,\n  bb: Glyphs.accidentalDoubleFlat,\n  n: Glyphs.accidentalNatural,\n  '{': Glyphs.accidentalParensLeft,\n  '}': Glyphs.accidentalParensRight,\n  db: Glyphs.accidentalThreeQuarterTonesFlatZimmermann,\n  d: Glyphs.accidentalQuarterToneFlatStein,\n  '++': Glyphs.accidentalThreeQuarterTonesSharpStein,\n  '+': Glyphs.accidentalQuarterToneSharpStein,\n  '+-': Glyphs.accidentalKucukMucennebSharp,\n  bs: Glyphs.accidentalBakiyeFlat,\n  bss: Glyphs.accidentalBuyukMucennebFlat,\n  o: Glyphs.accidentalSori,\n  k: Glyphs.accidentalKoron,\n  bbs: Glyphs.accidentalBuyukMucennebSharp,\n  '++-': Glyphs.accidentalBuyukMucennebSharp,\n  ashs: Glyphs.accidentalBuyukMucennebSharp,\n  afhf: Glyphs.accidentalBuyukMucennebSharp\n};\nconst accidentalColumns = {\n  1: {\n    a: [1],\n    b: [1]\n  },\n  2: {\n    a: [1, 2]\n  },\n  3: {\n    a: [1, 3, 2],\n    b: [1, 2, 1],\n    secondOnBottom: [1, 2, 3]\n  },\n  4: {\n    a: [1, 3, 4, 2],\n    b: [1, 2, 3, 1],\n    spacedOutTetrachord: [1, 2, 1, 2]\n  },\n  5: {\n    a: [1, 3, 5, 4, 2],\n    b: [1, 2, 4, 3, 1],\n    spacedOutPentachord: [1, 2, 3, 2, 1],\n    verySpacedOutPentachord: [1, 2, 1, 2, 1]\n  },\n  6: {\n    a: [1, 3, 5, 6, 4, 2],\n    b: [1, 2, 4, 5, 3, 1],\n    spacedOutHexachord: [1, 3, 2, 1, 3, 2],\n    verySpacedOutHexachord: [1, 2, 1, 2, 1, 2]\n  }\n};\nconst articulations = {\n  'a.': {\n    code: Glyphs.augmentationDot,\n    betweenLines: true\n  },\n  av: {\n    aboveCode: Glyphs.articStaccatissimoAbove,\n    belowCode: Glyphs.articStaccatissimoBelow,\n    betweenLines: true\n  },\n  'a>': {\n    aboveCode: Glyphs.articAccentAbove,\n    belowCode: Glyphs.articAccentBelow,\n    betweenLines: true\n  },\n  'a-': {\n    aboveCode: Glyphs.articTenutoAbove,\n    belowCode: Glyphs.articTenutoBelow,\n    betweenLines: true\n  },\n  'a^': {\n    aboveCode: Glyphs.articMarcatoAbove,\n    belowCode: Glyphs.articMarcatoBelow,\n    betweenLines: false\n  },\n  'a+': {\n    code: Glyphs.pluckedLeftHandPizzicato,\n    betweenLines: false\n  },\n  ao: {\n    aboveCode: Glyphs.pluckedSnapPizzicatoAbove,\n    belowCode: Glyphs.pluckedSnapPizzicatoBelow,\n    betweenLines: false\n  },\n  ah: {\n    code: Glyphs.stringsHarmonic,\n    betweenLines: false\n  },\n  'a@': {\n    aboveCode: Glyphs.fermataAbove,\n    belowCode: Glyphs.fermataBelow,\n    betweenLines: false\n  },\n  'a@a': {\n    code: Glyphs.fermataAbove,\n    betweenLines: false\n  },\n  'a@u': {\n    code: Glyphs.fermataBelow,\n    betweenLines: false\n  },\n  'a@s': {\n    aboveCode: Glyphs.fermataShortAbove,\n    belowCode: Glyphs.fermataShortBelow,\n    betweenLines: false\n  },\n  'a@as': {\n    code: Glyphs.fermataShortAbove,\n    betweenLines: false\n  },\n  'a@us': {\n    code: Glyphs.fermataShortBelow,\n    betweenLines: false\n  },\n  'a@l': {\n    aboveCode: Glyphs.fermataLongAbove,\n    belowCode: Glyphs.fermataLongBelow,\n    betweenLines: false\n  },\n  'a@al': {\n    code: Glyphs.fermataLongAbove,\n    betweenLines: false\n  },\n  'a@ul': {\n    code: Glyphs.fermataLongBelow,\n    betweenLines: false\n  },\n  'a@vl': {\n    aboveCode: Glyphs.fermataVeryLongAbove,\n    belowCode: Glyphs.fermataVeryLongBelow,\n    betweenLines: false\n  },\n  'a@avl': {\n    code: Glyphs.fermataVeryLongAbove,\n    betweenLines: false\n  },\n  'a@uvl': {\n    code: Glyphs.fermataVeryLongBelow,\n    betweenLines: false\n  },\n  'a|': {\n    code: Glyphs.stringsUpBow,\n    betweenLines: false\n  },\n  am: {\n    code: Glyphs.stringsDownBow,\n    betweenLines: false\n  },\n  'a,': {\n    code: Glyphs.pictChokeCymbal,\n    betweenLines: false\n  }\n};\nconst ornaments = {\n  mordent: Glyphs.ornamentShortTrill,\n  mordentInverted: Glyphs.ornamentMordent,\n  turn: Glyphs.ornamentTurn,\n  turnInverted: Glyphs.ornamentTurnSlash,\n  tr: Glyphs.ornamentTrill,\n  upprall: Glyphs.ornamentPrecompSlideTrillDAnglebert,\n  downprall: Glyphs.ornamentPrecompDoubleCadenceUpperPrefix,\n  prallup: Glyphs.ornamentPrecompTrillSuffixDandrieu,\n  pralldown: Glyphs.ornamentPrecompTrillLowerSuffix,\n  upmordent: Glyphs.ornamentPrecompSlideTrillBach,\n  downmordent: Glyphs.ornamentPrecompDoubleCadenceUpperPrefixTurn,\n  lineprall: Glyphs.ornamentPrecompAppoggTrill,\n  prallprall: Glyphs.ornamentTremblement,\n  scoop: Glyphs.brassScoop,\n  doit: Glyphs.brassDoitMedium,\n  fall: Glyphs.brassFallLipShort,\n  doitLong: Glyphs.brassLiftMedium,\n  fallLong: Glyphs.brassFallRoughMedium,\n  bend: Glyphs.brassBend,\n  plungerClosed: Glyphs.brassMuteClosed,\n  plungerOpen: Glyphs.brassMuteOpen,\n  flip: Glyphs.brassFlip,\n  jazzTurn: Glyphs.brassJazzTurn,\n  smear: Glyphs.brassSmear\n};\nexport class Tables {\n  static clefProperties(clef) {\n    if (!clef || !(clef in clefs)) throw new RuntimeError('BadArgument', 'Invalid clef: ' + clef);\n    return clefs[clef];\n  }\n  static keyProperties(keyOctaveGlyph, clef = 'treble', type = 'N', params) {\n    let options = {\n      octaveShift: 0,\n      duration: '4'\n    };\n    if (typeof params === 'object') {\n      options = Object.assign(Object.assign({}, options), params);\n    }\n    const duration = Tables.sanitizeDuration(options.duration);\n    const pieces = keyOctaveGlyph.split('/');\n    if (pieces.length < 2) {\n      throw new RuntimeError('BadArguments', `First argument must be note/octave or note/octave/glyph-code: ${keyOctaveGlyph}`);\n    }\n    const key = pieces[0].toUpperCase();\n    type = type.toUpperCase();\n    const value = notesInfo[key];\n    if (!value) throw new RuntimeError('BadArguments', 'Invalid key name: ' + key);\n    let octave = parseInt(pieces[1], 10);\n    octave -= options.octaveShift;\n    const baseIndex = octave * 7 - 4 * 7;\n    let line = (baseIndex + value.index) / 2;\n    line += Tables.clefProperties(clef).lineShift;\n    const intValue = typeof value.intVal !== 'undefined' ? octave * 12 + value.intVal : undefined;\n    let code = '';\n    let glyphName = 'N';\n    if (pieces.length > 2 && pieces[2]) {\n      glyphName = pieces[2].toUpperCase();\n    } else if (type !== 'N') {\n      glyphName = type;\n    } else glyphName = key;\n    code = this.codeNoteHead(glyphName, duration);\n    return {\n      key,\n      octave,\n      line,\n      intValue,\n      code,\n      displaced: false\n    };\n  }\n  static integerToNote(integer) {\n    if (typeof integer === 'undefined' || integer < 0 || integer > 11) {\n      throw new RuntimeError('BadArguments', `integerToNote() requires an integer in the range [0, 11]: ${integer}`);\n    }\n    const table = {\n      0: 'C',\n      1: 'C#',\n      2: 'D',\n      3: 'D#',\n      4: 'E',\n      5: 'F',\n      6: 'F#',\n      7: 'G',\n      8: 'G#',\n      9: 'A',\n      10: 'A#',\n      11: 'B'\n    };\n    const noteValue = table[integer];\n    if (!noteValue) {\n      throw new RuntimeError('BadArguments', `Unknown note value for integer: ${integer}`);\n    }\n    return noteValue;\n  }\n  static textWidth(text) {\n    return 7 * text.toString().length;\n  }\n  static articulationCodes(artic) {\n    return articulations[artic];\n  }\n  static accidentalCodes(accidental) {\n    var _a;\n    return (_a = accidentals[accidental]) !== null && _a !== void 0 ? _a : accidental;\n  }\n  static ornamentCodes(ornament) {\n    var _a;\n    return (_a = ornaments[ornament]) !== null && _a !== void 0 ? _a : ornament;\n  }\n  static keySignature(spec) {\n    const keySpec = keySignatures[spec];\n    if (!keySpec) {\n      throw new RuntimeError('BadKeySignature', `Bad key signature spec: '${spec}'`);\n    }\n    if (!keySpec.accidental) {\n      return [];\n    }\n    const accidentalList = {\n      b: [2, 0.5, 2.5, 1, 3, 1.5, 3.5],\n      '#': [0, 1.5, -0.5, 1, 2.5, 0.5, 2]\n    };\n    const notes = accidentalList[keySpec.accidental];\n    const accList = [];\n    for (let i = 0; i < keySpec.num; ++i) {\n      const line = notes[i];\n      accList.push({\n        type: keySpec.accidental,\n        line\n      });\n    }\n    return accList;\n  }\n  static getKeySignatures() {\n    return keySignatures;\n  }\n  static hasKeySignature(spec) {\n    return spec in keySignatures;\n  }\n  static sanitizeDuration(duration) {\n    const durationNumber = durationAliases[duration];\n    if (durationNumber !== undefined) {\n      duration = durationNumber;\n    }\n    if (durations[duration] === undefined) {\n      throw new RuntimeError('BadArguments', `The provided duration is not valid: ${duration}`);\n    }\n    return duration;\n  }\n  static durationToFraction(duration) {\n    return new Fraction().parse(Tables.sanitizeDuration(duration));\n  }\n  static durationToNumber(duration) {\n    return Tables.durationToFraction(duration).value();\n  }\n  static durationToTicks(duration) {\n    duration = Tables.sanitizeDuration(duration);\n    const ticks = durations[duration];\n    if (ticks === undefined) {\n      throw new RuntimeError('InvalidDuration');\n    }\n    return ticks;\n  }\n  static codeNoteHead(type, duration) {\n    switch (type) {\n      case 'D0':\n        return Glyphs.noteheadDiamondWhole;\n      case 'D1':\n        return Glyphs.noteheadDiamondHalf;\n      case 'D2':\n        return Glyphs.noteheadDiamondBlack;\n      case 'D3':\n        return Glyphs.noteheadDiamondBlack;\n      case 'T0':\n        return Glyphs.noteheadTriangleUpWhole;\n      case 'T1':\n        return Glyphs.noteheadTriangleUpHalf;\n      case 'T2':\n        return Glyphs.noteheadTriangleUpBlack;\n      case 'T3':\n        return Glyphs.noteheadTriangleUpBlack;\n      case 'X0':\n        return Glyphs.noteheadXWhole;\n      case 'X1':\n        return Glyphs.noteheadXHalf;\n      case 'X2':\n        return Glyphs.noteheadXBlack;\n      case 'X3':\n        return Glyphs.noteheadCircleX;\n      case 'S1':\n        return Glyphs.noteheadSquareWhite;\n      case 'S2':\n        return Glyphs.noteheadSquareBlack;\n      case 'R1':\n        return Glyphs.noteheadSquareWhite;\n      case 'R2':\n        return Glyphs.noteheadSquareWhite;\n      case 'DO':\n        return Glyphs.noteheadTriangleUpBlack;\n      case 'RE':\n        return Glyphs.noteheadMoonBlack;\n      case 'MI':\n        return Glyphs.noteheadDiamondBlack;\n      case 'FA':\n        return Glyphs.noteheadTriangleLeftBlack;\n      case 'FAUP':\n        return Glyphs.noteheadTriangleRightBlack;\n      case 'SO':\n        return Glyphs.noteheadBlack;\n      case 'LA':\n        return Glyphs.noteheadSquareBlack;\n      case 'TI':\n        return Glyphs.noteheadTriangleRoundDownBlack;\n      case 'DI':\n      case 'H':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadDiamondDoubleWhole;\n          case '1':\n            return Glyphs.noteheadDiamondWhole;\n          case '2':\n            return Glyphs.noteheadDiamondHalf;\n          default:\n            return Glyphs.noteheadDiamondBlack;\n        }\n      case 'X':\n      case 'M':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadXDoubleWhole;\n          case '1':\n            return Glyphs.noteheadXWhole;\n          case '2':\n            return Glyphs.noteheadXHalf;\n          default:\n            return Glyphs.noteheadXBlack;\n        }\n      case 'CX':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadCircleXDoubleWhole;\n          case '1':\n            return Glyphs.noteheadCircleXWhole;\n          case '2':\n            return Glyphs.noteheadCircleXHalf;\n          default:\n            return Glyphs.noteheadCircleX;\n        }\n      case 'CI':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadCircledDoubleWhole;\n          case '1':\n            return Glyphs.noteheadCircledWhole;\n          case '2':\n            return Glyphs.noteheadCircledHalf;\n          default:\n            return Glyphs.noteheadCircledBlack;\n        }\n      case 'SQ':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadDoubleWholeSquare;\n          case '1':\n            return Glyphs.noteheadSquareWhite;\n          case '2':\n            return Glyphs.noteheadSquareWhite;\n          default:\n            return Glyphs.noteheadSquareBlack;\n        }\n      case 'TU':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadTriangleUpDoubleWhole;\n          case '1':\n            return Glyphs.noteheadTriangleUpWhole;\n          case '2':\n            return Glyphs.noteheadTriangleUpHalf;\n          default:\n            return Glyphs.noteheadTriangleUpBlack;\n        }\n      case 'TD':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadTriangleDownDoubleWhole;\n          case '1':\n            return Glyphs.noteheadTriangleDownWhole;\n          case '2':\n            return Glyphs.noteheadTriangleDownHalf;\n          default:\n            return Glyphs.noteheadTriangleDownBlack;\n        }\n      case 'SF':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadSlashedDoubleWhole1;\n          case '1':\n            return Glyphs.noteheadSlashedWhole1;\n          case '2':\n            return Glyphs.noteheadSlashedHalf1;\n          default:\n            return Glyphs.noteheadSlashedBlack1;\n        }\n      case 'SB':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadSlashedDoubleWhole2;\n          case '1':\n            return Glyphs.noteheadSlashedWhole2;\n          case '2':\n            return Glyphs.noteheadSlashedHalf2;\n          default:\n            return Glyphs.noteheadSlashedBlack2;\n        }\n      case 'R':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.restDoubleWhole;\n          case '1':\n            return Glyphs.restWhole;\n          case '2':\n            return Glyphs.restHalf;\n          case '4':\n            return Glyphs.restQuarter;\n          case '8':\n            return Glyphs.rest8th;\n          case '16':\n            return Glyphs.rest16th;\n          case '32':\n            return Glyphs.rest32nd;\n          case '64':\n            return Glyphs.rest64th;\n          case '128':\n            return Glyphs.rest128th;\n        }\n        break;\n      case 'S':\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadSlashWhiteDoubleWhole;\n          case '1':\n            return Glyphs.noteheadSlashWhiteWhole;\n          case '2':\n            return Glyphs.noteheadSlashWhiteHalf;\n          default:\n            return Glyphs.noteheadSlashVerticalEnds;\n        }\n      default:\n        switch (duration) {\n          case '1/2':\n            return Glyphs.noteheadDoubleWhole;\n          case '1':\n            return Glyphs.noteheadWhole;\n          case '2':\n            return Glyphs.noteheadHalf;\n          default:\n            return Glyphs.noteheadBlack;\n        }\n    }\n    return Glyphs.null;\n  }\n}\nTables.UNISON = true;\nTables.SOFTMAX_FACTOR = 10;\nTables.STEM_WIDTH = 1.5;\nTables.STEM_HEIGHT = 35;\nTables.STAVE_LINE_THICKNESS = 1;\nTables.RENDER_PRECISION_PLACES = 3;\nTables.RESOLUTION = RESOLUTION;\nTables.durationCodes = {\n  '1/2': {\n    stem: false\n  },\n  1: {\n    stem: false\n  },\n  2: {\n    stem: true\n  },\n  4: {\n    stem: true\n  },\n  8: {\n    stem: true,\n    beamCount: 1,\n    stemBeamExtension: 0,\n    codeFlagUp: Glyphs.flag8thUp\n  },\n  16: {\n    beamCount: 2,\n    stemBeamExtension: 0,\n    stem: true,\n    codeFlagUp: Glyphs.flag16thUp\n  },\n  32: {\n    beamCount: 3,\n    stemBeamExtension: 7.5,\n    stem: true,\n    codeFlagUp: Glyphs.flag32ndUp\n  },\n  64: {\n    beamCount: 4,\n    stemBeamExtension: 15,\n    stem: true,\n    codeFlagUp: Glyphs.flag64thUp\n  },\n  128: {\n    beamCount: 5,\n    stemBeamExtension: 22.5,\n    stem: true,\n    codeFlagUp: Glyphs.flag128thUp\n  }\n};\nTables.NOTATION_FONT_SCALE = 39;\nTables.TABLATURE_FONT_SCALE = 39;\nTables.SLASH_NOTEHEAD_WIDTH = 15;\nTables.STAVE_LINE_DISTANCE = 10;\nTables.TEXT_HEIGHT_OFFSET_HACK = 1;\nTables.accidentalColumnsTable = accidentalColumns;\nTables.unicode = {\n  sharp: '\\u266f',\n  flat: '\\u266d',\n  natural: '\\u266e',\n  triangle: '\\u25b3',\n  'o-with-slash': '\\u00f8',\n  degrees: '\\u00b0',\n  circle: '\\u25cb'\n};\nTables.validTypes = validNoteTypes;\nTables.TIME4_4 = {\n  numBeats: 4,\n  beatValue: 4,\n  resolution: RESOLUTION\n};", "map": {"version": 3, "names": ["Fraction", "Glyphs", "RuntimeError", "RESOLUTION", "durations", "durationAliases", "w", "h", "q", "b", "keySignatures", "C", "num", "Am", "F", "accidental", "Dm", "Bb", "Gm", "Eb", "Cm", "Ab", "Fm", "Db", "Bbm", "Gb", "Ebm", "Cb", "Abm", "G", "Em", "D", "Bm", "A", "E", "B", "clefs", "treble", "lineShift", "bass", "tenor", "alto", "soprano", "percussion", "subbass", "french", "notesInfo", "index", "intVal", "CN", "CB", "CBB", "DN", "DB", "DBB", "EN", "EB", "EBB", "FN", "FB", "FBB", "GN", "GB", "GBB", "AN", "AB", "ABB", "BN", "BB", "BBB", "R", "X", "validNoteTypes", "n", "name", "r", "m", "s", "g", "d", "x", "ci", "cx", "sf", "sb", "sq", "tu", "td", "accidentals", "accidentalSharp", "accidentalDoubleSharp", "<PERSON><PERSON><PERSON>", "bb", "accidentalDoubleFlat", "accidentalNatural", "accidentalParensLeft", "accidentalParensRight", "db", "accidentalThreeQuarterTonesFlatZimmermann", "accidentalQuarterToneFlatStein", "accidentalThreeQuarterTonesSharpStein", "accidentalQuarterToneSharpStein", "accidentalKucukMucennebSharp", "bs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bss", "accidentalBuyukMucennebFlat", "o", "accidental<PERSON>ori", "k", "accidentalKoron", "bbs", "accidentalBuyukMucennebSharp", "ashs", "afhf", "accidentalColumns", "a", "secondOnBottom", "spacedOutTetrachord", "spacedOutPentachord", "verySpacedOutPentachord", "spacedOutHexachord", "verySpacedOutHexachord", "articulations", "code", "augmentationDot", "betweenLines", "av", "aboveCode", "articStaccatissimoAbove", "belowCode", "articStaccatissimoBelow", "articAccentAbove", "articAccentBelow", "articTenutoAbove", "artic<PERSON>enutoBelow", "articMarcatoAbove", "articMarcatoBelow", "pluckedLeftHandPizzicato", "ao", "pluckedSnapPizzicatoAbove", "pluckedSnapPizzicatoBelow", "ah", "stringsHarmonic", "fermataAbove", "fer<PERSON><PERSON><PERSON><PERSON>", "fermataShortAbove", "fermataShortBelow", "fermataLongAbove", "fermataLongBelow", "fermataVeryLongAbove", "fermataVeryLongBelow", "stringsUpBow", "am", "stringsDownBow", "pictChokeCymbal", "ornaments", "mordent", "ornamentShortTrill", "mordentInverted", "ornamentMordent", "turn", "ornamentTurn", "turnInverted", "ornamentTurnSlash", "tr", "ornamentTrill", "<PERSON>prall", "ornamentPrecompSlideTrillDAnglebert", "downprall", "ornamentPrecompDoubleCadenceUpperPrefix", "p<PERSON>lu<PERSON>", "ornamentPrecompTrillSuffixDandrieu", "pralldown", "ornamentPrecompTrillLowerSuffix", "upmordent", "ornamentPrecompSlideTrillBach", "downmordent", "ornamentPrecompDoubleCadenceUpperPrefixTurn", "lineprall", "ornamentPrecompAppoggTrill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ornamentTremblement", "scoop", "brassScoop", "doit", "brassDoitMedium", "fall", "brassFallLipShort", "doitLong", "brassLiftMedium", "fallLong", "brassFallRoughMedium", "bend", "brassBend", "plungerClosed", "brassMuteClosed", "plunger<PERSON>pen", "brassMuteOpen", "flip", "brassFlip", "jazzTurn", "brassJazzTurn", "smear", "brassSmear", "Tables", "clefProperties", "clef", "keyProperties", "keyOctaveGlyph", "type", "params", "options", "octaveShift", "duration", "Object", "assign", "sanitizeDuration", "pieces", "split", "length", "key", "toUpperCase", "value", "octave", "parseInt", "baseIndex", "line", "intValue", "undefined", "glyphName", "codeNoteHead", "displaced", "integerToNote", "integer", "table", "noteValue", "textWidth", "text", "toString", "articulationCodes", "artic", "accidentalCodes", "_a", "ornamentCodes", "ornament", "keySignature", "spec", "keySpec", "accidentalList", "notes", "accList", "i", "push", "getKeySignatures", "hasKeySignature", "durationNumber", "durationToFraction", "parse", "durationToNumber", "durationToTicks", "ticks", "noteheadDiamondWhole", "noteheadDiamondHalf", "noteheadDiamondBlack", "noteheadTriangleUpWhole", "noteheadTriangleUpHalf", "noteheadTriangleUpBlack", "noteheadXWhole", "noteheadXHalf", "noteheadXBlack", "noteheadCircleX", "noteheadSquareWhite", "noteheadSquareBlack", "noteheadMoonBlack", "noteheadTriangleLeftBlack", "noteheadTriangleRightBlack", "noteheadBlack", "noteheadTriangleRoundDownBlack", "noteheadDiamondDoubleWhole", "noteheadXDoubleWhole", "noteheadCircleXDoubleWhole", "noteheadCircleXWhole", "noteheadCircleXHalf", "noteheadCircledDoubleWhole", "noteheadCircledWhole", "noteheadCircledHalf", "noteheadCircledBlack", "noteheadDoubleWholeSquare", "noteheadTriangleUpDoubleWhole", "noteheadTriangleDownDoubleWhole", "noteheadTriangleDownWhole", "noteheadTriangleDownHalf", "noteheadTriangleDownBlack", "noteheadSlashedDoubleWhole1", "noteheadSlashedWhole1", "noteheadSlashedHalf1", "noteheadSlashedBlack1", "noteheadSlashedDoubleWhole2", "noteheadSlashedWhole2", "noteheadSlashedHalf2", "noteheadSlashedBlack2", "restDoubleWhole", "restWhole", "restHalf", "restQuarter", "rest8th", "rest16th", "rest32nd", "rest64th", "rest128th", "noteheadSlashWhiteDoubleWhole", "noteheadSlashWhiteWhole", "noteheadSlashWhiteHalf", "noteheadSlashVerticalEnds", "noteheadDoubleWhole", "noteheadWhole", "noteheadHalf", "null", "UNISON", "SOFTMAX_FACTOR", "STEM_WIDTH", "STEM_HEIGHT", "STAVE_LINE_THICKNESS", "RENDER_PRECISION_PLACES", "durationCodes", "stem", "beamCount", "stemBeamExtension", "codeFlagUp", "flag8thUp", "flag16thUp", "flag32ndUp", "flag64thUp", "flag128thUp", "NOTATION_FONT_SCALE", "TABLATURE_FONT_SCALE", "SLASH_NOTEHEAD_WIDTH", "STAVE_LINE_DISTANCE", "TEXT_HEIGHT_OFFSET_HACK", "accidentalColumnsTable", "unicode", "sharp", "flat", "natural", "triangle", "degrees", "circle", "validTypes", "TIME4_4", "numBeats", "beatValue", "resolution"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tables.js"], "sourcesContent": ["import { Fraction } from './fraction.js';\nimport { Glyphs } from './glyphs.js';\nimport { RuntimeError } from './util.js';\nconst RESOLUTION = 16384;\nconst durations = {\n    '1/2': RESOLUTION * 2,\n    1: RESOLUTION / 1,\n    2: RESOLUTION / 2,\n    4: RESOLUTION / 4,\n    8: RESOLUTION / 8,\n    16: RESOLUTION / 16,\n    32: RESOLUTION / 32,\n    64: RESOLUTION / 64,\n    128: RESOLUTION / 128,\n    256: RESOLUTION / 256,\n};\nconst durationAliases = {\n    w: '1',\n    h: '2',\n    q: '4',\n    b: '256',\n};\nconst keySignatures = {\n    C: { num: 0 },\n    Am: { num: 0 },\n    F: { accidental: 'b', num: 1 },\n    Dm: { accidental: 'b', num: 1 },\n    Bb: { accidental: 'b', num: 2 },\n    Gm: { accidental: 'b', num: 2 },\n    Eb: { accidental: 'b', num: 3 },\n    Cm: { accidental: 'b', num: 3 },\n    Ab: { accidental: 'b', num: 4 },\n    Fm: { accidental: 'b', num: 4 },\n    Db: { accidental: 'b', num: 5 },\n    Bbm: { accidental: 'b', num: 5 },\n    Gb: { accidental: 'b', num: 6 },\n    Ebm: { accidental: 'b', num: 6 },\n    Cb: { accidental: 'b', num: 7 },\n    Abm: { accidental: 'b', num: 7 },\n    G: { accidental: '#', num: 1 },\n    Em: { accidental: '#', num: 1 },\n    D: { accidental: '#', num: 2 },\n    Bm: { accidental: '#', num: 2 },\n    A: { accidental: '#', num: 3 },\n    'F#m': { accidental: '#', num: 3 },\n    E: { accidental: '#', num: 4 },\n    'C#m': { accidental: '#', num: 4 },\n    B: { accidental: '#', num: 5 },\n    'G#m': { accidental: '#', num: 5 },\n    'F#': { accidental: '#', num: 6 },\n    'D#m': { accidental: '#', num: 6 },\n    'C#': { accidental: '#', num: 7 },\n    'A#m': { accidental: '#', num: 7 },\n};\nconst clefs = {\n    treble: { lineShift: 0 },\n    bass: { lineShift: 6 },\n    tenor: { lineShift: 4 },\n    alto: { lineShift: 3 },\n    soprano: { lineShift: 1 },\n    percussion: { lineShift: 0 },\n    'mezzo-soprano': { lineShift: 2 },\n    'baritone-c': { lineShift: 5 },\n    'baritone-f': { lineShift: 5 },\n    subbass: { lineShift: 7 },\n    french: { lineShift: -1 },\n};\nconst notesInfo = {\n    C: { index: 0, intVal: 0 },\n    CN: { index: 0, intVal: 0 },\n    'C#': { index: 0, intVal: 1 },\n    'C##': { index: 0, intVal: 2 },\n    CB: { index: 0, intVal: 11 },\n    CBB: { index: 0, intVal: 10 },\n    D: { index: 1, intVal: 2 },\n    DN: { index: 1, intVal: 2 },\n    'D#': { index: 1, intVal: 3 },\n    'D##': { index: 1, intVal: 4 },\n    DB: { index: 1, intVal: 1 },\n    DBB: { index: 1, intVal: 0 },\n    E: { index: 2, intVal: 4 },\n    EN: { index: 2, intVal: 4 },\n    'E#': { index: 2, intVal: 5 },\n    'E##': { index: 2, intVal: 6 },\n    EB: { index: 2, intVal: 3 },\n    EBB: { index: 2, intVal: 2 },\n    F: { index: 3, intVal: 5 },\n    FN: { index: 3, intVal: 5 },\n    'F#': { index: 3, intVal: 6 },\n    'F##': { index: 3, intVal: 7 },\n    FB: { index: 3, intVal: 4 },\n    FBB: { index: 3, intVal: 3 },\n    G: { index: 4, intVal: 7 },\n    GN: { index: 4, intVal: 7 },\n    'G#': { index: 4, intVal: 8 },\n    'G##': { index: 4, intVal: 9 },\n    GB: { index: 4, intVal: 6 },\n    GBB: { index: 4, intVal: 5 },\n    A: { index: 5, intVal: 9 },\n    AN: { index: 5, intVal: 9 },\n    'A#': { index: 5, intVal: 10 },\n    'A##': { index: 5, intVal: 11 },\n    AB: { index: 5, intVal: 8 },\n    ABB: { index: 5, intVal: 7 },\n    B: { index: 6, intVal: 11 },\n    BN: { index: 6, intVal: 11 },\n    'B#': { index: 6, intVal: 12 },\n    'B##': { index: 6, intVal: 13 },\n    BB: { index: 6, intVal: 10 },\n    BBB: { index: 6, intVal: 9 },\n    R: { index: 6 },\n    X: { index: 6 },\n};\nconst validNoteTypes = {\n    n: { name: 'note' },\n    r: { name: 'rest' },\n    h: { name: 'harmonic' },\n    m: { name: 'muted' },\n    s: { name: 'slash' },\n    g: { name: 'ghost' },\n    d: { name: 'diamond' },\n    x: { name: 'x' },\n    ci: { name: 'circled' },\n    cx: { name: 'circle x' },\n    sf: { name: 'slashed' },\n    sb: { name: 'slashed backward' },\n    sq: { name: 'square' },\n    tu: { name: 'triangle up' },\n    td: { name: 'triangle down' },\n};\nconst accidentals = {\n    '#': Glyphs.accidentalSharp,\n    '##': Glyphs.accidentalDoubleSharp,\n    b: Glyphs.accidentalFlat,\n    bb: Glyphs.accidentalDoubleFlat,\n    n: Glyphs.accidentalNatural,\n    '{': Glyphs.accidentalParensLeft,\n    '}': Glyphs.accidentalParensRight,\n    db: Glyphs.accidentalThreeQuarterTonesFlatZimmermann,\n    d: Glyphs.accidentalQuarterToneFlatStein,\n    '++': Glyphs.accidentalThreeQuarterTonesSharpStein,\n    '+': Glyphs.accidentalQuarterToneSharpStein,\n    '+-': Glyphs.accidentalKucukMucennebSharp,\n    bs: Glyphs.accidentalBakiyeFlat,\n    bss: Glyphs.accidentalBuyukMucennebFlat,\n    o: Glyphs.accidentalSori,\n    k: Glyphs.accidentalKoron,\n    bbs: Glyphs.accidentalBuyukMucennebSharp,\n    '++-': Glyphs.accidentalBuyukMucennebSharp,\n    ashs: Glyphs.accidentalBuyukMucennebSharp,\n    afhf: Glyphs.accidentalBuyukMucennebSharp,\n};\nconst accidentalColumns = {\n    1: {\n        a: [1],\n        b: [1],\n    },\n    2: {\n        a: [1, 2],\n    },\n    3: {\n        a: [1, 3, 2],\n        b: [1, 2, 1],\n        secondOnBottom: [1, 2, 3],\n    },\n    4: {\n        a: [1, 3, 4, 2],\n        b: [1, 2, 3, 1],\n        spacedOutTetrachord: [1, 2, 1, 2],\n    },\n    5: {\n        a: [1, 3, 5, 4, 2],\n        b: [1, 2, 4, 3, 1],\n        spacedOutPentachord: [1, 2, 3, 2, 1],\n        verySpacedOutPentachord: [1, 2, 1, 2, 1],\n    },\n    6: {\n        a: [1, 3, 5, 6, 4, 2],\n        b: [1, 2, 4, 5, 3, 1],\n        spacedOutHexachord: [1, 3, 2, 1, 3, 2],\n        verySpacedOutHexachord: [1, 2, 1, 2, 1, 2],\n    },\n};\nconst articulations = {\n    'a.': { code: Glyphs.augmentationDot, betweenLines: true },\n    av: {\n        aboveCode: Glyphs.articStaccatissimoAbove,\n        belowCode: Glyphs.articStaccatissimoBelow,\n        betweenLines: true,\n    },\n    'a>': {\n        aboveCode: Glyphs.articAccentAbove,\n        belowCode: Glyphs.articAccentBelow,\n        betweenLines: true,\n    },\n    'a-': {\n        aboveCode: Glyphs.articTenutoAbove,\n        belowCode: Glyphs.articTenutoBelow,\n        betweenLines: true,\n    },\n    'a^': {\n        aboveCode: Glyphs.articMarcatoAbove,\n        belowCode: Glyphs.articMarcatoBelow,\n        betweenLines: false,\n    },\n    'a+': { code: Glyphs.pluckedLeftHandPizzicato, betweenLines: false },\n    ao: {\n        aboveCode: Glyphs.pluckedSnapPizzicatoAbove,\n        belowCode: Glyphs.pluckedSnapPizzicatoBelow,\n        betweenLines: false,\n    },\n    ah: { code: Glyphs.stringsHarmonic, betweenLines: false },\n    'a@': { aboveCode: Glyphs.fermataAbove, belowCode: Glyphs.fermataBelow, betweenLines: false },\n    'a@a': { code: Glyphs.fermataAbove, betweenLines: false },\n    'a@u': { code: Glyphs.fermataBelow, betweenLines: false },\n    'a@s': { aboveCode: Glyphs.fermataShortAbove, belowCode: Glyphs.fermataShortBelow, betweenLines: false },\n    'a@as': { code: Glyphs.fermataShortAbove, betweenLines: false },\n    'a@us': { code: Glyphs.fermataShortBelow, betweenLines: false },\n    'a@l': { aboveCode: Glyphs.fermataLongAbove, belowCode: Glyphs.fermataLongBelow, betweenLines: false },\n    'a@al': { code: Glyphs.fermataLongAbove, betweenLines: false },\n    'a@ul': { code: Glyphs.fermataLongBelow, betweenLines: false },\n    'a@vl': {\n        aboveCode: Glyphs.fermataVeryLongAbove,\n        belowCode: Glyphs.fermataVeryLongBelow,\n        betweenLines: false,\n    },\n    'a@avl': { code: Glyphs.fermataVeryLongAbove, betweenLines: false },\n    'a@uvl': { code: Glyphs.fermataVeryLongBelow, betweenLines: false },\n    'a|': { code: Glyphs.stringsUpBow, betweenLines: false },\n    am: { code: Glyphs.stringsDownBow, betweenLines: false },\n    'a,': { code: Glyphs.pictChokeCymbal, betweenLines: false },\n};\nconst ornaments = {\n    mordent: Glyphs.ornamentShortTrill,\n    mordentInverted: Glyphs.ornamentMordent,\n    turn: Glyphs.ornamentTurn,\n    turnInverted: Glyphs.ornamentTurnSlash,\n    tr: Glyphs.ornamentTrill,\n    upprall: Glyphs.ornamentPrecompSlideTrillDAnglebert,\n    downprall: Glyphs.ornamentPrecompDoubleCadenceUpperPrefix,\n    prallup: Glyphs.ornamentPrecompTrillSuffixDandrieu,\n    pralldown: Glyphs.ornamentPrecompTrillLowerSuffix,\n    upmordent: Glyphs.ornamentPrecompSlideTrillBach,\n    downmordent: Glyphs.ornamentPrecompDoubleCadenceUpperPrefixTurn,\n    lineprall: Glyphs.ornamentPrecompAppoggTrill,\n    prallprall: Glyphs.ornamentTremblement,\n    scoop: Glyphs.brassScoop,\n    doit: Glyphs.brassDoitMedium,\n    fall: Glyphs.brassFallLipShort,\n    doitLong: Glyphs.brassLiftMedium,\n    fallLong: Glyphs.brassFallRoughMedium,\n    bend: Glyphs.brassBend,\n    plungerClosed: Glyphs.brassMuteClosed,\n    plungerOpen: Glyphs.brassMuteOpen,\n    flip: Glyphs.brassFlip,\n    jazzTurn: Glyphs.brassJazzTurn,\n    smear: Glyphs.brassSmear,\n};\nexport class Tables {\n    static clefProperties(clef) {\n        if (!clef || !(clef in clefs))\n            throw new RuntimeError('BadArgument', 'Invalid clef: ' + clef);\n        return clefs[clef];\n    }\n    static keyProperties(keyOctaveGlyph, clef = 'treble', type = 'N', params) {\n        let options = { octaveShift: 0, duration: '4' };\n        if (typeof params === 'object') {\n            options = Object.assign(Object.assign({}, options), params);\n        }\n        const duration = Tables.sanitizeDuration(options.duration);\n        const pieces = keyOctaveGlyph.split('/');\n        if (pieces.length < 2) {\n            throw new RuntimeError('BadArguments', `First argument must be note/octave or note/octave/glyph-code: ${keyOctaveGlyph}`);\n        }\n        const key = pieces[0].toUpperCase();\n        type = type.toUpperCase();\n        const value = notesInfo[key];\n        if (!value)\n            throw new RuntimeError('BadArguments', 'Invalid key name: ' + key);\n        let octave = parseInt(pieces[1], 10);\n        octave -= options.octaveShift;\n        const baseIndex = octave * 7 - 4 * 7;\n        let line = (baseIndex + value.index) / 2;\n        line += Tables.clefProperties(clef).lineShift;\n        const intValue = typeof value.intVal !== 'undefined' ? octave * 12 + value.intVal : undefined;\n        let code = '';\n        let glyphName = 'N';\n        if (pieces.length > 2 && pieces[2]) {\n            glyphName = pieces[2].toUpperCase();\n        }\n        else if (type !== 'N') {\n            glyphName = type;\n        }\n        else\n            glyphName = key;\n        code = this.codeNoteHead(glyphName, duration);\n        return {\n            key,\n            octave,\n            line,\n            intValue,\n            code,\n            displaced: false,\n        };\n    }\n    static integerToNote(integer) {\n        if (typeof integer === 'undefined' || integer < 0 || integer > 11) {\n            throw new RuntimeError('BadArguments', `integerToNote() requires an integer in the range [0, 11]: ${integer}`);\n        }\n        const table = {\n            0: 'C',\n            1: 'C#',\n            2: 'D',\n            3: 'D#',\n            4: 'E',\n            5: 'F',\n            6: 'F#',\n            7: 'G',\n            8: 'G#',\n            9: 'A',\n            10: 'A#',\n            11: 'B',\n        };\n        const noteValue = table[integer];\n        if (!noteValue) {\n            throw new RuntimeError('BadArguments', `Unknown note value for integer: ${integer}`);\n        }\n        return noteValue;\n    }\n    static textWidth(text) {\n        return 7 * text.toString().length;\n    }\n    static articulationCodes(artic) {\n        return articulations[artic];\n    }\n    static accidentalCodes(accidental) {\n        var _a;\n        return (_a = accidentals[accidental]) !== null && _a !== void 0 ? _a : accidental;\n    }\n    static ornamentCodes(ornament) {\n        var _a;\n        return (_a = ornaments[ornament]) !== null && _a !== void 0 ? _a : ornament;\n    }\n    static keySignature(spec) {\n        const keySpec = keySignatures[spec];\n        if (!keySpec) {\n            throw new RuntimeError('BadKeySignature', `Bad key signature spec: '${spec}'`);\n        }\n        if (!keySpec.accidental) {\n            return [];\n        }\n        const accidentalList = {\n            b: [2, 0.5, 2.5, 1, 3, 1.5, 3.5],\n            '#': [0, 1.5, -0.5, 1, 2.5, 0.5, 2],\n        };\n        const notes = accidentalList[keySpec.accidental];\n        const accList = [];\n        for (let i = 0; i < keySpec.num; ++i) {\n            const line = notes[i];\n            accList.push({ type: keySpec.accidental, line });\n        }\n        return accList;\n    }\n    static getKeySignatures() {\n        return keySignatures;\n    }\n    static hasKeySignature(spec) {\n        return spec in keySignatures;\n    }\n    static sanitizeDuration(duration) {\n        const durationNumber = durationAliases[duration];\n        if (durationNumber !== undefined) {\n            duration = durationNumber;\n        }\n        if (durations[duration] === undefined) {\n            throw new RuntimeError('BadArguments', `The provided duration is not valid: ${duration}`);\n        }\n        return duration;\n    }\n    static durationToFraction(duration) {\n        return new Fraction().parse(Tables.sanitizeDuration(duration));\n    }\n    static durationToNumber(duration) {\n        return Tables.durationToFraction(duration).value();\n    }\n    static durationToTicks(duration) {\n        duration = Tables.sanitizeDuration(duration);\n        const ticks = durations[duration];\n        if (ticks === undefined) {\n            throw new RuntimeError('InvalidDuration');\n        }\n        return ticks;\n    }\n    static codeNoteHead(type, duration) {\n        switch (type) {\n            case 'D0':\n                return Glyphs.noteheadDiamondWhole;\n            case 'D1':\n                return Glyphs.noteheadDiamondHalf;\n            case 'D2':\n                return Glyphs.noteheadDiamondBlack;\n            case 'D3':\n                return Glyphs.noteheadDiamondBlack;\n            case 'T0':\n                return Glyphs.noteheadTriangleUpWhole;\n            case 'T1':\n                return Glyphs.noteheadTriangleUpHalf;\n            case 'T2':\n                return Glyphs.noteheadTriangleUpBlack;\n            case 'T3':\n                return Glyphs.noteheadTriangleUpBlack;\n            case 'X0':\n                return Glyphs.noteheadXWhole;\n            case 'X1':\n                return Glyphs.noteheadXHalf;\n            case 'X2':\n                return Glyphs.noteheadXBlack;\n            case 'X3':\n                return Glyphs.noteheadCircleX;\n            case 'S1':\n                return Glyphs.noteheadSquareWhite;\n            case 'S2':\n                return Glyphs.noteheadSquareBlack;\n            case 'R1':\n                return Glyphs.noteheadSquareWhite;\n            case 'R2':\n                return Glyphs.noteheadSquareWhite;\n            case 'DO':\n                return Glyphs.noteheadTriangleUpBlack;\n            case 'RE':\n                return Glyphs.noteheadMoonBlack;\n            case 'MI':\n                return Glyphs.noteheadDiamondBlack;\n            case 'FA':\n                return Glyphs.noteheadTriangleLeftBlack;\n            case 'FAUP':\n                return Glyphs.noteheadTriangleRightBlack;\n            case 'SO':\n                return Glyphs.noteheadBlack;\n            case 'LA':\n                return Glyphs.noteheadSquareBlack;\n            case 'TI':\n                return Glyphs.noteheadTriangleRoundDownBlack;\n            case 'DI':\n            case 'H':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadDiamondDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadDiamondWhole;\n                    case '2':\n                        return Glyphs.noteheadDiamondHalf;\n                    default:\n                        return Glyphs.noteheadDiamondBlack;\n                }\n            case 'X':\n            case 'M':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadXDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadXWhole;\n                    case '2':\n                        return Glyphs.noteheadXHalf;\n                    default:\n                        return Glyphs.noteheadXBlack;\n                }\n            case 'CX':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadCircleXDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadCircleXWhole;\n                    case '2':\n                        return Glyphs.noteheadCircleXHalf;\n                    default:\n                        return Glyphs.noteheadCircleX;\n                }\n            case 'CI':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadCircledDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadCircledWhole;\n                    case '2':\n                        return Glyphs.noteheadCircledHalf;\n                    default:\n                        return Glyphs.noteheadCircledBlack;\n                }\n            case 'SQ':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadDoubleWholeSquare;\n                    case '1':\n                        return Glyphs.noteheadSquareWhite;\n                    case '2':\n                        return Glyphs.noteheadSquareWhite;\n                    default:\n                        return Glyphs.noteheadSquareBlack;\n                }\n            case 'TU':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadTriangleUpDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadTriangleUpWhole;\n                    case '2':\n                        return Glyphs.noteheadTriangleUpHalf;\n                    default:\n                        return Glyphs.noteheadTriangleUpBlack;\n                }\n            case 'TD':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadTriangleDownDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadTriangleDownWhole;\n                    case '2':\n                        return Glyphs.noteheadTriangleDownHalf;\n                    default:\n                        return Glyphs.noteheadTriangleDownBlack;\n                }\n            case 'SF':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadSlashedDoubleWhole1;\n                    case '1':\n                        return Glyphs.noteheadSlashedWhole1;\n                    case '2':\n                        return Glyphs.noteheadSlashedHalf1;\n                    default:\n                        return Glyphs.noteheadSlashedBlack1;\n                }\n            case 'SB':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadSlashedDoubleWhole2;\n                    case '1':\n                        return Glyphs.noteheadSlashedWhole2;\n                    case '2':\n                        return Glyphs.noteheadSlashedHalf2;\n                    default:\n                        return Glyphs.noteheadSlashedBlack2;\n                }\n            case 'R':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.restDoubleWhole;\n                    case '1':\n                        return Glyphs.restWhole;\n                    case '2':\n                        return Glyphs.restHalf;\n                    case '4':\n                        return Glyphs.restQuarter;\n                    case '8':\n                        return Glyphs.rest8th;\n                    case '16':\n                        return Glyphs.rest16th;\n                    case '32':\n                        return Glyphs.rest32nd;\n                    case '64':\n                        return Glyphs.rest64th;\n                    case '128':\n                        return Glyphs.rest128th;\n                }\n                break;\n            case 'S':\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadSlashWhiteDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadSlashWhiteWhole;\n                    case '2':\n                        return Glyphs.noteheadSlashWhiteHalf;\n                    default:\n                        return Glyphs.noteheadSlashVerticalEnds;\n                }\n            default:\n                switch (duration) {\n                    case '1/2':\n                        return Glyphs.noteheadDoubleWhole;\n                    case '1':\n                        return Glyphs.noteheadWhole;\n                    case '2':\n                        return Glyphs.noteheadHalf;\n                    default:\n                        return Glyphs.noteheadBlack;\n                }\n        }\n        return Glyphs.null;\n    }\n}\nTables.UNISON = true;\nTables.SOFTMAX_FACTOR = 10;\nTables.STEM_WIDTH = 1.5;\nTables.STEM_HEIGHT = 35;\nTables.STAVE_LINE_THICKNESS = 1;\nTables.RENDER_PRECISION_PLACES = 3;\nTables.RESOLUTION = RESOLUTION;\nTables.durationCodes = {\n    '1/2': {\n        stem: false,\n    },\n    1: {\n        stem: false,\n    },\n    2: {\n        stem: true,\n    },\n    4: {\n        stem: true,\n    },\n    8: {\n        stem: true,\n        beamCount: 1,\n        stemBeamExtension: 0,\n        codeFlagUp: Glyphs.flag8thUp,\n    },\n    16: {\n        beamCount: 2,\n        stemBeamExtension: 0,\n        stem: true,\n        codeFlagUp: Glyphs.flag16thUp,\n    },\n    32: {\n        beamCount: 3,\n        stemBeamExtension: 7.5,\n        stem: true,\n        codeFlagUp: Glyphs.flag32ndUp,\n    },\n    64: {\n        beamCount: 4,\n        stemBeamExtension: 15,\n        stem: true,\n        codeFlagUp: Glyphs.flag64thUp,\n    },\n    128: {\n        beamCount: 5,\n        stemBeamExtension: 22.5,\n        stem: true,\n        codeFlagUp: Glyphs.flag128thUp,\n    },\n};\nTables.NOTATION_FONT_SCALE = 39;\nTables.TABLATURE_FONT_SCALE = 39;\nTables.SLASH_NOTEHEAD_WIDTH = 15;\nTables.STAVE_LINE_DISTANCE = 10;\nTables.TEXT_HEIGHT_OFFSET_HACK = 1;\nTables.accidentalColumnsTable = accidentalColumns;\nTables.unicode = {\n    sharp: '\\u266f',\n    flat: '\\u266d',\n    natural: '\\u266e',\n    triangle: '\\u25b3',\n    'o-with-slash': '\\u00f8',\n    degrees: '\\u00b0',\n    circle: '\\u25cb',\n};\nTables.validTypes = validNoteTypes;\nTables.TIME4_4 = {\n    numBeats: 4,\n    beatValue: 4,\n    resolution: RESOLUTION,\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,MAAMC,UAAU,GAAG,KAAK;AACxB,MAAMC,SAAS,GAAG;EACd,KAAK,EAAED,UAAU,GAAG,CAAC;EACrB,CAAC,EAAEA,UAAU,GAAG,CAAC;EACjB,CAAC,EAAEA,UAAU,GAAG,CAAC;EACjB,CAAC,EAAEA,UAAU,GAAG,CAAC;EACjB,CAAC,EAAEA,UAAU,GAAG,CAAC;EACjB,EAAE,EAAEA,UAAU,GAAG,EAAE;EACnB,EAAE,EAAEA,UAAU,GAAG,EAAE;EACnB,EAAE,EAAEA,UAAU,GAAG,EAAE;EACnB,GAAG,EAAEA,UAAU,GAAG,GAAG;EACrB,GAAG,EAAEA,UAAU,GAAG;AACtB,CAAC;AACD,MAAME,eAAe,GAAG;EACpBC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE;AACP,CAAC;AACD,MAAMC,aAAa,GAAG;EAClBC,CAAC,EAAE;IAAEC,GAAG,EAAE;EAAE,CAAC;EACbC,EAAE,EAAE;IAAED,GAAG,EAAE;EAAE,CAAC;EACdE,CAAC,EAAE;IAAEC,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9BI,EAAE,EAAE;IAAED,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BK,EAAE,EAAE;IAAEF,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BM,EAAE,EAAE;IAAEH,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BO,EAAE,EAAE;IAAEJ,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BQ,EAAE,EAAE;IAAEL,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BS,EAAE,EAAE;IAAEN,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BU,EAAE,EAAE;IAAEP,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BW,EAAE,EAAE;IAAER,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BY,GAAG,EAAE;IAAET,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAChCa,EAAE,EAAE;IAAEV,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/Bc,GAAG,EAAE;IAAEX,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAChCe,EAAE,EAAE;IAAEZ,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BgB,GAAG,EAAE;IAAEb,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAChCiB,CAAC,EAAE;IAAEd,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9BkB,EAAE,EAAE;IAAEf,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BmB,CAAC,EAAE;IAAEhB,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9BoB,EAAE,EAAE;IAAEjB,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC/BqB,CAAC,EAAE;IAAElB,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9B,KAAK,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAClCsB,CAAC,EAAE;IAAEnB,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9B,KAAK,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAClCuB,CAAC,EAAE;IAAEpB,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAC9B,KAAK,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAClC,IAAI,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EACjC,KAAK,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EAClC,IAAI,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE,CAAC;EACjC,KAAK,EAAE;IAAEG,UAAU,EAAE,GAAG;IAAEH,GAAG,EAAE;EAAE;AACrC,CAAC;AACD,MAAMwB,KAAK,GAAG;EACVC,MAAM,EAAE;IAAEC,SAAS,EAAE;EAAE,CAAC;EACxBC,IAAI,EAAE;IAAED,SAAS,EAAE;EAAE,CAAC;EACtBE,KAAK,EAAE;IAAEF,SAAS,EAAE;EAAE,CAAC;EACvBG,IAAI,EAAE;IAAEH,SAAS,EAAE;EAAE,CAAC;EACtBI,OAAO,EAAE;IAAEJ,SAAS,EAAE;EAAE,CAAC;EACzBK,UAAU,EAAE;IAAEL,SAAS,EAAE;EAAE,CAAC;EAC5B,eAAe,EAAE;IAAEA,SAAS,EAAE;EAAE,CAAC;EACjC,YAAY,EAAE;IAAEA,SAAS,EAAE;EAAE,CAAC;EAC9B,YAAY,EAAE;IAAEA,SAAS,EAAE;EAAE,CAAC;EAC9BM,OAAO,EAAE;IAAEN,SAAS,EAAE;EAAE,CAAC;EACzBO,MAAM,EAAE;IAAEP,SAAS,EAAE,CAAC;EAAE;AAC5B,CAAC;AACD,MAAMQ,SAAS,GAAG;EACdnC,CAAC,EAAE;IAAEoC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1BC,EAAE,EAAE;IAAEF,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC7B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC9BE,EAAE,EAAE;IAAEH,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC5BG,GAAG,EAAE;IAAEJ,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC7BjB,CAAC,EAAE;IAAEgB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1BI,EAAE,EAAE;IAAEL,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC7B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC9BK,EAAE,EAAE;IAAEN,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3BM,GAAG,EAAE;IAAEP,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5Bd,CAAC,EAAE;IAAEa,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1BO,EAAE,EAAE;IAAER,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC7B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC9BQ,EAAE,EAAE;IAAET,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3BS,GAAG,EAAE;IAAEV,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5BlC,CAAC,EAAE;IAAEiC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1BU,EAAE,EAAE;IAAEX,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC7B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC9BW,EAAE,EAAE;IAAEZ,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3BY,GAAG,EAAE;IAAEb,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5BnB,CAAC,EAAE;IAAEkB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1Ba,EAAE,EAAE;IAAEd,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC7B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC9Bc,EAAE,EAAE;IAAEf,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3Be,GAAG,EAAE;IAAEhB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5Bf,CAAC,EAAE;IAAEc,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC1BgB,EAAE,EAAE;IAAEjB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC9B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC/BiB,EAAE,EAAE;IAAElB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC3BkB,GAAG,EAAE;IAAEnB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5Bb,CAAC,EAAE;IAAEY,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC3BmB,EAAE,EAAE;IAAEpB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC5B,IAAI,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC9B,KAAK,EAAE;IAAED,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC/BoB,EAAE,EAAE;IAAErB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAG,CAAC;EAC5BqB,GAAG,EAAE;IAAEtB,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC;EAC5BsB,CAAC,EAAE;IAAEvB,KAAK,EAAE;EAAE,CAAC;EACfwB,CAAC,EAAE;IAAExB,KAAK,EAAE;EAAE;AAClB,CAAC;AACD,MAAMyB,cAAc,GAAG;EACnBC,CAAC,EAAE;IAAEC,IAAI,EAAE;EAAO,CAAC;EACnBC,CAAC,EAAE;IAAED,IAAI,EAAE;EAAO,CAAC;EACnBnE,CAAC,EAAE;IAAEmE,IAAI,EAAE;EAAW,CAAC;EACvBE,CAAC,EAAE;IAAEF,IAAI,EAAE;EAAQ,CAAC;EACpBG,CAAC,EAAE;IAAEH,IAAI,EAAE;EAAQ,CAAC;EACpBI,CAAC,EAAE;IAAEJ,IAAI,EAAE;EAAQ,CAAC;EACpBK,CAAC,EAAE;IAAEL,IAAI,EAAE;EAAU,CAAC;EACtBM,CAAC,EAAE;IAAEN,IAAI,EAAE;EAAI,CAAC;EAChBO,EAAE,EAAE;IAAEP,IAAI,EAAE;EAAU,CAAC;EACvBQ,EAAE,EAAE;IAAER,IAAI,EAAE;EAAW,CAAC;EACxBS,EAAE,EAAE;IAAET,IAAI,EAAE;EAAU,CAAC;EACvBU,EAAE,EAAE;IAAEV,IAAI,EAAE;EAAmB,CAAC;EAChCW,EAAE,EAAE;IAAEX,IAAI,EAAE;EAAS,CAAC;EACtBY,EAAE,EAAE;IAAEZ,IAAI,EAAE;EAAc,CAAC;EAC3Ba,EAAE,EAAE;IAAEb,IAAI,EAAE;EAAgB;AAChC,CAAC;AACD,MAAMc,WAAW,GAAG;EAChB,GAAG,EAAEvF,MAAM,CAACwF,eAAe;EAC3B,IAAI,EAAExF,MAAM,CAACyF,qBAAqB;EAClCjF,CAAC,EAAER,MAAM,CAAC0F,cAAc;EACxBC,EAAE,EAAE3F,MAAM,CAAC4F,oBAAoB;EAC/BpB,CAAC,EAAExE,MAAM,CAAC6F,iBAAiB;EAC3B,GAAG,EAAE7F,MAAM,CAAC8F,oBAAoB;EAChC,GAAG,EAAE9F,MAAM,CAAC+F,qBAAqB;EACjCC,EAAE,EAAEhG,MAAM,CAACiG,yCAAyC;EACpDnB,CAAC,EAAE9E,MAAM,CAACkG,8BAA8B;EACxC,IAAI,EAAElG,MAAM,CAACmG,qCAAqC;EAClD,GAAG,EAAEnG,MAAM,CAACoG,+BAA+B;EAC3C,IAAI,EAAEpG,MAAM,CAACqG,4BAA4B;EACzCC,EAAE,EAAEtG,MAAM,CAACuG,oBAAoB;EAC/BC,GAAG,EAAExG,MAAM,CAACyG,2BAA2B;EACvCC,CAAC,EAAE1G,MAAM,CAAC2G,cAAc;EACxBC,CAAC,EAAE5G,MAAM,CAAC6G,eAAe;EACzBC,GAAG,EAAE9G,MAAM,CAAC+G,4BAA4B;EACxC,KAAK,EAAE/G,MAAM,CAAC+G,4BAA4B;EAC1CC,IAAI,EAAEhH,MAAM,CAAC+G,4BAA4B;EACzCE,IAAI,EAAEjH,MAAM,CAAC+G;AACjB,CAAC;AACD,MAAMG,iBAAiB,GAAG;EACtB,CAAC,EAAE;IACCC,CAAC,EAAE,CAAC,CAAC,CAAC;IACN3G,CAAC,EAAE,CAAC,CAAC;EACT,CAAC;EACD,CAAC,EAAE;IACC2G,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;EACZ,CAAC;EACD,CAAC,EAAE;IACCA,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACZ3G,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACZ4G,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAC5B,CAAC;EACD,CAAC,EAAE;IACCD,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACf3G,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACf6G,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EACpC,CAAC;EACD,CAAC,EAAE;IACCF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClB3G,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAClB8G,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACpCC,uBAAuB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EAC3C,CAAC;EACD,CAAC,EAAE;IACCJ,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrB3G,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACrBgH,kBAAkB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCC,sBAAsB,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;EAC7C;AACJ,CAAC;AACD,MAAMC,aAAa,GAAG;EAClB,IAAI,EAAE;IAAEC,IAAI,EAAE3H,MAAM,CAAC4H,eAAe;IAAEC,YAAY,EAAE;EAAK,CAAC;EAC1DC,EAAE,EAAE;IACAC,SAAS,EAAE/H,MAAM,CAACgI,uBAAuB;IACzCC,SAAS,EAAEjI,MAAM,CAACkI,uBAAuB;IACzCL,YAAY,EAAE;EAClB,CAAC;EACD,IAAI,EAAE;IACFE,SAAS,EAAE/H,MAAM,CAACmI,gBAAgB;IAClCF,SAAS,EAAEjI,MAAM,CAACoI,gBAAgB;IAClCP,YAAY,EAAE;EAClB,CAAC;EACD,IAAI,EAAE;IACFE,SAAS,EAAE/H,MAAM,CAACqI,gBAAgB;IAClCJ,SAAS,EAAEjI,MAAM,CAACsI,gBAAgB;IAClCT,YAAY,EAAE;EAClB,CAAC;EACD,IAAI,EAAE;IACFE,SAAS,EAAE/H,MAAM,CAACuI,iBAAiB;IACnCN,SAAS,EAAEjI,MAAM,CAACwI,iBAAiB;IACnCX,YAAY,EAAE;EAClB,CAAC;EACD,IAAI,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACyI,wBAAwB;IAAEZ,YAAY,EAAE;EAAM,CAAC;EACpEa,EAAE,EAAE;IACAX,SAAS,EAAE/H,MAAM,CAAC2I,yBAAyB;IAC3CV,SAAS,EAAEjI,MAAM,CAAC4I,yBAAyB;IAC3Cf,YAAY,EAAE;EAClB,CAAC;EACDgB,EAAE,EAAE;IAAElB,IAAI,EAAE3H,MAAM,CAAC8I,eAAe;IAAEjB,YAAY,EAAE;EAAM,CAAC;EACzD,IAAI,EAAE;IAAEE,SAAS,EAAE/H,MAAM,CAAC+I,YAAY;IAAEd,SAAS,EAAEjI,MAAM,CAACgJ,YAAY;IAAEnB,YAAY,EAAE;EAAM,CAAC;EAC7F,KAAK,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAAC+I,YAAY;IAAElB,YAAY,EAAE;EAAM,CAAC;EACzD,KAAK,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACgJ,YAAY;IAAEnB,YAAY,EAAE;EAAM,CAAC;EACzD,KAAK,EAAE;IAAEE,SAAS,EAAE/H,MAAM,CAACiJ,iBAAiB;IAAEhB,SAAS,EAAEjI,MAAM,CAACkJ,iBAAiB;IAAErB,YAAY,EAAE;EAAM,CAAC;EACxG,MAAM,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACiJ,iBAAiB;IAAEpB,YAAY,EAAE;EAAM,CAAC;EAC/D,MAAM,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACkJ,iBAAiB;IAAErB,YAAY,EAAE;EAAM,CAAC;EAC/D,KAAK,EAAE;IAAEE,SAAS,EAAE/H,MAAM,CAACmJ,gBAAgB;IAAElB,SAAS,EAAEjI,MAAM,CAACoJ,gBAAgB;IAAEvB,YAAY,EAAE;EAAM,CAAC;EACtG,MAAM,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACmJ,gBAAgB;IAAEtB,YAAY,EAAE;EAAM,CAAC;EAC9D,MAAM,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACoJ,gBAAgB;IAAEvB,YAAY,EAAE;EAAM,CAAC;EAC9D,MAAM,EAAE;IACJE,SAAS,EAAE/H,MAAM,CAACqJ,oBAAoB;IACtCpB,SAAS,EAAEjI,MAAM,CAACsJ,oBAAoB;IACtCzB,YAAY,EAAE;EAClB,CAAC;EACD,OAAO,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACqJ,oBAAoB;IAAExB,YAAY,EAAE;EAAM,CAAC;EACnE,OAAO,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACsJ,oBAAoB;IAAEzB,YAAY,EAAE;EAAM,CAAC;EACnE,IAAI,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAACuJ,YAAY;IAAE1B,YAAY,EAAE;EAAM,CAAC;EACxD2B,EAAE,EAAE;IAAE7B,IAAI,EAAE3H,MAAM,CAACyJ,cAAc;IAAE5B,YAAY,EAAE;EAAM,CAAC;EACxD,IAAI,EAAE;IAAEF,IAAI,EAAE3H,MAAM,CAAC0J,eAAe;IAAE7B,YAAY,EAAE;EAAM;AAC9D,CAAC;AACD,MAAM8B,SAAS,GAAG;EACdC,OAAO,EAAE5J,MAAM,CAAC6J,kBAAkB;EAClCC,eAAe,EAAE9J,MAAM,CAAC+J,eAAe;EACvCC,IAAI,EAAEhK,MAAM,CAACiK,YAAY;EACzBC,YAAY,EAAElK,MAAM,CAACmK,iBAAiB;EACtCC,EAAE,EAAEpK,MAAM,CAACqK,aAAa;EACxBC,OAAO,EAAEtK,MAAM,CAACuK,mCAAmC;EACnDC,SAAS,EAAExK,MAAM,CAACyK,uCAAuC;EACzDC,OAAO,EAAE1K,MAAM,CAAC2K,kCAAkC;EAClDC,SAAS,EAAE5K,MAAM,CAAC6K,+BAA+B;EACjDC,SAAS,EAAE9K,MAAM,CAAC+K,6BAA6B;EAC/CC,WAAW,EAAEhL,MAAM,CAACiL,2CAA2C;EAC/DC,SAAS,EAAElL,MAAM,CAACmL,0BAA0B;EAC5CC,UAAU,EAAEpL,MAAM,CAACqL,mBAAmB;EACtCC,KAAK,EAAEtL,MAAM,CAACuL,UAAU;EACxBC,IAAI,EAAExL,MAAM,CAACyL,eAAe;EAC5BC,IAAI,EAAE1L,MAAM,CAAC2L,iBAAiB;EAC9BC,QAAQ,EAAE5L,MAAM,CAAC6L,eAAe;EAChCC,QAAQ,EAAE9L,MAAM,CAAC+L,oBAAoB;EACrCC,IAAI,EAAEhM,MAAM,CAACiM,SAAS;EACtBC,aAAa,EAAElM,MAAM,CAACmM,eAAe;EACrCC,WAAW,EAAEpM,MAAM,CAACqM,aAAa;EACjCC,IAAI,EAAEtM,MAAM,CAACuM,SAAS;EACtBC,QAAQ,EAAExM,MAAM,CAACyM,aAAa;EAC9BC,KAAK,EAAE1M,MAAM,CAAC2M;AAClB,CAAC;AACD,OAAO,MAAMC,MAAM,CAAC;EAChB,OAAOC,cAAcA,CAACC,IAAI,EAAE;IACxB,IAAI,CAACA,IAAI,IAAI,EAAEA,IAAI,IAAI3K,KAAK,CAAC,EACzB,MAAM,IAAIlC,YAAY,CAAC,aAAa,EAAE,gBAAgB,GAAG6M,IAAI,CAAC;IAClE,OAAO3K,KAAK,CAAC2K,IAAI,CAAC;EACtB;EACA,OAAOC,aAAaA,CAACC,cAAc,EAAEF,IAAI,GAAG,QAAQ,EAAEG,IAAI,GAAG,GAAG,EAAEC,MAAM,EAAE;IACtE,IAAIC,OAAO,GAAG;MAAEC,WAAW,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC;IAC/C,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;MAC5BC,OAAO,GAAGG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAED,MAAM,CAAC;IAC/D;IACA,MAAMG,QAAQ,GAAGT,MAAM,CAACY,gBAAgB,CAACL,OAAO,CAACE,QAAQ,CAAC;IAC1D,MAAMI,MAAM,GAAGT,cAAc,CAACU,KAAK,CAAC,GAAG,CAAC;IACxC,IAAID,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;MACnB,MAAM,IAAI1N,YAAY,CAAC,cAAc,EAAE,iEAAiE+M,cAAc,EAAE,CAAC;IAC7H;IACA,MAAMY,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;IACnCZ,IAAI,GAAGA,IAAI,CAACY,WAAW,CAAC,CAAC;IACzB,MAAMC,KAAK,GAAGjL,SAAS,CAAC+K,GAAG,CAAC;IAC5B,IAAI,CAACE,KAAK,EACN,MAAM,IAAI7N,YAAY,CAAC,cAAc,EAAE,oBAAoB,GAAG2N,GAAG,CAAC;IACtE,IAAIG,MAAM,GAAGC,QAAQ,CAACP,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpCM,MAAM,IAAIZ,OAAO,CAACC,WAAW;IAC7B,MAAMa,SAAS,GAAGF,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,IAAIG,IAAI,GAAG,CAACD,SAAS,GAAGH,KAAK,CAAChL,KAAK,IAAI,CAAC;IACxCoL,IAAI,IAAItB,MAAM,CAACC,cAAc,CAACC,IAAI,CAAC,CAACzK,SAAS;IAC7C,MAAM8L,QAAQ,GAAG,OAAOL,KAAK,CAAC/K,MAAM,KAAK,WAAW,GAAGgL,MAAM,GAAG,EAAE,GAAGD,KAAK,CAAC/K,MAAM,GAAGqL,SAAS;IAC7F,IAAIzG,IAAI,GAAG,EAAE;IACb,IAAI0G,SAAS,GAAG,GAAG;IACnB,IAAIZ,MAAM,CAACE,MAAM,GAAG,CAAC,IAAIF,MAAM,CAAC,CAAC,CAAC,EAAE;MAChCY,SAAS,GAAGZ,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;IACvC,CAAC,MACI,IAAIZ,IAAI,KAAK,GAAG,EAAE;MACnBoB,SAAS,GAAGpB,IAAI;IACpB,CAAC,MAEGoB,SAAS,GAAGT,GAAG;IACnBjG,IAAI,GAAG,IAAI,CAAC2G,YAAY,CAACD,SAAS,EAAEhB,QAAQ,CAAC;IAC7C,OAAO;MACHO,GAAG;MACHG,MAAM;MACNG,IAAI;MACJC,QAAQ;MACRxG,IAAI;MACJ4G,SAAS,EAAE;IACf,CAAC;EACL;EACA,OAAOC,aAAaA,CAACC,OAAO,EAAE;IAC1B,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,EAAE,EAAE;MAC/D,MAAM,IAAIxO,YAAY,CAAC,cAAc,EAAE,6DAA6DwO,OAAO,EAAE,CAAC;IAClH;IACA,MAAMC,KAAK,GAAG;MACV,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,IAAI;MACP,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,IAAI;MACP,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,IAAI;MACP,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,IAAI;MACP,CAAC,EAAE,GAAG;MACN,EAAE,EAAE,IAAI;MACR,EAAE,EAAE;IACR,CAAC;IACD,MAAMC,SAAS,GAAGD,KAAK,CAACD,OAAO,CAAC;IAChC,IAAI,CAACE,SAAS,EAAE;MACZ,MAAM,IAAI1O,YAAY,CAAC,cAAc,EAAE,mCAAmCwO,OAAO,EAAE,CAAC;IACxF;IACA,OAAOE,SAAS;EACpB;EACA,OAAOC,SAASA,CAACC,IAAI,EAAE;IACnB,OAAO,CAAC,GAAGA,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACnB,MAAM;EACrC;EACA,OAAOoB,iBAAiBA,CAACC,KAAK,EAAE;IAC5B,OAAOtH,aAAa,CAACsH,KAAK,CAAC;EAC/B;EACA,OAAOC,eAAeA,CAACnO,UAAU,EAAE;IAC/B,IAAIoO,EAAE;IACN,OAAO,CAACA,EAAE,GAAG3J,WAAW,CAACzE,UAAU,CAAC,MAAM,IAAI,IAAIoO,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGpO,UAAU;EACrF;EACA,OAAOqO,aAAaA,CAACC,QAAQ,EAAE;IAC3B,IAAIF,EAAE;IACN,OAAO,CAACA,EAAE,GAAGvF,SAAS,CAACyF,QAAQ,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,QAAQ;EAC/E;EACA,OAAOC,YAAYA,CAACC,IAAI,EAAE;IACtB,MAAMC,OAAO,GAAG9O,aAAa,CAAC6O,IAAI,CAAC;IACnC,IAAI,CAACC,OAAO,EAAE;MACV,MAAM,IAAItP,YAAY,CAAC,iBAAiB,EAAE,4BAA4BqP,IAAI,GAAG,CAAC;IAClF;IACA,IAAI,CAACC,OAAO,CAACzO,UAAU,EAAE;MACrB,OAAO,EAAE;IACb;IACA,MAAM0O,cAAc,GAAG;MACnBhP,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,GAAG,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACtC,CAAC;IACD,MAAMiP,KAAK,GAAGD,cAAc,CAACD,OAAO,CAACzO,UAAU,CAAC;IAChD,MAAM4O,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAAC5O,GAAG,EAAE,EAAEgP,CAAC,EAAE;MAClC,MAAMzB,IAAI,GAAGuB,KAAK,CAACE,CAAC,CAAC;MACrBD,OAAO,CAACE,IAAI,CAAC;QAAE3C,IAAI,EAAEsC,OAAO,CAACzO,UAAU;QAAEoN;MAAK,CAAC,CAAC;IACpD;IACA,OAAOwB,OAAO;EAClB;EACA,OAAOG,gBAAgBA,CAAA,EAAG;IACtB,OAAOpP,aAAa;EACxB;EACA,OAAOqP,eAAeA,CAACR,IAAI,EAAE;IACzB,OAAOA,IAAI,IAAI7O,aAAa;EAChC;EACA,OAAO+M,gBAAgBA,CAACH,QAAQ,EAAE;IAC9B,MAAM0C,cAAc,GAAG3P,eAAe,CAACiN,QAAQ,CAAC;IAChD,IAAI0C,cAAc,KAAK3B,SAAS,EAAE;MAC9Bf,QAAQ,GAAG0C,cAAc;IAC7B;IACA,IAAI5P,SAAS,CAACkN,QAAQ,CAAC,KAAKe,SAAS,EAAE;MACnC,MAAM,IAAInO,YAAY,CAAC,cAAc,EAAE,uCAAuCoN,QAAQ,EAAE,CAAC;IAC7F;IACA,OAAOA,QAAQ;EACnB;EACA,OAAO2C,kBAAkBA,CAAC3C,QAAQ,EAAE;IAChC,OAAO,IAAItN,QAAQ,CAAC,CAAC,CAACkQ,KAAK,CAACrD,MAAM,CAACY,gBAAgB,CAACH,QAAQ,CAAC,CAAC;EAClE;EACA,OAAO6C,gBAAgBA,CAAC7C,QAAQ,EAAE;IAC9B,OAAOT,MAAM,CAACoD,kBAAkB,CAAC3C,QAAQ,CAAC,CAACS,KAAK,CAAC,CAAC;EACtD;EACA,OAAOqC,eAAeA,CAAC9C,QAAQ,EAAE;IAC7BA,QAAQ,GAAGT,MAAM,CAACY,gBAAgB,CAACH,QAAQ,CAAC;IAC5C,MAAM+C,KAAK,GAAGjQ,SAAS,CAACkN,QAAQ,CAAC;IACjC,IAAI+C,KAAK,KAAKhC,SAAS,EAAE;MACrB,MAAM,IAAInO,YAAY,CAAC,iBAAiB,CAAC;IAC7C;IACA,OAAOmQ,KAAK;EAChB;EACA,OAAO9B,YAAYA,CAACrB,IAAI,EAAEI,QAAQ,EAAE;IAChC,QAAQJ,IAAI;MACR,KAAK,IAAI;QACL,OAAOjN,MAAM,CAACqQ,oBAAoB;MACtC,KAAK,IAAI;QACL,OAAOrQ,MAAM,CAACsQ,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAOtQ,MAAM,CAACuQ,oBAAoB;MACtC,KAAK,IAAI;QACL,OAAOvQ,MAAM,CAACuQ,oBAAoB;MACtC,KAAK,IAAI;QACL,OAAOvQ,MAAM,CAACwQ,uBAAuB;MACzC,KAAK,IAAI;QACL,OAAOxQ,MAAM,CAACyQ,sBAAsB;MACxC,KAAK,IAAI;QACL,OAAOzQ,MAAM,CAAC0Q,uBAAuB;MACzC,KAAK,IAAI;QACL,OAAO1Q,MAAM,CAAC0Q,uBAAuB;MACzC,KAAK,IAAI;QACL,OAAO1Q,MAAM,CAAC2Q,cAAc;MAChC,KAAK,IAAI;QACL,OAAO3Q,MAAM,CAAC4Q,aAAa;MAC/B,KAAK,IAAI;QACL,OAAO5Q,MAAM,CAAC6Q,cAAc;MAChC,KAAK,IAAI;QACL,OAAO7Q,MAAM,CAAC8Q,eAAe;MACjC,KAAK,IAAI;QACL,OAAO9Q,MAAM,CAAC+Q,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAO/Q,MAAM,CAACgR,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAOhR,MAAM,CAAC+Q,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAO/Q,MAAM,CAAC+Q,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAO/Q,MAAM,CAAC0Q,uBAAuB;MACzC,KAAK,IAAI;QACL,OAAO1Q,MAAM,CAACiR,iBAAiB;MACnC,KAAK,IAAI;QACL,OAAOjR,MAAM,CAACuQ,oBAAoB;MACtC,KAAK,IAAI;QACL,OAAOvQ,MAAM,CAACkR,yBAAyB;MAC3C,KAAK,MAAM;QACP,OAAOlR,MAAM,CAACmR,0BAA0B;MAC5C,KAAK,IAAI;QACL,OAAOnR,MAAM,CAACoR,aAAa;MAC/B,KAAK,IAAI;QACL,OAAOpR,MAAM,CAACgR,mBAAmB;MACrC,KAAK,IAAI;QACL,OAAOhR,MAAM,CAACqR,8BAA8B;MAChD,KAAK,IAAI;MACT,KAAK,GAAG;QACJ,QAAQhE,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACsR,0BAA0B;UAC5C,KAAK,GAAG;YACJ,OAAOtR,MAAM,CAACqQ,oBAAoB;UACtC,KAAK,GAAG;YACJ,OAAOrQ,MAAM,CAACsQ,mBAAmB;UACrC;YACI,OAAOtQ,MAAM,CAACuQ,oBAAoB;QAC1C;MACJ,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,QAAQlD,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACuR,oBAAoB;UACtC,KAAK,GAAG;YACJ,OAAOvR,MAAM,CAAC2Q,cAAc;UAChC,KAAK,GAAG;YACJ,OAAO3Q,MAAM,CAAC4Q,aAAa;UAC/B;YACI,OAAO5Q,MAAM,CAAC6Q,cAAc;QACpC;MACJ,KAAK,IAAI;QACL,QAAQxD,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACwR,0BAA0B;UAC5C,KAAK,GAAG;YACJ,OAAOxR,MAAM,CAACyR,oBAAoB;UACtC,KAAK,GAAG;YACJ,OAAOzR,MAAM,CAAC0R,mBAAmB;UACrC;YACI,OAAO1R,MAAM,CAAC8Q,eAAe;QACrC;MACJ,KAAK,IAAI;QACL,QAAQzD,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAAC2R,0BAA0B;UAC5C,KAAK,GAAG;YACJ,OAAO3R,MAAM,CAAC4R,oBAAoB;UACtC,KAAK,GAAG;YACJ,OAAO5R,MAAM,CAAC6R,mBAAmB;UACrC;YACI,OAAO7R,MAAM,CAAC8R,oBAAoB;QAC1C;MACJ,KAAK,IAAI;QACL,QAAQzE,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAAC+R,yBAAyB;UAC3C,KAAK,GAAG;YACJ,OAAO/R,MAAM,CAAC+Q,mBAAmB;UACrC,KAAK,GAAG;YACJ,OAAO/Q,MAAM,CAAC+Q,mBAAmB;UACrC;YACI,OAAO/Q,MAAM,CAACgR,mBAAmB;QACzC;MACJ,KAAK,IAAI;QACL,QAAQ3D,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACgS,6BAA6B;UAC/C,KAAK,GAAG;YACJ,OAAOhS,MAAM,CAACwQ,uBAAuB;UACzC,KAAK,GAAG;YACJ,OAAOxQ,MAAM,CAACyQ,sBAAsB;UACxC;YACI,OAAOzQ,MAAM,CAAC0Q,uBAAuB;QAC7C;MACJ,KAAK,IAAI;QACL,QAAQrD,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACiS,+BAA+B;UACjD,KAAK,GAAG;YACJ,OAAOjS,MAAM,CAACkS,yBAAyB;UAC3C,KAAK,GAAG;YACJ,OAAOlS,MAAM,CAACmS,wBAAwB;UAC1C;YACI,OAAOnS,MAAM,CAACoS,yBAAyB;QAC/C;MACJ,KAAK,IAAI;QACL,QAAQ/E,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACqS,2BAA2B;UAC7C,KAAK,GAAG;YACJ,OAAOrS,MAAM,CAACsS,qBAAqB;UACvC,KAAK,GAAG;YACJ,OAAOtS,MAAM,CAACuS,oBAAoB;UACtC;YACI,OAAOvS,MAAM,CAACwS,qBAAqB;QAC3C;MACJ,KAAK,IAAI;QACL,QAAQnF,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACyS,2BAA2B;UAC7C,KAAK,GAAG;YACJ,OAAOzS,MAAM,CAAC0S,qBAAqB;UACvC,KAAK,GAAG;YACJ,OAAO1S,MAAM,CAAC2S,oBAAoB;UACtC;YACI,OAAO3S,MAAM,CAAC4S,qBAAqB;QAC3C;MACJ,KAAK,GAAG;QACJ,QAAQvF,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAAC6S,eAAe;UACjC,KAAK,GAAG;YACJ,OAAO7S,MAAM,CAAC8S,SAAS;UAC3B,KAAK,GAAG;YACJ,OAAO9S,MAAM,CAAC+S,QAAQ;UAC1B,KAAK,GAAG;YACJ,OAAO/S,MAAM,CAACgT,WAAW;UAC7B,KAAK,GAAG;YACJ,OAAOhT,MAAM,CAACiT,OAAO;UACzB,KAAK,IAAI;YACL,OAAOjT,MAAM,CAACkT,QAAQ;UAC1B,KAAK,IAAI;YACL,OAAOlT,MAAM,CAACmT,QAAQ;UAC1B,KAAK,IAAI;YACL,OAAOnT,MAAM,CAACoT,QAAQ;UAC1B,KAAK,KAAK;YACN,OAAOpT,MAAM,CAACqT,SAAS;QAC/B;QACA;MACJ,KAAK,GAAG;QACJ,QAAQhG,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAACsT,6BAA6B;UAC/C,KAAK,GAAG;YACJ,OAAOtT,MAAM,CAACuT,uBAAuB;UACzC,KAAK,GAAG;YACJ,OAAOvT,MAAM,CAACwT,sBAAsB;UACxC;YACI,OAAOxT,MAAM,CAACyT,yBAAyB;QAC/C;MACJ;QACI,QAAQpG,QAAQ;UACZ,KAAK,KAAK;YACN,OAAOrN,MAAM,CAAC0T,mBAAmB;UACrC,KAAK,GAAG;YACJ,OAAO1T,MAAM,CAAC2T,aAAa;UAC/B,KAAK,GAAG;YACJ,OAAO3T,MAAM,CAAC4T,YAAY;UAC9B;YACI,OAAO5T,MAAM,CAACoR,aAAa;QACnC;IACR;IACA,OAAOpR,MAAM,CAAC6T,IAAI;EACtB;AACJ;AACAjH,MAAM,CAACkH,MAAM,GAAG,IAAI;AACpBlH,MAAM,CAACmH,cAAc,GAAG,EAAE;AAC1BnH,MAAM,CAACoH,UAAU,GAAG,GAAG;AACvBpH,MAAM,CAACqH,WAAW,GAAG,EAAE;AACvBrH,MAAM,CAACsH,oBAAoB,GAAG,CAAC;AAC/BtH,MAAM,CAACuH,uBAAuB,GAAG,CAAC;AAClCvH,MAAM,CAAC1M,UAAU,GAAGA,UAAU;AAC9B0M,MAAM,CAACwH,aAAa,GAAG;EACnB,KAAK,EAAE;IACHC,IAAI,EAAE;EACV,CAAC;EACD,CAAC,EAAE;IACCA,IAAI,EAAE;EACV,CAAC;EACD,CAAC,EAAE;IACCA,IAAI,EAAE;EACV,CAAC;EACD,CAAC,EAAE;IACCA,IAAI,EAAE;EACV,CAAC;EACD,CAAC,EAAE;IACCA,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAExU,MAAM,CAACyU;EACvB,CAAC;EACD,EAAE,EAAE;IACAH,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,CAAC;IACpBF,IAAI,EAAE,IAAI;IACVG,UAAU,EAAExU,MAAM,CAAC0U;EACvB,CAAC;EACD,EAAE,EAAE;IACAJ,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,GAAG;IACtBF,IAAI,EAAE,IAAI;IACVG,UAAU,EAAExU,MAAM,CAAC2U;EACvB,CAAC;EACD,EAAE,EAAE;IACAL,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,EAAE;IACrBF,IAAI,EAAE,IAAI;IACVG,UAAU,EAAExU,MAAM,CAAC4U;EACvB,CAAC;EACD,GAAG,EAAE;IACDN,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,IAAI;IACvBF,IAAI,EAAE,IAAI;IACVG,UAAU,EAAExU,MAAM,CAAC6U;EACvB;AACJ,CAAC;AACDjI,MAAM,CAACkI,mBAAmB,GAAG,EAAE;AAC/BlI,MAAM,CAACmI,oBAAoB,GAAG,EAAE;AAChCnI,MAAM,CAACoI,oBAAoB,GAAG,EAAE;AAChCpI,MAAM,CAACqI,mBAAmB,GAAG,EAAE;AAC/BrI,MAAM,CAACsI,uBAAuB,GAAG,CAAC;AAClCtI,MAAM,CAACuI,sBAAsB,GAAGjO,iBAAiB;AACjD0F,MAAM,CAACwI,OAAO,GAAG;EACbC,KAAK,EAAE,QAAQ;EACfC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,QAAQ;EAClB,cAAc,EAAE,QAAQ;EACxBC,OAAO,EAAE,QAAQ;EACjBC,MAAM,EAAE;AACZ,CAAC;AACD9I,MAAM,CAAC+I,UAAU,GAAGpR,cAAc;AAClCqI,MAAM,CAACgJ,OAAO,GAAG;EACbC,QAAQ,EAAE,CAAC;EACXC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE7V;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}