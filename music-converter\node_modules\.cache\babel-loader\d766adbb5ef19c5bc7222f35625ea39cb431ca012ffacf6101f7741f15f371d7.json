{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // VEXTAB SETUP\n\n  const defaultSetup = 'tabstave notation=true tablature=false';\n  const defaultNotes = ['G/4'];\n  const [key, setKey] = useState('G');\n  const [time, setTime] = useState('4/4');\n  const [notes, setNotes] = useState(defaultNotes);\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${key} notes=${notes.join(' ')}`);\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Helmet, {\n        children: /*#__PURE__*/_jsxDEV(\"script\", {\n          src: \"https://unpkg.com/vextab/releases/div.prod.js\",\n          type: \"text/javascript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"SAY SOMETHING!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"vex-tabdiv\",\n            width: \"800px\",\n            scale: \"1.0\",\n            editor: \"true\",\n            \"editor-height\": \"80px\",\n            children: \"tabstave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"tFTTIixf0Xce2ns0pcl8O9PprNk=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Converter", "_s", "defaultSetup", "defaultNotes", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "setRawVex", "join", "NOTES", "children", "src", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "class", "scale", "editor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["\r\nimport React, { useEffect, useState} from 'react';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nfunction Converter() {\r\n\r\n  // VEXTAB SETUP\r\n\r\n  const defaultSetup = 'tabstave notation=true tablature=false';\r\n  const defaultNotes = ['G/4'];\r\n  const [key, setKey] = useState('G');\r\n  const [time, setTime] = useState('4/4');\r\n  const [notes, setNotes] = useState(defaultNotes);\r\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${key} notes=${notes.join(' ')}`);\r\n\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n\r\n  return (\r\n    <main>\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <Helmet>\r\n          <script src=\"https://unpkg.com/vextab/releases/div.prod.js\" type=\"text/javascript\"></script>\r\n        </Helmet>\r\n        <section>\r\n          <h3>SAY SOMETHING!</h3>\r\n          <div style={{ width: '100%' }}>\r\n            <div class=\"vex-tabdiv\" width=\"800px\" scale=\"1.0\" editor=\"true\" editor-height=\"80px\">tabstave</div>\r\n          </div>\r\n          \r\n\r\n\r\n\r\n        </section>\r\n        \r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>Output</h3>\r\n        </section>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAO,OAAO;AACjD,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAEnB;;EAEA,MAAMC,YAAY,GAAG,wCAAwC;EAC7D,MAAMC,YAAY,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGT,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAACO,YAAY,CAAC;EAChD,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,GAAGM,YAAY,QAAQE,GAAG,SAASA,GAAG,UAAUI,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;;EAGvG;EACA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAEjD,oBACEd,OAAA;IAAAe,QAAA,gBAEEf,OAAA;MAAAe,QAAA,gBACEf,OAAA,CAACF,MAAM;QAAAiB,QAAA,eACLf,OAAA;UAAQgB,GAAG,EAAC,+CAA+C;UAACC,IAAI,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eACTrB,OAAA;QAAAe,QAAA,gBACEf,OAAA;UAAAe,QAAA,EAAI;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBrB,OAAA;UAAKsB,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,eAC5Bf,OAAA;YAAKwB,KAAK,EAAC,YAAY;YAACD,KAAK,EAAC,OAAO;YAACE,KAAK,EAAC,KAAK;YAACC,MAAM,EAAC,MAAM;YAAC,iBAAc,MAAM;YAAAX,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CAAC,eAGNrB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAAe,QAAA,eACEf,OAAA;UAAAe,QAAA,EAAI;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACnB,EAAA,CA5CQD,SAAS;AAAA0B,EAAA,GAAT1B,SAAS;AA8ClB,eAAeA,SAAS;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}