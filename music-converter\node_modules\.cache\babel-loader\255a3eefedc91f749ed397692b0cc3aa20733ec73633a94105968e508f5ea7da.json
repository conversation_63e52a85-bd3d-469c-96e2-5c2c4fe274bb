{"ast": null, "code": "import { Element } from './element.js';\nimport { defined } from './util.js';\nexport var StaveModifierPosition;\n(function (StaveModifierPosition) {\n  StaveModifierPosition[StaveModifierPosition[\"CENTER\"] = 0] = \"CENTER\";\n  StaveModifierPosition[StaveModifierPosition[\"LEFT\"] = 1] = \"LEFT\";\n  StaveModifierPosition[StaveModifierPosition[\"RIGHT\"] = 2] = \"RIGHT\";\n  StaveModifierPosition[StaveModifierPosition[\"ABOVE\"] = 3] = \"ABOVE\";\n  StaveModifierPosition[StaveModifierPosition[\"BELOW\"] = 4] = \"BELOW\";\n  StaveModifierPosition[StaveModifierPosition[\"BEGIN\"] = 5] = \"BEGIN\";\n  StaveModifierPosition[StaveModifierPosition[\"END\"] = 6] = \"END\";\n})(StaveModifierPosition || (StaveModifierPosition = {}));\nexport class StaveModifier extends Element {\n  static get CATEGORY() {\n    return \"StaveModifier\";\n  }\n  static get Position() {\n    return StaveModifierPosition;\n  }\n  constructor() {\n    super();\n    this.padding = 10;\n    this.position = StaveModifierPosition.ABOVE;\n  }\n  getPosition() {\n    return this.position;\n  }\n  setPosition(position) {\n    this.position = position;\n    return this;\n  }\n  getStave() {\n    return this.stave;\n  }\n  checkStave() {\n    return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n  }\n  setStave(stave) {\n    this.stave = stave;\n    return this;\n  }\n  getPadding(index) {\n    return index !== undefined && index < 2 ? 0 : this.padding;\n  }\n  setPadding(padding) {\n    this.padding = padding;\n    return this;\n  }\n  setLayoutMetrics(layoutMetrics) {\n    this.layoutMetrics = layoutMetrics;\n    return this;\n  }\n  getLayoutMetrics() {\n    return this.layoutMetrics;\n  }\n}", "map": {"version": 3, "names": ["Element", "defined", "StaveModifierPosition", "StaveModifier", "CATEGORY", "Position", "constructor", "padding", "position", "ABOVE", "getPosition", "setPosition", "getStave", "stave", "checkStave", "setStave", "getPadding", "index", "undefined", "setPadding", "setLayoutMetrics", "layoutMetrics", "getLayoutMetrics"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavemodifier.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { defined } from './util.js';\nexport var StaveModifierPosition;\n(function (StaveModifierPosition) {\n    StaveModifierPosition[StaveModifierPosition[\"CENTER\"] = 0] = \"CENTER\";\n    StaveModifierPosition[StaveModifierPosition[\"LEFT\"] = 1] = \"LEFT\";\n    StaveModifierPosition[StaveModifierPosition[\"RIGHT\"] = 2] = \"RIGHT\";\n    StaveModifierPosition[StaveModifierPosition[\"ABOVE\"] = 3] = \"ABOVE\";\n    StaveModifierPosition[StaveModifierPosition[\"BELOW\"] = 4] = \"BELOW\";\n    StaveModifierPosition[StaveModifierPosition[\"BEGIN\"] = 5] = \"BEGIN\";\n    StaveModifierPosition[StaveModifierPosition[\"END\"] = 6] = \"END\";\n})(StaveModifierPosition || (StaveModifierPosition = {}));\nexport class StaveModifier extends Element {\n    static get CATEGORY() {\n        return \"StaveModifier\";\n    }\n    static get Position() {\n        return StaveModifierPosition;\n    }\n    constructor() {\n        super();\n        this.padding = 10;\n        this.position = StaveModifierPosition.ABOVE;\n    }\n    getPosition() {\n        return this.position;\n    }\n    setPosition(position) {\n        this.position = position;\n        return this;\n    }\n    getStave() {\n        return this.stave;\n    }\n    checkStave() {\n        return defined(this.stave, 'NoStave', 'No stave attached to instance.');\n    }\n    setStave(stave) {\n        this.stave = stave;\n        return this;\n    }\n    getPadding(index) {\n        return index !== undefined && index < 2 ? 0 : this.padding;\n    }\n    setPadding(padding) {\n        this.padding = padding;\n        return this;\n    }\n    setLayoutMetrics(layoutMetrics) {\n        this.layoutMetrics = layoutMetrics;\n        return this;\n    }\n    getLayoutMetrics() {\n        return this.layoutMetrics;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,IAAIC,qBAAqB;AAChC,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACrEA,qBAAqB,CAACA,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjEA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnEA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnEA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnEA,qBAAqB,CAACA,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnEA,qBAAqB,CAACA,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACnE,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO,MAAMC,aAAa,SAASH,OAAO,CAAC;EACvC,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,eAAe;EAC1B;EACA,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAOH,qBAAqB;EAChC;EACAI,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGN,qBAAqB,CAACO,KAAK;EAC/C;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,QAAQ;EACxB;EACAG,WAAWA,CAACH,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;EACf;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACAC,UAAUA,CAAA,EAAG;IACT,OAAOb,OAAO,CAAC,IAAI,CAACY,KAAK,EAAE,SAAS,EAAE,gCAAgC,CAAC;EAC3E;EACAE,QAAQA,CAACF,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAG,UAAUA,CAACC,KAAK,EAAE;IACd,OAAOA,KAAK,KAAKC,SAAS,IAAID,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACV,OAAO;EAC9D;EACAY,UAAUA,CAACZ,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACAa,gBAAgBA,CAACC,aAAa,EAAE;IAC5B,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACf;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,aAAa;EAC7B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}