{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { isNote, isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Stroke extends Modifier {\n  static get CATEGORY() {\n    return \"Stroke\";\n  }\n  static format(strokes, state) {\n    const leftShift = state.leftShift;\n    const strokeSpacing = 5;\n    if (!strokes || strokes.length === 0) return false;\n    const strokeList = strokes.map(stroke => {\n      const note = stroke.getNote();\n      const index = stroke.checkIndex();\n      if (isStaveNote(note)) {\n        const {\n          line\n        } = note.getKeyProps()[index];\n        const shift = note.getLeftDisplacedHeadPx();\n        return {\n          line,\n          shift,\n          stroke\n        };\n      } else if (isTabNote(note)) {\n        const {\n          str: string\n        } = note.getPositions()[index];\n        return {\n          line: string,\n          shift: 0,\n          stroke\n        };\n      } else {\n        throw new RuntimeError('Internal', 'Unexpected instance.');\n      }\n    });\n    const strokeShift = leftShift;\n    const xShift = strokeList.reduce((xShift, {\n      stroke,\n      shift\n    }) => {\n      stroke.setXShift(strokeShift + shift);\n      return Math.max(stroke.getWidth() + strokeSpacing, xShift);\n    }, 0);\n    state.leftShift += xShift;\n    return true;\n  }\n  constructor(type, options) {\n    super();\n    this.options = Object.assign({\n      allVoices: true\n    }, options);\n    this.allVoices = this.options.allVoices;\n    this.type = type;\n    this.position = Modifier.Position.LEFT;\n    this.renderOptions = {\n      fontScale: Metrics.get('Stroke.fontSize')\n    };\n    this.setXShift(0);\n    this.setWidth(10);\n  }\n  getPosition() {\n    return this.position;\n  }\n  addEndNote(note) {\n    this.noteEnd = note;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(this.position, this.index);\n    let yPositions = note.getYs();\n    let topY = start.y;\n    let botY = start.y;\n    const x = start.x - 5;\n    const lineSpace = note.checkStave().getSpacingBetweenLines();\n    const notes = this.checkModifierContext().getMembers(note.getCategory());\n    for (let i = 0; i < notes.length; i++) {\n      const note = notes[i];\n      if (isNote(note)) {\n        yPositions = note.getYs();\n        for (let n = 0; n < yPositions.length; n++) {\n          if (this.note === notes[i] || this.allVoices) {\n            topY = Math.min(topY, yPositions[n]);\n            botY = Math.max(botY, yPositions[n]);\n          }\n        }\n      }\n    }\n    let arrow = '';\n    let arrowY = 0;\n    let textY = 0;\n    switch (this.type) {\n      case Stroke.Type.BRUSH_DOWN:\n      case Stroke.Type.ROLL_DOWN:\n      case Stroke.Type.RASGUEADO_DOWN:\n        arrow = Glyphs.arrowheadBlackUp;\n        arrowY = topY;\n        topY -= lineSpace / 2;\n        botY += lineSpace / 2;\n        break;\n      case Stroke.Type.BRUSH_UP:\n      case Stroke.Type.ROLL_UP:\n      case Stroke.Type.RASGUEADO_UP:\n        arrow = Glyphs.arrowheadBlackDown;\n        arrowY = botY + lineSpace;\n        topY -= lineSpace / 2;\n        break;\n      case Stroke.Type.ARPEGGIO_DIRECTIONLESS:\n        topY -= lineSpace / 2;\n        botY += lineSpace / 2;\n        break;\n      default:\n        throw new RuntimeError('InvalidType', `The stroke type ${this.type} does not exist`);\n    }\n    if (this.type === Stroke.Type.BRUSH_DOWN || this.type === Stroke.Type.BRUSH_UP) {\n      ctx.fillRect(x + this.xShift, topY, 1, botY - topY);\n    } else {\n      const lineGlyph = arrow === Glyphs.arrowheadBlackDown ? Glyphs.wiggleArpeggiatoDown : Glyphs.wiggleArpeggiatoUp;\n      let txt = '';\n      const el = new Element();\n      while (el.getWidth() < botY - topY) {\n        txt += lineGlyph;\n        el.setText(txt);\n      }\n      if (this.type === Stroke.Type.RASGUEADO_DOWN || this.type === Stroke.Type.ROLL_DOWN || this.type === Stroke.Type.ARPEGGIO_DIRECTIONLESS) {\n        ctx.openRotation(90, x + this.xShift, topY);\n        el.renderText(ctx, x + this.xShift, topY - el.getTextMetrics().actualBoundingBoxDescent + el.getHeight() / 2);\n        ctx.closeRotation();\n        textY = topY + el.getWidth() + 5;\n      } else {\n        ctx.openRotation(-90, x + this.xShift, botY);\n        el.renderText(ctx, x + this.xShift, botY - el.getTextMetrics().actualBoundingBoxDescent + el.getHeight() / 2);\n        ctx.closeRotation();\n        textY = botY - el.getWidth() - 5;\n      }\n    }\n    if (arrowY !== 0) {\n      const el = new Element();\n      el.setText(arrow);\n      el.renderText(ctx, x + this.xShift - el.getWidth() / 2, arrowY);\n    }\n    if (this.type === Stroke.Type.RASGUEADO_DOWN || this.type === Stroke.Type.RASGUEADO_UP) {\n      const el = new Element('Stroke.text');\n      el.setText('R');\n      el.renderText(ctx, x + this.xShift - el.getWidth() / 2, textY + (this.type === Stroke.Type.RASGUEADO_DOWN ? el.getHeight() : 0));\n    }\n  }\n}\nStroke.Type = {\n  BRUSH_DOWN: 1,\n  BRUSH_UP: 2,\n  ROLL_DOWN: 3,\n  ROLL_UP: 4,\n  RASGUEADO_DOWN: 5,\n  RASGUEADO_UP: 6,\n  ARPEGGIO_DIRECTIONLESS: 7\n};", "map": {"version": 3, "names": ["Element", "Glyphs", "Metrics", "Modifier", "isNote", "isStaveNote", "isTabNote", "RuntimeError", "Stroke", "CATEGORY", "format", "strokes", "state", "leftShift", "strokeSpacing", "length", "strokeList", "map", "stroke", "note", "getNote", "index", "checkIndex", "line", "getKeyProps", "shift", "getLeftDisplacedHeadPx", "str", "string", "getPositions", "strokeShift", "xShift", "reduce", "setXShift", "Math", "max", "getWidth", "constructor", "type", "options", "Object", "assign", "allVoices", "position", "Position", "LEFT", "renderOptions", "fontScale", "get", "<PERSON><PERSON><PERSON><PERSON>", "getPosition", "addEndNote", "noteEnd", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "yPositions", "getYs", "topY", "y", "botY", "x", "lineSpace", "checkStave", "getSpacingBetweenLines", "notes", "checkModifierContext", "getMembers", "getCategory", "i", "n", "min", "arrow", "arrowY", "textY", "Type", "BRUSH_DOWN", "ROLL_DOWN", "RASGUEADO_DOWN", "arrowheadBlackUp", "BRUSH_UP", "ROLL_UP", "RASGUEADO_UP", "arrowheadBlackDown", "ARPEGGIO_DIRECTIONLESS", "fillRect", "lineGlyph", "wiggleArpeggiatoDown", "wiggleArpeggiatoUp", "txt", "el", "setText", "openRotation", "renderText", "getTextMetrics", "actualBoundingBoxDescent", "getHeight", "closeRotation"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/strokes.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { isNote, isStaveNote, isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Stroke extends Modifier {\n    static get CATEGORY() {\n        return \"Stroke\";\n    }\n    static format(strokes, state) {\n        const leftShift = state.leftShift;\n        const strokeSpacing = 5;\n        if (!strokes || strokes.length === 0)\n            return false;\n        const strokeList = strokes.map((stroke) => {\n            const note = stroke.getNote();\n            const index = stroke.checkIndex();\n            if (isStaveNote(note)) {\n                const { line } = note.getKeyProps()[index];\n                const shift = note.getLeftDisplacedHeadPx();\n                return { line, shift, stroke };\n            }\n            else if (isTabNote(note)) {\n                const { str: string } = note.getPositions()[index];\n                return { line: string, shift: 0, stroke };\n            }\n            else {\n                throw new RuntimeError('Internal', 'Unexpected instance.');\n            }\n        });\n        const strokeShift = leftShift;\n        const xShift = strokeList.reduce((xShift, { stroke, shift }) => {\n            stroke.setXShift(strokeShift + shift);\n            return Math.max(stroke.getWidth() + strokeSpacing, xShift);\n        }, 0);\n        state.leftShift += xShift;\n        return true;\n    }\n    constructor(type, options) {\n        super();\n        this.options = Object.assign({ allVoices: true }, options);\n        this.allVoices = this.options.allVoices;\n        this.type = type;\n        this.position = Modifier.Position.LEFT;\n        this.renderOptions = {\n            fontScale: Metrics.get('Stroke.fontSize'),\n        };\n        this.setXShift(0);\n        this.setWidth(10);\n    }\n    getPosition() {\n        return this.position;\n    }\n    addEndNote(note) {\n        this.noteEnd = note;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(this.position, this.index);\n        let yPositions = note.getYs();\n        let topY = start.y;\n        let botY = start.y;\n        const x = start.x - 5;\n        const lineSpace = note.checkStave().getSpacingBetweenLines();\n        const notes = this.checkModifierContext().getMembers(note.getCategory());\n        for (let i = 0; i < notes.length; i++) {\n            const note = notes[i];\n            if (isNote(note)) {\n                yPositions = note.getYs();\n                for (let n = 0; n < yPositions.length; n++) {\n                    if (this.note === notes[i] || this.allVoices) {\n                        topY = Math.min(topY, yPositions[n]);\n                        botY = Math.max(botY, yPositions[n]);\n                    }\n                }\n            }\n        }\n        let arrow = '';\n        let arrowY = 0;\n        let textY = 0;\n        switch (this.type) {\n            case Stroke.Type.BRUSH_DOWN:\n            case Stroke.Type.ROLL_DOWN:\n            case Stroke.Type.RASGUEADO_DOWN:\n                arrow = Glyphs.arrowheadBlackUp;\n                arrowY = topY;\n                topY -= lineSpace / 2;\n                botY += lineSpace / 2;\n                break;\n            case Stroke.Type.BRUSH_UP:\n            case Stroke.Type.ROLL_UP:\n            case Stroke.Type.RASGUEADO_UP:\n                arrow = Glyphs.arrowheadBlackDown;\n                arrowY = botY + lineSpace;\n                topY -= lineSpace / 2;\n                break;\n            case Stroke.Type.ARPEGGIO_DIRECTIONLESS:\n                topY -= lineSpace / 2;\n                botY += lineSpace / 2;\n                break;\n            default:\n                throw new RuntimeError('InvalidType', `The stroke type ${this.type} does not exist`);\n        }\n        if (this.type === Stroke.Type.BRUSH_DOWN || this.type === Stroke.Type.BRUSH_UP) {\n            ctx.fillRect(x + this.xShift, topY, 1, botY - topY);\n        }\n        else {\n            const lineGlyph = arrow === Glyphs.arrowheadBlackDown ? Glyphs.wiggleArpeggiatoDown : Glyphs.wiggleArpeggiatoUp;\n            let txt = '';\n            const el = new Element();\n            while (el.getWidth() < botY - topY) {\n                txt += lineGlyph;\n                el.setText(txt);\n            }\n            if (this.type === Stroke.Type.RASGUEADO_DOWN ||\n                this.type === Stroke.Type.ROLL_DOWN ||\n                this.type === Stroke.Type.ARPEGGIO_DIRECTIONLESS) {\n                ctx.openRotation(90, x + this.xShift, topY);\n                el.renderText(ctx, x + this.xShift, topY - el.getTextMetrics().actualBoundingBoxDescent + el.getHeight() / 2);\n                ctx.closeRotation();\n                textY = topY + el.getWidth() + 5;\n            }\n            else {\n                ctx.openRotation(-90, x + this.xShift, botY);\n                el.renderText(ctx, x + this.xShift, botY - el.getTextMetrics().actualBoundingBoxDescent + el.getHeight() / 2);\n                ctx.closeRotation();\n                textY = botY - el.getWidth() - 5;\n            }\n        }\n        if (arrowY !== 0) {\n            const el = new Element();\n            el.setText(arrow);\n            el.renderText(ctx, x + this.xShift - el.getWidth() / 2, arrowY);\n        }\n        if (this.type === Stroke.Type.RASGUEADO_DOWN || this.type === Stroke.Type.RASGUEADO_UP) {\n            const el = new Element('Stroke.text');\n            el.setText('R');\n            el.renderText(ctx, x + this.xShift - el.getWidth() / 2, textY + (this.type === Stroke.Type.RASGUEADO_DOWN ? el.getHeight() : 0));\n        }\n    }\n}\nStroke.Type = {\n    BRUSH_DOWN: 1,\n    BRUSH_UP: 2,\n    ROLL_DOWN: 3,\n    ROLL_UP: 4,\n    RASGUEADO_DOWN: 5,\n    RASGUEADO_UP: 6,\n    ARPEGGIO_DIRECTIONLESS: 7,\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,EAAEC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;AAC/D,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,MAAM,SAASL,QAAQ,CAAC;EACjC,WAAWM,QAAQA,CAAA,EAAG;IAClB,OAAO,QAAQ;EACnB;EACA,OAAOC,MAAMA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAC1B,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS;IACjC,MAAMC,aAAa,GAAG,CAAC;IACvB,IAAI,CAACH,OAAO,IAAIA,OAAO,CAACI,MAAM,KAAK,CAAC,EAChC,OAAO,KAAK;IAChB,MAAMC,UAAU,GAAGL,OAAO,CAACM,GAAG,CAAEC,MAAM,IAAK;MACvC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAAC,CAAC;MAC7B,MAAMC,KAAK,GAAGH,MAAM,CAACI,UAAU,CAAC,CAAC;MACjC,IAAIjB,WAAW,CAACc,IAAI,CAAC,EAAE;QACnB,MAAM;UAAEI;QAAK,CAAC,GAAGJ,IAAI,CAACK,WAAW,CAAC,CAAC,CAACH,KAAK,CAAC;QAC1C,MAAMI,KAAK,GAAGN,IAAI,CAACO,sBAAsB,CAAC,CAAC;QAC3C,OAAO;UAAEH,IAAI;UAAEE,KAAK;UAAEP;QAAO,CAAC;MAClC,CAAC,MACI,IAAIZ,SAAS,CAACa,IAAI,CAAC,EAAE;QACtB,MAAM;UAAEQ,GAAG,EAAEC;QAAO,CAAC,GAAGT,IAAI,CAACU,YAAY,CAAC,CAAC,CAACR,KAAK,CAAC;QAClD,OAAO;UAAEE,IAAI,EAAEK,MAAM;UAAEH,KAAK,EAAE,CAAC;UAAEP;QAAO,CAAC;MAC7C,CAAC,MACI;QACD,MAAM,IAAIX,YAAY,CAAC,UAAU,EAAE,sBAAsB,CAAC;MAC9D;IACJ,CAAC,CAAC;IACF,MAAMuB,WAAW,GAAGjB,SAAS;IAC7B,MAAMkB,MAAM,GAAGf,UAAU,CAACgB,MAAM,CAAC,CAACD,MAAM,EAAE;MAAEb,MAAM;MAAEO;IAAM,CAAC,KAAK;MAC5DP,MAAM,CAACe,SAAS,CAACH,WAAW,GAAGL,KAAK,CAAC;MACrC,OAAOS,IAAI,CAACC,GAAG,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAGtB,aAAa,EAAEiB,MAAM,CAAC;IAC9D,CAAC,EAAE,CAAC,CAAC;IACLnB,KAAK,CAACC,SAAS,IAAIkB,MAAM;IACzB,OAAO,IAAI;EACf;EACAM,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,EAAEH,OAAO,CAAC;IAC1D,IAAI,CAACG,SAAS,GAAG,IAAI,CAACH,OAAO,CAACG,SAAS;IACvC,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,QAAQ,GAAGxC,QAAQ,CAACyC,QAAQ,CAACC,IAAI;IACtC,IAAI,CAACC,aAAa,GAAG;MACjBC,SAAS,EAAE7C,OAAO,CAAC8C,GAAG,CAAC,iBAAiB;IAC5C,CAAC;IACD,IAAI,CAACf,SAAS,CAAC,CAAC,CAAC;IACjB,IAAI,CAACgB,QAAQ,CAAC,EAAE,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,QAAQ;EACxB;EACAQ,UAAUA,CAAChC,IAAI,EAAE;IACb,IAAI,CAACiC,OAAO,GAAGjC,IAAI;IACnB,OAAO,IAAI;EACf;EACAkC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMpC,IAAI,GAAG,IAAI,CAACqC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGvC,IAAI,CAACwC,kBAAkB,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACtB,KAAK,CAAC;IAChE,IAAIuC,UAAU,GAAGzC,IAAI,CAAC0C,KAAK,CAAC,CAAC;IAC7B,IAAIC,IAAI,GAAGJ,KAAK,CAACK,CAAC;IAClB,IAAIC,IAAI,GAAGN,KAAK,CAACK,CAAC;IAClB,MAAME,CAAC,GAAGP,KAAK,CAACO,CAAC,GAAG,CAAC;IACrB,MAAMC,SAAS,GAAG/C,IAAI,CAACgD,UAAU,CAAC,CAAC,CAACC,sBAAsB,CAAC,CAAC;IAC5D,MAAMC,KAAK,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAACC,UAAU,CAACpD,IAAI,CAACqD,WAAW,CAAC,CAAC,CAAC;IACxE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACtD,MAAM,EAAE0D,CAAC,EAAE,EAAE;MACnC,MAAMtD,IAAI,GAAGkD,KAAK,CAACI,CAAC,CAAC;MACrB,IAAIrE,MAAM,CAACe,IAAI,CAAC,EAAE;QACdyC,UAAU,GAAGzC,IAAI,CAAC0C,KAAK,CAAC,CAAC;QACzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,CAAC7C,MAAM,EAAE2D,CAAC,EAAE,EAAE;UACxC,IAAI,IAAI,CAACvD,IAAI,KAAKkD,KAAK,CAACI,CAAC,CAAC,IAAI,IAAI,CAAC/B,SAAS,EAAE;YAC1CoB,IAAI,GAAG5B,IAAI,CAACyC,GAAG,CAACb,IAAI,EAAEF,UAAU,CAACc,CAAC,CAAC,CAAC;YACpCV,IAAI,GAAG9B,IAAI,CAACC,GAAG,CAAC6B,IAAI,EAAEJ,UAAU,CAACc,CAAC,CAAC,CAAC;UACxC;QACJ;MACJ;IACJ;IACA,IAAIE,KAAK,GAAG,EAAE;IACd,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,KAAK,GAAG,CAAC;IACb,QAAQ,IAAI,CAACxC,IAAI;MACb,KAAK9B,MAAM,CAACuE,IAAI,CAACC,UAAU;MAC3B,KAAKxE,MAAM,CAACuE,IAAI,CAACE,SAAS;MAC1B,KAAKzE,MAAM,CAACuE,IAAI,CAACG,cAAc;QAC3BN,KAAK,GAAG3E,MAAM,CAACkF,gBAAgB;QAC/BN,MAAM,GAAGf,IAAI;QACbA,IAAI,IAAII,SAAS,GAAG,CAAC;QACrBF,IAAI,IAAIE,SAAS,GAAG,CAAC;QACrB;MACJ,KAAK1D,MAAM,CAACuE,IAAI,CAACK,QAAQ;MACzB,KAAK5E,MAAM,CAACuE,IAAI,CAACM,OAAO;MACxB,KAAK7E,MAAM,CAACuE,IAAI,CAACO,YAAY;QACzBV,KAAK,GAAG3E,MAAM,CAACsF,kBAAkB;QACjCV,MAAM,GAAGb,IAAI,GAAGE,SAAS;QACzBJ,IAAI,IAAII,SAAS,GAAG,CAAC;QACrB;MACJ,KAAK1D,MAAM,CAACuE,IAAI,CAACS,sBAAsB;QACnC1B,IAAI,IAAII,SAAS,GAAG,CAAC;QACrBF,IAAI,IAAIE,SAAS,GAAG,CAAC;QACrB;MACJ;QACI,MAAM,IAAI3D,YAAY,CAAC,aAAa,EAAE,mBAAmB,IAAI,CAAC+B,IAAI,iBAAiB,CAAC;IAC5F;IACA,IAAI,IAAI,CAACA,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACC,UAAU,IAAI,IAAI,CAAC1C,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACK,QAAQ,EAAE;MAC5E9B,GAAG,CAACmC,QAAQ,CAACxB,CAAC,GAAG,IAAI,CAAClC,MAAM,EAAE+B,IAAI,EAAE,CAAC,EAAEE,IAAI,GAAGF,IAAI,CAAC;IACvD,CAAC,MACI;MACD,MAAM4B,SAAS,GAAGd,KAAK,KAAK3E,MAAM,CAACsF,kBAAkB,GAAGtF,MAAM,CAAC0F,oBAAoB,GAAG1F,MAAM,CAAC2F,kBAAkB;MAC/G,IAAIC,GAAG,GAAG,EAAE;MACZ,MAAMC,EAAE,GAAG,IAAI9F,OAAO,CAAC,CAAC;MACxB,OAAO8F,EAAE,CAAC1D,QAAQ,CAAC,CAAC,GAAG4B,IAAI,GAAGF,IAAI,EAAE;QAChC+B,GAAG,IAAIH,SAAS;QAChBI,EAAE,CAACC,OAAO,CAACF,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACvD,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACG,cAAc,IACxC,IAAI,CAAC5C,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACE,SAAS,IACnC,IAAI,CAAC3C,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACS,sBAAsB,EAAE;QAClDlC,GAAG,CAAC0C,YAAY,CAAC,EAAE,EAAE/B,CAAC,GAAG,IAAI,CAAClC,MAAM,EAAE+B,IAAI,CAAC;QAC3CgC,EAAE,CAACG,UAAU,CAAC3C,GAAG,EAAEW,CAAC,GAAG,IAAI,CAAClC,MAAM,EAAE+B,IAAI,GAAGgC,EAAE,CAACI,cAAc,CAAC,CAAC,CAACC,wBAAwB,GAAGL,EAAE,CAACM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7G9C,GAAG,CAAC+C,aAAa,CAAC,CAAC;QACnBvB,KAAK,GAAGhB,IAAI,GAAGgC,EAAE,CAAC1D,QAAQ,CAAC,CAAC,GAAG,CAAC;MACpC,CAAC,MACI;QACDkB,GAAG,CAAC0C,YAAY,CAAC,CAAC,EAAE,EAAE/B,CAAC,GAAG,IAAI,CAAClC,MAAM,EAAEiC,IAAI,CAAC;QAC5C8B,EAAE,CAACG,UAAU,CAAC3C,GAAG,EAAEW,CAAC,GAAG,IAAI,CAAClC,MAAM,EAAEiC,IAAI,GAAG8B,EAAE,CAACI,cAAc,CAAC,CAAC,CAACC,wBAAwB,GAAGL,EAAE,CAACM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;QAC7G9C,GAAG,CAAC+C,aAAa,CAAC,CAAC;QACnBvB,KAAK,GAAGd,IAAI,GAAG8B,EAAE,CAAC1D,QAAQ,CAAC,CAAC,GAAG,CAAC;MACpC;IACJ;IACA,IAAIyC,MAAM,KAAK,CAAC,EAAE;MACd,MAAMiB,EAAE,GAAG,IAAI9F,OAAO,CAAC,CAAC;MACxB8F,EAAE,CAACC,OAAO,CAACnB,KAAK,CAAC;MACjBkB,EAAE,CAACG,UAAU,CAAC3C,GAAG,EAAEW,CAAC,GAAG,IAAI,CAAClC,MAAM,GAAG+D,EAAE,CAAC1D,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEyC,MAAM,CAAC;IACnE;IACA,IAAI,IAAI,CAACvC,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACG,cAAc,IAAI,IAAI,CAAC5C,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACO,YAAY,EAAE;MACpF,MAAMQ,EAAE,GAAG,IAAI9F,OAAO,CAAC,aAAa,CAAC;MACrC8F,EAAE,CAACC,OAAO,CAAC,GAAG,CAAC;MACfD,EAAE,CAACG,UAAU,CAAC3C,GAAG,EAAEW,CAAC,GAAG,IAAI,CAAClC,MAAM,GAAG+D,EAAE,CAAC1D,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE0C,KAAK,IAAI,IAAI,CAACxC,IAAI,KAAK9B,MAAM,CAACuE,IAAI,CAACG,cAAc,GAAGY,EAAE,CAACM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpI;EACJ;AACJ;AACA5F,MAAM,CAACuE,IAAI,GAAG;EACVC,UAAU,EAAE,CAAC;EACbI,QAAQ,EAAE,CAAC;EACXH,SAAS,EAAE,CAAC;EACZI,OAAO,EAAE,CAAC;EACVH,cAAc,EAAE,CAAC;EACjBI,YAAY,EAAE,CAAC;EACfE,sBAAsB,EAAE;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}