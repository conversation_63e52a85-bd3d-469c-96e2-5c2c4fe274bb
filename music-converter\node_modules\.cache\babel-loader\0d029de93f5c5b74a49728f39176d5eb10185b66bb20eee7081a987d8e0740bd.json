{"ast": null, "code": "import { BoundingBox } from './boundingbox.js';\nimport { Element } from './element.js';\nimport { Formatter } from './formatter.js';\nimport { Note } from './note.js';\nimport { Stave } from './stave.js';\nimport { RuntimeError } from './util.js';\nexport class System extends Element {\n  static get CATEGORY() {\n    return \"System\";\n  }\n  constructor(params = {}) {\n    super();\n    this.setOptions(params);\n    this.partStaves = [];\n    this.partStaveInfos = [];\n    this.partVoices = [];\n  }\n  setOptions(options = {}) {\n    if (!options.factory) {\n      throw new RuntimeError('NoFactory', 'System.setOptions(options) requires a factory.');\n    }\n    this.factory = options.factory;\n    this.options = Object.assign(Object.assign({\n      factory: this.factory,\n      x: 10,\n      y: 10,\n      width: 500,\n      spaceBetweenStaves: 12,\n      autoWidth: false,\n      noJustification: false,\n      debugFormatter: false,\n      formatIterations: 0,\n      noPadding: false\n    }, options), {\n      details: Object.assign({\n        alpha: 0.5\n      }, options.details),\n      formatOptions: Object.assign({}, options.formatOptions)\n    });\n    if (this.options.noJustification === false && typeof options.width === 'undefined') {\n      this.options.autoWidth = true;\n    }\n  }\n  getX() {\n    return this.options.x;\n  }\n  setX(x) {\n    this.options.x = x;\n    this.partStaves.forEach(s => {\n      s.setX(x);\n    });\n    return this;\n  }\n  getY() {\n    return this.options.y;\n  }\n  setY(y) {\n    this.options.y = y;\n    this.partStaves.forEach(s => {\n      s.setY(y);\n    });\n    return this;\n  }\n  getStaves() {\n    return this.partStaves;\n  }\n  getVoices() {\n    return this.partVoices;\n  }\n  setContext(context) {\n    super.setContext(context);\n    this.factory.setContext(context);\n    return this;\n  }\n  addConnector(type = 'double') {\n    this.connector = this.factory.StaveConnector({\n      topStave: this.partStaves[0],\n      bottomStave: this.partStaves[this.partStaves.length - 1],\n      type\n    });\n    return this.connector;\n  }\n  addStave(params) {\n    var _a;\n    const staveOptions = Object.assign({\n      leftBar: false\n    }, params.options);\n    const stave = (_a = params.stave) !== null && _a !== void 0 ? _a : this.factory.Stave({\n      x: this.options.x,\n      y: this.options.y,\n      width: this.options.width,\n      options: staveOptions\n    });\n    const p = Object.assign(Object.assign({\n      spaceAbove: 0,\n      spaceBelow: 0,\n      debugNoteMetrics: false,\n      noJustification: false\n    }, params), {\n      options: staveOptions\n    });\n    const ctx = this.getContext();\n    p.voices.forEach(voice => {\n      voice.setContext(ctx).setStave(stave).getTickables().forEach(tickable => tickable.setStave(stave));\n      this.partVoices.push(voice);\n    });\n    this.partStaves.push(stave);\n    this.partStaveInfos.push(p);\n    return stave;\n  }\n  addVoices(voices) {\n    const ctx = this.getContext();\n    voices.forEach(voice => {\n      voice.setContext(ctx);\n      this.partVoices.push(voice);\n    });\n  }\n  format() {\n    const optionsDetails = this.options.details;\n    let justifyWidth = 0;\n    const formatter = new Formatter(optionsDetails);\n    this.formatter = formatter;\n    let y = this.options.y;\n    let startX = 0;\n    const debugNoteMetricsYs = [];\n    this.partStaves.forEach((part, index) => {\n      y = y + part.space(this.partStaveInfos[index].spaceAbove);\n      part.setY(y);\n      y = y + part.space(this.partStaveInfos[index].spaceBelow);\n      y = y + part.space(this.options.spaceBetweenStaves);\n      if (this.partStaveInfos[index].debugNoteMetrics) {\n        debugNoteMetricsYs.push({\n          y,\n          stave: part\n        });\n        y += 15;\n      }\n      startX = Math.max(startX, part.getNoteStartX());\n    });\n    this.partVoices.forEach(voice => {\n      voice.getTickables().forEach(tickable => {\n        const stave = tickable.getStave();\n        if (stave) tickable.setStave(stave);\n      });\n    });\n    formatter.joinVoices(this.partVoices);\n    this.partStaves.forEach(part => part.setNoteStartX(startX));\n    if (this.options.autoWidth && this.partVoices.length > 0) {\n      justifyWidth = formatter.preCalculateMinTotalWidth(this.partVoices);\n      this.options.width = justifyWidth + Stave.rightPadding + (startX - this.options.x);\n      this.partStaves.forEach(part => {\n        part.setWidth(this.options.width);\n      });\n    } else {\n      justifyWidth = this.options.noPadding ? this.options.width - (startX - this.options.x) : this.options.width - (startX - this.options.x) - Stave.defaultPadding;\n    }\n    if (this.partVoices.length > 0) {\n      formatter.format(this.partVoices, this.options.noJustification ? 0 : justifyWidth, this.options.formatOptions);\n    }\n    formatter.postFormat();\n    for (let i = 0; i < this.options.formatIterations; i++) {\n      formatter.tune(optionsDetails);\n    }\n    this.startX = startX;\n    this.debugNoteMetricsYs = debugNoteMetricsYs;\n    this.lastY = y;\n    Stave.formatBegModifiers(this.partStaves);\n  }\n  getBoundingBox() {\n    var _a;\n    return new BoundingBox(this.options.x, this.options.y, this.options.width, ((_a = this.lastY) !== null && _a !== void 0 ? _a : 0) - this.options.y);\n  }\n  draw() {\n    const ctx = this.checkContext();\n    if (!this.formatter || !this.startX || !this.lastY || !this.debugNoteMetricsYs) {\n      throw new RuntimeError('NoFormatter', 'format() must be called before draw()');\n    }\n    this.setRendered();\n    if (this.options.debugFormatter) {\n      Formatter.plotDebugging(ctx, this.formatter, this.startX, this.options.y, this.lastY);\n    }\n    this.debugNoteMetricsYs.forEach(d => {\n      this.partVoices.forEach(voice => {\n        voice.getTickables().forEach(tickable => {\n          if (tickable.getStave() === d.stave) Note.plotMetrics(ctx, tickable, d.y);\n        });\n      });\n    });\n  }\n}", "map": {"version": 3, "names": ["BoundingBox", "Element", "<PERSON><PERSON><PERSON>", "Note", "Stave", "RuntimeError", "System", "CATEGORY", "constructor", "params", "setOptions", "partStaves", "partStaveInfos", "partVoices", "options", "factory", "Object", "assign", "x", "y", "width", "spaceBetweenStaves", "autoWidth", "noJustification", "debugFormatter", "formatIterations", "noPadding", "details", "alpha", "formatOptions", "getX", "setX", "for<PERSON>ach", "s", "getY", "setY", "getStaves", "getVoices", "setContext", "context", "addConnector", "type", "connector", "StaveConnector", "topStave", "bottomStave", "length", "addStave", "_a", "staveOptions", "leftBar", "stave", "p", "spaceAbove", "spaceBelow", "debugNoteMetrics", "ctx", "getContext", "voices", "voice", "setStave", "getTickables", "tickable", "push", "addVoices", "format", "optionsDetails", "justifyWidth", "formatter", "startX", "debugNoteMetricsYs", "part", "index", "space", "Math", "max", "getNoteStartX", "getStave", "joinVoices", "setNoteStartX", "preCalculateMinTotalWidth", "rightPadding", "<PERSON><PERSON><PERSON><PERSON>", "defaultPadding", "postFormat", "i", "tune", "lastY", "formatBegModifiers", "getBoundingBox", "draw", "checkContext", "setRendered", "plotDebugging", "d", "plotMetrics"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/system.js"], "sourcesContent": ["import { BoundingBox } from './boundingbox.js';\nimport { Element } from './element.js';\nimport { Formatter } from './formatter.js';\nimport { Note } from './note.js';\nimport { Stave } from './stave.js';\nimport { RuntimeError } from './util.js';\nexport class System extends Element {\n    static get CATEGORY() {\n        return \"System\";\n    }\n    constructor(params = {}) {\n        super();\n        this.setOptions(params);\n        this.partStaves = [];\n        this.partStaveInfos = [];\n        this.partVoices = [];\n    }\n    setOptions(options = {}) {\n        if (!options.factory) {\n            throw new RuntimeError('NoFactory', 'System.setOptions(options) requires a factory.');\n        }\n        this.factory = options.factory;\n        this.options = Object.assign(Object.assign({ factory: this.factory, x: 10, y: 10, width: 500, spaceBetweenStaves: 12, autoWidth: false, noJustification: false, debugFormatter: false, formatIterations: 0, noPadding: false }, options), { details: Object.assign({ alpha: 0.5 }, options.details), formatOptions: Object.assign({}, options.formatOptions) });\n        if (this.options.noJustification === false && typeof options.width === 'undefined') {\n            this.options.autoWidth = true;\n        }\n    }\n    getX() {\n        return this.options.x;\n    }\n    setX(x) {\n        this.options.x = x;\n        this.partStaves.forEach((s) => {\n            s.setX(x);\n        });\n        return this;\n    }\n    getY() {\n        return this.options.y;\n    }\n    setY(y) {\n        this.options.y = y;\n        this.partStaves.forEach((s) => {\n            s.setY(y);\n        });\n        return this;\n    }\n    getStaves() {\n        return this.partStaves;\n    }\n    getVoices() {\n        return this.partVoices;\n    }\n    setContext(context) {\n        super.setContext(context);\n        this.factory.setContext(context);\n        return this;\n    }\n    addConnector(type = 'double') {\n        this.connector = this.factory.StaveConnector({\n            topStave: this.partStaves[0],\n            bottomStave: this.partStaves[this.partStaves.length - 1],\n            type,\n        });\n        return this.connector;\n    }\n    addStave(params) {\n        var _a;\n        const staveOptions = Object.assign({ leftBar: false }, params.options);\n        const stave = (_a = params.stave) !== null && _a !== void 0 ? _a : this.factory.Stave({ x: this.options.x, y: this.options.y, width: this.options.width, options: staveOptions });\n        const p = Object.assign(Object.assign({ spaceAbove: 0, spaceBelow: 0, debugNoteMetrics: false, noJustification: false }, params), { options: staveOptions });\n        const ctx = this.getContext();\n        p.voices.forEach((voice) => {\n            voice\n                .setContext(ctx)\n                .setStave(stave)\n                .getTickables()\n                .forEach((tickable) => tickable.setStave(stave));\n            this.partVoices.push(voice);\n        });\n        this.partStaves.push(stave);\n        this.partStaveInfos.push(p);\n        return stave;\n    }\n    addVoices(voices) {\n        const ctx = this.getContext();\n        voices.forEach((voice) => {\n            voice.setContext(ctx);\n            this.partVoices.push(voice);\n        });\n    }\n    format() {\n        const optionsDetails = this.options.details;\n        let justifyWidth = 0;\n        const formatter = new Formatter(optionsDetails);\n        this.formatter = formatter;\n        let y = this.options.y;\n        let startX = 0;\n        const debugNoteMetricsYs = [];\n        this.partStaves.forEach((part, index) => {\n            y = y + part.space(this.partStaveInfos[index].spaceAbove);\n            part.setY(y);\n            y = y + part.space(this.partStaveInfos[index].spaceBelow);\n            y = y + part.space(this.options.spaceBetweenStaves);\n            if (this.partStaveInfos[index].debugNoteMetrics) {\n                debugNoteMetricsYs.push({ y, stave: part });\n                y += 15;\n            }\n            startX = Math.max(startX, part.getNoteStartX());\n        });\n        this.partVoices.forEach((voice) => {\n            voice.getTickables().forEach((tickable) => {\n                const stave = tickable.getStave();\n                if (stave)\n                    tickable.setStave(stave);\n            });\n        });\n        formatter.joinVoices(this.partVoices);\n        this.partStaves.forEach((part) => part.setNoteStartX(startX));\n        if (this.options.autoWidth && this.partVoices.length > 0) {\n            justifyWidth = formatter.preCalculateMinTotalWidth(this.partVoices);\n            this.options.width = justifyWidth + Stave.rightPadding + (startX - this.options.x);\n            this.partStaves.forEach((part) => {\n                part.setWidth(this.options.width);\n            });\n        }\n        else {\n            justifyWidth = this.options.noPadding\n                ? this.options.width - (startX - this.options.x)\n                : this.options.width - (startX - this.options.x) - Stave.defaultPadding;\n        }\n        if (this.partVoices.length > 0) {\n            formatter.format(this.partVoices, this.options.noJustification ? 0 : justifyWidth, this.options.formatOptions);\n        }\n        formatter.postFormat();\n        for (let i = 0; i < this.options.formatIterations; i++) {\n            formatter.tune(optionsDetails);\n        }\n        this.startX = startX;\n        this.debugNoteMetricsYs = debugNoteMetricsYs;\n        this.lastY = y;\n        Stave.formatBegModifiers(this.partStaves);\n    }\n    getBoundingBox() {\n        var _a;\n        return new BoundingBox(this.options.x, this.options.y, this.options.width, ((_a = this.lastY) !== null && _a !== void 0 ? _a : 0) - this.options.y);\n    }\n    draw() {\n        const ctx = this.checkContext();\n        if (!this.formatter || !this.startX || !this.lastY || !this.debugNoteMetricsYs) {\n            throw new RuntimeError('NoFormatter', 'format() must be called before draw()');\n        }\n        this.setRendered();\n        if (this.options.debugFormatter) {\n            Formatter.plotDebugging(ctx, this.formatter, this.startX, this.options.y, this.lastY);\n        }\n        this.debugNoteMetricsYs.forEach((d) => {\n            this.partVoices.forEach((voice) => {\n                voice.getTickables().forEach((tickable) => {\n                    if (tickable.getStave() === d.stave)\n                        Note.plotMetrics(ctx, tickable, d.y);\n                });\n            });\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,MAAM,SAASL,OAAO,CAAC;EAChC,WAAWM,QAAQA,CAAA,EAAG;IAClB,OAAO,QAAQ;EACnB;EACAC,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,CAACD,MAAM,CAAC;IACvB,IAAI,CAACE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,UAAU,GAAG,EAAE;EACxB;EACAH,UAAUA,CAACI,OAAO,GAAG,CAAC,CAAC,EAAE;IACrB,IAAI,CAACA,OAAO,CAACC,OAAO,EAAE;MAClB,MAAM,IAAIV,YAAY,CAAC,WAAW,EAAE,gDAAgD,CAAC;IACzF;IACA,IAAI,CAACU,OAAO,GAAGD,OAAO,CAACC,OAAO;IAC9B,IAAI,CAACD,OAAO,GAAGE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAAEF,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEG,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,kBAAkB,EAAE,EAAE;MAAEC,SAAS,EAAE,KAAK;MAAEC,eAAe,EAAE,KAAK;MAAEC,cAAc,EAAE,KAAK;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAM,CAAC,EAAEZ,OAAO,CAAC,EAAE;MAAEa,OAAO,EAAEX,MAAM,CAACC,MAAM,CAAC;QAAEW,KAAK,EAAE;MAAI,CAAC,EAAEd,OAAO,CAACa,OAAO,CAAC;MAAEE,aAAa,EAAEb,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACe,aAAa;IAAE,CAAC,CAAC;IAC/V,IAAI,IAAI,CAACf,OAAO,CAACS,eAAe,KAAK,KAAK,IAAI,OAAOT,OAAO,CAACM,KAAK,KAAK,WAAW,EAAE;MAChF,IAAI,CAACN,OAAO,CAACQ,SAAS,GAAG,IAAI;IACjC;EACJ;EACAQ,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAChB,OAAO,CAACI,CAAC;EACzB;EACAa,IAAIA,CAACb,CAAC,EAAE;IACJ,IAAI,CAACJ,OAAO,CAACI,CAAC,GAAGA,CAAC;IAClB,IAAI,CAACP,UAAU,CAACqB,OAAO,CAAEC,CAAC,IAAK;MAC3BA,CAAC,CAACF,IAAI,CAACb,CAAC,CAAC;IACb,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAgB,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACpB,OAAO,CAACK,CAAC;EACzB;EACAgB,IAAIA,CAAChB,CAAC,EAAE;IACJ,IAAI,CAACL,OAAO,CAACK,CAAC,GAAGA,CAAC;IAClB,IAAI,CAACR,UAAU,CAACqB,OAAO,CAAEC,CAAC,IAAK;MAC3BA,CAAC,CAACE,IAAI,CAAChB,CAAC,CAAC;IACb,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAiB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACzB,UAAU;EAC1B;EACA0B,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxB,UAAU;EAC1B;EACAyB,UAAUA,CAACC,OAAO,EAAE;IAChB,KAAK,CAACD,UAAU,CAACC,OAAO,CAAC;IACzB,IAAI,CAACxB,OAAO,CAACuB,UAAU,CAACC,OAAO,CAAC;IAChC,OAAO,IAAI;EACf;EACAC,YAAYA,CAACC,IAAI,GAAG,QAAQ,EAAE;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC3B,OAAO,CAAC4B,cAAc,CAAC;MACzCC,QAAQ,EAAE,IAAI,CAACjC,UAAU,CAAC,CAAC,CAAC;MAC5BkC,WAAW,EAAE,IAAI,CAAClC,UAAU,CAAC,IAAI,CAACA,UAAU,CAACmC,MAAM,GAAG,CAAC,CAAC;MACxDL;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACC,SAAS;EACzB;EACAK,QAAQA,CAACtC,MAAM,EAAE;IACb,IAAIuC,EAAE;IACN,MAAMC,YAAY,GAAGjC,MAAM,CAACC,MAAM,CAAC;MAAEiC,OAAO,EAAE;IAAM,CAAC,EAAEzC,MAAM,CAACK,OAAO,CAAC;IACtE,MAAMqC,KAAK,GAAG,CAACH,EAAE,GAAGvC,MAAM,CAAC0C,KAAK,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACjC,OAAO,CAACX,KAAK,CAAC;MAAEc,CAAC,EAAE,IAAI,CAACJ,OAAO,CAACI,CAAC;MAAEC,CAAC,EAAE,IAAI,CAACL,OAAO,CAACK,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACN,OAAO,CAACM,KAAK;MAAEN,OAAO,EAAEmC;IAAa,CAAC,CAAC;IACjL,MAAMG,CAAC,GAAGpC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAAEoC,UAAU,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,gBAAgB,EAAE,KAAK;MAAEhC,eAAe,EAAE;IAAM,CAAC,EAAEd,MAAM,CAAC,EAAE;MAAEK,OAAO,EAAEmC;IAAa,CAAC,CAAC;IAC5J,MAAMO,GAAG,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC7BL,CAAC,CAACM,MAAM,CAAC1B,OAAO,CAAE2B,KAAK,IAAK;MACxBA,KAAK,CACArB,UAAU,CAACkB,GAAG,CAAC,CACfI,QAAQ,CAACT,KAAK,CAAC,CACfU,YAAY,CAAC,CAAC,CACd7B,OAAO,CAAE8B,QAAQ,IAAKA,QAAQ,CAACF,QAAQ,CAACT,KAAK,CAAC,CAAC;MACpD,IAAI,CAACtC,UAAU,CAACkD,IAAI,CAACJ,KAAK,CAAC;IAC/B,CAAC,CAAC;IACF,IAAI,CAAChD,UAAU,CAACoD,IAAI,CAACZ,KAAK,CAAC;IAC3B,IAAI,CAACvC,cAAc,CAACmD,IAAI,CAACX,CAAC,CAAC;IAC3B,OAAOD,KAAK;EAChB;EACAa,SAASA,CAACN,MAAM,EAAE;IACd,MAAMF,GAAG,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC7BC,MAAM,CAAC1B,OAAO,CAAE2B,KAAK,IAAK;MACtBA,KAAK,CAACrB,UAAU,CAACkB,GAAG,CAAC;MACrB,IAAI,CAAC3C,UAAU,CAACkD,IAAI,CAACJ,KAAK,CAAC;IAC/B,CAAC,CAAC;EACN;EACAM,MAAMA,CAAA,EAAG;IACL,MAAMC,cAAc,GAAG,IAAI,CAACpD,OAAO,CAACa,OAAO;IAC3C,IAAIwC,YAAY,GAAG,CAAC;IACpB,MAAMC,SAAS,GAAG,IAAIlE,SAAS,CAACgE,cAAc,CAAC;IAC/C,IAAI,CAACE,SAAS,GAAGA,SAAS;IAC1B,IAAIjD,CAAC,GAAG,IAAI,CAACL,OAAO,CAACK,CAAC;IACtB,IAAIkD,MAAM,GAAG,CAAC;IACd,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAI,CAAC3D,UAAU,CAACqB,OAAO,CAAC,CAACuC,IAAI,EAAEC,KAAK,KAAK;MACrCrD,CAAC,GAAGA,CAAC,GAAGoD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC7D,cAAc,CAAC4D,KAAK,CAAC,CAACnB,UAAU,CAAC;MACzDkB,IAAI,CAACpC,IAAI,CAAChB,CAAC,CAAC;MACZA,CAAC,GAAGA,CAAC,GAAGoD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC7D,cAAc,CAAC4D,KAAK,CAAC,CAAClB,UAAU,CAAC;MACzDnC,CAAC,GAAGA,CAAC,GAAGoD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC3D,OAAO,CAACO,kBAAkB,CAAC;MACnD,IAAI,IAAI,CAACT,cAAc,CAAC4D,KAAK,CAAC,CAACjB,gBAAgB,EAAE;QAC7Ce,kBAAkB,CAACP,IAAI,CAAC;UAAE5C,CAAC;UAAEgC,KAAK,EAAEoB;QAAK,CAAC,CAAC;QAC3CpD,CAAC,IAAI,EAAE;MACX;MACAkD,MAAM,GAAGK,IAAI,CAACC,GAAG,CAACN,MAAM,EAAEE,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IACF,IAAI,CAAC/D,UAAU,CAACmB,OAAO,CAAE2B,KAAK,IAAK;MAC/BA,KAAK,CAACE,YAAY,CAAC,CAAC,CAAC7B,OAAO,CAAE8B,QAAQ,IAAK;QACvC,MAAMX,KAAK,GAAGW,QAAQ,CAACe,QAAQ,CAAC,CAAC;QACjC,IAAI1B,KAAK,EACLW,QAAQ,CAACF,QAAQ,CAACT,KAAK,CAAC;MAChC,CAAC,CAAC;IACN,CAAC,CAAC;IACFiB,SAAS,CAACU,UAAU,CAAC,IAAI,CAACjE,UAAU,CAAC;IACrC,IAAI,CAACF,UAAU,CAACqB,OAAO,CAAEuC,IAAI,IAAKA,IAAI,CAACQ,aAAa,CAACV,MAAM,CAAC,CAAC;IAC7D,IAAI,IAAI,CAACvD,OAAO,CAACQ,SAAS,IAAI,IAAI,CAACT,UAAU,CAACiC,MAAM,GAAG,CAAC,EAAE;MACtDqB,YAAY,GAAGC,SAAS,CAACY,yBAAyB,CAAC,IAAI,CAACnE,UAAU,CAAC;MACnE,IAAI,CAACC,OAAO,CAACM,KAAK,GAAG+C,YAAY,GAAG/D,KAAK,CAAC6E,YAAY,IAAIZ,MAAM,GAAG,IAAI,CAACvD,OAAO,CAACI,CAAC,CAAC;MAClF,IAAI,CAACP,UAAU,CAACqB,OAAO,CAAEuC,IAAI,IAAK;QAC9BA,IAAI,CAACW,QAAQ,CAAC,IAAI,CAACpE,OAAO,CAACM,KAAK,CAAC;MACrC,CAAC,CAAC;IACN,CAAC,MACI;MACD+C,YAAY,GAAG,IAAI,CAACrD,OAAO,CAACY,SAAS,GAC/B,IAAI,CAACZ,OAAO,CAACM,KAAK,IAAIiD,MAAM,GAAG,IAAI,CAACvD,OAAO,CAACI,CAAC,CAAC,GAC9C,IAAI,CAACJ,OAAO,CAACM,KAAK,IAAIiD,MAAM,GAAG,IAAI,CAACvD,OAAO,CAACI,CAAC,CAAC,GAAGd,KAAK,CAAC+E,cAAc;IAC/E;IACA,IAAI,IAAI,CAACtE,UAAU,CAACiC,MAAM,GAAG,CAAC,EAAE;MAC5BsB,SAAS,CAACH,MAAM,CAAC,IAAI,CAACpD,UAAU,EAAE,IAAI,CAACC,OAAO,CAACS,eAAe,GAAG,CAAC,GAAG4C,YAAY,EAAE,IAAI,CAACrD,OAAO,CAACe,aAAa,CAAC;IAClH;IACAuC,SAAS,CAACgB,UAAU,CAAC,CAAC;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvE,OAAO,CAACW,gBAAgB,EAAE4D,CAAC,EAAE,EAAE;MACpDjB,SAAS,CAACkB,IAAI,CAACpB,cAAc,CAAC;IAClC;IACA,IAAI,CAACG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACiB,KAAK,GAAGpE,CAAC;IACdf,KAAK,CAACoF,kBAAkB,CAAC,IAAI,CAAC7E,UAAU,CAAC;EAC7C;EACA8E,cAAcA,CAAA,EAAG;IACb,IAAIzC,EAAE;IACN,OAAO,IAAIhD,WAAW,CAAC,IAAI,CAACc,OAAO,CAACI,CAAC,EAAE,IAAI,CAACJ,OAAO,CAACK,CAAC,EAAE,IAAI,CAACL,OAAO,CAACM,KAAK,EAAE,CAAC,CAAC4B,EAAE,GAAG,IAAI,CAACuC,KAAK,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAClC,OAAO,CAACK,CAAC,CAAC;EACvJ;EACAuE,IAAIA,CAAA,EAAG;IACH,MAAMlC,GAAG,GAAG,IAAI,CAACmC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACvB,SAAS,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACkB,KAAK,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;MAC5E,MAAM,IAAIjE,YAAY,CAAC,aAAa,EAAE,uCAAuC,CAAC;IAClF;IACA,IAAI,CAACuF,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC9E,OAAO,CAACU,cAAc,EAAE;MAC7BtB,SAAS,CAAC2F,aAAa,CAACrC,GAAG,EAAE,IAAI,CAACY,SAAS,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACvD,OAAO,CAACK,CAAC,EAAE,IAAI,CAACoE,KAAK,CAAC;IACzF;IACA,IAAI,CAACjB,kBAAkB,CAACtC,OAAO,CAAE8D,CAAC,IAAK;MACnC,IAAI,CAACjF,UAAU,CAACmB,OAAO,CAAE2B,KAAK,IAAK;QAC/BA,KAAK,CAACE,YAAY,CAAC,CAAC,CAAC7B,OAAO,CAAE8B,QAAQ,IAAK;UACvC,IAAIA,QAAQ,CAACe,QAAQ,CAAC,CAAC,KAAKiB,CAAC,CAAC3C,KAAK,EAC/BhD,IAAI,CAAC4F,WAAW,CAACvC,GAAG,EAAEM,QAAQ,EAAEgC,CAAC,CAAC3E,CAAC,CAAC;QAC5C,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}