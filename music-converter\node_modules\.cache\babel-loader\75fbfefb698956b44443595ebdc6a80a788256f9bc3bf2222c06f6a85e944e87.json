{"ast": null, "code": "import { Formatter } from './formatter.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { Voice } from './voice.js';\nexport class NoteSubGroup extends Modifier {\n  static get CATEGORY() {\n    return \"NoteSubGroup\";\n  }\n  static format(groups, state) {\n    if (!groups || groups.length === 0) return false;\n    let width = 0;\n    for (let i = 0; i < groups.length; ++i) {\n      const group = groups[i];\n      group.preFormat();\n      width += group.getWidth();\n    }\n    state.leftShift += width;\n    return true;\n  }\n  constructor(subNotes) {\n    super();\n    this.preFormatted = false;\n    this.position = Modifier.Position.LEFT;\n    this.subNotes = subNotes;\n    this.subNotes.forEach(subNote => {\n      subNote.setIgnoreTicks(false);\n    });\n    this.width = 0;\n    this.formatter = new Formatter();\n    this.voice = new Voice({\n      numBeats: 4,\n      beatValue: 4,\n      resolution: Tables.RESOLUTION\n    }).setStrict(false);\n    this.voice.addTickables(this.subNotes);\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    this.formatter.joinVoices([this.voice]).format([this.voice], 0);\n    this.setWidth(this.formatter.getMinTotalWidth());\n    this.preFormatted = true;\n  }\n  setWidth(width) {\n    this.width = width;\n    return this;\n  }\n  getWidth() {\n    return this.width;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    this.alignSubNotesWithNote(this.subNotes, note);\n    this.subNotes.forEach(subNote => subNote.setContext(ctx).drawWithStyle());\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Modifier", "Tables", "Voice", "NoteSubGroup", "CATEGORY", "format", "groups", "state", "length", "width", "i", "group", "preFormat", "getWidth", "leftShift", "constructor", "subNotes", "preFormatted", "position", "Position", "LEFT", "for<PERSON>ach", "subNote", "setIgnoreTicks", "formatter", "voice", "numBeats", "beatValue", "resolution", "RESOLUTION", "setStrict", "addTickables", "joinVoices", "<PERSON><PERSON><PERSON><PERSON>", "getMinTotalWidth", "draw", "ctx", "checkContext", "note", "checkAttachedNote", "setRendered", "alignSubNotesWithNote", "setContext", "drawWithStyle"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/notesubgroup.js"], "sourcesContent": ["import { Formatter } from './formatter.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { Voice } from './voice.js';\nexport class NoteSubGroup extends Modifier {\n    static get CATEGORY() {\n        return \"NoteSubGroup\";\n    }\n    static format(groups, state) {\n        if (!groups || groups.length === 0)\n            return false;\n        let width = 0;\n        for (let i = 0; i < groups.length; ++i) {\n            const group = groups[i];\n            group.preFormat();\n            width += group.getWidth();\n        }\n        state.leftShift += width;\n        return true;\n    }\n    constructor(subNotes) {\n        super();\n        this.preFormatted = false;\n        this.position = Modifier.Position.LEFT;\n        this.subNotes = subNotes;\n        this.subNotes.forEach((subNote) => {\n            subNote.setIgnoreTicks(false);\n        });\n        this.width = 0;\n        this.formatter = new Formatter();\n        this.voice = new Voice({\n            numBeats: 4,\n            beatValue: 4,\n            resolution: Tables.RESOLUTION,\n        }).setStrict(false);\n        this.voice.addTickables(this.subNotes);\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        this.formatter.joinVoices([this.voice]).format([this.voice], 0);\n        this.setWidth(this.formatter.getMinTotalWidth());\n        this.preFormatted = true;\n    }\n    setWidth(width) {\n        this.width = width;\n        return this;\n    }\n    getWidth() {\n        return this.width;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        this.alignSubNotesWithNote(this.subNotes, note);\n        this.subNotes.forEach((subNote) => subNote.setContext(ctx).drawWithStyle());\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,YAAY;AAClC,OAAO,MAAMC,YAAY,SAASH,QAAQ,CAAC;EACvC,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,OAAOC,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACzB,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACE,MAAM,KAAK,CAAC,EAC9B,OAAO,KAAK;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACE,MAAM,EAAE,EAAEE,CAAC,EAAE;MACpC,MAAMC,KAAK,GAAGL,MAAM,CAACI,CAAC,CAAC;MACvBC,KAAK,CAACC,SAAS,CAAC,CAAC;MACjBH,KAAK,IAAIE,KAAK,CAACE,QAAQ,CAAC,CAAC;IAC7B;IACAN,KAAK,CAACO,SAAS,IAAIL,KAAK;IACxB,OAAO,IAAI;EACf;EACAM,WAAWA,CAACC,QAAQ,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,QAAQ,GAAGlB,QAAQ,CAACmB,QAAQ,CAACC,IAAI;IACtC,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACA,QAAQ,CAACK,OAAO,CAAEC,OAAO,IAAK;MAC/BA,OAAO,CAACC,cAAc,CAAC,KAAK,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAACd,KAAK,GAAG,CAAC;IACd,IAAI,CAACe,SAAS,GAAG,IAAIzB,SAAS,CAAC,CAAC;IAChC,IAAI,CAAC0B,KAAK,GAAG,IAAIvB,KAAK,CAAC;MACnBwB,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE3B,MAAM,CAAC4B;IACvB,CAAC,CAAC,CAACC,SAAS,CAAC,KAAK,CAAC;IACnB,IAAI,CAACL,KAAK,CAACM,YAAY,CAAC,IAAI,CAACf,QAAQ,CAAC;EAC1C;EACAJ,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACK,YAAY,EACjB;IACJ,IAAI,CAACO,SAAS,CAACQ,UAAU,CAAC,CAAC,IAAI,CAACP,KAAK,CAAC,CAAC,CAACpB,MAAM,CAAC,CAAC,IAAI,CAACoB,KAAK,CAAC,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAACT,SAAS,CAACU,gBAAgB,CAAC,CAAC,CAAC;IAChD,IAAI,CAACjB,YAAY,GAAG,IAAI;EAC5B;EACAgB,QAAQA,CAACxB,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAI,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACJ,KAAK;EACrB;EACA0B,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMC,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACzB,QAAQ,EAAEsB,IAAI,CAAC;IAC/C,IAAI,CAACtB,QAAQ,CAACK,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACoB,UAAU,CAACN,GAAG,CAAC,CAACO,aAAa,CAAC,CAAC,CAAC;EAC/E;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}