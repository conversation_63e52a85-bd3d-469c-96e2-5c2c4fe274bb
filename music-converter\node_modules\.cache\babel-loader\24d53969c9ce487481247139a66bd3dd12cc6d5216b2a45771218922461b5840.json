{"ast": null, "code": "import { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Bend extends Modifier {\n  static get CATEGORY() {\n    return \"Bend\";\n  }\n  static get UP() {\n    return 0;\n  }\n  static get DOWN() {\n    return 1;\n  }\n  static format(bends, state) {\n    if (!bends || bends.length === 0) return false;\n    let lastWidth = 0;\n    for (let i = 0; i < bends.length; ++i) {\n      const bend = bends[i];\n      const note = bend.checkAttachedNote();\n      if (isTabNote(note)) {\n        const stringPos = note.leastString() - 1;\n        if (state.topTextLine < stringPos) {\n          state.topTextLine = stringPos;\n        }\n      }\n      bend.setXShift(lastWidth);\n      lastWidth = bend.getWidth();\n      bend.setTextLine(state.topTextLine);\n    }\n    state.rightShift += lastWidth;\n    state.topTextLine += 1;\n    return true;\n  }\n  setStyleLine(style) {\n    this.styleLine = style;\n    return this;\n  }\n  getStyleLine() {\n    return this.styleLine;\n  }\n  constructor(phrase) {\n    super();\n    this.styleLine = Metrics.getStyle('Bend.line');\n    this.xShift = 0;\n    this.tap = '';\n    this.renderOptions = {\n      bendWidth: 8,\n      releaseWidth: 8\n    };\n    this.phrase = phrase;\n    this.updateWidth();\n  }\n  setXShift(value) {\n    this.xShift = value;\n    this.updateWidth();\n    return this;\n  }\n  setTap(value) {\n    this.tap = value;\n    return this;\n  }\n  getTextHeight() {\n    const element = new Element(\"Bend\");\n    element.setText(this.phrase[0].text);\n    return element.getHeight();\n  }\n  updateWidth() {\n    const measureText = text => {\n      const element = new Element(\"Bend\");\n      element.setText(text);\n      return element.getWidth();\n    };\n    let totalWidth = 0;\n    for (let i = 0; i < this.phrase.length; ++i) {\n      const bend = this.phrase[i];\n      if (bend.width !== undefined) {\n        totalWidth += bend.width;\n      } else {\n        const additionalWidth = bend.type === Bend.UP ? this.renderOptions.bendWidth : this.renderOptions.releaseWidth;\n        bend.width = Math.max(additionalWidth, measureText(bend.text)) + 3;\n        bend.drawWidth = bend.width / 2;\n        totalWidth += bend.width;\n      }\n    }\n    this.setWidth(totalWidth + this.xShift);\n    return this;\n  }\n  draw() {\n    var _a;\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(Modifier.Position.RIGHT, this.index);\n    start.x += 3;\n    start.y += 0.5;\n    const xShift = this.xShift;\n    const stave = note.checkStave();\n    const spacing = stave.getSpacingBetweenLines();\n    const lowestY = note.getYs().reduce((a, b) => a < b ? a : b);\n    const bendHeight = start.y - ((this.textLine + 1) * spacing + start.y - lowestY) + 3;\n    const annotationY = start.y - ((this.textLine + 1) * spacing + start.y - lowestY) - 1;\n    const renderBend = (x, y, width, height) => {\n      const cpX = x + width;\n      const cpY = y;\n      this.applyStyle(ctx, this.styleLine);\n      ctx.beginPath();\n      ctx.moveTo(x, y);\n      ctx.quadraticCurveTo(cpX, cpY, x + width, height);\n      ctx.stroke();\n    };\n    const renderRelease = (x, y, width, height) => {\n      this.applyStyle(ctx, this.styleLine);\n      ctx.beginPath();\n      ctx.moveTo(x, height);\n      ctx.quadraticCurveTo(x + width, height, x + width, y);\n      ctx.stroke();\n    };\n    const renderArrowHead = (x, y, direction) => {\n      const width = 4;\n      const yBase = y + width * direction;\n      ctx.beginPath();\n      ctx.moveTo(x, y);\n      ctx.lineTo(x - width, yBase);\n      ctx.lineTo(x + width, yBase);\n      ctx.closePath();\n      ctx.fill();\n    };\n    const renderText = (x, text) => {\n      ctx.setFont(this.fontInfo);\n      const renderX = x - ctx.measureText(text).width / 2;\n      ctx.fillText(text, renderX, annotationY);\n    };\n    let lastBend = undefined;\n    let lastBendDrawWidth = 0;\n    let lastDrawnWidth = 0;\n    if ((_a = this.tap) === null || _a === void 0 ? void 0 : _a.length) {\n      const tapStart = note.getModifierStartXY(Modifier.Position.CENTER, this.index);\n      renderText(tapStart.x, this.tap);\n    }\n    for (let i = 0; i < this.phrase.length; ++i) {\n      const bend = this.phrase[i];\n      if (!bend.drawWidth) bend.drawWidth = 0;\n      if (i === 0) bend.drawWidth += xShift;\n      lastDrawnWidth = bend.drawWidth + lastBendDrawWidth - (i === 1 ? xShift : 0);\n      if (bend.type === Bend.UP) {\n        if (lastBend && lastBend.type === Bend.UP) {\n          renderArrowHead(start.x, bendHeight, +1);\n        }\n        renderBend(start.x, start.y, lastDrawnWidth, bendHeight);\n      }\n      if (bend.type === Bend.DOWN) {\n        if (lastBend && lastBend.type === Bend.UP) {\n          renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n        }\n        if (lastBend && lastBend.type === Bend.DOWN) {\n          renderArrowHead(start.x, start.y, -1);\n          renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n        }\n        if (!lastBend) {\n          lastDrawnWidth = bend.drawWidth;\n          renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n        }\n      }\n      renderText(start.x + lastDrawnWidth, bend.text);\n      lastBend = bend;\n      lastBendDrawWidth = bend.drawWidth;\n      lastBend.x = start.x;\n      start.x += lastDrawnWidth;\n    }\n    if (!lastBend || lastBend.x === undefined) {\n      throw new RuntimeError('NoLastBendForBend', 'Internal error.');\n    }\n    if (lastBend.type === Bend.UP) {\n      renderArrowHead(lastBend.x + lastDrawnWidth, bendHeight, +1);\n    } else if (lastBend.type === Bend.DOWN) {\n      renderArrowHead(lastBend.x + lastDrawnWidth, start.y, -1);\n    }\n  }\n}", "map": {"version": 3, "names": ["Element", "Metrics", "Modifier", "isTabNote", "RuntimeError", "Bend", "CATEGORY", "UP", "DOWN", "format", "bends", "state", "length", "lastWidth", "i", "bend", "note", "checkAttachedNote", "stringPos", "leastString", "topTextLine", "setXShift", "getWidth", "setTextLine", "rightShift", "setStyleLine", "style", "styleLine", "getStyleLine", "constructor", "phrase", "getStyle", "xShift", "tap", "renderOptions", "bendWidth", "releaseWidth", "updateWidth", "value", "setTap", "getTextHeight", "element", "setText", "text", "getHeight", "measureText", "totalWidth", "width", "undefined", "additionalWidth", "type", "Math", "max", "drawWidth", "<PERSON><PERSON><PERSON><PERSON>", "draw", "_a", "ctx", "checkContext", "setRendered", "start", "getModifierStartXY", "Position", "RIGHT", "index", "x", "y", "stave", "checkStave", "spacing", "getSpacingBetweenLines", "lowestY", "getYs", "reduce", "a", "b", "bendHeight", "textLine", "annotationY", "renderBend", "height", "cpX", "cpY", "applyStyle", "beginPath", "moveTo", "quadraticCurveTo", "stroke", "renderRelease", "renderArrowHead", "direction", "yBase", "lineTo", "closePath", "fill", "renderText", "setFont", "fontInfo", "renderX", "fillText", "lastBend", "lastBendDrawWidth", "lastDrawnWidth", "tapStart", "CENTER"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/bend.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { isTabNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class Bend extends Modifier {\n    static get CATEGORY() {\n        return \"Bend\";\n    }\n    static get UP() {\n        return 0;\n    }\n    static get DOWN() {\n        return 1;\n    }\n    static format(bends, state) {\n        if (!bends || bends.length === 0)\n            return false;\n        let lastWidth = 0;\n        for (let i = 0; i < bends.length; ++i) {\n            const bend = bends[i];\n            const note = bend.checkAttachedNote();\n            if (isTabNote(note)) {\n                const stringPos = note.leastString() - 1;\n                if (state.topTextLine < stringPos) {\n                    state.topTextLine = stringPos;\n                }\n            }\n            bend.setXShift(lastWidth);\n            lastWidth = bend.getWidth();\n            bend.setTextLine(state.topTextLine);\n        }\n        state.rightShift += lastWidth;\n        state.topTextLine += 1;\n        return true;\n    }\n    setStyleLine(style) {\n        this.styleLine = style;\n        return this;\n    }\n    getStyleLine() {\n        return this.styleLine;\n    }\n    constructor(phrase) {\n        super();\n        this.styleLine = Metrics.getStyle('Bend.line');\n        this.xShift = 0;\n        this.tap = '';\n        this.renderOptions = {\n            bendWidth: 8,\n            releaseWidth: 8,\n        };\n        this.phrase = phrase;\n        this.updateWidth();\n    }\n    setXShift(value) {\n        this.xShift = value;\n        this.updateWidth();\n        return this;\n    }\n    setTap(value) {\n        this.tap = value;\n        return this;\n    }\n    getTextHeight() {\n        const element = new Element(\"Bend\");\n        element.setText(this.phrase[0].text);\n        return element.getHeight();\n    }\n    updateWidth() {\n        const measureText = (text) => {\n            const element = new Element(\"Bend\");\n            element.setText(text);\n            return element.getWidth();\n        };\n        let totalWidth = 0;\n        for (let i = 0; i < this.phrase.length; ++i) {\n            const bend = this.phrase[i];\n            if (bend.width !== undefined) {\n                totalWidth += bend.width;\n            }\n            else {\n                const additionalWidth = bend.type === Bend.UP ? this.renderOptions.bendWidth : this.renderOptions.releaseWidth;\n                bend.width = Math.max(additionalWidth, measureText(bend.text)) + 3;\n                bend.drawWidth = bend.width / 2;\n                totalWidth += bend.width;\n            }\n        }\n        this.setWidth(totalWidth + this.xShift);\n        return this;\n    }\n    draw() {\n        var _a;\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(Modifier.Position.RIGHT, this.index);\n        start.x += 3;\n        start.y += 0.5;\n        const xShift = this.xShift;\n        const stave = note.checkStave();\n        const spacing = stave.getSpacingBetweenLines();\n        const lowestY = note.getYs().reduce((a, b) => (a < b ? a : b));\n        const bendHeight = start.y - ((this.textLine + 1) * spacing + start.y - lowestY) + 3;\n        const annotationY = start.y - ((this.textLine + 1) * spacing + start.y - lowestY) - 1;\n        const renderBend = (x, y, width, height) => {\n            const cpX = x + width;\n            const cpY = y;\n            this.applyStyle(ctx, this.styleLine);\n            ctx.beginPath();\n            ctx.moveTo(x, y);\n            ctx.quadraticCurveTo(cpX, cpY, x + width, height);\n            ctx.stroke();\n        };\n        const renderRelease = (x, y, width, height) => {\n            this.applyStyle(ctx, this.styleLine);\n            ctx.beginPath();\n            ctx.moveTo(x, height);\n            ctx.quadraticCurveTo(x + width, height, x + width, y);\n            ctx.stroke();\n        };\n        const renderArrowHead = (x, y, direction) => {\n            const width = 4;\n            const yBase = y + width * direction;\n            ctx.beginPath();\n            ctx.moveTo(x, y);\n            ctx.lineTo(x - width, yBase);\n            ctx.lineTo(x + width, yBase);\n            ctx.closePath();\n            ctx.fill();\n        };\n        const renderText = (x, text) => {\n            ctx.setFont(this.fontInfo);\n            const renderX = x - ctx.measureText(text).width / 2;\n            ctx.fillText(text, renderX, annotationY);\n        };\n        let lastBend = undefined;\n        let lastBendDrawWidth = 0;\n        let lastDrawnWidth = 0;\n        if ((_a = this.tap) === null || _a === void 0 ? void 0 : _a.length) {\n            const tapStart = note.getModifierStartXY(Modifier.Position.CENTER, this.index);\n            renderText(tapStart.x, this.tap);\n        }\n        for (let i = 0; i < this.phrase.length; ++i) {\n            const bend = this.phrase[i];\n            if (!bend.drawWidth)\n                bend.drawWidth = 0;\n            if (i === 0)\n                bend.drawWidth += xShift;\n            lastDrawnWidth = bend.drawWidth + lastBendDrawWidth - (i === 1 ? xShift : 0);\n            if (bend.type === Bend.UP) {\n                if (lastBend && lastBend.type === Bend.UP) {\n                    renderArrowHead(start.x, bendHeight, +1);\n                }\n                renderBend(start.x, start.y, lastDrawnWidth, bendHeight);\n            }\n            if (bend.type === Bend.DOWN) {\n                if (lastBend && lastBend.type === Bend.UP) {\n                    renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n                }\n                if (lastBend && lastBend.type === Bend.DOWN) {\n                    renderArrowHead(start.x, start.y, -1);\n                    renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n                }\n                if (!lastBend) {\n                    lastDrawnWidth = bend.drawWidth;\n                    renderRelease(start.x, start.y, lastDrawnWidth, bendHeight);\n                }\n            }\n            renderText(start.x + lastDrawnWidth, bend.text);\n            lastBend = bend;\n            lastBendDrawWidth = bend.drawWidth;\n            lastBend.x = start.x;\n            start.x += lastDrawnWidth;\n        }\n        if (!lastBend || lastBend.x === undefined) {\n            throw new RuntimeError('NoLastBendForBend', 'Internal error.');\n        }\n        if (lastBend.type === Bend.UP) {\n            renderArrowHead(lastBend.x + lastDrawnWidth, bendHeight, +1);\n        }\n        else if (lastBend.type === Bend.DOWN) {\n            renderArrowHead(lastBend.x + lastDrawnWidth, start.y, -1);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,IAAI,SAASH,QAAQ,CAAC;EAC/B,WAAWI,QAAQA,CAAA,EAAG;IAClB,OAAO,MAAM;EACjB;EACA,WAAWC,EAAEA,CAAA,EAAG;IACZ,OAAO,CAAC;EACZ;EACA,WAAWC,IAAIA,CAAA,EAAG;IACd,OAAO,CAAC;EACZ;EACA,OAAOC,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK,CAAC,EAC5B,OAAO,KAAK;IAChB,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACE,MAAM,EAAE,EAAEE,CAAC,EAAE;MACnC,MAAMC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;MACrB,MAAME,IAAI,GAAGD,IAAI,CAACE,iBAAiB,CAAC,CAAC;MACrC,IAAId,SAAS,CAACa,IAAI,CAAC,EAAE;QACjB,MAAME,SAAS,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC,GAAG,CAAC;QACxC,IAAIR,KAAK,CAACS,WAAW,GAAGF,SAAS,EAAE;UAC/BP,KAAK,CAACS,WAAW,GAAGF,SAAS;QACjC;MACJ;MACAH,IAAI,CAACM,SAAS,CAACR,SAAS,CAAC;MACzBA,SAAS,GAAGE,IAAI,CAACO,QAAQ,CAAC,CAAC;MAC3BP,IAAI,CAACQ,WAAW,CAACZ,KAAK,CAACS,WAAW,CAAC;IACvC;IACAT,KAAK,CAACa,UAAU,IAAIX,SAAS;IAC7BF,KAAK,CAACS,WAAW,IAAI,CAAC;IACtB,OAAO,IAAI;EACf;EACAK,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,SAAS,GAAGD,KAAK;IACtB,OAAO,IAAI;EACf;EACAE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS;EACzB;EACAE,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,SAAS,GAAG1B,OAAO,CAAC8B,QAAQ,CAAC,WAAW,CAAC;IAC9C,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,aAAa,GAAG;MACjBC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE;IAClB,CAAC;IACD,IAAI,CAACN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACO,WAAW,CAAC,CAAC;EACtB;EACAhB,SAASA,CAACiB,KAAK,EAAE;IACb,IAAI,CAACN,MAAM,GAAGM,KAAK;IACnB,IAAI,CAACD,WAAW,CAAC,CAAC;IAClB,OAAO,IAAI;EACf;EACAE,MAAMA,CAACD,KAAK,EAAE;IACV,IAAI,CAACL,GAAG,GAAGK,KAAK;IAChB,OAAO,IAAI;EACf;EACAE,aAAaA,CAAA,EAAG;IACZ,MAAMC,OAAO,GAAG,IAAIzC,OAAO,CAAC,MAAM,CAAC;IACnCyC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,CAACa,IAAI,CAAC;IACpC,OAAOF,OAAO,CAACG,SAAS,CAAC,CAAC;EAC9B;EACAP,WAAWA,CAAA,EAAG;IACV,MAAMQ,WAAW,GAAIF,IAAI,IAAK;MAC1B,MAAMF,OAAO,GAAG,IAAIzC,OAAO,CAAC,MAAM,CAAC;MACnCyC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAC;MACrB,OAAOF,OAAO,CAACnB,QAAQ,CAAC,CAAC;IAC7B,CAAC;IACD,IAAIwB,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgB,MAAM,CAAClB,MAAM,EAAE,EAAEE,CAAC,EAAE;MACzC,MAAMC,IAAI,GAAG,IAAI,CAACe,MAAM,CAAChB,CAAC,CAAC;MAC3B,IAAIC,IAAI,CAACgC,KAAK,KAAKC,SAAS,EAAE;QAC1BF,UAAU,IAAI/B,IAAI,CAACgC,KAAK;MAC5B,CAAC,MACI;QACD,MAAME,eAAe,GAAGlC,IAAI,CAACmC,IAAI,KAAK7C,IAAI,CAACE,EAAE,GAAG,IAAI,CAAC2B,aAAa,CAACC,SAAS,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY;QAC9GrB,IAAI,CAACgC,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACH,eAAe,EAAEJ,WAAW,CAAC9B,IAAI,CAAC4B,IAAI,CAAC,CAAC,GAAG,CAAC;QAClE5B,IAAI,CAACsC,SAAS,GAAGtC,IAAI,CAACgC,KAAK,GAAG,CAAC;QAC/BD,UAAU,IAAI/B,IAAI,CAACgC,KAAK;MAC5B;IACJ;IACA,IAAI,CAACO,QAAQ,CAACR,UAAU,GAAG,IAAI,CAACd,MAAM,CAAC;IACvC,OAAO,IAAI;EACf;EACAuB,IAAIA,CAAA,EAAG;IACH,IAAIC,EAAE;IACN,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAM1C,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAAC0C,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG5C,IAAI,CAAC6C,kBAAkB,CAAC3D,QAAQ,CAAC4D,QAAQ,CAACC,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;IAC1EJ,KAAK,CAACK,CAAC,IAAI,CAAC;IACZL,KAAK,CAACM,CAAC,IAAI,GAAG;IACd,MAAMlC,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,MAAMmC,KAAK,GAAGnD,IAAI,CAACoD,UAAU,CAAC,CAAC;IAC/B,MAAMC,OAAO,GAAGF,KAAK,CAACG,sBAAsB,CAAC,CAAC;IAC9C,MAAMC,OAAO,GAAGvD,IAAI,CAACwD,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;IAC9D,MAAMC,UAAU,GAAGhB,KAAK,CAACM,CAAC,IAAI,CAAC,IAAI,CAACW,QAAQ,GAAG,CAAC,IAAIR,OAAO,GAAGT,KAAK,CAACM,CAAC,GAAGK,OAAO,CAAC,GAAG,CAAC;IACpF,MAAMO,WAAW,GAAGlB,KAAK,CAACM,CAAC,IAAI,CAAC,IAAI,CAACW,QAAQ,GAAG,CAAC,IAAIR,OAAO,GAAGT,KAAK,CAACM,CAAC,GAAGK,OAAO,CAAC,GAAG,CAAC;IACrF,MAAMQ,UAAU,GAAGA,CAACd,CAAC,EAAEC,CAAC,EAAEnB,KAAK,EAAEiC,MAAM,KAAK;MACxC,MAAMC,GAAG,GAAGhB,CAAC,GAAGlB,KAAK;MACrB,MAAMmC,GAAG,GAAGhB,CAAC;MACb,IAAI,CAACiB,UAAU,CAAC1B,GAAG,EAAE,IAAI,CAAC9B,SAAS,CAAC;MACpC8B,GAAG,CAAC2B,SAAS,CAAC,CAAC;MACf3B,GAAG,CAAC4B,MAAM,CAACpB,CAAC,EAAEC,CAAC,CAAC;MAChBT,GAAG,CAAC6B,gBAAgB,CAACL,GAAG,EAAEC,GAAG,EAAEjB,CAAC,GAAGlB,KAAK,EAAEiC,MAAM,CAAC;MACjDvB,GAAG,CAAC8B,MAAM,CAAC,CAAC;IAChB,CAAC;IACD,MAAMC,aAAa,GAAGA,CAACvB,CAAC,EAAEC,CAAC,EAAEnB,KAAK,EAAEiC,MAAM,KAAK;MAC3C,IAAI,CAACG,UAAU,CAAC1B,GAAG,EAAE,IAAI,CAAC9B,SAAS,CAAC;MACpC8B,GAAG,CAAC2B,SAAS,CAAC,CAAC;MACf3B,GAAG,CAAC4B,MAAM,CAACpB,CAAC,EAAEe,MAAM,CAAC;MACrBvB,GAAG,CAAC6B,gBAAgB,CAACrB,CAAC,GAAGlB,KAAK,EAAEiC,MAAM,EAAEf,CAAC,GAAGlB,KAAK,EAAEmB,CAAC,CAAC;MACrDT,GAAG,CAAC8B,MAAM,CAAC,CAAC;IAChB,CAAC;IACD,MAAME,eAAe,GAAGA,CAACxB,CAAC,EAAEC,CAAC,EAAEwB,SAAS,KAAK;MACzC,MAAM3C,KAAK,GAAG,CAAC;MACf,MAAM4C,KAAK,GAAGzB,CAAC,GAAGnB,KAAK,GAAG2C,SAAS;MACnCjC,GAAG,CAAC2B,SAAS,CAAC,CAAC;MACf3B,GAAG,CAAC4B,MAAM,CAACpB,CAAC,EAAEC,CAAC,CAAC;MAChBT,GAAG,CAACmC,MAAM,CAAC3B,CAAC,GAAGlB,KAAK,EAAE4C,KAAK,CAAC;MAC5BlC,GAAG,CAACmC,MAAM,CAAC3B,CAAC,GAAGlB,KAAK,EAAE4C,KAAK,CAAC;MAC5BlC,GAAG,CAACoC,SAAS,CAAC,CAAC;MACfpC,GAAG,CAACqC,IAAI,CAAC,CAAC;IACd,CAAC;IACD,MAAMC,UAAU,GAAGA,CAAC9B,CAAC,EAAEtB,IAAI,KAAK;MAC5Bc,GAAG,CAACuC,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC;MAC1B,MAAMC,OAAO,GAAGjC,CAAC,GAAGR,GAAG,CAACZ,WAAW,CAACF,IAAI,CAAC,CAACI,KAAK,GAAG,CAAC;MACnDU,GAAG,CAAC0C,QAAQ,CAACxD,IAAI,EAAEuD,OAAO,EAAEpB,WAAW,CAAC;IAC5C,CAAC;IACD,IAAIsB,QAAQ,GAAGpD,SAAS;IACxB,IAAIqD,iBAAiB,GAAG,CAAC;IACzB,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAI,CAAC9C,EAAE,GAAG,IAAI,CAACvB,GAAG,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5C,MAAM,EAAE;MAChE,MAAM2F,QAAQ,GAAGvF,IAAI,CAAC6C,kBAAkB,CAAC3D,QAAQ,CAAC4D,QAAQ,CAAC0C,MAAM,EAAE,IAAI,CAACxC,KAAK,CAAC;MAC9E+B,UAAU,CAACQ,QAAQ,CAACtC,CAAC,EAAE,IAAI,CAAChC,GAAG,CAAC;IACpC;IACA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgB,MAAM,CAAClB,MAAM,EAAE,EAAEE,CAAC,EAAE;MACzC,MAAMC,IAAI,GAAG,IAAI,CAACe,MAAM,CAAChB,CAAC,CAAC;MAC3B,IAAI,CAACC,IAAI,CAACsC,SAAS,EACftC,IAAI,CAACsC,SAAS,GAAG,CAAC;MACtB,IAAIvC,CAAC,KAAK,CAAC,EACPC,IAAI,CAACsC,SAAS,IAAIrB,MAAM;MAC5BsE,cAAc,GAAGvF,IAAI,CAACsC,SAAS,GAAGgD,iBAAiB,IAAIvF,CAAC,KAAK,CAAC,GAAGkB,MAAM,GAAG,CAAC,CAAC;MAC5E,IAAIjB,IAAI,CAACmC,IAAI,KAAK7C,IAAI,CAACE,EAAE,EAAE;QACvB,IAAI6F,QAAQ,IAAIA,QAAQ,CAAClD,IAAI,KAAK7C,IAAI,CAACE,EAAE,EAAE;UACvCkF,eAAe,CAAC7B,KAAK,CAACK,CAAC,EAAEW,UAAU,EAAE,CAAC,CAAC,CAAC;QAC5C;QACAG,UAAU,CAACnB,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAEoC,cAAc,EAAE1B,UAAU,CAAC;MAC5D;MACA,IAAI7D,IAAI,CAACmC,IAAI,KAAK7C,IAAI,CAACG,IAAI,EAAE;QACzB,IAAI4F,QAAQ,IAAIA,QAAQ,CAAClD,IAAI,KAAK7C,IAAI,CAACE,EAAE,EAAE;UACvCiF,aAAa,CAAC5B,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAEoC,cAAc,EAAE1B,UAAU,CAAC;QAC/D;QACA,IAAIwB,QAAQ,IAAIA,QAAQ,CAAClD,IAAI,KAAK7C,IAAI,CAACG,IAAI,EAAE;UACzCiF,eAAe,CAAC7B,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAE,CAAC,CAAC,CAAC;UACrCsB,aAAa,CAAC5B,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAEoC,cAAc,EAAE1B,UAAU,CAAC;QAC/D;QACA,IAAI,CAACwB,QAAQ,EAAE;UACXE,cAAc,GAAGvF,IAAI,CAACsC,SAAS;UAC/BmC,aAAa,CAAC5B,KAAK,CAACK,CAAC,EAAEL,KAAK,CAACM,CAAC,EAAEoC,cAAc,EAAE1B,UAAU,CAAC;QAC/D;MACJ;MACAmB,UAAU,CAACnC,KAAK,CAACK,CAAC,GAAGqC,cAAc,EAAEvF,IAAI,CAAC4B,IAAI,CAAC;MAC/CyD,QAAQ,GAAGrF,IAAI;MACfsF,iBAAiB,GAAGtF,IAAI,CAACsC,SAAS;MAClC+C,QAAQ,CAACnC,CAAC,GAAGL,KAAK,CAACK,CAAC;MACpBL,KAAK,CAACK,CAAC,IAAIqC,cAAc;IAC7B;IACA,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACnC,CAAC,KAAKjB,SAAS,EAAE;MACvC,MAAM,IAAI5C,YAAY,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;IAClE;IACA,IAAIgG,QAAQ,CAAClD,IAAI,KAAK7C,IAAI,CAACE,EAAE,EAAE;MAC3BkF,eAAe,CAACW,QAAQ,CAACnC,CAAC,GAAGqC,cAAc,EAAE1B,UAAU,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC,MACI,IAAIwB,QAAQ,CAAClD,IAAI,KAAK7C,IAAI,CAACG,IAAI,EAAE;MAClCiF,eAAe,CAACW,QAAQ,CAACnC,CAAC,GAAGqC,cAAc,EAAE1C,KAAK,CAACM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}