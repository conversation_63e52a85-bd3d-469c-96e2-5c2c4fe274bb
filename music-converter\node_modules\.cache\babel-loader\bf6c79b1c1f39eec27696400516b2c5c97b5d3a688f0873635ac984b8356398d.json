{"ast": null, "code": "import { RuntimeError } from './util.js';\nclass Index {\n  constructor() {\n    this.id = {};\n    this.type = {};\n    this.class = {};\n  }\n}\nexport class Registry {\n  static getDefaultRegistry() {\n    return Registry.defaultRegistry;\n  }\n  static enableDefaultRegistry(registry) {\n    Registry.defaultRegistry = registry;\n  }\n  static disableDefaultRegistry() {\n    Registry.defaultRegistry = undefined;\n  }\n  constructor() {\n    this.index = new Index();\n  }\n  clear() {\n    this.index = new Index();\n    return this;\n  }\n  setIndexValue(name, value, id, elem) {\n    const index = this.index;\n    if (!index[name][value]) {\n      index[name][value] = {};\n    }\n    index[name][value][id] = elem;\n  }\n  updateIndex({\n    id,\n    name,\n    value,\n    oldValue\n  }) {\n    const elem = this.getElementById(id);\n    if (oldValue !== undefined && this.index[name][oldValue]) {\n      delete this.index[name][oldValue][id];\n    }\n    if (value && elem) {\n      this.setIndexValue(name, value, elem.getAttribute('id'), elem);\n    }\n  }\n  register(elem, id) {\n    id = id || elem.getAttribute('id');\n    if (!id) {\n      throw new RuntimeError(\"Can't add element without `id` attribute to registry\");\n    }\n    elem.setAttribute('id', id);\n    this.setIndexValue('id', id, id, elem);\n    this.updateIndex({\n      id,\n      name: 'type',\n      value: elem.getAttribute('type'),\n      oldValue: undefined\n    });\n    elem.onRegister(this);\n    return this;\n  }\n  getElementById(id) {\n    var _a, _b;\n    return (_b = (_a = this.index.id) === null || _a === void 0 ? void 0 : _a[id]) === null || _b === void 0 ? void 0 : _b[id];\n  }\n  getElementsByAttribute(attribute, value) {\n    const indexAttr = this.index[attribute];\n    if (indexAttr) {\n      const indexAttrVal = indexAttr[value];\n      if (indexAttrVal) {\n        const keys = Object.keys(indexAttrVal);\n        return keys.map(k => indexAttrVal[k]);\n      }\n    }\n    return [];\n  }\n  getElementsByType(type) {\n    return this.getElementsByAttribute('type', type);\n  }\n  getElementsByClass(className) {\n    return this.getElementsByAttribute('class', className);\n  }\n  onUpdate(info) {\n    const allowedNames = ['id', 'type', 'class'];\n    if (allowedNames.includes(info.name)) {\n      this.updateIndex(info);\n    }\n    return this;\n  }\n}", "map": {"version": 3, "names": ["RuntimeError", "Index", "constructor", "id", "type", "class", "Registry", "getDefaultRegistry", "defaultRegistry", "enableDefaultRegistry", "registry", "disableDefaultRegistry", "undefined", "index", "clear", "setIndexValue", "name", "value", "elem", "updateIndex", "oldValue", "getElementById", "getAttribute", "register", "setAttribute", "onRegister", "_a", "_b", "getElementsByAttribute", "attribute", "indexAttr", "indexAttrVal", "keys", "Object", "map", "k", "getElementsByType", "getElementsByClass", "className", "onUpdate", "info", "allowedNames", "includes"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/registry.js"], "sourcesContent": ["import { RuntimeError } from './util.js';\nclass Index {\n    constructor() {\n        this.id = {};\n        this.type = {};\n        this.class = {};\n    }\n}\nexport class Registry {\n    static getDefaultRegistry() {\n        return Registry.defaultRegistry;\n    }\n    static enableDefaultRegistry(registry) {\n        Registry.defaultRegistry = registry;\n    }\n    static disableDefaultRegistry() {\n        Registry.defaultRegistry = undefined;\n    }\n    constructor() {\n        this.index = new Index();\n    }\n    clear() {\n        this.index = new Index();\n        return this;\n    }\n    setIndexValue(name, value, id, elem) {\n        const index = this.index;\n        if (!index[name][value]) {\n            index[name][value] = {};\n        }\n        index[name][value][id] = elem;\n    }\n    updateIndex({ id, name, value, oldValue }) {\n        const elem = this.getElementById(id);\n        if (oldValue !== undefined && this.index[name][oldValue]) {\n            delete this.index[name][oldValue][id];\n        }\n        if (value && elem) {\n            this.setIndexValue(name, value, elem.getAttribute('id'), elem);\n        }\n    }\n    register(elem, id) {\n        id = id || elem.getAttribute('id');\n        if (!id) {\n            throw new RuntimeError(\"Can't add element without `id` attribute to registry\");\n        }\n        elem.setAttribute('id', id);\n        this.setIndexValue('id', id, id, elem);\n        this.updateIndex({ id, name: 'type', value: elem.getAttribute('type'), oldValue: undefined });\n        elem.onRegister(this);\n        return this;\n    }\n    getElementById(id) {\n        var _a, _b;\n        return (_b = (_a = this.index.id) === null || _a === void 0 ? void 0 : _a[id]) === null || _b === void 0 ? void 0 : _b[id];\n    }\n    getElementsByAttribute(attribute, value) {\n        const indexAttr = this.index[attribute];\n        if (indexAttr) {\n            const indexAttrVal = indexAttr[value];\n            if (indexAttrVal) {\n                const keys = Object.keys(indexAttrVal);\n                return keys.map((k) => indexAttrVal[k]);\n            }\n        }\n        return [];\n    }\n    getElementsByType(type) {\n        return this.getElementsByAttribute('type', type);\n    }\n    getElementsByClass(className) {\n        return this.getElementsByAttribute('class', className);\n    }\n    onUpdate(info) {\n        const allowedNames = ['id', 'type', 'class'];\n        if (allowedNames.includes(info.name)) {\n            this.updateIndex(info);\n        }\n        return this;\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,WAAW;AACxC,MAAMC,KAAK,CAAC;EACRC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;IACZ,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;AACJ;AACA,OAAO,MAAMC,QAAQ,CAAC;EAClB,OAAOC,kBAAkBA,CAAA,EAAG;IACxB,OAAOD,QAAQ,CAACE,eAAe;EACnC;EACA,OAAOC,qBAAqBA,CAACC,QAAQ,EAAE;IACnCJ,QAAQ,CAACE,eAAe,GAAGE,QAAQ;EACvC;EACA,OAAOC,sBAAsBA,CAAA,EAAG;IAC5BL,QAAQ,CAACE,eAAe,GAAGI,SAAS;EACxC;EACAV,WAAWA,CAAA,EAAG;IACV,IAAI,CAACW,KAAK,GAAG,IAAIZ,KAAK,CAAC,CAAC;EAC5B;EACAa,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACD,KAAK,GAAG,IAAIZ,KAAK,CAAC,CAAC;IACxB,OAAO,IAAI;EACf;EACAc,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAEd,EAAE,EAAEe,IAAI,EAAE;IACjC,MAAML,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACA,KAAK,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,EAAE;MACrBJ,KAAK,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B;IACAJ,KAAK,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,CAACd,EAAE,CAAC,GAAGe,IAAI;EACjC;EACAC,WAAWA,CAAC;IAAEhB,EAAE;IAAEa,IAAI;IAAEC,KAAK;IAAEG;EAAS,CAAC,EAAE;IACvC,MAAMF,IAAI,GAAG,IAAI,CAACG,cAAc,CAAClB,EAAE,CAAC;IACpC,IAAIiB,QAAQ,KAAKR,SAAS,IAAI,IAAI,CAACC,KAAK,CAACG,IAAI,CAAC,CAACI,QAAQ,CAAC,EAAE;MACtD,OAAO,IAAI,CAACP,KAAK,CAACG,IAAI,CAAC,CAACI,QAAQ,CAAC,CAACjB,EAAE,CAAC;IACzC;IACA,IAAIc,KAAK,IAAIC,IAAI,EAAE;MACf,IAAI,CAACH,aAAa,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,CAACI,YAAY,CAAC,IAAI,CAAC,EAAEJ,IAAI,CAAC;IAClE;EACJ;EACAK,QAAQA,CAACL,IAAI,EAAEf,EAAE,EAAE;IACfA,EAAE,GAAGA,EAAE,IAAIe,IAAI,CAACI,YAAY,CAAC,IAAI,CAAC;IAClC,IAAI,CAACnB,EAAE,EAAE;MACL,MAAM,IAAIH,YAAY,CAAC,sDAAsD,CAAC;IAClF;IACAkB,IAAI,CAACM,YAAY,CAAC,IAAI,EAAErB,EAAE,CAAC;IAC3B,IAAI,CAACY,aAAa,CAAC,IAAI,EAAEZ,EAAE,EAAEA,EAAE,EAAEe,IAAI,CAAC;IACtC,IAAI,CAACC,WAAW,CAAC;MAAEhB,EAAE;MAAEa,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAEC,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC;MAAEF,QAAQ,EAAER;IAAU,CAAC,CAAC;IAC7FM,IAAI,CAACO,UAAU,CAAC,IAAI,CAAC;IACrB,OAAO,IAAI;EACf;EACAJ,cAAcA,CAAClB,EAAE,EAAE;IACf,IAAIuB,EAAE,EAAEC,EAAE;IACV,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACb,KAAK,CAACV,EAAE,MAAM,IAAI,IAAIuB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvB,EAAE,CAAC,MAAM,IAAI,IAAIwB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACxB,EAAE,CAAC;EAC9H;EACAyB,sBAAsBA,CAACC,SAAS,EAAEZ,KAAK,EAAE;IACrC,MAAMa,SAAS,GAAG,IAAI,CAACjB,KAAK,CAACgB,SAAS,CAAC;IACvC,IAAIC,SAAS,EAAE;MACX,MAAMC,YAAY,GAAGD,SAAS,CAACb,KAAK,CAAC;MACrC,IAAIc,YAAY,EAAE;QACd,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,YAAY,CAAC;QACtC,OAAOC,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKJ,YAAY,CAACI,CAAC,CAAC,CAAC;MAC3C;IACJ;IACA,OAAO,EAAE;EACb;EACAC,iBAAiBA,CAAChC,IAAI,EAAE;IACpB,OAAO,IAAI,CAACwB,sBAAsB,CAAC,MAAM,EAAExB,IAAI,CAAC;EACpD;EACAiC,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,OAAO,IAAI,CAACV,sBAAsB,CAAC,OAAO,EAAEU,SAAS,CAAC;EAC1D;EACAC,QAAQA,CAACC,IAAI,EAAE;IACX,MAAMC,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC;IAC5C,IAAIA,YAAY,CAACC,QAAQ,CAACF,IAAI,CAACxB,IAAI,CAAC,EAAE;MAClC,IAAI,CAACG,WAAW,CAACqB,IAAI,CAAC;IAC1B;IACA,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}