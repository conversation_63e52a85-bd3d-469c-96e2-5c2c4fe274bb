[{"C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx": "4", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx": "5", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx": "6", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx": "7"}, {"size": 535, "mtime": 1751849359543, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1751849359790, "results": "10", "hashOfConfig": "9"}, {"size": 726, "mtime": 1751901812342, "results": "11", "hashOfConfig": "9"}, {"size": 7835, "mtime": 1752014289863, "results": "12", "hashOfConfig": "9"}, {"size": 1913, "mtime": 1751929887877, "results": "13", "hashOfConfig": "9"}, {"size": 4796, "mtime": 1751901815242, "results": "14", "hashOfConfig": "9"}, {"size": 624, "mtime": 1752014288163, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f3m73l", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx", ["37", "38", "39", "40", "41", "42"], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx", [], [], {"ruleId": "43", "severity": 1, "message": "44", "line": 2, "column": 17, "nodeType": "45", "messageId": "46", "endLine": 2, "endColumn": 26}, {"ruleId": "43", "severity": 1, "message": "47", "line": 2, "column": 38, "nodeType": "45", "messageId": "46", "endLine": 2, "endColumn": 44}, {"ruleId": "43", "severity": 1, "message": "48", "line": 54, "column": 15, "nodeType": "45", "messageId": "46", "endLine": 54, "endColumn": 21}, {"ruleId": "43", "severity": 1, "message": "49", "line": 55, "column": 16, "nodeType": "45", "messageId": "46", "endLine": 55, "endColumn": 23}, {"ruleId": "43", "severity": 1, "message": "50", "line": 60, "column": 21, "nodeType": "45", "messageId": "46", "endLine": 60, "endColumn": 33}, {"ruleId": "51", "severity": 2, "message": "52", "line": 183, "column": 55, "nodeType": "45", "messageId": "53", "endLine": 183, "endColumn": 69}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useRef' is defined but never used.", "'setKey' is assigned a value but never used.", "'setTime' is assigned a value but never used.", "'setConverted' is assigned a value but never used.", "no-undef", "'inputEditorRef' is not defined.", "undef"]