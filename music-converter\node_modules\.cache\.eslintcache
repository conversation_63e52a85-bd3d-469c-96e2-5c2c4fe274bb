[{"C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx": "4", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx": "5", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx": "6"}, {"size": 535, "mtime": 1751849359543, "results": "7", "hashOfConfig": "8"}, {"size": 362, "mtime": 1751849359790, "results": "9", "hashOfConfig": "8"}, {"size": 726, "mtime": 1751901812342, "results": "10", "hashOfConfig": "8"}, {"size": 1238, "mtime": 1751908919694, "results": "11", "hashOfConfig": "8"}, {"size": 1913, "mtime": 1751901814592, "results": "12", "hashOfConfig": "8"}, {"size": 4796, "mtime": 1751901815242, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f3m73l", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx", ["32", "33", "34", "35", "36", "37", "38", "39"], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx", [], [], {"ruleId": "40", "severity": 1, "message": "41", "line": 2, "column": 17, "nodeType": "42", "messageId": "43", "endLine": 2, "endColumn": 26}, {"ruleId": "40", "severity": 1, "message": "44", "line": 11, "column": 15, "nodeType": "42", "messageId": "43", "endLine": 11, "endColumn": 21}, {"ruleId": "40", "severity": 1, "message": "45", "line": 12, "column": 10, "nodeType": "42", "messageId": "43", "endLine": 12, "endColumn": 14}, {"ruleId": "40", "severity": 1, "message": "46", "line": 12, "column": 16, "nodeType": "42", "messageId": "43", "endLine": 12, "endColumn": 23}, {"ruleId": "40", "severity": 1, "message": "47", "line": 13, "column": 17, "nodeType": "42", "messageId": "43", "endLine": 13, "endColumn": 25}, {"ruleId": "40", "severity": 1, "message": "48", "line": 14, "column": 10, "nodeType": "42", "messageId": "43", "endLine": 14, "endColumn": 16}, {"ruleId": "40", "severity": 1, "message": "49", "line": 14, "column": 18, "nodeType": "42", "messageId": "43", "endLine": 14, "endColumn": 27}, {"ruleId": "40", "severity": 1, "message": "50", "line": 18, "column": 9, "nodeType": "42", "messageId": "43", "endLine": 18, "endColumn": 14}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setKey' is assigned a value but never used.", "'time' is assigned a value but never used.", "'setTime' is assigned a value but never used.", "'setNotes' is assigned a value but never used.", "'rawVex' is assigned a value but never used.", "'setRawVex' is assigned a value but never used.", "'NOTES' is assigned a value but never used."]