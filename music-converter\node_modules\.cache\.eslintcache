[{"C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx": "4", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx": "5", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx": "6"}, {"size": 535, "mtime": 1751849359543, "results": "7", "hashOfConfig": "8"}, {"size": 362, "mtime": 1751849359790, "results": "9", "hashOfConfig": "8"}, {"size": 726, "mtime": 1751901812342, "results": "10", "hashOfConfig": "8"}, {"size": 535, "mtime": 1751905132170, "results": "11", "hashOfConfig": "8"}, {"size": 1913, "mtime": 1751901814592, "results": "12", "hashOfConfig": "8"}, {"size": 4796, "mtime": 1751901815242, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f3m73l", {"filePath": "17", "messages": "18", "suppressedMessages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx", ["32", "33"], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx", [], [], {"ruleId": "34", "severity": 1, "message": "35", "line": 2, "column": 8, "nodeType": "36", "messageId": "37", "endLine": 2, "endColumn": 24}, {"ruleId": "34", "severity": 1, "message": "38", "line": 8, "column": 9, "nodeType": "36", "messageId": "37", "endLine": 8, "endColumn": 14}, "no-unused-vars", "'VexFlowComponent' is defined but never used.", "Identifier", "unusedVar", "'NOTES' is assigned a value but never used."]