[{"C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx": "4", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx": "5", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx": "6", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx": "7"}, {"size": 535, "mtime": 1751849359543, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1751849359790, "results": "10", "hashOfConfig": "9"}, {"size": 726, "mtime": 1751901812342, "results": "11", "hashOfConfig": "9"}, {"size": 9778, "mtime": 1752086785608, "results": "12", "hashOfConfig": "9"}, {"size": 1913, "mtime": 1751929887877, "results": "13", "hashOfConfig": "9"}, {"size": 4796, "mtime": 1751901815242, "results": "14", "hashOfConfig": "9"}, {"size": 1522, "mtime": 1752086503344, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f3m73l", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx", ["37", "38", "39", "40", "41", "42", "43"], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 1, "column": 17, "nodeType": "46", "messageId": "47", "endLine": 1, "endColumn": 26}, {"ruleId": "44", "severity": 1, "message": "48", "line": 1, "column": 38, "nodeType": "46", "messageId": "47", "endLine": 1, "endColumn": 44}, {"ruleId": "44", "severity": 1, "message": "49", "line": 52, "column": 21, "nodeType": "46", "messageId": "47", "endLine": 52, "endColumn": 33}, {"ruleId": "44", "severity": 1, "message": "50", "line": 53, "column": 19, "nodeType": "46", "messageId": "47", "endLine": 53, "endColumn": 29}, {"ruleId": "44", "severity": 1, "message": "51", "line": 57, "column": 15, "nodeType": "46", "messageId": "47", "endLine": 57, "endColumn": 21}, {"ruleId": "44", "severity": 1, "message": "52", "line": 58, "column": 16, "nodeType": "46", "messageId": "47", "endLine": 58, "endColumn": 23}, {"ruleId": "44", "severity": 1, "message": "53", "line": 66, "column": 21, "nodeType": "46", "messageId": "47", "endLine": 66, "endColumn": 33}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useRef' is defined but never used.", "'setCurAccent' is assigned a value but never used.", "'setCurSlur' is assigned a value but never used.", "'setKey' is assigned a value but never used.", "'setTime' is assigned a value but never used.", "'setConverted' is assigned a value but never used."]