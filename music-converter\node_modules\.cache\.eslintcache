[{"C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx": "4", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx": "5", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx": "6", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx": "7", "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\ConvertFunctions.js": "8"}, {"size": 535, "mtime": 1751849359543, "results": "9", "hashOfConfig": "10"}, {"size": 362, "mtime": 1751849359790, "results": "11", "hashOfConfig": "10"}, {"size": 726, "mtime": 1751901812342, "results": "12", "hashOfConfig": "10"}, {"size": 11061, "mtime": 1752273796375, "results": "13", "hashOfConfig": "10"}, {"size": 1913, "mtime": 1751929887877, "results": "14", "hashOfConfig": "10"}, {"size": 4796, "mtime": 1751901815242, "results": "15", "hashOfConfig": "10"}, {"size": 1492, "mtime": 1752087121483, "results": "16", "hashOfConfig": "10"}, {"size": 1932, "mtime": 1752274086473, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f3m73l", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Converter.jsx", ["42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56"], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Header.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\components\\VexTabBlock.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\music\\music-converter\\src\\ConvertFunctions.js", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 1, "column": 17, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 26}, {"ruleId": "57", "severity": 1, "message": "61", "line": 1, "column": 38, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 44}, {"ruleId": "57", "severity": 1, "message": "62", "line": 4, "column": 23, "nodeType": "59", "messageId": "60", "endLine": 4, "endColumn": 32}, {"ruleId": "57", "severity": 1, "message": "63", "line": 4, "column": 34, "nodeType": "59", "messageId": "60", "endLine": 4, "endColumn": 40}, {"ruleId": "57", "severity": 1, "message": "64", "line": 4, "column": 42, "nodeType": "59", "messageId": "60", "endLine": 4, "endColumn": 50}, {"ruleId": "57", "severity": 1, "message": "65", "line": 4, "column": 52, "nodeType": "59", "messageId": "60", "endLine": 4, "endColumn": 62}, {"ruleId": "57", "severity": 1, "message": "66", "line": 53, "column": 21, "nodeType": "59", "messageId": "60", "endLine": 53, "endColumn": 33}, {"ruleId": "57", "severity": 1, "message": "67", "line": 54, "column": 19, "nodeType": "59", "messageId": "60", "endLine": 54, "endColumn": 29}, {"ruleId": "57", "severity": 1, "message": "68", "line": 58, "column": 15, "nodeType": "59", "messageId": "60", "endLine": 58, "endColumn": 21}, {"ruleId": "57", "severity": 1, "message": "69", "line": 59, "column": 16, "nodeType": "59", "messageId": "60", "endLine": 59, "endColumn": 23}, {"ruleId": "57", "severity": 1, "message": "70", "line": 67, "column": 21, "nodeType": "59", "messageId": "60", "endLine": 67, "endColumn": 33}, {"ruleId": "71", "severity": 1, "message": "72", "line": 130, "column": 91, "nodeType": "73", "messageId": "74", "endLine": 130, "endColumn": 92}, {"ruleId": "71", "severity": 1, "message": "72", "line": 142, "column": 91, "nodeType": "73", "messageId": "74", "endLine": 142, "endColumn": 92}, {"ruleId": "71", "severity": 1, "message": "72", "line": 173, "column": 95, "nodeType": "73", "messageId": "74", "endLine": 173, "endColumn": 96}, {"ruleId": "71", "severity": 1, "message": "72", "line": 213, "column": 93, "nodeType": "73", "messageId": "74", "endLine": 213, "endColumn": 94}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useRef' is defined but never used.", "'parseNote' is defined but never used.", "'toMidi' is defined but never used.", "'fromMidi' is defined but never used.", "'formatNote' is defined but never used.", "'setCurAccent' is assigned a value but never used.", "'setCurSlur' is assigned a value but never used.", "'setKey' is assigned a value but never used.", "'setTime' is assigned a value but never used.", "'setConverted' is assigned a value but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "BinaryExpression", "unexpectedConcat"]