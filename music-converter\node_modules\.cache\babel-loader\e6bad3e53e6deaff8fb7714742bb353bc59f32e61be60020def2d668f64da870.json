{"ast": null, "code": "import { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Renderer } from './renderer.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStaveNote, isStemmableNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class StringNumber extends Modifier {\n  static get CATEGORY() {\n    return \"StringNumber\";\n  }\n  static format(nums, state) {\n    const leftShift = state.leftShift;\n    const rightShift = state.rightShift;\n    const numSpacing = 1;\n    if (!nums || nums.length === 0) return false;\n    const numsList = [];\n    let prevNote = null;\n    let extraXSpaceForDisplacedNotehead = 0;\n    let shiftRight = 0;\n    const modLines = 0;\n    for (let i = 0; i < nums.length; ++i) {\n      const num = nums[i];\n      const note = num.getNote();\n      const pos = num.getPosition();\n      if (!isStaveNote(note)) {\n        throw new RuntimeError('NoStaveNote');\n      }\n      const index = num.checkIndex();\n      const props = note.getKeyProps()[index];\n      const mc = note.getModifierContext();\n      const verticalSpaceNeeded = num.radius * 2 / Tables.STAVE_LINE_DISTANCE + 0.5;\n      if (mc) {\n        if (pos === ModifierPosition.ABOVE) {\n          num.textLine = mc.getState().topTextLine;\n          state.topTextLine += verticalSpaceNeeded;\n        } else if (pos === ModifierPosition.BELOW) {\n          num.textLine = mc.getState().textLine;\n          state.textLine += verticalSpaceNeeded;\n        }\n      }\n      if (note !== prevNote) {\n        for (let n = 0; n < note.keys.length; ++n) {\n          if (pos === Modifier.Position.LEFT) {\n            extraXSpaceForDisplacedNotehead = Math.max(note.getLeftDisplacedHeadPx(), extraXSpaceForDisplacedNotehead);\n          }\n          if (rightShift === 0) {\n            shiftRight = Math.max(note.getRightDisplacedHeadPx(), shiftRight);\n          }\n        }\n        prevNote = note;\n      }\n      const glyphLine = modLines === 0 ? props.line : modLines;\n      numsList.push({\n        pos,\n        note,\n        num,\n        line: glyphLine,\n        shiftL: extraXSpaceForDisplacedNotehead,\n        shiftR: shiftRight\n      });\n    }\n    numsList.sort((a, b) => b.line - a.line);\n    let numShiftR = 0;\n    let xWidthL = 0;\n    let xWidthR = 0;\n    let lastLine = null;\n    let lastNote = null;\n    for (let i = 0; i < numsList.length; ++i) {\n      const note = numsList[i].note;\n      const pos = numsList[i].pos;\n      const num = numsList[i].num;\n      const line = numsList[i].line;\n      const shiftR = numsList[i].shiftR;\n      if (line !== lastLine || note !== lastNote) {\n        numShiftR = rightShift + shiftR;\n      }\n      const numWidth = num.getWidth() + numSpacing;\n      let numXShift = 0;\n      if (pos === Modifier.Position.LEFT) {\n        num.setXShift(leftShift + extraXSpaceForDisplacedNotehead);\n        numXShift = numWidth;\n        xWidthL = Math.max(numXShift, xWidthL);\n      } else if (pos === Modifier.Position.RIGHT) {\n        num.setXShift(numShiftR);\n        numXShift += numWidth;\n        xWidthR = numXShift > xWidthR ? numXShift : xWidthR;\n      }\n      lastLine = line;\n      lastNote = note;\n    }\n    state.leftShift += xWidthL;\n    state.rightShift += xWidthR;\n    return true;\n  }\n  constructor(number) {\n    super();\n    this.stringNumber = number;\n    this.position = Modifier.Position.ABOVE;\n    this.xShift = 0;\n    this.yShift = 0;\n    this.textLine = 0;\n    this.stemOffset = 0;\n    this.xOffset = 0;\n    this.yOffset = 0;\n    this.dashed = true;\n    this.leg = Renderer.LineEndType.NONE;\n    this.radius = 8;\n    this.drawCircle = true;\n    this.setWidth(this.radius * 2 + 4);\n  }\n  setLineEndType(leg) {\n    if (leg >= Renderer.LineEndType.NONE && leg <= Renderer.LineEndType.DOWN) {\n      this.leg = leg;\n    }\n    return this;\n  }\n  setStringNumber(number) {\n    this.stringNumber = number;\n    return this;\n  }\n  setOffsetX(x) {\n    this.xOffset = x;\n    return this;\n  }\n  setOffsetY(y) {\n    this.yOffset = y;\n    return this;\n  }\n  setLastNote(note) {\n    this.lastNote = note;\n    return this;\n  }\n  setDashed(dashed) {\n    this.dashed = dashed;\n    return this;\n  }\n  setDrawCircle(drawCircle) {\n    this.drawCircle = drawCircle;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const start = note.getModifierStartXY(this.position, this.index);\n    const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n    let dotX = start.x + this.xShift + this.xOffset;\n    let stemExt = {};\n    if (note.hasStem()) {\n      stemExt = note.checkStem().getExtents();\n    }\n    let dotY = start.y + this.yShift + this.yOffset;\n    switch (this.position) {\n      case Modifier.Position.ABOVE:\n        {\n          const ys = note.getYs();\n          dotY = ys.reduce((a, b) => a < b ? a : b);\n          if (note.hasStem() && stemDirection === Stem.UP) {\n            dotY = stemExt.topY + Metrics.get('StringNumber.stemPadding');\n          }\n          dotY -= this.radius + Metrics.get('StringNumber.verticalPadding') + this.textLine * Tables.STAVE_LINE_DISTANCE;\n        }\n        break;\n      case Modifier.Position.BELOW:\n        {\n          const ys = note.getYs();\n          dotY = ys.reduce((a, b) => a > b ? a : b);\n          if (note.hasStem() && stemDirection === Stem.DOWN) {\n            dotY = stemExt.topY - Metrics.get('StringNumber.stemPadding');\n          }\n          dotY += this.radius + Metrics.get('StringNumber.verticalPadding') + this.textLine * Tables.STAVE_LINE_DISTANCE;\n        }\n        break;\n      case Modifier.Position.LEFT:\n        dotX -= this.radius / 2 + Metrics.get('StringNumber.leftPadding');\n        break;\n      case Modifier.Position.RIGHT:\n        dotX += this.radius / 2 + Metrics.get('StringNumber.rightPadding');\n        break;\n      default:\n        throw new RuntimeError('InvalidPosition', `The position ${this.position} is invalid`);\n    }\n    if (this.drawCircle) {\n      ctx.beginPath();\n      ctx.arc(dotX, dotY, this.radius, 0, Math.PI * 2, false);\n      ctx.setLineWidth(1.5);\n      ctx.stroke();\n    }\n    ctx.setFont(this.fontInfo);\n    const x = dotX - ctx.measureText(this.stringNumber).width / 2;\n    ctx.fillText('' + this.stringNumber, x, dotY + 4.5);\n    const lastNote = this.lastNote;\n    if (isStemmableNote(lastNote)) {\n      const end = lastNote.getStemX() - note.getX() + 5;\n      ctx.setStrokeStyle('#000000');\n      ctx.setLineCap('round');\n      ctx.setLineWidth(0.6);\n      if (this.dashed) {\n        Renderer.drawDashedLine(ctx, dotX + 10, dotY, dotX + end, dotY, [3, 3]);\n      } else {\n        Renderer.drawDashedLine(ctx, dotX + 10, dotY, dotX + end, dotY, [3, 0]);\n      }\n      let len;\n      let pattern;\n      switch (this.leg) {\n        case Renderer.LineEndType.UP:\n          len = -10;\n          pattern = this.dashed ? [3, 3] : [3, 0];\n          Renderer.drawDashedLine(ctx, dotX + end, dotY, dotX + end, dotY + len, pattern);\n          break;\n        case Renderer.LineEndType.DOWN:\n          len = 10;\n          pattern = this.dashed ? [3, 3] : [3, 0];\n          Renderer.drawDashedLine(ctx, dotX + end, dotY, dotX + end, dotY + len, pattern);\n          break;\n        default:\n          break;\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["Metrics", "Modifier", "ModifierPosition", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tables", "isStaveNote", "isStemmableNote", "RuntimeError", "StringNumber", "CATEGORY", "format", "nums", "state", "leftShift", "rightShift", "numSpacing", "length", "numsList", "prevNote", "extraXSpaceForDisplacedNotehead", "shiftRight", "modLines", "i", "num", "note", "getNote", "pos", "getPosition", "index", "checkIndex", "props", "getKeyProps", "mc", "getModifierContext", "verticalSpaceNeeded", "radius", "STAVE_LINE_DISTANCE", "ABOVE", "textLine", "getState", "topTextLine", "BELOW", "n", "keys", "Position", "LEFT", "Math", "max", "getLeftDisplacedHeadPx", "getRightDisplacedHeadPx", "glyphLine", "line", "push", "shiftL", "shiftR", "sort", "a", "b", "numShiftR", "xWidthL", "xWidthR", "lastLine", "lastNote", "numWidth", "getWidth", "numXShift", "setXShift", "RIGHT", "constructor", "number", "stringNumber", "position", "xShift", "yShift", "stemOffset", "xOffset", "yOffset", "dashed", "leg", "LineEndType", "NONE", "drawCircle", "<PERSON><PERSON><PERSON><PERSON>", "setLineEndType", "DOWN", "setStringNumber", "setOffsetX", "x", "setOffsetY", "y", "setLastNote", "setDashed", "setDrawCircle", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "start", "getModifierStartXY", "stemDirection", "hasStem", "getStemDirection", "UP", "dotX", "stemExt", "checkStem", "getExtents", "dotY", "ys", "getYs", "reduce", "topY", "get", "beginPath", "arc", "PI", "setLineWidth", "stroke", "setFont", "fontInfo", "measureText", "width", "fillText", "end", "getStemX", "getX", "setStrokeStyle", "setLineCap", "drawDashedLine", "len", "pattern"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stringnumber.js"], "sourcesContent": ["import { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Renderer } from './renderer.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStaveNote, isStemmableNote } from './typeguard.js';\nimport { RuntimeError } from './util.js';\nexport class StringNumber extends Modifier {\n    static get CATEGORY() {\n        return \"StringNumber\";\n    }\n    static format(nums, state) {\n        const leftShift = state.leftShift;\n        const rightShift = state.rightShift;\n        const numSpacing = 1;\n        if (!nums || nums.length === 0)\n            return false;\n        const numsList = [];\n        let prevNote = null;\n        let extraXSpaceForDisplacedNotehead = 0;\n        let shiftRight = 0;\n        const modLines = 0;\n        for (let i = 0; i < nums.length; ++i) {\n            const num = nums[i];\n            const note = num.getNote();\n            const pos = num.getPosition();\n            if (!isStaveNote(note)) {\n                throw new RuntimeError('NoStaveNote');\n            }\n            const index = num.checkIndex();\n            const props = note.getKeyProps()[index];\n            const mc = note.getModifierContext();\n            const verticalSpaceNeeded = (num.radius * 2) / Tables.STAVE_LINE_DISTANCE + 0.5;\n            if (mc) {\n                if (pos === ModifierPosition.ABOVE) {\n                    num.textLine = mc.getState().topTextLine;\n                    state.topTextLine += verticalSpaceNeeded;\n                }\n                else if (pos === ModifierPosition.BELOW) {\n                    num.textLine = mc.getState().textLine;\n                    state.textLine += verticalSpaceNeeded;\n                }\n            }\n            if (note !== prevNote) {\n                for (let n = 0; n < note.keys.length; ++n) {\n                    if (pos === Modifier.Position.LEFT) {\n                        extraXSpaceForDisplacedNotehead = Math.max(note.getLeftDisplacedHeadPx(), extraXSpaceForDisplacedNotehead);\n                    }\n                    if (rightShift === 0) {\n                        shiftRight = Math.max(note.getRightDisplacedHeadPx(), shiftRight);\n                    }\n                }\n                prevNote = note;\n            }\n            const glyphLine = modLines === 0 ? props.line : modLines;\n            numsList.push({\n                pos,\n                note,\n                num,\n                line: glyphLine,\n                shiftL: extraXSpaceForDisplacedNotehead,\n                shiftR: shiftRight,\n            });\n        }\n        numsList.sort((a, b) => b.line - a.line);\n        let numShiftR = 0;\n        let xWidthL = 0;\n        let xWidthR = 0;\n        let lastLine = null;\n        let lastNote = null;\n        for (let i = 0; i < numsList.length; ++i) {\n            const note = numsList[i].note;\n            const pos = numsList[i].pos;\n            const num = numsList[i].num;\n            const line = numsList[i].line;\n            const shiftR = numsList[i].shiftR;\n            if (line !== lastLine || note !== lastNote) {\n                numShiftR = rightShift + shiftR;\n            }\n            const numWidth = num.getWidth() + numSpacing;\n            let numXShift = 0;\n            if (pos === Modifier.Position.LEFT) {\n                num.setXShift(leftShift + extraXSpaceForDisplacedNotehead);\n                numXShift = numWidth;\n                xWidthL = Math.max(numXShift, xWidthL);\n            }\n            else if (pos === Modifier.Position.RIGHT) {\n                num.setXShift(numShiftR);\n                numXShift += numWidth;\n                xWidthR = numXShift > xWidthR ? numXShift : xWidthR;\n            }\n            lastLine = line;\n            lastNote = note;\n        }\n        state.leftShift += xWidthL;\n        state.rightShift += xWidthR;\n        return true;\n    }\n    constructor(number) {\n        super();\n        this.stringNumber = number;\n        this.position = Modifier.Position.ABOVE;\n        this.xShift = 0;\n        this.yShift = 0;\n        this.textLine = 0;\n        this.stemOffset = 0;\n        this.xOffset = 0;\n        this.yOffset = 0;\n        this.dashed = true;\n        this.leg = Renderer.LineEndType.NONE;\n        this.radius = 8;\n        this.drawCircle = true;\n        this.setWidth(this.radius * 2 + 4);\n    }\n    setLineEndType(leg) {\n        if (leg >= Renderer.LineEndType.NONE && leg <= Renderer.LineEndType.DOWN) {\n            this.leg = leg;\n        }\n        return this;\n    }\n    setStringNumber(number) {\n        this.stringNumber = number;\n        return this;\n    }\n    setOffsetX(x) {\n        this.xOffset = x;\n        return this;\n    }\n    setOffsetY(y) {\n        this.yOffset = y;\n        return this;\n    }\n    setLastNote(note) {\n        this.lastNote = note;\n        return this;\n    }\n    setDashed(dashed) {\n        this.dashed = dashed;\n        return this;\n    }\n    setDrawCircle(drawCircle) {\n        this.drawCircle = drawCircle;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const start = note.getModifierStartXY(this.position, this.index);\n        const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n        let dotX = start.x + this.xShift + this.xOffset;\n        let stemExt = {};\n        if (note.hasStem()) {\n            stemExt = note.checkStem().getExtents();\n        }\n        let dotY = start.y + this.yShift + this.yOffset;\n        switch (this.position) {\n            case Modifier.Position.ABOVE:\n                {\n                    const ys = note.getYs();\n                    dotY = ys.reduce((a, b) => (a < b ? a : b));\n                    if (note.hasStem() && stemDirection === Stem.UP) {\n                        dotY = stemExt.topY + Metrics.get('StringNumber.stemPadding');\n                    }\n                    dotY -=\n                        this.radius + Metrics.get('StringNumber.verticalPadding') + this.textLine * Tables.STAVE_LINE_DISTANCE;\n                }\n                break;\n            case Modifier.Position.BELOW:\n                {\n                    const ys = note.getYs();\n                    dotY = ys.reduce((a, b) => (a > b ? a : b));\n                    if (note.hasStem() && stemDirection === Stem.DOWN) {\n                        dotY = stemExt.topY - Metrics.get('StringNumber.stemPadding');\n                    }\n                    dotY +=\n                        this.radius + Metrics.get('StringNumber.verticalPadding') + this.textLine * Tables.STAVE_LINE_DISTANCE;\n                }\n                break;\n            case Modifier.Position.LEFT:\n                dotX -= this.radius / 2 + Metrics.get('StringNumber.leftPadding');\n                break;\n            case Modifier.Position.RIGHT:\n                dotX += this.radius / 2 + Metrics.get('StringNumber.rightPadding');\n                break;\n            default:\n                throw new RuntimeError('InvalidPosition', `The position ${this.position} is invalid`);\n        }\n        if (this.drawCircle) {\n            ctx.beginPath();\n            ctx.arc(dotX, dotY, this.radius, 0, Math.PI * 2, false);\n            ctx.setLineWidth(1.5);\n            ctx.stroke();\n        }\n        ctx.setFont(this.fontInfo);\n        const x = dotX - ctx.measureText(this.stringNumber).width / 2;\n        ctx.fillText('' + this.stringNumber, x, dotY + 4.5);\n        const lastNote = this.lastNote;\n        if (isStemmableNote(lastNote)) {\n            const end = lastNote.getStemX() - note.getX() + 5;\n            ctx.setStrokeStyle('#000000');\n            ctx.setLineCap('round');\n            ctx.setLineWidth(0.6);\n            if (this.dashed) {\n                Renderer.drawDashedLine(ctx, dotX + 10, dotY, dotX + end, dotY, [3, 3]);\n            }\n            else {\n                Renderer.drawDashedLine(ctx, dotX + 10, dotY, dotX + end, dotY, [3, 0]);\n            }\n            let len;\n            let pattern;\n            switch (this.leg) {\n                case Renderer.LineEndType.UP:\n                    len = -10;\n                    pattern = this.dashed ? [3, 3] : [3, 0];\n                    Renderer.drawDashedLine(ctx, dotX + end, dotY, dotX + end, dotY + len, pattern);\n                    break;\n                case Renderer.LineEndType.DOWN:\n                    len = 10;\n                    pattern = this.dashed ? [3, 3] : [3, 0];\n                    Renderer.drawDashedLine(ctx, dotX + end, dotY, dotX + end, dotY + len, pattern);\n                    break;\n                default:\n                    break;\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,EAAEC,eAAe,QAAQ,gBAAgB;AAC7D,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,YAAY,SAASR,QAAQ,CAAC;EACvC,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACA,OAAOC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACvB,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS;IACjC,MAAMC,UAAU,GAAGF,KAAK,CAACE,UAAU;IACnC,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAI,CAACJ,IAAI,IAAIA,IAAI,CAACK,MAAM,KAAK,CAAC,EAC1B,OAAO,KAAK;IAChB,MAAMC,QAAQ,GAAG,EAAE;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,+BAA+B,GAAG,CAAC;IACvC,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAMC,QAAQ,GAAG,CAAC;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACK,MAAM,EAAE,EAAEM,CAAC,EAAE;MAClC,MAAMC,GAAG,GAAGZ,IAAI,CAACW,CAAC,CAAC;MACnB,MAAME,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,CAAC;MAC1B,MAAMC,GAAG,GAAGH,GAAG,CAACI,WAAW,CAAC,CAAC;MAC7B,IAAI,CAACtB,WAAW,CAACmB,IAAI,CAAC,EAAE;QACpB,MAAM,IAAIjB,YAAY,CAAC,aAAa,CAAC;MACzC;MACA,MAAMqB,KAAK,GAAGL,GAAG,CAACM,UAAU,CAAC,CAAC;MAC9B,MAAMC,KAAK,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC,CAACH,KAAK,CAAC;MACvC,MAAMI,EAAE,GAAGR,IAAI,CAACS,kBAAkB,CAAC,CAAC;MACpC,MAAMC,mBAAmB,GAAIX,GAAG,CAACY,MAAM,GAAG,CAAC,GAAI/B,MAAM,CAACgC,mBAAmB,GAAG,GAAG;MAC/E,IAAIJ,EAAE,EAAE;QACJ,IAAIN,GAAG,KAAKzB,gBAAgB,CAACoC,KAAK,EAAE;UAChCd,GAAG,CAACe,QAAQ,GAAGN,EAAE,CAACO,QAAQ,CAAC,CAAC,CAACC,WAAW;UACxC5B,KAAK,CAAC4B,WAAW,IAAIN,mBAAmB;QAC5C,CAAC,MACI,IAAIR,GAAG,KAAKzB,gBAAgB,CAACwC,KAAK,EAAE;UACrClB,GAAG,CAACe,QAAQ,GAAGN,EAAE,CAACO,QAAQ,CAAC,CAAC,CAACD,QAAQ;UACrC1B,KAAK,CAAC0B,QAAQ,IAAIJ,mBAAmB;QACzC;MACJ;MACA,IAAIV,IAAI,KAAKN,QAAQ,EAAE;QACnB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,IAAI,CAACmB,IAAI,CAAC3B,MAAM,EAAE,EAAE0B,CAAC,EAAE;UACvC,IAAIhB,GAAG,KAAK1B,QAAQ,CAAC4C,QAAQ,CAACC,IAAI,EAAE;YAChC1B,+BAA+B,GAAG2B,IAAI,CAACC,GAAG,CAACvB,IAAI,CAACwB,sBAAsB,CAAC,CAAC,EAAE7B,+BAA+B,CAAC;UAC9G;UACA,IAAIL,UAAU,KAAK,CAAC,EAAE;YAClBM,UAAU,GAAG0B,IAAI,CAACC,GAAG,CAACvB,IAAI,CAACyB,uBAAuB,CAAC,CAAC,EAAE7B,UAAU,CAAC;UACrE;QACJ;QACAF,QAAQ,GAAGM,IAAI;MACnB;MACA,MAAM0B,SAAS,GAAG7B,QAAQ,KAAK,CAAC,GAAGS,KAAK,CAACqB,IAAI,GAAG9B,QAAQ;MACxDJ,QAAQ,CAACmC,IAAI,CAAC;QACV1B,GAAG;QACHF,IAAI;QACJD,GAAG;QACH4B,IAAI,EAAED,SAAS;QACfG,MAAM,EAAElC,+BAA+B;QACvCmC,MAAM,EAAElC;MACZ,CAAC,CAAC;IACN;IACAH,QAAQ,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACN,IAAI,GAAGK,CAAC,CAACL,IAAI,CAAC;IACxC,IAAIO,SAAS,GAAG,CAAC;IACjB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,QAAQ,GAAG,IAAI;IACnB,IAAIC,QAAQ,GAAG,IAAI;IACnB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACD,MAAM,EAAE,EAAEM,CAAC,EAAE;MACtC,MAAME,IAAI,GAAGP,QAAQ,CAACK,CAAC,CAAC,CAACE,IAAI;MAC7B,MAAME,GAAG,GAAGT,QAAQ,CAACK,CAAC,CAAC,CAACI,GAAG;MAC3B,MAAMH,GAAG,GAAGN,QAAQ,CAACK,CAAC,CAAC,CAACC,GAAG;MAC3B,MAAM4B,IAAI,GAAGlC,QAAQ,CAACK,CAAC,CAAC,CAAC6B,IAAI;MAC7B,MAAMG,MAAM,GAAGrC,QAAQ,CAACK,CAAC,CAAC,CAACgC,MAAM;MACjC,IAAIH,IAAI,KAAKU,QAAQ,IAAIrC,IAAI,KAAKsC,QAAQ,EAAE;QACxCJ,SAAS,GAAG5C,UAAU,GAAGwC,MAAM;MACnC;MACA,MAAMS,QAAQ,GAAGxC,GAAG,CAACyC,QAAQ,CAAC,CAAC,GAAGjD,UAAU;MAC5C,IAAIkD,SAAS,GAAG,CAAC;MACjB,IAAIvC,GAAG,KAAK1B,QAAQ,CAAC4C,QAAQ,CAACC,IAAI,EAAE;QAChCtB,GAAG,CAAC2C,SAAS,CAACrD,SAAS,GAAGM,+BAA+B,CAAC;QAC1D8C,SAAS,GAAGF,QAAQ;QACpBJ,OAAO,GAAGb,IAAI,CAACC,GAAG,CAACkB,SAAS,EAAEN,OAAO,CAAC;MAC1C,CAAC,MACI,IAAIjC,GAAG,KAAK1B,QAAQ,CAAC4C,QAAQ,CAACuB,KAAK,EAAE;QACtC5C,GAAG,CAAC2C,SAAS,CAACR,SAAS,CAAC;QACxBO,SAAS,IAAIF,QAAQ;QACrBH,OAAO,GAAGK,SAAS,GAAGL,OAAO,GAAGK,SAAS,GAAGL,OAAO;MACvD;MACAC,QAAQ,GAAGV,IAAI;MACfW,QAAQ,GAAGtC,IAAI;IACnB;IACAZ,KAAK,CAACC,SAAS,IAAI8C,OAAO;IAC1B/C,KAAK,CAACE,UAAU,IAAI8C,OAAO;IAC3B,OAAO,IAAI;EACf;EACAQ,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAGD,MAAM;IAC1B,IAAI,CAACE,QAAQ,GAAGvE,QAAQ,CAAC4C,QAAQ,CAACP,KAAK;IACvC,IAAI,CAACmC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACnC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACoC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,GAAG,GAAG5E,QAAQ,CAAC6E,WAAW,CAACC,IAAI;IACpC,IAAI,CAAC7C,MAAM,GAAG,CAAC;IACf,IAAI,CAAC8C,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC/C,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACtC;EACAgD,cAAcA,CAACL,GAAG,EAAE;IAChB,IAAIA,GAAG,IAAI5E,QAAQ,CAAC6E,WAAW,CAACC,IAAI,IAAIF,GAAG,IAAI5E,QAAQ,CAAC6E,WAAW,CAACK,IAAI,EAAE;MACtE,IAAI,CAACN,GAAG,GAAGA,GAAG;IAClB;IACA,OAAO,IAAI;EACf;EACAO,eAAeA,CAAChB,MAAM,EAAE;IACpB,IAAI,CAACC,YAAY,GAAGD,MAAM;IAC1B,OAAO,IAAI;EACf;EACAiB,UAAUA,CAACC,CAAC,EAAE;IACV,IAAI,CAACZ,OAAO,GAAGY,CAAC;IAChB,OAAO,IAAI;EACf;EACAC,UAAUA,CAACC,CAAC,EAAE;IACV,IAAI,CAACb,OAAO,GAAGa,CAAC;IAChB,OAAO,IAAI;EACf;EACAC,WAAWA,CAAClE,IAAI,EAAE;IACd,IAAI,CAACsC,QAAQ,GAAGtC,IAAI;IACpB,OAAO,IAAI;EACf;EACAmE,SAASA,CAACd,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAe,aAAaA,CAACX,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,OAAO,IAAI;EACf;EACAY,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMvE,IAAI,GAAG,IAAI,CAACwE,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG1E,IAAI,CAAC2E,kBAAkB,CAAC,IAAI,CAAC5B,QAAQ,EAAE,IAAI,CAAC3C,KAAK,CAAC;IAChE,MAAMwE,aAAa,GAAG5E,IAAI,CAAC6E,OAAO,CAAC,CAAC,GAAG7E,IAAI,CAAC8E,gBAAgB,CAAC,CAAC,GAAGnG,IAAI,CAACoG,EAAE;IACxE,IAAIC,IAAI,GAAGN,KAAK,CAACX,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,IAAI,CAACG,OAAO;IAC/C,IAAI8B,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIjF,IAAI,CAAC6E,OAAO,CAAC,CAAC,EAAE;MAChBI,OAAO,GAAGjF,IAAI,CAACkF,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;IAC3C;IACA,IAAIC,IAAI,GAAGV,KAAK,CAACT,CAAC,GAAG,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACG,OAAO;IAC/C,QAAQ,IAAI,CAACL,QAAQ;MACjB,KAAKvE,QAAQ,CAAC4C,QAAQ,CAACP,KAAK;QACxB;UACI,MAAMwE,EAAE,GAAGrF,IAAI,CAACsF,KAAK,CAAC,CAAC;UACvBF,IAAI,GAAGC,EAAE,CAACE,MAAM,CAAC,CAACvD,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;UAC3C,IAAIjC,IAAI,CAAC6E,OAAO,CAAC,CAAC,IAAID,aAAa,KAAKjG,IAAI,CAACoG,EAAE,EAAE;YAC7CK,IAAI,GAAGH,OAAO,CAACO,IAAI,GAAGjH,OAAO,CAACkH,GAAG,CAAC,0BAA0B,CAAC;UACjE;UACAL,IAAI,IACA,IAAI,CAACzE,MAAM,GAAGpC,OAAO,CAACkH,GAAG,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC3E,QAAQ,GAAGlC,MAAM,CAACgC,mBAAmB;QAC9G;QACA;MACJ,KAAKpC,QAAQ,CAAC4C,QAAQ,CAACH,KAAK;QACxB;UACI,MAAMoE,EAAE,GAAGrF,IAAI,CAACsF,KAAK,CAAC,CAAC;UACvBF,IAAI,GAAGC,EAAE,CAACE,MAAM,CAAC,CAACvD,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;UAC3C,IAAIjC,IAAI,CAAC6E,OAAO,CAAC,CAAC,IAAID,aAAa,KAAKjG,IAAI,CAACiF,IAAI,EAAE;YAC/CwB,IAAI,GAAGH,OAAO,CAACO,IAAI,GAAGjH,OAAO,CAACkH,GAAG,CAAC,0BAA0B,CAAC;UACjE;UACAL,IAAI,IACA,IAAI,CAACzE,MAAM,GAAGpC,OAAO,CAACkH,GAAG,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC3E,QAAQ,GAAGlC,MAAM,CAACgC,mBAAmB;QAC9G;QACA;MACJ,KAAKpC,QAAQ,CAAC4C,QAAQ,CAACC,IAAI;QACvB2D,IAAI,IAAI,IAAI,CAACrE,MAAM,GAAG,CAAC,GAAGpC,OAAO,CAACkH,GAAG,CAAC,0BAA0B,CAAC;QACjE;MACJ,KAAKjH,QAAQ,CAAC4C,QAAQ,CAACuB,KAAK;QACxBqC,IAAI,IAAI,IAAI,CAACrE,MAAM,GAAG,CAAC,GAAGpC,OAAO,CAACkH,GAAG,CAAC,2BAA2B,CAAC;QAClE;MACJ;QACI,MAAM,IAAI1G,YAAY,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAACgE,QAAQ,aAAa,CAAC;IAC7F;IACA,IAAI,IAAI,CAACU,UAAU,EAAE;MACjBa,GAAG,CAACoB,SAAS,CAAC,CAAC;MACfpB,GAAG,CAACqB,GAAG,CAACX,IAAI,EAAEI,IAAI,EAAE,IAAI,CAACzE,MAAM,EAAE,CAAC,EAAEW,IAAI,CAACsE,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;MACvDtB,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAC;MACrBvB,GAAG,CAACwB,MAAM,CAAC,CAAC;IAChB;IACAxB,GAAG,CAACyB,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC;IAC1B,MAAMjC,CAAC,GAAGiB,IAAI,GAAGV,GAAG,CAAC2B,WAAW,CAAC,IAAI,CAACnD,YAAY,CAAC,CAACoD,KAAK,GAAG,CAAC;IAC7D5B,GAAG,CAAC6B,QAAQ,CAAC,EAAE,GAAG,IAAI,CAACrD,YAAY,EAAEiB,CAAC,EAAEqB,IAAI,GAAG,GAAG,CAAC;IACnD,MAAM9C,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIxD,eAAe,CAACwD,QAAQ,CAAC,EAAE;MAC3B,MAAM8D,GAAG,GAAG9D,QAAQ,CAAC+D,QAAQ,CAAC,CAAC,GAAGrG,IAAI,CAACsG,IAAI,CAAC,CAAC,GAAG,CAAC;MACjDhC,GAAG,CAACiC,cAAc,CAAC,SAAS,CAAC;MAC7BjC,GAAG,CAACkC,UAAU,CAAC,OAAO,CAAC;MACvBlC,GAAG,CAACuB,YAAY,CAAC,GAAG,CAAC;MACrB,IAAI,IAAI,CAACxC,MAAM,EAAE;QACb3E,QAAQ,CAAC+H,cAAc,CAACnC,GAAG,EAAEU,IAAI,GAAG,EAAE,EAAEI,IAAI,EAAEJ,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3E,CAAC,MACI;QACD1G,QAAQ,CAAC+H,cAAc,CAACnC,GAAG,EAAEU,IAAI,GAAG,EAAE,EAAEI,IAAI,EAAEJ,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3E;MACA,IAAIsB,GAAG;MACP,IAAIC,OAAO;MACX,QAAQ,IAAI,CAACrD,GAAG;QACZ,KAAK5E,QAAQ,CAAC6E,WAAW,CAACwB,EAAE;UACxB2B,GAAG,GAAG,CAAC,EAAE;UACTC,OAAO,GAAG,IAAI,CAACtD,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;UACvC3E,QAAQ,CAAC+H,cAAc,CAACnC,GAAG,EAAEU,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,EAAEJ,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,GAAGsB,GAAG,EAAEC,OAAO,CAAC;UAC/E;QACJ,KAAKjI,QAAQ,CAAC6E,WAAW,CAACK,IAAI;UAC1B8C,GAAG,GAAG,EAAE;UACRC,OAAO,GAAG,IAAI,CAACtD,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;UACvC3E,QAAQ,CAAC+H,cAAc,CAACnC,GAAG,EAAEU,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,EAAEJ,IAAI,GAAGoB,GAAG,EAAEhB,IAAI,GAAGsB,GAAG,EAAEC,OAAO,CAAC;UAC/E;QACJ;UACI;MACR;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}