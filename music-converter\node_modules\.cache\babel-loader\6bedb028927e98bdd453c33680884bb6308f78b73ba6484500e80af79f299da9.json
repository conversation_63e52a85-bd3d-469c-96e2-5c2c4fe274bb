{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { Metrics } from './metrics.js';\nexport var FontWeight;\n(function (FontWeight) {\n  FontWeight[\"NORMAL\"] = \"normal\";\n  FontWeight[\"BOLD\"] = \"bold\";\n})(FontWeight || (FontWeight = {}));\nexport var FontStyle;\n(function (FontStyle) {\n  FontStyle[\"NORMAL\"] = \"normal\";\n  FontStyle[\"ITALIC\"] = \"italic\";\n})(FontStyle || (FontStyle = {}));\nlet fontParser;\nexport class Font {\n  static convertSizeToPixelValue(fontSize) {\n    var _a;\n    if (typeof fontSize === 'number') {\n      return fontSize * Font.scaleToPxFrom.pt;\n    } else {\n      const value = parseFloat(fontSize);\n      if (isNaN(value)) {\n        return 0;\n      }\n      const unit = fontSize.replace(/[\\d.\\s]/g, '').toLowerCase();\n      const conversionFactor = (_a = Font.scaleToPxFrom[unit]) !== null && _a !== void 0 ? _a : 1;\n      return value * conversionFactor;\n    }\n  }\n  static convertSizeToPointValue(fontSize) {\n    var _a;\n    if (typeof fontSize === 'number') {\n      return fontSize;\n    } else {\n      const value = parseFloat(fontSize);\n      if (isNaN(value)) {\n        return 0;\n      }\n      const unit = fontSize.replace(/[\\d.\\s]/g, '').toLowerCase();\n      const conversionFactor = ((_a = Font.scaleToPxFrom[unit]) !== null && _a !== void 0 ? _a : 1) / Font.scaleToPxFrom.pt;\n      return value * conversionFactor;\n    }\n  }\n  static validate(f, size, weight, style) {\n    if (typeof f === 'string' && size === undefined && weight === undefined && style === undefined) {\n      return Font.fromCSSString(f);\n    }\n    let family;\n    if (typeof f === 'object') {\n      family = f.family;\n      size = f.size;\n      weight = f.weight;\n      style = f.style;\n    } else {\n      family = f;\n    }\n    family = family !== null && family !== void 0 ? family : Metrics.get('fontFamily');\n    size = size !== null && size !== void 0 ? size : Metrics.get('fontSize') + 'pt';\n    weight = weight !== null && weight !== void 0 ? weight : FontWeight.NORMAL;\n    style = style !== null && style !== void 0 ? style : FontStyle.NORMAL;\n    if (weight === '') {\n      weight = FontWeight.NORMAL;\n    }\n    if (style === '') {\n      style = FontStyle.NORMAL;\n    }\n    if (typeof size === 'number') {\n      size = `${size}pt`;\n    }\n    if (typeof weight === 'number') {\n      weight = weight.toString();\n    }\n    return {\n      family,\n      size,\n      weight,\n      style\n    };\n  }\n  static fromCSSString(cssFontShorthand) {\n    if (!fontParser) {\n      fontParser = document.createElement('span');\n    }\n    fontParser.style.font = cssFontShorthand;\n    const {\n      fontFamily,\n      fontSize,\n      fontWeight,\n      fontStyle\n    } = fontParser.style;\n    return {\n      family: fontFamily,\n      size: fontSize,\n      weight: fontWeight,\n      style: fontStyle\n    };\n  }\n  static toCSSString(fontInfo) {\n    var _a;\n    if (!fontInfo) {\n      return '';\n    }\n    let style;\n    const st = fontInfo.style;\n    if (st === FontStyle.NORMAL || st === '' || st === undefined) {\n      style = '';\n    } else {\n      style = st.trim() + ' ';\n    }\n    let weight;\n    const wt = fontInfo.weight;\n    if (wt === FontWeight.NORMAL || wt === '' || wt === undefined) {\n      weight = '';\n    } else if (typeof wt === 'number') {\n      weight = wt + ' ';\n    } else {\n      weight = wt.trim() + ' ';\n    }\n    let size;\n    const sz = fontInfo.size;\n    if (sz === undefined) {\n      size = Metrics.get('fontSize') + 'pt';\n    } else if (typeof sz === 'number') {\n      size = sz + 'pt ';\n    } else {\n      size = sz.trim() + ' ';\n    }\n    const family = (_a = fontInfo.family) !== null && _a !== void 0 ? _a : Metrics.get('fontFamily');\n    return `${style}${weight}${size}${family}`;\n  }\n  static scaleSize(fontSize, scaleFactor) {\n    if (typeof fontSize === 'number') {\n      return fontSize * scaleFactor;\n    } else {\n      const value = parseFloat(fontSize);\n      const unit = fontSize.replace(/[\\d.\\s]/g, '');\n      return `${value * scaleFactor}${unit}`;\n    }\n  }\n  static isBold(weight) {\n    if (!weight) {\n      return false;\n    } else if (typeof weight === 'number') {\n      return weight >= 600;\n    } else {\n      const parsedWeight = parseInt(weight, 10);\n      if (isNaN(parsedWeight)) {\n        return weight.toLowerCase() === 'bold';\n      } else {\n        return parsedWeight >= 600;\n      }\n    }\n  }\n  static isItalic(style) {\n    if (!style) {\n      return false;\n    } else {\n      return style.toLowerCase() === FontStyle.ITALIC;\n    }\n  }\n  static load(fontName, url, descriptors) {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (typeof FontFace === 'undefined') {\n        return Promise.reject(new Error('FontFace API is not available in this environment. Cannot load fonts.'));\n      }\n      if (url === undefined) {\n        const files = Font.FILES;\n        if (!(fontName in files)) {\n          return Promise.reject(new Error(`Font ${fontName} not found in Font.FILES`));\n        }\n        url = Font.HOST_URL + files[fontName];\n      }\n      const fontFace = new FontFace(fontName, `url(${url})`, descriptors);\n      const fontFaceLoadPromise = fontFace.load();\n      let fontFaceSet;\n      if (typeof document !== 'undefined') {\n        fontFaceSet = document.fonts;\n      } else if (typeof self !== 'undefined' && 'fonts' in self) {\n        fontFaceSet = self.fonts;\n      }\n      fontFaceSet === null || fontFaceSet === void 0 ? void 0 : fontFaceSet.add(fontFace);\n      return fontFaceLoadPromise;\n    });\n  }\n  static getURLForFont(fontName) {\n    const files = Font.FILES;\n    if (!(fontName in files)) {\n      return undefined;\n    }\n    return Font.HOST_URL + files[fontName];\n  }\n}\nFont.scaleToPxFrom = {\n  pt: 4 / 3,\n  px: 1,\n  em: 16,\n  '%': 4 / 25,\n  in: 96,\n  mm: 96 / 25.4,\n  cm: 96 / 2.54\n};\nFont.HOST_URL = 'https://cdn.jsdelivr.net/npm/@vexflow-fonts/';\nFont.FILES = {\n  Academico: 'academico/academico.woff2',\n  Bravura: 'bravura/bravura.woff2',\n  'Bravura Text': 'bravuratext/bravuratext.woff2',\n  Edwin: 'edwin/edwin-roman.woff2',\n  'Finale Ash': 'finaleash/finaleash.woff2',\n  'Finale Ash Text': 'finaleashtext/finaleashtext.woff2',\n  'Finale Broadway': 'finalebroadway/finalebroadway.woff2',\n  'Finale Broadway Text': 'finalebroadwaytext/finalebroadwaytext.woff2',\n  'Finale Jazz': 'finalejazz/finalejazz.woff2',\n  'Finale Jazz Text': 'finalejazztext/finalejazztext.woff2',\n  'Finale Maestro': 'finalemaestro/finalemaestro.woff2',\n  'Finale Maestro Text': 'finalemaestrotext/finalemaestrotext-regular.woff2',\n  Gonville: 'gonville/gonville.woff2',\n  Gootville: 'gootville/gootville.woff2',\n  'Gootville Text': 'gootvilletext/gootvilletext.woff2',\n  Leipzig: 'leipzig/leipzig.woff2',\n  Leland: 'leland/leland.woff2',\n  'Leland Text': 'lelandtext/lelandtext.woff2',\n  MuseJazz: 'musejazz/musejazz.woff2',\n  'MuseJazz Text': 'musejazztext/musejazztext.woff2',\n  Nepomuk: 'nepomuk/nepomuk.woff2',\n  Petaluma: 'petaluma/petaluma.woff2',\n  'Petaluma Script': 'petalumascript/petalumascript.woff2',\n  'Petaluma Text': 'petalumatext/petalumatext.woff2',\n  'Roboto Slab': 'robotoslab/robotoslab-regular-400.woff2',\n  Sebastian: 'sebastian/sebastian.woff2',\n  'Sebastian Text': 'sebastiantext/sebastiantext.woff2'\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "Metrics", "FontWeight", "FontStyle", "font<PERSON><PERSON>er", "Font", "convertSizeToPixelValue", "fontSize", "_a", "scaleToPxFrom", "pt", "parseFloat", "isNaN", "unit", "replace", "toLowerCase", "conversionFactor", "convertSizeToPointValue", "validate", "f", "size", "weight", "style", "undefined", "fromCSSString", "family", "get", "NORMAL", "toString", "cssFontShorthand", "document", "createElement", "font", "fontFamily", "fontWeight", "fontStyle", "toCSSString", "fontInfo", "st", "trim", "wt", "sz", "scaleSize", "scaleFactor", "isBold", "parsedWeight", "parseInt", "isItalic", "ITALIC", "load", "fontName", "url", "descriptors", "FontFace", "Error", "files", "FILES", "HOST_URL", "fontFace", "fontFaceLoadPromise", "fontFaceSet", "fonts", "self", "add", "getURLForFont", "px", "em", "in", "mm", "cm", "<PERSON><PERSON>", "Bravura", "<PERSON>", "Gonville", "Gootville", "Leipzig", "Leland", "MuseJazz", "Nepomuk", "Petaluma", "<PERSON>"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/font.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { Metrics } from './metrics.js';\nexport var FontWeight;\n(function (FontWeight) {\n    FontWeight[\"NORMAL\"] = \"normal\";\n    FontWeight[\"BOLD\"] = \"bold\";\n})(FontWeight || (FontWeight = {}));\nexport var FontStyle;\n(function (FontStyle) {\n    FontStyle[\"NORMAL\"] = \"normal\";\n    FontStyle[\"ITALIC\"] = \"italic\";\n})(FontStyle || (FontStyle = {}));\nlet fontParser;\nexport class Font {\n    static convertSizeToPixelValue(fontSize) {\n        var _a;\n        if (typeof fontSize === 'number') {\n            return fontSize * Font.scaleToPxFrom.pt;\n        }\n        else {\n            const value = parseFloat(fontSize);\n            if (isNaN(value)) {\n                return 0;\n            }\n            const unit = fontSize.replace(/[\\d.\\s]/g, '').toLowerCase();\n            const conversionFactor = (_a = Font.scaleToPxFrom[unit]) !== null && _a !== void 0 ? _a : 1;\n            return value * conversionFactor;\n        }\n    }\n    static convertSizeToPointValue(fontSize) {\n        var _a;\n        if (typeof fontSize === 'number') {\n            return fontSize;\n        }\n        else {\n            const value = parseFloat(fontSize);\n            if (isNaN(value)) {\n                return 0;\n            }\n            const unit = fontSize.replace(/[\\d.\\s]/g, '').toLowerCase();\n            const conversionFactor = ((_a = Font.scaleToPxFrom[unit]) !== null && _a !== void 0 ? _a : 1) / Font.scaleToPxFrom.pt;\n            return value * conversionFactor;\n        }\n    }\n    static validate(f, size, weight, style) {\n        if (typeof f === 'string' && size === undefined && weight === undefined && style === undefined) {\n            return Font.fromCSSString(f);\n        }\n        let family;\n        if (typeof f === 'object') {\n            family = f.family;\n            size = f.size;\n            weight = f.weight;\n            style = f.style;\n        }\n        else {\n            family = f;\n        }\n        family = family !== null && family !== void 0 ? family : Metrics.get('fontFamily');\n        size = size !== null && size !== void 0 ? size : Metrics.get('fontSize') + 'pt';\n        weight = weight !== null && weight !== void 0 ? weight : FontWeight.NORMAL;\n        style = style !== null && style !== void 0 ? style : FontStyle.NORMAL;\n        if (weight === '') {\n            weight = FontWeight.NORMAL;\n        }\n        if (style === '') {\n            style = FontStyle.NORMAL;\n        }\n        if (typeof size === 'number') {\n            size = `${size}pt`;\n        }\n        if (typeof weight === 'number') {\n            weight = weight.toString();\n        }\n        return { family, size, weight, style };\n    }\n    static fromCSSString(cssFontShorthand) {\n        if (!fontParser) {\n            fontParser = document.createElement('span');\n        }\n        fontParser.style.font = cssFontShorthand;\n        const { fontFamily, fontSize, fontWeight, fontStyle } = fontParser.style;\n        return { family: fontFamily, size: fontSize, weight: fontWeight, style: fontStyle };\n    }\n    static toCSSString(fontInfo) {\n        var _a;\n        if (!fontInfo) {\n            return '';\n        }\n        let style;\n        const st = fontInfo.style;\n        if (st === FontStyle.NORMAL || st === '' || st === undefined) {\n            style = '';\n        }\n        else {\n            style = st.trim() + ' ';\n        }\n        let weight;\n        const wt = fontInfo.weight;\n        if (wt === FontWeight.NORMAL || wt === '' || wt === undefined) {\n            weight = '';\n        }\n        else if (typeof wt === 'number') {\n            weight = wt + ' ';\n        }\n        else {\n            weight = wt.trim() + ' ';\n        }\n        let size;\n        const sz = fontInfo.size;\n        if (sz === undefined) {\n            size = Metrics.get('fontSize') + 'pt';\n        }\n        else if (typeof sz === 'number') {\n            size = sz + 'pt ';\n        }\n        else {\n            size = sz.trim() + ' ';\n        }\n        const family = (_a = fontInfo.family) !== null && _a !== void 0 ? _a : Metrics.get('fontFamily');\n        return `${style}${weight}${size}${family}`;\n    }\n    static scaleSize(fontSize, scaleFactor) {\n        if (typeof fontSize === 'number') {\n            return (fontSize * scaleFactor);\n        }\n        else {\n            const value = parseFloat(fontSize);\n            const unit = fontSize.replace(/[\\d.\\s]/g, '');\n            return `${value * scaleFactor}${unit}`;\n        }\n    }\n    static isBold(weight) {\n        if (!weight) {\n            return false;\n        }\n        else if (typeof weight === 'number') {\n            return weight >= 600;\n        }\n        else {\n            const parsedWeight = parseInt(weight, 10);\n            if (isNaN(parsedWeight)) {\n                return weight.toLowerCase() === 'bold';\n            }\n            else {\n                return parsedWeight >= 600;\n            }\n        }\n    }\n    static isItalic(style) {\n        if (!style) {\n            return false;\n        }\n        else {\n            return style.toLowerCase() === FontStyle.ITALIC;\n        }\n    }\n    static load(fontName, url, descriptors) {\n        return __awaiter(this, void 0, void 0, function* () {\n            if (typeof FontFace === 'undefined') {\n                return Promise.reject(new Error('FontFace API is not available in this environment. Cannot load fonts.'));\n            }\n            if (url === undefined) {\n                const files = Font.FILES;\n                if (!(fontName in files)) {\n                    return Promise.reject(new Error(`Font ${fontName} not found in Font.FILES`));\n                }\n                url = Font.HOST_URL + files[fontName];\n            }\n            const fontFace = new FontFace(fontName, `url(${url})`, descriptors);\n            const fontFaceLoadPromise = fontFace.load();\n            let fontFaceSet;\n            if (typeof document !== 'undefined') {\n                fontFaceSet = document.fonts;\n            }\n            else if (typeof self !== 'undefined' && 'fonts' in self) {\n                fontFaceSet = self.fonts;\n            }\n            fontFaceSet === null || fontFaceSet === void 0 ? void 0 : fontFaceSet.add(fontFace);\n            return fontFaceLoadPromise;\n        });\n    }\n    static getURLForFont(fontName) {\n        const files = Font.FILES;\n        if (!(fontName in files)) {\n            return undefined;\n        }\n        return Font.HOST_URL + files[fontName];\n    }\n}\nFont.scaleToPxFrom = {\n    pt: 4 / 3,\n    px: 1,\n    em: 16,\n    '%': 4 / 25,\n    in: 96,\n    mm: 96 / 25.4,\n    cm: 96 / 2.54,\n};\nFont.HOST_URL = 'https://cdn.jsdelivr.net/npm/@vexflow-fonts/';\nFont.FILES = {\n    Academico: 'academico/academico.woff2',\n    Bravura: 'bravura/bravura.woff2',\n    'Bravura Text': 'bravuratext/bravuratext.woff2',\n    Edwin: 'edwin/edwin-roman.woff2',\n    'Finale Ash': 'finaleash/finaleash.woff2',\n    'Finale Ash Text': 'finaleashtext/finaleashtext.woff2',\n    'Finale Broadway': 'finalebroadway/finalebroadway.woff2',\n    'Finale Broadway Text': 'finalebroadwaytext/finalebroadwaytext.woff2',\n    'Finale Jazz': 'finalejazz/finalejazz.woff2',\n    'Finale Jazz Text': 'finalejazztext/finalejazztext.woff2',\n    'Finale Maestro': 'finalemaestro/finalemaestro.woff2',\n    'Finale Maestro Text': 'finalemaestrotext/finalemaestrotext-regular.woff2',\n    Gonville: 'gonville/gonville.woff2',\n    Gootville: 'gootville/gootville.woff2',\n    'Gootville Text': 'gootvilletext/gootvilletext.woff2',\n    Leipzig: 'leipzig/leipzig.woff2',\n    Leland: 'leland/leland.woff2',\n    'Leland Text': 'lelandtext/lelandtext.woff2',\n    MuseJazz: 'musejazz/musejazz.woff2',\n    'MuseJazz Text': 'musejazztext/musejazztext.woff2',\n    Nepomuk: 'nepomuk/nepomuk.woff2',\n    Petaluma: 'petaluma/petaluma.woff2',\n    'Petaluma Script': 'petalumascript/petalumascript.woff2',\n    'Petaluma Text': 'petalumatext/petalumatext.woff2',\n    'Roboto Slab': 'robotoslab/robotoslab-regular-400.woff2',\n    Sebastian: 'sebastian/sebastian.woff2',\n    'Sebastian Text': 'sebastiantext/sebastiantext.woff2',\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,OAAO,QAAQ,cAAc;AACtC,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/BA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM;AAC/B,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9BA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAClC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIC,UAAU;AACd,OAAO,MAAMC,IAAI,CAAC;EACd,OAAOC,uBAAuBA,CAACC,QAAQ,EAAE;IACrC,IAAIC,EAAE;IACN,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAOA,QAAQ,GAAGF,IAAI,CAACI,aAAa,CAACC,EAAE;IAC3C,CAAC,MACI;MACD,MAAMtB,KAAK,GAAGuB,UAAU,CAACJ,QAAQ,CAAC;MAClC,IAAIK,KAAK,CAACxB,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,MAAMyB,IAAI,GAAGN,QAAQ,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3D,MAAMC,gBAAgB,GAAG,CAACR,EAAE,GAAGH,IAAI,CAACI,aAAa,CAACI,IAAI,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;MAC3F,OAAOpB,KAAK,GAAG4B,gBAAgB;IACnC;EACJ;EACA,OAAOC,uBAAuBA,CAACV,QAAQ,EAAE;IACrC,IAAIC,EAAE;IACN,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAOA,QAAQ;IACnB,CAAC,MACI;MACD,MAAMnB,KAAK,GAAGuB,UAAU,CAACJ,QAAQ,CAAC;MAClC,IAAIK,KAAK,CAACxB,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,MAAMyB,IAAI,GAAGN,QAAQ,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3D,MAAMC,gBAAgB,GAAG,CAAC,CAACR,EAAE,GAAGH,IAAI,CAACI,aAAa,CAACI,IAAI,CAAC,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,IAAIH,IAAI,CAACI,aAAa,CAACC,EAAE;MACrH,OAAOtB,KAAK,GAAG4B,gBAAgB;IACnC;EACJ;EACA,OAAOE,QAAQA,CAACC,CAAC,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACpC,IAAI,OAAOH,CAAC,KAAK,QAAQ,IAAIC,IAAI,KAAKG,SAAS,IAAIF,MAAM,KAAKE,SAAS,IAAID,KAAK,KAAKC,SAAS,EAAE;MAC5F,OAAOlB,IAAI,CAACmB,aAAa,CAACL,CAAC,CAAC;IAChC;IACA,IAAIM,MAAM;IACV,IAAI,OAAON,CAAC,KAAK,QAAQ,EAAE;MACvBM,MAAM,GAAGN,CAAC,CAACM,MAAM;MACjBL,IAAI,GAAGD,CAAC,CAACC,IAAI;MACbC,MAAM,GAAGF,CAAC,CAACE,MAAM;MACjBC,KAAK,GAAGH,CAAC,CAACG,KAAK;IACnB,CAAC,MACI;MACDG,MAAM,GAAGN,CAAC;IACd;IACAM,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGxB,OAAO,CAACyB,GAAG,CAAC,YAAY,CAAC;IAClFN,IAAI,GAAGA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGnB,OAAO,CAACyB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;IAC/EL,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGnB,UAAU,CAACyB,MAAM;IAC1EL,KAAK,GAAGA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGnB,SAAS,CAACwB,MAAM;IACrE,IAAIN,MAAM,KAAK,EAAE,EAAE;MACfA,MAAM,GAAGnB,UAAU,CAACyB,MAAM;IAC9B;IACA,IAAIL,KAAK,KAAK,EAAE,EAAE;MACdA,KAAK,GAAGnB,SAAS,CAACwB,MAAM;IAC5B;IACA,IAAI,OAAOP,IAAI,KAAK,QAAQ,EAAE;MAC1BA,IAAI,GAAG,GAAGA,IAAI,IAAI;IACtB;IACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGA,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC9B;IACA,OAAO;MAAEH,MAAM;MAAEL,IAAI;MAAEC,MAAM;MAAEC;IAAM,CAAC;EAC1C;EACA,OAAOE,aAAaA,CAACK,gBAAgB,EAAE;IACnC,IAAI,CAACzB,UAAU,EAAE;MACbA,UAAU,GAAG0B,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC/C;IACA3B,UAAU,CAACkB,KAAK,CAACU,IAAI,GAAGH,gBAAgB;IACxC,MAAM;MAAEI,UAAU;MAAE1B,QAAQ;MAAE2B,UAAU;MAAEC;IAAU,CAAC,GAAG/B,UAAU,CAACkB,KAAK;IACxE,OAAO;MAAEG,MAAM,EAAEQ,UAAU;MAAEb,IAAI,EAAEb,QAAQ;MAAEc,MAAM,EAAEa,UAAU;MAAEZ,KAAK,EAAEa;IAAU,CAAC;EACvF;EACA,OAAOC,WAAWA,CAACC,QAAQ,EAAE;IACzB,IAAI7B,EAAE;IACN,IAAI,CAAC6B,QAAQ,EAAE;MACX,OAAO,EAAE;IACb;IACA,IAAIf,KAAK;IACT,MAAMgB,EAAE,GAAGD,QAAQ,CAACf,KAAK;IACzB,IAAIgB,EAAE,KAAKnC,SAAS,CAACwB,MAAM,IAAIW,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAKf,SAAS,EAAE;MAC1DD,KAAK,GAAG,EAAE;IACd,CAAC,MACI;MACDA,KAAK,GAAGgB,EAAE,CAACC,IAAI,CAAC,CAAC,GAAG,GAAG;IAC3B;IACA,IAAIlB,MAAM;IACV,MAAMmB,EAAE,GAAGH,QAAQ,CAAChB,MAAM;IAC1B,IAAImB,EAAE,KAAKtC,UAAU,CAACyB,MAAM,IAAIa,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAKjB,SAAS,EAAE;MAC3DF,MAAM,GAAG,EAAE;IACf,CAAC,MACI,IAAI,OAAOmB,EAAE,KAAK,QAAQ,EAAE;MAC7BnB,MAAM,GAAGmB,EAAE,GAAG,GAAG;IACrB,CAAC,MACI;MACDnB,MAAM,GAAGmB,EAAE,CAACD,IAAI,CAAC,CAAC,GAAG,GAAG;IAC5B;IACA,IAAInB,IAAI;IACR,MAAMqB,EAAE,GAAGJ,QAAQ,CAACjB,IAAI;IACxB,IAAIqB,EAAE,KAAKlB,SAAS,EAAE;MAClBH,IAAI,GAAGnB,OAAO,CAACyB,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;IACzC,CAAC,MACI,IAAI,OAAOe,EAAE,KAAK,QAAQ,EAAE;MAC7BrB,IAAI,GAAGqB,EAAE,GAAG,KAAK;IACrB,CAAC,MACI;MACDrB,IAAI,GAAGqB,EAAE,CAACF,IAAI,CAAC,CAAC,GAAG,GAAG;IAC1B;IACA,MAAMd,MAAM,GAAG,CAACjB,EAAE,GAAG6B,QAAQ,CAACZ,MAAM,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGP,OAAO,CAACyB,GAAG,CAAC,YAAY,CAAC;IAChG,OAAO,GAAGJ,KAAK,GAAGD,MAAM,GAAGD,IAAI,GAAGK,MAAM,EAAE;EAC9C;EACA,OAAOiB,SAASA,CAACnC,QAAQ,EAAEoC,WAAW,EAAE;IACpC,IAAI,OAAOpC,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAQA,QAAQ,GAAGoC,WAAW;IAClC,CAAC,MACI;MACD,MAAMvD,KAAK,GAAGuB,UAAU,CAACJ,QAAQ,CAAC;MAClC,MAAMM,IAAI,GAAGN,QAAQ,CAACO,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;MAC7C,OAAO,GAAG1B,KAAK,GAAGuD,WAAW,GAAG9B,IAAI,EAAE;IAC1C;EACJ;EACA,OAAO+B,MAAMA,CAACvB,MAAM,EAAE;IAClB,IAAI,CAACA,MAAM,EAAE;MACT,OAAO,KAAK;IAChB,CAAC,MACI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MACjC,OAAOA,MAAM,IAAI,GAAG;IACxB,CAAC,MACI;MACD,MAAMwB,YAAY,GAAGC,QAAQ,CAACzB,MAAM,EAAE,EAAE,CAAC;MACzC,IAAIT,KAAK,CAACiC,YAAY,CAAC,EAAE;QACrB,OAAOxB,MAAM,CAACN,WAAW,CAAC,CAAC,KAAK,MAAM;MAC1C,CAAC,MACI;QACD,OAAO8B,YAAY,IAAI,GAAG;MAC9B;IACJ;EACJ;EACA,OAAOE,QAAQA,CAACzB,KAAK,EAAE;IACnB,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,KAAK;IAChB,CAAC,MACI;MACD,OAAOA,KAAK,CAACP,WAAW,CAAC,CAAC,KAAKZ,SAAS,CAAC6C,MAAM;IACnD;EACJ;EACA,OAAOC,IAAIA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAE;IACpC,OAAOtE,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI,OAAOuE,QAAQ,KAAK,WAAW,EAAE;QACjC,OAAO/D,OAAO,CAACC,MAAM,CAAC,IAAI+D,KAAK,CAAC,uEAAuE,CAAC,CAAC;MAC7G;MACA,IAAIH,GAAG,KAAK5B,SAAS,EAAE;QACnB,MAAMgC,KAAK,GAAGlD,IAAI,CAACmD,KAAK;QACxB,IAAI,EAAEN,QAAQ,IAAIK,KAAK,CAAC,EAAE;UACtB,OAAOjE,OAAO,CAACC,MAAM,CAAC,IAAI+D,KAAK,CAAC,QAAQJ,QAAQ,0BAA0B,CAAC,CAAC;QAChF;QACAC,GAAG,GAAG9C,IAAI,CAACoD,QAAQ,GAAGF,KAAK,CAACL,QAAQ,CAAC;MACzC;MACA,MAAMQ,QAAQ,GAAG,IAAIL,QAAQ,CAACH,QAAQ,EAAE,OAAOC,GAAG,GAAG,EAAEC,WAAW,CAAC;MACnE,MAAMO,mBAAmB,GAAGD,QAAQ,CAACT,IAAI,CAAC,CAAC;MAC3C,IAAIW,WAAW;MACf,IAAI,OAAO9B,QAAQ,KAAK,WAAW,EAAE;QACjC8B,WAAW,GAAG9B,QAAQ,CAAC+B,KAAK;MAChC,CAAC,MACI,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAO,IAAIA,IAAI,EAAE;QACrDF,WAAW,GAAGE,IAAI,CAACD,KAAK;MAC5B;MACAD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,GAAG,CAACL,QAAQ,CAAC;MACnF,OAAOC,mBAAmB;IAC9B,CAAC,CAAC;EACN;EACA,OAAOK,aAAaA,CAACd,QAAQ,EAAE;IAC3B,MAAMK,KAAK,GAAGlD,IAAI,CAACmD,KAAK;IACxB,IAAI,EAAEN,QAAQ,IAAIK,KAAK,CAAC,EAAE;MACtB,OAAOhC,SAAS;IACpB;IACA,OAAOlB,IAAI,CAACoD,QAAQ,GAAGF,KAAK,CAACL,QAAQ,CAAC;EAC1C;AACJ;AACA7C,IAAI,CAACI,aAAa,GAAG;EACjBC,EAAE,EAAE,CAAC,GAAG,CAAC;EACTuD,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,EAAE;EACN,GAAG,EAAE,CAAC,GAAG,EAAE;EACXC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE,GAAG,IAAI;EACbC,EAAE,EAAE,EAAE,GAAG;AACb,CAAC;AACDhE,IAAI,CAACoD,QAAQ,GAAG,8CAA8C;AAC9DpD,IAAI,CAACmD,KAAK,GAAG;EACTc,SAAS,EAAE,2BAA2B;EACtCC,OAAO,EAAE,uBAAuB;EAChC,cAAc,EAAE,+BAA+B;EAC/CC,KAAK,EAAE,yBAAyB;EAChC,YAAY,EAAE,2BAA2B;EACzC,iBAAiB,EAAE,mCAAmC;EACtD,iBAAiB,EAAE,qCAAqC;EACxD,sBAAsB,EAAE,6CAA6C;EACrE,aAAa,EAAE,6BAA6B;EAC5C,kBAAkB,EAAE,qCAAqC;EACzD,gBAAgB,EAAE,mCAAmC;EACrD,qBAAqB,EAAE,mDAAmD;EAC1EC,QAAQ,EAAE,yBAAyB;EACnCC,SAAS,EAAE,2BAA2B;EACtC,gBAAgB,EAAE,mCAAmC;EACrDC,OAAO,EAAE,uBAAuB;EAChCC,MAAM,EAAE,qBAAqB;EAC7B,aAAa,EAAE,6BAA6B;EAC5CC,QAAQ,EAAE,yBAAyB;EACnC,eAAe,EAAE,iCAAiC;EAClDC,OAAO,EAAE,uBAAuB;EAChCC,QAAQ,EAAE,yBAAyB;EACnC,iBAAiB,EAAE,qCAAqC;EACxD,eAAe,EAAE,iCAAiC;EAClD,aAAa,EAAE,yCAAyC;EACxDC,SAAS,EAAE,2BAA2B;EACtC,gBAAgB,EAAE;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}