{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier } from './stavemodifier.js';\nexport class Repetition extends StaveModifier {\n  static get CATEGORY() {\n    return \"Repetition\";\n  }\n  constructor(type, x, yShift) {\n    super();\n    this.symbolType = type;\n    this.x = x;\n    this.xShift = 0;\n    this.yShift = yShift;\n  }\n  setShiftX(x) {\n    this.xShift = x;\n    return this;\n  }\n  setShiftY(y) {\n    this.yShift = y;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const x = stave.getModifierXShift(this.getPosition());\n    this.setRendered();\n    switch (this.symbolType) {\n      case Repetition.type.CODA_RIGHT:\n        this.drawCodaFixed(stave, x + stave.getWidth());\n        break;\n      case Repetition.type.CODA_LEFT:\n        this.drawSymbolText(stave, x, 'Coda', true);\n        break;\n      case Repetition.type.SEGNO_LEFT:\n        this.drawSegnoFixed(stave, x);\n        break;\n      case Repetition.type.SEGNO_RIGHT:\n        this.drawSegnoFixed(stave, x + stave.getWidth());\n        break;\n      case Repetition.type.DC:\n        this.drawSymbolText(stave, x, 'D.C.', false);\n        break;\n      case Repetition.type.DC_AL_CODA:\n        this.drawSymbolText(stave, x, 'D.C. al', true);\n        break;\n      case Repetition.type.DC_AL_FINE:\n        this.drawSymbolText(stave, x, 'D.C. al Fine', false);\n        break;\n      case Repetition.type.DS:\n        this.drawSymbolText(stave, x, 'D.S.', false);\n        break;\n      case Repetition.type.DS_AL_CODA:\n        this.drawSymbolText(stave, x, 'D.S. al', true);\n        break;\n      case Repetition.type.DS_AL_FINE:\n        this.drawSymbolText(stave, x, 'D.S. al Fine', false);\n        break;\n      case Repetition.type.FINE:\n        this.drawSymbolText(stave, x, 'Fine', false);\n        break;\n      case Repetition.type.TO_CODA:\n        this.drawSymbolText(stave, x, 'To', true);\n        break;\n      default:\n        break;\n    }\n  }\n  drawCodaFixed(stave, x) {\n    const y = stave.getYForTopText(stave.getNumLines());\n    this.text = Glyphs.coda;\n    this.renderText(stave.checkContext(), x, y + Metrics.get('Repetition.coda.offsetY'));\n    return this;\n  }\n  drawSegnoFixed(stave, x) {\n    const y = stave.getYForTopText(stave.getNumLines());\n    this.text = Glyphs.segno;\n    this.renderText(stave.checkContext(), x, y + Metrics.get('Repetition.segno.offsetY'));\n    return this;\n  }\n  drawSymbolText(stave, x, text, drawCoda) {\n    const ctx = stave.checkContext();\n    let textX = 0;\n    this.text = text;\n    if (drawCoda) {\n      this.text += ' \\ue048';\n    }\n    this.setFont(Metrics.getFontInfo('Repetition.text'));\n    switch (this.symbolType) {\n      case Repetition.type.CODA_LEFT:\n        textX = stave.getVerticalBarWidth();\n        break;\n      case Repetition.type.DC:\n      case Repetition.type.DC_AL_FINE:\n      case Repetition.type.DS:\n      case Repetition.type.DS_AL_FINE:\n      case Repetition.type.FINE:\n      default:\n        textX = x - (stave.getNoteStartX() - this.x) + stave.getWidth() - this.width - Metrics.get('Repetition.text.offsetX');\n    }\n    const y = stave.getYForTopText(stave.getNumLines()) + Metrics.get('Repetition.text.offsetY');\n    this.renderText(ctx, textX, y);\n    return this;\n  }\n}\nRepetition.type = {\n  NONE: 1,\n  CODA_LEFT: 2,\n  CODA_RIGHT: 3,\n  SEGNO_LEFT: 4,\n  SEGNO_RIGHT: 5,\n  DC: 6,\n  DC_AL_CODA: 7,\n  DC_AL_FINE: 8,\n  DS: 9,\n  DS_AL_CODA: 10,\n  DS_AL_FINE: 11,\n  FINE: 12,\n  TO_CODA: 13\n};", "map": {"version": 3, "names": ["Glyphs", "Metrics", "StaveModifier", "Repetition", "CATEGORY", "constructor", "type", "x", "yShift", "symbolType", "xShift", "setShiftX", "setShiftY", "y", "draw", "stave", "checkStave", "getModifierXShift", "getPosition", "setRendered", "CODA_RIGHT", "drawCodaFixed", "getWidth", "CODA_LEFT", "drawSymbolText", "SEGNO_LEFT", "drawSegnoFixed", "SEGNO_RIGHT", "DC", "DC_AL_CODA", "DC_AL_FINE", "DS", "DS_AL_CODA", "DS_AL_FINE", "FINE", "TO_CODA", "getYForTopText", "getNumLines", "text", "coda", "renderText", "checkContext", "get", "segno", "drawCoda", "ctx", "textX", "setFont", "getFontInfo", "getVerticalBarWidth", "getNoteStartX", "width", "NONE"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/staverepetition.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier } from './stavemodifier.js';\nexport class Repetition extends StaveModifier {\n    static get CATEGORY() {\n        return \"Repetition\";\n    }\n    constructor(type, x, yShift) {\n        super();\n        this.symbolType = type;\n        this.x = x;\n        this.xShift = 0;\n        this.yShift = yShift;\n    }\n    setShiftX(x) {\n        this.xShift = x;\n        return this;\n    }\n    setShiftY(y) {\n        this.yShift = y;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const x = stave.getModifierXShift(this.getPosition());\n        this.setRendered();\n        switch (this.symbolType) {\n            case Repetition.type.CODA_RIGHT:\n                this.drawCodaFixed(stave, x + stave.getWidth());\n                break;\n            case Repetition.type.CODA_LEFT:\n                this.drawSymbolText(stave, x, 'Coda', true);\n                break;\n            case Repetition.type.SEGNO_LEFT:\n                this.drawSegnoFixed(stave, x);\n                break;\n            case Repetition.type.SEGNO_RIGHT:\n                this.drawSegnoFixed(stave, x + stave.getWidth());\n                break;\n            case Repetition.type.DC:\n                this.drawSymbolText(stave, x, 'D.C.', false);\n                break;\n            case Repetition.type.DC_AL_CODA:\n                this.drawSymbolText(stave, x, 'D.C. al', true);\n                break;\n            case Repetition.type.DC_AL_FINE:\n                this.drawSymbolText(stave, x, 'D.C. al Fine', false);\n                break;\n            case Repetition.type.DS:\n                this.drawSymbolText(stave, x, 'D.S.', false);\n                break;\n            case Repetition.type.DS_AL_CODA:\n                this.drawSymbolText(stave, x, 'D.S. al', true);\n                break;\n            case Repetition.type.DS_AL_FINE:\n                this.drawSymbolText(stave, x, 'D.S. al Fine', false);\n                break;\n            case Repetition.type.FINE:\n                this.drawSymbolText(stave, x, 'Fine', false);\n                break;\n            case Repetition.type.TO_CODA:\n                this.drawSymbolText(stave, x, 'To', true);\n                break;\n            default:\n                break;\n        }\n    }\n    drawCodaFixed(stave, x) {\n        const y = stave.getYForTopText(stave.getNumLines());\n        this.text = Glyphs.coda;\n        this.renderText(stave.checkContext(), x, y + Metrics.get('Repetition.coda.offsetY'));\n        return this;\n    }\n    drawSegnoFixed(stave, x) {\n        const y = stave.getYForTopText(stave.getNumLines());\n        this.text = Glyphs.segno;\n        this.renderText(stave.checkContext(), x, y + Metrics.get('Repetition.segno.offsetY'));\n        return this;\n    }\n    drawSymbolText(stave, x, text, drawCoda) {\n        const ctx = stave.checkContext();\n        let textX = 0;\n        this.text = text;\n        if (drawCoda) {\n            this.text += ' \\ue048';\n        }\n        this.setFont(Metrics.getFontInfo('Repetition.text'));\n        switch (this.symbolType) {\n            case Repetition.type.CODA_LEFT:\n                textX = stave.getVerticalBarWidth();\n                break;\n            case Repetition.type.DC:\n            case Repetition.type.DC_AL_FINE:\n            case Repetition.type.DS:\n            case Repetition.type.DS_AL_FINE:\n            case Repetition.type.FINE:\n            default:\n                textX =\n                    x - (stave.getNoteStartX() - this.x) + stave.getWidth() - this.width - Metrics.get('Repetition.text.offsetX');\n        }\n        const y = stave.getYForTopText(stave.getNumLines()) + Metrics.get('Repetition.text.offsetY');\n        this.renderText(ctx, textX, y);\n        return this;\n    }\n}\nRepetition.type = {\n    NONE: 1,\n    CODA_LEFT: 2,\n    CODA_RIGHT: 3,\n    SEGNO_LEFT: 4,\n    SEGNO_RIGHT: 5,\n    DC: 6,\n    DC_AL_CODA: 7,\n    DC_AL_FINE: 8,\n    DS: 9,\n    DS_AL_CODA: 10,\n    DS_AL_FINE: 11,\n    FINE: 12,\n    TO_CODA: 13,\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,UAAU,SAASD,aAAa,CAAC;EAC1C,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACAC,WAAWA,CAACC,IAAI,EAAEC,CAAC,EAAEC,MAAM,EAAE;IACzB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,UAAU,GAAGH,IAAI;IACtB,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACG,MAAM,GAAG,CAAC;IACf,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACAG,SAASA,CAACJ,CAAC,EAAE;IACT,IAAI,CAACG,MAAM,GAAGH,CAAC;IACf,OAAO,IAAI;EACf;EACAK,SAASA,CAACC,CAAC,EAAE;IACT,IAAI,CAACL,MAAM,GAAGK,CAAC;IACf,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMT,CAAC,GAAGQ,KAAK,CAACE,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IACrD,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,QAAQ,IAAI,CAACV,UAAU;MACnB,KAAKN,UAAU,CAACG,IAAI,CAACc,UAAU;QAC3B,IAAI,CAACC,aAAa,CAACN,KAAK,EAAER,CAAC,GAAGQ,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC;QAC/C;MACJ,KAAKnB,UAAU,CAACG,IAAI,CAACiB,SAAS;QAC1B,IAAI,CAACC,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC;QAC3C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAACmB,UAAU;QAC3B,IAAI,CAACC,cAAc,CAACX,KAAK,EAAER,CAAC,CAAC;QAC7B;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAACqB,WAAW;QAC5B,IAAI,CAACD,cAAc,CAACX,KAAK,EAAER,CAAC,GAAGQ,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC;QAChD;MACJ,KAAKnB,UAAU,CAACG,IAAI,CAACsB,EAAE;QACnB,IAAI,CAACJ,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;QAC5C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAACuB,UAAU;QAC3B,IAAI,CAACL,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;QAC9C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAACwB,UAAU;QAC3B,IAAI,CAACN,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC;QACpD;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAACyB,EAAE;QACnB,IAAI,CAACP,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;QAC5C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAAC0B,UAAU;QAC3B,IAAI,CAACR,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC;QAC9C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAAC2B,UAAU;QAC3B,IAAI,CAACT,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC;QACpD;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAAC4B,IAAI;QACrB,IAAI,CAACV,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC;QAC5C;MACJ,KAAKJ,UAAU,CAACG,IAAI,CAAC6B,OAAO;QACxB,IAAI,CAACX,cAAc,CAACT,KAAK,EAAER,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QACzC;MACJ;QACI;IACR;EACJ;EACAc,aAAaA,CAACN,KAAK,EAAER,CAAC,EAAE;IACpB,MAAMM,CAAC,GAAGE,KAAK,CAACqB,cAAc,CAACrB,KAAK,CAACsB,WAAW,CAAC,CAAC,CAAC;IACnD,IAAI,CAACC,IAAI,GAAGtC,MAAM,CAACuC,IAAI;IACvB,IAAI,CAACC,UAAU,CAACzB,KAAK,CAAC0B,YAAY,CAAC,CAAC,EAAElC,CAAC,EAAEM,CAAC,GAAGZ,OAAO,CAACyC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACpF,OAAO,IAAI;EACf;EACAhB,cAAcA,CAACX,KAAK,EAAER,CAAC,EAAE;IACrB,MAAMM,CAAC,GAAGE,KAAK,CAACqB,cAAc,CAACrB,KAAK,CAACsB,WAAW,CAAC,CAAC,CAAC;IACnD,IAAI,CAACC,IAAI,GAAGtC,MAAM,CAAC2C,KAAK;IACxB,IAAI,CAACH,UAAU,CAACzB,KAAK,CAAC0B,YAAY,CAAC,CAAC,EAAElC,CAAC,EAAEM,CAAC,GAAGZ,OAAO,CAACyC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IACrF,OAAO,IAAI;EACf;EACAlB,cAAcA,CAACT,KAAK,EAAER,CAAC,EAAE+B,IAAI,EAAEM,QAAQ,EAAE;IACrC,MAAMC,GAAG,GAAG9B,KAAK,CAAC0B,YAAY,CAAC,CAAC;IAChC,IAAIK,KAAK,GAAG,CAAC;IACb,IAAI,CAACR,IAAI,GAAGA,IAAI;IAChB,IAAIM,QAAQ,EAAE;MACV,IAAI,CAACN,IAAI,IAAI,SAAS;IAC1B;IACA,IAAI,CAACS,OAAO,CAAC9C,OAAO,CAAC+C,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACpD,QAAQ,IAAI,CAACvC,UAAU;MACnB,KAAKN,UAAU,CAACG,IAAI,CAACiB,SAAS;QAC1BuB,KAAK,GAAG/B,KAAK,CAACkC,mBAAmB,CAAC,CAAC;QACnC;MACJ,KAAK9C,UAAU,CAACG,IAAI,CAACsB,EAAE;MACvB,KAAKzB,UAAU,CAACG,IAAI,CAACwB,UAAU;MAC/B,KAAK3B,UAAU,CAACG,IAAI,CAACyB,EAAE;MACvB,KAAK5B,UAAU,CAACG,IAAI,CAAC2B,UAAU;MAC/B,KAAK9B,UAAU,CAACG,IAAI,CAAC4B,IAAI;MACzB;QACIY,KAAK,GACDvC,CAAC,IAAIQ,KAAK,CAACmC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC3C,CAAC,CAAC,GAAGQ,KAAK,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC6B,KAAK,GAAGlD,OAAO,CAACyC,GAAG,CAAC,yBAAyB,CAAC;IACzH;IACA,MAAM7B,CAAC,GAAGE,KAAK,CAACqB,cAAc,CAACrB,KAAK,CAACsB,WAAW,CAAC,CAAC,CAAC,GAAGpC,OAAO,CAACyC,GAAG,CAAC,yBAAyB,CAAC;IAC5F,IAAI,CAACF,UAAU,CAACK,GAAG,EAAEC,KAAK,EAAEjC,CAAC,CAAC;IAC9B,OAAO,IAAI;EACf;AACJ;AACAV,UAAU,CAACG,IAAI,GAAG;EACd8C,IAAI,EAAE,CAAC;EACP7B,SAAS,EAAE,CAAC;EACZH,UAAU,EAAE,CAAC;EACbK,UAAU,EAAE,CAAC;EACbE,WAAW,EAAE,CAAC;EACdC,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,EAAE,EAAE,CAAC;EACLC,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,EAAE;EACdC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}