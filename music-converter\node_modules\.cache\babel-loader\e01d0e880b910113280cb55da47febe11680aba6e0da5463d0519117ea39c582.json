{"ast": null, "code": "import { Note } from './note.js';\nexport class GlyphN<PERSON> extends Note {\n  static get CATEGORY() {\n    return \"GlyphNote\";\n  }\n  constructor(glyph, noteStruct, options) {\n    super(noteStruct);\n    this.options = Object.assign({\n      ignoreTicks: false,\n      line: 2\n    }, options);\n    this.ignoreTicks = this.options.ignoreTicks;\n    this.setGlyph(glyph);\n  }\n  setGlyph(glyph) {\n    this.text = glyph;\n    return this;\n  }\n  preFormat() {\n    if (!this.preFormatted && this.modifierContext) {\n      this.modifierContext.preFormat();\n    }\n    this.preFormatted = true;\n    return this;\n  }\n  drawModifiers() {\n    const ctx = this.checkContext();\n    for (let i = 0; i < this.modifiers.length; i++) {\n      const modifier = this.modifiers[i];\n      modifier.setContext(ctx);\n      modifier.drawWithStyle();\n    }\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    ctx.openGroup('glyphNote', this.getAttribute('id'));\n    this.x = this.isCenterAligned() ? this.getAbsoluteX() - this.getWidth() / 2 : this.getAbsoluteX();\n    this.y = stave.getYForLine(this.options.line);\n    this.renderText(ctx, 0, 0);\n    this.drawModifiers();\n    ctx.closeGroup();\n  }\n}", "map": {"version": 3, "names": ["Note", "GlyphNote", "CATEGORY", "constructor", "glyph", "noteStruct", "options", "Object", "assign", "ignoreTicks", "line", "setGlyph", "text", "preFormat", "preFormatted", "modifierContext", "drawModifiers", "ctx", "checkContext", "i", "modifiers", "length", "modifier", "setContext", "drawWithStyle", "draw", "stave", "checkStave", "setRendered", "openGroup", "getAttribute", "x", "isCenterAligned", "getAbsoluteX", "getWidth", "y", "getYForLine", "renderText", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/glyphnote.js"], "sourcesContent": ["import { Note } from './note.js';\nexport class GlyphN<PERSON> extends Note {\n    static get CATEGORY() {\n        return \"GlyphNote\";\n    }\n    constructor(glyph, noteStruct, options) {\n        super(noteStruct);\n        this.options = Object.assign({ ignoreTicks: false, line: 2 }, options);\n        this.ignoreTicks = this.options.ignoreTicks;\n        this.setGlyph(glyph);\n    }\n    setGlyph(glyph) {\n        this.text = glyph;\n        return this;\n    }\n    preFormat() {\n        if (!this.preFormatted && this.modifierContext) {\n            this.modifierContext.preFormat();\n        }\n        this.preFormatted = true;\n        return this;\n    }\n    drawModifiers() {\n        const ctx = this.checkContext();\n        for (let i = 0; i < this.modifiers.length; i++) {\n            const modifier = this.modifiers[i];\n            modifier.setContext(ctx);\n            modifier.drawWithStyle();\n        }\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        ctx.openGroup('glyphNote', this.getAttribute('id'));\n        this.x = this.isCenterAligned() ? this.getAbsoluteX() - this.getWidth() / 2 : this.getAbsoluteX();\n        this.y = stave.getYForLine(this.options.line);\n        this.renderText(ctx, 0, 0);\n        this.drawModifiers();\n        ctx.closeGroup();\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,OAAO,MAAMC,SAAS,SAASD,IAAI,CAAC;EAChC,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACAC,WAAWA,CAACC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACpC,KAAK,CAACD,UAAU,CAAC;IACjB,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,WAAW,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAE,CAAC,EAAEJ,OAAO,CAAC;IACtE,IAAI,CAACG,WAAW,GAAG,IAAI,CAACH,OAAO,CAACG,WAAW;IAC3C,IAAI,CAACE,QAAQ,CAACP,KAAK,CAAC;EACxB;EACAO,QAAQA,CAACP,KAAK,EAAE;IACZ,IAAI,CAACQ,IAAI,GAAGR,KAAK;IACjB,OAAO,IAAI;EACf;EACAS,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACC,YAAY,IAAI,IAAI,CAACC,eAAe,EAAE;MAC5C,IAAI,CAACA,eAAe,CAACF,SAAS,CAAC,CAAC;IACpC;IACA,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAE,aAAaA,CAAA,EAAG;IACZ,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5C,MAAMG,QAAQ,GAAG,IAAI,CAACF,SAAS,CAACD,CAAC,CAAC;MAClCG,QAAQ,CAACC,UAAU,CAACN,GAAG,CAAC;MACxBK,QAAQ,CAACE,aAAa,CAAC,CAAC;IAC5B;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMV,GAAG,GAAGS,KAAK,CAACR,YAAY,CAAC,CAAC;IAChC,IAAI,CAACU,WAAW,CAAC,CAAC;IAClBX,GAAG,CAACY,SAAS,CAAC,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACnD,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IACjG,IAAI,CAACE,CAAC,GAAGT,KAAK,CAACU,WAAW,CAAC,IAAI,CAAC9B,OAAO,CAACI,IAAI,CAAC;IAC7C,IAAI,CAAC2B,UAAU,CAACpB,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACD,aAAa,CAAC,CAAC;IACpBC,GAAG,CAACqB,UAAU,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}