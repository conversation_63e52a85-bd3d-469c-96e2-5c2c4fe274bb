{"ast": null, "code": "import { Element } from './element.js';\nimport { defined, RuntimeError } from './util.js';\nexport var ModifierPosition;\n(function (ModifierPosition) {\n  ModifierPosition[ModifierPosition[\"CENTER\"] = 0] = \"CENTER\";\n  ModifierPosition[ModifierPosition[\"LEFT\"] = 1] = \"LEFT\";\n  ModifierPosition[ModifierPosition[\"RIGHT\"] = 2] = \"RIGHT\";\n  ModifierPosition[ModifierPosition[\"ABOVE\"] = 3] = \"ABOVE\";\n  ModifierPosition[ModifierPosition[\"BELOW\"] = 4] = \"BELOW\";\n})(ModifierPosition || (ModifierPosition = {}));\nexport class Modifier extends Element {\n  static get CATEGORY() {\n    return \"Modifier\";\n  }\n  static get Position() {\n    return ModifierPosition;\n  }\n  static get PositionString() {\n    return {\n      center: ModifierPosition.CENTER,\n      above: ModifierPosition.ABOVE,\n      below: ModifierPosition.BELOW,\n      left: ModifierPosition.LEFT,\n      right: ModifierPosition.RIGHT\n    };\n  }\n  constructor() {\n    super();\n    this.width = 0;\n    this.textLine = 0;\n    this.position = Modifier.Position.LEFT;\n    this.spacingFromNextModifier = 0;\n  }\n  reset() {}\n  getNote() {\n    return defined(this.note, 'NoNote', 'Modifier has no note.');\n  }\n  checkAttachedNote() {\n    const category = this.getCategory();\n    defined(this.index, 'NoIndex', `Can't draw ${category} without an index.`);\n    return defined(this.note, 'NoNote', `Can't draw ${category} without a note.`);\n  }\n  setNote(note) {\n    this.note = note;\n    return this;\n  }\n  getIndex() {\n    return this.index;\n  }\n  checkIndex() {\n    return defined(this.index, 'NoIndex', 'Modifier has an invalid index.');\n  }\n  setIndex(index) {\n    this.index = index;\n    return this;\n  }\n  getModifierContext() {\n    return this.modifierContext;\n  }\n  checkModifierContext() {\n    return defined(this.modifierContext, 'NoModifierContext', 'Modifier Context Required');\n  }\n  setModifierContext(c) {\n    this.modifierContext = c;\n    return this;\n  }\n  getPosition() {\n    return this.position;\n  }\n  setPosition(position) {\n    this.position = typeof position === 'string' ? Modifier.PositionString[position] : position;\n    this.reset();\n    return this;\n  }\n  setTextLine(line) {\n    this.textLine = line;\n    return this;\n  }\n  setYShift(y) {\n    this.yShift = y;\n    return this;\n  }\n  setSpacingFromNextModifier(x) {\n    this.spacingFromNextModifier = x;\n  }\n  getSpacingFromNextModifier() {\n    return this.spacingFromNextModifier;\n  }\n  setXShift(x) {\n    this.xShift = 0;\n    if (this.position === Modifier.Position.LEFT) {\n      this.xShift -= x;\n    } else {\n      this.xShift += x;\n    }\n    return this;\n  }\n  getXShift() {\n    return this.xShift;\n  }\n  draw() {\n    this.checkContext();\n    throw new RuntimeError('NotImplemented', 'draw() not implemented for this modifier.');\n  }\n  alignSubNotesWithNote(subNotes, note, position = Modifier.Position.LEFT) {\n    const tickContext = note.getTickContext();\n    const metrics = tickContext.getMetrics();\n    const stave = note.getStave();\n    const subNoteXOffset = position === Modifier.Position.RIGHT ? tickContext.getX() + this.getSpacingFromNextModifier() * subNotes.length + 10 : tickContext.getX() - metrics.modLeftPx - metrics.modRightPx + this.getSpacingFromNextModifier();\n    subNotes.forEach(subNote => {\n      const subTickContext = subNote.getTickContext();\n      if (stave) subNote.setStave(stave);\n      subTickContext.setXOffset(subNoteXOffset);\n    });\n  }\n}", "map": {"version": 3, "names": ["Element", "defined", "RuntimeError", "ModifierPosition", "Modifier", "CATEGORY", "Position", "PositionString", "center", "CENTER", "above", "ABOVE", "below", "BELOW", "left", "LEFT", "right", "RIGHT", "constructor", "width", "textLine", "position", "spacingFromNextModifier", "reset", "getNote", "note", "checkAttachedNote", "category", "getCategory", "index", "setNote", "getIndex", "checkIndex", "setIndex", "getModifierContext", "modifierContext", "checkModifierContext", "setModifierContext", "c", "getPosition", "setPosition", "setTextLine", "line", "setYShift", "y", "yShift", "setSpacingFromNextModifier", "x", "getSpacingFromNextModifier", "setXShift", "xShift", "getXShift", "draw", "checkContext", "alignSubNotesWithNote", "subNotes", "tickContext", "getTickContext", "metrics", "getMetrics", "stave", "getStave", "subNoteXOffset", "getX", "length", "modLeftPx", "modRightPx", "for<PERSON>ach", "subNote", "subTickContext", "setStave", "setXOffset"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/modifier.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { defined, RuntimeError } from './util.js';\nexport var ModifierPosition;\n(function (ModifierPosition) {\n    ModifierPosition[ModifierPosition[\"CENTER\"] = 0] = \"CENTER\";\n    ModifierPosition[ModifierPosition[\"LEFT\"] = 1] = \"LEFT\";\n    ModifierPosition[ModifierPosition[\"RIGHT\"] = 2] = \"RIGHT\";\n    ModifierPosition[ModifierPosition[\"ABOVE\"] = 3] = \"ABOVE\";\n    ModifierPosition[ModifierPosition[\"BELOW\"] = 4] = \"BELOW\";\n})(ModifierPosition || (ModifierPosition = {}));\nexport class Modifier extends Element {\n    static get CATEGORY() {\n        return \"Modifier\";\n    }\n    static get Position() {\n        return ModifierPosition;\n    }\n    static get PositionString() {\n        return {\n            center: ModifierPosition.CENTER,\n            above: ModifierPosition.ABOVE,\n            below: ModifierPosition.BELOW,\n            left: ModifierPosition.LEFT,\n            right: ModifierPosition.RIGHT,\n        };\n    }\n    constructor() {\n        super();\n        this.width = 0;\n        this.textLine = 0;\n        this.position = Modifier.Position.LEFT;\n        this.spacingFromNextModifier = 0;\n    }\n    reset() {\n    }\n    getNote() {\n        return defined(this.note, 'NoNote', 'Modifier has no note.');\n    }\n    checkAttachedNote() {\n        const category = this.getCategory();\n        defined(this.index, 'NoIndex', `Can't draw ${category} without an index.`);\n        return defined(this.note, 'NoNote', `Can't draw ${category} without a note.`);\n    }\n    setNote(note) {\n        this.note = note;\n        return this;\n    }\n    getIndex() {\n        return this.index;\n    }\n    checkIndex() {\n        return defined(this.index, 'NoIndex', 'Modifier has an invalid index.');\n    }\n    setIndex(index) {\n        this.index = index;\n        return this;\n    }\n    getModifierContext() {\n        return this.modifierContext;\n    }\n    checkModifierContext() {\n        return defined(this.modifierContext, 'NoModifierContext', 'Modifier Context Required');\n    }\n    setModifierContext(c) {\n        this.modifierContext = c;\n        return this;\n    }\n    getPosition() {\n        return this.position;\n    }\n    setPosition(position) {\n        this.position = typeof position === 'string' ? Modifier.PositionString[position] : position;\n        this.reset();\n        return this;\n    }\n    setTextLine(line) {\n        this.textLine = line;\n        return this;\n    }\n    setYShift(y) {\n        this.yShift = y;\n        return this;\n    }\n    setSpacingFromNextModifier(x) {\n        this.spacingFromNextModifier = x;\n    }\n    getSpacingFromNextModifier() {\n        return this.spacingFromNextModifier;\n    }\n    setXShift(x) {\n        this.xShift = 0;\n        if (this.position === Modifier.Position.LEFT) {\n            this.xShift -= x;\n        }\n        else {\n            this.xShift += x;\n        }\n        return this;\n    }\n    getXShift() {\n        return this.xShift;\n    }\n    draw() {\n        this.checkContext();\n        throw new RuntimeError('NotImplemented', 'draw() not implemented for this modifier.');\n    }\n    alignSubNotesWithNote(subNotes, note, position = Modifier.Position.LEFT) {\n        const tickContext = note.getTickContext();\n        const metrics = tickContext.getMetrics();\n        const stave = note.getStave();\n        const subNoteXOffset = position === Modifier.Position.RIGHT\n            ? tickContext.getX() + this.getSpacingFromNextModifier() * subNotes.length + 10\n            : tickContext.getX() - metrics.modLeftPx - metrics.modRightPx + this.getSpacingFromNextModifier();\n        subNotes.forEach((subNote) => {\n            const subTickContext = subNote.getTickContext();\n            if (stave)\n                subNote.setStave(stave);\n            subTickContext.setXOffset(subNoteXOffset);\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AACjD,OAAO,IAAIC,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC3DA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzDA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzDA,gBAAgB,CAACA,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC7D,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,OAAO,MAAMC,QAAQ,SAASJ,OAAO,CAAC;EAClC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACA,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAOH,gBAAgB;EAC3B;EACA,WAAWI,cAAcA,CAAA,EAAG;IACxB,OAAO;MACHC,MAAM,EAAEL,gBAAgB,CAACM,MAAM;MAC/BC,KAAK,EAAEP,gBAAgB,CAACQ,KAAK;MAC7BC,KAAK,EAAET,gBAAgB,CAACU,KAAK;MAC7BC,IAAI,EAAEX,gBAAgB,CAACY,IAAI;MAC3BC,KAAK,EAAEb,gBAAgB,CAACc;IAC5B,CAAC;EACL;EACAC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAGjB,QAAQ,CAACE,QAAQ,CAACS,IAAI;IACtC,IAAI,CAACO,uBAAuB,GAAG,CAAC;EACpC;EACAC,KAAKA,CAAA,EAAG,CACR;EACAC,OAAOA,CAAA,EAAG;IACN,OAAOvB,OAAO,CAAC,IAAI,CAACwB,IAAI,EAAE,QAAQ,EAAE,uBAAuB,CAAC;EAChE;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACnC3B,OAAO,CAAC,IAAI,CAAC4B,KAAK,EAAE,SAAS,EAAE,cAAcF,QAAQ,oBAAoB,CAAC;IAC1E,OAAO1B,OAAO,CAAC,IAAI,CAACwB,IAAI,EAAE,QAAQ,EAAE,cAAcE,QAAQ,kBAAkB,CAAC;EACjF;EACAG,OAAOA,CAACL,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,KAAK;EACrB;EACAG,UAAUA,CAAA,EAAG;IACT,OAAO/B,OAAO,CAAC,IAAI,CAAC4B,KAAK,EAAE,SAAS,EAAE,gCAAgC,CAAC;EAC3E;EACAI,QAAQA,CAACJ,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAK,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,eAAe;EAC/B;EACAC,oBAAoBA,CAAA,EAAG;IACnB,OAAOnC,OAAO,CAAC,IAAI,CAACkC,eAAe,EAAE,mBAAmB,EAAE,2BAA2B,CAAC;EAC1F;EACAE,kBAAkBA,CAACC,CAAC,EAAE;IAClB,IAAI,CAACH,eAAe,GAAGG,CAAC;IACxB,OAAO,IAAI;EACf;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClB,QAAQ;EACxB;EACAmB,WAAWA,CAACnB,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAG,OAAOA,QAAQ,KAAK,QAAQ,GAAGjB,QAAQ,CAACG,cAAc,CAACc,QAAQ,CAAC,GAAGA,QAAQ;IAC3F,IAAI,CAACE,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf;EACAkB,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACtB,QAAQ,GAAGsB,IAAI;IACpB,OAAO,IAAI;EACf;EACAC,SAASA,CAACC,CAAC,EAAE;IACT,IAAI,CAACC,MAAM,GAAGD,CAAC;IACf,OAAO,IAAI;EACf;EACAE,0BAA0BA,CAACC,CAAC,EAAE;IAC1B,IAAI,CAACzB,uBAAuB,GAAGyB,CAAC;EACpC;EACAC,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAAC1B,uBAAuB;EACvC;EACA2B,SAASA,CAACF,CAAC,EAAE;IACT,IAAI,CAACG,MAAM,GAAG,CAAC;IACf,IAAI,IAAI,CAAC7B,QAAQ,KAAKjB,QAAQ,CAACE,QAAQ,CAACS,IAAI,EAAE;MAC1C,IAAI,CAACmC,MAAM,IAAIH,CAAC;IACpB,CAAC,MACI;MACD,IAAI,CAACG,MAAM,IAAIH,CAAC;IACpB;IACA,OAAO,IAAI;EACf;EACAI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,MAAM;EACtB;EACAE,IAAIA,CAAA,EAAG;IACH,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,MAAM,IAAInD,YAAY,CAAC,gBAAgB,EAAE,2CAA2C,CAAC;EACzF;EACAoD,qBAAqBA,CAACC,QAAQ,EAAE9B,IAAI,EAAEJ,QAAQ,GAAGjB,QAAQ,CAACE,QAAQ,CAACS,IAAI,EAAE;IACrE,MAAMyC,WAAW,GAAG/B,IAAI,CAACgC,cAAc,CAAC,CAAC;IACzC,MAAMC,OAAO,GAAGF,WAAW,CAACG,UAAU,CAAC,CAAC;IACxC,MAAMC,KAAK,GAAGnC,IAAI,CAACoC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,cAAc,GAAGzC,QAAQ,KAAKjB,QAAQ,CAACE,QAAQ,CAACW,KAAK,GACrDuC,WAAW,CAACO,IAAI,CAAC,CAAC,GAAG,IAAI,CAACf,0BAA0B,CAAC,CAAC,GAAGO,QAAQ,CAACS,MAAM,GAAG,EAAE,GAC7ER,WAAW,CAACO,IAAI,CAAC,CAAC,GAAGL,OAAO,CAACO,SAAS,GAAGP,OAAO,CAACQ,UAAU,GAAG,IAAI,CAAClB,0BAA0B,CAAC,CAAC;IACrGO,QAAQ,CAACY,OAAO,CAAEC,OAAO,IAAK;MAC1B,MAAMC,cAAc,GAAGD,OAAO,CAACX,cAAc,CAAC,CAAC;MAC/C,IAAIG,KAAK,EACLQ,OAAO,CAACE,QAAQ,CAACV,KAAK,CAAC;MAC3BS,cAAc,CAACE,UAAU,CAACT,cAAc,CAAC;IAC7C,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}