{"ast": null, "code": "import { Note } from './note.js';\nimport { TickContext } from './tickcontext.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (Crescendo.DEBUG) log('VexFlow.Crescendo', args);\n}\nfunction renderHairpin(ctx, params) {\n  const beginX = params.beginX;\n  const endX = params.endX;\n  const y = params.y;\n  const halfHeight = params.height / 2;\n  ctx.beginPath();\n  if (params.reverse) {\n    ctx.moveTo(beginX, y - halfHeight);\n    ctx.lineTo(endX, y);\n    ctx.lineTo(beginX, y + halfHeight);\n  } else {\n    ctx.moveTo(endX, y - halfHeight);\n    ctx.lineTo(beginX, y);\n    ctx.lineTo(endX, y + halfHeight);\n  }\n  ctx.stroke();\n  ctx.closePath();\n}\nexport class Crescendo extends Note {\n  static get CATEGORY() {\n    return \"Crescendo\";\n  }\n  constructor(noteStruct) {\n    var _a;\n    super(noteStruct);\n    this.options = {\n      extendLeft: 0,\n      extendRight: 0,\n      yShift: 0\n    };\n    this.decrescendo = false;\n    this.line = (_a = noteStruct.line) !== null && _a !== void 0 ? _a : 0;\n    this.height = 15;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  setHeight(height) {\n    this.height = height;\n    return this;\n  }\n  setDecrescendo(decresc) {\n    this.decrescendo = decresc;\n    return this;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const stave = this.checkStave();\n    this.setRendered();\n    const tickContext = this.getTickContext();\n    const nextContext = TickContext.getNextContext(tickContext);\n    const beginX = this.getAbsoluteX();\n    const endX = nextContext ? nextContext.getX() : stave.getX() + stave.getWidth();\n    const y = stave.getYForLine(this.line + -3) + 1;\n    L('Drawing ', this.decrescendo ? 'decrescendo ' : 'crescendo ', this.height, 'x', beginX - endX);\n    renderHairpin(ctx, {\n      beginX: beginX - this.options.extendLeft,\n      endX: endX + this.options.extendRight,\n      y: y + this.options.yShift,\n      height: this.height,\n      reverse: this.decrescendo\n    });\n  }\n}\nCrescendo.DEBUG = false;", "map": {"version": 3, "names": ["Note", "TickContext", "log", "L", "args", "Crescendo", "DEBUG", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "params", "beginX", "endX", "y", "halfHeight", "height", "beginPath", "reverse", "moveTo", "lineTo", "stroke", "closePath", "CATEGORY", "constructor", "noteStruct", "_a", "options", "extendLeft", "extendRight", "yShift", "decrescendo", "line", "setLine", "setHeight", "setDecrescendo", "decresc", "preFormat", "preFormatted", "draw", "checkContext", "stave", "checkStave", "setRendered", "tickContext", "getTickContext", "nextContext", "getNextContext", "getAbsoluteX", "getX", "getWidth", "getYForLine"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/crescendo.js"], "sourcesContent": ["import { Note } from './note.js';\nimport { TickContext } from './tickcontext.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (Crescendo.DEBUG)\n        log('VexFlow.Crescendo', args);\n}\nfunction renderHairpin(ctx, params) {\n    const beginX = params.beginX;\n    const endX = params.endX;\n    const y = params.y;\n    const halfHeight = params.height / 2;\n    ctx.beginPath();\n    if (params.reverse) {\n        ctx.moveTo(beginX, y - halfHeight);\n        ctx.lineTo(endX, y);\n        ctx.lineTo(beginX, y + halfHeight);\n    }\n    else {\n        ctx.moveTo(endX, y - halfHeight);\n        ctx.lineTo(beginX, y);\n        ctx.lineTo(endX, y + halfHeight);\n    }\n    ctx.stroke();\n    ctx.closePath();\n}\nexport class Crescendo extends Note {\n    static get CATEGORY() {\n        return \"Crescendo\";\n    }\n    constructor(noteStruct) {\n        var _a;\n        super(noteStruct);\n        this.options = {\n            extendLeft: 0,\n            extendRight: 0,\n            yShift: 0,\n        };\n        this.decrescendo = false;\n        this.line = (_a = noteStruct.line) !== null && _a !== void 0 ? _a : 0;\n        this.height = 15;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    setHeight(height) {\n        this.height = height;\n        return this;\n    }\n    setDecrescendo(decresc) {\n        this.decrescendo = decresc;\n        return this;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const stave = this.checkStave();\n        this.setRendered();\n        const tickContext = this.getTickContext();\n        const nextContext = TickContext.getNextContext(tickContext);\n        const beginX = this.getAbsoluteX();\n        const endX = nextContext ? nextContext.getX() : stave.getX() + stave.getWidth();\n        const y = stave.getYForLine(this.line + -3) + 1;\n        L('Drawing ', this.decrescendo ? 'decrescendo ' : 'crescendo ', this.height, 'x', beginX - endX);\n        renderHairpin(ctx, {\n            beginX: beginX - this.options.extendLeft,\n            endX: endX + this.options.extendRight,\n            y: y + this.options.yShift,\n            height: this.height,\n            reverse: this.decrescendo,\n        });\n    }\n}\nCrescendo.DEBUG = false;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,SAAS,CAACC,KAAK,EACfJ,GAAG,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACtC;AACA,SAASG,aAAaA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAChC,MAAMC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC5B,MAAMC,IAAI,GAAGF,MAAM,CAACE,IAAI;EACxB,MAAMC,CAAC,GAAGH,MAAM,CAACG,CAAC;EAClB,MAAMC,UAAU,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC;EACpCN,GAAG,CAACO,SAAS,CAAC,CAAC;EACf,IAAIN,MAAM,CAACO,OAAO,EAAE;IAChBR,GAAG,CAACS,MAAM,CAACP,MAAM,EAAEE,CAAC,GAAGC,UAAU,CAAC;IAClCL,GAAG,CAACU,MAAM,CAACP,IAAI,EAAEC,CAAC,CAAC;IACnBJ,GAAG,CAACU,MAAM,CAACR,MAAM,EAAEE,CAAC,GAAGC,UAAU,CAAC;EACtC,CAAC,MACI;IACDL,GAAG,CAACS,MAAM,CAACN,IAAI,EAAEC,CAAC,GAAGC,UAAU,CAAC;IAChCL,GAAG,CAACU,MAAM,CAACR,MAAM,EAAEE,CAAC,CAAC;IACrBJ,GAAG,CAACU,MAAM,CAACP,IAAI,EAAEC,CAAC,GAAGC,UAAU,CAAC;EACpC;EACAL,GAAG,CAACW,MAAM,CAAC,CAAC;EACZX,GAAG,CAACY,SAAS,CAAC,CAAC;AACnB;AACA,OAAO,MAAMf,SAAS,SAASL,IAAI,CAAC;EAChC,WAAWqB,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIC,EAAE;IACN,KAAK,CAACD,UAAU,CAAC;IACjB,IAAI,CAACE,OAAO,GAAG;MACXC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,MAAM,EAAE;IACZ,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,IAAI,GAAG,CAACN,EAAE,GAAGD,UAAU,CAACO,IAAI,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACrE,IAAI,CAACV,MAAM,GAAG,EAAE;EACpB;EACAiB,OAAOA,CAACD,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAE,SAASA,CAAClB,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAmB,cAAcA,CAACC,OAAO,EAAE;IACpB,IAAI,CAACL,WAAW,GAAGK,OAAO;IAC1B,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAM7B,GAAG,GAAG,IAAI,CAAC8B,YAAY,CAAC,CAAC;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACzC,MAAMC,WAAW,GAAG3C,WAAW,CAAC4C,cAAc,CAACH,WAAW,CAAC;IAC3D,MAAMhC,MAAM,GAAG,IAAI,CAACoC,YAAY,CAAC,CAAC;IAClC,MAAMnC,IAAI,GAAGiC,WAAW,GAAGA,WAAW,CAACG,IAAI,CAAC,CAAC,GAAGR,KAAK,CAACQ,IAAI,CAAC,CAAC,GAAGR,KAAK,CAACS,QAAQ,CAAC,CAAC;IAC/E,MAAMpC,CAAC,GAAG2B,KAAK,CAACU,WAAW,CAAC,IAAI,CAACnB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/C3B,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC0B,WAAW,GAAG,cAAc,GAAG,YAAY,EAAE,IAAI,CAACf,MAAM,EAAE,GAAG,EAAEJ,MAAM,GAAGC,IAAI,CAAC;IAChGJ,aAAa,CAACC,GAAG,EAAE;MACfE,MAAM,EAAEA,MAAM,GAAG,IAAI,CAACe,OAAO,CAACC,UAAU;MACxCf,IAAI,EAAEA,IAAI,GAAG,IAAI,CAACc,OAAO,CAACE,WAAW;MACrCf,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACa,OAAO,CAACG,MAAM;MAC1Bd,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,OAAO,EAAE,IAAI,CAACa;IAClB,CAAC,CAAC;EACN;AACJ;AACAxB,SAAS,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}