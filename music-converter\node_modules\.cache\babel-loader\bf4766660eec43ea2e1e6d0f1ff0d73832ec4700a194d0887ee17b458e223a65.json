{"ast": null, "code": "import { Metrics } from './metrics.js';\nimport { StaveModifier } from './stavemodifier.js';\nexport class StaveSection extends StaveModifier {\n  static get CATEGORY() {\n    return \"StaveSection\";\n  }\n  constructor(section, x = 0, yShift = 0, drawRect = true) {\n    super();\n    this.setText(section);\n    this.x = x;\n    this.yShift = yShift;\n    this.drawRect = drawRect;\n    this.padding = Metrics.get('StaveSection.padding');\n  }\n  setDrawRect(drawRect) {\n    this.drawRect = drawRect;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    this.x = stave.getX() + stave.getModifierXShift(this.getPosition());\n    const headroom = -1 * this.textMetrics.actualBoundingBoxDescent;\n    const width = this.width + 2 * this.padding;\n    const height = this.height + 2 * this.padding;\n    const y = stave.getYForTopText(1.5) + this.yShift;\n    const x = this.x + this.xShift;\n    if (this.drawRect) {\n      ctx.beginPath();\n      ctx.rect(x, y - height + headroom, width, height);\n      ctx.stroke();\n    }\n    this.renderText(ctx, this.xShift + this.padding, y - this.padding);\n  }\n}", "map": {"version": 3, "names": ["Metrics", "StaveModifier", "StaveSection", "CATEGORY", "constructor", "section", "x", "yShift", "drawRect", "setText", "padding", "get", "setDrawRect", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "getX", "getModifierXShift", "getPosition", "headroom", "textMetrics", "actualBoundingBoxDescent", "width", "height", "y", "getYForTopText", "xShift", "beginPath", "rect", "stroke", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavesection.js"], "sourcesContent": ["import { Metrics } from './metrics.js';\nimport { StaveModifier } from './stavemodifier.js';\nexport class StaveSection extends StaveModifier {\n    static get CATEGORY() {\n        return \"StaveSection\";\n    }\n    constructor(section, x = 0, yShift = 0, drawRect = true) {\n        super();\n        this.setText(section);\n        this.x = x;\n        this.yShift = yShift;\n        this.drawRect = drawRect;\n        this.padding = Metrics.get('StaveSection.padding');\n    }\n    setDrawRect(drawRect) {\n        this.drawRect = drawRect;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        this.x = stave.getX() + stave.getModifierXShift(this.getPosition());\n        const headroom = -1 * this.textMetrics.actualBoundingBoxDescent;\n        const width = this.width + 2 * this.padding;\n        const height = this.height + 2 * this.padding;\n        const y = stave.getYForTopText(1.5) + this.yShift;\n        const x = this.x + this.xShift;\n        if (this.drawRect) {\n            ctx.beginPath();\n            ctx.rect(x, y - height + headroom, width, height);\n            ctx.stroke();\n        }\n        this.renderText(ctx, this.xShift + this.padding, y - this.padding);\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,YAAY,SAASD,aAAa,CAAC;EAC5C,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACAC,WAAWA,CAACC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACrD,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,CAACJ,OAAO,CAAC;IACrB,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,OAAO,GAAGV,OAAO,CAACW,GAAG,CAAC,sBAAsB,CAAC;EACtD;EACAC,WAAWA,CAACJ,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;EACf;EACAK,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGF,KAAK,CAACG,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACZ,CAAC,GAAGQ,KAAK,CAACK,IAAI,CAAC,CAAC,GAAGL,KAAK,CAACM,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IACnE,MAAMC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAACC,WAAW,CAACC,wBAAwB;IAC/D,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACf,OAAO;IAC3C,MAAMgB,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC,GAAG,IAAI,CAAChB,OAAO;IAC7C,MAAMiB,CAAC,GAAGb,KAAK,CAACc,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM;IACjD,MAAMD,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,IAAI,CAACuB,MAAM;IAC9B,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACfQ,GAAG,CAACc,SAAS,CAAC,CAAC;MACfd,GAAG,CAACe,IAAI,CAACzB,CAAC,EAAEqB,CAAC,GAAGD,MAAM,GAAGJ,QAAQ,EAAEG,KAAK,EAAEC,MAAM,CAAC;MACjDV,GAAG,CAACgB,MAAM,CAAC,CAAC;IAChB;IACA,IAAI,CAACC,UAAU,CAACjB,GAAG,EAAE,IAAI,CAACa,MAAM,GAAG,IAAI,CAACnB,OAAO,EAAEiB,CAAC,GAAG,IAAI,CAACjB,OAAO,CAAC;EACtE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}