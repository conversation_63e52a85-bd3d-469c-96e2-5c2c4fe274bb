{"ast": null, "code": "import { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { TextJustification, TextNote } from './textnote.js';\nimport { RuntimeError } from './util.js';\nexport class StaveText extends StaveModifier {\n  static get CATEGORY() {\n    return \"StaveText\";\n  }\n  constructor(text, position, options = {}) {\n    var _a, _b, _c;\n    super();\n    this.setText(text);\n    this.setXShift((_a = options.shiftX) !== null && _a !== void 0 ? _a : 0);\n    this.setYShift((_b = options.shiftY) !== null && _b !== void 0 ? _b : 0);\n    this.position = position;\n    this.justification = (_c = options.justification) !== null && _c !== void 0 ? _c : TextNote.Justification.CENTER;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    this.setRendered();\n    let x;\n    let y;\n    switch (this.position) {\n      case StaveModifierPosition.LEFT:\n      case StaveModifierPosition.RIGHT:\n        y = (stave.getYForLine(0) + stave.getBottomLineY()) / 2;\n        if (this.position === StaveModifierPosition.LEFT) {\n          x = stave.getX() - this.width - 24;\n        } else {\n          x = stave.getX() + stave.getWidth() + 24;\n        }\n        break;\n      case StaveModifierPosition.ABOVE:\n      case StaveModifierPosition.BELOW:\n        x = stave.getX();\n        if (this.justification === TextJustification.CENTER) {\n          x += stave.getWidth() / 2 - this.width / 2;\n        } else if (this.justification === TextJustification.RIGHT) {\n          x += stave.getWidth() - this.width;\n        }\n        if (this.position === StaveModifierPosition.ABOVE) {\n          y = stave.getYForTopText(2);\n        } else {\n          y = stave.getYForBottomText(2);\n        }\n        break;\n      default:\n        throw new RuntimeError('InvalidPosition', 'Value Must be in Modifier.Position.');\n    }\n    this.renderText(ctx, x, y + 4);\n  }\n}", "map": {"version": 3, "names": ["StaveModifier", "StaveModifierPosition", "TextJustification", "TextNote", "RuntimeError", "StaveText", "CATEGORY", "constructor", "text", "position", "options", "_a", "_b", "_c", "setText", "setXShift", "shiftX", "setYShift", "shiftY", "justification", "Justification", "CENTER", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "x", "y", "LEFT", "RIGHT", "getYForLine", "getBottomLineY", "getX", "width", "getWidth", "ABOVE", "BELOW", "getYForTopText", "getYForBottomText", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavetext.js"], "sourcesContent": ["import { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { TextJustification, TextNote } from './textnote.js';\nimport { RuntimeError } from './util.js';\nexport class StaveText extends StaveModifier {\n    static get CATEGORY() {\n        return \"StaveText\";\n    }\n    constructor(text, position, options = {}) {\n        var _a, _b, _c;\n        super();\n        this.setText(text);\n        this.setXShift((_a = options.shiftX) !== null && _a !== void 0 ? _a : 0);\n        this.setYShift((_b = options.shiftY) !== null && _b !== void 0 ? _b : 0);\n        this.position = position;\n        this.justification = (_c = options.justification) !== null && _c !== void 0 ? _c : TextNote.Justification.CENTER;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        this.setRendered();\n        let x;\n        let y;\n        switch (this.position) {\n            case StaveModifierPosition.LEFT:\n            case StaveModifierPosition.RIGHT:\n                y = (stave.getYForLine(0) + stave.getBottomLineY()) / 2;\n                if (this.position === StaveModifierPosition.LEFT) {\n                    x = stave.getX() - this.width - 24;\n                }\n                else {\n                    x = stave.getX() + stave.getWidth() + 24;\n                }\n                break;\n            case StaveModifierPosition.ABOVE:\n            case StaveModifierPosition.BELOW:\n                x = stave.getX();\n                if (this.justification === TextJustification.CENTER) {\n                    x += stave.getWidth() / 2 - this.width / 2;\n                }\n                else if (this.justification === TextJustification.RIGHT) {\n                    x += stave.getWidth() - this.width;\n                }\n                if (this.position === StaveModifierPosition.ABOVE) {\n                    y = stave.getYForTopText(2);\n                }\n                else {\n                    y = stave.getYForBottomText(2);\n                }\n                break;\n            default:\n                throw new RuntimeError('InvalidPosition', 'Value Must be in Modifier.Position.');\n        }\n        this.renderText(ctx, x, y + 4);\n    }\n}\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,eAAe;AAC3D,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,SAAS,SAASL,aAAa,CAAC;EACzC,WAAWM,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACAC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACtC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,CAACN,IAAI,CAAC;IAClB,IAAI,CAACO,SAAS,CAAC,CAACJ,EAAE,GAAGD,OAAO,CAACM,MAAM,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACxE,IAAI,CAACM,SAAS,CAAC,CAACL,EAAE,GAAGF,OAAO,CAACQ,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACxE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACU,aAAa,GAAG,CAACN,EAAE,GAAGH,OAAO,CAACS,aAAa,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGV,QAAQ,CAACiB,aAAa,CAACC,MAAM;EACpH;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGF,KAAK,CAACG,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,QAAQ,IAAI,CAACpB,QAAQ;MACjB,KAAKR,qBAAqB,CAAC6B,IAAI;MAC/B,KAAK7B,qBAAqB,CAAC8B,KAAK;QAC5BF,CAAC,GAAG,CAACN,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC,GAAGT,KAAK,CAACU,cAAc,CAAC,CAAC,IAAI,CAAC;QACvD,IAAI,IAAI,CAACxB,QAAQ,KAAKR,qBAAqB,CAAC6B,IAAI,EAAE;UAC9CF,CAAC,GAAGL,KAAK,CAACW,IAAI,CAAC,CAAC,GAAG,IAAI,CAACC,KAAK,GAAG,EAAE;QACtC,CAAC,MACI;UACDP,CAAC,GAAGL,KAAK,CAACW,IAAI,CAAC,CAAC,GAAGX,KAAK,CAACa,QAAQ,CAAC,CAAC,GAAG,EAAE;QAC5C;QACA;MACJ,KAAKnC,qBAAqB,CAACoC,KAAK;MAChC,KAAKpC,qBAAqB,CAACqC,KAAK;QAC5BV,CAAC,GAAGL,KAAK,CAACW,IAAI,CAAC,CAAC;QAChB,IAAI,IAAI,CAACf,aAAa,KAAKjB,iBAAiB,CAACmB,MAAM,EAAE;UACjDO,CAAC,IAAIL,KAAK,CAACa,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACD,KAAK,GAAG,CAAC;QAC9C,CAAC,MACI,IAAI,IAAI,CAAChB,aAAa,KAAKjB,iBAAiB,CAAC6B,KAAK,EAAE;UACrDH,CAAC,IAAIL,KAAK,CAACa,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACD,KAAK;QACtC;QACA,IAAI,IAAI,CAAC1B,QAAQ,KAAKR,qBAAqB,CAACoC,KAAK,EAAE;UAC/CR,CAAC,GAAGN,KAAK,CAACgB,cAAc,CAAC,CAAC,CAAC;QAC/B,CAAC,MACI;UACDV,CAAC,GAAGN,KAAK,CAACiB,iBAAiB,CAAC,CAAC,CAAC;QAClC;QACA;MACJ;QACI,MAAM,IAAIpC,YAAY,CAAC,iBAAiB,EAAE,qCAAqC,CAAC;IACxF;IACA,IAAI,CAACqC,UAAU,CAAChB,GAAG,EAAEG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC;EAClC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}