{"ast": null, "code": "export class BoundingBox {\n  static copy(that) {\n    return new BoundingBox(that.x, that.y, that.w, that.h);\n  }\n  constructor(x, y, w, h) {\n    this.x = x;\n    this.y = y;\n    this.w = w;\n    this.h = h;\n  }\n  getX() {\n    return this.x;\n  }\n  getY() {\n    return this.y;\n  }\n  getW() {\n    return this.w;\n  }\n  getH() {\n    return this.h;\n  }\n  setX(x) {\n    this.x = x;\n    return this;\n  }\n  setY(y) {\n    this.y = y;\n    return this;\n  }\n  setW(w) {\n    this.w = w;\n    return this;\n  }\n  setH(h) {\n    this.h = h;\n    return this;\n  }\n  move(x, y) {\n    this.x += x;\n    this.y += y;\n    return this;\n  }\n  clone() {\n    return BoundingBox.copy(this);\n  }\n  mergeWith(boundingBox) {\n    const that = boundingBox;\n    const newX = this.x < that.x ? this.x : that.x;\n    const newY = this.y < that.y ? this.y : that.y;\n    const newW = Math.max(this.x + this.w, that.x + that.w) - newX;\n    const newH = Math.max(this.y + this.h, that.y + that.h) - newY;\n    this.x = newX;\n    this.y = newY;\n    this.w = newW;\n    this.h = newH;\n    return this;\n  }\n}", "map": {"version": 3, "names": ["BoundingBox", "copy", "that", "x", "y", "w", "h", "constructor", "getX", "getY", "getW", "getH", "setX", "setY", "setW", "setH", "move", "clone", "mergeWith", "boundingBox", "newX", "newY", "newW", "Math", "max", "newH"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/boundingbox.js"], "sourcesContent": ["export class BoundingBox {\n    static copy(that) {\n        return new BoundingBox(that.x, that.y, that.w, that.h);\n    }\n    constructor(x, y, w, h) {\n        this.x = x;\n        this.y = y;\n        this.w = w;\n        this.h = h;\n    }\n    getX() {\n        return this.x;\n    }\n    getY() {\n        return this.y;\n    }\n    getW() {\n        return this.w;\n    }\n    getH() {\n        return this.h;\n    }\n    setX(x) {\n        this.x = x;\n        return this;\n    }\n    setY(y) {\n        this.y = y;\n        return this;\n    }\n    setW(w) {\n        this.w = w;\n        return this;\n    }\n    setH(h) {\n        this.h = h;\n        return this;\n    }\n    move(x, y) {\n        this.x += x;\n        this.y += y;\n        return this;\n    }\n    clone() {\n        return BoundingBox.copy(this);\n    }\n    mergeWith(boundingBox) {\n        const that = boundingBox;\n        const newX = this.x < that.x ? this.x : that.x;\n        const newY = this.y < that.y ? this.y : that.y;\n        const newW = Math.max(this.x + this.w, that.x + that.w) - newX;\n        const newH = Math.max(this.y + this.h, that.y + that.h) - newY;\n        this.x = newX;\n        this.y = newY;\n        this.w = newW;\n        this.h = newH;\n        return this;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,CAAC;EACrB,OAAOC,IAAIA,CAACC,IAAI,EAAE;IACd,OAAO,IAAIF,WAAW,CAACE,IAAI,CAACC,CAAC,EAAED,IAAI,CAACE,CAAC,EAAEF,IAAI,CAACG,CAAC,EAAEH,IAAI,CAACI,CAAC,CAAC;EAC1D;EACAC,WAAWA,CAACJ,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd;EACAE,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,CAAC;EACjB;EACAM,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,CAAC;EACjB;EACAM,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,CAAC;EACjB;EACAM,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACL,CAAC;EACjB;EACAM,IAAIA,CAACT,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACAU,IAAIA,CAACT,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACAU,IAAIA,CAACT,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACAU,IAAIA,CAACT,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACAU,IAAIA,CAACb,CAAC,EAAEC,CAAC,EAAE;IACP,IAAI,CAACD,CAAC,IAAIA,CAAC;IACX,IAAI,CAACC,CAAC,IAAIA,CAAC;IACX,OAAO,IAAI;EACf;EACAa,KAAKA,CAAA,EAAG;IACJ,OAAOjB,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC;EACjC;EACAiB,SAASA,CAACC,WAAW,EAAE;IACnB,MAAMjB,IAAI,GAAGiB,WAAW;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACjB,CAAC,GAAGD,IAAI,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGD,IAAI,CAACC,CAAC;IAC9C,MAAMkB,IAAI,GAAG,IAAI,CAACjB,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGF,IAAI,CAACE,CAAC;IAC9C,MAAMkB,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,CAAC,GAAG,IAAI,CAACE,CAAC,EAAEH,IAAI,CAACC,CAAC,GAAGD,IAAI,CAACG,CAAC,CAAC,GAAGe,IAAI;IAC9D,MAAMK,IAAI,GAAGF,IAAI,CAACC,GAAG,CAAC,IAAI,CAACpB,CAAC,GAAG,IAAI,CAACE,CAAC,EAAEJ,IAAI,CAACE,CAAC,GAAGF,IAAI,CAACI,CAAC,CAAC,GAAGe,IAAI;IAC9D,IAAI,CAAClB,CAAC,GAAGiB,IAAI;IACb,IAAI,CAAChB,CAAC,GAAGiB,IAAI;IACb,IAAI,CAAChB,CAAC,GAAGiB,IAAI;IACb,IAAI,CAAChB,CAAC,GAAGmB,IAAI;IACb,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}