{"ast": null, "code": "import { Element } from './element.js';\nimport { RuntimeError } from './util.js';\nexport class StaveTie extends Element {\n  static get CATEGORY() {\n    return \"StaveTie\";\n  }\n  constructor(notes, text = '') {\n    super();\n    this.setNotes(notes);\n    this.text = text;\n    this.renderOptions = {\n      cp1: 8,\n      cp2: 12,\n      shortTieCutoff: 10,\n      cp1Short: 2,\n      cp2Short: 8,\n      textShiftX: 0,\n      firstXShift: 0,\n      lastXShift: 0,\n      yShift: 7,\n      tieSpacing: 0\n    };\n  }\n  getDirection() {\n    if (this.direction !== undefined && this.direction !== null) {\n      return this.direction;\n    } else if (this.notes.lastNote) {\n      return this.notes.lastNote.getStemDirection();\n    } else if (this.notes.firstNote) {\n      return this.notes.firstNote.getStemDirection();\n    } else {\n      return 0;\n    }\n  }\n  setDirection(direction) {\n    this.direction = direction;\n    return this;\n  }\n  setNotes(notes) {\n    if (!notes.firstNote && !notes.lastNote) {\n      throw new RuntimeError('BadArguments', '<PERSON><PERSON> needs to have either firstNote or lastNote set.');\n    }\n    if (!notes.firstIndexes) {\n      notes.firstIndexes = [0];\n    }\n    if (!notes.lastIndexes) {\n      notes.lastIndexes = [0];\n    }\n    if (notes.firstIndexes.length !== notes.lastIndexes.length) {\n      throw new RuntimeError('BadArguments', 'Tied notes must have same number of indexes.');\n    }\n    this.notes = notes;\n    return this;\n  }\n  isPartial() {\n    return !this.notes.firstNote || !this.notes.lastNote;\n  }\n  renderTie(params) {\n    if (params.firstYs.length === 0 || params.lastYs.length === 0) {\n      throw new RuntimeError('BadArguments', 'No Y-values to render');\n    }\n    const ctx = this.checkContext();\n    let cp1 = this.renderOptions.cp1;\n    let cp2 = this.renderOptions.cp2;\n    if (Math.abs(params.lastX - params.firstX) < this.renderOptions.shortTieCutoff) {\n      cp1 = this.renderOptions.cp1Short;\n      cp2 = this.renderOptions.cp2Short;\n    }\n    const firstXShift = this.renderOptions.firstXShift;\n    const lastXShift = this.renderOptions.lastXShift;\n    const yShift = this.renderOptions.yShift * params.direction;\n    const firstIndexes = this.notes.firstIndexes;\n    const lastIndexes = this.notes.lastIndexes;\n    ctx.openGroup('stavetie', this.getAttribute('id'));\n    for (let i = 0; i < firstIndexes.length; ++i) {\n      const cpX = (params.lastX + lastXShift + (params.firstX + firstXShift)) / 2;\n      const firstY = params.firstYs[firstIndexes[i]] + yShift;\n      const lastY = params.lastYs[lastIndexes[i]] + yShift;\n      if (isNaN(firstY) || isNaN(lastY)) {\n        throw new RuntimeError('BadArguments', 'Bad indexes for tie rendering.');\n      }\n      const topControlPointY = (firstY + lastY) / 2 + cp1 * params.direction;\n      const bottomControlPointY = (firstY + lastY) / 2 + cp2 * params.direction;\n      ctx.beginPath();\n      ctx.moveTo(params.firstX + firstXShift, firstY);\n      ctx.quadraticCurveTo(cpX, topControlPointY, params.lastX + lastXShift, lastY);\n      ctx.quadraticCurveTo(cpX, bottomControlPointY, params.firstX + firstXShift, firstY);\n      ctx.closePath();\n      ctx.fill();\n    }\n    ctx.closeGroup();\n  }\n  renderTieText(firstX, lastX) {\n    var _a, _b, _c;\n    const ctx = this.checkContext();\n    let centerX = (firstX + lastX) / 2;\n    centerX -= ctx.measureText(this.text).width / 2;\n    const stave = (_b = (_a = this.notes.firstNote) === null || _a === void 0 ? void 0 : _a.checkStave()) !== null && _b !== void 0 ? _b : (_c = this.notes.lastNote) === null || _c === void 0 ? void 0 : _c.checkStave();\n    if (stave) {\n      ctx.setFont(this.fontInfo);\n      ctx.fillText(this.text, centerX + this.renderOptions.textShiftX, stave.getYForTopText() - 1);\n    }\n  }\n  getNotes() {\n    return this.notes;\n  }\n  getFirstX() {\n    if (this.notes.firstNote) {\n      return this.notes.firstNote.getTieRightX() + this.renderOptions.tieSpacing;\n    } else if (this.notes.lastNote) {\n      return this.notes.lastNote.checkStave().getTieStartX();\n    } else {\n      return 0;\n    }\n  }\n  getLastX() {\n    if (this.notes.lastNote) {\n      return this.notes.lastNote.getTieLeftX() + this.renderOptions.tieSpacing;\n    } else if (this.notes.firstNote) {\n      return this.notes.firstNote.checkStave().getTieEndX();\n    } else {\n      return 0;\n    }\n  }\n  getFirstYs() {\n    if (this.notes.firstNote) {\n      return this.notes.firstNote.getYs();\n    } else if (this.notes.lastNote) {\n      return this.notes.lastNote.getYs();\n    } else {\n      return [0];\n    }\n  }\n  getLastYs() {\n    if (this.notes.lastNote) {\n      return this.notes.lastNote.getYs();\n    } else if (this.notes.firstNote) {\n      return this.notes.firstNote.getYs();\n    } else {\n      return [0];\n    }\n  }\n  synchronizeIndexes() {\n    if (this.notes.firstNote && this.notes.lastNote) {\n      return;\n    } else if (!this.notes.firstNote && !this.notes.lastNote) {\n      return;\n    } else if (this.notes.firstNote) {\n      this.notes.lastIndexes = this.notes.firstIndexes;\n    } else {\n      this.notes.firstIndexes = this.notes.lastIndexes;\n    }\n  }\n  draw() {\n    this.checkContext();\n    this.setRendered();\n    this.synchronizeIndexes();\n    const firstX = this.getFirstX();\n    const lastX = this.getLastX();\n    this.renderTie({\n      firstX,\n      lastX,\n      firstYs: this.getFirstYs(),\n      lastYs: this.getLastYs(),\n      direction: this.getDirection()\n    });\n    this.renderTieText(firstX, lastX);\n    return true;\n  }\n}", "map": {"version": 3, "names": ["Element", "RuntimeError", "StaveTie", "CATEGORY", "constructor", "notes", "text", "setNotes", "renderOptions", "cp1", "cp2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cp1Short", "cp2Short", "textShiftX", "firstXShift", "lastXShift", "yShift", "tieSpacing", "getDirection", "direction", "undefined", "lastNote", "getStemDirection", "firstNote", "setDirection", "firstIndexes", "lastIndexes", "length", "isPartial", "<PERSON><PERSON>ie", "params", "firstYs", "lastYs", "ctx", "checkContext", "Math", "abs", "lastX", "firstX", "openGroup", "getAttribute", "i", "cpX", "firstY", "lastY", "isNaN", "topControlPointY", "bottomControlPointY", "beginPath", "moveTo", "quadraticCurveTo", "closePath", "fill", "closeGroup", "renderTieText", "_a", "_b", "_c", "centerX", "measureText", "width", "stave", "checkStave", "setFont", "fontInfo", "fillText", "getYForTopText", "getNotes", "getFirstX", "getTieRightX", "getTieStartX", "getLastX", "getTieLeftX", "getTieEndX", "getFirstYs", "getYs", "getLastYs", "synchronizeIndexes", "draw", "setRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavetie.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { RuntimeError } from './util.js';\nexport class StaveTie extends Element {\n    static get CATEGORY() {\n        return \"StaveTie\";\n    }\n    constructor(notes, text = '') {\n        super();\n        this.setNotes(notes);\n        this.text = text;\n        this.renderOptions = {\n            cp1: 8,\n            cp2: 12,\n            shortTieCutoff: 10,\n            cp1Short: 2,\n            cp2Short: 8,\n            textShiftX: 0,\n            firstXShift: 0,\n            lastXShift: 0,\n            yShift: 7,\n            tieSpacing: 0,\n        };\n    }\n    getDirection() {\n        if (this.direction !== undefined && this.direction !== null) {\n            return this.direction;\n        }\n        else if (this.notes.lastNote) {\n            return this.notes.lastNote.getStemDirection();\n        }\n        else if (this.notes.firstNote) {\n            return this.notes.firstNote.getStemDirection();\n        }\n        else {\n            return 0;\n        }\n    }\n    setDirection(direction) {\n        this.direction = direction;\n        return this;\n    }\n    setNotes(notes) {\n        if (!notes.firstNote && !notes.lastNote) {\n            throw new RuntimeError('BadArguments', '<PERSON>ie needs to have either firstNote or lastNote set.');\n        }\n        if (!notes.firstIndexes) {\n            notes.firstIndexes = [0];\n        }\n        if (!notes.lastIndexes) {\n            notes.lastIndexes = [0];\n        }\n        if (notes.firstIndexes.length !== notes.lastIndexes.length) {\n            throw new RuntimeError('BadArguments', 'Tied notes must have same number of indexes.');\n        }\n        this.notes = notes;\n        return this;\n    }\n    isPartial() {\n        return !this.notes.firstNote || !this.notes.lastNote;\n    }\n    renderTie(params) {\n        if (params.firstYs.length === 0 || params.lastYs.length === 0) {\n            throw new RuntimeError('BadArguments', 'No Y-values to render');\n        }\n        const ctx = this.checkContext();\n        let cp1 = this.renderOptions.cp1;\n        let cp2 = this.renderOptions.cp2;\n        if (Math.abs(params.lastX - params.firstX) < this.renderOptions.shortTieCutoff) {\n            cp1 = this.renderOptions.cp1Short;\n            cp2 = this.renderOptions.cp2Short;\n        }\n        const firstXShift = this.renderOptions.firstXShift;\n        const lastXShift = this.renderOptions.lastXShift;\n        const yShift = this.renderOptions.yShift * params.direction;\n        const firstIndexes = this.notes.firstIndexes;\n        const lastIndexes = this.notes.lastIndexes;\n        ctx.openGroup('stavetie', this.getAttribute('id'));\n        for (let i = 0; i < firstIndexes.length; ++i) {\n            const cpX = (params.lastX + lastXShift + (params.firstX + firstXShift)) / 2;\n            const firstY = params.firstYs[firstIndexes[i]] + yShift;\n            const lastY = params.lastYs[lastIndexes[i]] + yShift;\n            if (isNaN(firstY) || isNaN(lastY)) {\n                throw new RuntimeError('BadArguments', 'Bad indexes for tie rendering.');\n            }\n            const topControlPointY = (firstY + lastY) / 2 + cp1 * params.direction;\n            const bottomControlPointY = (firstY + lastY) / 2 + cp2 * params.direction;\n            ctx.beginPath();\n            ctx.moveTo(params.firstX + firstXShift, firstY);\n            ctx.quadraticCurveTo(cpX, topControlPointY, params.lastX + lastXShift, lastY);\n            ctx.quadraticCurveTo(cpX, bottomControlPointY, params.firstX + firstXShift, firstY);\n            ctx.closePath();\n            ctx.fill();\n        }\n        ctx.closeGroup();\n    }\n    renderTieText(firstX, lastX) {\n        var _a, _b, _c;\n        const ctx = this.checkContext();\n        let centerX = (firstX + lastX) / 2;\n        centerX -= ctx.measureText(this.text).width / 2;\n        const stave = (_b = (_a = this.notes.firstNote) === null || _a === void 0 ? void 0 : _a.checkStave()) !== null && _b !== void 0 ? _b : (_c = this.notes.lastNote) === null || _c === void 0 ? void 0 : _c.checkStave();\n        if (stave) {\n            ctx.setFont(this.fontInfo);\n            ctx.fillText(this.text, centerX + this.renderOptions.textShiftX, stave.getYForTopText() - 1);\n        }\n    }\n    getNotes() {\n        return this.notes;\n    }\n    getFirstX() {\n        if (this.notes.firstNote) {\n            return this.notes.firstNote.getTieRightX() + this.renderOptions.tieSpacing;\n        }\n        else if (this.notes.lastNote) {\n            return this.notes.lastNote.checkStave().getTieStartX();\n        }\n        else {\n            return 0;\n        }\n    }\n    getLastX() {\n        if (this.notes.lastNote) {\n            return this.notes.lastNote.getTieLeftX() + this.renderOptions.tieSpacing;\n        }\n        else if (this.notes.firstNote) {\n            return this.notes.firstNote.checkStave().getTieEndX();\n        }\n        else {\n            return 0;\n        }\n    }\n    getFirstYs() {\n        if (this.notes.firstNote) {\n            return this.notes.firstNote.getYs();\n        }\n        else if (this.notes.lastNote) {\n            return this.notes.lastNote.getYs();\n        }\n        else {\n            return [0];\n        }\n    }\n    getLastYs() {\n        if (this.notes.lastNote) {\n            return this.notes.lastNote.getYs();\n        }\n        else if (this.notes.firstNote) {\n            return this.notes.firstNote.getYs();\n        }\n        else {\n            return [0];\n        }\n    }\n    synchronizeIndexes() {\n        if (this.notes.firstNote && this.notes.lastNote) {\n            return;\n        }\n        else if (!this.notes.firstNote && !this.notes.lastNote) {\n            return;\n        }\n        else if (this.notes.firstNote) {\n            this.notes.lastIndexes = this.notes.firstIndexes;\n        }\n        else {\n            this.notes.firstIndexes = this.notes.lastIndexes;\n        }\n    }\n    draw() {\n        this.checkContext();\n        this.setRendered();\n        this.synchronizeIndexes();\n        const firstX = this.getFirstX();\n        const lastX = this.getLastX();\n        this.renderTie({\n            firstX,\n            lastX,\n            firstYs: this.getFirstYs(),\n            lastYs: this.getLastYs(),\n            direction: this.getDirection(),\n        });\n        this.renderTieText(firstX, lastX);\n        return true;\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,QAAQ,SAASF,OAAO,CAAC;EAClC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACAC,WAAWA,CAACC,KAAK,EAAEC,IAAI,GAAG,EAAE,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,QAAQ,CAACF,KAAK,CAAC;IACpB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,aAAa,GAAG;MACjBC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,EAAE;MACPC,cAAc,EAAE,EAAE;MAClBC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;IAChB,CAAC;EACL;EACAC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,IAAI,IAAI,CAACD,SAAS,KAAK,IAAI,EAAE;MACzD,OAAO,IAAI,CAACA,SAAS;IACzB,CAAC,MACI,IAAI,IAAI,CAACf,KAAK,CAACiB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,CAACC,gBAAgB,CAAC,CAAC;IACjD,CAAC,MACI,IAAI,IAAI,CAAClB,KAAK,CAACmB,SAAS,EAAE;MAC3B,OAAO,IAAI,CAACnB,KAAK,CAACmB,SAAS,CAACD,gBAAgB,CAAC,CAAC;IAClD,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACAE,YAAYA,CAACL,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,OAAO,IAAI;EACf;EACAb,QAAQA,CAACF,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,CAACmB,SAAS,IAAI,CAACnB,KAAK,CAACiB,QAAQ,EAAE;MACrC,MAAM,IAAIrB,YAAY,CAAC,cAAc,EAAE,qDAAqD,CAAC;IACjG;IACA,IAAI,CAACI,KAAK,CAACqB,YAAY,EAAE;MACrBrB,KAAK,CAACqB,YAAY,GAAG,CAAC,CAAC,CAAC;IAC5B;IACA,IAAI,CAACrB,KAAK,CAACsB,WAAW,EAAE;MACpBtB,KAAK,CAACsB,WAAW,GAAG,CAAC,CAAC,CAAC;IAC3B;IACA,IAAItB,KAAK,CAACqB,YAAY,CAACE,MAAM,KAAKvB,KAAK,CAACsB,WAAW,CAACC,MAAM,EAAE;MACxD,MAAM,IAAI3B,YAAY,CAAC,cAAc,EAAE,8CAA8C,CAAC;IAC1F;IACA,IAAI,CAACI,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAwB,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACxB,KAAK,CAACmB,SAAS,IAAI,CAAC,IAAI,CAACnB,KAAK,CAACiB,QAAQ;EACxD;EACAQ,SAASA,CAACC,MAAM,EAAE;IACd,IAAIA,MAAM,CAACC,OAAO,CAACJ,MAAM,KAAK,CAAC,IAAIG,MAAM,CAACE,MAAM,CAACL,MAAM,KAAK,CAAC,EAAE;MAC3D,MAAM,IAAI3B,YAAY,CAAC,cAAc,EAAE,uBAAuB,CAAC;IACnE;IACA,MAAMiC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI1B,GAAG,GAAG,IAAI,CAACD,aAAa,CAACC,GAAG;IAChC,IAAIC,GAAG,GAAG,IAAI,CAACF,aAAa,CAACE,GAAG;IAChC,IAAI0B,IAAI,CAACC,GAAG,CAACN,MAAM,CAACO,KAAK,GAAGP,MAAM,CAACQ,MAAM,CAAC,GAAG,IAAI,CAAC/B,aAAa,CAACG,cAAc,EAAE;MAC5EF,GAAG,GAAG,IAAI,CAACD,aAAa,CAACI,QAAQ;MACjCF,GAAG,GAAG,IAAI,CAACF,aAAa,CAACK,QAAQ;IACrC;IACA,MAAME,WAAW,GAAG,IAAI,CAACP,aAAa,CAACO,WAAW;IAClD,MAAMC,UAAU,GAAG,IAAI,CAACR,aAAa,CAACQ,UAAU;IAChD,MAAMC,MAAM,GAAG,IAAI,CAACT,aAAa,CAACS,MAAM,GAAGc,MAAM,CAACX,SAAS;IAC3D,MAAMM,YAAY,GAAG,IAAI,CAACrB,KAAK,CAACqB,YAAY;IAC5C,MAAMC,WAAW,GAAG,IAAI,CAACtB,KAAK,CAACsB,WAAW;IAC1CO,GAAG,CAACM,SAAS,CAAC,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,YAAY,CAACE,MAAM,EAAE,EAAEc,CAAC,EAAE;MAC1C,MAAMC,GAAG,GAAG,CAACZ,MAAM,CAACO,KAAK,GAAGtB,UAAU,IAAIe,MAAM,CAACQ,MAAM,GAAGxB,WAAW,CAAC,IAAI,CAAC;MAC3E,MAAM6B,MAAM,GAAGb,MAAM,CAACC,OAAO,CAACN,YAAY,CAACgB,CAAC,CAAC,CAAC,GAAGzB,MAAM;MACvD,MAAM4B,KAAK,GAAGd,MAAM,CAACE,MAAM,CAACN,WAAW,CAACe,CAAC,CAAC,CAAC,GAAGzB,MAAM;MACpD,IAAI6B,KAAK,CAACF,MAAM,CAAC,IAAIE,KAAK,CAACD,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAI5C,YAAY,CAAC,cAAc,EAAE,gCAAgC,CAAC;MAC5E;MACA,MAAM8C,gBAAgB,GAAG,CAACH,MAAM,GAAGC,KAAK,IAAI,CAAC,GAAGpC,GAAG,GAAGsB,MAAM,CAACX,SAAS;MACtE,MAAM4B,mBAAmB,GAAG,CAACJ,MAAM,GAAGC,KAAK,IAAI,CAAC,GAAGnC,GAAG,GAAGqB,MAAM,CAACX,SAAS;MACzEc,GAAG,CAACe,SAAS,CAAC,CAAC;MACff,GAAG,CAACgB,MAAM,CAACnB,MAAM,CAACQ,MAAM,GAAGxB,WAAW,EAAE6B,MAAM,CAAC;MAC/CV,GAAG,CAACiB,gBAAgB,CAACR,GAAG,EAAEI,gBAAgB,EAAEhB,MAAM,CAACO,KAAK,GAAGtB,UAAU,EAAE6B,KAAK,CAAC;MAC7EX,GAAG,CAACiB,gBAAgB,CAACR,GAAG,EAAEK,mBAAmB,EAAEjB,MAAM,CAACQ,MAAM,GAAGxB,WAAW,EAAE6B,MAAM,CAAC;MACnFV,GAAG,CAACkB,SAAS,CAAC,CAAC;MACflB,GAAG,CAACmB,IAAI,CAAC,CAAC;IACd;IACAnB,GAAG,CAACoB,UAAU,CAAC,CAAC;EACpB;EACAC,aAAaA,CAAChB,MAAM,EAAED,KAAK,EAAE;IACzB,IAAIkB,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,MAAMxB,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAIwB,OAAO,GAAG,CAACpB,MAAM,GAAGD,KAAK,IAAI,CAAC;IAClCqB,OAAO,IAAIzB,GAAG,CAAC0B,WAAW,CAAC,IAAI,CAACtD,IAAI,CAAC,CAACuD,KAAK,GAAG,CAAC;IAC/C,MAAMC,KAAK,GAAG,CAACL,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACnD,KAAK,CAACmB,SAAS,MAAM,IAAI,IAAIgC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,UAAU,CAAC,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAACC,EAAE,GAAG,IAAI,CAACrD,KAAK,CAACiB,QAAQ,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,UAAU,CAAC,CAAC;IACtN,IAAID,KAAK,EAAE;MACP5B,GAAG,CAAC8B,OAAO,CAAC,IAAI,CAACC,QAAQ,CAAC;MAC1B/B,GAAG,CAACgC,QAAQ,CAAC,IAAI,CAAC5D,IAAI,EAAEqD,OAAO,GAAG,IAAI,CAACnD,aAAa,CAACM,UAAU,EAAEgD,KAAK,CAACK,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;IAChG;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/D,KAAK;EACrB;EACAgE,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChE,KAAK,CAACmB,SAAS,EAAE;MACtB,OAAO,IAAI,CAACnB,KAAK,CAACmB,SAAS,CAAC8C,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC9D,aAAa,CAACU,UAAU;IAC9E,CAAC,MACI,IAAI,IAAI,CAACb,KAAK,CAACiB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,CAACyC,UAAU,CAAC,CAAC,CAACQ,YAAY,CAAC,CAAC;IAC1D,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACnE,KAAK,CAACiB,QAAQ,EAAE;MACrB,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,CAACmD,WAAW,CAAC,CAAC,GAAG,IAAI,CAACjE,aAAa,CAACU,UAAU;IAC5E,CAAC,MACI,IAAI,IAAI,CAACb,KAAK,CAACmB,SAAS,EAAE;MAC3B,OAAO,IAAI,CAACnB,KAAK,CAACmB,SAAS,CAACuC,UAAU,CAAC,CAAC,CAACW,UAAU,CAAC,CAAC;IACzD,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACtE,KAAK,CAACmB,SAAS,EAAE;MACtB,OAAO,IAAI,CAACnB,KAAK,CAACmB,SAAS,CAACoD,KAAK,CAAC,CAAC;IACvC,CAAC,MACI,IAAI,IAAI,CAACvE,KAAK,CAACiB,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,CAACsD,KAAK,CAAC,CAAC;IACtC,CAAC,MACI;MACD,OAAO,CAAC,CAAC,CAAC;IACd;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACxE,KAAK,CAACiB,QAAQ,EAAE;MACrB,OAAO,IAAI,CAACjB,KAAK,CAACiB,QAAQ,CAACsD,KAAK,CAAC,CAAC;IACtC,CAAC,MACI,IAAI,IAAI,CAACvE,KAAK,CAACmB,SAAS,EAAE;MAC3B,OAAO,IAAI,CAACnB,KAAK,CAACmB,SAAS,CAACoD,KAAK,CAAC,CAAC;IACvC,CAAC,MACI;MACD,OAAO,CAAC,CAAC,CAAC;IACd;EACJ;EACAE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACzE,KAAK,CAACmB,SAAS,IAAI,IAAI,CAACnB,KAAK,CAACiB,QAAQ,EAAE;MAC7C;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACjB,KAAK,CAACmB,SAAS,IAAI,CAAC,IAAI,CAACnB,KAAK,CAACiB,QAAQ,EAAE;MACpD;IACJ,CAAC,MACI,IAAI,IAAI,CAACjB,KAAK,CAACmB,SAAS,EAAE;MAC3B,IAAI,CAACnB,KAAK,CAACsB,WAAW,GAAG,IAAI,CAACtB,KAAK,CAACqB,YAAY;IACpD,CAAC,MACI;MACD,IAAI,CAACrB,KAAK,CAACqB,YAAY,GAAG,IAAI,CAACrB,KAAK,CAACsB,WAAW;IACpD;EACJ;EACAoD,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5C,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC6C,WAAW,CAAC,CAAC;IAClB,IAAI,CAACF,kBAAkB,CAAC,CAAC;IACzB,MAAMvC,MAAM,GAAG,IAAI,CAAC8B,SAAS,CAAC,CAAC;IAC/B,MAAM/B,KAAK,GAAG,IAAI,CAACkC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAC1C,SAAS,CAAC;MACXS,MAAM;MACND,KAAK;MACLN,OAAO,EAAE,IAAI,CAAC2C,UAAU,CAAC,CAAC;MAC1B1C,MAAM,EAAE,IAAI,CAAC4C,SAAS,CAAC,CAAC;MACxBzD,SAAS,EAAE,IAAI,CAACD,YAAY,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAACoC,aAAa,CAAChB,MAAM,EAAED,KAAK,CAAC;IACjC,OAAO,IAAI;EACf;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}