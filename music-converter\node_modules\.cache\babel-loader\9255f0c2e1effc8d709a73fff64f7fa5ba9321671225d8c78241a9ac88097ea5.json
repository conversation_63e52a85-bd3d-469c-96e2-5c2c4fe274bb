{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VexTabBlock({\n  source\n}) {\n  _s();\n  useEffect(() => {\n    console.log('VexTabBlock source: ', source);\n    if (window.vextab && window.vextab.Div) {\n      document.querySelectorAll('vextab-auto').forEach(div => {\n        div.textContent = '';\n        div.textContent = source;\n        new window.vextab.Div(div);\n      });\n    }\n  }, [source]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"vextab-auto\",\n    children: source\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 10\n  }, this);\n}\n_s(VexTabBlock, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = VexTabBlock;\nfunction Converter() {\n  _s2();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'rgba(68, 25, 240, 0.08)',\n      display: 'grid',\n      gridTemplateRows: '1fr auto auto'\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'white'\n    },\n    sideButton: {\n      backgroundColor: 'white',\n      width: '4rem',\n      height: '4rem',\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: '#5c14ba',\n      border: '2px solid #9a62e3',\n      borderRadius: '1rem',\n      cursor: 'pointer',\n      margin: 'auto'\n    },\n    sideButtonActive: {\n      backgroundColor: '#9a62e3',\n      color: 'white',\n      border: '2px solid #7f3bd9',\n      boxShadow: '0 4px 8px rgba(127, 59, 217, 0.5)'\n    }\n  };\n\n  // VEXTAB SETUP\n  const defaultSetup = 'tabstave notation=true tablature=false';\n  const defaultNotes = ['G/4'];\n  const [key, setKey] = useState('G');\n  const [time, setTime] = useState('4/4');\n  const [notes, setNotes] = useState(defaultNotes);\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${time}\nnotes ${notes.join(' ')}`);\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  const ACCIDENTALS = ['♯', '♮', '♭'];\n  const VEXTAB_ACCIDENTALS = {\n    '♯': '#',\n    '♮': 'n',\n    '♭': 'b',\n    '': ''\n  };\n\n  // CURRENT NOTE\n  const [curOctave, setCurOctave] = useState(4);\n  const [curStep, setCurStep] = useState('A');\n  const [curAccident, setCurAccident] = useState('♮');\n  const [curNote, setCurNote] = useState();\n\n  // REFS FOR VEXTAB EDITORS\n  const inputEditorRef = useRef(null);\n  const outputEditorRef = useRef(null);\n  const [inputEditorInstance, setInputEditorInstance] = useState(null);\n  const [outputEditorInstance, setOutputEditorInstance] = useState(null);\n\n  // FUNCTIONS TO INTERACT WITH VEXTAB EDITORS\n  const getEditorText = editorInstance => {\n    if (editorInstance && editorInstance.getCode) {\n      return editorInstance.getCode();\n    }\n    return '';\n  };\n  const setEditorText = (editorInstance, text) => {\n    if (editorInstance && editorInstance.setCode) {\n      editorInstance.setCode(text);\n    }\n  };\n  const processInputToOutput = () => {\n    const inputText = getEditorText(inputEditorInstance);\n    console.log('Input text:', inputText);\n\n    // Here you'll add your conversion algorithm later\n    // For now, let's just copy the input to output\n    const processedText = inputText; // Replace with your algorithm\n\n    setEditorText(outputEditorInstance, processedText);\n  };\n\n  // FUNCTION TO UPDATE INPUT EDITOR FROM GRAPHICAL CONTROLS\n  const updateInputFromGraphics = () => {\n    const newNote = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\n    const newVexTab = `${defaultSetup} key=${key} time=${time}\\nnotes ${newNote}`;\n    setEditorText(inputEditorInstance, newVexTab);\n  };\n\n  // INITIALIZE VEXTAB EDITORS\n  useEffect(() => {\n    const initializeEditors = () => {\n      if (window.VexTab && window.VexTab.Div) {\n        // Initialize input editor\n        if (inputEditorRef.current && !inputEditorInstance) {\n          const inputDiv = new window.VexTab.Div(inputEditorRef.current);\n          setInputEditorInstance(inputDiv);\n\n          // Set up event listener for input changes\n          inputEditorRef.current.addEventListener('input', () => {\n            // Trigger processing when input changes\n            setTimeout(processInputToOutput, 100);\n          });\n        }\n\n        // Initialize output editor\n        if (outputEditorRef.current && !outputEditorInstance) {\n          const outputDiv = new window.VexTab.Div(outputEditorRef.current);\n          setOutputEditorInstance(outputDiv);\n        }\n      }\n    };\n\n    // Wait for VexTab to load\n    const timer = setTimeout(initializeEditors, 500);\n    return () => clearTimeout(timer);\n  }, [inputEditorInstance, outputEditorInstance]);\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyItems: 'center',\n      paddingTop: '4rem',\n      paddingBottom: '4rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vexflow/releases/vexflow-min.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/vextab-core.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: '2px solid #9a62e3',\n          borderRadius: '2rem',\n          background: 'rgba(120, 25, 240, 0.06)',\n          display: 'flex',\n          flexDirection: 'column',\n          margin: '2rem 4rem',\n          padding: '2rem',\n          gap: '2rem',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '6rem 1fr 6rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignContent: 'center',\n                gap: '0.5rem',\n                padding: '1rem 0rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(curOctave + 1),\n                children: \"\\u2191\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                children: curOctave\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: styles.sideButton,\n                onClick: () => setCurOctave(curOctave - 1),\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateRows: 'auto 1fr'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'grid',\n                  gridTemplateColumns: 'repeat(7, 1fr)',\n                  padding: '1rem'\n                },\n                children: NOTES.map(note => /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    ...styles.sideButton,\n                    ...(curStep === note ? styles.sideButtonActive : {})\n                  },\n                  onClick: () => setCurStep(note),\n                  children: note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  border: '2px solid red',\n                  padding: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(VexTabBlock, {\n                  source: rawVex\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignContent: 'center',\n                gap: '0.5rem',\n                padding: '1rem 0rem'\n              },\n              children: ACCIDENTALS.map(acc => /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  ...styles.sideButton,\n                  ...(curAccident === acc ? styles.sideButtonActive : {})\n                },\n                onClick: curAccident === acc ? () => setCurAccident('') : () => setCurAccident(acc),\n                children: acc\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Add or remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Input Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: inputEditorRef,\n            className: \"vextab-auto\",\n            editor: \"true\",\n            style: {\n              minHeight: '200px',\n              border: '1px solid #ccc',\n              margin: '1rem'\n            },\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem',\n              margin: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: updateInputFromGraphics,\n              style: {\n                ...styles.sideButton,\n                width: 'auto',\n                padding: '0.5rem 1rem'\n              },\n              children: \"Update from Graphics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: processInputToOutput,\n              style: {\n                ...styles.sideButton,\n                width: 'auto',\n                padding: '0.5rem 1rem'\n              },\n              children: \"Process to Output\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: '2px solid #9a62e3',\n          borderRadius: '2rem',\n          background: 'rgba(120, 25, 240, 0.06)',\n          display: 'flex',\n          flexDirection: 'column',\n          margin: '2rem 4rem',\n          padding: '2rem',\n          gap: '2rem',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"VexTab Output Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: outputEditorRef,\n            className: \"vextab-auto\",\n            editor: \"true\",\n            style: {\n              minHeight: '200px',\n              border: '1px solid #ccc',\n              margin: '1rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n}\n_s2(Converter, \"+pxYLw9KvolSPXdnidM8YPB0yFI=\");\n_c2 = Converter;\nexport default Converter;\nvar _c, _c2;\n$RefreshReg$(_c, \"VexTabBlock\");\n$RefreshReg$(_c2, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "VexTabBlock", "source", "_s", "console", "log", "window", "vextab", "Div", "document", "querySelectorAll", "for<PERSON>ach", "div", "textContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Converter", "_s2", "styles", "graphicDisplay", "width", "Math", "min", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "sideButton", "backgroundColor", "height", "fontSize", "fontWeight", "color", "cursor", "margin", "sideButtonActive", "boxShadow", "defaultSetup", "defaultNotes", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "setRawVex", "join", "NOTES", "ACCIDENTALS", "VEXTAB_ACCIDENTALS", "curOctave", "setCurOctave", "curStep", "setCurStep", "curAccident", "setCurAccident", "cur<PERSON><PERSON>", "setCurNote", "inputEditorRef", "outputEditorRef", "inputEditorInstance", "setInputEditorInstance", "outputEditorInstance", "setOutputEditorInstance", "getEditorText", "editorInstance", "getCode", "setEditorText", "text", "setCode", "processInputToOutput", "inputText", "processedText", "updateInputFromGraphics", "newNote", "newVexTab", "initializeEditors", "VexTab", "current", "inputDiv", "addEventListener", "setTimeout", "outputDiv", "timer", "clearTimeout", "style", "flexDirection", "alignItems", "justifyItems", "paddingTop", "paddingBottom", "src", "padding", "gap", "gridTemplateColumns", "justifyContent", "align<PERSON><PERSON><PERSON>", "onClick", "map", "note", "acc", "ref", "editor", "minHeight", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["\r\nimport React, { useEffect, useState, useRef } from 'react';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nfunction VexTabBlock({ source }) {\r\n  useEffect(() => {\r\n    console.log('VexTabBlock source: ', source);\r\n    if (window.vextab && window.vextab.Div) {\r\n      document.querySelectorAll('vextab-auto').forEach(div => {\r\n        div.textContent = '';\r\n        div.textContent = source;\r\n        new window.vextab.Div(div);\r\n      });\r\n    }\r\n  }, [source]);\r\n\r\n  return <div className=\"vextab-auto\">{source}</div>;\r\n}\r\n\r\nfunction Converter() {\r\n\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'rgba(68, 25, 240, 0.08)',\r\n      display: 'grid',\r\n      gridTemplateRows: '1fr auto auto',\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'white',\r\n    },\r\n    sideButton: {\r\n      backgroundColor: 'white',\r\n      width: '4rem',\r\n      height: '4rem',\r\n      fontSize: '1.2rem',\r\n      fontWeight: 'bold',\r\n      color: '#5c14ba',\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '1rem',\r\n      cursor: 'pointer',\r\n      margin: 'auto',\r\n    },\r\n    sideButtonActive: {\r\n      backgroundColor: '#9a62e3',\r\n      color: 'white',\r\n      border: '2px solid #7f3bd9',\r\n      boxShadow: '0 4px 8px rgba(127, 59, 217, 0.5)',\r\n    },\r\n  }\r\n\r\n  // VEXTAB SETUP\r\n  const defaultSetup = 'tabstave notation=true tablature=false';\r\n  const defaultNotes = ['G/4'];\r\n  const [key, setKey] = useState('G');\r\n  const [time, setTime] = useState('4/4');\r\n  const [notes, setNotes] = useState(defaultNotes);\r\n  const [rawVex, setRawVex] = useState(\r\n`${defaultSetup} key=${key} time=${time}\r\nnotes ${notes.join(' ')}`\r\n  );\r\n\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n  const ACCIDENTALS = ['♯', '♮', '♭'];\r\n  const VEXTAB_ACCIDENTALS = { '♯': '#', '♮': 'n', '♭': 'b', '': '' };\r\n\r\n  // CURRENT NOTE\r\n  const [curOctave, setCurOctave] = useState(4);\r\n  const [curStep, setCurStep] = useState('A');\r\n  const [curAccident, setCurAccident] = useState('♮');\r\n  const [curNote, setCurNote] = useState();\r\n\r\n\r\n  \r\n  // REFS FOR VEXTAB EDITORS\r\n  const inputEditorRef = useRef(null);\r\n  const outputEditorRef = useRef(null);\r\n  const [inputEditorInstance, setInputEditorInstance] = useState(null);\r\n  const [outputEditorInstance, setOutputEditorInstance] = useState(null);\r\n\r\n  // FUNCTIONS TO INTERACT WITH VEXTAB EDITORS\r\n  const getEditorText = (editorInstance) => {\r\n    if (editorInstance && editorInstance.getCode) {\r\n      return editorInstance.getCode();\r\n    }\r\n    return '';\r\n  };\r\n\r\n  const setEditorText = (editorInstance, text) => {\r\n    if (editorInstance && editorInstance.setCode) {\r\n      editorInstance.setCode(text);\r\n    }\r\n  };\r\n\r\n  const processInputToOutput = () => {\r\n    const inputText = getEditorText(inputEditorInstance);\r\n    console.log('Input text:', inputText);\r\n\r\n    // Here you'll add your conversion algorithm later\r\n    // For now, let's just copy the input to output\r\n    const processedText = inputText; // Replace with your algorithm\r\n\r\n    setEditorText(outputEditorInstance, processedText);\r\n  };\r\n\r\n  // FUNCTION TO UPDATE INPUT EDITOR FROM GRAPHICAL CONTROLS\r\n  const updateInputFromGraphics = () => {\r\n    const newNote = `${curStep}${VEXTAB_ACCIDENTALS[curAccident]}/${curOctave}`;\r\n    const newVexTab = `${defaultSetup} key=${key} time=${time}\\nnotes ${newNote}`;\r\n    setEditorText(inputEditorInstance, newVexTab);\r\n  };\r\n\r\n  // INITIALIZE VEXTAB EDITORS\r\n  useEffect(() => {\r\n    const initializeEditors = () => {\r\n      if (window.VexTab && window.VexTab.Div) {\r\n        // Initialize input editor\r\n        if (inputEditorRef.current && !inputEditorInstance) {\r\n          const inputDiv = new window.VexTab.Div(inputEditorRef.current);\r\n          setInputEditorInstance(inputDiv);\r\n\r\n          // Set up event listener for input changes\r\n          inputEditorRef.current.addEventListener('input', () => {\r\n            // Trigger processing when input changes\r\n            setTimeout(processInputToOutput, 100);\r\n          });\r\n        }\r\n\r\n        // Initialize output editor\r\n        if (outputEditorRef.current && !outputEditorInstance) {\r\n          const outputDiv = new window.VexTab.Div(outputEditorRef.current);\r\n          setOutputEditorInstance(outputDiv);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Wait for VexTab to load\r\n    const timer = setTimeout(initializeEditors, 500);\r\n    return () => clearTimeout(timer);\r\n  }, [inputEditorInstance, outputEditorInstance]);\r\n\r\n\r\n\r\n  return (\r\n    <main\r\n      style={{\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        alignItems: 'center',\r\n        justifyItems: 'center',\r\n        paddingTop: '4rem',\r\n        paddingBottom: '4rem',\r\n      }}\r\n    >\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vexflow/releases/vexflow-min.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/vextab-core.prod.js\"></script>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section style={{\r\n          border: '2px solid #9a62e3',\r\n          borderRadius: '2rem',\r\n          background: 'rgba(120, 25, 240, 0.06)',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          margin: '2rem 4rem',\r\n          padding: '2rem',\r\n          gap: '2rem',\r\n          alignItems: 'center',\r\n        }}>\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: 'grid',\r\n                gridTemplateColumns: '6rem 1fr 6rem',\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  justifyContent: 'center',\r\n                  alignContent: 'center',\r\n                  gap: '0.5rem',\r\n                  padding: '1rem 0rem',\r\n                }}>\r\n                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave + 1)}>↑</button>\r\n                <button style={styles.sideButton}>{curOctave}</button>\r\n                <button style={styles.sideButton} onClick={() => setCurOctave(curOctave - 1)}>↓</button>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: 'grid',\r\n                  gridTemplateRows: 'auto 1fr',\r\n                }}\r\n              >\r\n                <div\r\n                  style={{\r\n                    display: 'grid',\r\n                    gridTemplateColumns: 'repeat(7, 1fr)',\r\n                    padding: '1rem',\r\n                  }}>\r\n                  {NOTES.map(note => (\r\n                    <button style={{...styles.sideButton, ...(curStep === note ? styles.sideButtonActive : {})}}\r\n                    onClick={() => setCurStep(note)}>\r\n                      {note}\r\n                    </button>\r\n\r\n                  ))}\r\n                </div>\r\n\r\n                <div style={{border: '2px solid red', padding: '1rem'}}>\r\n                  <VexTabBlock source={rawVex} />\r\n                </div>\r\n                \r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  display: 'flex',\r\n                  flexDirection: 'column',\r\n                  justifyContent: 'center',\r\n                  alignContent: 'center',\r\n                  gap: '0.5rem',\r\n                  padding: '1rem 0rem',\r\n                }}>\r\n                {ACCIDENTALS.map(acc => (\r\n                  <button style={{...styles.sideButton, ...(curAccident === acc ? styles.sideButtonActive : {})}} onClick={(curAccident === acc) ? () => setCurAccident('') : () => setCurAccident(acc)}\r\n                  >\r\n                    {acc}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Add or remove</h3>\r\n            </div>\r\n\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Input Editor</h4>\r\n            <div\r\n              ref={inputEditorRef}\r\n              className=\"vextab-auto\"\r\n              editor=\"true\"\r\n              style={{ minHeight: '200px', border: '1px solid #ccc', margin: '1rem' }}\r\n            >\r\n              {rawVex}\r\n            </div>\r\n            <div style={{ display: 'flex', gap: '1rem', margin: '1rem' }}>\r\n              <button\r\n                onClick={updateInputFromGraphics}\r\n                style={{\r\n                  ...styles.sideButton,\r\n                  width: 'auto',\r\n                  padding: '0.5rem 1rem'\r\n                }}\r\n              >\r\n                Update from Graphics\r\n              </button>\r\n              <button\r\n                onClick={processInputToOutput}\r\n                style={{\r\n                  ...styles.sideButton,\r\n                  width: 'auto',\r\n                  padding: '0.5rem 1rem'\r\n                }}\r\n              >\r\n                Process to Output\r\n              </button>\r\n            </div>\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section style={{\r\n          border: '2px solid #9a62e3',\r\n          borderRadius: '2rem',\r\n          background: 'rgba(120, 25, 240, 0.06)',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          margin: '2rem 4rem',\r\n          padding: '2rem',\r\n          gap: '2rem',\r\n          alignItems: 'center',\r\n        }}>\r\n          <h3>Output</h3>\r\n          <div style={styles.musicDisplay}>\r\n            <h4>VexTab Output Editor</h4>\r\n            <div\r\n              ref={outputEditorRef}\r\n              className=\"vextab-auto\"\r\n              editor=\"true\"\r\n              style={{ minHeight: '200px', border: '1px solid #ccc', margin: '1rem' }}\r\n            >\r\n              {/* Output will be populated by the processing function */}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";;;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,WAAWA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EAC/BR,SAAS,CAAC,MAAM;IACdS,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEH,MAAM,CAAC;IAC3C,IAAII,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,GAAG,EAAE;MACtCC,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAACC,OAAO,CAACC,GAAG,IAAI;QACtDA,GAAG,CAACC,WAAW,GAAG,EAAE;QACpBD,GAAG,CAACC,WAAW,GAAGX,MAAM;QACxB,IAAII,MAAM,CAACC,MAAM,CAACC,GAAG,CAACI,GAAG,CAAC;MAC5B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EAEZ,oBAAOF,OAAA;IAAKc,SAAS,EAAC,aAAa;IAAAC,QAAA,EAAEb;EAAM;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AACpD;AAAChB,EAAA,CAbQF,WAAW;AAAAmB,EAAA,GAAXnB,WAAW;AAepB,SAASoB,SAASA,CAAA,EAAG;EAAAC,GAAA;EAEnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACrB,MAAM,CAACsB,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZT,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACrB,MAAM,CAACsB,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd,CAAC;IACDI,UAAU,EAAE;MACVC,eAAe,EAAE,OAAO;MACxBX,KAAK,EAAE,MAAM;MACbY,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,SAAS;MAChBX,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBW,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE;MAChBP,eAAe,EAAE,SAAS;MAC1BI,KAAK,EAAE,OAAO;MACdX,MAAM,EAAE,mBAAmB;MAC3Be,SAAS,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,wCAAwC;EAC7D,MAAMC,YAAY,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGpD,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAACkD,YAAY,CAAC;EAChD,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CACtC,GAAGiD,YAAY,QAAQE,GAAG,SAASE,IAAI;AACvC,QAAQE,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EACrB,CAAC;;EAGD;EACA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnC,MAAMC,kBAAkB,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,GAAG,EAAE,GAAG;IAAE,EAAE,EAAE;EAAG,CAAC;;EAEnE;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,GAAG,CAAC;EACnD,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,CAAC;;EAIxC;EACA,MAAMuE,cAAc,GAAGtE,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuE,eAAe,GAAGvE,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM6E,aAAa,GAAIC,cAAc,IAAK;IACxC,IAAIA,cAAc,IAAIA,cAAc,CAACC,OAAO,EAAE;MAC5C,OAAOD,cAAc,CAACC,OAAO,CAAC,CAAC;IACjC;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACF,cAAc,EAAEG,IAAI,KAAK;IAC9C,IAAIH,cAAc,IAAIA,cAAc,CAACI,OAAO,EAAE;MAC5CJ,cAAc,CAACI,OAAO,CAACD,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,SAAS,GAAGP,aAAa,CAACJ,mBAAmB,CAAC;IACpDjE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE2E,SAAS,CAAC;;IAErC;IACA;IACA,MAAMC,aAAa,GAAGD,SAAS,CAAC,CAAC;;IAEjCJ,aAAa,CAACL,oBAAoB,EAAEU,aAAa,CAAC;EACpD,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,OAAO,GAAG,GAAGtB,OAAO,GAAGH,kBAAkB,CAACK,WAAW,CAAC,IAAIJ,SAAS,EAAE;IAC3E,MAAMyB,SAAS,GAAG,GAAGvC,YAAY,QAAQE,GAAG,SAASE,IAAI,WAAWkC,OAAO,EAAE;IAC7EP,aAAa,CAACP,mBAAmB,EAAEe,SAAS,CAAC;EAC/C,CAAC;;EAED;EACAzF,SAAS,CAAC,MAAM;IACd,MAAM0F,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI/E,MAAM,CAACgF,MAAM,IAAIhF,MAAM,CAACgF,MAAM,CAAC9E,GAAG,EAAE;QACtC;QACA,IAAI2D,cAAc,CAACoB,OAAO,IAAI,CAAClB,mBAAmB,EAAE;UAClD,MAAMmB,QAAQ,GAAG,IAAIlF,MAAM,CAACgF,MAAM,CAAC9E,GAAG,CAAC2D,cAAc,CAACoB,OAAO,CAAC;UAC9DjB,sBAAsB,CAACkB,QAAQ,CAAC;;UAEhC;UACArB,cAAc,CAACoB,OAAO,CAACE,gBAAgB,CAAC,OAAO,EAAE,MAAM;YACrD;YACAC,UAAU,CAACX,oBAAoB,EAAE,GAAG,CAAC;UACvC,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIX,eAAe,CAACmB,OAAO,IAAI,CAAChB,oBAAoB,EAAE;UACpD,MAAMoB,SAAS,GAAG,IAAIrF,MAAM,CAACgF,MAAM,CAAC9E,GAAG,CAAC4D,eAAe,CAACmB,OAAO,CAAC;UAChEf,uBAAuB,CAACmB,SAAS,CAAC;QACpC;MACF;IACF,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGF,UAAU,CAACL,iBAAiB,EAAE,GAAG,CAAC;IAChD,OAAO,MAAMQ,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACvB,mBAAmB,EAAEE,oBAAoB,CAAC,CAAC;EAI/C,oBACEvE,OAAA;IACE8F,KAAK,EAAE;MACL9D,OAAO,EAAE,MAAM;MACf+D,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE,QAAQ;MACtBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACjB,CAAE;IAAApF,QAAA,gBAEFf,OAAA,CAACF,MAAM;MAAAiB,QAAA,gBACLf,OAAA;QAAQoG,GAAG,EAAC;MAAmD;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACzEnB,OAAA;QAAQoG,GAAG,EAAC;MAAuD;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC7EnB,OAAA;QAAQoG,GAAG,EAAC;MAA+C;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAGTnB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAS8F,KAAK,EAAE;UACdjE,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACf+D,aAAa,EAAE,QAAQ;UACvBrD,MAAM,EAAE,WAAW;UACnB2D,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXN,UAAU,EAAE;QACd,CAAE;QAAAjF,QAAA,gBACAf,OAAA;UAAAe,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGdnB,OAAA;UAAK8F,KAAK,EAAEvE,MAAM,CAACC,cAAe;UAAAT,QAAA,gBAChCf,OAAA;YACE8F,KAAK,EAAE;cACL9D,OAAO,EAAE,MAAM;cACfuE,mBAAmB,EAAE;YACvB,CAAE;YAAAxF,QAAA,gBAEFf,OAAA;cACE8F,KAAK,EAAE;gBACL9D,OAAO,EAAE,MAAM;gBACf+D,aAAa,EAAE,QAAQ;gBACvBS,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAtF,QAAA,gBACFf,OAAA;gBAAQ8F,KAAK,EAAEvE,MAAM,CAACY,UAAW;gBAACuE,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACD,SAAS,GAAG,CAAC,CAAE;gBAAA5C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxFnB,OAAA;gBAAQ8F,KAAK,EAAEvE,MAAM,CAACY,UAAW;gBAAApB,QAAA,EAAE4C;cAAS;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eACtDnB,OAAA;gBAAQ8F,KAAK,EAAEvE,MAAM,CAACY,UAAW;gBAACuE,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACD,SAAS,GAAG,CAAC,CAAE;gBAAA5C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENnB,OAAA;cACE8F,KAAK,EAAE;gBACL9D,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAAlB,QAAA,gBAEFf,OAAA;gBACE8F,KAAK,EAAE;kBACL9D,OAAO,EAAE,MAAM;kBACfuE,mBAAmB,EAAE,gBAAgB;kBACrCF,OAAO,EAAE;gBACX,CAAE;gBAAAtF,QAAA,EACDyC,KAAK,CAACmD,GAAG,CAACC,IAAI,iBACb5G,OAAA;kBAAQ8F,KAAK,EAAE;oBAAC,GAAGvE,MAAM,CAACY,UAAU;oBAAE,IAAI0B,OAAO,KAAK+C,IAAI,GAAGrF,MAAM,CAACoB,gBAAgB,GAAG,CAAC,CAAC;kBAAC,CAAE;kBAC5F+D,OAAO,EAAEA,CAAA,KAAM5C,UAAU,CAAC8C,IAAI,CAAE;kBAAA7F,QAAA,EAC7B6F;gBAAI;kBAAA5F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAET;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENnB,OAAA;gBAAK8F,KAAK,EAAE;kBAACjE,MAAM,EAAE,eAAe;kBAAEwE,OAAO,EAAE;gBAAM,CAAE;gBAAAtF,QAAA,eACrDf,OAAA,CAACC,WAAW;kBAACC,MAAM,EAAEmD;gBAAO;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH,CAAC,eAENnB,OAAA;cACE8F,KAAK,EAAE;gBACL9D,OAAO,EAAE,MAAM;gBACf+D,aAAa,EAAE,QAAQ;gBACvBS,cAAc,EAAE,QAAQ;gBACxBC,YAAY,EAAE,QAAQ;gBACtBH,GAAG,EAAE,QAAQ;gBACbD,OAAO,EAAE;cACX,CAAE;cAAAtF,QAAA,EACD0C,WAAW,CAACkD,GAAG,CAACE,GAAG,iBAClB7G,OAAA;gBAAQ8F,KAAK,EAAE;kBAAC,GAAGvE,MAAM,CAACY,UAAU;kBAAE,IAAI4B,WAAW,KAAK8C,GAAG,GAAGtF,MAAM,CAACoB,gBAAgB,GAAG,CAAC,CAAC;gBAAC,CAAE;gBAAC+D,OAAO,EAAG3C,WAAW,KAAK8C,GAAG,GAAI,MAAM7C,cAAc,CAAC,EAAE,CAAC,GAAG,MAAMA,cAAc,CAAC6C,GAAG,CAAE;gBAAA9F,QAAA,EAEnL8F;cAAG;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,eAENnB,OAAA;YAAAe,QAAA,eACEf,OAAA;cAAAe,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAENnB,OAAA;YAAAe,QAAA,eACEf,OAAA;cAAAe,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,eAINnB,OAAA;UAAK8F,KAAK,EAAEvE,MAAM,CAACW,YAAa;UAAAnB,QAAA,gBAC9Bf,OAAA;YAAAe,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BnB,OAAA;YACE8G,GAAG,EAAE3C,cAAe;YACpBrD,SAAS,EAAC,aAAa;YACvBiG,MAAM,EAAC,MAAM;YACbjB,KAAK,EAAE;cAAEkB,SAAS,EAAE,OAAO;cAAEnF,MAAM,EAAE,gBAAgB;cAAEa,MAAM,EAAE;YAAO,CAAE;YAAA3B,QAAA,EAEvEsC;UAAM;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnB,OAAA;YAAK8F,KAAK,EAAE;cAAE9D,OAAO,EAAE,MAAM;cAAEsE,GAAG,EAAE,MAAM;cAAE5D,MAAM,EAAE;YAAO,CAAE;YAAA3B,QAAA,gBAC3Df,OAAA;cACE0G,OAAO,EAAExB,uBAAwB;cACjCY,KAAK,EAAE;gBACL,GAAGvE,MAAM,CAACY,UAAU;gBACpBV,KAAK,EAAE,MAAM;gBACb4E,OAAO,EAAE;cACX,CAAE;cAAAtF,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnB,OAAA;cACE0G,OAAO,EAAE3B,oBAAqB;cAC9Be,KAAK,EAAE;gBACL,GAAGvE,MAAM,CAACY,UAAU;gBACpBV,KAAK,EAAE,MAAM;gBACb4E,OAAO,EAAE;cACX,CAAE;cAAAtF,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNnB,OAAA;MAAAe,QAAA,eACEf,OAAA;QAAS8F,KAAK,EAAE;UACdjE,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACf+D,aAAa,EAAE,QAAQ;UACvBrD,MAAM,EAAE,WAAW;UACnB2D,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE,MAAM;UACXN,UAAU,EAAE;QACd,CAAE;QAAAjF,QAAA,gBACAf,OAAA;UAAAe,QAAA,EAAI;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfnB,OAAA;UAAK8F,KAAK,EAAEvE,MAAM,CAACW,YAAa;UAAAnB,QAAA,gBAC9Bf,OAAA;YAAAe,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BnB,OAAA;YACE8G,GAAG,EAAE1C,eAAgB;YACrBtD,SAAS,EAAC,aAAa;YACvBiG,MAAM,EAAC,MAAM;YACbjB,KAAK,EAAE;cAAEkB,SAAS,EAAE,OAAO;cAAEnF,MAAM,EAAE,gBAAgB;cAAEa,MAAM,EAAE;YAAO;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACG,GAAA,CAvTQD,SAAS;AAAA4F,GAAA,GAAT5F,SAAS;AAyTlB,eAAeA,SAAS;AAAC,IAAAD,EAAA,EAAA6F,GAAA;AAAAC,YAAA,CAAA9F,EAAA;AAAA8F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}