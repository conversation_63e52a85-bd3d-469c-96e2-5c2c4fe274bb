{"ast": null, "code": "import { StaveNote } from './stavenote.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class GraceNote extends StaveNote {\n  static get CATEGORY() {\n    return \"GraceNote\";\n  }\n  static get LEDGER_LINE_OFFSET() {\n    return 2;\n  }\n  constructor(noteStruct) {\n    super(Object.assign({\n      strokePx: GraceNote.LEDGER_LINE_OFFSET\n    }, noteStruct));\n    this.slash = noteStruct.slash || false;\n    this.slur = true;\n    this.buildNoteHeads();\n    this.width = 3;\n  }\n  getStemExtension() {\n    if (this.stemExtensionOverride) {\n      return this.stemExtensionOverride;\n    }\n    let ret = super.getStemExtension();\n    ret = Stem.HEIGHT * this.getFontScale() - Stem.HEIGHT + ret;\n    return ret;\n  }\n  draw() {\n    super.draw();\n    this.setRendered();\n    const stem = this.stem;\n    if (this.slash && stem) {\n      const scale = this.getFontScale();\n      let slashBBox = undefined;\n      const beam = this.beam;\n      if (beam) {\n        if (!beam.postFormatted) {\n          beam.postFormat();\n        }\n        slashBBox = this.calcBeamedNotesSlashBBox(8 * scale, 8 * scale, {\n          stem: 6 * scale,\n          beam: 5 * scale\n        });\n      } else {\n        const stemDirection = this.getStemDirection();\n        const noteHeadBounds = this.getNoteHeadBounds();\n        const noteHeadWidth = this.noteHeads[0].getWidth();\n        const x = stemDirection === Stem.DOWN ? this.getAbsoluteX() : this.getAbsoluteX() + noteHeadWidth;\n        const defaultOffsetY = Tables.STEM_HEIGHT * scale / 2;\n        const y = stemDirection === Stem.DOWN ? noteHeadBounds.yBottom + defaultOffsetY : noteHeadBounds.yTop - defaultOffsetY;\n        if (stemDirection === Stem.DOWN) {\n          slashBBox = {\n            x1: x - noteHeadWidth,\n            y1: y - noteHeadWidth,\n            x2: x + noteHeadWidth,\n            y2: y + noteHeadWidth\n          };\n        } else {\n          slashBBox = {\n            x1: x - noteHeadWidth,\n            y1: y + noteHeadWidth,\n            x2: x + noteHeadWidth,\n            y2: y - noteHeadWidth\n          };\n        }\n      }\n      const ctx = this.checkContext();\n      ctx.setLineWidth(1 * scale);\n      ctx.beginPath();\n      ctx.moveTo(slashBBox.x1, slashBBox.y1);\n      ctx.lineTo(slashBBox.x2, slashBBox.y2);\n      ctx.closePath();\n      ctx.stroke();\n    }\n  }\n  calcBeamedNotesSlashBBox(slashStemOffset, slashBeamOffset, protrusions) {\n    const beam = this.beam;\n    if (!beam) throw new RuntimeError('NoBeam', \"Can't calculate without a beam.\");\n    const beamSlope = beam.slope;\n    const isBeamEndNote = beam.notes[beam.notes.length - 1] === this;\n    const scaleX = isBeamEndNote ? -1 : 1;\n    const beamAngle = Math.atan(beamSlope * scaleX);\n    const iPointOnBeam = {\n      dx: Math.cos(beamAngle) * slashBeamOffset,\n      dy: Math.sin(beamAngle) * slashBeamOffset\n    };\n    slashStemOffset *= this.getStemDirection();\n    const slashAngle = Math.atan((iPointOnBeam.dy - slashStemOffset) / iPointOnBeam.dx);\n    const protrusionStemDeltaX = Math.cos(slashAngle) * protrusions.stem * scaleX;\n    const protrusionStemDeltaY = Math.sin(slashAngle) * protrusions.stem;\n    const protrusionBeamDeltaX = Math.cos(slashAngle) * protrusions.beam * scaleX;\n    const protrusionBeamDeltaY = Math.sin(slashAngle) * protrusions.beam;\n    const stemX = this.getStemX();\n    const stem0X = beam.notes[0].getStemX();\n    const stemY = beam.getBeamYToDraw() + (stemX - stem0X) * beamSlope;\n    const ret = {\n      x1: stemX - protrusionStemDeltaX,\n      y1: stemY + slashStemOffset - protrusionStemDeltaY,\n      x2: stemX + iPointOnBeam.dx * scaleX + protrusionBeamDeltaX,\n      y2: stemY + iPointOnBeam.dy + protrusionBeamDeltaY\n    };\n    return ret;\n  }\n}", "map": {"version": 3, "names": ["StaveNote", "<PERSON><PERSON>", "Tables", "RuntimeError", "<PERSON><PERSON><PERSON>", "CATEGORY", "LEDGER_LINE_OFFSET", "constructor", "noteStruct", "Object", "assign", "strokePx", "slash", "slur", "buildNoteHeads", "width", "getStemExtension", "stemExtensionOverride", "ret", "HEIGHT", "getFontScale", "draw", "setRendered", "stem", "scale", "slashBBox", "undefined", "beam", "postFormatted", "postFormat", "calcBeamedNotesSlashBBox", "stemDirection", "getStemDirection", "noteHeadBounds", "getNoteHeadBounds", "noteHeadWidth", "noteHeads", "getWidth", "x", "DOWN", "getAbsoluteX", "defaultOffsetY", "STEM_HEIGHT", "y", "yBottom", "yTop", "x1", "y1", "x2", "y2", "ctx", "checkContext", "setLineWidth", "beginPath", "moveTo", "lineTo", "closePath", "stroke", "slashStemOffset", "slashBeamOffset", "protrusions", "beamSlope", "slope", "isBeamEndNote", "notes", "length", "scaleX", "beamAngle", "Math", "atan", "iPointOnBeam", "dx", "cos", "dy", "sin", "slashAngle", "protrusionStemDeltaX", "protrusionStemDeltaY", "protrusionBeamDeltaX", "protrusionBeamDeltaY", "stemX", "getStemX", "stem0X", "stemY", "getBeamYToDraw"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/gracenote.js"], "sourcesContent": ["import { StaveNote } from './stavenote.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class GraceNote extends StaveNote {\n    static get CATEGORY() {\n        return \"GraceNote\";\n    }\n    static get LEDGER_LINE_OFFSET() {\n        return 2;\n    }\n    constructor(noteStruct) {\n        super(Object.assign({ strokePx: GraceNote.LEDGER_LINE_OFFSET }, noteStruct));\n        this.slash = noteStruct.slash || false;\n        this.slur = true;\n        this.buildNoteHeads();\n        this.width = 3;\n    }\n    getStemExtension() {\n        if (this.stemExtensionOverride) {\n            return this.stemExtensionOverride;\n        }\n        let ret = super.getStemExtension();\n        ret = Stem.HEIGHT * this.getFontScale() - Stem.HEIGHT + ret;\n        return ret;\n    }\n    draw() {\n        super.draw();\n        this.setRendered();\n        const stem = this.stem;\n        if (this.slash && stem) {\n            const scale = this.getFontScale();\n            let slashBBox = undefined;\n            const beam = this.beam;\n            if (beam) {\n                if (!beam.postFormatted) {\n                    beam.postFormat();\n                }\n                slashBBox = this.calcBeamedNotesSlashBBox(8 * scale, 8 * scale, {\n                    stem: 6 * scale,\n                    beam: 5 * scale,\n                });\n            }\n            else {\n                const stemDirection = this.getStemDirection();\n                const noteHeadBounds = this.getNoteHeadBounds();\n                const noteHeadWidth = this.noteHeads[0].getWidth();\n                const x = stemDirection === Stem.DOWN ? this.getAbsoluteX() : this.getAbsoluteX() + noteHeadWidth;\n                const defaultOffsetY = (Tables.STEM_HEIGHT * scale) / 2;\n                const y = stemDirection === Stem.DOWN ? noteHeadBounds.yBottom + defaultOffsetY : noteHeadBounds.yTop - defaultOffsetY;\n                if (stemDirection === Stem.DOWN) {\n                    slashBBox = {\n                        x1: x - noteHeadWidth,\n                        y1: y - noteHeadWidth,\n                        x2: x + noteHeadWidth,\n                        y2: y + noteHeadWidth,\n                    };\n                }\n                else {\n                    slashBBox = {\n                        x1: x - noteHeadWidth,\n                        y1: y + noteHeadWidth,\n                        x2: x + noteHeadWidth,\n                        y2: y - noteHeadWidth,\n                    };\n                }\n            }\n            const ctx = this.checkContext();\n            ctx.setLineWidth(1 * scale);\n            ctx.beginPath();\n            ctx.moveTo(slashBBox.x1, slashBBox.y1);\n            ctx.lineTo(slashBBox.x2, slashBBox.y2);\n            ctx.closePath();\n            ctx.stroke();\n        }\n    }\n    calcBeamedNotesSlashBBox(slashStemOffset, slashBeamOffset, protrusions) {\n        const beam = this.beam;\n        if (!beam)\n            throw new RuntimeError('NoBeam', \"Can't calculate without a beam.\");\n        const beamSlope = beam.slope;\n        const isBeamEndNote = beam.notes[beam.notes.length - 1] === this;\n        const scaleX = isBeamEndNote ? -1 : 1;\n        const beamAngle = Math.atan(beamSlope * scaleX);\n        const iPointOnBeam = {\n            dx: Math.cos(beamAngle) * slashBeamOffset,\n            dy: Math.sin(beamAngle) * slashBeamOffset,\n        };\n        slashStemOffset *= this.getStemDirection();\n        const slashAngle = Math.atan((iPointOnBeam.dy - slashStemOffset) / iPointOnBeam.dx);\n        const protrusionStemDeltaX = Math.cos(slashAngle) * protrusions.stem * scaleX;\n        const protrusionStemDeltaY = Math.sin(slashAngle) * protrusions.stem;\n        const protrusionBeamDeltaX = Math.cos(slashAngle) * protrusions.beam * scaleX;\n        const protrusionBeamDeltaY = Math.sin(slashAngle) * protrusions.beam;\n        const stemX = this.getStemX();\n        const stem0X = beam.notes[0].getStemX();\n        const stemY = beam.getBeamYToDraw() + (stemX - stem0X) * beamSlope;\n        const ret = {\n            x1: stemX - protrusionStemDeltaX,\n            y1: stemY + slashStemOffset - protrusionStemDeltaY,\n            x2: stemX + iPointOnBeam.dx * scaleX + protrusionBeamDeltaX,\n            y2: stemY + iPointOnBeam.dy + protrusionBeamDeltaY,\n        };\n        return ret;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,SAAS,SAASJ,SAAS,CAAC;EACrC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACA,WAAWC,kBAAkBA,CAAA,EAAG;IAC5B,OAAO,CAAC;EACZ;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,KAAK,CAACC,MAAM,CAACC,MAAM,CAAC;MAAEC,QAAQ,EAAEP,SAAS,CAACE;IAAmB,CAAC,EAAEE,UAAU,CAAC,CAAC;IAC5E,IAAI,CAACI,KAAK,GAAGJ,UAAU,CAACI,KAAK,IAAI,KAAK;IACtC,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,KAAK,GAAG,CAAC;EAClB;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,qBAAqB,EAAE;MAC5B,OAAO,IAAI,CAACA,qBAAqB;IACrC;IACA,IAAIC,GAAG,GAAG,KAAK,CAACF,gBAAgB,CAAC,CAAC;IAClCE,GAAG,GAAGjB,IAAI,CAACkB,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAGnB,IAAI,CAACkB,MAAM,GAAGD,GAAG;IAC3D,OAAOA,GAAG;EACd;EACAG,IAAIA,CAAA,EAAG;IACH,KAAK,CAACA,IAAI,CAAC,CAAC;IACZ,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,IAAI,CAACX,KAAK,IAAIW,IAAI,EAAE;MACpB,MAAMC,KAAK,GAAG,IAAI,CAACJ,YAAY,CAAC,CAAC;MACjC,IAAIK,SAAS,GAAGC,SAAS;MACzB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;MACtB,IAAIA,IAAI,EAAE;QACN,IAAI,CAACA,IAAI,CAACC,aAAa,EAAE;UACrBD,IAAI,CAACE,UAAU,CAAC,CAAC;QACrB;QACAJ,SAAS,GAAG,IAAI,CAACK,wBAAwB,CAAC,CAAC,GAAGN,KAAK,EAAE,CAAC,GAAGA,KAAK,EAAE;UAC5DD,IAAI,EAAE,CAAC,GAAGC,KAAK;UACfG,IAAI,EAAE,CAAC,GAAGH;QACd,CAAC,CAAC;MACN,CAAC,MACI;QACD,MAAMO,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;QAC7C,MAAMC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC/C,MAAMC,aAAa,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QAClD,MAAMC,CAAC,GAAGP,aAAa,KAAK9B,IAAI,CAACsC,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACA,YAAY,CAAC,CAAC,GAAGL,aAAa;QACjG,MAAMM,cAAc,GAAIvC,MAAM,CAACwC,WAAW,GAAGlB,KAAK,GAAI,CAAC;QACvD,MAAMmB,CAAC,GAAGZ,aAAa,KAAK9B,IAAI,CAACsC,IAAI,GAAGN,cAAc,CAACW,OAAO,GAAGH,cAAc,GAAGR,cAAc,CAACY,IAAI,GAAGJ,cAAc;QACtH,IAAIV,aAAa,KAAK9B,IAAI,CAACsC,IAAI,EAAE;UAC7Bd,SAAS,GAAG;YACRqB,EAAE,EAAER,CAAC,GAAGH,aAAa;YACrBY,EAAE,EAAEJ,CAAC,GAAGR,aAAa;YACrBa,EAAE,EAAEV,CAAC,GAAGH,aAAa;YACrBc,EAAE,EAAEN,CAAC,GAAGR;UACZ,CAAC;QACL,CAAC,MACI;UACDV,SAAS,GAAG;YACRqB,EAAE,EAAER,CAAC,GAAGH,aAAa;YACrBY,EAAE,EAAEJ,CAAC,GAAGR,aAAa;YACrBa,EAAE,EAAEV,CAAC,GAAGH,aAAa;YACrBc,EAAE,EAAEN,CAAC,GAAGR;UACZ,CAAC;QACL;MACJ;MACA,MAAMe,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MAC/BD,GAAG,CAACE,YAAY,CAAC,CAAC,GAAG5B,KAAK,CAAC;MAC3B0B,GAAG,CAACG,SAAS,CAAC,CAAC;MACfH,GAAG,CAACI,MAAM,CAAC7B,SAAS,CAACqB,EAAE,EAAErB,SAAS,CAACsB,EAAE,CAAC;MACtCG,GAAG,CAACK,MAAM,CAAC9B,SAAS,CAACuB,EAAE,EAAEvB,SAAS,CAACwB,EAAE,CAAC;MACtCC,GAAG,CAACM,SAAS,CAAC,CAAC;MACfN,GAAG,CAACO,MAAM,CAAC,CAAC;IAChB;EACJ;EACA3B,wBAAwBA,CAAC4B,eAAe,EAAEC,eAAe,EAAEC,WAAW,EAAE;IACpE,MAAMjC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAI,CAACA,IAAI,EACL,MAAM,IAAIxB,YAAY,CAAC,QAAQ,EAAE,iCAAiC,CAAC;IACvE,MAAM0D,SAAS,GAAGlC,IAAI,CAACmC,KAAK;IAC5B,MAAMC,aAAa,GAAGpC,IAAI,CAACqC,KAAK,CAACrC,IAAI,CAACqC,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI;IAChE,MAAMC,MAAM,GAAGH,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;IACrC,MAAMI,SAAS,GAAGC,IAAI,CAACC,IAAI,CAACR,SAAS,GAAGK,MAAM,CAAC;IAC/C,MAAMI,YAAY,GAAG;MACjBC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAACL,SAAS,CAAC,GAAGR,eAAe;MACzCc,EAAE,EAAEL,IAAI,CAACM,GAAG,CAACP,SAAS,CAAC,GAAGR;IAC9B,CAAC;IACDD,eAAe,IAAI,IAAI,CAAC1B,gBAAgB,CAAC,CAAC;IAC1C,MAAM2C,UAAU,GAAGP,IAAI,CAACC,IAAI,CAAC,CAACC,YAAY,CAACG,EAAE,GAAGf,eAAe,IAAIY,YAAY,CAACC,EAAE,CAAC;IACnF,MAAMK,oBAAoB,GAAGR,IAAI,CAACI,GAAG,CAACG,UAAU,CAAC,GAAGf,WAAW,CAACrC,IAAI,GAAG2C,MAAM;IAC7E,MAAMW,oBAAoB,GAAGT,IAAI,CAACM,GAAG,CAACC,UAAU,CAAC,GAAGf,WAAW,CAACrC,IAAI;IACpE,MAAMuD,oBAAoB,GAAGV,IAAI,CAACI,GAAG,CAACG,UAAU,CAAC,GAAGf,WAAW,CAACjC,IAAI,GAAGuC,MAAM;IAC7E,MAAMa,oBAAoB,GAAGX,IAAI,CAACM,GAAG,CAACC,UAAU,CAAC,GAAGf,WAAW,CAACjC,IAAI;IACpE,MAAMqD,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,MAAM,GAAGvD,IAAI,CAACqC,KAAK,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC;IACvC,MAAME,KAAK,GAAGxD,IAAI,CAACyD,cAAc,CAAC,CAAC,GAAG,CAACJ,KAAK,GAAGE,MAAM,IAAIrB,SAAS;IAClE,MAAM3C,GAAG,GAAG;MACR4B,EAAE,EAAEkC,KAAK,GAAGJ,oBAAoB;MAChC7B,EAAE,EAAEoC,KAAK,GAAGzB,eAAe,GAAGmB,oBAAoB;MAClD7B,EAAE,EAAEgC,KAAK,GAAGV,YAAY,CAACC,EAAE,GAAGL,MAAM,GAAGY,oBAAoB;MAC3D7B,EAAE,EAAEkC,KAAK,GAAGb,YAAY,CAACG,EAAE,GAAGM;IAClC,CAAC;IACD,OAAO7D,GAAG;EACd;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}