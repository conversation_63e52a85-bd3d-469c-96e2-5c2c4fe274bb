{"ast": null, "code": "import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { isStemmableNote } from './typeguard.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (ChordSymbol.DEBUG) log('VexFlow.ChordSymbol', args);\n}\nexport class ChordSymbolBlock extends Element {\n  constructor(text, symbolModifier, xShift, yShift, vAlign) {\n    super();\n    this.text = text;\n    this.symbolModifier = symbolModifier;\n    this.xShift = xShift;\n    this.yShift = yShift;\n    this.vAlign = vAlign;\n  }\n  isSuperscript() {\n    return this.symbolModifier === SymbolModifiers.SUPERSCRIPT;\n  }\n  isSubscript() {\n    return this.symbolModifier === SymbolModifiers.SUBSCRIPT;\n  }\n}\nexport var ChordSymbolHorizontalJustify;\n(function (ChordSymbolHorizontalJustify) {\n  ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"LEFT\"] = 1] = \"LEFT\";\n  ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"CENTER\"] = 2] = \"CENTER\";\n  ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"RIGHT\"] = 3] = \"RIGHT\";\n  ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(ChordSymbolHorizontalJustify || (ChordSymbolHorizontalJustify = {}));\nexport var ChordSymbolVerticalJustify;\n(function (ChordSymbolVerticalJustify) {\n  ChordSymbolVerticalJustify[ChordSymbolVerticalJustify[\"TOP\"] = 1] = \"TOP\";\n  ChordSymbolVerticalJustify[ChordSymbolVerticalJustify[\"BOTTOM\"] = 2] = \"BOTTOM\";\n})(ChordSymbolVerticalJustify || (ChordSymbolVerticalJustify = {}));\nexport var SymbolModifiers;\n(function (SymbolModifiers) {\n  SymbolModifiers[SymbolModifiers[\"NONE\"] = 1] = \"NONE\";\n  SymbolModifiers[SymbolModifiers[\"SUBSCRIPT\"] = 2] = \"SUBSCRIPT\";\n  SymbolModifiers[SymbolModifiers[\"SUPERSCRIPT\"] = 3] = \"SUPERSCRIPT\";\n})(SymbolModifiers || (SymbolModifiers = {}));\nexport class ChordSymbol extends Modifier {\n  static get CATEGORY() {\n    return \"ChordSymbol\";\n  }\n  static get superSubRatio() {\n    return Metrics.get('ChordSymbol.superSubRatio');\n  }\n  static get spacingBetweenBlocks() {\n    return Metrics.get('ChordSymbol.spacing');\n  }\n  static get superscriptOffset() {\n    return Metrics.get('ChordSymbol.superscriptOffset');\n  }\n  static get subscriptOffset() {\n    return Metrics.get('ChordSymbol.subscriptOffset');\n  }\n  static get minPadding() {\n    return Metrics.get('NoteHead.minPadding');\n  }\n  static format(symbols, state) {\n    if (!symbols || symbols.length === 0) return false;\n    let width = 0;\n    let leftWidth = 0;\n    let rightWidth = 0;\n    let maxLeftGlyphWidth = 0;\n    let maxRightGlyphWidth = 0;\n    for (const symbol of symbols) {\n      const note = symbol.checkAttachedNote();\n      let lineSpaces = 1;\n      for (let j = 0; j < symbol.symbolBlocks.length; ++j) {\n        const block = symbol.symbolBlocks[j];\n        const sup = block.isSuperscript();\n        const sub = block.isSubscript();\n        block.setXShift(width);\n        if (sup || sub) {\n          lineSpaces = 2;\n        }\n        if (sub && j > 0) {\n          const prev = symbol.symbolBlocks[j - 1];\n          if (prev.isSuperscript()) {\n            block.setXShift(width - prev.getWidth() - ChordSymbol.minPadding);\n            block.vAlign = true;\n            width += -prev.getWidth() - ChordSymbol.minPadding + (prev.getWidth() > block.getWidth() ? prev.getWidth() - block.getWidth() : 0);\n          }\n        }\n        width += block.getWidth() + ChordSymbol.minPadding;\n      }\n      if (symbol.getVertical() === ChordSymbolVerticalJustify.TOP) {\n        symbol.setTextLine(state.topTextLine);\n        state.topTextLine += lineSpaces;\n      } else {\n        symbol.setTextLine(state.textLine + 1);\n        state.textLine += lineSpaces + 1;\n      }\n      if (symbol.getReportWidth()) {\n        if (isStemmableNote(note)) {\n          const glyphWidth = note.getGlyphWidth();\n          if (symbol.getHorizontal() === ChordSymbolHorizontalJustify.RIGHT) {\n            maxLeftGlyphWidth = Math.max(glyphWidth, maxLeftGlyphWidth);\n            leftWidth = Math.max(leftWidth, width) + ChordSymbol.minPadding;\n          } else if (symbol.getHorizontal() === ChordSymbolHorizontalJustify.LEFT) {\n            maxRightGlyphWidth = Math.max(glyphWidth, maxRightGlyphWidth);\n            rightWidth = Math.max(rightWidth, width);\n          } else {\n            leftWidth = Math.max(leftWidth, width / 2) + ChordSymbol.minPadding;\n            rightWidth = Math.max(rightWidth, width / 2);\n            maxLeftGlyphWidth = Math.max(glyphWidth / 2, maxLeftGlyphWidth);\n            maxRightGlyphWidth = Math.max(glyphWidth / 2, maxRightGlyphWidth);\n          }\n        }\n        symbol.width = width;\n      }\n      width = 0;\n    }\n    const rightOverlap = Math.min(Math.max(rightWidth - maxRightGlyphWidth, 0), Math.max(rightWidth - state.rightShift, 0));\n    const leftOverlap = Math.min(Math.max(leftWidth - maxLeftGlyphWidth, 0), Math.max(leftWidth - state.leftShift, 0));\n    state.leftShift += leftOverlap;\n    state.rightShift += rightOverlap;\n    return true;\n  }\n  constructor() {\n    super();\n    this.symbolBlocks = [];\n    this.horizontal = ChordSymbolHorizontalJustify.LEFT;\n    this.vertical = ChordSymbolVerticalJustify.TOP;\n    this.reportWidth = true;\n  }\n  get superscriptOffset() {\n    return ChordSymbol.superscriptOffset * Font.convertSizeToPixelValue(this.fontInfo.size);\n  }\n  get subscriptOffset() {\n    return ChordSymbol.subscriptOffset * Font.convertSizeToPixelValue(this.fontInfo.size);\n  }\n  setReportWidth(value) {\n    this.reportWidth = value;\n    return this;\n  }\n  getReportWidth() {\n    return this.reportWidth;\n  }\n  getSymbolBlock(params = {}) {\n    var _a, _b;\n    const symbolBlock = new ChordSymbolBlock((_a = params.text) !== null && _a !== void 0 ? _a : '', (_b = params.symbolModifier) !== null && _b !== void 0 ? _b : SymbolModifiers.NONE, 0, 0, false);\n    if (symbolBlock.isSubscript()) {\n      symbolBlock.setYShift(this.subscriptOffset);\n    }\n    if (symbolBlock.isSuperscript()) {\n      symbolBlock.setYShift(this.superscriptOffset);\n    }\n    if (symbolBlock.isSubscript() || symbolBlock.isSuperscript()) {\n      const {\n        family,\n        size,\n        weight,\n        style\n      } = this.fontInfo;\n      const smallerFontSize = Font.scaleSize(size, ChordSymbol.superSubRatio);\n      symbolBlock.setFont(family, smallerFontSize, weight, style);\n    } else {\n      symbolBlock.setFont(this.fontInfo);\n    }\n    return symbolBlock;\n  }\n  addSymbolBlock(parameters) {\n    this.symbolBlocks.push(this.getSymbolBlock(parameters));\n    return this;\n  }\n  addText(text, parameters = {}) {\n    return this.addSymbolBlock(Object.assign(Object.assign({}, parameters), {\n      text\n    }));\n  }\n  addTextSuperscript(text) {\n    const symbolModifier = SymbolModifiers.SUPERSCRIPT;\n    return this.addSymbolBlock({\n      text,\n      symbolModifier\n    });\n  }\n  addTextSubscript(text) {\n    const symbolModifier = SymbolModifiers.SUBSCRIPT;\n    return this.addSymbolBlock({\n      text,\n      symbolModifier\n    });\n  }\n  addGlyphSuperscript(glyph) {\n    return this.addTextSuperscript(ChordSymbol.glyphs[glyph]);\n  }\n  addGlyph(glyph, params = {}) {\n    return this.addText(ChordSymbol.glyphs[glyph], params);\n  }\n  addGlyphOrText(text, params = {}) {\n    let str = '';\n    for (let i = 0; i < text.length; ++i) {\n      const char = text[i];\n      const glyph = ChordSymbol.glyphs[char];\n      if (glyph) {\n        str += glyph;\n      } else {\n        str += char;\n      }\n    }\n    if (str.length > 0) {\n      this.addText(str, params);\n    }\n    return this;\n  }\n  addLine(params = {}) {\n    return this.addText('\\ue874\\ue874', params);\n  }\n  setVertical(vj) {\n    this.vertical = typeof vj === 'string' ? ChordSymbol.VerticalJustifyString[vj] : vj;\n    return this;\n  }\n  getVertical() {\n    return this.vertical;\n  }\n  setHorizontal(hj) {\n    this.horizontal = typeof hj === 'string' ? ChordSymbol.HorizontalJustifyString[hj] : hj;\n    return this;\n  }\n  getHorizontal() {\n    return this.horizontal;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    ctx.openGroup('chordsymbol', this.getAttribute('id'));\n    const start = note.getModifierStartXY(Modifier.Position.ABOVE, this.index);\n    ctx.setFont(this.fontInfo);\n    let y;\n    const hasStem = note.hasStem();\n    const stave = note.checkStave();\n    if (this.vertical === ChordSymbolVerticalJustify.BOTTOM) {\n      y = stave.getYForBottomText(this.textLine + Tables.TEXT_HEIGHT_OFFSET_HACK);\n      if (hasStem) {\n        const stemExt = note.checkStem().getExtents();\n        const spacing = stave.getSpacingBetweenLines();\n        const stemBase = note.getStemDirection() === 1 ? stemExt.baseY : stemExt.topY;\n        y = Math.max(y, stemBase + spacing * (this.textLine + 2));\n      }\n    } else {\n      const topY = Math.min(...note.getYs());\n      y = Math.min(stave.getYForTopText(this.textLine), topY - 10);\n      if (hasStem) {\n        const stemExt = note.checkStem().getExtents();\n        const spacing = stave.getSpacingBetweenLines();\n        y = Math.min(y, stemExt.topY - 5 - spacing * this.textLine);\n      }\n    }\n    let x = start.x;\n    if (this.horizontal === ChordSymbolHorizontalJustify.LEFT) {\n      x = start.x;\n    } else if (this.horizontal === ChordSymbolHorizontalJustify.RIGHT) {\n      x = start.x + this.getWidth();\n    } else if (this.horizontal === ChordSymbolHorizontalJustify.CENTER) {\n      x = start.x - this.getWidth() / 2;\n    } else {\n      x = note.getStemX() - this.getWidth() / 2;\n    }\n    L('Rendering ChordSymbol: ', x, y);\n    this.symbolBlocks.forEach(symbol => {\n      L('Rendering Text: ', symbol.getText(), x + symbol.getXShift(), y + symbol.getYShift());\n      symbol.setX(x);\n      symbol.setY(y);\n      symbol.renderText(ctx, 0, 0);\n    });\n    ctx.closeGroup();\n  }\n  getBoundingBox() {\n    const boundingBox = this.symbolBlocks[0].getBoundingBox();\n    for (let i = 1; i < this.symbolBlocks.length; i++) {\n      boundingBox.mergeWith(this.symbolBlocks[i].getBoundingBox());\n    }\n    return boundingBox;\n  }\n}\nChordSymbol.DEBUG = false;\nChordSymbol.HorizontalJustify = ChordSymbolHorizontalJustify;\nChordSymbol.HorizontalJustifyString = {\n  left: ChordSymbolHorizontalJustify.LEFT,\n  right: ChordSymbolHorizontalJustify.RIGHT,\n  center: ChordSymbolHorizontalJustify.CENTER,\n  centerStem: ChordSymbolHorizontalJustify.CENTER_STEM\n};\nChordSymbol.VerticalJustify = ChordSymbolVerticalJustify;\nChordSymbol.VerticalJustifyString = {\n  top: ChordSymbolVerticalJustify.TOP,\n  above: ChordSymbolVerticalJustify.TOP,\n  below: ChordSymbolVerticalJustify.BOTTOM,\n  bottom: ChordSymbolVerticalJustify.BOTTOM\n};\nChordSymbol.glyphs = {\n  diminished: Glyphs.csymDiminished,\n  dim: Glyphs.csymDiminished,\n  halfDiminished: Glyphs.csymHalfDiminished,\n  '+': Glyphs.csymAugmented,\n  augmented: Glyphs.csymAugmented,\n  majorSeventh: Glyphs.csymMajorSeventh,\n  minor: Glyphs.csymMinor,\n  '-': Glyphs.csymMinor,\n  '(': '(',\n  leftParen: '(',\n  ')': ')',\n  rightParen: ')',\n  leftBracket: Glyphs.csymBracketLeftTall,\n  rightBracket: Glyphs.csymBracketRightTall,\n  leftParenTall: '(',\n  rightParenTall: ')',\n  '/': Glyphs.csymDiagonalArrangementSlash,\n  over: Glyphs.csymDiagonalArrangementSlash,\n  '#': Glyphs.csymAccidentalSharp,\n  b: Glyphs.csymAccidentalFlat\n};\nChordSymbol.symbolModifiers = SymbolModifiers;", "map": {"version": 3, "names": ["Element", "Font", "Glyphs", "Metrics", "Modifier", "Tables", "isStemmableNote", "log", "L", "args", "ChordSymbol", "DEBUG", "ChordSymbolBlock", "constructor", "text", "symbolModifier", "xShift", "yShift", "vAlign", "isSuperscript", "SymbolModifiers", "SUPERSCRIPT", "isSubscript", "SUBSCRIPT", "ChordSymbolHorizontalJustify", "ChordSymbolVerticalJustify", "CATEGORY", "superSubRatio", "get", "spacingBetweenBlocks", "superscriptOffset", "subscriptOffset", "minPadding", "format", "symbols", "state", "length", "width", "leftWidth", "rightWidth", "maxLeftGlyphWidth", "maxRightGlyphWidth", "symbol", "note", "checkAttachedNote", "lineSpaces", "j", "symbolBlocks", "block", "sup", "sub", "setXShift", "prev", "getWidth", "getVertical", "TOP", "setTextLine", "topTextLine", "textLine", "getReportWidth", "glyphWidth", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getHorizontal", "RIGHT", "Math", "max", "LEFT", "rightOverlap", "min", "rightShift", "leftOverlap", "leftShift", "horizontal", "vertical", "reportWidth", "convertSizeToPixelValue", "fontInfo", "size", "setReport<PERSON>idth", "value", "getSymbolBlock", "params", "_a", "_b", "symbolBlock", "NONE", "setYShift", "family", "weight", "style", "smallerFontSize", "scaleSize", "setFont", "addSymbolBlock", "parameters", "push", "addText", "Object", "assign", "addTextSuperscript", "addTextSubscript", "addGlyphSuperscript", "glyph", "glyphs", "addGlyph", "addGlyphOrText", "str", "i", "char", "addLine", "setVertical", "vj", "VerticalJustifyString", "setHorizontal", "hj", "HorizontalJustifyString", "draw", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "start", "getModifierStartXY", "Position", "ABOVE", "index", "y", "hasStem", "stave", "checkStave", "BOTTOM", "getYForBottomText", "TEXT_HEIGHT_OFFSET_HACK", "stemExt", "checkStem", "getExtents", "spacing", "getSpacingBetweenLines", "stemBase", "getStemDirection", "baseY", "topY", "getYs", "getYForTopText", "x", "CENTER", "getStemX", "for<PERSON>ach", "getText", "getXShift", "getYShift", "setX", "setY", "renderText", "closeGroup", "getBoundingBox", "boundingBox", "mergeWith", "HorizontalJustify", "left", "right", "center", "centerStem", "CENTER_STEM", "VerticalJustify", "top", "above", "below", "bottom", "diminished", "csymDiminished", "dim", "halfDiminished", "csymHalfDiminished", "csymAugmented", "augmented", "majorSeventh", "csymMajorSeventh", "minor", "csymMinor", "leftParen", "rightParen", "leftBracket", "csymBracketLeftTall", "rightBracket", "csymBracketRightTall", "leftParenTall", "rightParenTall", "csymDiagonalArrangementSlash", "over", "csymAccidentalSharp", "b", "csymAccidentalFlat", "symbolModifiers"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/chordsymbol.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Font } from './font.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Tables } from './tables.js';\nimport { isStemmableNote } from './typeguard.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (ChordSymbol.DEBUG)\n        log('VexFlow.ChordSymbol', args);\n}\nexport class ChordSymbolBlock extends Element {\n    constructor(text, symbolModifier, xShift, yShift, vAlign) {\n        super();\n        this.text = text;\n        this.symbolModifier = symbolModifier;\n        this.xShift = xShift;\n        this.yShift = yShift;\n        this.vAlign = vAlign;\n    }\n    isSuperscript() {\n        return this.symbolModifier === SymbolModifiers.SUPERSCRIPT;\n    }\n    isSubscript() {\n        return this.symbolModifier === SymbolModifiers.SUBSCRIPT;\n    }\n}\nexport var ChordSymbolHorizontalJustify;\n(function (ChordSymbolHorizontalJustify) {\n    ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"LEFT\"] = 1] = \"LEFT\";\n    ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"CENTER\"] = 2] = \"CENTER\";\n    ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"RIGHT\"] = 3] = \"RIGHT\";\n    ChordSymbolHorizontalJustify[ChordSymbolHorizontalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(ChordSymbolHorizontalJustify || (ChordSymbolHorizontalJustify = {}));\nexport var ChordSymbolVerticalJustify;\n(function (ChordSymbolVerticalJustify) {\n    ChordSymbolVerticalJustify[ChordSymbolVerticalJustify[\"TOP\"] = 1] = \"TOP\";\n    ChordSymbolVerticalJustify[ChordSymbolVerticalJustify[\"BOTTOM\"] = 2] = \"BOTTOM\";\n})(ChordSymbolVerticalJustify || (ChordSymbolVerticalJustify = {}));\nexport var SymbolModifiers;\n(function (SymbolModifiers) {\n    SymbolModifiers[SymbolModifiers[\"NONE\"] = 1] = \"NONE\";\n    SymbolModifiers[SymbolModifiers[\"SUBSCRIPT\"] = 2] = \"SUBSCRIPT\";\n    SymbolModifiers[SymbolModifiers[\"SUPERSCRIPT\"] = 3] = \"SUPERSCRIPT\";\n})(SymbolModifiers || (SymbolModifiers = {}));\nexport class ChordSymbol extends Modifier {\n    static get CATEGORY() {\n        return \"ChordSymbol\";\n    }\n    static get superSubRatio() {\n        return Metrics.get('ChordSymbol.superSubRatio');\n    }\n    static get spacingBetweenBlocks() {\n        return Metrics.get('ChordSymbol.spacing');\n    }\n    static get superscriptOffset() {\n        return Metrics.get('ChordSymbol.superscriptOffset');\n    }\n    static get subscriptOffset() {\n        return Metrics.get('ChordSymbol.subscriptOffset');\n    }\n    static get minPadding() {\n        return Metrics.get('NoteHead.minPadding');\n    }\n    static format(symbols, state) {\n        if (!symbols || symbols.length === 0)\n            return false;\n        let width = 0;\n        let leftWidth = 0;\n        let rightWidth = 0;\n        let maxLeftGlyphWidth = 0;\n        let maxRightGlyphWidth = 0;\n        for (const symbol of symbols) {\n            const note = symbol.checkAttachedNote();\n            let lineSpaces = 1;\n            for (let j = 0; j < symbol.symbolBlocks.length; ++j) {\n                const block = symbol.symbolBlocks[j];\n                const sup = block.isSuperscript();\n                const sub = block.isSubscript();\n                block.setXShift(width);\n                if (sup || sub) {\n                    lineSpaces = 2;\n                }\n                if (sub && j > 0) {\n                    const prev = symbol.symbolBlocks[j - 1];\n                    if (prev.isSuperscript()) {\n                        block.setXShift(width - prev.getWidth() - ChordSymbol.minPadding);\n                        block.vAlign = true;\n                        width +=\n                            -prev.getWidth() -\n                                ChordSymbol.minPadding +\n                                (prev.getWidth() > block.getWidth() ? prev.getWidth() - block.getWidth() : 0);\n                    }\n                }\n                width += block.getWidth() + ChordSymbol.minPadding;\n            }\n            if (symbol.getVertical() === ChordSymbolVerticalJustify.TOP) {\n                symbol.setTextLine(state.topTextLine);\n                state.topTextLine += lineSpaces;\n            }\n            else {\n                symbol.setTextLine(state.textLine + 1);\n                state.textLine += lineSpaces + 1;\n            }\n            if (symbol.getReportWidth()) {\n                if (isStemmableNote(note)) {\n                    const glyphWidth = note.getGlyphWidth();\n                    if (symbol.getHorizontal() === ChordSymbolHorizontalJustify.RIGHT) {\n                        maxLeftGlyphWidth = Math.max(glyphWidth, maxLeftGlyphWidth);\n                        leftWidth = Math.max(leftWidth, width) + ChordSymbol.minPadding;\n                    }\n                    else if (symbol.getHorizontal() === ChordSymbolHorizontalJustify.LEFT) {\n                        maxRightGlyphWidth = Math.max(glyphWidth, maxRightGlyphWidth);\n                        rightWidth = Math.max(rightWidth, width);\n                    }\n                    else {\n                        leftWidth = Math.max(leftWidth, width / 2) + ChordSymbol.minPadding;\n                        rightWidth = Math.max(rightWidth, width / 2);\n                        maxLeftGlyphWidth = Math.max(glyphWidth / 2, maxLeftGlyphWidth);\n                        maxRightGlyphWidth = Math.max(glyphWidth / 2, maxRightGlyphWidth);\n                    }\n                }\n                symbol.width = width;\n            }\n            width = 0;\n        }\n        const rightOverlap = Math.min(Math.max(rightWidth - maxRightGlyphWidth, 0), Math.max(rightWidth - state.rightShift, 0));\n        const leftOverlap = Math.min(Math.max(leftWidth - maxLeftGlyphWidth, 0), Math.max(leftWidth - state.leftShift, 0));\n        state.leftShift += leftOverlap;\n        state.rightShift += rightOverlap;\n        return true;\n    }\n    constructor() {\n        super();\n        this.symbolBlocks = [];\n        this.horizontal = ChordSymbolHorizontalJustify.LEFT;\n        this.vertical = ChordSymbolVerticalJustify.TOP;\n        this.reportWidth = true;\n    }\n    get superscriptOffset() {\n        return ChordSymbol.superscriptOffset * Font.convertSizeToPixelValue(this.fontInfo.size);\n    }\n    get subscriptOffset() {\n        return ChordSymbol.subscriptOffset * Font.convertSizeToPixelValue(this.fontInfo.size);\n    }\n    setReportWidth(value) {\n        this.reportWidth = value;\n        return this;\n    }\n    getReportWidth() {\n        return this.reportWidth;\n    }\n    getSymbolBlock(params = {}) {\n        var _a, _b;\n        const symbolBlock = new ChordSymbolBlock((_a = params.text) !== null && _a !== void 0 ? _a : '', (_b = params.symbolModifier) !== null && _b !== void 0 ? _b : SymbolModifiers.NONE, 0, 0, false);\n        if (symbolBlock.isSubscript()) {\n            symbolBlock.setYShift(this.subscriptOffset);\n        }\n        if (symbolBlock.isSuperscript()) {\n            symbolBlock.setYShift(this.superscriptOffset);\n        }\n        if (symbolBlock.isSubscript() || symbolBlock.isSuperscript()) {\n            const { family, size, weight, style } = this.fontInfo;\n            const smallerFontSize = Font.scaleSize(size, ChordSymbol.superSubRatio);\n            symbolBlock.setFont(family, smallerFontSize, weight, style);\n        }\n        else {\n            symbolBlock.setFont(this.fontInfo);\n        }\n        return symbolBlock;\n    }\n    addSymbolBlock(parameters) {\n        this.symbolBlocks.push(this.getSymbolBlock(parameters));\n        return this;\n    }\n    addText(text, parameters = {}) {\n        return this.addSymbolBlock(Object.assign(Object.assign({}, parameters), { text }));\n    }\n    addTextSuperscript(text) {\n        const symbolModifier = SymbolModifiers.SUPERSCRIPT;\n        return this.addSymbolBlock({ text, symbolModifier });\n    }\n    addTextSubscript(text) {\n        const symbolModifier = SymbolModifiers.SUBSCRIPT;\n        return this.addSymbolBlock({ text, symbolModifier });\n    }\n    addGlyphSuperscript(glyph) {\n        return this.addTextSuperscript(ChordSymbol.glyphs[glyph]);\n    }\n    addGlyph(glyph, params = {}) {\n        return this.addText(ChordSymbol.glyphs[glyph], params);\n    }\n    addGlyphOrText(text, params = {}) {\n        let str = '';\n        for (let i = 0; i < text.length; ++i) {\n            const char = text[i];\n            const glyph = ChordSymbol.glyphs[char];\n            if (glyph) {\n                str += glyph;\n            }\n            else {\n                str += char;\n            }\n        }\n        if (str.length > 0) {\n            this.addText(str, params);\n        }\n        return this;\n    }\n    addLine(params = {}) {\n        return this.addText('\\ue874\\ue874', params);\n    }\n    setVertical(vj) {\n        this.vertical = typeof vj === 'string' ? ChordSymbol.VerticalJustifyString[vj] : vj;\n        return this;\n    }\n    getVertical() {\n        return this.vertical;\n    }\n    setHorizontal(hj) {\n        this.horizontal = typeof hj === 'string' ? ChordSymbol.HorizontalJustifyString[hj] : hj;\n        return this;\n    }\n    getHorizontal() {\n        return this.horizontal;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        ctx.openGroup('chordsymbol', this.getAttribute('id'));\n        const start = note.getModifierStartXY(Modifier.Position.ABOVE, this.index);\n        ctx.setFont(this.fontInfo);\n        let y;\n        const hasStem = note.hasStem();\n        const stave = note.checkStave();\n        if (this.vertical === ChordSymbolVerticalJustify.BOTTOM) {\n            y = stave.getYForBottomText(this.textLine + Tables.TEXT_HEIGHT_OFFSET_HACK);\n            if (hasStem) {\n                const stemExt = note.checkStem().getExtents();\n                const spacing = stave.getSpacingBetweenLines();\n                const stemBase = note.getStemDirection() === 1 ? stemExt.baseY : stemExt.topY;\n                y = Math.max(y, stemBase + spacing * (this.textLine + 2));\n            }\n        }\n        else {\n            const topY = Math.min(...note.getYs());\n            y = Math.min(stave.getYForTopText(this.textLine), topY - 10);\n            if (hasStem) {\n                const stemExt = note.checkStem().getExtents();\n                const spacing = stave.getSpacingBetweenLines();\n                y = Math.min(y, stemExt.topY - 5 - spacing * this.textLine);\n            }\n        }\n        let x = start.x;\n        if (this.horizontal === ChordSymbolHorizontalJustify.LEFT) {\n            x = start.x;\n        }\n        else if (this.horizontal === ChordSymbolHorizontalJustify.RIGHT) {\n            x = start.x + this.getWidth();\n        }\n        else if (this.horizontal === ChordSymbolHorizontalJustify.CENTER) {\n            x = start.x - this.getWidth() / 2;\n        }\n        else {\n            x = note.getStemX() - this.getWidth() / 2;\n        }\n        L('Rendering ChordSymbol: ', x, y);\n        this.symbolBlocks.forEach((symbol) => {\n            L('Rendering Text: ', symbol.getText(), x + symbol.getXShift(), y + symbol.getYShift());\n            symbol.setX(x);\n            symbol.setY(y);\n            symbol.renderText(ctx, 0, 0);\n        });\n        ctx.closeGroup();\n    }\n    getBoundingBox() {\n        const boundingBox = this.symbolBlocks[0].getBoundingBox();\n        for (let i = 1; i < this.symbolBlocks.length; i++) {\n            boundingBox.mergeWith(this.symbolBlocks[i].getBoundingBox());\n        }\n        return boundingBox;\n    }\n}\nChordSymbol.DEBUG = false;\nChordSymbol.HorizontalJustify = ChordSymbolHorizontalJustify;\nChordSymbol.HorizontalJustifyString = {\n    left: ChordSymbolHorizontalJustify.LEFT,\n    right: ChordSymbolHorizontalJustify.RIGHT,\n    center: ChordSymbolHorizontalJustify.CENTER,\n    centerStem: ChordSymbolHorizontalJustify.CENTER_STEM,\n};\nChordSymbol.VerticalJustify = ChordSymbolVerticalJustify;\nChordSymbol.VerticalJustifyString = {\n    top: ChordSymbolVerticalJustify.TOP,\n    above: ChordSymbolVerticalJustify.TOP,\n    below: ChordSymbolVerticalJustify.BOTTOM,\n    bottom: ChordSymbolVerticalJustify.BOTTOM,\n};\nChordSymbol.glyphs = {\n    diminished: Glyphs.csymDiminished,\n    dim: Glyphs.csymDiminished,\n    halfDiminished: Glyphs.csymHalfDiminished,\n    '+': Glyphs.csymAugmented,\n    augmented: Glyphs.csymAugmented,\n    majorSeventh: Glyphs.csymMajorSeventh,\n    minor: Glyphs.csymMinor,\n    '-': Glyphs.csymMinor,\n    '(': '(',\n    leftParen: '(',\n    ')': ')',\n    rightParen: ')',\n    leftBracket: Glyphs.csymBracketLeftTall,\n    rightBracket: Glyphs.csymBracketRightTall,\n    leftParenTall: '(',\n    rightParenTall: ')',\n    '/': Glyphs.csymDiagonalArrangementSlash,\n    over: Glyphs.csymDiagonalArrangementSlash,\n    '#': Glyphs.csymAccidentalSharp,\n    b: Glyphs.csymAccidentalFlat,\n};\nChordSymbol.symbolModifiers = SymbolModifiers;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,WAAW,CAACC,KAAK,EACjBJ,GAAG,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AACxC;AACA,OAAO,MAAMG,gBAAgB,SAASZ,OAAO,CAAC;EAC1Ca,WAAWA,CAACC,IAAI,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACtD,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACJ,cAAc,KAAKK,eAAe,CAACC,WAAW;EAC9D;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,cAAc,KAAKK,eAAe,CAACG,SAAS;EAC5D;AACJ;AACA,OAAO,IAAIC,4BAA4B;AACvC,CAAC,UAAUA,4BAA4B,EAAE;EACrCA,4BAA4B,CAACA,4BAA4B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/EA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnFA,4BAA4B,CAACA,4BAA4B,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjFA,4BAA4B,CAACA,4BAA4B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AACjG,CAAC,EAAEA,4BAA4B,KAAKA,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,OAAO,IAAIC,0BAA0B;AACrC,CAAC,UAAUA,0BAA0B,EAAE;EACnCA,0BAA0B,CAACA,0BAA0B,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACzEA,0BAA0B,CAACA,0BAA0B,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACnF,CAAC,EAAEA,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE,OAAO,IAAIL,eAAe;AAC1B,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACrDA,eAAe,CAACA,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC/DA,eAAe,CAACA,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AACvE,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,OAAO,MAAMV,WAAW,SAASN,QAAQ,CAAC;EACtC,WAAWsB,QAAQA,CAAA,EAAG;IAClB,OAAO,aAAa;EACxB;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAOxB,OAAO,CAACyB,GAAG,CAAC,2BAA2B,CAAC;EACnD;EACA,WAAWC,oBAAoBA,CAAA,EAAG;IAC9B,OAAO1B,OAAO,CAACyB,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACA,WAAWE,iBAAiBA,CAAA,EAAG;IAC3B,OAAO3B,OAAO,CAACyB,GAAG,CAAC,+BAA+B,CAAC;EACvD;EACA,WAAWG,eAAeA,CAAA,EAAG;IACzB,OAAO5B,OAAO,CAACyB,GAAG,CAAC,6BAA6B,CAAC;EACrD;EACA,WAAWI,UAAUA,CAAA,EAAG;IACpB,OAAO7B,OAAO,CAACyB,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACA,OAAOK,MAAMA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAC1B,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACE,MAAM,KAAK,CAAC,EAChC,OAAO,KAAK;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,KAAK,MAAMC,MAAM,IAAIR,OAAO,EAAE;MAC1B,MAAMS,IAAI,GAAGD,MAAM,CAACE,iBAAiB,CAAC,CAAC;MACvC,IAAIC,UAAU,GAAG,CAAC;MAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,YAAY,CAACX,MAAM,EAAE,EAAEU,CAAC,EAAE;QACjD,MAAME,KAAK,GAAGN,MAAM,CAACK,YAAY,CAACD,CAAC,CAAC;QACpC,MAAMG,GAAG,GAAGD,KAAK,CAAC7B,aAAa,CAAC,CAAC;QACjC,MAAM+B,GAAG,GAAGF,KAAK,CAAC1B,WAAW,CAAC,CAAC;QAC/B0B,KAAK,CAACG,SAAS,CAACd,KAAK,CAAC;QACtB,IAAIY,GAAG,IAAIC,GAAG,EAAE;UACZL,UAAU,GAAG,CAAC;QAClB;QACA,IAAIK,GAAG,IAAIJ,CAAC,GAAG,CAAC,EAAE;UACd,MAAMM,IAAI,GAAGV,MAAM,CAACK,YAAY,CAACD,CAAC,GAAG,CAAC,CAAC;UACvC,IAAIM,IAAI,CAACjC,aAAa,CAAC,CAAC,EAAE;YACtB6B,KAAK,CAACG,SAAS,CAACd,KAAK,GAAGe,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAG3C,WAAW,CAACsB,UAAU,CAAC;YACjEgB,KAAK,CAAC9B,MAAM,GAAG,IAAI;YACnBmB,KAAK,IACD,CAACe,IAAI,CAACC,QAAQ,CAAC,CAAC,GACZ3C,WAAW,CAACsB,UAAU,IACrBoB,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAGL,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAGD,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAGL,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UACzF;QACJ;QACAhB,KAAK,IAAIW,KAAK,CAACK,QAAQ,CAAC,CAAC,GAAG3C,WAAW,CAACsB,UAAU;MACtD;MACA,IAAIU,MAAM,CAACY,WAAW,CAAC,CAAC,KAAK7B,0BAA0B,CAAC8B,GAAG,EAAE;QACzDb,MAAM,CAACc,WAAW,CAACrB,KAAK,CAACsB,WAAW,CAAC;QACrCtB,KAAK,CAACsB,WAAW,IAAIZ,UAAU;MACnC,CAAC,MACI;QACDH,MAAM,CAACc,WAAW,CAACrB,KAAK,CAACuB,QAAQ,GAAG,CAAC,CAAC;QACtCvB,KAAK,CAACuB,QAAQ,IAAIb,UAAU,GAAG,CAAC;MACpC;MACA,IAAIH,MAAM,CAACiB,cAAc,CAAC,CAAC,EAAE;QACzB,IAAIrD,eAAe,CAACqC,IAAI,CAAC,EAAE;UACvB,MAAMiB,UAAU,GAAGjB,IAAI,CAACkB,aAAa,CAAC,CAAC;UACvC,IAAInB,MAAM,CAACoB,aAAa,CAAC,CAAC,KAAKtC,4BAA4B,CAACuC,KAAK,EAAE;YAC/DvB,iBAAiB,GAAGwB,IAAI,CAACC,GAAG,CAACL,UAAU,EAAEpB,iBAAiB,CAAC;YAC3DF,SAAS,GAAG0B,IAAI,CAACC,GAAG,CAAC3B,SAAS,EAAED,KAAK,CAAC,GAAG3B,WAAW,CAACsB,UAAU;UACnE,CAAC,MACI,IAAIU,MAAM,CAACoB,aAAa,CAAC,CAAC,KAAKtC,4BAA4B,CAAC0C,IAAI,EAAE;YACnEzB,kBAAkB,GAAGuB,IAAI,CAACC,GAAG,CAACL,UAAU,EAAEnB,kBAAkB,CAAC;YAC7DF,UAAU,GAAGyB,IAAI,CAACC,GAAG,CAAC1B,UAAU,EAAEF,KAAK,CAAC;UAC5C,CAAC,MACI;YACDC,SAAS,GAAG0B,IAAI,CAACC,GAAG,CAAC3B,SAAS,EAAED,KAAK,GAAG,CAAC,CAAC,GAAG3B,WAAW,CAACsB,UAAU;YACnEO,UAAU,GAAGyB,IAAI,CAACC,GAAG,CAAC1B,UAAU,EAAEF,KAAK,GAAG,CAAC,CAAC;YAC5CG,iBAAiB,GAAGwB,IAAI,CAACC,GAAG,CAACL,UAAU,GAAG,CAAC,EAAEpB,iBAAiB,CAAC;YAC/DC,kBAAkB,GAAGuB,IAAI,CAACC,GAAG,CAACL,UAAU,GAAG,CAAC,EAAEnB,kBAAkB,CAAC;UACrE;QACJ;QACAC,MAAM,CAACL,KAAK,GAAGA,KAAK;MACxB;MACAA,KAAK,GAAG,CAAC;IACb;IACA,MAAM8B,YAAY,GAAGH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACC,GAAG,CAAC1B,UAAU,GAAGE,kBAAkB,EAAE,CAAC,CAAC,EAAEuB,IAAI,CAACC,GAAG,CAAC1B,UAAU,GAAGJ,KAAK,CAACkC,UAAU,EAAE,CAAC,CAAC,CAAC;IACvH,MAAMC,WAAW,GAAGN,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACC,GAAG,CAAC3B,SAAS,GAAGE,iBAAiB,EAAE,CAAC,CAAC,EAAEwB,IAAI,CAACC,GAAG,CAAC3B,SAAS,GAAGH,KAAK,CAACoC,SAAS,EAAE,CAAC,CAAC,CAAC;IAClHpC,KAAK,CAACoC,SAAS,IAAID,WAAW;IAC9BnC,KAAK,CAACkC,UAAU,IAAIF,YAAY;IAChC,OAAO,IAAI;EACf;EACAtD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACkC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACyB,UAAU,GAAGhD,4BAA4B,CAAC0C,IAAI;IACnD,IAAI,CAACO,QAAQ,GAAGhD,0BAA0B,CAAC8B,GAAG;IAC9C,IAAI,CAACmB,WAAW,GAAG,IAAI;EAC3B;EACA,IAAI5C,iBAAiBA,CAAA,EAAG;IACpB,OAAOpB,WAAW,CAACoB,iBAAiB,GAAG7B,IAAI,CAAC0E,uBAAuB,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;EAC3F;EACA,IAAI9C,eAAeA,CAAA,EAAG;IAClB,OAAOrB,WAAW,CAACqB,eAAe,GAAG9B,IAAI,CAAC0E,uBAAuB,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;EACzF;EACAC,cAAcA,CAACC,KAAK,EAAE;IAClB,IAAI,CAACL,WAAW,GAAGK,KAAK;IACxB,OAAO,IAAI;EACf;EACApB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACe,WAAW;EAC3B;EACAM,cAAcA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACxB,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,WAAW,GAAG,IAAIxE,gBAAgB,CAAC,CAACsE,EAAE,GAAGD,MAAM,CAACnE,IAAI,MAAM,IAAI,IAAIoE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAE,CAACC,EAAE,GAAGF,MAAM,CAAClE,cAAc,MAAM,IAAI,IAAIoE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG/D,eAAe,CAACiE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;IACjM,IAAID,WAAW,CAAC9D,WAAW,CAAC,CAAC,EAAE;MAC3B8D,WAAW,CAACE,SAAS,CAAC,IAAI,CAACvD,eAAe,CAAC;IAC/C;IACA,IAAIqD,WAAW,CAACjE,aAAa,CAAC,CAAC,EAAE;MAC7BiE,WAAW,CAACE,SAAS,CAAC,IAAI,CAACxD,iBAAiB,CAAC;IACjD;IACA,IAAIsD,WAAW,CAAC9D,WAAW,CAAC,CAAC,IAAI8D,WAAW,CAACjE,aAAa,CAAC,CAAC,EAAE;MAC1D,MAAM;QAAEoE,MAAM;QAAEV,IAAI;QAAEW,MAAM;QAAEC;MAAM,CAAC,GAAG,IAAI,CAACb,QAAQ;MACrD,MAAMc,eAAe,GAAGzF,IAAI,CAAC0F,SAAS,CAACd,IAAI,EAAEnE,WAAW,CAACiB,aAAa,CAAC;MACvEyD,WAAW,CAACQ,OAAO,CAACL,MAAM,EAAEG,eAAe,EAAEF,MAAM,EAAEC,KAAK,CAAC;IAC/D,CAAC,MACI;MACDL,WAAW,CAACQ,OAAO,CAAC,IAAI,CAAChB,QAAQ,CAAC;IACtC;IACA,OAAOQ,WAAW;EACtB;EACAS,cAAcA,CAACC,UAAU,EAAE;IACvB,IAAI,CAAC/C,YAAY,CAACgD,IAAI,CAAC,IAAI,CAACf,cAAc,CAACc,UAAU,CAAC,CAAC;IACvD,OAAO,IAAI;EACf;EACAE,OAAOA,CAAClF,IAAI,EAAEgF,UAAU,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACD,cAAc,CAACI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,UAAU,CAAC,EAAE;MAAEhF;IAAK,CAAC,CAAC,CAAC;EACtF;EACAqF,kBAAkBA,CAACrF,IAAI,EAAE;IACrB,MAAMC,cAAc,GAAGK,eAAe,CAACC,WAAW;IAClD,OAAO,IAAI,CAACwE,cAAc,CAAC;MAAE/E,IAAI;MAAEC;IAAe,CAAC,CAAC;EACxD;EACAqF,gBAAgBA,CAACtF,IAAI,EAAE;IACnB,MAAMC,cAAc,GAAGK,eAAe,CAACG,SAAS;IAChD,OAAO,IAAI,CAACsE,cAAc,CAAC;MAAE/E,IAAI;MAAEC;IAAe,CAAC,CAAC;EACxD;EACAsF,mBAAmBA,CAACC,KAAK,EAAE;IACvB,OAAO,IAAI,CAACH,kBAAkB,CAACzF,WAAW,CAAC6F,MAAM,CAACD,KAAK,CAAC,CAAC;EAC7D;EACAE,QAAQA,CAACF,KAAK,EAAErB,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACe,OAAO,CAACtF,WAAW,CAAC6F,MAAM,CAACD,KAAK,CAAC,EAAErB,MAAM,CAAC;EAC1D;EACAwB,cAAcA,CAAC3F,IAAI,EAAEmE,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAIyB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7F,IAAI,CAACsB,MAAM,EAAE,EAAEuE,CAAC,EAAE;MAClC,MAAMC,IAAI,GAAG9F,IAAI,CAAC6F,CAAC,CAAC;MACpB,MAAML,KAAK,GAAG5F,WAAW,CAAC6F,MAAM,CAACK,IAAI,CAAC;MACtC,IAAIN,KAAK,EAAE;QACPI,GAAG,IAAIJ,KAAK;MAChB,CAAC,MACI;QACDI,GAAG,IAAIE,IAAI;MACf;IACJ;IACA,IAAIF,GAAG,CAACtE,MAAM,GAAG,CAAC,EAAE;MAChB,IAAI,CAAC4D,OAAO,CAACU,GAAG,EAAEzB,MAAM,CAAC;IAC7B;IACA,OAAO,IAAI;EACf;EACA4B,OAAOA,CAAC5B,MAAM,GAAG,CAAC,CAAC,EAAE;IACjB,OAAO,IAAI,CAACe,OAAO,CAAC,cAAc,EAAEf,MAAM,CAAC;EAC/C;EACA6B,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAACtC,QAAQ,GAAG,OAAOsC,EAAE,KAAK,QAAQ,GAAGrG,WAAW,CAACsG,qBAAqB,CAACD,EAAE,CAAC,GAAGA,EAAE;IACnF,OAAO,IAAI;EACf;EACAzD,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmB,QAAQ;EACxB;EACAwC,aAAaA,CAACC,EAAE,EAAE;IACd,IAAI,CAAC1C,UAAU,GAAG,OAAO0C,EAAE,KAAK,QAAQ,GAAGxG,WAAW,CAACyG,uBAAuB,CAACD,EAAE,CAAC,GAAGA,EAAE;IACvF,OAAO,IAAI;EACf;EACApD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACU,UAAU;EAC1B;EACA4C,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAM3E,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAAC2E,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,aAAa,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACrD,MAAMC,KAAK,GAAG/E,IAAI,CAACgF,kBAAkB,CAACvH,QAAQ,CAACwH,QAAQ,CAACC,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;IAC1ET,GAAG,CAACzB,OAAO,CAAC,IAAI,CAAChB,QAAQ,CAAC;IAC1B,IAAImD,CAAC;IACL,MAAMC,OAAO,GAAGrF,IAAI,CAACqF,OAAO,CAAC,CAAC;IAC9B,MAAMC,KAAK,GAAGtF,IAAI,CAACuF,UAAU,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACzD,QAAQ,KAAKhD,0BAA0B,CAAC0G,MAAM,EAAE;MACrDJ,CAAC,GAAGE,KAAK,CAACG,iBAAiB,CAAC,IAAI,CAAC1E,QAAQ,GAAGrD,MAAM,CAACgI,uBAAuB,CAAC;MAC3E,IAAIL,OAAO,EAAE;QACT,MAAMM,OAAO,GAAG3F,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;QAC7C,MAAMC,OAAO,GAAGR,KAAK,CAACS,sBAAsB,CAAC,CAAC;QAC9C,MAAMC,QAAQ,GAAGhG,IAAI,CAACiG,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAGN,OAAO,CAACO,KAAK,GAAGP,OAAO,CAACQ,IAAI;QAC7Ef,CAAC,GAAG/D,IAAI,CAACC,GAAG,CAAC8D,CAAC,EAAEY,QAAQ,GAAGF,OAAO,IAAI,IAAI,CAAC/E,QAAQ,GAAG,CAAC,CAAC,CAAC;MAC7D;IACJ,CAAC,MACI;MACD,MAAMoF,IAAI,GAAG9E,IAAI,CAACI,GAAG,CAAC,GAAGzB,IAAI,CAACoG,KAAK,CAAC,CAAC,CAAC;MACtChB,CAAC,GAAG/D,IAAI,CAACI,GAAG,CAAC6D,KAAK,CAACe,cAAc,CAAC,IAAI,CAACtF,QAAQ,CAAC,EAAEoF,IAAI,GAAG,EAAE,CAAC;MAC5D,IAAId,OAAO,EAAE;QACT,MAAMM,OAAO,GAAG3F,IAAI,CAAC4F,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;QAC7C,MAAMC,OAAO,GAAGR,KAAK,CAACS,sBAAsB,CAAC,CAAC;QAC9CX,CAAC,GAAG/D,IAAI,CAACI,GAAG,CAAC2D,CAAC,EAAEO,OAAO,CAACQ,IAAI,GAAG,CAAC,GAAGL,OAAO,GAAG,IAAI,CAAC/E,QAAQ,CAAC;MAC/D;IACJ;IACA,IAAIuF,CAAC,GAAGvB,KAAK,CAACuB,CAAC;IACf,IAAI,IAAI,CAACzE,UAAU,KAAKhD,4BAA4B,CAAC0C,IAAI,EAAE;MACvD+E,CAAC,GAAGvB,KAAK,CAACuB,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAACzE,UAAU,KAAKhD,4BAA4B,CAACuC,KAAK,EAAE;MAC7DkF,CAAC,GAAGvB,KAAK,CAACuB,CAAC,GAAG,IAAI,CAAC5F,QAAQ,CAAC,CAAC;IACjC,CAAC,MACI,IAAI,IAAI,CAACmB,UAAU,KAAKhD,4BAA4B,CAAC0H,MAAM,EAAE;MAC9DD,CAAC,GAAGvB,KAAK,CAACuB,CAAC,GAAG,IAAI,CAAC5F,QAAQ,CAAC,CAAC,GAAG,CAAC;IACrC,CAAC,MACI;MACD4F,CAAC,GAAGtG,IAAI,CAACwG,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC9F,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC7C;IACA7C,CAAC,CAAC,yBAAyB,EAAEyI,CAAC,EAAElB,CAAC,CAAC;IAClC,IAAI,CAAChF,YAAY,CAACqG,OAAO,CAAE1G,MAAM,IAAK;MAClClC,CAAC,CAAC,kBAAkB,EAAEkC,MAAM,CAAC2G,OAAO,CAAC,CAAC,EAAEJ,CAAC,GAAGvG,MAAM,CAAC4G,SAAS,CAAC,CAAC,EAAEvB,CAAC,GAAGrF,MAAM,CAAC6G,SAAS,CAAC,CAAC,CAAC;MACvF7G,MAAM,CAAC8G,IAAI,CAACP,CAAC,CAAC;MACdvG,MAAM,CAAC+G,IAAI,CAAC1B,CAAC,CAAC;MACdrF,MAAM,CAACgH,UAAU,CAACrC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC,CAAC;IACFA,GAAG,CAACsC,UAAU,CAAC,CAAC;EACpB;EACAC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,IAAI,CAAC9G,YAAY,CAAC,CAAC,CAAC,CAAC6G,cAAc,CAAC,CAAC;IACzD,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,YAAY,CAACX,MAAM,EAAEuE,CAAC,EAAE,EAAE;MAC/CkD,WAAW,CAACC,SAAS,CAAC,IAAI,CAAC/G,YAAY,CAAC4D,CAAC,CAAC,CAACiD,cAAc,CAAC,CAAC,CAAC;IAChE;IACA,OAAOC,WAAW;EACtB;AACJ;AACAnJ,WAAW,CAACC,KAAK,GAAG,KAAK;AACzBD,WAAW,CAACqJ,iBAAiB,GAAGvI,4BAA4B;AAC5Dd,WAAW,CAACyG,uBAAuB,GAAG;EAClC6C,IAAI,EAAExI,4BAA4B,CAAC0C,IAAI;EACvC+F,KAAK,EAAEzI,4BAA4B,CAACuC,KAAK;EACzCmG,MAAM,EAAE1I,4BAA4B,CAAC0H,MAAM;EAC3CiB,UAAU,EAAE3I,4BAA4B,CAAC4I;AAC7C,CAAC;AACD1J,WAAW,CAAC2J,eAAe,GAAG5I,0BAA0B;AACxDf,WAAW,CAACsG,qBAAqB,GAAG;EAChCsD,GAAG,EAAE7I,0BAA0B,CAAC8B,GAAG;EACnCgH,KAAK,EAAE9I,0BAA0B,CAAC8B,GAAG;EACrCiH,KAAK,EAAE/I,0BAA0B,CAAC0G,MAAM;EACxCsC,MAAM,EAAEhJ,0BAA0B,CAAC0G;AACvC,CAAC;AACDzH,WAAW,CAAC6F,MAAM,GAAG;EACjBmE,UAAU,EAAExK,MAAM,CAACyK,cAAc;EACjCC,GAAG,EAAE1K,MAAM,CAACyK,cAAc;EAC1BE,cAAc,EAAE3K,MAAM,CAAC4K,kBAAkB;EACzC,GAAG,EAAE5K,MAAM,CAAC6K,aAAa;EACzBC,SAAS,EAAE9K,MAAM,CAAC6K,aAAa;EAC/BE,YAAY,EAAE/K,MAAM,CAACgL,gBAAgB;EACrCC,KAAK,EAAEjL,MAAM,CAACkL,SAAS;EACvB,GAAG,EAAElL,MAAM,CAACkL,SAAS;EACrB,GAAG,EAAE,GAAG;EACRC,SAAS,EAAE,GAAG;EACd,GAAG,EAAE,GAAG;EACRC,UAAU,EAAE,GAAG;EACfC,WAAW,EAAErL,MAAM,CAACsL,mBAAmB;EACvCC,YAAY,EAAEvL,MAAM,CAACwL,oBAAoB;EACzCC,aAAa,EAAE,GAAG;EAClBC,cAAc,EAAE,GAAG;EACnB,GAAG,EAAE1L,MAAM,CAAC2L,4BAA4B;EACxCC,IAAI,EAAE5L,MAAM,CAAC2L,4BAA4B;EACzC,GAAG,EAAE3L,MAAM,CAAC6L,mBAAmB;EAC/BC,CAAC,EAAE9L,MAAM,CAAC+L;AACd,CAAC;AACDvL,WAAW,CAACwL,eAAe,GAAG9K,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}