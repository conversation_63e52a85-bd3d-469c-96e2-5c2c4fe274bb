{"ast": null, "code": "import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nexport class <PERSON>aveT<PERSON>po extends StaveModifier {\n  static get CATEGORY() {\n    return \"StaveTempo\";\n  }\n  constructor(tempo, x, shiftY) {\n    super();\n    this.durationToCode = {\n      '1/4': Glyphs.metNoteDoubleWholeSquare,\n      long: Glyphs.metNoteDoubleWholeSquare,\n      '1/2': Glyphs.metNoteDoubleWhole,\n      breve: Glyphs.metNoteDoubleWhole,\n      1: Glyphs.metNoteWhole,\n      whole: Glyphs.metNoteWhole,\n      w: Glyphs.metNoteWhole,\n      2: Glyphs.metNoteHalfUp,\n      half: Glyphs.metNoteHalfUp,\n      h: Glyphs.metNoteHalfUp,\n      4: Glyphs.metNoteQuarterUp,\n      quarter: Glyphs.metNoteQuarterUp,\n      q: Glyphs.metNoteQuarterUp,\n      8: Glyphs.metNote8thUp,\n      eighth: Glyphs.metNote8thUp,\n      16: Glyphs.metNote16thUp,\n      '16th': Glyphs.metNote16thUp,\n      32: Glyphs.metNote32ndUp,\n      '32nd': Glyphs.metNote32ndUp,\n      64: Glyphs.metNote64thUp,\n      '64th': Glyphs.metNote64thUp,\n      128: Glyphs.metNote128thUp,\n      '128th': Glyphs.metNote128thUp,\n      256: Glyphs.metNote256thUp,\n      '256th': Glyphs.metNote256thUp,\n      512: Glyphs.metNote512thUp,\n      '512th': Glyphs.metNote512thUp,\n      1024: Glyphs.metNote1024thUp,\n      '1024th': Glyphs.metNote1024thUp\n    };\n    this.tempo = tempo;\n    this.position = StaveModifierPosition.ABOVE;\n    this.x = x;\n    this.setXShift(10);\n    this.setYShift(shiftY);\n  }\n  setTempo(tempo) {\n    this.tempo = tempo;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const shiftX = stave.getModifierXShift(this.getPosition());\n    const ctx = stave.checkContext();\n    this.setRendered();\n    const {\n      name,\n      duration,\n      dots,\n      bpm,\n      duration2,\n      dots2,\n      parenthesis\n    } = this.tempo;\n    let x = this.x + shiftX;\n    const y = stave.getYForTopText(1);\n    const el = new Element('StaveTempo.glyph');\n    const elText = new Element('StaveTempo');\n    if (name) {\n      this.text = name;\n      this.fontInfo = Metrics.getFontInfo('StaveTempo.name');\n      this.renderText(ctx, shiftX, y);\n      x += this.getWidth() + 3;\n    }\n    if (name && duration || parenthesis) {\n      elText.setText('(');\n      elText.renderText(ctx, x + this.xShift, y + this.yShift);\n      x += elText.getWidth() + 3;\n    }\n    if (duration) {\n      el.setText(this.durationToCode[duration]);\n      el.renderText(ctx, x + this.xShift, y + this.yShift);\n      x += el.getWidth() + 3;\n      if (dots) {\n        el.setText(Glyphs.metAugmentationDot);\n        for (let i = 0; i < dots; i++) {\n          el.renderText(ctx, x + this.xShift, y + 2 + this.yShift);\n          x += el.getWidth() + 3;\n        }\n      }\n      elText.setText('=');\n      elText.renderText(ctx, x + this.xShift, y + this.yShift);\n      x += elText.getWidth() + 3;\n      if (duration2) {\n        el.setText(this.durationToCode[duration2]);\n        el.renderText(ctx, x + this.xShift, y + this.yShift);\n        x += el.getWidth() + 3;\n        if (dots2) {\n          el.setText(Glyphs.metAugmentationDot);\n          for (let i = 0; i < dots2; i++) {\n            el.renderText(ctx, x + this.xShift, y + 2 + this.yShift);\n            x += el.getWidth() + 3;\n          }\n        }\n      } else if (bpm) {\n        elText.setText('' + bpm);\n        elText.renderText(ctx, x + this.xShift, y + this.yShift);\n        x += elText.getWidth() + 3;\n      }\n      if (name || parenthesis) {\n        elText.setText(')');\n        elText.renderText(ctx, x + this.xShift, y + this.yShift);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["Element", "Glyphs", "Metrics", "StaveModifier", "StaveModifierPosition", "StaveTempo", "CATEGORY", "constructor", "tempo", "x", "shiftY", "durationToCode", "metNoteDoubleWholeSquare", "long", "metNoteDoubleWhole", "breve", "metNoteWhole", "whole", "w", "metNoteHalfUp", "half", "h", "metNoteQuarterUp", "quarter", "q", "metNote8thUp", "eighth", "metNote16thUp", "metNote32ndUp", "metNote64thUp", "metNote128thUp", "metNote256thUp", "metNote512thUp", "metNote1024thUp", "position", "ABOVE", "setXShift", "setYShift", "set<PERSON><PERSON><PERSON>", "draw", "stave", "checkStave", "shiftX", "getModifierXShift", "getPosition", "ctx", "checkContext", "setRendered", "name", "duration", "dots", "bpm", "duration2", "dots2", "parenthesis", "y", "getYForTopText", "el", "elText", "text", "fontInfo", "getFontInfo", "renderText", "getWidth", "setText", "xShift", "yShift", "metAugmentationDot", "i"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavetempo.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nexport class <PERSON>aveT<PERSON>po extends StaveModifier {\n    static get CATEGORY() {\n        return \"StaveTempo\";\n    }\n    constructor(tempo, x, shiftY) {\n        super();\n        this.durationToCode = {\n            '1/4': Glyphs.metNoteDoubleWholeSquare,\n            long: Glyphs.metNoteDoubleWholeSquare,\n            '1/2': Glyphs.metNoteDoubleWhole,\n            breve: Glyphs.metNoteDoubleWhole,\n            1: Glyphs.metNoteWhole,\n            whole: Glyphs.metNoteWhole,\n            w: Glyphs.metNoteWhole,\n            2: Glyphs.metNoteHalfUp,\n            half: Glyphs.metNoteHalfUp,\n            h: Glyphs.metNoteHalfUp,\n            4: Glyphs.metNoteQuarterUp,\n            quarter: Glyphs.metNoteQuarterUp,\n            q: Glyphs.metNoteQuarterUp,\n            8: Glyphs.metNote8thUp,\n            eighth: Glyphs.metNote8thUp,\n            16: Glyphs.metNote16thUp,\n            '16th': Glyphs.metNote16thUp,\n            32: Glyphs.metNote32ndUp,\n            '32nd': Glyphs.metNote32ndUp,\n            64: Glyphs.metNote64thUp,\n            '64th': Glyphs.metNote64thUp,\n            128: Glyphs.metNote128thUp,\n            '128th': Glyphs.metNote128thUp,\n            256: Glyphs.metNote256thUp,\n            '256th': Glyphs.metNote256thUp,\n            512: Glyphs.metNote512thUp,\n            '512th': Glyphs.metNote512thUp,\n            1024: Glyphs.metNote1024thUp,\n            '1024th': Glyphs.metNote1024thUp,\n        };\n        this.tempo = tempo;\n        this.position = StaveModifierPosition.ABOVE;\n        this.x = x;\n        this.setXShift(10);\n        this.setYShift(shiftY);\n    }\n    setTempo(tempo) {\n        this.tempo = tempo;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const shiftX = stave.getModifierXShift(this.getPosition());\n        const ctx = stave.checkContext();\n        this.setRendered();\n        const { name, duration, dots, bpm, duration2, dots2, parenthesis } = this.tempo;\n        let x = this.x + shiftX;\n        const y = stave.getYForTopText(1);\n        const el = new Element('StaveTempo.glyph');\n        const elText = new Element('StaveTempo');\n        if (name) {\n            this.text = name;\n            this.fontInfo = Metrics.getFontInfo('StaveTempo.name');\n            this.renderText(ctx, shiftX, y);\n            x += this.getWidth() + 3;\n        }\n        if ((name && duration) || parenthesis) {\n            elText.setText('(');\n            elText.renderText(ctx, x + this.xShift, y + this.yShift);\n            x += elText.getWidth() + 3;\n        }\n        if (duration) {\n            el.setText(this.durationToCode[duration]);\n            el.renderText(ctx, x + this.xShift, y + this.yShift);\n            x += el.getWidth() + 3;\n            if (dots) {\n                el.setText(Glyphs.metAugmentationDot);\n                for (let i = 0; i < dots; i++) {\n                    el.renderText(ctx, x + this.xShift, y + 2 + this.yShift);\n                    x += el.getWidth() + 3;\n                }\n            }\n            elText.setText('=');\n            elText.renderText(ctx, x + this.xShift, y + this.yShift);\n            x += elText.getWidth() + 3;\n            if (duration2) {\n                el.setText(this.durationToCode[duration2]);\n                el.renderText(ctx, x + this.xShift, y + this.yShift);\n                x += el.getWidth() + 3;\n                if (dots2) {\n                    el.setText(Glyphs.metAugmentationDot);\n                    for (let i = 0; i < dots2; i++) {\n                        el.renderText(ctx, x + this.xShift, y + 2 + this.yShift);\n                        x += el.getWidth() + 3;\n                    }\n                }\n            }\n            else if (bpm) {\n                elText.setText('' + bpm);\n                elText.renderText(ctx, x + this.xShift, y + this.yShift);\n                x += elText.getWidth() + 3;\n            }\n            if (name || parenthesis) {\n                elText.setText(')');\n                elText.renderText(ctx, x + this.xShift, y + this.yShift);\n            }\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,OAAO,MAAMC,UAAU,SAASF,aAAa,CAAC;EAC1C,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACAC,WAAWA,CAACC,KAAK,EAAEC,CAAC,EAAEC,MAAM,EAAE;IAC1B,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,cAAc,GAAG;MAClB,KAAK,EAAEV,MAAM,CAACW,wBAAwB;MACtCC,IAAI,EAAEZ,MAAM,CAACW,wBAAwB;MACrC,KAAK,EAAEX,MAAM,CAACa,kBAAkB;MAChCC,KAAK,EAAEd,MAAM,CAACa,kBAAkB;MAChC,CAAC,EAAEb,MAAM,CAACe,YAAY;MACtBC,KAAK,EAAEhB,MAAM,CAACe,YAAY;MAC1BE,CAAC,EAAEjB,MAAM,CAACe,YAAY;MACtB,CAAC,EAAEf,MAAM,CAACkB,aAAa;MACvBC,IAAI,EAAEnB,MAAM,CAACkB,aAAa;MAC1BE,CAAC,EAAEpB,MAAM,CAACkB,aAAa;MACvB,CAAC,EAAElB,MAAM,CAACqB,gBAAgB;MAC1BC,OAAO,EAAEtB,MAAM,CAACqB,gBAAgB;MAChCE,CAAC,EAAEvB,MAAM,CAACqB,gBAAgB;MAC1B,CAAC,EAAErB,MAAM,CAACwB,YAAY;MACtBC,MAAM,EAAEzB,MAAM,CAACwB,YAAY;MAC3B,EAAE,EAAExB,MAAM,CAAC0B,aAAa;MACxB,MAAM,EAAE1B,MAAM,CAAC0B,aAAa;MAC5B,EAAE,EAAE1B,MAAM,CAAC2B,aAAa;MACxB,MAAM,EAAE3B,MAAM,CAAC2B,aAAa;MAC5B,EAAE,EAAE3B,MAAM,CAAC4B,aAAa;MACxB,MAAM,EAAE5B,MAAM,CAAC4B,aAAa;MAC5B,GAAG,EAAE5B,MAAM,CAAC6B,cAAc;MAC1B,OAAO,EAAE7B,MAAM,CAAC6B,cAAc;MAC9B,GAAG,EAAE7B,MAAM,CAAC8B,cAAc;MAC1B,OAAO,EAAE9B,MAAM,CAAC8B,cAAc;MAC9B,GAAG,EAAE9B,MAAM,CAAC+B,cAAc;MAC1B,OAAO,EAAE/B,MAAM,CAAC+B,cAAc;MAC9B,IAAI,EAAE/B,MAAM,CAACgC,eAAe;MAC5B,QAAQ,EAAEhC,MAAM,CAACgC;IACrB,CAAC;IACD,IAAI,CAACzB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC0B,QAAQ,GAAG9B,qBAAqB,CAAC+B,KAAK;IAC3C,IAAI,CAAC1B,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC2B,SAAS,CAAC,EAAE,CAAC;IAClB,IAAI,CAACC,SAAS,CAAC3B,MAAM,CAAC;EAC1B;EACA4B,QAAQA,CAAC9B,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACA+B,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,MAAM,GAAGF,KAAK,CAACG,iBAAiB,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;IAC1D,MAAMC,GAAG,GAAGL,KAAK,CAACM,YAAY,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAM;MAAEC,IAAI;MAAEC,QAAQ;MAAEC,IAAI;MAAEC,GAAG;MAAEC,SAAS;MAAEC,KAAK;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAC9C,KAAK;IAC/E,IAAIC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGiC,MAAM;IACvB,MAAMa,CAAC,GAAGf,KAAK,CAACgB,cAAc,CAAC,CAAC,CAAC;IACjC,MAAMC,EAAE,GAAG,IAAIzD,OAAO,CAAC,kBAAkB,CAAC;IAC1C,MAAM0D,MAAM,GAAG,IAAI1D,OAAO,CAAC,YAAY,CAAC;IACxC,IAAIgD,IAAI,EAAE;MACN,IAAI,CAACW,IAAI,GAAGX,IAAI;MAChB,IAAI,CAACY,QAAQ,GAAG1D,OAAO,CAAC2D,WAAW,CAAC,iBAAiB,CAAC;MACtD,IAAI,CAACC,UAAU,CAACjB,GAAG,EAAEH,MAAM,EAAEa,CAAC,CAAC;MAC/B9C,CAAC,IAAI,IAAI,CAACsD,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC5B;IACA,IAAKf,IAAI,IAAIC,QAAQ,IAAKK,WAAW,EAAE;MACnCI,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;MACnBN,MAAM,CAACI,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;MACxDzD,CAAC,IAAIiD,MAAM,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC9B;IACA,IAAId,QAAQ,EAAE;MACVQ,EAAE,CAACO,OAAO,CAAC,IAAI,CAACrD,cAAc,CAACsC,QAAQ,CAAC,CAAC;MACzCQ,EAAE,CAACK,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;MACpDzD,CAAC,IAAIgD,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC;MACtB,IAAIb,IAAI,EAAE;QACNO,EAAE,CAACO,OAAO,CAAC/D,MAAM,CAACkE,kBAAkB,CAAC;QACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,IAAI,EAAEkB,CAAC,EAAE,EAAE;UAC3BX,EAAE,CAACK,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;UACxDzD,CAAC,IAAIgD,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC;QAC1B;MACJ;MACAL,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;MACnBN,MAAM,CAACI,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;MACxDzD,CAAC,IAAIiD,MAAM,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;MAC1B,IAAIX,SAAS,EAAE;QACXK,EAAE,CAACO,OAAO,CAAC,IAAI,CAACrD,cAAc,CAACyC,SAAS,CAAC,CAAC;QAC1CK,EAAE,CAACK,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;QACpDzD,CAAC,IAAIgD,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC;QACtB,IAAIV,KAAK,EAAE;UACPI,EAAE,CAACO,OAAO,CAAC/D,MAAM,CAACkE,kBAAkB,CAAC;UACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,EAAEe,CAAC,EAAE,EAAE;YAC5BX,EAAE,CAACK,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;YACxDzD,CAAC,IAAIgD,EAAE,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC;UAC1B;QACJ;MACJ,CAAC,MACI,IAAIZ,GAAG,EAAE;QACVO,MAAM,CAACM,OAAO,CAAC,EAAE,GAAGb,GAAG,CAAC;QACxBO,MAAM,CAACI,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;QACxDzD,CAAC,IAAIiD,MAAM,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC;MAC9B;MACA,IAAIf,IAAI,IAAIM,WAAW,EAAE;QACrBI,MAAM,CAACM,OAAO,CAAC,GAAG,CAAC;QACnBN,MAAM,CAACI,UAAU,CAACjB,GAAG,EAAEpC,CAAC,GAAG,IAAI,CAACwD,MAAM,EAAEV,CAAC,GAAG,IAAI,CAACW,MAAM,CAAC;MAC5D;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}