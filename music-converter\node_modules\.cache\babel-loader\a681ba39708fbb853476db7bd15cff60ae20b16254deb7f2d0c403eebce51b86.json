{"ast": null, "code": "import { Font } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStemmableNote, isTabNote } from './typeguard.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (Annotation.DEBUG) log('VexFlow.Annotation', args);\n}\nexport var AnnotationHorizontalJustify;\n(function (AnnotationHorizontalJustify) {\n  AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"LEFT\"] = 1] = \"LEFT\";\n  AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"CENTER\"] = 2] = \"CENTER\";\n  AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"RIGHT\"] = 3] = \"RIGHT\";\n  AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(AnnotationHorizontalJustify || (AnnotationHorizontalJustify = {}));\nexport var AnnotationVerticalJustify;\n(function (AnnotationVerticalJustify) {\n  AnnotationVerticalJustify[AnnotationVerticalJustify[\"TOP\"] = 1] = \"TOP\";\n  AnnotationVerticalJustify[AnnotationVerticalJustify[\"CENTER\"] = 2] = \"CENTER\";\n  AnnotationVerticalJustify[AnnotationVerticalJustify[\"BOTTOM\"] = 3] = \"BOTTOM\";\n  AnnotationVerticalJustify[AnnotationVerticalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(AnnotationVerticalJustify || (AnnotationVerticalJustify = {}));\nexport class Annotation extends Modifier {\n  static get CATEGORY() {\n    return \"Annotation\";\n  }\n  static get minAnnotationPadding() {\n    return Metrics.get('NoteHead.minPadding');\n  }\n  static format(annotations, state) {\n    if (!annotations || annotations.length === 0) return false;\n    let leftWidth = 0;\n    let rightWidth = 0;\n    let maxLeftGlyphWidth = 0;\n    let maxRightGlyphWidth = 0;\n    for (let i = 0; i < annotations.length; ++i) {\n      const annotation = annotations[i];\n      const textLines = (2 + Font.convertSizeToPixelValue(annotation.fontInfo.size)) / Tables.STAVE_LINE_DISTANCE;\n      let verticalSpaceNeeded = textLines;\n      const note = annotation.checkAttachedNote();\n      const glyphWidth = note.getGlyphWidth();\n      const textWidth = annotation.getWidth();\n      if (annotation.horizontalJustification === AnnotationHorizontalJustify.RIGHT) {\n        maxLeftGlyphWidth = Math.max(glyphWidth, maxLeftGlyphWidth);\n        leftWidth = Math.max(leftWidth, textWidth) + Annotation.minAnnotationPadding;\n      } else if (annotation.horizontalJustification === AnnotationHorizontalJustify.LEFT) {\n        maxRightGlyphWidth = Math.max(glyphWidth, maxRightGlyphWidth);\n        rightWidth = Math.max(rightWidth, textWidth);\n      } else {\n        leftWidth = Math.max(leftWidth, textWidth / 2) + Annotation.minAnnotationPadding;\n        rightWidth = Math.max(rightWidth, textWidth / 2);\n        maxLeftGlyphWidth = Math.max(glyphWidth / 2, maxLeftGlyphWidth);\n        maxRightGlyphWidth = Math.max(glyphWidth / 2, maxRightGlyphWidth);\n      }\n      const stave = note.getStave();\n      const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n      let stemHeight = 0;\n      let lines = 5;\n      if (isTabNote(note)) {\n        if (note.renderOptions.drawStem) {\n          const stem = note.getStem();\n          if (stem) {\n            stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n          }\n        } else {\n          stemHeight = 0;\n        }\n      } else if (isStemmableNote(note)) {\n        const stem = note.getStem();\n        if (stem && note.getNoteType() === 'n') {\n          stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n        }\n      }\n      if (stave) {\n        lines = stave.getNumLines();\n      }\n      if (annotation.verticalJustification === this.VerticalJustify.TOP) {\n        let noteLine = note.getLineNumber(true);\n        if (isTabNote(note)) {\n          noteLine = lines - (note.leastString() - 0.5);\n        }\n        if (stemDirection === Stem.UP) {\n          noteLine += stemHeight;\n        }\n        const curTop = noteLine + state.topTextLine + 0.5;\n        if (curTop < lines) {\n          annotation.setTextLine(lines - noteLine);\n          verticalSpaceNeeded += lines - noteLine;\n          state.topTextLine = verticalSpaceNeeded;\n        } else {\n          annotation.setTextLine(state.topTextLine);\n          state.topTextLine += verticalSpaceNeeded;\n        }\n      } else if (annotation.verticalJustification === this.VerticalJustify.BOTTOM) {\n        let noteLine = lines - note.getLineNumber();\n        if (isTabNote(note)) {\n          noteLine = note.greatestString() - 1;\n        }\n        if (stemDirection === Stem.DOWN) {\n          noteLine += stemHeight;\n        }\n        const curBottom = noteLine + state.textLine + 1;\n        if (curBottom < lines) {\n          annotation.setTextLine(lines - curBottom);\n          verticalSpaceNeeded += lines - curBottom;\n          state.textLine = verticalSpaceNeeded;\n        } else {\n          annotation.setTextLine(state.textLine);\n          state.textLine += verticalSpaceNeeded;\n        }\n      } else {\n        annotation.setTextLine(state.textLine);\n      }\n    }\n    const rightOverlap = Math.min(Math.max(rightWidth - maxRightGlyphWidth, 0), Math.max(rightWidth - state.rightShift, 0));\n    const leftOverlap = Math.min(Math.max(leftWidth - maxLeftGlyphWidth, 0), Math.max(leftWidth - state.leftShift, 0));\n    state.leftShift += leftOverlap;\n    state.rightShift += rightOverlap;\n    return true;\n  }\n  constructor(text) {\n    super();\n    this.text = text;\n    this.horizontalJustification = AnnotationHorizontalJustify.CENTER;\n    this.verticalJustification = AnnotationVerticalJustify.TOP;\n  }\n  setVerticalJustification(just) {\n    this.verticalJustification = typeof just === 'string' ? Annotation.VerticalJustifyString[just] : just;\n    return this;\n  }\n  getJustification() {\n    return this.horizontalJustification;\n  }\n  setJustification(just) {\n    this.horizontalJustification = typeof just === 'string' ? Annotation.HorizontalJustifyString[just] : just;\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n    const start = note.getModifierStartXY(ModifierPosition.ABOVE, this.index);\n    this.setRendered();\n    ctx.openGroup('annotation', this.getAttribute('id'));\n    const textWidth = this.getWidth();\n    const textHeight = Font.convertSizeToPixelValue(this.fontInfo.size);\n    let x;\n    let y;\n    if (this.horizontalJustification === AnnotationHorizontalJustify.LEFT) {\n      x = start.x;\n    } else if (this.horizontalJustification === AnnotationHorizontalJustify.RIGHT) {\n      x = start.x - textWidth;\n    } else if (this.horizontalJustification === AnnotationHorizontalJustify.CENTER) {\n      x = start.x - textWidth / 2;\n    } else {\n      x = note.getStemX() - textWidth / 2;\n    }\n    let stemExt = {};\n    let spacing = 0;\n    const hasStem = note.hasStem();\n    const stave = note.checkStave();\n    if (hasStem) {\n      stemExt = note.checkStem().getExtents();\n      spacing = stave.getSpacingBetweenLines();\n    }\n    if (this.verticalJustification === AnnotationVerticalJustify.BOTTOM) {\n      const ys = note.getYs();\n      y = ys.reduce((a, b) => a > b ? a : b);\n      y += (this.textLine + 1) * Tables.STAVE_LINE_DISTANCE + textHeight;\n      if (hasStem && stemDirection === Stem.DOWN) {\n        y = Math.max(y, stemExt.topY + textHeight + spacing * this.textLine);\n      }\n    } else if (this.verticalJustification === AnnotationVerticalJustify.CENTER) {\n      const yt = note.getYForTopText(this.textLine) - 1;\n      const yb = stave.getYForBottomText(this.textLine);\n      y = yt + (yb - yt) / 2 + textHeight / 2;\n    } else if (this.verticalJustification === AnnotationVerticalJustify.TOP) {\n      const topY = Math.min(...note.getYs());\n      y = topY - (this.textLine + 1) * Tables.STAVE_LINE_DISTANCE;\n      if (hasStem && stemDirection === Stem.UP) {\n        spacing = stemExt.topY < stave.getTopLineTopY() ? Tables.STAVE_LINE_DISTANCE : spacing;\n        y = Math.min(y, stemExt.topY - spacing * (this.textLine + 1));\n      }\n    } else {\n      const extents = note.getStemExtents();\n      y = extents.topY + (extents.baseY - extents.topY) / 2 + textHeight / 2;\n    }\n    L('Rendering annotation: ', this.text, x, y);\n    this.x = x;\n    this.y = y;\n    this.renderText(ctx, 0, 0);\n    ctx.closeGroup();\n  }\n}\nAnnotation.DEBUG = false;\nAnnotation.HorizontalJustify = AnnotationHorizontalJustify;\nAnnotation.HorizontalJustifyString = {\n  left: AnnotationHorizontalJustify.LEFT,\n  right: AnnotationHorizontalJustify.RIGHT,\n  center: AnnotationHorizontalJustify.CENTER,\n  centerStem: AnnotationHorizontalJustify.CENTER_STEM\n};\nAnnotation.VerticalJustify = AnnotationVerticalJustify;\nAnnotation.VerticalJustifyString = {\n  above: AnnotationVerticalJustify.TOP,\n  top: AnnotationVerticalJustify.TOP,\n  below: AnnotationVerticalJustify.BOTTOM,\n  bottom: AnnotationVerticalJustify.BOTTOM,\n  center: AnnotationVerticalJustify.CENTER,\n  centerStem: AnnotationVerticalJustify.CENTER_STEM\n};", "map": {"version": 3, "names": ["Font", "Metrics", "Modifier", "ModifierPosition", "<PERSON><PERSON>", "Tables", "isStemmableNote", "isTabNote", "log", "L", "args", "Annotation", "DEBUG", "AnnotationHorizontalJustify", "AnnotationVerticalJustify", "CATEGORY", "minAnnotationPadding", "get", "format", "annotations", "state", "length", "leftWidth", "rightWidth", "maxLeftGlyphWidth", "maxRightGlyphWidth", "i", "annotation", "textLines", "convertSizeToPixelValue", "fontInfo", "size", "STAVE_LINE_DISTANCE", "verticalSpaceNeeded", "note", "checkAttachedNote", "glyphWidth", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textWidth", "getWidth", "horizontalJustification", "RIGHT", "Math", "max", "LEFT", "stave", "getStave", "stemDirection", "hasStem", "getStemDirection", "UP", "stemHeight", "lines", "renderOptions", "drawStem", "stem", "getStem", "abs", "getHeight", "getNoteType", "getNumLines", "verticalJustification", "VerticalJustify", "TOP", "noteLine", "getLineNumber", "leastString", "curTop", "topTextLine", "setTextLine", "BOTTOM", "greatestString", "DOWN", "cur<PERSON><PERSON><PERSON>", "textLine", "rightOverlap", "min", "rightShift", "leftOverlap", "leftShift", "constructor", "text", "CENTER", "setVerticalJustification", "just", "VerticalJustifyString", "getJustification", "setJustification", "HorizontalJustifyString", "draw", "ctx", "checkContext", "start", "getModifierStartXY", "ABOVE", "index", "setRendered", "openGroup", "getAttribute", "textHeight", "x", "y", "getStemX", "stemExt", "spacing", "checkStave", "checkStem", "getExtents", "getSpacingBetweenLines", "ys", "getYs", "reduce", "a", "b", "topY", "yt", "getYForTopText", "yb", "getYForBottomText", "getTopLineTopY", "extents", "getStemExtents", "baseY", "renderText", "closeGroup", "HorizontalJustify", "left", "right", "center", "centerStem", "CENTER_STEM", "above", "top", "below", "bottom"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/annotation.js"], "sourcesContent": ["import { Font } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { isStemmableNote, isTabNote } from './typeguard.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (Annotation.DEBUG)\n        log('VexFlow.Annotation', args);\n}\nexport var AnnotationHorizontalJustify;\n(function (AnnotationHorizontalJustify) {\n    AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"LEFT\"] = 1] = \"LEFT\";\n    AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"CENTER\"] = 2] = \"CENTER\";\n    AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"RIGHT\"] = 3] = \"RIGHT\";\n    AnnotationHorizontalJustify[AnnotationHorizontalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(AnnotationHorizontalJustify || (AnnotationHorizontalJustify = {}));\nexport var AnnotationVerticalJustify;\n(function (AnnotationVerticalJustify) {\n    AnnotationVerticalJustify[AnnotationVerticalJustify[\"TOP\"] = 1] = \"TOP\";\n    AnnotationVerticalJustify[AnnotationVerticalJustify[\"CENTER\"] = 2] = \"CENTER\";\n    AnnotationVerticalJustify[AnnotationVerticalJustify[\"BOTTOM\"] = 3] = \"BOTTOM\";\n    AnnotationVerticalJustify[AnnotationVerticalJustify[\"CENTER_STEM\"] = 4] = \"CENTER_STEM\";\n})(AnnotationVerticalJustify || (AnnotationVerticalJustify = {}));\nexport class Annotation extends Modifier {\n    static get CATEGORY() {\n        return \"Annotation\";\n    }\n    static get minAnnotationPadding() {\n        return Metrics.get('NoteHead.minPadding');\n    }\n    static format(annotations, state) {\n        if (!annotations || annotations.length === 0)\n            return false;\n        let leftWidth = 0;\n        let rightWidth = 0;\n        let maxLeftGlyphWidth = 0;\n        let maxRightGlyphWidth = 0;\n        for (let i = 0; i < annotations.length; ++i) {\n            const annotation = annotations[i];\n            const textLines = (2 + Font.convertSizeToPixelValue(annotation.fontInfo.size)) / Tables.STAVE_LINE_DISTANCE;\n            let verticalSpaceNeeded = textLines;\n            const note = annotation.checkAttachedNote();\n            const glyphWidth = note.getGlyphWidth();\n            const textWidth = annotation.getWidth();\n            if (annotation.horizontalJustification === AnnotationHorizontalJustify.RIGHT) {\n                maxLeftGlyphWidth = Math.max(glyphWidth, maxLeftGlyphWidth);\n                leftWidth = Math.max(leftWidth, textWidth) + Annotation.minAnnotationPadding;\n            }\n            else if (annotation.horizontalJustification === AnnotationHorizontalJustify.LEFT) {\n                maxRightGlyphWidth = Math.max(glyphWidth, maxRightGlyphWidth);\n                rightWidth = Math.max(rightWidth, textWidth);\n            }\n            else {\n                leftWidth = Math.max(leftWidth, textWidth / 2) + Annotation.minAnnotationPadding;\n                rightWidth = Math.max(rightWidth, textWidth / 2);\n                maxLeftGlyphWidth = Math.max(glyphWidth / 2, maxLeftGlyphWidth);\n                maxRightGlyphWidth = Math.max(glyphWidth / 2, maxRightGlyphWidth);\n            }\n            const stave = note.getStave();\n            const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n            let stemHeight = 0;\n            let lines = 5;\n            if (isTabNote(note)) {\n                if (note.renderOptions.drawStem) {\n                    const stem = note.getStem();\n                    if (stem) {\n                        stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n                    }\n                }\n                else {\n                    stemHeight = 0;\n                }\n            }\n            else if (isStemmableNote(note)) {\n                const stem = note.getStem();\n                if (stem && note.getNoteType() === 'n') {\n                    stemHeight = Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n                }\n            }\n            if (stave) {\n                lines = stave.getNumLines();\n            }\n            if (annotation.verticalJustification === this.VerticalJustify.TOP) {\n                let noteLine = note.getLineNumber(true);\n                if (isTabNote(note)) {\n                    noteLine = lines - (note.leastString() - 0.5);\n                }\n                if (stemDirection === Stem.UP) {\n                    noteLine += stemHeight;\n                }\n                const curTop = noteLine + state.topTextLine + 0.5;\n                if (curTop < lines) {\n                    annotation.setTextLine(lines - noteLine);\n                    verticalSpaceNeeded += lines - noteLine;\n                    state.topTextLine = verticalSpaceNeeded;\n                }\n                else {\n                    annotation.setTextLine(state.topTextLine);\n                    state.topTextLine += verticalSpaceNeeded;\n                }\n            }\n            else if (annotation.verticalJustification === this.VerticalJustify.BOTTOM) {\n                let noteLine = lines - note.getLineNumber();\n                if (isTabNote(note)) {\n                    noteLine = note.greatestString() - 1;\n                }\n                if (stemDirection === Stem.DOWN) {\n                    noteLine += stemHeight;\n                }\n                const curBottom = noteLine + state.textLine + 1;\n                if (curBottom < lines) {\n                    annotation.setTextLine(lines - curBottom);\n                    verticalSpaceNeeded += lines - curBottom;\n                    state.textLine = verticalSpaceNeeded;\n                }\n                else {\n                    annotation.setTextLine(state.textLine);\n                    state.textLine += verticalSpaceNeeded;\n                }\n            }\n            else {\n                annotation.setTextLine(state.textLine);\n            }\n        }\n        const rightOverlap = Math.min(Math.max(rightWidth - maxRightGlyphWidth, 0), Math.max(rightWidth - state.rightShift, 0));\n        const leftOverlap = Math.min(Math.max(leftWidth - maxLeftGlyphWidth, 0), Math.max(leftWidth - state.leftShift, 0));\n        state.leftShift += leftOverlap;\n        state.rightShift += rightOverlap;\n        return true;\n    }\n    constructor(text) {\n        super();\n        this.text = text;\n        this.horizontalJustification = AnnotationHorizontalJustify.CENTER;\n        this.verticalJustification = AnnotationVerticalJustify.TOP;\n    }\n    setVerticalJustification(just) {\n        this.verticalJustification = typeof just === 'string' ? Annotation.VerticalJustifyString[just] : just;\n        return this;\n    }\n    getJustification() {\n        return this.horizontalJustification;\n    }\n    setJustification(just) {\n        this.horizontalJustification = typeof just === 'string' ? Annotation.HorizontalJustifyString[just] : just;\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        const stemDirection = note.hasStem() ? note.getStemDirection() : Stem.UP;\n        const start = note.getModifierStartXY(ModifierPosition.ABOVE, this.index);\n        this.setRendered();\n        ctx.openGroup('annotation', this.getAttribute('id'));\n        const textWidth = this.getWidth();\n        const textHeight = Font.convertSizeToPixelValue(this.fontInfo.size);\n        let x;\n        let y;\n        if (this.horizontalJustification === AnnotationHorizontalJustify.LEFT) {\n            x = start.x;\n        }\n        else if (this.horizontalJustification === AnnotationHorizontalJustify.RIGHT) {\n            x = start.x - textWidth;\n        }\n        else if (this.horizontalJustification === AnnotationHorizontalJustify.CENTER) {\n            x = start.x - textWidth / 2;\n        }\n        else {\n            x = note.getStemX() - textWidth / 2;\n        }\n        let stemExt = {};\n        let spacing = 0;\n        const hasStem = note.hasStem();\n        const stave = note.checkStave();\n        if (hasStem) {\n            stemExt = note.checkStem().getExtents();\n            spacing = stave.getSpacingBetweenLines();\n        }\n        if (this.verticalJustification === AnnotationVerticalJustify.BOTTOM) {\n            const ys = note.getYs();\n            y = ys.reduce((a, b) => (a > b ? a : b));\n            y += (this.textLine + 1) * Tables.STAVE_LINE_DISTANCE + textHeight;\n            if (hasStem && stemDirection === Stem.DOWN) {\n                y = Math.max(y, stemExt.topY + textHeight + spacing * this.textLine);\n            }\n        }\n        else if (this.verticalJustification === AnnotationVerticalJustify.CENTER) {\n            const yt = note.getYForTopText(this.textLine) - 1;\n            const yb = stave.getYForBottomText(this.textLine);\n            y = yt + (yb - yt) / 2 + textHeight / 2;\n        }\n        else if (this.verticalJustification === AnnotationVerticalJustify.TOP) {\n            const topY = Math.min(...note.getYs());\n            y = topY - (this.textLine + 1) * Tables.STAVE_LINE_DISTANCE;\n            if (hasStem && stemDirection === Stem.UP) {\n                spacing = stemExt.topY < stave.getTopLineTopY() ? Tables.STAVE_LINE_DISTANCE : spacing;\n                y = Math.min(y, stemExt.topY - spacing * (this.textLine + 1));\n            }\n        }\n        else {\n            const extents = note.getStemExtents();\n            y = extents.topY + (extents.baseY - extents.topY) / 2 + textHeight / 2;\n        }\n        L('Rendering annotation: ', this.text, x, y);\n        this.x = x;\n        this.y = y;\n        this.renderText(ctx, 0, 0);\n        ctx.closeGroup();\n    }\n}\nAnnotation.DEBUG = false;\nAnnotation.HorizontalJustify = AnnotationHorizontalJustify;\nAnnotation.HorizontalJustifyString = {\n    left: AnnotationHorizontalJustify.LEFT,\n    right: AnnotationHorizontalJustify.RIGHT,\n    center: AnnotationHorizontalJustify.CENTER,\n    centerStem: AnnotationHorizontalJustify.CENTER_STEM,\n};\nAnnotation.VerticalJustify = AnnotationVerticalJustify;\nAnnotation.VerticalJustifyString = {\n    above: AnnotationVerticalJustify.TOP,\n    top: AnnotationVerticalJustify.TOP,\n    below: AnnotationVerticalJustify.BOTTOM,\n    bottom: AnnotationVerticalJustify.BOTTOM,\n    center: AnnotationVerticalJustify.CENTER,\n    centerStem: AnnotationVerticalJustify.CENTER_STEM,\n};\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,eAAe,EAAEC,SAAS,QAAQ,gBAAgB;AAC3D,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,UAAU,CAACC,KAAK,EAChBJ,GAAG,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACvC;AACA,OAAO,IAAIG,2BAA2B;AACtC,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAACA,2BAA2B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7EA,2BAA2B,CAACA,2BAA2B,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACjFA,2BAA2B,CAACA,2BAA2B,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC/EA,2BAA2B,CAACA,2BAA2B,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AAC/F,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,OAAO,IAAIC,yBAAyB;AACpC,CAAC,UAAUA,yBAAyB,EAAE;EAClCA,yBAAyB,CAACA,yBAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACvEA,yBAAyB,CAACA,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC7EA,yBAAyB,CAACA,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EAC7EA,yBAAyB,CAACA,yBAAyB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AAC3F,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE,OAAO,MAAMH,UAAU,SAAST,QAAQ,CAAC;EACrC,WAAWa,QAAQA,CAAA,EAAG;IAClB,OAAO,YAAY;EACvB;EACA,WAAWC,oBAAoBA,CAAA,EAAG;IAC9B,OAAOf,OAAO,CAACgB,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACA,OAAOC,MAAMA,CAACC,WAAW,EAAEC,KAAK,EAAE;IAC9B,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,MAAM,KAAK,CAAC,EACxC,OAAO,KAAK;IAChB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,iBAAiB,GAAG,CAAC;IACzB,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,WAAW,CAACE,MAAM,EAAE,EAAEK,CAAC,EAAE;MACzC,MAAMC,UAAU,GAAGR,WAAW,CAACO,CAAC,CAAC;MACjC,MAAME,SAAS,GAAG,CAAC,CAAC,GAAG5B,IAAI,CAAC6B,uBAAuB,CAACF,UAAU,CAACG,QAAQ,CAACC,IAAI,CAAC,IAAI1B,MAAM,CAAC2B,mBAAmB;MAC3G,IAAIC,mBAAmB,GAAGL,SAAS;MACnC,MAAMM,IAAI,GAAGP,UAAU,CAACQ,iBAAiB,CAAC,CAAC;MAC3C,MAAMC,UAAU,GAAGF,IAAI,CAACG,aAAa,CAAC,CAAC;MACvC,MAAMC,SAAS,GAAGX,UAAU,CAACY,QAAQ,CAAC,CAAC;MACvC,IAAIZ,UAAU,CAACa,uBAAuB,KAAK3B,2BAA2B,CAAC4B,KAAK,EAAE;QAC1EjB,iBAAiB,GAAGkB,IAAI,CAACC,GAAG,CAACP,UAAU,EAAEZ,iBAAiB,CAAC;QAC3DF,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAACrB,SAAS,EAAEgB,SAAS,CAAC,GAAG3B,UAAU,CAACK,oBAAoB;MAChF,CAAC,MACI,IAAIW,UAAU,CAACa,uBAAuB,KAAK3B,2BAA2B,CAAC+B,IAAI,EAAE;QAC9EnB,kBAAkB,GAAGiB,IAAI,CAACC,GAAG,CAACP,UAAU,EAAEX,kBAAkB,CAAC;QAC7DF,UAAU,GAAGmB,IAAI,CAACC,GAAG,CAACpB,UAAU,EAAEe,SAAS,CAAC;MAChD,CAAC,MACI;QACDhB,SAAS,GAAGoB,IAAI,CAACC,GAAG,CAACrB,SAAS,EAAEgB,SAAS,GAAG,CAAC,CAAC,GAAG3B,UAAU,CAACK,oBAAoB;QAChFO,UAAU,GAAGmB,IAAI,CAACC,GAAG,CAACpB,UAAU,EAAEe,SAAS,GAAG,CAAC,CAAC;QAChDd,iBAAiB,GAAGkB,IAAI,CAACC,GAAG,CAACP,UAAU,GAAG,CAAC,EAAEZ,iBAAiB,CAAC;QAC/DC,kBAAkB,GAAGiB,IAAI,CAACC,GAAG,CAACP,UAAU,GAAG,CAAC,EAAEX,kBAAkB,CAAC;MACrE;MACA,MAAMoB,KAAK,GAAGX,IAAI,CAACY,QAAQ,CAAC,CAAC;MAC7B,MAAMC,aAAa,GAAGb,IAAI,CAACc,OAAO,CAAC,CAAC,GAAGd,IAAI,CAACe,gBAAgB,CAAC,CAAC,GAAG7C,IAAI,CAAC8C,EAAE;MACxE,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAIC,KAAK,GAAG,CAAC;MACb,IAAI7C,SAAS,CAAC2B,IAAI,CAAC,EAAE;QACjB,IAAIA,IAAI,CAACmB,aAAa,CAACC,QAAQ,EAAE;UAC7B,MAAMC,IAAI,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;UAC3B,IAAID,IAAI,EAAE;YACNJ,UAAU,GAAGT,IAAI,CAACe,GAAG,CAACF,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGrD,MAAM,CAAC2B,mBAAmB;UACxE;QACJ,CAAC,MACI;UACDmB,UAAU,GAAG,CAAC;QAClB;MACJ,CAAC,MACI,IAAI7C,eAAe,CAAC4B,IAAI,CAAC,EAAE;QAC5B,MAAMqB,IAAI,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;QAC3B,IAAID,IAAI,IAAIrB,IAAI,CAACyB,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;UACpCR,UAAU,GAAGT,IAAI,CAACe,GAAG,CAACF,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGrD,MAAM,CAAC2B,mBAAmB;QACxE;MACJ;MACA,IAAIa,KAAK,EAAE;QACPO,KAAK,GAAGP,KAAK,CAACe,WAAW,CAAC,CAAC;MAC/B;MACA,IAAIjC,UAAU,CAACkC,qBAAqB,KAAK,IAAI,CAACC,eAAe,CAACC,GAAG,EAAE;QAC/D,IAAIC,QAAQ,GAAG9B,IAAI,CAAC+B,aAAa,CAAC,IAAI,CAAC;QACvC,IAAI1D,SAAS,CAAC2B,IAAI,CAAC,EAAE;UACjB8B,QAAQ,GAAGZ,KAAK,IAAIlB,IAAI,CAACgC,WAAW,CAAC,CAAC,GAAG,GAAG,CAAC;QACjD;QACA,IAAInB,aAAa,KAAK3C,IAAI,CAAC8C,EAAE,EAAE;UAC3Bc,QAAQ,IAAIb,UAAU;QAC1B;QACA,MAAMgB,MAAM,GAAGH,QAAQ,GAAG5C,KAAK,CAACgD,WAAW,GAAG,GAAG;QACjD,IAAID,MAAM,GAAGf,KAAK,EAAE;UAChBzB,UAAU,CAAC0C,WAAW,CAACjB,KAAK,GAAGY,QAAQ,CAAC;UACxC/B,mBAAmB,IAAImB,KAAK,GAAGY,QAAQ;UACvC5C,KAAK,CAACgD,WAAW,GAAGnC,mBAAmB;QAC3C,CAAC,MACI;UACDN,UAAU,CAAC0C,WAAW,CAACjD,KAAK,CAACgD,WAAW,CAAC;UACzChD,KAAK,CAACgD,WAAW,IAAInC,mBAAmB;QAC5C;MACJ,CAAC,MACI,IAAIN,UAAU,CAACkC,qBAAqB,KAAK,IAAI,CAACC,eAAe,CAACQ,MAAM,EAAE;QACvE,IAAIN,QAAQ,GAAGZ,KAAK,GAAGlB,IAAI,CAAC+B,aAAa,CAAC,CAAC;QAC3C,IAAI1D,SAAS,CAAC2B,IAAI,CAAC,EAAE;UACjB8B,QAAQ,GAAG9B,IAAI,CAACqC,cAAc,CAAC,CAAC,GAAG,CAAC;QACxC;QACA,IAAIxB,aAAa,KAAK3C,IAAI,CAACoE,IAAI,EAAE;UAC7BR,QAAQ,IAAIb,UAAU;QAC1B;QACA,MAAMsB,SAAS,GAAGT,QAAQ,GAAG5C,KAAK,CAACsD,QAAQ,GAAG,CAAC;QAC/C,IAAID,SAAS,GAAGrB,KAAK,EAAE;UACnBzB,UAAU,CAAC0C,WAAW,CAACjB,KAAK,GAAGqB,SAAS,CAAC;UACzCxC,mBAAmB,IAAImB,KAAK,GAAGqB,SAAS;UACxCrD,KAAK,CAACsD,QAAQ,GAAGzC,mBAAmB;QACxC,CAAC,MACI;UACDN,UAAU,CAAC0C,WAAW,CAACjD,KAAK,CAACsD,QAAQ,CAAC;UACtCtD,KAAK,CAACsD,QAAQ,IAAIzC,mBAAmB;QACzC;MACJ,CAAC,MACI;QACDN,UAAU,CAAC0C,WAAW,CAACjD,KAAK,CAACsD,QAAQ,CAAC;MAC1C;IACJ;IACA,MAAMC,YAAY,GAAGjC,IAAI,CAACkC,GAAG,CAAClC,IAAI,CAACC,GAAG,CAACpB,UAAU,GAAGE,kBAAkB,EAAE,CAAC,CAAC,EAAEiB,IAAI,CAACC,GAAG,CAACpB,UAAU,GAAGH,KAAK,CAACyD,UAAU,EAAE,CAAC,CAAC,CAAC;IACvH,MAAMC,WAAW,GAAGpC,IAAI,CAACkC,GAAG,CAAClC,IAAI,CAACC,GAAG,CAACrB,SAAS,GAAGE,iBAAiB,EAAE,CAAC,CAAC,EAAEkB,IAAI,CAACC,GAAG,CAACrB,SAAS,GAAGF,KAAK,CAAC2D,SAAS,EAAE,CAAC,CAAC,CAAC;IAClH3D,KAAK,CAAC2D,SAAS,IAAID,WAAW;IAC9B1D,KAAK,CAACyD,UAAU,IAAIF,YAAY;IAChC,OAAO,IAAI;EACf;EACAK,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACzC,uBAAuB,GAAG3B,2BAA2B,CAACqE,MAAM;IACjE,IAAI,CAACrB,qBAAqB,GAAG/C,yBAAyB,CAACiD,GAAG;EAC9D;EACAoB,wBAAwBA,CAACC,IAAI,EAAE;IAC3B,IAAI,CAACvB,qBAAqB,GAAG,OAAOuB,IAAI,KAAK,QAAQ,GAAGzE,UAAU,CAAC0E,qBAAqB,CAACD,IAAI,CAAC,GAAGA,IAAI;IACrG,OAAO,IAAI;EACf;EACAE,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC9C,uBAAuB;EACvC;EACA+C,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAI,CAAC5C,uBAAuB,GAAG,OAAO4C,IAAI,KAAK,QAAQ,GAAGzE,UAAU,CAAC6E,uBAAuB,CAACJ,IAAI,CAAC,GAAGA,IAAI;IACzG,OAAO,IAAI;EACf;EACAK,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMzD,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,MAAMY,aAAa,GAAGb,IAAI,CAACc,OAAO,CAAC,CAAC,GAAGd,IAAI,CAACe,gBAAgB,CAAC,CAAC,GAAG7C,IAAI,CAAC8C,EAAE;IACxE,MAAM0C,KAAK,GAAG1D,IAAI,CAAC2D,kBAAkB,CAAC1F,gBAAgB,CAAC2F,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;IACzE,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBN,GAAG,CAACO,SAAS,CAAC,YAAY,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM5D,SAAS,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IACjC,MAAM4D,UAAU,GAAGnG,IAAI,CAAC6B,uBAAuB,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC;IACnE,IAAIqE,CAAC;IACL,IAAIC,CAAC;IACL,IAAI,IAAI,CAAC7D,uBAAuB,KAAK3B,2BAA2B,CAAC+B,IAAI,EAAE;MACnEwD,CAAC,GAAGR,KAAK,CAACQ,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAAC5D,uBAAuB,KAAK3B,2BAA2B,CAAC4B,KAAK,EAAE;MACzE2D,CAAC,GAAGR,KAAK,CAACQ,CAAC,GAAG9D,SAAS;IAC3B,CAAC,MACI,IAAI,IAAI,CAACE,uBAAuB,KAAK3B,2BAA2B,CAACqE,MAAM,EAAE;MAC1EkB,CAAC,GAAGR,KAAK,CAACQ,CAAC,GAAG9D,SAAS,GAAG,CAAC;IAC/B,CAAC,MACI;MACD8D,CAAC,GAAGlE,IAAI,CAACoE,QAAQ,CAAC,CAAC,GAAGhE,SAAS,GAAG,CAAC;IACvC;IACA,IAAIiE,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,OAAO,GAAG,CAAC;IACf,MAAMxD,OAAO,GAAGd,IAAI,CAACc,OAAO,CAAC,CAAC;IAC9B,MAAMH,KAAK,GAAGX,IAAI,CAACuE,UAAU,CAAC,CAAC;IAC/B,IAAIzD,OAAO,EAAE;MACTuD,OAAO,GAAGrE,IAAI,CAACwE,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC;MACvCH,OAAO,GAAG3D,KAAK,CAAC+D,sBAAsB,CAAC,CAAC;IAC5C;IACA,IAAI,IAAI,CAAC/C,qBAAqB,KAAK/C,yBAAyB,CAACwD,MAAM,EAAE;MACjE,MAAMuC,EAAE,GAAG3E,IAAI,CAAC4E,KAAK,CAAC,CAAC;MACvBT,CAAC,GAAGQ,EAAE,CAACE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;MACxCZ,CAAC,IAAI,CAAC,IAAI,CAAC3B,QAAQ,GAAG,CAAC,IAAIrE,MAAM,CAAC2B,mBAAmB,GAAGmE,UAAU;MAClE,IAAInD,OAAO,IAAID,aAAa,KAAK3C,IAAI,CAACoE,IAAI,EAAE;QACxC6B,CAAC,GAAG3D,IAAI,CAACC,GAAG,CAAC0D,CAAC,EAAEE,OAAO,CAACW,IAAI,GAAGf,UAAU,GAAGK,OAAO,GAAG,IAAI,CAAC9B,QAAQ,CAAC;MACxE;IACJ,CAAC,MACI,IAAI,IAAI,CAACb,qBAAqB,KAAK/C,yBAAyB,CAACoE,MAAM,EAAE;MACtE,MAAMiC,EAAE,GAAGjF,IAAI,CAACkF,cAAc,CAAC,IAAI,CAAC1C,QAAQ,CAAC,GAAG,CAAC;MACjD,MAAM2C,EAAE,GAAGxE,KAAK,CAACyE,iBAAiB,CAAC,IAAI,CAAC5C,QAAQ,CAAC;MACjD2B,CAAC,GAAGc,EAAE,GAAG,CAACE,EAAE,GAAGF,EAAE,IAAI,CAAC,GAAGhB,UAAU,GAAG,CAAC;IAC3C,CAAC,MACI,IAAI,IAAI,CAACtC,qBAAqB,KAAK/C,yBAAyB,CAACiD,GAAG,EAAE;MACnE,MAAMmD,IAAI,GAAGxE,IAAI,CAACkC,GAAG,CAAC,GAAG1C,IAAI,CAAC4E,KAAK,CAAC,CAAC,CAAC;MACtCT,CAAC,GAAGa,IAAI,GAAG,CAAC,IAAI,CAACxC,QAAQ,GAAG,CAAC,IAAIrE,MAAM,CAAC2B,mBAAmB;MAC3D,IAAIgB,OAAO,IAAID,aAAa,KAAK3C,IAAI,CAAC8C,EAAE,EAAE;QACtCsD,OAAO,GAAGD,OAAO,CAACW,IAAI,GAAGrE,KAAK,CAAC0E,cAAc,CAAC,CAAC,GAAGlH,MAAM,CAAC2B,mBAAmB,GAAGwE,OAAO;QACtFH,CAAC,GAAG3D,IAAI,CAACkC,GAAG,CAACyB,CAAC,EAAEE,OAAO,CAACW,IAAI,GAAGV,OAAO,IAAI,IAAI,CAAC9B,QAAQ,GAAG,CAAC,CAAC,CAAC;MACjE;IACJ,CAAC,MACI;MACD,MAAM8C,OAAO,GAAGtF,IAAI,CAACuF,cAAc,CAAC,CAAC;MACrCpB,CAAC,GAAGmB,OAAO,CAACN,IAAI,GAAG,CAACM,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACN,IAAI,IAAI,CAAC,GAAGf,UAAU,GAAG,CAAC;IAC1E;IACA1F,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAACwE,IAAI,EAAEmB,CAAC,EAAEC,CAAC,CAAC;IAC5C,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACsB,UAAU,CAACjC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1BA,GAAG,CAACkC,UAAU,CAAC,CAAC;EACpB;AACJ;AACAjH,UAAU,CAACC,KAAK,GAAG,KAAK;AACxBD,UAAU,CAACkH,iBAAiB,GAAGhH,2BAA2B;AAC1DF,UAAU,CAAC6E,uBAAuB,GAAG;EACjCsC,IAAI,EAAEjH,2BAA2B,CAAC+B,IAAI;EACtCmF,KAAK,EAAElH,2BAA2B,CAAC4B,KAAK;EACxCuF,MAAM,EAAEnH,2BAA2B,CAACqE,MAAM;EAC1C+C,UAAU,EAAEpH,2BAA2B,CAACqH;AAC5C,CAAC;AACDvH,UAAU,CAACmD,eAAe,GAAGhD,yBAAyB;AACtDH,UAAU,CAAC0E,qBAAqB,GAAG;EAC/B8C,KAAK,EAAErH,yBAAyB,CAACiD,GAAG;EACpCqE,GAAG,EAAEtH,yBAAyB,CAACiD,GAAG;EAClCsE,KAAK,EAAEvH,yBAAyB,CAACwD,MAAM;EACvCgE,MAAM,EAAExH,yBAAyB,CAACwD,MAAM;EACxC0D,MAAM,EAAElH,yBAAyB,CAACoE,MAAM;EACxC+C,UAAU,EAAEnH,yBAAyB,CAACoH;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}