{"ast": null, "code": "import { Flag } from './flag.js';\nimport { Glyphs } from './glyphs.js';\nimport { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class StemmableNote extends Note {\n  static get CATEGORY() {\n    return \"StemmableNote\";\n  }\n  constructor(noteStruct) {\n    super(noteStruct);\n    this.flag = new Flag();\n  }\n  getStem() {\n    return this.stem;\n  }\n  checkStem() {\n    if (!this.stem) {\n      throw new RuntimeError('NoStem', 'No stem attached to instance');\n    }\n    return this.stem;\n  }\n  setStem(stem) {\n    this.stem = stem;\n    this.addChild(stem);\n    return this;\n  }\n  buildStem() {\n    const stem = new Stem();\n    this.setStem(stem);\n    return this;\n  }\n  buildFlag() {\n    var _a, _b, _c;\n    const {\n      glyphProps\n    } = this;\n    if (this.hasFlag()) {\n      const flagCode = this.getStemDirection() === Stem.DOWN ? String.fromCodePoint(((_b = (_a = glyphProps.codeFlagUp) === null || _a === void 0 ? void 0 : _a.codePointAt(0)) !== null && _b !== void 0 ? _b : -1) + 1) : (_c = glyphProps.codeFlagUp) !== null && _c !== void 0 ? _c : Glyphs.null;\n      this.flag.setText(flagCode);\n      this.flag.fontInfo = this.fontInfo;\n    }\n  }\n  getBaseCustomNoteHeadGlyphProps() {\n    if (this.getStemDirection() === Stem.DOWN) {\n      return this.customGlyphs[this.customGlyphs.length - 1];\n    } else {\n      return this.customGlyphs[0];\n    }\n  }\n  getStemLength() {\n    return Stem.HEIGHT + this.getStemExtension();\n  }\n  getBeamCount() {\n    const glyphProps = this.getGlyphProps();\n    if (glyphProps) {\n      return glyphProps.beamCount;\n    } else {\n      return 0;\n    }\n  }\n  getStemMinimumLength() {\n    const frac = Tables.durationToFraction(this.duration);\n    const beamIsUndefined = this.beam === undefined;\n    let length = frac.value() <= 1 ? 0 : 20;\n    switch (this.duration) {\n      case '8':\n      case '16':\n        length = beamIsUndefined ? 35 : 25;\n        break;\n      case '32':\n        length = beamIsUndefined ? 45 : 35;\n        break;\n      case '64':\n        length = beamIsUndefined ? 50 : 40;\n        break;\n      case '128':\n        length = beamIsUndefined ? 55 : 45;\n        break;\n      default:\n        break;\n    }\n    return length;\n  }\n  getStemDirection() {\n    if (!this.stemDirection) throw new RuntimeError('NoStem', 'No stem attached to this note.');\n    return this.stemDirection;\n  }\n  setStemDirection(direction) {\n    if (!direction) direction = Stem.UP;\n    if (direction !== Stem.UP && direction !== Stem.DOWN) {\n      throw new RuntimeError('BadArgument', `Invalid stem direction: ${direction}`);\n    }\n    this.stemDirection = direction;\n    this.reset();\n    if (this.hasFlag()) {\n      this.buildFlag();\n    }\n    this.beam = undefined;\n    if (this.stem) {\n      this.stem.setDirection(direction);\n      this.stem.setExtension(this.getStemExtension());\n    }\n    if (this.preFormatted) {\n      this.preFormat();\n    }\n    return this;\n  }\n  getStemX() {\n    const xBegin = this.getAbsoluteX() + this.xShift;\n    const xEnd = this.getAbsoluteX() + this.xShift + this.getGlyphWidth();\n    const stemX = this.stemDirection === Stem.DOWN ? xBegin : xEnd;\n    return stemX;\n  }\n  getCenterGlyphX() {\n    return this.getAbsoluteX() + this.xShift + this.getGlyphWidth() / 2;\n  }\n  getStemExtension() {\n    const glyphProps = this.getGlyphProps();\n    const flagHeight = this.flag.getHeight();\n    const scale = this.getFontScale();\n    if (this.stemExtensionOverride !== undefined) {\n      return this.stemExtensionOverride;\n    }\n    if (this.beam) {\n      return glyphProps.stemBeamExtension * scale;\n    }\n    return flagHeight > Stem.HEIGHT * scale ? flagHeight - Stem.HEIGHT * scale : 0;\n  }\n  setStemLength(height) {\n    this.stemExtensionOverride = height - Stem.HEIGHT;\n    return this;\n  }\n  getStemExtents() {\n    if (!this.stem) throw new RuntimeError('NoStem', 'No stem attached to this note.');\n    return this.stem.getExtents();\n  }\n  getYForTopText(textLine) {\n    const stave = this.checkStave();\n    if (this.hasStem()) {\n      const extents = this.getStemExtents();\n      if (!extents) throw new RuntimeError('InvalidState', 'Stem does not have extents.');\n      return Math.min(stave.getYForTopText(textLine), extents.topY - this.renderOptions.annotationSpacing * (textLine + 1));\n    } else {\n      return stave.getYForTopText(textLine);\n    }\n  }\n  getYForBottomText(textLine) {\n    const stave = this.checkStave();\n    if (this.hasStem()) {\n      const extents = this.getStemExtents();\n      if (!extents) throw new RuntimeError('InvalidState', 'Stem does not have extents.');\n      return Math.max(stave.getYForTopText(textLine), extents.baseY + this.renderOptions.annotationSpacing * textLine);\n    } else {\n      return stave.getYForBottomText(textLine);\n    }\n  }\n  hasFlag() {\n    return this.glyphProps.codeFlagUp !== undefined && !this.beam && !this.isRest();\n  }\n  postFormat() {\n    var _a;\n    (_a = this.beam) === null || _a === void 0 ? void 0 : _a.postFormat();\n    this.postFormatted = true;\n    return this;\n  }\n  drawStem(stemOptions) {\n    var _a;\n    this.checkContext();\n    this.setRendered();\n    this.setStem(new Stem(stemOptions));\n    (_a = this.stem) === null || _a === void 0 ? void 0 : _a.setContext(this.getContext()).drawWithStyle();\n  }\n}", "map": {"version": 3, "names": ["Flag", "Glyphs", "Note", "<PERSON><PERSON>", "Tables", "RuntimeError", "StemmableNote", "CATEGORY", "constructor", "noteStruct", "flag", "getStem", "stem", "checkStem", "setStem", "<PERSON><PERSON><PERSON><PERSON>", "buildStem", "buildFlag", "_a", "_b", "_c", "glyphProps", "hasFlag", "flagCode", "getStemDirection", "DOWN", "String", "fromCodePoint", "codeFlagUp", "codePointAt", "null", "setText", "fontInfo", "getBaseCustomNoteHeadGlyphProps", "customGlyphs", "length", "getStemLength", "HEIGHT", "getStemExtension", "getBeamCount", "getGlyphProps", "beamCount", "getStemMinimumLength", "frac", "durationToFraction", "duration", "beamIsUndefined", "beam", "undefined", "value", "stemDirection", "setStemDirection", "direction", "UP", "reset", "setDirection", "setExtension", "preFormatted", "preFormat", "getStemX", "xBegin", "getAbsoluteX", "xShift", "xEnd", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stemX", "getCenterGlyphX", "flagHeight", "getHeight", "scale", "getFontScale", "stemExtensionOverride", "stemBeamExtension", "set<PERSON><PERSON><PERSON>ength", "height", "getStemExtents", "getExtents", "getYForTopText", "textLine", "stave", "checkStave", "hasStem", "extents", "Math", "min", "topY", "renderOptions", "annotationSpacing", "getYForBottomText", "max", "baseY", "isRest", "postFormat", "postFormatted", "drawStem", "stemOptions", "checkContext", "setRendered", "setContext", "getContext", "drawWithStyle"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stemmablenote.js"], "sourcesContent": ["import { Flag } from './flag.js';\nimport { Glyphs } from './glyphs.js';\nimport { Note } from './note.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { RuntimeError } from './util.js';\nexport class StemmableNote extends Note {\n    static get CATEGORY() {\n        return \"StemmableNote\";\n    }\n    constructor(noteStruct) {\n        super(noteStruct);\n        this.flag = new Flag();\n    }\n    getStem() {\n        return this.stem;\n    }\n    checkStem() {\n        if (!this.stem) {\n            throw new RuntimeError('NoStem', 'No stem attached to instance');\n        }\n        return this.stem;\n    }\n    setStem(stem) {\n        this.stem = stem;\n        this.addChild(stem);\n        return this;\n    }\n    buildStem() {\n        const stem = new Stem();\n        this.setStem(stem);\n        return this;\n    }\n    buildFlag() {\n        var _a, _b, _c;\n        const { glyphProps } = this;\n        if (this.hasFlag()) {\n            const flagCode = this.getStemDirection() === Stem.DOWN\n                ? String.fromCodePoint(((_b = (_a = glyphProps.codeFlagUp) === null || _a === void 0 ? void 0 : _a.codePointAt(0)) !== null && _b !== void 0 ? _b : -1) + 1)\n                : ((_c = glyphProps.codeFlagUp) !== null && _c !== void 0 ? _c : Glyphs.null);\n            this.flag.setText(flagCode);\n            this.flag.fontInfo = this.fontInfo;\n        }\n    }\n    getBaseCustomNoteHeadGlyphProps() {\n        if (this.getStemDirection() === Stem.DOWN) {\n            return this.customGlyphs[this.customGlyphs.length - 1];\n        }\n        else {\n            return this.customGlyphs[0];\n        }\n    }\n    getStemLength() {\n        return Stem.HEIGHT + this.getStemExtension();\n    }\n    getBeamCount() {\n        const glyphProps = this.getGlyphProps();\n        if (glyphProps) {\n            return glyphProps.beamCount;\n        }\n        else {\n            return 0;\n        }\n    }\n    getStemMinimumLength() {\n        const frac = Tables.durationToFraction(this.duration);\n        const beamIsUndefined = this.beam === undefined;\n        let length = frac.value() <= 1 ? 0 : 20;\n        switch (this.duration) {\n            case '8':\n            case '16':\n                length = beamIsUndefined ? 35 : 25;\n                break;\n            case '32':\n                length = beamIsUndefined ? 45 : 35;\n                break;\n            case '64':\n                length = beamIsUndefined ? 50 : 40;\n                break;\n            case '128':\n                length = beamIsUndefined ? 55 : 45;\n                break;\n            default:\n                break;\n        }\n        return length;\n    }\n    getStemDirection() {\n        if (!this.stemDirection)\n            throw new RuntimeError('NoStem', 'No stem attached to this note.');\n        return this.stemDirection;\n    }\n    setStemDirection(direction) {\n        if (!direction)\n            direction = Stem.UP;\n        if (direction !== Stem.UP && direction !== Stem.DOWN) {\n            throw new RuntimeError('BadArgument', `Invalid stem direction: ${direction}`);\n        }\n        this.stemDirection = direction;\n        this.reset();\n        if (this.hasFlag()) {\n            this.buildFlag();\n        }\n        this.beam = undefined;\n        if (this.stem) {\n            this.stem.setDirection(direction);\n            this.stem.setExtension(this.getStemExtension());\n        }\n        if (this.preFormatted) {\n            this.preFormat();\n        }\n        return this;\n    }\n    getStemX() {\n        const xBegin = this.getAbsoluteX() + this.xShift;\n        const xEnd = this.getAbsoluteX() + this.xShift + this.getGlyphWidth();\n        const stemX = this.stemDirection === Stem.DOWN ? xBegin : xEnd;\n        return stemX;\n    }\n    getCenterGlyphX() {\n        return this.getAbsoluteX() + this.xShift + this.getGlyphWidth() / 2;\n    }\n    getStemExtension() {\n        const glyphProps = this.getGlyphProps();\n        const flagHeight = this.flag.getHeight();\n        const scale = this.getFontScale();\n        if (this.stemExtensionOverride !== undefined) {\n            return this.stemExtensionOverride;\n        }\n        if (this.beam) {\n            return glyphProps.stemBeamExtension * scale;\n        }\n        return flagHeight > Stem.HEIGHT * scale ? flagHeight - Stem.HEIGHT * scale : 0;\n    }\n    setStemLength(height) {\n        this.stemExtensionOverride = height - Stem.HEIGHT;\n        return this;\n    }\n    getStemExtents() {\n        if (!this.stem)\n            throw new RuntimeError('NoStem', 'No stem attached to this note.');\n        return this.stem.getExtents();\n    }\n    getYForTopText(textLine) {\n        const stave = this.checkStave();\n        if (this.hasStem()) {\n            const extents = this.getStemExtents();\n            if (!extents)\n                throw new RuntimeError('InvalidState', 'Stem does not have extents.');\n            return Math.min(stave.getYForTopText(textLine), extents.topY - this.renderOptions.annotationSpacing * (textLine + 1));\n        }\n        else {\n            return stave.getYForTopText(textLine);\n        }\n    }\n    getYForBottomText(textLine) {\n        const stave = this.checkStave();\n        if (this.hasStem()) {\n            const extents = this.getStemExtents();\n            if (!extents)\n                throw new RuntimeError('InvalidState', 'Stem does not have extents.');\n            return Math.max(stave.getYForTopText(textLine), extents.baseY + this.renderOptions.annotationSpacing * textLine);\n        }\n        else {\n            return stave.getYForBottomText(textLine);\n        }\n    }\n    hasFlag() {\n        return this.glyphProps.codeFlagUp !== undefined && !this.beam && !this.isRest();\n    }\n    postFormat() {\n        var _a;\n        (_a = this.beam) === null || _a === void 0 ? void 0 : _a.postFormat();\n        this.postFormatted = true;\n        return this;\n    }\n    drawStem(stemOptions) {\n        var _a;\n        this.checkContext();\n        this.setRendered();\n        this.setStem(new Stem(stemOptions));\n        (_a = this.stem) === null || _a === void 0 ? void 0 : _a.setContext(this.getContext()).drawWithStyle();\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,aAAa,SAASJ,IAAI,CAAC;EACpC,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,eAAe;EAC1B;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,KAAK,CAACA,UAAU,CAAC;IACjB,IAAI,CAACC,IAAI,GAAG,IAAIV,IAAI,CAAC,CAAC;EAC1B;EACAW,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACD,IAAI,EAAE;MACZ,MAAM,IAAIP,YAAY,CAAC,QAAQ,EAAE,8BAA8B,CAAC;IACpE;IACA,OAAO,IAAI,CAACO,IAAI;EACpB;EACAE,OAAOA,CAACF,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,QAAQ,CAACH,IAAI,CAAC;IACnB,OAAO,IAAI;EACf;EACAI,SAASA,CAAA,EAAG;IACR,MAAMJ,IAAI,GAAG,IAAIT,IAAI,CAAC,CAAC;IACvB,IAAI,CAACW,OAAO,CAACF,IAAI,CAAC;IAClB,OAAO,IAAI;EACf;EACAK,SAASA,CAAA,EAAG;IACR,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,MAAM;MAAEC;IAAW,CAAC,GAAG,IAAI;IAC3B,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MAChB,MAAMC,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,KAAKrB,IAAI,CAACsB,IAAI,GAChDC,MAAM,CAACC,aAAa,CAAC,CAAC,CAACR,EAAE,GAAG,CAACD,EAAE,GAAGG,UAAU,CAACO,UAAU,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,WAAW,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GACzJ,CAACC,EAAE,GAAGC,UAAU,CAACO,UAAU,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGnB,MAAM,CAAC6B,IAAK;MACjF,IAAI,CAACpB,IAAI,CAACqB,OAAO,CAACR,QAAQ,CAAC;MAC3B,IAAI,CAACb,IAAI,CAACsB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACtC;EACJ;EACAC,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACT,gBAAgB,CAAC,CAAC,KAAKrB,IAAI,CAACsB,IAAI,EAAE;MACvC,OAAO,IAAI,CAACS,YAAY,CAAC,IAAI,CAACA,YAAY,CAACC,MAAM,GAAG,CAAC,CAAC;IAC1D,CAAC,MACI;MACD,OAAO,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC;IAC/B;EACJ;EACAE,aAAaA,CAAA,EAAG;IACZ,OAAOjC,IAAI,CAACkC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAChD;EACAC,YAAYA,CAAA,EAAG;IACX,MAAMlB,UAAU,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC;IACvC,IAAInB,UAAU,EAAE;MACZ,OAAOA,UAAU,CAACoB,SAAS;IAC/B,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ;EACAC,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,IAAI,GAAGvC,MAAM,CAACwC,kBAAkB,CAAC,IAAI,CAACC,QAAQ,CAAC;IACrD,MAAMC,eAAe,GAAG,IAAI,CAACC,IAAI,KAAKC,SAAS;IAC/C,IAAIb,MAAM,GAAGQ,IAAI,CAACM,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;IACvC,QAAQ,IAAI,CAACJ,QAAQ;MACjB,KAAK,GAAG;MACR,KAAK,IAAI;QACLV,MAAM,GAAGW,eAAe,GAAG,EAAE,GAAG,EAAE;QAClC;MACJ,KAAK,IAAI;QACLX,MAAM,GAAGW,eAAe,GAAG,EAAE,GAAG,EAAE;QAClC;MACJ,KAAK,IAAI;QACLX,MAAM,GAAGW,eAAe,GAAG,EAAE,GAAG,EAAE;QAClC;MACJ,KAAK,KAAK;QACNX,MAAM,GAAGW,eAAe,GAAG,EAAE,GAAG,EAAE;QAClC;MACJ;QACI;IACR;IACA,OAAOX,MAAM;EACjB;EACAX,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC0B,aAAa,EACnB,MAAM,IAAI7C,YAAY,CAAC,QAAQ,EAAE,gCAAgC,CAAC;IACtE,OAAO,IAAI,CAAC6C,aAAa;EAC7B;EACAC,gBAAgBA,CAACC,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,EACVA,SAAS,GAAGjD,IAAI,CAACkD,EAAE;IACvB,IAAID,SAAS,KAAKjD,IAAI,CAACkD,EAAE,IAAID,SAAS,KAAKjD,IAAI,CAACsB,IAAI,EAAE;MAClD,MAAM,IAAIpB,YAAY,CAAC,aAAa,EAAE,2BAA2B+C,SAAS,EAAE,CAAC;IACjF;IACA,IAAI,CAACF,aAAa,GAAGE,SAAS;IAC9B,IAAI,CAACE,KAAK,CAAC,CAAC;IACZ,IAAI,IAAI,CAAChC,OAAO,CAAC,CAAC,EAAE;MAChB,IAAI,CAACL,SAAS,CAAC,CAAC;IACpB;IACA,IAAI,CAAC8B,IAAI,GAAGC,SAAS;IACrB,IAAI,IAAI,CAACpC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC2C,YAAY,CAACH,SAAS,CAAC;MACjC,IAAI,CAACxC,IAAI,CAAC4C,YAAY,CAAC,IAAI,CAAClB,gBAAgB,CAAC,CAAC,CAAC;IACnD;IACA,IAAI,IAAI,CAACmB,YAAY,EAAE;MACnB,IAAI,CAACC,SAAS,CAAC,CAAC;IACpB;IACA,OAAO,IAAI;EACf;EACAC,QAAQA,CAAA,EAAG;IACP,MAAMC,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM;IAChD,MAAMC,IAAI,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC;IACrE,MAAMC,KAAK,GAAG,IAAI,CAACf,aAAa,KAAK/C,IAAI,CAACsB,IAAI,GAAGmC,MAAM,GAAGG,IAAI;IAC9D,OAAOE,KAAK;EAChB;EACAC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACL,YAAY,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACE,aAAa,CAAC,CAAC,GAAG,CAAC;EACvE;EACA1B,gBAAgBA,CAAA,EAAG;IACf,MAAMjB,UAAU,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC;IACvC,MAAM2B,UAAU,GAAG,IAAI,CAACzD,IAAI,CAAC0D,SAAS,CAAC,CAAC;IACxC,MAAMC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACjC,IAAI,IAAI,CAACC,qBAAqB,KAAKvB,SAAS,EAAE;MAC1C,OAAO,IAAI,CAACuB,qBAAqB;IACrC;IACA,IAAI,IAAI,CAACxB,IAAI,EAAE;MACX,OAAO1B,UAAU,CAACmD,iBAAiB,GAAGH,KAAK;IAC/C;IACA,OAAOF,UAAU,GAAGhE,IAAI,CAACkC,MAAM,GAAGgC,KAAK,GAAGF,UAAU,GAAGhE,IAAI,CAACkC,MAAM,GAAGgC,KAAK,GAAG,CAAC;EAClF;EACAI,aAAaA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACH,qBAAqB,GAAGG,MAAM,GAAGvE,IAAI,CAACkC,MAAM;IACjD,OAAO,IAAI;EACf;EACAsC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC/D,IAAI,EACV,MAAM,IAAIP,YAAY,CAAC,QAAQ,EAAE,gCAAgC,CAAC;IACtE,OAAO,IAAI,CAACO,IAAI,CAACgE,UAAU,CAAC,CAAC;EACjC;EACAC,cAAcA,CAACC,QAAQ,EAAE;IACrB,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MAChB,MAAMC,OAAO,GAAG,IAAI,CAACP,cAAc,CAAC,CAAC;MACrC,IAAI,CAACO,OAAO,EACR,MAAM,IAAI7E,YAAY,CAAC,cAAc,EAAE,6BAA6B,CAAC;MACzE,OAAO8E,IAAI,CAACC,GAAG,CAACL,KAAK,CAACF,cAAc,CAACC,QAAQ,CAAC,EAAEI,OAAO,CAACG,IAAI,GAAG,IAAI,CAACC,aAAa,CAACC,iBAAiB,IAAIT,QAAQ,GAAG,CAAC,CAAC,CAAC;IACzH,CAAC,MACI;MACD,OAAOC,KAAK,CAACF,cAAc,CAACC,QAAQ,CAAC;IACzC;EACJ;EACAU,iBAAiBA,CAACV,QAAQ,EAAE;IACxB,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;MAChB,MAAMC,OAAO,GAAG,IAAI,CAACP,cAAc,CAAC,CAAC;MACrC,IAAI,CAACO,OAAO,EACR,MAAM,IAAI7E,YAAY,CAAC,cAAc,EAAE,6BAA6B,CAAC;MACzE,OAAO8E,IAAI,CAACM,GAAG,CAACV,KAAK,CAACF,cAAc,CAACC,QAAQ,CAAC,EAAEI,OAAO,CAACQ,KAAK,GAAG,IAAI,CAACJ,aAAa,CAACC,iBAAiB,GAAGT,QAAQ,CAAC;IACpH,CAAC,MACI;MACD,OAAOC,KAAK,CAACS,iBAAiB,CAACV,QAAQ,CAAC;IAC5C;EACJ;EACAxD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACD,UAAU,CAACO,UAAU,KAAKoB,SAAS,IAAI,CAAC,IAAI,CAACD,IAAI,IAAI,CAAC,IAAI,CAAC4C,MAAM,CAAC,CAAC;EACnF;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI1E,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC6B,IAAI,MAAM,IAAI,IAAI7B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0E,UAAU,CAAC,CAAC;IACrE,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,OAAO,IAAI;EACf;EACAC,QAAQA,CAACC,WAAW,EAAE;IAClB,IAAI7E,EAAE;IACN,IAAI,CAAC8E,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACnF,OAAO,CAAC,IAAIX,IAAI,CAAC4F,WAAW,CAAC,CAAC;IACnC,CAAC7E,EAAE,GAAG,IAAI,CAACN,IAAI,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgF,UAAU,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,aAAa,CAAC,CAAC;EAC1G;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}