!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):(e=e||self).withSideEffect=t(e.React)}(this,function(e){"use strict";var t="default"in e?e.default:e;function n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var o=!("undefined"==typeof window||!window.document||!window.document.createElement);return function(r,i,c){if("function"!=typeof r)throw new Error("Expected reducePropsToState to be a function.");if("function"!=typeof i)throw new Error("Expected handleStateChangeOnClient to be a function.");if(void 0!==c&&"function"!=typeof c)throw new Error("Expected mapStateOnServer to either be undefined or a function.");return function(u){if("function"!=typeof u)throw new Error("Expected WrappedComponent to be a React component.");var f,a=[];function p(){f=r(a.map(function(e){return e.props})),d.canUseDOM?i(f):c&&(f=c(f))}var d=function(e){var n,o;function r(){return e.apply(this,arguments)||this}o=e,(n=r).prototype=Object.create(o.prototype),n.prototype.constructor=n,n.__proto__=o,r.peek=function(){return f},r.rewind=function(){if(r.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var e=f;return f=void 0,a=[],e};var i=r.prototype;return i.UNSAFE_componentWillMount=function(){a.push(this),p()},i.componentDidUpdate=function(){p()},i.componentWillUnmount=function(){var e=a.indexOf(this);a.splice(e,1),p()},i.render=function(){return t.createElement(u,this.props)},r}(e.PureComponent);return n(d,"displayName","SideEffect("+function(e){return e.displayName||e.name||"Component"}(u)+")"),n(d,"canUseDOM",o),d}}});
