{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Helmet } from 'react-helmet';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  _s();\n  // STYLES\n  const styles = {\n    graphicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'rgba(68, 25, 240, 0.08)',\n      display: 'grid',\n      gridTemplateRows: '1fr auto auto'\n    },\n    musicDisplay: {\n      width: Math.min(window.innerWidth * 0.8, 900),\n      border: '2px solid #9a62e3',\n      borderRadius: '2rem',\n      background: 'white'\n    }\n  };\n\n  // VEXTAB SETUP\n\n  const defaultSetup = 'tabstave notation=true tablature=false';\n  const defaultNotes = ['G/4'];\n  const [key, setKey] = useState('G');\n  const [time, setTime] = useState('4/4');\n  const [notes, setNotes] = useState(defaultNotes);\n  const [rawVex, setRawVex] = useState(`${defaultSetup} key=${key} time=${time}\nnotes ${notes.join(' ')}`);\n\n  // GRAPHIC INPUT CONSTANTS\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  let octave = 4;\n  const expressions = [''];\n\n  // Effect to process VexTab divs after component mounts\n  useEffect(() => {\n    // Wait for the script to load and then process VexTab divs\n    const timer = setTimeout(() => {\n      if (window.VexTab) {\n        window.VexTab.Artist.render();\n      }\n    }, 100);\n    return () => clearTimeout(timer);\n  }, [rawVex]);\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: /*#__PURE__*/_jsxDEV(\"script\", {\n        src: \"https://unpkg.com/vextab/releases/div.prod.js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        style: {\n          border: '2px solid #9a62e3',\n          borderRadius: '2rem',\n          background: 'rgba(120, 25, 240, 0.06)',\n          display: 'flex',\n          flexDirection: 'column',\n          margin: '2rem 4rem',\n          padding: '2rem',\n          gap: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.graphicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'Math.min(window.innerWidth * 0.12, 8rem) 1fr Math.min(window.innerWidth * 0.12, 8rem)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '2px solid red'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"octave\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '2px solid red',\n                display: 'grid',\n                gridTemplateRows: '3rem 1fr'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"note selector\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"current note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                border: '2px solid red'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Accidentals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Duration and Expression\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Add or remove\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: styles.musicDisplay,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"vextab-auto\",\n            width: \"800\",\n            scale: \"1.0\",\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: rawVex,\n            style: {\n              width: Math.min(window.innerWidth * 0.7, 600),\n              margin: '1rem'\n            },\n            onChange: e => setRawVex(e.target.value),\n            children: rawVex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Output\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"vextab-auto\",\n          width: \"800\",\n          scale: \"1.0\",\n          children: rawVex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n}\n_s(Converter, \"PFwVF9NdayGMJBIl4RHHsQX6LfA=\");\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Converter", "_s", "styles", "graphicDisplay", "width", "Math", "min", "window", "innerWidth", "border", "borderRadius", "background", "display", "gridTemplateRows", "musicDisplay", "defaultSetup", "defaultNotes", "key", "<PERSON><PERSON><PERSON>", "time", "setTime", "notes", "setNotes", "rawVex", "setRawVex", "join", "NOTES", "octave", "expressions", "timer", "setTimeout", "VexTab", "Artist", "render", "clearTimeout", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "flexDirection", "margin", "padding", "gap", "gridTemplateColumns", "className", "scale", "value", "onChange", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["\r\nimport React, { useEffect, useState } from 'react';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nfunction Converter() {\r\n\r\n  // STYLES\r\n  const styles = {\r\n    graphicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'rgba(68, 25, 240, 0.08)',\r\n      display: 'grid',\r\n      gridTemplateRows: '1fr auto auto',\r\n    },\r\n    musicDisplay: {\r\n      width: Math.min(window.innerWidth * 0.8, 900),\r\n      border: '2px solid #9a62e3',\r\n      borderRadius: '2rem',\r\n      background: 'white',\r\n    }\r\n  }\r\n\r\n  // VEXTAB SETUP\r\n\r\n  const defaultSetup = 'tabstave notation=true tablature=false';\r\n  const defaultNotes = ['G/4'];\r\n  const [key, setKey] = useState('G');\r\n  const [time, setTime] = useState('4/4');\r\n  const [notes, setNotes] = useState(defaultNotes);\r\n  const [rawVex, setRawVex] = useState(\r\n`${defaultSetup} key=${key} time=${time}\r\nnotes ${notes.join(' ')}`\r\n  );\r\n\r\n\r\n  // GRAPHIC INPUT CONSTANTS\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n  let octave = 4;\r\n  const expressions = ['']\r\n\r\n  // Effect to process VexTab divs after component mounts\r\n  useEffect(() => {\r\n    // Wait for the script to load and then process VexTab divs\r\n    const timer = setTimeout(() => {\r\n      if (window.VexTab) {\r\n        window.VexTab.Artist.render();\r\n      }\r\n    }, 100);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [rawVex]);\r\n\r\n  return (\r\n    <main>\r\n      <Helmet>\r\n        <script src=\"https://unpkg.com/vextab/releases/div.prod.js\"></script>\r\n      </Helmet>\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section style={{\r\n          border: '2px solid #9a62e3',\r\n          borderRadius: '2rem',\r\n          background: 'rgba(120, 25, 240, 0.06)',\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          margin: '2rem 4rem',\r\n          padding: '2rem',\r\n          gap: '2rem',\r\n        }}>\r\n          <h3>Input</h3>\r\n\r\n          {/* ----- Start of Graphical Input Compartment ----- */}\r\n          <div style={styles.graphicDisplay}>\r\n            <div\r\n              style={{\r\n                display: 'grid',\r\n                gridTemplateColumns: 'Math.min(window.innerWidth * 0.12, 8rem) 1fr Math.min(window.innerWidth * 0.12, 8rem)',\r\n              }}\r\n            >\r\n              <div\r\n                style={{\r\n                  border: '2px solid red'\r\n                }}>\r\n                <h3>octave</h3>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  border: '2px solid red',\r\n                  display: 'grid',\r\n                  gridTemplateRows: '3rem 1fr',\r\n                }}\r\n              >\r\n                <div>\r\n                  <h3>note selector</h3>\r\n                </div>\r\n\r\n                <div>\r\n                  <h3>current note</h3>\r\n                </div>\r\n              </div>\r\n\r\n              <div\r\n                style={{\r\n                  border: '2px solid red'\r\n                }}>\r\n                <h3>Accidentals</h3>\r\n              </div>\r\n\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Duration and Expression</h3>\r\n            </div>\r\n\r\n            <div>\r\n              <h3>Add or remove</h3>\r\n            </div>\r\n\r\n          </div>\r\n          {/* ----- End of Graphical Input Compartment ----- */}\r\n\r\n          {/* ----- Start of VexTab Input Compartment ----- */}\r\n          <div style={styles.musicDisplay}>\r\n            <div className=\"vextab-auto\" width=\"800\" scale=\"1.0\">\r\n              {rawVex}\r\n            </div>\r\n            <textarea \r\n              value={rawVex}\r\n              style={{width: Math.min(window.innerWidth * 0.7, 600), margin: '1rem'}}\r\n              onChange={(e) => setRawVex(e.target.value)}\r\n            >\r\n              {rawVex}\r\n            </textarea>\r\n          </div>\r\n          {/* ----- End of VexTab Input Compartment ----- */}\r\n        </section>\r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>Output</h3>\r\n          <div className=\"vextab-auto\" width=\"800\" scale=\"1.0\">\r\n            {rawVex}\r\n          </div>\r\n        </section>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAEnB;EACA,MAAMC,MAAM,GAAG;IACbC,cAAc,EAAE;MACdC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE,yBAAyB;MACrCC,OAAO,EAAE,MAAM;MACfC,gBAAgB,EAAE;IACpB,CAAC;IACDC,YAAY,EAAE;MACZV,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;MAC7CC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,MAAM;MACpBC,UAAU,EAAE;IACd;EACF,CAAC;;EAED;;EAEA,MAAMI,YAAY,GAAG,wCAAwC;EAC7D,MAAMC,YAAY,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGtB,QAAQ,CAAC,GAAG,CAAC;EACnC,MAAM,CAACuB,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAACoB,YAAY,CAAC;EAChD,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CACtC,GAAGmB,YAAY,QAAQE,GAAG,SAASE,IAAI;AACvC,QAAQE,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,EACrB,CAAC;;EAGD;EACA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,IAAIC,MAAM,GAAG,CAAC;EACd,MAAMC,WAAW,GAAG,CAAC,EAAE,CAAC;;EAExB;EACAjC,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,IAAIvB,MAAM,CAACwB,MAAM,EAAE;QACjBxB,MAAM,CAACwB,MAAM,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC;MAC/B;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMC,YAAY,CAACL,KAAK,CAAC;EAClC,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EAEZ,oBACExB,OAAA;IAAAoC,QAAA,gBACEpC,OAAA,CAACF,MAAM;MAAAsC,QAAA,eACLpC,OAAA;QAAQqC,GAAG,EAAC;MAA+C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,eAETzC,OAAA;MAAAoC,QAAA,eACEpC,OAAA;QAAS0C,KAAK,EAAE;UACdhC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,0BAA0B;UACtCC,OAAO,EAAE,MAAM;UACf8B,aAAa,EAAE,QAAQ;UACvBC,MAAM,EAAE,WAAW;UACnBC,OAAO,EAAE,MAAM;UACfC,GAAG,EAAE;QACP,CAAE;QAAAV,QAAA,gBACApC,OAAA;UAAAoC,QAAA,EAAI;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGdzC,OAAA;UAAK0C,KAAK,EAAEvC,MAAM,CAACC,cAAe;UAAAgC,QAAA,gBAChCpC,OAAA;YACE0C,KAAK,EAAE;cACL7B,OAAO,EAAE,MAAM;cACfkC,mBAAmB,EAAE;YACvB,CAAE;YAAAX,QAAA,gBAEFpC,OAAA;cACE0C,KAAK,EAAE;gBACLhC,MAAM,EAAE;cACV,CAAE;cAAA0B,QAAA,eACFpC,OAAA;gBAAAoC,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAENzC,OAAA;cACE0C,KAAK,EAAE;gBACLhC,MAAM,EAAE,eAAe;gBACvBG,OAAO,EAAE,MAAM;gBACfC,gBAAgB,EAAE;cACpB,CAAE;cAAAsB,QAAA,gBAEFpC,OAAA;gBAAAoC,QAAA,eACEpC,OAAA;kBAAAoC,QAAA,EAAI;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eAENzC,OAAA;gBAAAoC,QAAA,eACEpC,OAAA;kBAAAoC,QAAA,EAAI;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzC,OAAA;cACE0C,KAAK,EAAE;gBACLhC,MAAM,EAAE;cACV,CAAE;cAAA0B,QAAA,eACFpC,OAAA;gBAAAoC,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,eAENzC,OAAA;YAAAoC,QAAA,eACEpC,OAAA;cAAAoC,QAAA,EAAI;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAENzC,OAAA;YAAAoC,QAAA,eACEpC,OAAA;cAAAoC,QAAA,EAAI;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC,eAINzC,OAAA;UAAK0C,KAAK,EAAEvC,MAAM,CAACY,YAAa;UAAAqB,QAAA,gBAC9BpC,OAAA;YAAKgD,SAAS,EAAC,aAAa;YAAC3C,KAAK,EAAC,KAAK;YAAC4C,KAAK,EAAC,KAAK;YAAAb,QAAA,EACjDZ;UAAM;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzC,OAAA;YACEkD,KAAK,EAAE1B,MAAO;YACdkB,KAAK,EAAE;cAACrC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE,GAAG,CAAC;cAAEmC,MAAM,EAAE;YAAM,CAAE;YACvEO,QAAQ,EAAGC,CAAC,IAAK3B,SAAS,CAAC2B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAd,QAAA,EAE1CZ;UAAM;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNzC,OAAA;MAAAoC,QAAA,eACEpC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAAoC,QAAA,EAAI;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfzC,OAAA;UAAKgD,SAAS,EAAC,aAAa;UAAC3C,KAAK,EAAC,KAAK;UAAC4C,KAAK,EAAC,KAAK;UAAAb,QAAA,EACjDZ;QAAM;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACvC,EAAA,CArJQD,SAAS;AAAAqD,EAAA,GAATrD,SAAS;AAuJlB,eAAeA,SAAS;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}