{"ast": null, "code": "import { Beam } from './beam.js';\nimport { Fraction } from './fraction.js';\nimport { Metrics } from './metrics.js';\nimport { ModifierContext } from './modifiercontext.js';\nimport { Stave } from './stave.js';\nimport { StaveConnector } from './staveconnector.js';\nimport { Tables } from './tables.js';\nimport { TickContext } from './tickcontext.js';\nimport { isNote, isStaveNote } from './typeguard.js';\nimport { defined, log, midLine, RuntimeError, sumArray } from './util.js';\nimport { Voice } from './voice.js';\nfunction createContexts(voices, makeContext, addToContext) {\n  if (voices.length === 0) return {\n    map: {},\n    array: [],\n    list: [],\n    resolutionMultiplier: 0\n  };\n  const tickToContextMap = {};\n  const tickList = [];\n  const contexts = [];\n  const resolutionMultiplier = Formatter.getResolutionMultiplier(voices);\n  voices.forEach((voice, voiceIndex) => {\n    const ticksUsed = new Fraction(0, resolutionMultiplier);\n    voice.getTickables().forEach(tickable => {\n      const integerTicks = ticksUsed.numerator;\n      if (!tickToContextMap[integerTicks]) {\n        const newContext = makeContext({\n          tickID: integerTicks\n        });\n        contexts.push(newContext);\n        tickToContextMap[integerTicks] = newContext;\n        tickList.push(integerTicks);\n      }\n      addToContext(tickable, tickToContextMap[integerTicks], voiceIndex);\n      ticksUsed.add(tickable.getTicks());\n    });\n  });\n  return {\n    map: tickToContextMap,\n    array: contexts,\n    list: tickList.sort((a, b) => a - b),\n    resolutionMultiplier\n  };\n}\nfunction L(...args) {\n  if (Formatter.DEBUG) log('VexFlow.Formatter', args);\n}\nfunction getRestLineForNextNoteGroup(notes, currRestLine, currNoteIndex, compare) {\n  let nextRestLine = currRestLine;\n  for (let noteIndex = currNoteIndex + 1; noteIndex < notes.length; noteIndex++) {\n    const note = notes[noteIndex];\n    if (isNote(note) && !note.isRest() && !note.shouldIgnoreTicks()) {\n      nextRestLine = note.getLineForRest();\n      break;\n    }\n  }\n  if (compare && currRestLine !== nextRestLine) {\n    const top = Math.max(currRestLine, nextRestLine);\n    const bot = Math.min(currRestLine, nextRestLine);\n    nextRestLine = midLine(top, bot);\n  }\n  return nextRestLine;\n}\nexport class Formatter {\n  static SimpleFormat(notes, x = 0, {\n    paddingBetween = 10\n  } = {}) {\n    notes.reduce((accumulator, note) => {\n      note.addToModifierContext(new ModifierContext());\n      const tickContext = new TickContext().addTickable(note).preFormat();\n      const metrics = tickContext.getMetrics();\n      tickContext.setX(accumulator + metrics.totalLeftPx);\n      return accumulator + tickContext.getWidth() + metrics.totalRightPx + paddingBetween;\n    }, x);\n  }\n  static plotDebugging(ctx, formatter, xPos, y1, y2, options) {\n    var _a, _b;\n    options = Object.assign({\n      stavePadding: Metrics.get('Stave.padding')\n    }, options);\n    const x = xPos + options.stavePadding;\n    const contextGaps = formatter.contextGaps;\n    function stroke(x1, x2, color) {\n      ctx.beginPath();\n      ctx.setStrokeStyle(color);\n      ctx.setFillStyle(color);\n      ctx.setLineWidth(1);\n      ctx.fillRect(x1, y1, Math.max(x2 - x1, 0), y2 - y1);\n    }\n    ctx.save();\n    ctx.setFont(Metrics.get('fontFamily'), 8);\n    contextGaps.gaps.forEach(gap => {\n      stroke(x + gap.x1, x + gap.x2, 'rgba(100,200,100,0.4)');\n      ctx.setFillStyle('green');\n      ctx.fillText(Math.round(gap.x2 - gap.x1).toString(), x + gap.x1, y2 + 12);\n    });\n    ctx.setFillStyle('red');\n    ctx.fillText(`Loss: ${((_a = formatter.totalCost) !== null && _a !== void 0 ? _a : 0).toFixed(2)} Shift: ${((_b = formatter.totalShift) !== null && _b !== void 0 ? _b : 0).toFixed(2)} Gap: ${contextGaps.total.toFixed(2)}`, x - 20, y2 + 27);\n    ctx.restore();\n  }\n  static FormatAndDraw(ctx, stave, notes, params) {\n    let options = {\n      autoBeam: false,\n      alignRests: false\n    };\n    if (typeof params === 'object') {\n      options = Object.assign(Object.assign({}, options), params);\n    } else if (typeof params === 'boolean') {\n      options.autoBeam = params;\n    }\n    const voice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(notes);\n    const beams = options.autoBeam ? Beam.applyAndGetBeams(voice) : [];\n    new Formatter().joinVoices([voice]).formatToStave([voice], stave, {\n      alignRests: options.alignRests,\n      stave\n    });\n    voice.setContext(ctx).setStave(stave).drawWithStyle();\n    beams.forEach(beam => beam.setContext(ctx).drawWithStyle());\n    return voice.getBoundingBox();\n  }\n  static FormatAndDrawTab(ctx, tabstave, stave, tabnotes, notes, autoBeam, params) {\n    let opts = {\n      autoBeam,\n      alignRests: false\n    };\n    if (typeof params === 'object') {\n      opts = Object.assign(Object.assign({}, opts), params);\n    } else if (typeof params === 'boolean') {\n      opts.autoBeam = params;\n    }\n    const notevoice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(notes);\n    const tabvoice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(tabnotes);\n    const beams = opts.autoBeam ? Beam.applyAndGetBeams(notevoice) : [];\n    new Formatter().joinVoices([notevoice]).joinVoices([tabvoice]).formatToStave([notevoice, tabvoice], stave, {\n      alignRests: opts.alignRests\n    });\n    notevoice.draw(ctx, stave);\n    tabvoice.draw(ctx, tabstave);\n    beams.forEach(beam => beam.setContext(ctx).drawWithStyle());\n    new StaveConnector(stave, tabstave).setContext(ctx).drawWithStyle();\n  }\n  static AlignRestsToNotes(tickables, alignAllNotes, alignTuplets) {\n    tickables.forEach((currTickable, index) => {\n      if (isStaveNote(currTickable) && currTickable.isRest()) {\n        if (currTickable.getTuplet() && !alignTuplets) {\n          return;\n        }\n        const line = currTickable.getLineForRest();\n        if (line !== 3) {\n          return;\n        }\n        if (alignAllNotes || currTickable.getBeam()) {\n          const props = currTickable.getKeyProps()[0];\n          if (index === 0) {\n            props.line = getRestLineForNextNoteGroup(tickables, props.line, index, false);\n          } else if (index > 0 && index < tickables.length) {\n            const prevTickable = tickables[index - 1];\n            if (isStaveNote(prevTickable)) {\n              if (prevTickable.isRest()) {\n                props.line = prevTickable.getKeyProps()[0].line;\n              } else {\n                const restLine = prevTickable.getLineForRest();\n                props.line = getRestLineForNextNoteGroup(tickables, restLine, index, true);\n              }\n            }\n          }\n          currTickable.setKeyLine(0, props.line);\n        }\n      }\n    });\n  }\n  constructor(options) {\n    this.formatterOptions = Object.assign({\n      globalSoftmax: false,\n      softmaxFactor: Tables.SOFTMAX_FACTOR,\n      maxIterations: 5\n    }, options);\n    this.justifyWidth = 0;\n    this.totalCost = 0;\n    this.totalShift = 0;\n    this.durationStats = {};\n    this.minTotalWidth = 0;\n    this.hasMinTotalWidth = false;\n    this.tickContexts = {\n      map: {},\n      array: [],\n      list: [],\n      resolutionMultiplier: 0\n    };\n    this.modifierContexts = [];\n    this.contextGaps = {\n      total: 0,\n      gaps: []\n    };\n    this.voices = [];\n    this.lossHistory = [];\n  }\n  alignRests(voices, alignAllNotes) {\n    if (!voices || !voices.length) {\n      throw new RuntimeError('BadArgument', 'No voices to format rests');\n    }\n    voices.forEach(voice => Formatter.AlignRestsToNotes(voice.getTickables(), alignAllNotes));\n  }\n  preCalculateMinTotalWidth(voices) {\n    const unalignedPadding = Metrics.get('Stave.unalignedNotePadding');\n    let unalignedCtxCount = 0;\n    let wsum = 0;\n    let dsum = 0;\n    const widths = [];\n    const durations = [];\n    if (this.hasMinTotalWidth) return this.minTotalWidth;\n    if (!voices) {\n      throw new RuntimeError('BadArgument', \"'voices' required to run preCalculateMinTotalWidth\");\n    }\n    this.createTickContexts(voices);\n    const {\n      list: contextList,\n      map: contextMap\n    } = this.tickContexts;\n    this.minTotalWidth = 0;\n    contextList.forEach(tick => {\n      const context = contextMap[tick];\n      context.preFormat();\n      if (context.getTickables().length < voices.length) {\n        unalignedCtxCount += 1;\n      }\n      context.getTickables().forEach(t => {\n        wsum += t.getMetrics().width;\n        dsum += t.getTicks().value();\n        widths.push(t.getMetrics().width);\n        durations.push(t.getTicks().value());\n      });\n      const width = context.getWidth();\n      this.minTotalWidth += width;\n    });\n    this.hasMinTotalWidth = true;\n    const wavg = wsum > 0 ? wsum / widths.length : 1 / widths.length;\n    const wvar = sumArray(widths.map(ll => Math.pow(ll - wavg, 2)));\n    const wpads = Math.pow(wvar / widths.length, 0.5) / wavg;\n    const davg = dsum / durations.length;\n    const dvar = sumArray(durations.map(ll => Math.pow(ll - davg, 2)));\n    const dpads = Math.pow(dvar / durations.length, 0.5) / davg;\n    const padmax = Math.max(dpads, wpads) * contextList.length * unalignedPadding;\n    const unalignedPad = unalignedPadding * unalignedCtxCount;\n    return this.minTotalWidth + Math.max(unalignedPad, padmax);\n  }\n  getMinTotalWidth() {\n    if (!this.hasMinTotalWidth) {\n      throw new RuntimeError('NoMinTotalWidth', \"Call 'preCalculateMinTotalWidth' or 'preFormat' before calling 'getMinTotalWidth'\");\n    }\n    return this.minTotalWidth;\n  }\n  static getResolutionMultiplier(voices) {\n    if (!voices || !voices.length) {\n      throw new RuntimeError('BadArgument', 'No voices to format');\n    }\n    const totalTicks = voices[0].getTotalTicks();\n    const resolutionMultiplier = voices.reduce((accumulator, voice) => {\n      if (!voice.getTotalTicks().equals(totalTicks)) {\n        throw new RuntimeError('TickMismatch', 'Voices should have same total note duration in ticks.');\n      }\n      if (voice.getMode() === Voice.Mode.STRICT && !voice.isComplete()) {\n        throw new RuntimeError('IncompleteVoice', 'Voice does not have enough notes.');\n      }\n      return Math.max(accumulator, Fraction.LCM(accumulator, voice.getResolutionMultiplier()));\n    }, 1);\n    return resolutionMultiplier;\n  }\n  createModifierContexts(voices) {\n    if (voices.length === 0) return;\n    const resolutionMultiplier = Formatter.getResolutionMultiplier(voices);\n    const tickToContextMap = new Map();\n    const contexts = [];\n    voices.forEach(voice => {\n      const ticksUsed = new Fraction(0, resolutionMultiplier);\n      voice.getTickables().forEach(tickable => {\n        const integerTicks = ticksUsed.numerator;\n        let staveTickToContextMap = tickToContextMap.get(tickable.getStave());\n        if (!staveTickToContextMap) {\n          tickToContextMap.set(tickable.getStave(), {});\n          staveTickToContextMap = tickToContextMap.get(tickable.getStave());\n        }\n        if (!(staveTickToContextMap ? staveTickToContextMap[integerTicks] : undefined)) {\n          const newContext = new ModifierContext();\n          contexts.push(newContext);\n          staveTickToContextMap[integerTicks] = newContext;\n        }\n        tickable.addToModifierContext(staveTickToContextMap[integerTicks]);\n        ticksUsed.add(tickable.getTicks());\n      });\n    });\n    this.modifierContexts.push({\n      map: tickToContextMap,\n      array: contexts,\n      resolutionMultiplier\n    });\n  }\n  createTickContexts(voices) {\n    const fn = (tickable, context, voiceIndex) => context.addTickable(tickable, voiceIndex);\n    const contexts = createContexts(voices, tick => new TickContext(tick), fn);\n    this.tickContexts = contexts;\n    const contextArray = this.tickContexts.array;\n    contextArray.forEach(context => {\n      context.tContexts = contextArray;\n    });\n    return contexts;\n  }\n  getTickContexts() {\n    return this.tickContexts;\n  }\n  preFormat(justifyWidth = 0, renderingContext, voicesParam, stave) {\n    const contexts = this.tickContexts;\n    if (!contexts) {\n      throw new RuntimeError('NoTickContexts', 'preFormat requires TickContexts');\n    }\n    const {\n      list: contextList,\n      map: contextMap\n    } = contexts;\n    this.lossHistory = [];\n    if (voicesParam && stave) {\n      voicesParam.forEach(voice => voice.setStave(stave).preFormat());\n    }\n    let x = 0;\n    let shift = 0;\n    this.minTotalWidth = 0;\n    let totalTicks = 0;\n    contextList.forEach(tick => {\n      const context = contextMap[tick];\n      context.preFormat();\n      const width = context.getWidth();\n      this.minTotalWidth += width;\n      const maxTicks = context.getMaxTicks().value();\n      totalTicks += maxTicks;\n      const metrics = context.getMetrics();\n      x = x + shift + metrics.totalLeftPx;\n      context.setX(x);\n      shift = width - metrics.totalLeftPx;\n    });\n    const {\n      globalSoftmax,\n      softmaxFactor,\n      maxIterations\n    } = this.formatterOptions;\n    const exp = tick => Math.pow(softmaxFactor, contextMap[tick].getMaxTicks().value() / totalTicks);\n    const expTicksUsed = sumArray(contextList.map(exp));\n    this.minTotalWidth = x + shift;\n    this.hasMinTotalWidth = true;\n    if (justifyWidth <= 0) return this.evaluate();\n    const firstContext = contextMap[contextList[0]];\n    const lastContext = contextMap[contextList[contextList.length - 1]];\n    function calculateIdealDistances(adjustedJustifyWidth) {\n      const distances = contextList.map((tick, i) => {\n        const context = contextMap[tick];\n        const voices = context.getTickablesByVoice();\n        let backTickable;\n        if (i > 0) {\n          const prevContext = contextMap[contextList[i - 1]];\n          for (let j = i - 1; j >= 0; j--) {\n            const backTick = contextMap[contextList[j]];\n            const backVoices = backTick.getTickablesByVoice();\n            const matchingVoices = [];\n            Object.keys(voices).forEach(v => {\n              if (backVoices[v]) {\n                matchingVoices.push(v);\n              }\n            });\n            if (matchingVoices.length > 0) {\n              let maxTicks = 0;\n              let maxNegativeShiftPx = Infinity;\n              let expectedDistance = 0;\n              matchingVoices.forEach(v => {\n                const ticks = backVoices[v].getTicks().value();\n                if (ticks > maxTicks) {\n                  backTickable = backVoices[v];\n                  maxTicks = ticks;\n                }\n                const thisTickable = voices[v];\n                const insideLeftEdge = thisTickable.getX() - (thisTickable.getMetrics().modLeftPx + thisTickable.getMetrics().leftDisplacedHeadPx);\n                const backMetrics = backVoices[v].getMetrics();\n                const insideRightEdge = backVoices[v].getX() + backMetrics.notePx + backMetrics.modRightPx + backMetrics.rightDisplacedHeadPx;\n                maxNegativeShiftPx = Math.min(maxNegativeShiftPx, insideLeftEdge - insideRightEdge);\n              });\n              maxNegativeShiftPx = Math.min(maxNegativeShiftPx, context.getX() - (prevContext.getX() + adjustedJustifyWidth * 0.05));\n              if (globalSoftmax) {\n                const t = totalTicks;\n                expectedDistance = Math.pow(softmaxFactor, maxTicks / t) / expTicksUsed * adjustedJustifyWidth;\n              } else if (typeof backTickable !== 'undefined') {\n                expectedDistance = backTickable.getVoice().softmax(maxTicks) * adjustedJustifyWidth;\n              }\n              return {\n                expectedDistance,\n                maxNegativeShiftPx,\n                fromTickable: backTickable\n              };\n            }\n          }\n        }\n        return {\n          expectedDistance: 0,\n          fromTickablePx: 0,\n          maxNegativeShiftPx: 0\n        };\n      });\n      return distances;\n    }\n    function shiftToIdealDistances(idealDistances) {\n      const centerX = adjustedJustifyWidth / 2;\n      let spaceAccum = 0;\n      contextList.forEach((tick, index) => {\n        const context = contextMap[tick];\n        if (index > 0) {\n          const contextX = context.getX();\n          const ideal = idealDistances[index];\n          const errorPx = defined(ideal.fromTickable).getX() + ideal.expectedDistance - (contextX + spaceAccum);\n          let negativeShiftPx = 0;\n          if (errorPx > 0) {\n            spaceAccum += errorPx;\n          } else if (errorPx < 0) {\n            negativeShiftPx = Math.min(ideal.maxNegativeShiftPx, Math.abs(errorPx));\n            spaceAccum += -negativeShiftPx;\n          }\n          context.setX(contextX + spaceAccum);\n        }\n        context.getCenterAlignedTickables().forEach(tickable => {\n          tickable.setCenterXShift(centerX - context.getX());\n        });\n      });\n      return lastContext.getX() - firstContext.getX();\n    }\n    const adjustedJustifyWidth = justifyWidth - lastContext.getMetrics().notePx - lastContext.getMetrics().totalRightPx - firstContext.getMetrics().totalLeftPx;\n    const configMinPadding = Metrics.get('Stave.endPaddingMin');\n    const configMaxPadding = Metrics.get('Stave.endPaddingMax');\n    const leftPadding = Metrics.get('Stave.padding');\n    let targetWidth = adjustedJustifyWidth;\n    const distances = calculateIdealDistances(targetWidth);\n    let actualWidth = shiftToIdealDistances(distances);\n    if (contextList.length === 1) return 0;\n    const calcMinDistance = (targetWidth, distances) => {\n      let mdCalc = targetWidth / 2;\n      if (distances.length > 1) {\n        for (let di = 1; di < distances.length; ++di) {\n          mdCalc = Math.min(distances[di].expectedDistance / 2, mdCalc);\n        }\n      }\n      return mdCalc;\n    };\n    const minDistance = calcMinDistance(targetWidth, distances);\n    const paddingMaxCalc = curTargetWidth => {\n      let lastTickablePadding = 0;\n      const lastTickable = lastContext && lastContext.getMaxTickable();\n      if (lastTickable) {\n        const voice = lastTickable.getVoice();\n        if (voice.getTicksUsed().value() > voice.getTotalTicks().value()) {\n          return configMaxPadding * 2 < minDistance ? minDistance : configMaxPadding;\n        }\n        const tickWidth = lastTickable.getWidth();\n        lastTickablePadding = voice.softmax(lastContext.getMaxTicks().value()) * curTargetWidth - (tickWidth + leftPadding);\n      }\n      return configMaxPadding * 2 < lastTickablePadding ? lastTickablePadding : configMaxPadding;\n    };\n    let paddingMax = paddingMaxCalc(targetWidth);\n    let paddingMin = paddingMax - (configMaxPadding - configMinPadding);\n    const maxX = adjustedJustifyWidth - paddingMin;\n    let iterations = maxIterations;\n    while (actualWidth > maxX && iterations > 0 || actualWidth + paddingMax < maxX && iterations > 1) {\n      targetWidth -= actualWidth - maxX;\n      paddingMax = paddingMaxCalc(targetWidth);\n      paddingMin = paddingMax - (configMaxPadding - configMinPadding);\n      actualWidth = shiftToIdealDistances(calculateIdealDistances(targetWidth));\n      iterations--;\n    }\n    this.justifyWidth = justifyWidth;\n    return this.evaluate();\n  }\n  evaluate() {\n    const contexts = this.tickContexts;\n    const justifyWidth = this.justifyWidth;\n    this.contextGaps = {\n      total: 0,\n      gaps: []\n    };\n    contexts.list.forEach((tick, index) => {\n      if (index === 0) return;\n      const prevTick = contexts.list[index - 1];\n      const prevContext = contexts.map[prevTick];\n      const context = contexts.map[tick];\n      const prevMetrics = prevContext.getMetrics();\n      const currMetrics = context.getMetrics();\n      const insideRightEdge = prevContext.getX() + prevMetrics.notePx + prevMetrics.totalRightPx;\n      const insideLeftEdge = context.getX() - currMetrics.totalLeftPx;\n      const gap = insideLeftEdge - insideRightEdge;\n      this.contextGaps.total += gap;\n      this.contextGaps.gaps.push({\n        x1: insideRightEdge,\n        x2: insideLeftEdge\n      });\n      context.getFormatterMetrics().freedom.left = gap;\n      prevContext.getFormatterMetrics().freedom.right = gap;\n    });\n    this.durationStats = {};\n    const durationStats = this.durationStats;\n    function updateStats(duration, space) {\n      const stats = durationStats[duration];\n      if (stats === undefined) {\n        durationStats[duration] = {\n          mean: space,\n          count: 1,\n          total: space\n        };\n      } else {\n        stats.count += 1;\n        stats.total += space;\n        stats.mean = stats.total / stats.count;\n      }\n    }\n    this.voices.forEach(voice => {\n      voice.getTickables().forEach((note, i, notes) => {\n        const duration = note.getTicks().clone().simplify().toString();\n        const metrics = note.getMetrics();\n        const formatterMetrics = note.getFormatterMetrics();\n        const leftNoteEdge = note.getX() + metrics.notePx + metrics.modRightPx + metrics.rightDisplacedHeadPx;\n        let space = 0;\n        if (i < notes.length - 1) {\n          const rightNote = notes[i + 1];\n          const rightMetrics = rightNote.getMetrics();\n          const rightNoteEdge = rightNote.getX() - rightMetrics.modLeftPx - rightMetrics.leftDisplacedHeadPx;\n          space = rightNoteEdge - leftNoteEdge;\n          formatterMetrics.space.used = rightNote.getX() - note.getX();\n          rightNote.getFormatterMetrics().freedom.left = space;\n        } else {\n          space = justifyWidth - leftNoteEdge;\n          formatterMetrics.space.used = justifyWidth - note.getX();\n        }\n        formatterMetrics.freedom.right = space;\n        updateStats(duration, formatterMetrics.space.used);\n      });\n    });\n    let totalDeviation = 0;\n    this.voices.forEach(voice => {\n      voice.getTickables().forEach(note => {\n        const duration = note.getTicks().clone().simplify().toString();\n        const metrics = note.getFormatterMetrics();\n        metrics.space.mean = durationStats[duration].mean;\n        metrics.duration = duration;\n        metrics.iterations += 1;\n        metrics.space.deviation = metrics.space.used - metrics.space.mean;\n        totalDeviation += Math.pow(metrics.space.deviation, 2);\n      });\n    });\n    this.totalCost = Math.sqrt(totalDeviation);\n    this.lossHistory.push(this.totalCost);\n    return this.totalCost;\n  }\n  tune(options) {\n    var _a;\n    const contexts = this.tickContexts;\n    if (!contexts) {\n      return 0;\n    }\n    const alpha = (_a = options === null || options === void 0 ? void 0 : options.alpha) !== null && _a !== void 0 ? _a : 0.5;\n    let shift = 0;\n    this.totalShift = 0;\n    contexts.list.forEach((tick, index, list) => {\n      const context = contexts.map[tick];\n      const prevContext = index > 0 ? contexts.map[list[index - 1]] : undefined;\n      const nextContext = index < list.length - 1 ? contexts.map[list[index + 1]] : undefined;\n      context.move(shift, prevContext, nextContext);\n      const cost = -context.getDeviationCost();\n      if (cost > 0) {\n        shift = -Math.min(context.getFormatterMetrics().freedom.right, Math.abs(cost));\n      } else if (cost < 0) {\n        if (nextContext) {\n          shift = Math.min(nextContext.getFormatterMetrics().freedom.right, Math.abs(cost));\n        } else {\n          shift = 0;\n        }\n      }\n      shift *= alpha;\n      this.totalShift += shift;\n    });\n    return this.evaluate();\n  }\n  postFormat() {\n    this.modifierContexts.forEach(modifierContexts => {\n      modifierContexts.array.forEach(mc => mc.postFormat());\n    });\n    this.tickContexts.list.forEach(tick => {\n      this.tickContexts.map[tick].postFormat();\n    });\n    return this;\n  }\n  joinVoices(voices) {\n    this.createModifierContexts(voices);\n    this.hasMinTotalWidth = false;\n    return this;\n  }\n  format(voices, justifyWidth, options) {\n    const opts = Object.assign({\n      alignRests: false\n    }, options);\n    this.voices = voices;\n    const softmaxFactor = this.formatterOptions.softmaxFactor;\n    if (softmaxFactor) {\n      this.voices.forEach(v => v.setSoftmaxFactor(softmaxFactor));\n    }\n    this.alignRests(voices, opts.alignRests);\n    this.createTickContexts(voices);\n    this.preFormat(justifyWidth, opts.context, voices, opts.stave);\n    if (opts.stave) this.postFormat();\n    return this;\n  }\n  formatToStave(voices, stave, optionsParam) {\n    const options = Object.assign({\n      context: stave.getContext()\n    }, optionsParam);\n    const justifyWidth = stave.getNoteEndX() - stave.getNoteStartX() - Stave.defaultPadding;\n    L('Formatting voices to width: ', justifyWidth);\n    return this.format(voices, justifyWidth, options);\n  }\n  getTickContext(tick) {\n    var _a;\n    return (_a = this.tickContexts) === null || _a === void 0 ? void 0 : _a.map[tick];\n  }\n}\nFormatter.DEBUG = false;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Fraction", "Metrics", "ModifierContext", "Stave", "StaveConnector", "Tables", "TickContext", "isNote", "isStaveNote", "defined", "log", "midLine", "RuntimeError", "sumArray", "Voice", "createContexts", "voices", "makeContext", "addToContext", "length", "map", "array", "list", "resolutionMultiplier", "tickToContextMap", "tickList", "contexts", "<PERSON><PERSON><PERSON>", "getResolutionMultiplier", "for<PERSON>ach", "voice", "voiceIndex", "ticksUsed", "getTickables", "tickable", "integerTicks", "numerator", "newContext", "tickID", "push", "add", "getTicks", "sort", "a", "b", "L", "args", "DEBUG", "getRestLineForNextNoteGroup", "notes", "currRestLine", "currNoteIndex", "compare", "nextRestLine", "noteIndex", "note", "isRest", "shouldIgnoreTicks", "getLineForRest", "top", "Math", "max", "bot", "min", "SimpleFormat", "x", "paddingBetween", "reduce", "accumulator", "addToModifierContext", "tickContext", "addTickable", "preFormat", "metrics", "getMetrics", "setX", "totalLeftPx", "getWidth", "totalRightPx", "plotDebugging", "ctx", "formatter", "xPos", "y1", "y2", "options", "_a", "_b", "Object", "assign", "stavePadding", "get", "contextGaps", "stroke", "x1", "x2", "color", "beginPath", "setStrokeStyle", "setFillStyle", "setLineWidth", "fillRect", "save", "setFont", "gaps", "gap", "fillText", "round", "toString", "totalCost", "toFixed", "totalShift", "total", "restore", "FormatAndDraw", "stave", "params", "autoBeam", "alignRests", "TIME4_4", "setMode", "Mode", "SOFT", "addTickables", "beams", "applyAndGetBeams", "joinVoices", "formatToStave", "setContext", "setStave", "drawWithStyle", "beam", "getBoundingBox", "FormatAndDrawTab", "tabstave", "tabnotes", "opts", "notevoice", "tabvoice", "draw", "AlignRestsToNotes", "tickables", "alignAllNotes", "alignTuplets", "currTickable", "index", "getTuplet", "line", "getBeam", "props", "getKeyProps", "prevTickable", "restLine", "setKeyLine", "constructor", "formatterOptions", "globalSoftmax", "softmaxFactor", "SOFTMAX_FACTOR", "maxIterations", "justifyWidth", "durationStats", "minTotalWidth", "hasMinTotalWidth", "tickContexts", "modifierContexts", "lossHistory", "preCalculateMinTotalWidth", "unalignedPadding", "unalignedCtxCount", "wsum", "dsum", "widths", "durations", "createTickContexts", "contextList", "contextMap", "tick", "context", "t", "width", "value", "wavg", "wvar", "ll", "pow", "wpads", "davg", "dvar", "dpads", "padmax", "unalignedPad", "getMinTotalWidth", "totalTicks", "getTotalTicks", "equals", "getMode", "STRICT", "isComplete", "LCM", "createModifierContexts", "Map", "staveTickToContextMap", "getStave", "set", "undefined", "fn", "contextArray", "tContexts", "getTickContexts", "renderingContext", "voices<PERSON><PERSON><PERSON>", "shift", "maxTicks", "getMaxTicks", "exp", "expTicksUsed", "evaluate", "firstContext", "lastContext", "calculateIdealDistances", "adjustedJustifyWidth", "distances", "i", "getTickablesByVoice", "backTickable", "prevContext", "j", "backTick", "backVoices", "matchingVoices", "keys", "v", "maxNegativeShiftPx", "Infinity", "expectedDistance", "ticks", "thisTickable", "insideLeftEdge", "getX", "modLeftPx", "leftDisplacedHeadPx", "backMetrics", "insideRightEdge", "notePx", "modRightPx", "rightDisplacedHeadPx", "getVoice", "softmax", "fromTickable", "fromTickablePx", "shiftToIdealDistances", "idealDistances", "centerX", "spaceAccum", "contextX", "ideal", "errorPx", "negativeShiftPx", "abs", "getCenterAlignedTickables", "setCenterXShift", "configMinPadding", "configMaxPadding", "leftPadding", "targetWidth", "actualWidth", "calcMinDistance", "mdCalc", "di", "minDistance", "paddingMaxCalc", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastTickablePadding", "lastTickable", "getMaxTickable", "getTicksUsed", "tickWidth", "paddingMax", "paddingMin", "maxX", "iterations", "prevTick", "prevMetrics", "currMetrics", "getFormatterMetrics", "freedom", "left", "right", "updateStats", "duration", "space", "stats", "mean", "count", "clone", "simplify", "formatterMetrics", "leftNoteEdge", "rightNote", "rightMetrics", "rightNoteEdge", "used", "totalDeviation", "deviation", "sqrt", "tune", "alpha", "nextContext", "move", "cost", "getDeviationCost", "postFormat", "mc", "format", "setSoftmaxFactor", "optionsParam", "getContext", "getNoteEndX", "getNoteStartX", "defaultPadding", "getTickContext"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/formatter.js"], "sourcesContent": ["import { Beam } from './beam.js';\nimport { Fraction } from './fraction.js';\nimport { Metrics } from './metrics.js';\nimport { ModifierContext } from './modifiercontext.js';\nimport { Stave } from './stave.js';\nimport { StaveConnector } from './staveconnector.js';\nimport { Tables } from './tables.js';\nimport { TickContext } from './tickcontext.js';\nimport { isNote, isStaveNote } from './typeguard.js';\nimport { defined, log, midLine, RuntimeError, sumArray } from './util.js';\nimport { Voice } from './voice.js';\nfunction createContexts(voices, makeContext, addToContext) {\n    if (voices.length === 0)\n        return {\n            map: {},\n            array: [],\n            list: [],\n            resolutionMultiplier: 0,\n        };\n    const tickToContextMap = {};\n    const tickList = [];\n    const contexts = [];\n    const resolutionMultiplier = Formatter.getResolutionMultiplier(voices);\n    voices.forEach((voice, voiceIndex) => {\n        const ticksUsed = new Fraction(0, resolutionMultiplier);\n        voice.getTickables().forEach((tickable) => {\n            const integerTicks = ticksUsed.numerator;\n            if (!tickToContextMap[integerTicks]) {\n                const newContext = makeContext({ tickID: integerTicks });\n                contexts.push(newContext);\n                tickToContextMap[integerTicks] = newContext;\n                tickList.push(integerTicks);\n            }\n            addToContext(tickable, tickToContextMap[integerTicks], voiceIndex);\n            ticksUsed.add(tickable.getTicks());\n        });\n    });\n    return {\n        map: tickToContextMap,\n        array: contexts,\n        list: tickList.sort((a, b) => a - b),\n        resolutionMultiplier,\n    };\n}\nfunction L(...args) {\n    if (Formatter.DEBUG)\n        log('VexFlow.Formatter', args);\n}\nfunction getRestLineForNextNoteGroup(notes, currRestLine, currNoteIndex, compare) {\n    let nextRestLine = currRestLine;\n    for (let noteIndex = currNoteIndex + 1; noteIndex < notes.length; noteIndex++) {\n        const note = notes[noteIndex];\n        if (isNote(note) && !note.isRest() && !note.shouldIgnoreTicks()) {\n            nextRestLine = note.getLineForRest();\n            break;\n        }\n    }\n    if (compare && currRestLine !== nextRestLine) {\n        const top = Math.max(currRestLine, nextRestLine);\n        const bot = Math.min(currRestLine, nextRestLine);\n        nextRestLine = midLine(top, bot);\n    }\n    return nextRestLine;\n}\nexport class Formatter {\n    static SimpleFormat(notes, x = 0, { paddingBetween = 10 } = {}) {\n        notes.reduce((accumulator, note) => {\n            note.addToModifierContext(new ModifierContext());\n            const tickContext = new TickContext().addTickable(note).preFormat();\n            const metrics = tickContext.getMetrics();\n            tickContext.setX(accumulator + metrics.totalLeftPx);\n            return accumulator + tickContext.getWidth() + metrics.totalRightPx + paddingBetween;\n        }, x);\n    }\n    static plotDebugging(ctx, formatter, xPos, y1, y2, options) {\n        var _a, _b;\n        options = Object.assign({ stavePadding: Metrics.get('Stave.padding') }, options);\n        const x = xPos + options.stavePadding;\n        const contextGaps = formatter.contextGaps;\n        function stroke(x1, x2, color) {\n            ctx.beginPath();\n            ctx.setStrokeStyle(color);\n            ctx.setFillStyle(color);\n            ctx.setLineWidth(1);\n            ctx.fillRect(x1, y1, Math.max(x2 - x1, 0), y2 - y1);\n        }\n        ctx.save();\n        ctx.setFont(Metrics.get('fontFamily'), 8);\n        contextGaps.gaps.forEach((gap) => {\n            stroke(x + gap.x1, x + gap.x2, 'rgba(100,200,100,0.4)');\n            ctx.setFillStyle('green');\n            ctx.fillText(Math.round(gap.x2 - gap.x1).toString(), x + gap.x1, y2 + 12);\n        });\n        ctx.setFillStyle('red');\n        ctx.fillText(`Loss: ${((_a = formatter.totalCost) !== null && _a !== void 0 ? _a : 0).toFixed(2)} Shift: ${((_b = formatter.totalShift) !== null && _b !== void 0 ? _b : 0).toFixed(2)} Gap: ${contextGaps.total.toFixed(2)}`, x - 20, y2 + 27);\n        ctx.restore();\n    }\n    static FormatAndDraw(ctx, stave, notes, params) {\n        let options = {\n            autoBeam: false,\n            alignRests: false,\n        };\n        if (typeof params === 'object') {\n            options = Object.assign(Object.assign({}, options), params);\n        }\n        else if (typeof params === 'boolean') {\n            options.autoBeam = params;\n        }\n        const voice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(notes);\n        const beams = options.autoBeam ? Beam.applyAndGetBeams(voice) : [];\n        new Formatter()\n            .joinVoices([voice])\n            .formatToStave([voice], stave, { alignRests: options.alignRests, stave });\n        voice.setContext(ctx).setStave(stave).drawWithStyle();\n        beams.forEach((beam) => beam.setContext(ctx).drawWithStyle());\n        return voice.getBoundingBox();\n    }\n    static FormatAndDrawTab(ctx, tabstave, stave, tabnotes, notes, autoBeam, params) {\n        let opts = {\n            autoBeam,\n            alignRests: false,\n        };\n        if (typeof params === 'object') {\n            opts = Object.assign(Object.assign({}, opts), params);\n        }\n        else if (typeof params === 'boolean') {\n            opts.autoBeam = params;\n        }\n        const notevoice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(notes);\n        const tabvoice = new Voice(Tables.TIME4_4).setMode(Voice.Mode.SOFT).addTickables(tabnotes);\n        const beams = opts.autoBeam ? Beam.applyAndGetBeams(notevoice) : [];\n        new Formatter()\n            .joinVoices([notevoice])\n            .joinVoices([tabvoice])\n            .formatToStave([notevoice, tabvoice], stave, { alignRests: opts.alignRests });\n        notevoice.draw(ctx, stave);\n        tabvoice.draw(ctx, tabstave);\n        beams.forEach((beam) => beam.setContext(ctx).drawWithStyle());\n        new StaveConnector(stave, tabstave).setContext(ctx).drawWithStyle();\n    }\n    static AlignRestsToNotes(tickables, alignAllNotes, alignTuplets) {\n        tickables.forEach((currTickable, index) => {\n            if (isStaveNote(currTickable) && currTickable.isRest()) {\n                if (currTickable.getTuplet() && !alignTuplets) {\n                    return;\n                }\n                const line = currTickable.getLineForRest();\n                if (line !== 3) {\n                    return;\n                }\n                if (alignAllNotes || currTickable.getBeam()) {\n                    const props = currTickable.getKeyProps()[0];\n                    if (index === 0) {\n                        props.line = getRestLineForNextNoteGroup(tickables, props.line, index, false);\n                    }\n                    else if (index > 0 && index < tickables.length) {\n                        const prevTickable = tickables[index - 1];\n                        if (isStaveNote(prevTickable)) {\n                            if (prevTickable.isRest()) {\n                                props.line = prevTickable.getKeyProps()[0].line;\n                            }\n                            else {\n                                const restLine = prevTickable.getLineForRest();\n                                props.line = getRestLineForNextNoteGroup(tickables, restLine, index, true);\n                            }\n                        }\n                    }\n                    currTickable.setKeyLine(0, props.line);\n                }\n            }\n        });\n    }\n    constructor(options) {\n        this.formatterOptions = Object.assign({ globalSoftmax: false, softmaxFactor: Tables.SOFTMAX_FACTOR, maxIterations: 5 }, options);\n        this.justifyWidth = 0;\n        this.totalCost = 0;\n        this.totalShift = 0;\n        this.durationStats = {};\n        this.minTotalWidth = 0;\n        this.hasMinTotalWidth = false;\n        this.tickContexts = {\n            map: {},\n            array: [],\n            list: [],\n            resolutionMultiplier: 0,\n        };\n        this.modifierContexts = [];\n        this.contextGaps = {\n            total: 0,\n            gaps: [],\n        };\n        this.voices = [];\n        this.lossHistory = [];\n    }\n    alignRests(voices, alignAllNotes) {\n        if (!voices || !voices.length) {\n            throw new RuntimeError('BadArgument', 'No voices to format rests');\n        }\n        voices.forEach((voice) => Formatter.AlignRestsToNotes(voice.getTickables(), alignAllNotes));\n    }\n    preCalculateMinTotalWidth(voices) {\n        const unalignedPadding = Metrics.get('Stave.unalignedNotePadding');\n        let unalignedCtxCount = 0;\n        let wsum = 0;\n        let dsum = 0;\n        const widths = [];\n        const durations = [];\n        if (this.hasMinTotalWidth)\n            return this.minTotalWidth;\n        if (!voices) {\n            throw new RuntimeError('BadArgument', \"'voices' required to run preCalculateMinTotalWidth\");\n        }\n        this.createTickContexts(voices);\n        const { list: contextList, map: contextMap } = this.tickContexts;\n        this.minTotalWidth = 0;\n        contextList.forEach((tick) => {\n            const context = contextMap[tick];\n            context.preFormat();\n            if (context.getTickables().length < voices.length) {\n                unalignedCtxCount += 1;\n            }\n            context.getTickables().forEach((t) => {\n                wsum += t.getMetrics().width;\n                dsum += t.getTicks().value();\n                widths.push(t.getMetrics().width);\n                durations.push(t.getTicks().value());\n            });\n            const width = context.getWidth();\n            this.minTotalWidth += width;\n        });\n        this.hasMinTotalWidth = true;\n        const wavg = wsum > 0 ? wsum / widths.length : 1 / widths.length;\n        const wvar = sumArray(widths.map((ll) => Math.pow(ll - wavg, 2)));\n        const wpads = Math.pow(wvar / widths.length, 0.5) / wavg;\n        const davg = dsum / durations.length;\n        const dvar = sumArray(durations.map((ll) => Math.pow(ll - davg, 2)));\n        const dpads = Math.pow(dvar / durations.length, 0.5) / davg;\n        const padmax = Math.max(dpads, wpads) * contextList.length * unalignedPadding;\n        const unalignedPad = unalignedPadding * unalignedCtxCount;\n        return this.minTotalWidth + Math.max(unalignedPad, padmax);\n    }\n    getMinTotalWidth() {\n        if (!this.hasMinTotalWidth) {\n            throw new RuntimeError('NoMinTotalWidth', \"Call 'preCalculateMinTotalWidth' or 'preFormat' before calling 'getMinTotalWidth'\");\n        }\n        return this.minTotalWidth;\n    }\n    static getResolutionMultiplier(voices) {\n        if (!voices || !voices.length) {\n            throw new RuntimeError('BadArgument', 'No voices to format');\n        }\n        const totalTicks = voices[0].getTotalTicks();\n        const resolutionMultiplier = voices.reduce((accumulator, voice) => {\n            if (!voice.getTotalTicks().equals(totalTicks)) {\n                throw new RuntimeError('TickMismatch', 'Voices should have same total note duration in ticks.');\n            }\n            if (voice.getMode() === Voice.Mode.STRICT && !voice.isComplete()) {\n                throw new RuntimeError('IncompleteVoice', 'Voice does not have enough notes.');\n            }\n            return Math.max(accumulator, Fraction.LCM(accumulator, voice.getResolutionMultiplier()));\n        }, 1);\n        return resolutionMultiplier;\n    }\n    createModifierContexts(voices) {\n        if (voices.length === 0)\n            return;\n        const resolutionMultiplier = Formatter.getResolutionMultiplier(voices);\n        const tickToContextMap = new Map();\n        const contexts = [];\n        voices.forEach((voice) => {\n            const ticksUsed = new Fraction(0, resolutionMultiplier);\n            voice.getTickables().forEach((tickable) => {\n                const integerTicks = ticksUsed.numerator;\n                let staveTickToContextMap = tickToContextMap.get(tickable.getStave());\n                if (!staveTickToContextMap) {\n                    tickToContextMap.set(tickable.getStave(), {});\n                    staveTickToContextMap = tickToContextMap.get(tickable.getStave());\n                }\n                if (!(staveTickToContextMap ? staveTickToContextMap[integerTicks] : undefined)) {\n                    const newContext = new ModifierContext();\n                    contexts.push(newContext);\n                    staveTickToContextMap[integerTicks] = newContext;\n                }\n                tickable.addToModifierContext(staveTickToContextMap[integerTicks]);\n                ticksUsed.add(tickable.getTicks());\n            });\n        });\n        this.modifierContexts.push({\n            map: tickToContextMap,\n            array: contexts,\n            resolutionMultiplier,\n        });\n    }\n    createTickContexts(voices) {\n        const fn = (tickable, context, voiceIndex) => context.addTickable(tickable, voiceIndex);\n        const contexts = createContexts(voices, (tick) => new TickContext(tick), fn);\n        this.tickContexts = contexts;\n        const contextArray = this.tickContexts.array;\n        contextArray.forEach((context) => {\n            context.tContexts = contextArray;\n        });\n        return contexts;\n    }\n    getTickContexts() {\n        return this.tickContexts;\n    }\n    preFormat(justifyWidth = 0, renderingContext, voicesParam, stave) {\n        const contexts = this.tickContexts;\n        if (!contexts) {\n            throw new RuntimeError('NoTickContexts', 'preFormat requires TickContexts');\n        }\n        const { list: contextList, map: contextMap } = contexts;\n        this.lossHistory = [];\n        if (voicesParam && stave) {\n            voicesParam.forEach((voice) => voice.setStave(stave).preFormat());\n        }\n        let x = 0;\n        let shift = 0;\n        this.minTotalWidth = 0;\n        let totalTicks = 0;\n        contextList.forEach((tick) => {\n            const context = contextMap[tick];\n            context.preFormat();\n            const width = context.getWidth();\n            this.minTotalWidth += width;\n            const maxTicks = context.getMaxTicks().value();\n            totalTicks += maxTicks;\n            const metrics = context.getMetrics();\n            x = x + shift + metrics.totalLeftPx;\n            context.setX(x);\n            shift = width - metrics.totalLeftPx;\n        });\n        const { globalSoftmax, softmaxFactor, maxIterations } = this.formatterOptions;\n        const exp = (tick) => Math.pow(softmaxFactor, (contextMap[tick].getMaxTicks().value() / totalTicks));\n        const expTicksUsed = sumArray(contextList.map(exp));\n        this.minTotalWidth = x + shift;\n        this.hasMinTotalWidth = true;\n        if (justifyWidth <= 0)\n            return this.evaluate();\n        const firstContext = contextMap[contextList[0]];\n        const lastContext = contextMap[contextList[contextList.length - 1]];\n        function calculateIdealDistances(adjustedJustifyWidth) {\n            const distances = contextList.map((tick, i) => {\n                const context = contextMap[tick];\n                const voices = context.getTickablesByVoice();\n                let backTickable;\n                if (i > 0) {\n                    const prevContext = contextMap[contextList[i - 1]];\n                    for (let j = i - 1; j >= 0; j--) {\n                        const backTick = contextMap[contextList[j]];\n                        const backVoices = backTick.getTickablesByVoice();\n                        const matchingVoices = [];\n                        Object.keys(voices).forEach((v) => {\n                            if (backVoices[v]) {\n                                matchingVoices.push(v);\n                            }\n                        });\n                        if (matchingVoices.length > 0) {\n                            let maxTicks = 0;\n                            let maxNegativeShiftPx = Infinity;\n                            let expectedDistance = 0;\n                            matchingVoices.forEach((v) => {\n                                const ticks = backVoices[v].getTicks().value();\n                                if (ticks > maxTicks) {\n                                    backTickable = backVoices[v];\n                                    maxTicks = ticks;\n                                }\n                                const thisTickable = voices[v];\n                                const insideLeftEdge = thisTickable.getX() -\n                                    (thisTickable.getMetrics().modLeftPx + thisTickable.getMetrics().leftDisplacedHeadPx);\n                                const backMetrics = backVoices[v].getMetrics();\n                                const insideRightEdge = backVoices[v].getX() + backMetrics.notePx + backMetrics.modRightPx + backMetrics.rightDisplacedHeadPx;\n                                maxNegativeShiftPx = Math.min(maxNegativeShiftPx, insideLeftEdge - insideRightEdge);\n                            });\n                            maxNegativeShiftPx = Math.min(maxNegativeShiftPx, context.getX() - (prevContext.getX() + adjustedJustifyWidth * 0.05));\n                            if (globalSoftmax) {\n                                const t = totalTicks;\n                                expectedDistance = (Math.pow(softmaxFactor, (maxTicks / t)) / expTicksUsed) * adjustedJustifyWidth;\n                            }\n                            else if (typeof backTickable !== 'undefined') {\n                                expectedDistance = backTickable.getVoice().softmax(maxTicks) * adjustedJustifyWidth;\n                            }\n                            return {\n                                expectedDistance,\n                                maxNegativeShiftPx,\n                                fromTickable: backTickable,\n                            };\n                        }\n                    }\n                }\n                return { expectedDistance: 0, fromTickablePx: 0, maxNegativeShiftPx: 0 };\n            });\n            return distances;\n        }\n        function shiftToIdealDistances(idealDistances) {\n            const centerX = adjustedJustifyWidth / 2;\n            let spaceAccum = 0;\n            contextList.forEach((tick, index) => {\n                const context = contextMap[tick];\n                if (index > 0) {\n                    const contextX = context.getX();\n                    const ideal = idealDistances[index];\n                    const errorPx = defined(ideal.fromTickable).getX() + ideal.expectedDistance - (contextX + spaceAccum);\n                    let negativeShiftPx = 0;\n                    if (errorPx > 0) {\n                        spaceAccum += errorPx;\n                    }\n                    else if (errorPx < 0) {\n                        negativeShiftPx = Math.min(ideal.maxNegativeShiftPx, Math.abs(errorPx));\n                        spaceAccum += -negativeShiftPx;\n                    }\n                    context.setX(contextX + spaceAccum);\n                }\n                context.getCenterAlignedTickables().forEach((tickable) => {\n                    tickable.setCenterXShift(centerX - context.getX());\n                });\n            });\n            return lastContext.getX() - firstContext.getX();\n        }\n        const adjustedJustifyWidth = justifyWidth -\n            lastContext.getMetrics().notePx -\n            lastContext.getMetrics().totalRightPx -\n            firstContext.getMetrics().totalLeftPx;\n        const configMinPadding = Metrics.get('Stave.endPaddingMin');\n        const configMaxPadding = Metrics.get('Stave.endPaddingMax');\n        const leftPadding = Metrics.get('Stave.padding');\n        let targetWidth = adjustedJustifyWidth;\n        const distances = calculateIdealDistances(targetWidth);\n        let actualWidth = shiftToIdealDistances(distances);\n        if (contextList.length === 1)\n            return 0;\n        const calcMinDistance = (targetWidth, distances) => {\n            let mdCalc = targetWidth / 2;\n            if (distances.length > 1) {\n                for (let di = 1; di < distances.length; ++di) {\n                    mdCalc = Math.min(distances[di].expectedDistance / 2, mdCalc);\n                }\n            }\n            return mdCalc;\n        };\n        const minDistance = calcMinDistance(targetWidth, distances);\n        const paddingMaxCalc = (curTargetWidth) => {\n            let lastTickablePadding = 0;\n            const lastTickable = lastContext && lastContext.getMaxTickable();\n            if (lastTickable) {\n                const voice = lastTickable.getVoice();\n                if (voice.getTicksUsed().value() > voice.getTotalTicks().value()) {\n                    return configMaxPadding * 2 < minDistance ? minDistance : configMaxPadding;\n                }\n                const tickWidth = lastTickable.getWidth();\n                lastTickablePadding =\n                    voice.softmax(lastContext.getMaxTicks().value()) * curTargetWidth - (tickWidth + leftPadding);\n            }\n            return configMaxPadding * 2 < lastTickablePadding ? lastTickablePadding : configMaxPadding;\n        };\n        let paddingMax = paddingMaxCalc(targetWidth);\n        let paddingMin = paddingMax - (configMaxPadding - configMinPadding);\n        const maxX = adjustedJustifyWidth - paddingMin;\n        let iterations = maxIterations;\n        while ((actualWidth > maxX && iterations > 0) || (actualWidth + paddingMax < maxX && iterations > 1)) {\n            targetWidth -= actualWidth - maxX;\n            paddingMax = paddingMaxCalc(targetWidth);\n            paddingMin = paddingMax - (configMaxPadding - configMinPadding);\n            actualWidth = shiftToIdealDistances(calculateIdealDistances(targetWidth));\n            iterations--;\n        }\n        this.justifyWidth = justifyWidth;\n        return this.evaluate();\n    }\n    evaluate() {\n        const contexts = this.tickContexts;\n        const justifyWidth = this.justifyWidth;\n        this.contextGaps = { total: 0, gaps: [] };\n        contexts.list.forEach((tick, index) => {\n            if (index === 0)\n                return;\n            const prevTick = contexts.list[index - 1];\n            const prevContext = contexts.map[prevTick];\n            const context = contexts.map[tick];\n            const prevMetrics = prevContext.getMetrics();\n            const currMetrics = context.getMetrics();\n            const insideRightEdge = prevContext.getX() + prevMetrics.notePx + prevMetrics.totalRightPx;\n            const insideLeftEdge = context.getX() - currMetrics.totalLeftPx;\n            const gap = insideLeftEdge - insideRightEdge;\n            this.contextGaps.total += gap;\n            this.contextGaps.gaps.push({ x1: insideRightEdge, x2: insideLeftEdge });\n            context.getFormatterMetrics().freedom.left = gap;\n            prevContext.getFormatterMetrics().freedom.right = gap;\n        });\n        this.durationStats = {};\n        const durationStats = this.durationStats;\n        function updateStats(duration, space) {\n            const stats = durationStats[duration];\n            if (stats === undefined) {\n                durationStats[duration] = { mean: space, count: 1, total: space };\n            }\n            else {\n                stats.count += 1;\n                stats.total += space;\n                stats.mean = stats.total / stats.count;\n            }\n        }\n        this.voices.forEach((voice) => {\n            voice.getTickables().forEach((note, i, notes) => {\n                const duration = note.getTicks().clone().simplify().toString();\n                const metrics = note.getMetrics();\n                const formatterMetrics = note.getFormatterMetrics();\n                const leftNoteEdge = note.getX() + metrics.notePx + metrics.modRightPx + metrics.rightDisplacedHeadPx;\n                let space = 0;\n                if (i < notes.length - 1) {\n                    const rightNote = notes[i + 1];\n                    const rightMetrics = rightNote.getMetrics();\n                    const rightNoteEdge = rightNote.getX() - rightMetrics.modLeftPx - rightMetrics.leftDisplacedHeadPx;\n                    space = rightNoteEdge - leftNoteEdge;\n                    formatterMetrics.space.used = rightNote.getX() - note.getX();\n                    rightNote.getFormatterMetrics().freedom.left = space;\n                }\n                else {\n                    space = justifyWidth - leftNoteEdge;\n                    formatterMetrics.space.used = justifyWidth - note.getX();\n                }\n                formatterMetrics.freedom.right = space;\n                updateStats(duration, formatterMetrics.space.used);\n            });\n        });\n        let totalDeviation = 0;\n        this.voices.forEach((voice) => {\n            voice.getTickables().forEach((note) => {\n                const duration = note.getTicks().clone().simplify().toString();\n                const metrics = note.getFormatterMetrics();\n                metrics.space.mean = durationStats[duration].mean;\n                metrics.duration = duration;\n                metrics.iterations += 1;\n                metrics.space.deviation = metrics.space.used - metrics.space.mean;\n                totalDeviation += Math.pow(metrics.space.deviation, 2);\n            });\n        });\n        this.totalCost = Math.sqrt(totalDeviation);\n        this.lossHistory.push(this.totalCost);\n        return this.totalCost;\n    }\n    tune(options) {\n        var _a;\n        const contexts = this.tickContexts;\n        if (!contexts) {\n            return 0;\n        }\n        const alpha = (_a = options === null || options === void 0 ? void 0 : options.alpha) !== null && _a !== void 0 ? _a : 0.5;\n        let shift = 0;\n        this.totalShift = 0;\n        contexts.list.forEach((tick, index, list) => {\n            const context = contexts.map[tick];\n            const prevContext = index > 0 ? contexts.map[list[index - 1]] : undefined;\n            const nextContext = index < list.length - 1 ? contexts.map[list[index + 1]] : undefined;\n            context.move(shift, prevContext, nextContext);\n            const cost = -context.getDeviationCost();\n            if (cost > 0) {\n                shift = -Math.min(context.getFormatterMetrics().freedom.right, Math.abs(cost));\n            }\n            else if (cost < 0) {\n                if (nextContext) {\n                    shift = Math.min(nextContext.getFormatterMetrics().freedom.right, Math.abs(cost));\n                }\n                else {\n                    shift = 0;\n                }\n            }\n            shift *= alpha;\n            this.totalShift += shift;\n        });\n        return this.evaluate();\n    }\n    postFormat() {\n        this.modifierContexts.forEach((modifierContexts) => {\n            modifierContexts.array.forEach((mc) => mc.postFormat());\n        });\n        this.tickContexts.list.forEach((tick) => {\n            this.tickContexts.map[tick].postFormat();\n        });\n        return this;\n    }\n    joinVoices(voices) {\n        this.createModifierContexts(voices);\n        this.hasMinTotalWidth = false;\n        return this;\n    }\n    format(voices, justifyWidth, options) {\n        const opts = Object.assign({ alignRests: false }, options);\n        this.voices = voices;\n        const softmaxFactor = this.formatterOptions.softmaxFactor;\n        if (softmaxFactor) {\n            this.voices.forEach((v) => v.setSoftmaxFactor(softmaxFactor));\n        }\n        this.alignRests(voices, opts.alignRests);\n        this.createTickContexts(voices);\n        this.preFormat(justifyWidth, opts.context, voices, opts.stave);\n        if (opts.stave)\n            this.postFormat();\n        return this;\n    }\n    formatToStave(voices, stave, optionsParam) {\n        const options = Object.assign({ context: stave.getContext() }, optionsParam);\n        const justifyWidth = stave.getNoteEndX() - stave.getNoteStartX() - Stave.defaultPadding;\n        L('Formatting voices to width: ', justifyWidth);\n        return this.format(voices, justifyWidth, options);\n    }\n    getTickContext(tick) {\n        var _a;\n        return (_a = this.tickContexts) === null || _a === void 0 ? void 0 : _a.map[tick];\n    }\n}\nFormatter.DEBUG = false;\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,WAAW,QAAQ,gBAAgB;AACpD,SAASC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,WAAW;AACzE,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,cAAcA,CAACC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAE;EACvD,IAAIF,MAAM,CAACG,MAAM,KAAK,CAAC,EACnB,OAAO;IACHC,GAAG,EAAE,CAAC,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE;EAC1B,CAAC;EACL,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3B,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMH,oBAAoB,GAAGI,SAAS,CAACC,uBAAuB,CAACZ,MAAM,CAAC;EACtEA,MAAM,CAACa,OAAO,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAClC,MAAMC,SAAS,GAAG,IAAIhC,QAAQ,CAAC,CAAC,EAAEuB,oBAAoB,CAAC;IACvDO,KAAK,CAACG,YAAY,CAAC,CAAC,CAACJ,OAAO,CAAEK,QAAQ,IAAK;MACvC,MAAMC,YAAY,GAAGH,SAAS,CAACI,SAAS;MACxC,IAAI,CAACZ,gBAAgB,CAACW,YAAY,CAAC,EAAE;QACjC,MAAME,UAAU,GAAGpB,WAAW,CAAC;UAAEqB,MAAM,EAAEH;QAAa,CAAC,CAAC;QACxDT,QAAQ,CAACa,IAAI,CAACF,UAAU,CAAC;QACzBb,gBAAgB,CAACW,YAAY,CAAC,GAAGE,UAAU;QAC3CZ,QAAQ,CAACc,IAAI,CAACJ,YAAY,CAAC;MAC/B;MACAjB,YAAY,CAACgB,QAAQ,EAAEV,gBAAgB,CAACW,YAAY,CAAC,EAAEJ,UAAU,CAAC;MAClEC,SAAS,CAACQ,GAAG,CAACN,QAAQ,CAACO,QAAQ,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAO;IACHrB,GAAG,EAAEI,gBAAgB;IACrBH,KAAK,EAAEK,QAAQ;IACfJ,IAAI,EAAEG,QAAQ,CAACiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IACpCrB;EACJ,CAAC;AACL;AACA,SAASsB,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAInB,SAAS,CAACoB,KAAK,EACfrC,GAAG,CAAC,mBAAmB,EAAEoC,IAAI,CAAC;AACtC;AACA,SAASE,2BAA2BA,CAACC,KAAK,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAE;EAC9E,IAAIC,YAAY,GAAGH,YAAY;EAC/B,KAAK,IAAII,SAAS,GAAGH,aAAa,GAAG,CAAC,EAAEG,SAAS,GAAGL,KAAK,CAAC9B,MAAM,EAAEmC,SAAS,EAAE,EAAE;IAC3E,MAAMC,IAAI,GAAGN,KAAK,CAACK,SAAS,CAAC;IAC7B,IAAI/C,MAAM,CAACgD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI,CAACD,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAC7DJ,YAAY,GAAGE,IAAI,CAACG,cAAc,CAAC,CAAC;MACpC;IACJ;EACJ;EACA,IAAIN,OAAO,IAAIF,YAAY,KAAKG,YAAY,EAAE;IAC1C,MAAMM,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACX,YAAY,EAAEG,YAAY,CAAC;IAChD,MAAMS,GAAG,GAAGF,IAAI,CAACG,GAAG,CAACb,YAAY,EAAEG,YAAY,CAAC;IAChDA,YAAY,GAAG1C,OAAO,CAACgD,GAAG,EAAEG,GAAG,CAAC;EACpC;EACA,OAAOT,YAAY;AACvB;AACA,OAAO,MAAM1B,SAAS,CAAC;EACnB,OAAOqC,YAAYA,CAACf,KAAK,EAAEgB,CAAC,GAAG,CAAC,EAAE;IAAEC,cAAc,GAAG;EAAG,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5DjB,KAAK,CAACkB,MAAM,CAAC,CAACC,WAAW,EAAEb,IAAI,KAAK;MAChCA,IAAI,CAACc,oBAAoB,CAAC,IAAInE,eAAe,CAAC,CAAC,CAAC;MAChD,MAAMoE,WAAW,GAAG,IAAIhE,WAAW,CAAC,CAAC,CAACiE,WAAW,CAAChB,IAAI,CAAC,CAACiB,SAAS,CAAC,CAAC;MACnE,MAAMC,OAAO,GAAGH,WAAW,CAACI,UAAU,CAAC,CAAC;MACxCJ,WAAW,CAACK,IAAI,CAACP,WAAW,GAAGK,OAAO,CAACG,WAAW,CAAC;MACnD,OAAOR,WAAW,GAAGE,WAAW,CAACO,QAAQ,CAAC,CAAC,GAAGJ,OAAO,CAACK,YAAY,GAAGZ,cAAc;IACvF,CAAC,EAAED,CAAC,CAAC;EACT;EACA,OAAOc,aAAaA,CAACC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAE;IACxD,IAAIC,EAAE,EAAEC,EAAE;IACVF,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC;MAAEC,YAAY,EAAEzF,OAAO,CAAC0F,GAAG,CAAC,eAAe;IAAE,CAAC,EAAEN,OAAO,CAAC;IAChF,MAAMpB,CAAC,GAAGiB,IAAI,GAAGG,OAAO,CAACK,YAAY;IACrC,MAAME,WAAW,GAAGX,SAAS,CAACW,WAAW;IACzC,SAASC,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;MAC3BhB,GAAG,CAACiB,SAAS,CAAC,CAAC;MACfjB,GAAG,CAACkB,cAAc,CAACF,KAAK,CAAC;MACzBhB,GAAG,CAACmB,YAAY,CAACH,KAAK,CAAC;MACvBhB,GAAG,CAACoB,YAAY,CAAC,CAAC,CAAC;MACnBpB,GAAG,CAACqB,QAAQ,CAACP,EAAE,EAAEX,EAAE,EAAEvB,IAAI,CAACC,GAAG,CAACkC,EAAE,GAAGD,EAAE,EAAE,CAAC,CAAC,EAAEV,EAAE,GAAGD,EAAE,CAAC;IACvD;IACAH,GAAG,CAACsB,IAAI,CAAC,CAAC;IACVtB,GAAG,CAACuB,OAAO,CAACtG,OAAO,CAAC0F,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACzCC,WAAW,CAACY,IAAI,CAAC3E,OAAO,CAAE4E,GAAG,IAAK;MAC9BZ,MAAM,CAAC5B,CAAC,GAAGwC,GAAG,CAACX,EAAE,EAAE7B,CAAC,GAAGwC,GAAG,CAACV,EAAE,EAAE,uBAAuB,CAAC;MACvDf,GAAG,CAACmB,YAAY,CAAC,OAAO,CAAC;MACzBnB,GAAG,CAAC0B,QAAQ,CAAC9C,IAAI,CAAC+C,KAAK,CAACF,GAAG,CAACV,EAAE,GAAGU,GAAG,CAACX,EAAE,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE3C,CAAC,GAAGwC,GAAG,CAACX,EAAE,EAAEV,EAAE,GAAG,EAAE,CAAC;IAC7E,CAAC,CAAC;IACFJ,GAAG,CAACmB,YAAY,CAAC,KAAK,CAAC;IACvBnB,GAAG,CAAC0B,QAAQ,CAAC,SAAS,CAAC,CAACpB,EAAE,GAAGL,SAAS,CAAC4B,SAAS,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAEwB,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAACvB,EAAE,GAAGN,SAAS,CAAC8B,UAAU,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,EAAEuB,OAAO,CAAC,CAAC,CAAC,SAASlB,WAAW,CAACoB,KAAK,CAACF,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE7C,CAAC,GAAG,EAAE,EAAEmB,EAAE,GAAG,EAAE,CAAC;IAC/OJ,GAAG,CAACiC,OAAO,CAAC,CAAC;EACjB;EACA,OAAOC,aAAaA,CAAClC,GAAG,EAAEmC,KAAK,EAAElE,KAAK,EAAEmE,MAAM,EAAE;IAC5C,IAAI/B,OAAO,GAAG;MACVgC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;IAChB,CAAC;IACD,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;MAC5B/B,OAAO,GAAGG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE+B,MAAM,CAAC;IAC/D,CAAC,MACI,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;MAClC/B,OAAO,CAACgC,QAAQ,GAAGD,MAAM;IAC7B;IACA,MAAMtF,KAAK,GAAG,IAAIhB,KAAK,CAACT,MAAM,CAACkH,OAAO,CAAC,CAACC,OAAO,CAAC1G,KAAK,CAAC2G,IAAI,CAACC,IAAI,CAAC,CAACC,YAAY,CAAC1E,KAAK,CAAC;IACpF,MAAM2E,KAAK,GAAGvC,OAAO,CAACgC,QAAQ,GAAGtH,IAAI,CAAC8H,gBAAgB,CAAC/F,KAAK,CAAC,GAAG,EAAE;IAClE,IAAIH,SAAS,CAAC,CAAC,CACVmG,UAAU,CAAC,CAAChG,KAAK,CAAC,CAAC,CACnBiG,aAAa,CAAC,CAACjG,KAAK,CAAC,EAAEqF,KAAK,EAAE;MAAEG,UAAU,EAAEjC,OAAO,CAACiC,UAAU;MAAEH;IAAM,CAAC,CAAC;IAC7ErF,KAAK,CAACkG,UAAU,CAAChD,GAAG,CAAC,CAACiD,QAAQ,CAACd,KAAK,CAAC,CAACe,aAAa,CAAC,CAAC;IACrDN,KAAK,CAAC/F,OAAO,CAAEsG,IAAI,IAAKA,IAAI,CAACH,UAAU,CAAChD,GAAG,CAAC,CAACkD,aAAa,CAAC,CAAC,CAAC;IAC7D,OAAOpG,KAAK,CAACsG,cAAc,CAAC,CAAC;EACjC;EACA,OAAOC,gBAAgBA,CAACrD,GAAG,EAAEsD,QAAQ,EAAEnB,KAAK,EAAEoB,QAAQ,EAAEtF,KAAK,EAAEoE,QAAQ,EAAED,MAAM,EAAE;IAC7E,IAAIoB,IAAI,GAAG;MACPnB,QAAQ;MACRC,UAAU,EAAE;IAChB,CAAC;IACD,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;MAC5BoB,IAAI,GAAGhD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+C,IAAI,CAAC,EAAEpB,MAAM,CAAC;IACzD,CAAC,MACI,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;MAClCoB,IAAI,CAACnB,QAAQ,GAAGD,MAAM;IAC1B;IACA,MAAMqB,SAAS,GAAG,IAAI3H,KAAK,CAACT,MAAM,CAACkH,OAAO,CAAC,CAACC,OAAO,CAAC1G,KAAK,CAAC2G,IAAI,CAACC,IAAI,CAAC,CAACC,YAAY,CAAC1E,KAAK,CAAC;IACxF,MAAMyF,QAAQ,GAAG,IAAI5H,KAAK,CAACT,MAAM,CAACkH,OAAO,CAAC,CAACC,OAAO,CAAC1G,KAAK,CAAC2G,IAAI,CAACC,IAAI,CAAC,CAACC,YAAY,CAACY,QAAQ,CAAC;IAC1F,MAAMX,KAAK,GAAGY,IAAI,CAACnB,QAAQ,GAAGtH,IAAI,CAAC8H,gBAAgB,CAACY,SAAS,CAAC,GAAG,EAAE;IACnE,IAAI9G,SAAS,CAAC,CAAC,CACVmG,UAAU,CAAC,CAACW,SAAS,CAAC,CAAC,CACvBX,UAAU,CAAC,CAACY,QAAQ,CAAC,CAAC,CACtBX,aAAa,CAAC,CAACU,SAAS,EAAEC,QAAQ,CAAC,EAAEvB,KAAK,EAAE;MAAEG,UAAU,EAAEkB,IAAI,CAAClB;IAAW,CAAC,CAAC;IACjFmB,SAAS,CAACE,IAAI,CAAC3D,GAAG,EAAEmC,KAAK,CAAC;IAC1BuB,QAAQ,CAACC,IAAI,CAAC3D,GAAG,EAAEsD,QAAQ,CAAC;IAC5BV,KAAK,CAAC/F,OAAO,CAAEsG,IAAI,IAAKA,IAAI,CAACH,UAAU,CAAChD,GAAG,CAAC,CAACkD,aAAa,CAAC,CAAC,CAAC;IAC7D,IAAI9H,cAAc,CAAC+G,KAAK,EAAEmB,QAAQ,CAAC,CAACN,UAAU,CAAChD,GAAG,CAAC,CAACkD,aAAa,CAAC,CAAC;EACvE;EACA,OAAOU,iBAAiBA,CAACC,SAAS,EAAEC,aAAa,EAAEC,YAAY,EAAE;IAC7DF,SAAS,CAAChH,OAAO,CAAC,CAACmH,YAAY,EAAEC,KAAK,KAAK;MACvC,IAAIzI,WAAW,CAACwI,YAAY,CAAC,IAAIA,YAAY,CAACxF,MAAM,CAAC,CAAC,EAAE;QACpD,IAAIwF,YAAY,CAACE,SAAS,CAAC,CAAC,IAAI,CAACH,YAAY,EAAE;UAC3C;QACJ;QACA,MAAMI,IAAI,GAAGH,YAAY,CAACtF,cAAc,CAAC,CAAC;QAC1C,IAAIyF,IAAI,KAAK,CAAC,EAAE;UACZ;QACJ;QACA,IAAIL,aAAa,IAAIE,YAAY,CAACI,OAAO,CAAC,CAAC,EAAE;UACzC,MAAMC,KAAK,GAAGL,YAAY,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,IAAIL,KAAK,KAAK,CAAC,EAAE;YACbI,KAAK,CAACF,IAAI,GAAGnG,2BAA2B,CAAC6F,SAAS,EAAEQ,KAAK,CAACF,IAAI,EAAEF,KAAK,EAAE,KAAK,CAAC;UACjF,CAAC,MACI,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGJ,SAAS,CAAC1H,MAAM,EAAE;YAC5C,MAAMoI,YAAY,GAAGV,SAAS,CAACI,KAAK,GAAG,CAAC,CAAC;YACzC,IAAIzI,WAAW,CAAC+I,YAAY,CAAC,EAAE;cAC3B,IAAIA,YAAY,CAAC/F,MAAM,CAAC,CAAC,EAAE;gBACvB6F,KAAK,CAACF,IAAI,GAAGI,YAAY,CAACD,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACH,IAAI;cACnD,CAAC,MACI;gBACD,MAAMK,QAAQ,GAAGD,YAAY,CAAC7F,cAAc,CAAC,CAAC;gBAC9C2F,KAAK,CAACF,IAAI,GAAGnG,2BAA2B,CAAC6F,SAAS,EAAEW,QAAQ,EAAEP,KAAK,EAAE,IAAI,CAAC;cAC9E;YACJ;UACJ;UACAD,YAAY,CAACS,UAAU,CAAC,CAAC,EAAEJ,KAAK,CAACF,IAAI,CAAC;QAC1C;MACJ;IACJ,CAAC,CAAC;EACN;EACAO,WAAWA,CAACrE,OAAO,EAAE;IACjB,IAAI,CAACsE,gBAAgB,GAAGnE,MAAM,CAACC,MAAM,CAAC;MAAEmE,aAAa,EAAE,KAAK;MAAEC,aAAa,EAAExJ,MAAM,CAACyJ,cAAc;MAAEC,aAAa,EAAE;IAAE,CAAC,EAAE1E,OAAO,CAAC;IAChI,IAAI,CAAC2E,YAAY,GAAG,CAAC;IACrB,IAAI,CAACnD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACkD,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG;MAChBhJ,GAAG,EAAE,CAAC,CAAC;MACPC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,oBAAoB,EAAE;IAC1B,CAAC;IACD,IAAI,CAAC8I,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACzE,WAAW,GAAG;MACfoB,KAAK,EAAE,CAAC;MACRR,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACxF,MAAM,GAAG,EAAE;IAChB,IAAI,CAACsJ,WAAW,GAAG,EAAE;EACzB;EACAhD,UAAUA,CAACtG,MAAM,EAAE8H,aAAa,EAAE;IAC9B,IAAI,CAAC9H,MAAM,IAAI,CAACA,MAAM,CAACG,MAAM,EAAE;MAC3B,MAAM,IAAIP,YAAY,CAAC,aAAa,EAAE,2BAA2B,CAAC;IACtE;IACAI,MAAM,CAACa,OAAO,CAAEC,KAAK,IAAKH,SAAS,CAACiH,iBAAiB,CAAC9G,KAAK,CAACG,YAAY,CAAC,CAAC,EAAE6G,aAAa,CAAC,CAAC;EAC/F;EACAyB,yBAAyBA,CAACvJ,MAAM,EAAE;IAC9B,MAAMwJ,gBAAgB,GAAGvK,OAAO,CAAC0F,GAAG,CAAC,4BAA4B,CAAC;IAClE,IAAI8E,iBAAiB,GAAG,CAAC;IACzB,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,IAAI,GAAG,CAAC;IACZ,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,SAAS,GAAG,EAAE;IACpB,IAAI,IAAI,CAACV,gBAAgB,EACrB,OAAO,IAAI,CAACD,aAAa;IAC7B,IAAI,CAAClJ,MAAM,EAAE;MACT,MAAM,IAAIJ,YAAY,CAAC,aAAa,EAAE,oDAAoD,CAAC;IAC/F;IACA,IAAI,CAACkK,kBAAkB,CAAC9J,MAAM,CAAC;IAC/B,MAAM;MAAEM,IAAI,EAAEyJ,WAAW;MAAE3J,GAAG,EAAE4J;IAAW,CAAC,GAAG,IAAI,CAACZ,YAAY;IAChE,IAAI,CAACF,aAAa,GAAG,CAAC;IACtBa,WAAW,CAAClJ,OAAO,CAAEoJ,IAAI,IAAK;MAC1B,MAAMC,OAAO,GAAGF,UAAU,CAACC,IAAI,CAAC;MAChCC,OAAO,CAAC1G,SAAS,CAAC,CAAC;MACnB,IAAI0G,OAAO,CAACjJ,YAAY,CAAC,CAAC,CAACd,MAAM,GAAGH,MAAM,CAACG,MAAM,EAAE;QAC/CsJ,iBAAiB,IAAI,CAAC;MAC1B;MACAS,OAAO,CAACjJ,YAAY,CAAC,CAAC,CAACJ,OAAO,CAAEsJ,CAAC,IAAK;QAClCT,IAAI,IAAIS,CAAC,CAACzG,UAAU,CAAC,CAAC,CAAC0G,KAAK;QAC5BT,IAAI,IAAIQ,CAAC,CAAC1I,QAAQ,CAAC,CAAC,CAAC4I,KAAK,CAAC,CAAC;QAC5BT,MAAM,CAACrI,IAAI,CAAC4I,CAAC,CAACzG,UAAU,CAAC,CAAC,CAAC0G,KAAK,CAAC;QACjCP,SAAS,CAACtI,IAAI,CAAC4I,CAAC,CAAC1I,QAAQ,CAAC,CAAC,CAAC4I,KAAK,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MACF,MAAMD,KAAK,GAAGF,OAAO,CAACrG,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACqF,aAAa,IAAIkB,KAAK;IAC/B,CAAC,CAAC;IACF,IAAI,CAACjB,gBAAgB,GAAG,IAAI;IAC5B,MAAMmB,IAAI,GAAGZ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGE,MAAM,CAACzJ,MAAM,GAAG,CAAC,GAAGyJ,MAAM,CAACzJ,MAAM;IAChE,MAAMoK,IAAI,GAAG1K,QAAQ,CAAC+J,MAAM,CAACxJ,GAAG,CAAEoK,EAAE,IAAK5H,IAAI,CAAC6H,GAAG,CAACD,EAAE,GAAGF,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACjE,MAAMI,KAAK,GAAG9H,IAAI,CAAC6H,GAAG,CAACF,IAAI,GAAGX,MAAM,CAACzJ,MAAM,EAAE,GAAG,CAAC,GAAGmK,IAAI;IACxD,MAAMK,IAAI,GAAGhB,IAAI,GAAGE,SAAS,CAAC1J,MAAM;IACpC,MAAMyK,IAAI,GAAG/K,QAAQ,CAACgK,SAAS,CAACzJ,GAAG,CAAEoK,EAAE,IAAK5H,IAAI,CAAC6H,GAAG,CAACD,EAAE,GAAGG,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACpE,MAAME,KAAK,GAAGjI,IAAI,CAAC6H,GAAG,CAACG,IAAI,GAAGf,SAAS,CAAC1J,MAAM,EAAE,GAAG,CAAC,GAAGwK,IAAI;IAC3D,MAAMG,MAAM,GAAGlI,IAAI,CAACC,GAAG,CAACgI,KAAK,EAAEH,KAAK,CAAC,GAAGX,WAAW,CAAC5J,MAAM,GAAGqJ,gBAAgB;IAC7E,MAAMuB,YAAY,GAAGvB,gBAAgB,GAAGC,iBAAiB;IACzD,OAAO,IAAI,CAACP,aAAa,GAAGtG,IAAI,CAACC,GAAG,CAACkI,YAAY,EAAED,MAAM,CAAC;EAC9D;EACAE,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC7B,gBAAgB,EAAE;MACxB,MAAM,IAAIvJ,YAAY,CAAC,iBAAiB,EAAE,mFAAmF,CAAC;IAClI;IACA,OAAO,IAAI,CAACsJ,aAAa;EAC7B;EACA,OAAOtI,uBAAuBA,CAACZ,MAAM,EAAE;IACnC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACG,MAAM,EAAE;MAC3B,MAAM,IAAIP,YAAY,CAAC,aAAa,EAAE,qBAAqB,CAAC;IAChE;IACA,MAAMqL,UAAU,GAAGjL,MAAM,CAAC,CAAC,CAAC,CAACkL,aAAa,CAAC,CAAC;IAC5C,MAAM3K,oBAAoB,GAAGP,MAAM,CAACmD,MAAM,CAAC,CAACC,WAAW,EAAEtC,KAAK,KAAK;MAC/D,IAAI,CAACA,KAAK,CAACoK,aAAa,CAAC,CAAC,CAACC,MAAM,CAACF,UAAU,CAAC,EAAE;QAC3C,MAAM,IAAIrL,YAAY,CAAC,cAAc,EAAE,uDAAuD,CAAC;MACnG;MACA,IAAIkB,KAAK,CAACsK,OAAO,CAAC,CAAC,KAAKtL,KAAK,CAAC2G,IAAI,CAAC4E,MAAM,IAAI,CAACvK,KAAK,CAACwK,UAAU,CAAC,CAAC,EAAE;QAC9D,MAAM,IAAI1L,YAAY,CAAC,iBAAiB,EAAE,mCAAmC,CAAC;MAClF;MACA,OAAOgD,IAAI,CAACC,GAAG,CAACO,WAAW,EAAEpE,QAAQ,CAACuM,GAAG,CAACnI,WAAW,EAAEtC,KAAK,CAACF,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAC5F,CAAC,EAAE,CAAC,CAAC;IACL,OAAOL,oBAAoB;EAC/B;EACAiL,sBAAsBA,CAACxL,MAAM,EAAE;IAC3B,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EACnB;IACJ,MAAMI,oBAAoB,GAAGI,SAAS,CAACC,uBAAuB,CAACZ,MAAM,CAAC;IACtE,MAAMQ,gBAAgB,GAAG,IAAIiL,GAAG,CAAC,CAAC;IAClC,MAAM/K,QAAQ,GAAG,EAAE;IACnBV,MAAM,CAACa,OAAO,CAAEC,KAAK,IAAK;MACtB,MAAME,SAAS,GAAG,IAAIhC,QAAQ,CAAC,CAAC,EAAEuB,oBAAoB,CAAC;MACvDO,KAAK,CAACG,YAAY,CAAC,CAAC,CAACJ,OAAO,CAAEK,QAAQ,IAAK;QACvC,MAAMC,YAAY,GAAGH,SAAS,CAACI,SAAS;QACxC,IAAIsK,qBAAqB,GAAGlL,gBAAgB,CAACmE,GAAG,CAACzD,QAAQ,CAACyK,QAAQ,CAAC,CAAC,CAAC;QACrE,IAAI,CAACD,qBAAqB,EAAE;UACxBlL,gBAAgB,CAACoL,GAAG,CAAC1K,QAAQ,CAACyK,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC7CD,qBAAqB,GAAGlL,gBAAgB,CAACmE,GAAG,CAACzD,QAAQ,CAACyK,QAAQ,CAAC,CAAC,CAAC;QACrE;QACA,IAAI,EAAED,qBAAqB,GAAGA,qBAAqB,CAACvK,YAAY,CAAC,GAAG0K,SAAS,CAAC,EAAE;UAC5E,MAAMxK,UAAU,GAAG,IAAInC,eAAe,CAAC,CAAC;UACxCwB,QAAQ,CAACa,IAAI,CAACF,UAAU,CAAC;UACzBqK,qBAAqB,CAACvK,YAAY,CAAC,GAAGE,UAAU;QACpD;QACAH,QAAQ,CAACmC,oBAAoB,CAACqI,qBAAqB,CAACvK,YAAY,CAAC,CAAC;QAClEH,SAAS,CAACQ,GAAG,CAACN,QAAQ,CAACO,QAAQ,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC4H,gBAAgB,CAAC9H,IAAI,CAAC;MACvBnB,GAAG,EAAEI,gBAAgB;MACrBH,KAAK,EAAEK,QAAQ;MACfH;IACJ,CAAC,CAAC;EACN;EACAuJ,kBAAkBA,CAAC9J,MAAM,EAAE;IACvB,MAAM8L,EAAE,GAAGA,CAAC5K,QAAQ,EAAEgJ,OAAO,EAAEnJ,UAAU,KAAKmJ,OAAO,CAAC3G,WAAW,CAACrC,QAAQ,EAAEH,UAAU,CAAC;IACvF,MAAML,QAAQ,GAAGX,cAAc,CAACC,MAAM,EAAGiK,IAAI,IAAK,IAAI3K,WAAW,CAAC2K,IAAI,CAAC,EAAE6B,EAAE,CAAC;IAC5E,IAAI,CAAC1C,YAAY,GAAG1I,QAAQ;IAC5B,MAAMqL,YAAY,GAAG,IAAI,CAAC3C,YAAY,CAAC/I,KAAK;IAC5C0L,YAAY,CAAClL,OAAO,CAAEqJ,OAAO,IAAK;MAC9BA,OAAO,CAAC8B,SAAS,GAAGD,YAAY;IACpC,CAAC,CAAC;IACF,OAAOrL,QAAQ;EACnB;EACAuL,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7C,YAAY;EAC5B;EACA5F,SAASA,CAACwF,YAAY,GAAG,CAAC,EAAEkD,gBAAgB,EAAEC,WAAW,EAAEhG,KAAK,EAAE;IAC9D,MAAMzF,QAAQ,GAAG,IAAI,CAAC0I,YAAY;IAClC,IAAI,CAAC1I,QAAQ,EAAE;MACX,MAAM,IAAId,YAAY,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;IAC/E;IACA,MAAM;MAAEU,IAAI,EAAEyJ,WAAW;MAAE3J,GAAG,EAAE4J;IAAW,CAAC,GAAGtJ,QAAQ;IACvD,IAAI,CAAC4I,WAAW,GAAG,EAAE;IACrB,IAAI6C,WAAW,IAAIhG,KAAK,EAAE;MACtBgG,WAAW,CAACtL,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACmG,QAAQ,CAACd,KAAK,CAAC,CAAC3C,SAAS,CAAC,CAAC,CAAC;IACrE;IACA,IAAIP,CAAC,GAAG,CAAC;IACT,IAAImJ,KAAK,GAAG,CAAC;IACb,IAAI,CAAClD,aAAa,GAAG,CAAC;IACtB,IAAI+B,UAAU,GAAG,CAAC;IAClBlB,WAAW,CAAClJ,OAAO,CAAEoJ,IAAI,IAAK;MAC1B,MAAMC,OAAO,GAAGF,UAAU,CAACC,IAAI,CAAC;MAChCC,OAAO,CAAC1G,SAAS,CAAC,CAAC;MACnB,MAAM4G,KAAK,GAAGF,OAAO,CAACrG,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACqF,aAAa,IAAIkB,KAAK;MAC3B,MAAMiC,QAAQ,GAAGnC,OAAO,CAACoC,WAAW,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC;MAC9CY,UAAU,IAAIoB,QAAQ;MACtB,MAAM5I,OAAO,GAAGyG,OAAO,CAACxG,UAAU,CAAC,CAAC;MACpCT,CAAC,GAAGA,CAAC,GAAGmJ,KAAK,GAAG3I,OAAO,CAACG,WAAW;MACnCsG,OAAO,CAACvG,IAAI,CAACV,CAAC,CAAC;MACfmJ,KAAK,GAAGhC,KAAK,GAAG3G,OAAO,CAACG,WAAW;IACvC,CAAC,CAAC;IACF,MAAM;MAAEgF,aAAa;MAAEC,aAAa;MAAEE;IAAc,CAAC,GAAG,IAAI,CAACJ,gBAAgB;IAC7E,MAAM4D,GAAG,GAAItC,IAAI,IAAKrH,IAAI,CAAC6H,GAAG,CAAC5B,aAAa,EAAGmB,UAAU,CAACC,IAAI,CAAC,CAACqC,WAAW,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC,GAAGY,UAAW,CAAC;IACpG,MAAMuB,YAAY,GAAG3M,QAAQ,CAACkK,WAAW,CAAC3J,GAAG,CAACmM,GAAG,CAAC,CAAC;IACnD,IAAI,CAACrD,aAAa,GAAGjG,CAAC,GAAGmJ,KAAK;IAC9B,IAAI,CAACjD,gBAAgB,GAAG,IAAI;IAC5B,IAAIH,YAAY,IAAI,CAAC,EACjB,OAAO,IAAI,CAACyD,QAAQ,CAAC,CAAC;IAC1B,MAAMC,YAAY,GAAG1C,UAAU,CAACD,WAAW,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM4C,WAAW,GAAG3C,UAAU,CAACD,WAAW,CAACA,WAAW,CAAC5J,MAAM,GAAG,CAAC,CAAC,CAAC;IACnE,SAASyM,uBAAuBA,CAACC,oBAAoB,EAAE;MACnD,MAAMC,SAAS,GAAG/C,WAAW,CAAC3J,GAAG,CAAC,CAAC6J,IAAI,EAAE8C,CAAC,KAAK;QAC3C,MAAM7C,OAAO,GAAGF,UAAU,CAACC,IAAI,CAAC;QAChC,MAAMjK,MAAM,GAAGkK,OAAO,CAAC8C,mBAAmB,CAAC,CAAC;QAC5C,IAAIC,YAAY;QAChB,IAAIF,CAAC,GAAG,CAAC,EAAE;UACP,MAAMG,WAAW,GAAGlD,UAAU,CAACD,WAAW,CAACgD,CAAC,GAAG,CAAC,CAAC,CAAC;UAClD,KAAK,IAAII,CAAC,GAAGJ,CAAC,GAAG,CAAC,EAAEI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC7B,MAAMC,QAAQ,GAAGpD,UAAU,CAACD,WAAW,CAACoD,CAAC,CAAC,CAAC;YAC3C,MAAME,UAAU,GAAGD,QAAQ,CAACJ,mBAAmB,CAAC,CAAC;YACjD,MAAMM,cAAc,GAAG,EAAE;YACzB9I,MAAM,CAAC+I,IAAI,CAACvN,MAAM,CAAC,CAACa,OAAO,CAAE2M,CAAC,IAAK;cAC/B,IAAIH,UAAU,CAACG,CAAC,CAAC,EAAE;gBACfF,cAAc,CAAC/L,IAAI,CAACiM,CAAC,CAAC;cAC1B;YACJ,CAAC,CAAC;YACF,IAAIF,cAAc,CAACnN,MAAM,GAAG,CAAC,EAAE;cAC3B,IAAIkM,QAAQ,GAAG,CAAC;cAChB,IAAIoB,kBAAkB,GAAGC,QAAQ;cACjC,IAAIC,gBAAgB,GAAG,CAAC;cACxBL,cAAc,CAACzM,OAAO,CAAE2M,CAAC,IAAK;gBAC1B,MAAMI,KAAK,GAAGP,UAAU,CAACG,CAAC,CAAC,CAAC/L,QAAQ,CAAC,CAAC,CAAC4I,KAAK,CAAC,CAAC;gBAC9C,IAAIuD,KAAK,GAAGvB,QAAQ,EAAE;kBAClBY,YAAY,GAAGI,UAAU,CAACG,CAAC,CAAC;kBAC5BnB,QAAQ,GAAGuB,KAAK;gBACpB;gBACA,MAAMC,YAAY,GAAG7N,MAAM,CAACwN,CAAC,CAAC;gBAC9B,MAAMM,cAAc,GAAGD,YAAY,CAACE,IAAI,CAAC,CAAC,IACrCF,YAAY,CAACnK,UAAU,CAAC,CAAC,CAACsK,SAAS,GAAGH,YAAY,CAACnK,UAAU,CAAC,CAAC,CAACuK,mBAAmB,CAAC;gBACzF,MAAMC,WAAW,GAAGb,UAAU,CAACG,CAAC,CAAC,CAAC9J,UAAU,CAAC,CAAC;gBAC9C,MAAMyK,eAAe,GAAGd,UAAU,CAACG,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,GAAGG,WAAW,CAACE,MAAM,GAAGF,WAAW,CAACG,UAAU,GAAGH,WAAW,CAACI,oBAAoB;gBAC7Hb,kBAAkB,GAAG7K,IAAI,CAACG,GAAG,CAAC0K,kBAAkB,EAAEK,cAAc,GAAGK,eAAe,CAAC;cACvF,CAAC,CAAC;cACFV,kBAAkB,GAAG7K,IAAI,CAACG,GAAG,CAAC0K,kBAAkB,EAAEvD,OAAO,CAAC6D,IAAI,CAAC,CAAC,IAAIb,WAAW,CAACa,IAAI,CAAC,CAAC,GAAGlB,oBAAoB,GAAG,IAAI,CAAC,CAAC;cACtH,IAAIjE,aAAa,EAAE;gBACf,MAAMuB,CAAC,GAAGc,UAAU;gBACpB0C,gBAAgB,GAAI/K,IAAI,CAAC6H,GAAG,CAAC5B,aAAa,EAAGwD,QAAQ,GAAGlC,CAAE,CAAC,GAAGqC,YAAY,GAAIK,oBAAoB;cACtG,CAAC,MACI,IAAI,OAAOI,YAAY,KAAK,WAAW,EAAE;gBAC1CU,gBAAgB,GAAGV,YAAY,CAACsB,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACnC,QAAQ,CAAC,GAAGQ,oBAAoB;cACvF;cACA,OAAO;gBACHc,gBAAgB;gBAChBF,kBAAkB;gBAClBgB,YAAY,EAAExB;cAClB,CAAC;YACL;UACJ;QACJ;QACA,OAAO;UAAEU,gBAAgB,EAAE,CAAC;UAAEe,cAAc,EAAE,CAAC;UAAEjB,kBAAkB,EAAE;QAAE,CAAC;MAC5E,CAAC,CAAC;MACF,OAAOX,SAAS;IACpB;IACA,SAAS6B,qBAAqBA,CAACC,cAAc,EAAE;MAC3C,MAAMC,OAAO,GAAGhC,oBAAoB,GAAG,CAAC;MACxC,IAAIiC,UAAU,GAAG,CAAC;MAClB/E,WAAW,CAAClJ,OAAO,CAAC,CAACoJ,IAAI,EAAEhC,KAAK,KAAK;QACjC,MAAMiC,OAAO,GAAGF,UAAU,CAACC,IAAI,CAAC;QAChC,IAAIhC,KAAK,GAAG,CAAC,EAAE;UACX,MAAM8G,QAAQ,GAAG7E,OAAO,CAAC6D,IAAI,CAAC,CAAC;UAC/B,MAAMiB,KAAK,GAAGJ,cAAc,CAAC3G,KAAK,CAAC;UACnC,MAAMgH,OAAO,GAAGxP,OAAO,CAACuP,KAAK,CAACP,YAAY,CAAC,CAACV,IAAI,CAAC,CAAC,GAAGiB,KAAK,CAACrB,gBAAgB,IAAIoB,QAAQ,GAAGD,UAAU,CAAC;UACrG,IAAII,eAAe,GAAG,CAAC;UACvB,IAAID,OAAO,GAAG,CAAC,EAAE;YACbH,UAAU,IAAIG,OAAO;UACzB,CAAC,MACI,IAAIA,OAAO,GAAG,CAAC,EAAE;YAClBC,eAAe,GAAGtM,IAAI,CAACG,GAAG,CAACiM,KAAK,CAACvB,kBAAkB,EAAE7K,IAAI,CAACuM,GAAG,CAACF,OAAO,CAAC,CAAC;YACvEH,UAAU,IAAI,CAACI,eAAe;UAClC;UACAhF,OAAO,CAACvG,IAAI,CAACoL,QAAQ,GAAGD,UAAU,CAAC;QACvC;QACA5E,OAAO,CAACkF,yBAAyB,CAAC,CAAC,CAACvO,OAAO,CAAEK,QAAQ,IAAK;UACtDA,QAAQ,CAACmO,eAAe,CAACR,OAAO,GAAG3E,OAAO,CAAC6D,IAAI,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAOpB,WAAW,CAACoB,IAAI,CAAC,CAAC,GAAGrB,YAAY,CAACqB,IAAI,CAAC,CAAC;IACnD;IACA,MAAMlB,oBAAoB,GAAG7D,YAAY,GACrC2D,WAAW,CAACjJ,UAAU,CAAC,CAAC,CAAC0K,MAAM,GAC/BzB,WAAW,CAACjJ,UAAU,CAAC,CAAC,CAACI,YAAY,GACrC4I,YAAY,CAAChJ,UAAU,CAAC,CAAC,CAACE,WAAW;IACzC,MAAM0L,gBAAgB,GAAGrQ,OAAO,CAAC0F,GAAG,CAAC,qBAAqB,CAAC;IAC3D,MAAM4K,gBAAgB,GAAGtQ,OAAO,CAAC0F,GAAG,CAAC,qBAAqB,CAAC;IAC3D,MAAM6K,WAAW,GAAGvQ,OAAO,CAAC0F,GAAG,CAAC,eAAe,CAAC;IAChD,IAAI8K,WAAW,GAAG5C,oBAAoB;IACtC,MAAMC,SAAS,GAAGF,uBAAuB,CAAC6C,WAAW,CAAC;IACtD,IAAIC,WAAW,GAAGf,qBAAqB,CAAC7B,SAAS,CAAC;IAClD,IAAI/C,WAAW,CAAC5J,MAAM,KAAK,CAAC,EACxB,OAAO,CAAC;IACZ,MAAMwP,eAAe,GAAGA,CAACF,WAAW,EAAE3C,SAAS,KAAK;MAChD,IAAI8C,MAAM,GAAGH,WAAW,GAAG,CAAC;MAC5B,IAAI3C,SAAS,CAAC3M,MAAM,GAAG,CAAC,EAAE;QACtB,KAAK,IAAI0P,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG/C,SAAS,CAAC3M,MAAM,EAAE,EAAE0P,EAAE,EAAE;UAC1CD,MAAM,GAAGhN,IAAI,CAACG,GAAG,CAAC+J,SAAS,CAAC+C,EAAE,CAAC,CAAClC,gBAAgB,GAAG,CAAC,EAAEiC,MAAM,CAAC;QACjE;MACJ;MACA,OAAOA,MAAM;IACjB,CAAC;IACD,MAAME,WAAW,GAAGH,eAAe,CAACF,WAAW,EAAE3C,SAAS,CAAC;IAC3D,MAAMiD,cAAc,GAAIC,cAAc,IAAK;MACvC,IAAIC,mBAAmB,GAAG,CAAC;MAC3B,MAAMC,YAAY,GAAGvD,WAAW,IAAIA,WAAW,CAACwD,cAAc,CAAC,CAAC;MAChE,IAAID,YAAY,EAAE;QACd,MAAMpP,KAAK,GAAGoP,YAAY,CAAC3B,QAAQ,CAAC,CAAC;QACrC,IAAIzN,KAAK,CAACsP,YAAY,CAAC,CAAC,CAAC/F,KAAK,CAAC,CAAC,GAAGvJ,KAAK,CAACoK,aAAa,CAAC,CAAC,CAACb,KAAK,CAAC,CAAC,EAAE;UAC9D,OAAOkF,gBAAgB,GAAG,CAAC,GAAGO,WAAW,GAAGA,WAAW,GAAGP,gBAAgB;QAC9E;QACA,MAAMc,SAAS,GAAGH,YAAY,CAACrM,QAAQ,CAAC,CAAC;QACzCoM,mBAAmB,GACfnP,KAAK,CAAC0N,OAAO,CAAC7B,WAAW,CAACL,WAAW,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC,CAAC,GAAG2F,cAAc,IAAIK,SAAS,GAAGb,WAAW,CAAC;MACrG;MACA,OAAOD,gBAAgB,GAAG,CAAC,GAAGU,mBAAmB,GAAGA,mBAAmB,GAAGV,gBAAgB;IAC9F,CAAC;IACD,IAAIe,UAAU,GAAGP,cAAc,CAACN,WAAW,CAAC;IAC5C,IAAIc,UAAU,GAAGD,UAAU,IAAIf,gBAAgB,GAAGD,gBAAgB,CAAC;IACnE,MAAMkB,IAAI,GAAG3D,oBAAoB,GAAG0D,UAAU;IAC9C,IAAIE,UAAU,GAAG1H,aAAa;IAC9B,OAAQ2G,WAAW,GAAGc,IAAI,IAAIC,UAAU,GAAG,CAAC,IAAMf,WAAW,GAAGY,UAAU,GAAGE,IAAI,IAAIC,UAAU,GAAG,CAAE,EAAE;MAClGhB,WAAW,IAAIC,WAAW,GAAGc,IAAI;MACjCF,UAAU,GAAGP,cAAc,CAACN,WAAW,CAAC;MACxCc,UAAU,GAAGD,UAAU,IAAIf,gBAAgB,GAAGD,gBAAgB,CAAC;MAC/DI,WAAW,GAAGf,qBAAqB,CAAC/B,uBAAuB,CAAC6C,WAAW,CAAC,CAAC;MACzEgB,UAAU,EAAE;IAChB;IACA,IAAI,CAACzH,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI,CAACyD,QAAQ,CAAC,CAAC;EAC1B;EACAA,QAAQA,CAAA,EAAG;IACP,MAAM/L,QAAQ,GAAG,IAAI,CAAC0I,YAAY;IAClC,MAAMJ,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAI,CAACpE,WAAW,GAAG;MAAEoB,KAAK,EAAE,CAAC;MAAER,IAAI,EAAE;IAAG,CAAC;IACzC9E,QAAQ,CAACJ,IAAI,CAACO,OAAO,CAAC,CAACoJ,IAAI,EAAEhC,KAAK,KAAK;MACnC,IAAIA,KAAK,KAAK,CAAC,EACX;MACJ,MAAMyI,QAAQ,GAAGhQ,QAAQ,CAACJ,IAAI,CAAC2H,KAAK,GAAG,CAAC,CAAC;MACzC,MAAMiF,WAAW,GAAGxM,QAAQ,CAACN,GAAG,CAACsQ,QAAQ,CAAC;MAC1C,MAAMxG,OAAO,GAAGxJ,QAAQ,CAACN,GAAG,CAAC6J,IAAI,CAAC;MAClC,MAAM0G,WAAW,GAAGzD,WAAW,CAACxJ,UAAU,CAAC,CAAC;MAC5C,MAAMkN,WAAW,GAAG1G,OAAO,CAACxG,UAAU,CAAC,CAAC;MACxC,MAAMyK,eAAe,GAAGjB,WAAW,CAACa,IAAI,CAAC,CAAC,GAAG4C,WAAW,CAACvC,MAAM,GAAGuC,WAAW,CAAC7M,YAAY;MAC1F,MAAMgK,cAAc,GAAG5D,OAAO,CAAC6D,IAAI,CAAC,CAAC,GAAG6C,WAAW,CAAChN,WAAW;MAC/D,MAAM6B,GAAG,GAAGqI,cAAc,GAAGK,eAAe;MAC5C,IAAI,CAACvJ,WAAW,CAACoB,KAAK,IAAIP,GAAG;MAC7B,IAAI,CAACb,WAAW,CAACY,IAAI,CAACjE,IAAI,CAAC;QAAEuD,EAAE,EAAEqJ,eAAe;QAAEpJ,EAAE,EAAE+I;MAAe,CAAC,CAAC;MACvE5D,OAAO,CAAC2G,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACC,IAAI,GAAGtL,GAAG;MAChDyH,WAAW,CAAC2D,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACE,KAAK,GAAGvL,GAAG;IACzD,CAAC,CAAC;IACF,IAAI,CAACwD,aAAa,GAAG,CAAC,CAAC;IACvB,MAAMA,aAAa,GAAG,IAAI,CAACA,aAAa;IACxC,SAASgI,WAAWA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MAClC,MAAMC,KAAK,GAAGnI,aAAa,CAACiI,QAAQ,CAAC;MACrC,IAAIE,KAAK,KAAKvF,SAAS,EAAE;QACrB5C,aAAa,CAACiI,QAAQ,CAAC,GAAG;UAAEG,IAAI,EAAEF,KAAK;UAAEG,KAAK,EAAE,CAAC;UAAEtL,KAAK,EAAEmL;QAAM,CAAC;MACrE,CAAC,MACI;QACDC,KAAK,CAACE,KAAK,IAAI,CAAC;QAChBF,KAAK,CAACpL,KAAK,IAAImL,KAAK;QACpBC,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACpL,KAAK,GAAGoL,KAAK,CAACE,KAAK;MAC1C;IACJ;IACA,IAAI,CAACtR,MAAM,CAACa,OAAO,CAAEC,KAAK,IAAK;MAC3BA,KAAK,CAACG,YAAY,CAAC,CAAC,CAACJ,OAAO,CAAC,CAAC0B,IAAI,EAAEwK,CAAC,EAAE9K,KAAK,KAAK;QAC7C,MAAMiP,QAAQ,GAAG3O,IAAI,CAACd,QAAQ,CAAC,CAAC,CAAC8P,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC5L,QAAQ,CAAC,CAAC;QAC9D,MAAMnC,OAAO,GAAGlB,IAAI,CAACmB,UAAU,CAAC,CAAC;QACjC,MAAM+N,gBAAgB,GAAGlP,IAAI,CAACsO,mBAAmB,CAAC,CAAC;QACnD,MAAMa,YAAY,GAAGnP,IAAI,CAACwL,IAAI,CAAC,CAAC,GAAGtK,OAAO,CAAC2K,MAAM,GAAG3K,OAAO,CAAC4K,UAAU,GAAG5K,OAAO,CAAC6K,oBAAoB;QACrG,IAAI6C,KAAK,GAAG,CAAC;QACb,IAAIpE,CAAC,GAAG9K,KAAK,CAAC9B,MAAM,GAAG,CAAC,EAAE;UACtB,MAAMwR,SAAS,GAAG1P,KAAK,CAAC8K,CAAC,GAAG,CAAC,CAAC;UAC9B,MAAM6E,YAAY,GAAGD,SAAS,CAACjO,UAAU,CAAC,CAAC;UAC3C,MAAMmO,aAAa,GAAGF,SAAS,CAAC5D,IAAI,CAAC,CAAC,GAAG6D,YAAY,CAAC5D,SAAS,GAAG4D,YAAY,CAAC3D,mBAAmB;UAClGkD,KAAK,GAAGU,aAAa,GAAGH,YAAY;UACpCD,gBAAgB,CAACN,KAAK,CAACW,IAAI,GAAGH,SAAS,CAAC5D,IAAI,CAAC,CAAC,GAAGxL,IAAI,CAACwL,IAAI,CAAC,CAAC;UAC5D4D,SAAS,CAACd,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACC,IAAI,GAAGI,KAAK;QACxD,CAAC,MACI;UACDA,KAAK,GAAGnI,YAAY,GAAG0I,YAAY;UACnCD,gBAAgB,CAACN,KAAK,CAACW,IAAI,GAAG9I,YAAY,GAAGzG,IAAI,CAACwL,IAAI,CAAC,CAAC;QAC5D;QACA0D,gBAAgB,CAACX,OAAO,CAACE,KAAK,GAAGG,KAAK;QACtCF,WAAW,CAACC,QAAQ,EAAEO,gBAAgB,CAACN,KAAK,CAACW,IAAI,CAAC;MACtD,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAI,CAAC/R,MAAM,CAACa,OAAO,CAAEC,KAAK,IAAK;MAC3BA,KAAK,CAACG,YAAY,CAAC,CAAC,CAACJ,OAAO,CAAE0B,IAAI,IAAK;QACnC,MAAM2O,QAAQ,GAAG3O,IAAI,CAACd,QAAQ,CAAC,CAAC,CAAC8P,KAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC5L,QAAQ,CAAC,CAAC;QAC9D,MAAMnC,OAAO,GAAGlB,IAAI,CAACsO,mBAAmB,CAAC,CAAC;QAC1CpN,OAAO,CAAC0N,KAAK,CAACE,IAAI,GAAGpI,aAAa,CAACiI,QAAQ,CAAC,CAACG,IAAI;QACjD5N,OAAO,CAACyN,QAAQ,GAAGA,QAAQ;QAC3BzN,OAAO,CAACgN,UAAU,IAAI,CAAC;QACvBhN,OAAO,CAAC0N,KAAK,CAACa,SAAS,GAAGvO,OAAO,CAAC0N,KAAK,CAACW,IAAI,GAAGrO,OAAO,CAAC0N,KAAK,CAACE,IAAI;QACjEU,cAAc,IAAInP,IAAI,CAAC6H,GAAG,CAAChH,OAAO,CAAC0N,KAAK,CAACa,SAAS,EAAE,CAAC,CAAC;MAC1D,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACnM,SAAS,GAAGjD,IAAI,CAACqP,IAAI,CAACF,cAAc,CAAC;IAC1C,IAAI,CAACzI,WAAW,CAAC/H,IAAI,CAAC,IAAI,CAACsE,SAAS,CAAC;IACrC,OAAO,IAAI,CAACA,SAAS;EACzB;EACAqM,IAAIA,CAAC7N,OAAO,EAAE;IACV,IAAIC,EAAE;IACN,MAAM5D,QAAQ,GAAG,IAAI,CAAC0I,YAAY;IAClC,IAAI,CAAC1I,QAAQ,EAAE;MACX,OAAO,CAAC;IACZ;IACA,MAAMyR,KAAK,GAAG,CAAC7N,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC8N,KAAK,MAAM,IAAI,IAAI7N,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;IACzH,IAAI8H,KAAK,GAAG,CAAC;IACb,IAAI,CAACrG,UAAU,GAAG,CAAC;IACnBrF,QAAQ,CAACJ,IAAI,CAACO,OAAO,CAAC,CAACoJ,IAAI,EAAEhC,KAAK,EAAE3H,IAAI,KAAK;MACzC,MAAM4J,OAAO,GAAGxJ,QAAQ,CAACN,GAAG,CAAC6J,IAAI,CAAC;MAClC,MAAMiD,WAAW,GAAGjF,KAAK,GAAG,CAAC,GAAGvH,QAAQ,CAACN,GAAG,CAACE,IAAI,CAAC2H,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG4D,SAAS;MACzE,MAAMuG,WAAW,GAAGnK,KAAK,GAAG3H,IAAI,CAACH,MAAM,GAAG,CAAC,GAAGO,QAAQ,CAACN,GAAG,CAACE,IAAI,CAAC2H,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG4D,SAAS;MACvF3B,OAAO,CAACmI,IAAI,CAACjG,KAAK,EAAEc,WAAW,EAAEkF,WAAW,CAAC;MAC7C,MAAME,IAAI,GAAG,CAACpI,OAAO,CAACqI,gBAAgB,CAAC,CAAC;MACxC,IAAID,IAAI,GAAG,CAAC,EAAE;QACVlG,KAAK,GAAG,CAACxJ,IAAI,CAACG,GAAG,CAACmH,OAAO,CAAC2G,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACE,KAAK,EAAEpO,IAAI,CAACuM,GAAG,CAACmD,IAAI,CAAC,CAAC;MAClF,CAAC,MACI,IAAIA,IAAI,GAAG,CAAC,EAAE;QACf,IAAIF,WAAW,EAAE;UACbhG,KAAK,GAAGxJ,IAAI,CAACG,GAAG,CAACqP,WAAW,CAACvB,mBAAmB,CAAC,CAAC,CAACC,OAAO,CAACE,KAAK,EAAEpO,IAAI,CAACuM,GAAG,CAACmD,IAAI,CAAC,CAAC;QACrF,CAAC,MACI;UACDlG,KAAK,GAAG,CAAC;QACb;MACJ;MACAA,KAAK,IAAI+F,KAAK;MACd,IAAI,CAACpM,UAAU,IAAIqG,KAAK;IAC5B,CAAC,CAAC;IACF,OAAO,IAAI,CAACK,QAAQ,CAAC,CAAC;EAC1B;EACA+F,UAAUA,CAAA,EAAG;IACT,IAAI,CAACnJ,gBAAgB,CAACxI,OAAO,CAAEwI,gBAAgB,IAAK;MAChDA,gBAAgB,CAAChJ,KAAK,CAACQ,OAAO,CAAE4R,EAAE,IAAKA,EAAE,CAACD,UAAU,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;IACF,IAAI,CAACpJ,YAAY,CAAC9I,IAAI,CAACO,OAAO,CAAEoJ,IAAI,IAAK;MACrC,IAAI,CAACb,YAAY,CAAChJ,GAAG,CAAC6J,IAAI,CAAC,CAACuI,UAAU,CAAC,CAAC;IAC5C,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA1L,UAAUA,CAAC9G,MAAM,EAAE;IACf,IAAI,CAACwL,sBAAsB,CAACxL,MAAM,CAAC;IACnC,IAAI,CAACmJ,gBAAgB,GAAG,KAAK;IAC7B,OAAO,IAAI;EACf;EACAuJ,MAAMA,CAAC1S,MAAM,EAAEgJ,YAAY,EAAE3E,OAAO,EAAE;IAClC,MAAMmD,IAAI,GAAGhD,MAAM,CAACC,MAAM,CAAC;MAAE6B,UAAU,EAAE;IAAM,CAAC,EAAEjC,OAAO,CAAC;IAC1D,IAAI,CAACrE,MAAM,GAAGA,MAAM;IACpB,MAAM6I,aAAa,GAAG,IAAI,CAACF,gBAAgB,CAACE,aAAa;IACzD,IAAIA,aAAa,EAAE;MACf,IAAI,CAAC7I,MAAM,CAACa,OAAO,CAAE2M,CAAC,IAAKA,CAAC,CAACmF,gBAAgB,CAAC9J,aAAa,CAAC,CAAC;IACjE;IACA,IAAI,CAACvC,UAAU,CAACtG,MAAM,EAAEwH,IAAI,CAAClB,UAAU,CAAC;IACxC,IAAI,CAACwD,kBAAkB,CAAC9J,MAAM,CAAC;IAC/B,IAAI,CAACwD,SAAS,CAACwF,YAAY,EAAExB,IAAI,CAAC0C,OAAO,EAAElK,MAAM,EAAEwH,IAAI,CAACrB,KAAK,CAAC;IAC9D,IAAIqB,IAAI,CAACrB,KAAK,EACV,IAAI,CAACqM,UAAU,CAAC,CAAC;IACrB,OAAO,IAAI;EACf;EACAzL,aAAaA,CAAC/G,MAAM,EAAEmG,KAAK,EAAEyM,YAAY,EAAE;IACvC,MAAMvO,OAAO,GAAGG,MAAM,CAACC,MAAM,CAAC;MAAEyF,OAAO,EAAE/D,KAAK,CAAC0M,UAAU,CAAC;IAAE,CAAC,EAAED,YAAY,CAAC;IAC5E,MAAM5J,YAAY,GAAG7C,KAAK,CAAC2M,WAAW,CAAC,CAAC,GAAG3M,KAAK,CAAC4M,aAAa,CAAC,CAAC,GAAG5T,KAAK,CAAC6T,cAAc;IACvFnR,CAAC,CAAC,8BAA8B,EAAEmH,YAAY,CAAC;IAC/C,OAAO,IAAI,CAAC0J,MAAM,CAAC1S,MAAM,EAAEgJ,YAAY,EAAE3E,OAAO,CAAC;EACrD;EACA4O,cAAcA,CAAChJ,IAAI,EAAE;IACjB,IAAI3F,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAAC8E,YAAY,MAAM,IAAI,IAAI9E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAClE,GAAG,CAAC6J,IAAI,CAAC;EACrF;AACJ;AACAtJ,SAAS,CAACoB,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}