{"ast": null, "code": "import { Clef } from './clef.js';\nimport { Note } from './note.js';\nexport class ClefNote extends Note {\n  static get CATEGORY() {\n    return \"ClefNote\";\n  }\n  constructor(type, size = 'default', annotation) {\n    super({\n      duration: 'b'\n    });\n    this.setType(type, size, annotation);\n    this.ignoreTicks = true;\n  }\n  setType(type, size, annotation) {\n    this.clef = new Clef(type, size, annotation);\n    this.setWidth(this.clef.getWidth());\n    return this;\n  }\n  getClef() {\n    return this.clef;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = this.checkContext();\n    this.setRendered();\n    this.clef.setX(this.getAbsoluteX());\n    this.clef.setY(stave.getYForLine(this.clef.line));\n    this.clef.renderText(ctx, 0, 0);\n  }\n  getBoundingBox() {\n    return this.clef.getBoundingBox();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Note", "ClefNote", "CATEGORY", "constructor", "type", "size", "annotation", "duration", "setType", "ignoreTicks", "clef", "<PERSON><PERSON><PERSON><PERSON>", "getWidth", "getClef", "preFormat", "preFormatted", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "setX", "getAbsoluteX", "setY", "getYForLine", "line", "renderText", "getBoundingBox"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/clefnote.js"], "sourcesContent": ["import { Clef } from './clef.js';\nimport { Note } from './note.js';\nexport class ClefNote extends Note {\n    static get CATEGORY() {\n        return \"ClefNote\";\n    }\n    constructor(type, size = 'default', annotation) {\n        super({ duration: 'b' });\n        this.setType(type, size, annotation);\n        this.ignoreTicks = true;\n    }\n    setType(type, size, annotation) {\n        this.clef = new Clef(type, size, annotation);\n        this.setWidth(this.clef.getWidth());\n        return this;\n    }\n    getClef() {\n        return this.clef;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = this.checkContext();\n        this.setRendered();\n        this.clef.setX(this.getAbsoluteX());\n        this.clef.setY(stave.getYForLine(this.clef.line));\n        this.clef.renderText(ctx, 0, 0);\n    }\n    getBoundingBox() {\n        return this.clef.getBoundingBox();\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,IAAI,QAAQ,WAAW;AAChC,OAAO,MAAMC,QAAQ,SAASD,IAAI,CAAC;EAC/B,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACAC,WAAWA,CAACC,IAAI,EAAEC,IAAI,GAAG,SAAS,EAAEC,UAAU,EAAE;IAC5C,KAAK,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,OAAO,CAACJ,IAAI,EAAEC,IAAI,EAAEC,UAAU,CAAC;IACpC,IAAI,CAACG,WAAW,GAAG,IAAI;EAC3B;EACAD,OAAOA,CAACJ,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAE;IAC5B,IAAI,CAACI,IAAI,GAAG,IAAIX,IAAI,CAACK,IAAI,EAAEC,IAAI,EAAEC,UAAU,CAAC;IAC5C,IAAI,CAACK,QAAQ,CAAC,IAAI,CAACD,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;IACnC,OAAO,IAAI;EACf;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACH,IAAI;EACpB;EACAI,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;IACnC,IAAI,CAACb,IAAI,CAACc,IAAI,CAACP,KAAK,CAACQ,WAAW,CAAC,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,CAAC;IACjD,IAAI,CAAChB,IAAI,CAACiB,UAAU,CAACR,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EACnC;EACAS,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClB,IAAI,CAACkB,cAAc,CAAC,CAAC;EACrC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}