{"ast": null, "code": "import { TabNote } from './tabnote.js';\nexport class GraceT<PERSON><PERSON>ote extends TabNote {\n  static get CATEGORY() {\n    return \"GraceTabNote\";\n  }\n  constructor(noteStruct) {\n    super(noteStruct, false);\n    this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), {\n      yShift: 0.3\n    });\n    this.updateWidth();\n  }\n}", "map": {"version": 3, "names": ["TabNote", "GraceTabNote", "CATEGORY", "constructor", "noteStruct", "renderOptions", "Object", "assign", "yShift", "updateWidth"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/gracetabnote.js"], "sourcesContent": ["import { TabNote } from './tabnote.js';\nexport class GraceT<PERSON><PERSON>ote extends TabNote {\n    static get CATEGORY() {\n        return \"GraceTabNote\";\n    }\n    constructor(noteStruct) {\n        super(noteStruct, false);\n        this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), { yShift: 0.3 });\n        this.updateWidth();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,MAAMC,YAAY,SAASD,OAAO,CAAC;EACtC,WAAWE,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACAC,WAAWA,CAACC,UAAU,EAAE;IACpB,KAAK,CAACA,UAAU,EAAE,KAAK,CAAC;IACxB,IAAI,CAACC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,aAAa,CAAC,EAAE;MAAEG,MAAM,EAAE;IAAI,CAAC,CAAC;IAC1F,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}