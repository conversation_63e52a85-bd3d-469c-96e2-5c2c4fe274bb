{"ast": null, "code": "import { Accidental } from './accidental.js';\nimport { Annotation } from './annotation.js';\nimport { Articulation } from './articulation.js';\nimport { Bend } from './bend.js';\nimport { ChordSymbol } from './chordsymbol.js';\nimport { Dot } from './dot.js';\nimport { FretHand<PERSON>inger } from './frethandfinger.js';\nimport { GraceNoteGroup } from './gracenotegroup.js';\nimport { NoteSubGroup } from './notesubgroup.js';\nimport { Ornament } from './ornament.js';\nimport { Parenthesis } from './parenthesis.js';\nimport { StaveNote } from './stavenote.js';\nimport { StringNumber } from './stringnumber.js';\nimport { Stroke } from './strokes.js';\nimport { log, RuntimeError } from './util.js';\nimport { Vibrato } from './vibrato.js';\nfunction L(...args) {\n  if (ModifierContext.DEBUG) log('VexFlow.ModifierContext', args);\n}\nexport class ModifierContext {\n  constructor() {\n    this.state = {\n      leftShift: 0,\n      rightShift: 0,\n      textLine: 0,\n      topTextLine: 0\n    };\n    this.members = {};\n    this.preFormatted = false;\n    this.postFormatted = false;\n    this.formatted = false;\n    this.width = 0;\n    this.spacing = 0;\n  }\n  addModifier(member) {\n    L('addModifier is deprecated, use addMember instead.');\n    return this.addMember(member);\n  }\n  addMember(member) {\n    const category = member.getCategory();\n    if (!this.members[category]) {\n      this.members[category] = [];\n    }\n    this.members[category].push(member);\n    member.setModifierContext(this);\n    this.preFormatted = false;\n    return this;\n  }\n  getModifiers(category) {\n    L('getModifiers is deprecated, use getMembers instead.');\n    return this.getMembers(category);\n  }\n  getMembers(category) {\n    var _a;\n    return (_a = this.members[category]) !== null && _a !== void 0 ? _a : [];\n  }\n  getWidth() {\n    return this.width;\n  }\n  getLeftShift() {\n    return this.state.leftShift;\n  }\n  getRightShift() {\n    return this.state.rightShift;\n  }\n  getState() {\n    return this.state;\n  }\n  getMetrics() {\n    if (!this.formatted) {\n      throw new RuntimeError('UnformattedMember', 'Unformatted member has no metrics.');\n    }\n    return {\n      width: this.state.leftShift + this.state.rightShift + this.spacing,\n      spacing: this.spacing\n    };\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    L('Preformatting ModifierContext');\n    const state = this.state;\n    const members = this.members;\n    StaveNote.format(members[\"StaveNote\"], state);\n    Parenthesis.format(members[\"Parenthesis\"], state);\n    Dot.format(members[\"Dot\"], state);\n    FretHandFinger.format(members[\"FretHandFinger\"], state);\n    Accidental.format(members[\"Accidental\"], state);\n    Stroke.format(members[\"Stroke\"], state);\n    GraceNoteGroup.format(members[\"GraceNoteGroup\"], state);\n    NoteSubGroup.format(members[\"NoteSubGroup\"], state);\n    StringNumber.format(members[\"StringNumber\"], state);\n    Articulation.format(members[\"Articulation\"], state);\n    Ornament.format(members[\"Ornament\"], state);\n    Annotation.format(members[\"Annotation\"], state);\n    ChordSymbol.format(members[\"ChordSymbol\"], state);\n    Bend.format(members[\"Bend\"], state);\n    Vibrato.format(members[\"Vibrato\"], state, this);\n    this.width = state.leftShift + state.rightShift;\n    this.preFormatted = true;\n  }\n  postFormat() {\n    if (this.postFormatted) return;\n    L('Postformatting ModifierContext');\n    StaveNote.postFormat(this.getMembers(\"StaveNote\"));\n  }\n}\nModifierContext.DEBUG = false;", "map": {"version": 3, "names": ["Accidental", "Annotation", "Articulation", "Bend", "ChordSymbol", "Dot", "FretHandFinger", "GraceNoteGroup", "NoteSubGroup", "Ornament", "Parenthesis", "StaveNote", "StringNumber", "Stroke", "log", "RuntimeError", "Vibrato", "L", "args", "ModifierContext", "DEBUG", "constructor", "state", "leftShift", "rightShift", "textLine", "topTextLine", "members", "preFormatted", "postFormatted", "formatted", "width", "spacing", "addModifier", "member", "addMember", "category", "getCategory", "push", "setModifierContext", "getModifiers", "getMembers", "_a", "getWidth", "getLeftShift", "getRightShift", "getState", "getMetrics", "preFormat", "format", "postFormat"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/modifiercontext.js"], "sourcesContent": ["import { Accidental } from './accidental.js';\nimport { Annotation } from './annotation.js';\nimport { Articulation } from './articulation.js';\nimport { Bend } from './bend.js';\nimport { ChordSymbol } from './chordsymbol.js';\nimport { Dot } from './dot.js';\nimport { FretHand<PERSON>inger } from './frethandfinger.js';\nimport { GraceNoteGroup } from './gracenotegroup.js';\nimport { NoteSubGroup } from './notesubgroup.js';\nimport { Ornament } from './ornament.js';\nimport { Parenthesis } from './parenthesis.js';\nimport { StaveNote } from './stavenote.js';\nimport { StringNumber } from './stringnumber.js';\nimport { Stroke } from './strokes.js';\nimport { log, RuntimeError } from './util.js';\nimport { Vibrato } from './vibrato.js';\nfunction L(...args) {\n    if (ModifierContext.DEBUG)\n        log('VexFlow.ModifierContext', args);\n}\nexport class ModifierContext {\n    constructor() {\n        this.state = {\n            leftShift: 0,\n            rightShift: 0,\n            textLine: 0,\n            topTextLine: 0,\n        };\n        this.members = {};\n        this.preFormatted = false;\n        this.postFormatted = false;\n        this.formatted = false;\n        this.width = 0;\n        this.spacing = 0;\n    }\n    addModifier(member) {\n        L('addModifier is deprecated, use addMember instead.');\n        return this.addMember(member);\n    }\n    addMember(member) {\n        const category = member.getCategory();\n        if (!this.members[category]) {\n            this.members[category] = [];\n        }\n        this.members[category].push(member);\n        member.setModifierContext(this);\n        this.preFormatted = false;\n        return this;\n    }\n    getModifiers(category) {\n        L('getModifiers is deprecated, use getMembers instead.');\n        return this.getMembers(category);\n    }\n    getMembers(category) {\n        var _a;\n        return (_a = this.members[category]) !== null && _a !== void 0 ? _a : [];\n    }\n    getWidth() {\n        return this.width;\n    }\n    getLeftShift() {\n        return this.state.leftShift;\n    }\n    getRightShift() {\n        return this.state.rightShift;\n    }\n    getState() {\n        return this.state;\n    }\n    getMetrics() {\n        if (!this.formatted) {\n            throw new RuntimeError('UnformattedMember', 'Unformatted member has no metrics.');\n        }\n        return {\n            width: this.state.leftShift + this.state.rightShift + this.spacing,\n            spacing: this.spacing,\n        };\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        L('Preformatting ModifierContext');\n        const state = this.state;\n        const members = this.members;\n        StaveNote.format(members[\"StaveNote\"], state);\n        Parenthesis.format(members[\"Parenthesis\"], state);\n        Dot.format(members[\"Dot\"], state);\n        FretHandFinger.format(members[\"FretHandFinger\"], state);\n        Accidental.format(members[\"Accidental\"], state);\n        Stroke.format(members[\"Stroke\"], state);\n        GraceNoteGroup.format(members[\"GraceNoteGroup\"], state);\n        NoteSubGroup.format(members[\"NoteSubGroup\"], state);\n        StringNumber.format(members[\"StringNumber\"], state);\n        Articulation.format(members[\"Articulation\"], state);\n        Ornament.format(members[\"Ornament\"], state);\n        Annotation.format(members[\"Annotation\"], state);\n        ChordSymbol.format(members[\"ChordSymbol\"], state);\n        Bend.format(members[\"Bend\"], state);\n        Vibrato.format(members[\"Vibrato\"], state, this);\n        this.width = state.leftShift + state.rightShift;\n        this.preFormatted = true;\n    }\n    postFormat() {\n        if (this.postFormatted)\n            return;\n        L('Postformatting ModifierContext');\n        StaveNote.postFormat(this.getMembers(\"StaveNote\"));\n    }\n}\nModifierContext.DEBUG = false;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,QAAQ,UAAU;AAC9B,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,GAAG,EAAEC,YAAY,QAAQ,WAAW;AAC7C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,eAAe,CAACC,KAAK,EACrBN,GAAG,CAAC,yBAAyB,EAAEI,IAAI,CAAC;AAC5C;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG;MACTC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE;IACjB,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,OAAO,GAAG,CAAC;EACpB;EACAC,WAAWA,CAACC,MAAM,EAAE;IAChBjB,CAAC,CAAC,mDAAmD,CAAC;IACtD,OAAO,IAAI,CAACkB,SAAS,CAACD,MAAM,CAAC;EACjC;EACAC,SAASA,CAACD,MAAM,EAAE;IACd,MAAME,QAAQ,GAAGF,MAAM,CAACG,WAAW,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,CAACV,OAAO,CAACS,QAAQ,CAAC,EAAE;MACzB,IAAI,CAACT,OAAO,CAACS,QAAQ,CAAC,GAAG,EAAE;IAC/B;IACA,IAAI,CAACT,OAAO,CAACS,QAAQ,CAAC,CAACE,IAAI,CAACJ,MAAM,CAAC;IACnCA,MAAM,CAACK,kBAAkB,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACX,YAAY,GAAG,KAAK;IACzB,OAAO,IAAI;EACf;EACAY,YAAYA,CAACJ,QAAQ,EAAE;IACnBnB,CAAC,CAAC,qDAAqD,CAAC;IACxD,OAAO,IAAI,CAACwB,UAAU,CAACL,QAAQ,CAAC;EACpC;EACAK,UAAUA,CAACL,QAAQ,EAAE;IACjB,IAAIM,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACf,OAAO,CAACS,QAAQ,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;EAC5E;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACZ,KAAK;EACrB;EACAa,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtB,KAAK,CAACC,SAAS;EAC/B;EACAsB,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvB,KAAK,CAACE,UAAU;EAChC;EACAsB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,KAAK;EACrB;EACAyB,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MACjB,MAAM,IAAIf,YAAY,CAAC,mBAAmB,EAAE,oCAAoC,CAAC;IACrF;IACA,OAAO;MACHgB,KAAK,EAAE,IAAI,CAACT,KAAK,CAACC,SAAS,GAAG,IAAI,CAACD,KAAK,CAACE,UAAU,GAAG,IAAI,CAACQ,OAAO;MAClEA,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC;EACL;EACAgB,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACpB,YAAY,EACjB;IACJX,CAAC,CAAC,+BAA+B,CAAC;IAClC,MAAMK,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMK,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5BhB,SAAS,CAACsC,MAAM,CAACtB,OAAO,CAAC,WAAW,CAAC,EAAEL,KAAK,CAAC;IAC7CZ,WAAW,CAACuC,MAAM,CAACtB,OAAO,CAAC,aAAa,CAAC,EAAEL,KAAK,CAAC;IACjDjB,GAAG,CAAC4C,MAAM,CAACtB,OAAO,CAAC,KAAK,CAAC,EAAEL,KAAK,CAAC;IACjChB,cAAc,CAAC2C,MAAM,CAACtB,OAAO,CAAC,gBAAgB,CAAC,EAAEL,KAAK,CAAC;IACvDtB,UAAU,CAACiD,MAAM,CAACtB,OAAO,CAAC,YAAY,CAAC,EAAEL,KAAK,CAAC;IAC/CT,MAAM,CAACoC,MAAM,CAACtB,OAAO,CAAC,QAAQ,CAAC,EAAEL,KAAK,CAAC;IACvCf,cAAc,CAAC0C,MAAM,CAACtB,OAAO,CAAC,gBAAgB,CAAC,EAAEL,KAAK,CAAC;IACvDd,YAAY,CAACyC,MAAM,CAACtB,OAAO,CAAC,cAAc,CAAC,EAAEL,KAAK,CAAC;IACnDV,YAAY,CAACqC,MAAM,CAACtB,OAAO,CAAC,cAAc,CAAC,EAAEL,KAAK,CAAC;IACnDpB,YAAY,CAAC+C,MAAM,CAACtB,OAAO,CAAC,cAAc,CAAC,EAAEL,KAAK,CAAC;IACnDb,QAAQ,CAACwC,MAAM,CAACtB,OAAO,CAAC,UAAU,CAAC,EAAEL,KAAK,CAAC;IAC3CrB,UAAU,CAACgD,MAAM,CAACtB,OAAO,CAAC,YAAY,CAAC,EAAEL,KAAK,CAAC;IAC/ClB,WAAW,CAAC6C,MAAM,CAACtB,OAAO,CAAC,aAAa,CAAC,EAAEL,KAAK,CAAC;IACjDnB,IAAI,CAAC8C,MAAM,CAACtB,OAAO,CAAC,MAAM,CAAC,EAAEL,KAAK,CAAC;IACnCN,OAAO,CAACiC,MAAM,CAACtB,OAAO,CAAC,SAAS,CAAC,EAAEL,KAAK,EAAE,IAAI,CAAC;IAC/C,IAAI,CAACS,KAAK,GAAGT,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACE,UAAU;IAC/C,IAAI,CAACI,YAAY,GAAG,IAAI;EAC5B;EACAsB,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACrB,aAAa,EAClB;IACJZ,CAAC,CAAC,gCAAgC,CAAC;IACnCN,SAAS,CAACuC,UAAU,CAAC,IAAI,CAACT,UAAU,CAAC,WAAW,CAAC,CAAC;EACtD;AACJ;AACAtB,eAAe,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}