{"ast": null, "code": "import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Stem } from './stem.js';\nexport class Tremolo extends Modifier {\n  static get CATEGORY() {\n    return \"Tremolo\";\n  }\n  constructor(num) {\n    super();\n    this.num = num;\n    this.position = Modifier.Position.CENTER;\n    this.text = Glyphs.tremolo1;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const stemDirection = note.getStemDirection();\n    const scale = note.getFontScale();\n    const ySpacing = Metrics.get(`Tremolo.spacing`) * stemDirection * scale;\n    const x = note.getAbsoluteX() + (stemDirection === Stem.UP ? note.getGlyphWidth() - Stem.WIDTH / 2 : Stem.WIDTH / 2);\n    let y = note.getStemExtents().topY + (this.num <= 3 ? ySpacing : 0);\n    this.fontInfo.size = Metrics.get(`Tremolo.fontSize`) * scale;\n    for (let i = 0; i < this.num; ++i) {\n      this.renderText(ctx, x, y);\n      y += ySpacing;\n    }\n  }\n}", "map": {"version": 3, "names": ["Glyphs", "Metrics", "Modifier", "<PERSON><PERSON>", "Tremolo", "CATEGORY", "constructor", "num", "position", "Position", "CENTER", "text", "tremolo1", "draw", "ctx", "checkContext", "note", "checkAttachedNote", "setRendered", "stemDirection", "getStemDirection", "scale", "getFontScale", "ySpacing", "get", "x", "getAbsoluteX", "UP", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WIDTH", "y", "getStemExtents", "topY", "fontInfo", "size", "i", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tremolo.js"], "sourcesContent": ["import { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Stem } from './stem.js';\nexport class Tremolo extends Modifier {\n    static get CATEGORY() {\n        return \"Tremolo\";\n    }\n    constructor(num) {\n        super();\n        this.num = num;\n        this.position = Modifier.Position.CENTER;\n        this.text = Glyphs.tremolo1;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const stemDirection = note.getStemDirection();\n        const scale = note.getFontScale();\n        const ySpacing = Metrics.get(`Tremolo.spacing`) * stemDirection * scale;\n        const x = note.getAbsoluteX() + (stemDirection === Stem.UP ? note.getGlyphWidth() - Stem.WIDTH / 2 : Stem.WIDTH / 2);\n        let y = note.getStemExtents().topY + (this.num <= 3 ? ySpacing : 0);\n        this.fontInfo.size = Metrics.get(`Tremolo.fontSize`) * scale;\n        for (let i = 0; i < this.num; ++i) {\n            this.renderText(ctx, x, y);\n            y += ySpacing;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,OAAO,MAAMC,OAAO,SAASF,QAAQ,CAAC;EAClC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACAC,WAAWA,CAACC,GAAG,EAAE;IACb,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGN,QAAQ,CAACO,QAAQ,CAACC,MAAM;IACxC,IAAI,CAACC,IAAI,GAAGX,MAAM,CAACY,QAAQ;EAC/B;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMC,IAAI,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,aAAa,GAAGH,IAAI,CAACI,gBAAgB,CAAC,CAAC;IAC7C,MAAMC,KAAK,GAAGL,IAAI,CAACM,YAAY,CAAC,CAAC;IACjC,MAAMC,QAAQ,GAAGtB,OAAO,CAACuB,GAAG,CAAC,iBAAiB,CAAC,GAAGL,aAAa,GAAGE,KAAK;IACvE,MAAMI,CAAC,GAAGT,IAAI,CAACU,YAAY,CAAC,CAAC,IAAIP,aAAa,KAAKhB,IAAI,CAACwB,EAAE,GAAGX,IAAI,CAACY,aAAa,CAAC,CAAC,GAAGzB,IAAI,CAAC0B,KAAK,GAAG,CAAC,GAAG1B,IAAI,CAAC0B,KAAK,GAAG,CAAC,CAAC;IACpH,IAAIC,CAAC,GAAGd,IAAI,CAACe,cAAc,CAAC,CAAC,CAACC,IAAI,IAAI,IAAI,CAACzB,GAAG,IAAI,CAAC,GAAGgB,QAAQ,GAAG,CAAC,CAAC;IACnE,IAAI,CAACU,QAAQ,CAACC,IAAI,GAAGjC,OAAO,CAACuB,GAAG,CAAC,kBAAkB,CAAC,GAAGH,KAAK;IAC5D,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5B,GAAG,EAAE,EAAE4B,CAAC,EAAE;MAC/B,IAAI,CAACC,UAAU,CAACtB,GAAG,EAAEW,CAAC,EAAEK,CAAC,CAAC;MAC1BA,CAAC,IAAIP,QAAQ;IACjB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}