{"ast": null, "code": "import { BoundingBox } from './boundingbox.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Note } from './note.js';\nimport { NoteHead } from './notehead.js';\nimport { Stem } from './stem.js';\nimport { StemmableNote } from './stemmablenote.js';\nimport { Tables } from './tables.js';\nimport { defined, log, midLine, RuntimeError } from './util.js';\nfunction L(...args) {\n  if (StaveNote.DEBUG) log('VexFlow.StaveNote', args);\n}\nconst isInnerNoteIndex = (note, index) => index === (note.getStemDirection() === Stem.UP ? note.keyProps.length - 1 : 0);\nfunction shiftRestVertical(rest, note, dir) {\n  const delta = dir;\n  rest.line += delta;\n  rest.maxLine += delta;\n  rest.minLine += delta;\n  rest.note.setKeyLine(0, rest.note.getKeyLine(0) + delta);\n}\nfunction centerRest(rest, noteU, noteL) {\n  const delta = rest.line - midLine(noteU.minLine, noteL.maxLine);\n  rest.note.setKeyLine(0, rest.note.getKeyLine(0) - delta);\n  rest.line -= delta;\n  rest.maxLine -= delta;\n  rest.minLine -= delta;\n}\nexport class StaveNote extends StemmableNote {\n  static get CATEGORY() {\n    return \"StaveNote\";\n  }\n  static get LEDGER_LINE_OFFSET() {\n    return 3;\n  }\n  static get minNoteheadPadding() {\n    return Metrics.get('NoteHead.minPadding');\n  }\n  static format(notes, state) {\n    if (!notes || notes.length < 2) return false;\n    const notesList = [];\n    for (let i = 0; i < notes.length; i++) {\n      const props = notes[i].sortedKeyProps;\n      const line = props[0].keyProps.line;\n      let minL = props[props.length - 1].keyProps.line;\n      const stemDirection = notes[i].getStemDirection();\n      const stemMax = notes[i].getStemLength() / 10;\n      const stemMin = notes[i].getStemMinimumLength() / 10;\n      let maxL;\n      if (notes[i].isRest()) {\n        maxL = line + Math.ceil(notes[i]._noteHeads[0].getTextMetrics().actualBoundingBoxAscent / Tables.STAVE_LINE_DISTANCE);\n        minL = line - Math.ceil(notes[i]._noteHeads[0].getTextMetrics().actualBoundingBoxDescent / Tables.STAVE_LINE_DISTANCE);\n      } else {\n        maxL = stemDirection === 1 ? props[props.length - 1].keyProps.line + stemMax : props[props.length - 1].keyProps.line;\n        minL = stemDirection === 1 ? props[0].keyProps.line : props[0].keyProps.line - stemMax;\n      }\n      notesList.push({\n        line: props[0].keyProps.line,\n        maxLine: maxL,\n        minLine: minL,\n        isrest: notes[i].isRest(),\n        stemDirection,\n        stemMax,\n        stemMin,\n        voiceShift: notes[i].getVoiceShiftWidth(),\n        isDisplaced: notes[i].isDisplaced(),\n        note: notes[i]\n      });\n    }\n    let voices = 0;\n    let noteU = undefined;\n    let noteM = undefined;\n    let noteL = undefined;\n    const draw = [false, false, false];\n    for (let i = 0; i < notesList.length; i++) {\n      draw[i] = notesList[i].note.renderOptions.draw !== false;\n    }\n    if (draw[0] && draw[1] && draw[2]) {\n      voices = 3;\n      noteU = notesList[0];\n      noteM = notesList[1];\n      noteL = notesList[2];\n    } else if (draw[0] && draw[1]) {\n      voices = 2;\n      noteU = notesList[0];\n      noteL = notesList[1];\n    } else if (draw[0] && draw[2]) {\n      voices = 2;\n      noteU = notesList[0];\n      noteL = notesList[2];\n    } else if (draw[1] && draw[2]) {\n      voices = 2;\n      noteU = notesList[1];\n      noteL = notesList[2];\n    } else {\n      return true;\n    }\n    if (voices === 2 && noteU.stemDirection === -1 && noteL.stemDirection === 1) {\n      noteU = notesList[1];\n      noteL = notesList[0];\n    }\n    const voiceXShift = Math.max(noteU.voiceShift, noteL.voiceShift);\n    let xShift = 0;\n    if (voices === 2) {\n      const lineSpacing = noteU.note.hasStem() && noteL.note.hasStem() && noteU.stemDirection === noteL.stemDirection ? 0.0 : 0.5;\n      if (noteL.isrest && noteU.isrest && noteU.note.duration === noteL.note.duration) {\n        noteL.note.renderOptions.draw = false;\n      } else if (noteU.minLine <= noteL.maxLine + lineSpacing) {\n        if (noteU.isrest) {\n          shiftRestVertical(noteU, noteL, 1);\n        } else if (noteL.isrest) {\n          shiftRestVertical(noteL, noteU, -1);\n        } else {\n          const lineDiff = Math.abs(noteU.line - noteL.line);\n          if (noteU.note.hasStem() && noteL.note.hasStem()) {\n            const noteUHead = noteU.note.sortedKeyProps[0].keyProps.code;\n            const noteLHead = noteL.note.sortedKeyProps[noteL.note.sortedKeyProps.length - 1].keyProps.code;\n            if (!Tables.UNISON || noteUHead !== noteLHead || noteU.note.getModifiers().filter(item => item.getCategory() === \"Dot\" && item.getIndex() === 0).length !== noteL.note.getModifiers().filter(item => item.getCategory() === \"Dot\" && item.getIndex() === 0).length || lineDiff < 1 && lineDiff > 0 || JSON.stringify(noteU.note.getStyle()) !== JSON.stringify(noteL.note.getStyle())) {\n              xShift = voiceXShift + 2;\n              if (noteU.stemDirection === noteL.stemDirection) {\n                noteU.note.setXShift(xShift);\n              } else {\n                noteL.note.setXShift(xShift);\n              }\n            } else if (noteU.note.voice !== noteL.note.voice) {\n              if (noteU.stemDirection === noteL.stemDirection) {\n                if (noteU.line !== noteL.line) {\n                  xShift = voiceXShift + 2;\n                  noteU.note.setXShift(xShift);\n                } else {\n                  if (noteL.stemDirection === 1) {\n                    noteL.stemDirection = -1;\n                    noteL.note.setStemDirection(-1);\n                  }\n                }\n              }\n            }\n          } else if (lineDiff < 1) {\n            xShift = voiceXShift + 2;\n            if (noteU.note.duration < noteL.note.duration) {\n              noteU.note.setXShift(xShift);\n            } else {\n              noteL.note.setXShift(xShift);\n            }\n          } else if (noteU.note.hasStem()) {\n            noteU.stemDirection = -noteU.note.getStemDirection();\n            noteU.note.setStemDirection(noteU.stemDirection);\n          } else if (noteL.note.hasStem()) {\n            noteL.stemDirection = -noteL.note.getStemDirection();\n            noteL.note.setStemDirection(noteL.stemDirection);\n          }\n        }\n      }\n      state.rightShift += xShift;\n      return true;\n    }\n    if (!noteM) throw new RuntimeError('InvalidState', 'noteM not defined.');\n    if (noteM.isrest && !noteU.isrest && !noteL.isrest) {\n      if (noteU.minLine <= noteM.maxLine || noteM.minLine <= noteL.maxLine) {\n        const restHeight = noteM.maxLine - noteM.minLine;\n        const space = noteU.minLine - noteL.maxLine;\n        if (restHeight < space) {\n          centerRest(noteM, noteU, noteL);\n        } else {\n          xShift = voiceXShift + 2;\n          noteM.note.setXShift(xShift);\n          if (noteL.note.hasBeam() === false) {\n            noteL.stemDirection = -1;\n            noteL.note.setStemDirection(-1);\n          }\n          if (noteU.minLine <= noteL.maxLine && noteU.note.hasBeam() === false) {\n            noteU.stemDirection = 1;\n            noteU.note.setStemDirection(1);\n          }\n        }\n        state.rightShift += xShift;\n        return true;\n      }\n    }\n    if (noteU.isrest && noteM.isrest && noteL.isrest) {\n      noteU.note.renderOptions.draw = false;\n      noteL.note.renderOptions.draw = false;\n      state.rightShift += xShift;\n      return true;\n    }\n    if (noteM.isrest && noteU.isrest && noteM.minLine <= noteL.maxLine) {\n      noteM.note.renderOptions.draw = false;\n    }\n    if (noteM.isrest && noteL.isrest && noteU.minLine <= noteM.maxLine) {\n      noteM.note.renderOptions.draw = false;\n    }\n    if (noteU.isrest && noteU.minLine <= noteM.maxLine) {\n      shiftRestVertical(noteU, noteM, 1);\n    }\n    if (noteL.isrest && noteM.minLine <= noteL.maxLine) {\n      shiftRestVertical(noteL, noteM, -1);\n    }\n    if (noteU.minLine <= noteM.maxLine + 0.5 || noteM.minLine <= noteL.maxLine) {\n      xShift = voiceXShift + 2;\n      noteM.note.setXShift(xShift);\n      if (noteL.note.hasBeam() === false) {\n        noteL.stemDirection = -1;\n        noteL.note.setStemDirection(-1);\n      }\n      if (noteU.minLine <= noteL.maxLine && noteU.note.hasBeam() === false) {\n        noteU.stemDirection = 1;\n        noteU.note.setStemDirection(1);\n      }\n    }\n    state.rightShift += xShift;\n    return true;\n  }\n  static postFormat(notes) {\n    if (!notes) return false;\n    notes.forEach(note => note.postFormat());\n    return true;\n  }\n  constructor(noteStruct) {\n    var _a, _b, _c;\n    super(noteStruct);\n    this.minLine = 0;\n    this.maxLine = 0;\n    this.sortedKeyProps = [];\n    this.ledgerLineStyle = {};\n    this.clef = (_a = noteStruct.clef) !== null && _a !== void 0 ? _a : 'treble';\n    this.octaveShift = (_b = noteStruct.octaveShift) !== null && _b !== void 0 ? _b : 0;\n    this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n    defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n    this.displaced = false;\n    this.dotShiftY = 0;\n    this.useDefaultHeadX = false;\n    this._noteHeads = [];\n    this.modifiers = [];\n    this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), {\n      strokePx: noteStruct.strokePx || StaveNote.LEDGER_LINE_OFFSET\n    });\n    this.calculateKeyProps();\n    this.buildStem();\n    if (noteStruct.autoStem) {\n      this.autoStem();\n    } else {\n      this.setStemDirection((_c = noteStruct.stemDirection) !== null && _c !== void 0 ? _c : Stem.UP);\n    }\n    this.reset();\n    this.buildFlag();\n  }\n  reset() {\n    super.reset();\n    const noteHeadStyles = this._noteHeads.map(noteHead => noteHead.getStyle());\n    this.buildNoteHeads();\n    this._noteHeads.forEach((noteHead, index) => {\n      const noteHeadStyle = noteHeadStyles[index];\n      if (noteHeadStyle) noteHead.setStyle(noteHeadStyle);\n    });\n    const stave = this.stave;\n    if (stave) {\n      this.setStave(stave);\n    }\n    this.calcNoteDisplacements();\n    return this;\n  }\n  setBeam(beam) {\n    this.beam = beam;\n    this.calcNoteDisplacements();\n    if (this.stem) {\n      this.stem.setExtension(this.getStemExtension());\n    }\n    return this;\n  }\n  buildStem() {\n    this.setStem(new Stem({\n      hide: this.isRest()\n    }));\n    return this;\n  }\n  buildNoteHeads() {\n    this._noteHeads = [];\n    const stemDirection = this.getStemDirection();\n    const keys = this.getKeys();\n    let lastLine = undefined;\n    let lineDiff = undefined;\n    let displaced = false;\n    let start;\n    let end;\n    let step;\n    if (stemDirection === Stem.UP) {\n      start = 0;\n      end = keys.length;\n      step = 1;\n    } else {\n      start = keys.length - 1;\n      end = -1;\n      step = -1;\n    }\n    for (let i = start; i !== end; i += step) {\n      const noteProps = this.sortedKeyProps[i].keyProps;\n      const line = noteProps.line;\n      if (lastLine === undefined) {\n        lastLine = line;\n      } else {\n        lineDiff = Math.abs(lastLine - line);\n        if (lineDiff === 0 || lineDiff === 0.5) {\n          displaced = !displaced;\n        } else {\n          displaced = false;\n          this.useDefaultHeadX = true;\n        }\n      }\n      lastLine = line;\n      const notehead = new NoteHead({\n        duration: this.duration,\n        noteType: this.noteType,\n        displaced,\n        stemDirection,\n        customGlyphCode: noteProps.code,\n        line: noteProps.line\n      });\n      notehead.fontInfo = this.fontInfo;\n      this.addChild(notehead);\n      this._noteHeads[this.sortedKeyProps[i].index] = notehead;\n    }\n    return this._noteHeads;\n  }\n  autoStem() {\n    this.setStemDirection(this.calculateOptimalStemDirection());\n  }\n  calculateOptimalStemDirection() {\n    this.minLine = this.sortedKeyProps[0].keyProps.line;\n    this.maxLine = this.sortedKeyProps[this.keyProps.length - 1].keyProps.line;\n    const MIDDLE_LINE = 3;\n    const decider = (this.minLine + this.maxLine) / 2;\n    const stemDirection = decider < MIDDLE_LINE ? Stem.UP : Stem.DOWN;\n    return stemDirection;\n  }\n  calculateKeyProps() {\n    var _a;\n    let lastLine;\n    for (let i = 0; i < this.keys.length; ++i) {\n      const key = this.keys[i];\n      const options = {\n        octaveShift: (_a = this.octaveShift) !== null && _a !== void 0 ? _a : 0,\n        duration: this.duration\n      };\n      const props = Tables.keyProperties(key, this.clef, this.noteType, options);\n      if (!props) {\n        throw new RuntimeError('BadArguments', `Invalid key for note properties: ${key}`);\n      }\n      if (props.key === 'R') {\n        if (this.duration === '1' || this.duration === 'w') {\n          props.line = 4;\n        } else {\n          props.line = 3;\n        }\n      }\n      const line = props.line;\n      if (lastLine === undefined) {\n        lastLine = line;\n      } else {\n        if (Math.abs(lastLine - line) < 1) {\n          this.displaced = true;\n          props.displaced = true;\n          if (this.keyProps.length > 0) {\n            this.keyProps[i - 1].displaced = true;\n          }\n        }\n      }\n      lastLine = line;\n      this.keyProps.push(props);\n    }\n    this.keyProps.forEach((keyProps, index) => {\n      this.sortedKeyProps.push({\n        keyProps,\n        index\n      });\n    });\n    this.sortedKeyProps.sort((a, b) => a.keyProps.line - b.keyProps.line);\n  }\n  getBoundingBox() {\n    const boundingBox = new BoundingBox(this.getAbsoluteX(), this.ys[0], 0, 0);\n    this._noteHeads.forEach(notehead => {\n      boundingBox.mergeWith(notehead.getBoundingBox());\n    });\n    const {\n      yTop,\n      yBottom\n    } = this.getNoteHeadBounds();\n    if (!this.isRest() && this.hasStem()) {\n      const noteStemHeight = this.stem.getHeight();\n      const stemY = this.getStemDirection() === Stem.DOWN ? yTop - noteStemHeight - this.flag.getTextMetrics().actualBoundingBoxDescent : yBottom - noteStemHeight + this.flag.getTextMetrics().actualBoundingBoxAscent;\n      boundingBox.mergeWith(new BoundingBox(this.getAbsoluteX(), stemY, 0, 0));\n    }\n    if (this.hasFlag()) {\n      const bbFlag = this.flag.getBoundingBox();\n      boundingBox.mergeWith(bbFlag);\n    }\n    for (let i = 0; i < this.modifiers.length; i++) {\n      boundingBox.mergeWith(this.modifiers[i].getBoundingBox());\n    }\n    return boundingBox;\n  }\n  getLineNumber(isTopNote) {\n    if (!this.keyProps.length) {\n      throw new RuntimeError('NoKeyProps', \"Can't get bottom note line, because note is not initialized properly.\");\n    }\n    let resultLine = this.keyProps[0].line;\n    for (let i = 0; i < this.keyProps.length; i++) {\n      const thisLine = this.keyProps[i].line;\n      if (isTopNote) {\n        if (thisLine > resultLine) resultLine = thisLine;\n      } else {\n        if (thisLine < resultLine) resultLine = thisLine;\n      }\n    }\n    return resultLine;\n  }\n  isRest() {\n    const val = this.glyphProps.codeHead;\n    return val >= '\\ue4e0' && val <= '\\ue4ff';\n  }\n  isChord() {\n    return !this.isRest() && this.keys.length > 1;\n  }\n  hasStem() {\n    return this.glyphProps.stem;\n  }\n  hasFlag() {\n    return super.hasFlag() && !this.isRest();\n  }\n  getStemX() {\n    if (this.noteType === 'r') {\n      return this.getCenterGlyphX();\n    } else {\n      return super.getStemX() + (this.stemDirection ? Stem.WIDTH / (2 * -this.stemDirection) : 0);\n    }\n  }\n  getYForTopText(textLine) {\n    const extents = this.getStemExtents();\n    return Math.min(this.checkStave().getYForTopText(textLine), extents.topY - this.renderOptions.annotationSpacing * (textLine + 1));\n  }\n  getYForBottomText(textLine) {\n    const extents = this.getStemExtents();\n    return Math.max(this.checkStave().getYForTopText(textLine), extents.baseY + this.renderOptions.annotationSpacing * textLine);\n  }\n  setStave(stave) {\n    super.setStave(stave);\n    const ys = this._noteHeads.map(notehead => {\n      notehead.setStave(stave);\n      return notehead.getY();\n    });\n    this.setYs(ys);\n    if (this.stem) {\n      const {\n        yTop,\n        yBottom\n      } = this.getNoteHeadBounds();\n      this.stem.setYBounds(yTop, yBottom);\n    }\n    return this;\n  }\n  isDisplaced() {\n    return this.displaced;\n  }\n  setNoteDisplaced(displaced) {\n    this.displaced = displaced;\n    return this;\n  }\n  getTieRightX() {\n    let tieStartX = this.getAbsoluteX();\n    tieStartX += this.getGlyphWidth() + this.xShift + this.rightDisplacedHeadPx;\n    if (this.modifierContext) tieStartX += this.modifierContext.getRightShift();\n    return tieStartX;\n  }\n  getTieLeftX() {\n    let tieEndX = this.getAbsoluteX();\n    tieEndX += this.xShift - this.leftDisplacedHeadPx;\n    return tieEndX;\n  }\n  getLineForRest() {\n    let restLine = this.keyProps[0].line;\n    if (this.keyProps.length > 1) {\n      const lastLine = this.keyProps[this.keyProps.length - 1].line;\n      const top = Math.max(restLine, lastLine);\n      const bot = Math.min(restLine, lastLine);\n      restLine = midLine(top, bot);\n    }\n    return restLine;\n  }\n  getModifierStartXY(position, index, options = {}) {\n    if (!this.preFormatted) {\n      throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n    }\n    if (this.ys.length === 0) {\n      throw new RuntimeError('NoYValues', 'No Y-Values calculated for this note.');\n    }\n    const {\n      ABOVE,\n      BELOW,\n      LEFT,\n      RIGHT\n    } = Modifier.Position;\n    let x = 0;\n    if (position === LEFT) {\n      x = -1 * 2;\n    } else if (position === RIGHT) {\n      x = this.getGlyphWidth() + this.xShift + 2;\n      if (this.stemDirection === Stem.UP && this.hasFlag() && (options.forceFlagRight || isInnerNoteIndex(this, index))) {\n        x += this.flag.getWidth();\n      }\n    } else if (position === BELOW || position === ABOVE) {\n      x = this.getGlyphWidth() / 2;\n    }\n    let restShift = 0;\n    switch (this._noteHeads[index].getText()) {\n      case Glyphs.restDoubleWhole:\n      case Glyphs.restWhole:\n        restShift += 0.5;\n        break;\n      case Glyphs.restHalf:\n      case Glyphs.restQuarter:\n      case Glyphs.rest8th:\n      case Glyphs.rest16th:\n        restShift -= 0.5;\n        break;\n      case Glyphs.rest32nd:\n      case Glyphs.rest64th:\n        restShift -= 1.5;\n        break;\n      case Glyphs.rest128th:\n        restShift -= 2.5;\n        break;\n    }\n    return {\n      x: this.getAbsoluteX() + x,\n      y: this.ys[index] + restShift * this.checkStave().getSpacingBetweenLines()\n    };\n  }\n  setStyle(style) {\n    return super.setGroupStyle(style);\n  }\n  setStemStyle(style) {\n    const stem = this.getStem();\n    if (stem) stem.setStyle(style);\n    return this;\n  }\n  getStemStyle() {\n    var _a;\n    return (_a = this.stem) === null || _a === void 0 ? void 0 : _a.getStyle();\n  }\n  setLedgerLineStyle(style) {\n    this.ledgerLineStyle = style;\n  }\n  getLedgerLineStyle() {\n    return this.ledgerLineStyle;\n  }\n  setFlagStyle(style) {\n    this.flag.setStyle(style);\n  }\n  getFlagStyle() {\n    return this.flag.getStyle();\n  }\n  getGlyphWidth() {\n    return this.noteHeads[0].getWidth();\n  }\n  setKeyStyle(index, style) {\n    this._noteHeads[index].setStyle(style);\n    return this;\n  }\n  setKeyLine(index, line) {\n    this.keyProps[index].line = line;\n    this.reset();\n    return this;\n  }\n  getKeyLine(index) {\n    return this.keyProps[index].line;\n  }\n  getVoiceShiftWidth() {\n    return this.getGlyphWidth() * (this.displaced ? 2 : 1);\n  }\n  calcNoteDisplacements() {\n    this.setLeftDisplacedHeadPx(this.displaced && this.stemDirection === Stem.DOWN ? this.getGlyphWidth() : 0);\n    this.setRightDisplacedHeadPx(!this.hasFlag() && this.displaced && this.stemDirection === Stem.UP ? this.getGlyphWidth() : 0);\n  }\n  preFormat() {\n    if (this.preFormatted) return;\n    let noteHeadPadding = 0;\n    if (this.modifierContext) {\n      this.modifierContext.preFormat();\n      if (this.modifierContext.getWidth() === 0) {\n        noteHeadPadding = StaveNote.minNoteheadPadding;\n      }\n    }\n    let width = this.getGlyphWidth() + this.leftDisplacedHeadPx + this.rightDisplacedHeadPx + noteHeadPadding;\n    if (this.shouldDrawFlag() && this.stemDirection === Stem.UP) {\n      width += this.getGlyphWidth();\n    }\n    this.setWidth(width);\n    this.preFormatted = true;\n  }\n  getNoteHeadBounds() {\n    let yTop = +Infinity;\n    let yBottom = -Infinity;\n    let nonDisplacedX;\n    let displacedX;\n    let highestLine = this.checkStave().getNumLines();\n    let lowestLine = 1;\n    let highestDisplacedLine;\n    let lowestDisplacedLine;\n    let highestNonDisplacedLine = highestLine;\n    let lowestNonDisplacedLine = lowestLine;\n    this._noteHeads.forEach(notehead => {\n      const line = notehead.getLine();\n      const y = notehead.getY();\n      yTop = Math.min(y, yTop);\n      yBottom = Math.max(y, yBottom);\n      if (displacedX === undefined && notehead.isDisplaced()) {\n        displacedX = notehead.getAbsoluteX();\n      }\n      if (nonDisplacedX === undefined && !notehead.isDisplaced()) {\n        nonDisplacedX = notehead.getAbsoluteX();\n      }\n      highestLine = Math.max(line, highestLine);\n      lowestLine = Math.min(line, lowestLine);\n      if (notehead.isDisplaced()) {\n        highestDisplacedLine = highestDisplacedLine === undefined ? line : Math.max(line, highestDisplacedLine);\n        lowestDisplacedLine = lowestDisplacedLine === undefined ? line : Math.min(line, lowestDisplacedLine);\n      } else {\n        highestNonDisplacedLine = Math.max(line, highestNonDisplacedLine);\n        lowestNonDisplacedLine = Math.min(line, lowestNonDisplacedLine);\n      }\n    }, this);\n    return {\n      yTop,\n      yBottom,\n      displacedX,\n      nonDisplacedX,\n      highestLine,\n      lowestLine,\n      highestDisplacedLine,\n      lowestDisplacedLine,\n      highestNonDisplacedLine,\n      lowestNonDisplacedLine\n    };\n  }\n  getNoteHeadBeginX() {\n    return this.getAbsoluteX() + this.xShift;\n  }\n  getNoteHeadEndX() {\n    const xBegin = this.getNoteHeadBeginX();\n    return xBegin + this.getGlyphWidth();\n  }\n  get noteHeads() {\n    return this._noteHeads.slice();\n  }\n  drawLedgerLines() {\n    const stave = this.checkStave();\n    const {\n      renderOptions: {\n        strokePx\n      }\n    } = this;\n    const ctx = this.checkContext();\n    const width = this.getGlyphWidth() + strokePx * 2;\n    const doubleWidth = 2 * (this.getGlyphWidth() + strokePx) - Stem.WIDTH / 2;\n    if (this.isRest()) return;\n    if (!ctx) {\n      throw new RuntimeError('NoCanvasContext', \"Can't draw without a canvas context.\");\n    }\n    const {\n      highestLine,\n      lowestLine,\n      highestDisplacedLine,\n      highestNonDisplacedLine,\n      lowestDisplacedLine,\n      lowestNonDisplacedLine,\n      displacedX,\n      nonDisplacedX\n    } = this.getNoteHeadBounds();\n    if (highestLine < 6 && lowestLine > 0) return;\n    const minX = Math.min(displacedX !== null && displacedX !== void 0 ? displacedX : 0, nonDisplacedX !== null && nonDisplacedX !== void 0 ? nonDisplacedX : 0);\n    const drawLedgerLine = (y, normal, displaced) => {\n      let x;\n      if (displaced && normal) x = minX - strokePx;else if (normal) x = (nonDisplacedX !== null && nonDisplacedX !== void 0 ? nonDisplacedX : 0) - strokePx;else x = (displacedX !== null && displacedX !== void 0 ? displacedX : 0) - strokePx;\n      const ledgerWidth = normal && displaced ? doubleWidth : width;\n      ctx.beginPath();\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + ledgerWidth, y);\n      ctx.stroke();\n    };\n    const style = Object.assign(Object.assign({}, stave.getDefaultLedgerLineStyle()), this.getLedgerLineStyle());\n    ctx.save();\n    this.applyStyle(ctx, style);\n    for (let line = 6; line <= highestLine; ++line) {\n      const normal = nonDisplacedX !== undefined && line <= highestNonDisplacedLine;\n      const displaced = highestDisplacedLine !== undefined && line <= highestDisplacedLine;\n      drawLedgerLine(stave.getYForNote(line), normal, displaced);\n    }\n    for (let line = 0; line >= lowestLine; --line) {\n      const normal = nonDisplacedX !== undefined && line >= lowestNonDisplacedLine;\n      const displaced = lowestDisplacedLine !== undefined && line >= lowestDisplacedLine;\n      drawLedgerLine(stave.getYForNote(line), normal, displaced);\n    }\n    ctx.restore();\n  }\n  drawModifiers(noteheadParam) {\n    const ctx = this.checkContext();\n    for (let i = 0; i < this.modifiers.length; i++) {\n      const modifier = this.modifiers[i];\n      const index = modifier.checkIndex();\n      const notehead = this._noteHeads[index];\n      if (notehead === noteheadParam) {\n        modifier.setContext(ctx);\n        modifier.drawWithStyle();\n      }\n    }\n  }\n  shouldDrawFlag() {\n    const hasStem = this.stem !== undefined;\n    const hasFlag = this.glyphProps.codeFlagUp !== undefined;\n    const hasNoBeam = this.beam === undefined;\n    return hasStem && hasFlag && hasNoBeam && !this.isRest();\n  }\n  drawFlag() {\n    const ctx = this.checkContext();\n    if (!ctx) {\n      throw new RuntimeError('NoCanvasContext', \"Can't draw without a canvas context.\");\n    }\n    if (this.shouldDrawFlag()) {\n      const {\n        yTop,\n        yBottom\n      } = this.getNoteHeadBounds();\n      const noteStemHeight = this.stem.getHeight();\n      const flagX = this.getStemX() - Tables.STEM_WIDTH / 2;\n      const flagY = this.getStemDirection() === Stem.DOWN ? yTop - noteStemHeight - this.flag.getTextMetrics().actualBoundingBoxDescent : yBottom - noteStemHeight + this.flag.getTextMetrics().actualBoundingBoxAscent;\n      this.flag.setContext(ctx).setX(flagX).setY(flagY).drawWithStyle();\n    }\n  }\n  drawNoteHeads() {\n    const ctx = this.checkContext();\n    this._noteHeads.forEach(notehead => {\n      notehead.setContext(ctx).drawWithStyle();\n    });\n  }\n  drawStem(stemOptions) {\n    const ctx = this.checkContext();\n    if (stemOptions) {\n      this.setStem(new Stem(stemOptions));\n    }\n    if (this.shouldDrawFlag() && this.stem) {\n      this.stem.adjustHeightForFlag();\n    }\n    if (this.stem) {\n      this.stem.setContext(ctx).drawWithStyle();\n    }\n  }\n  getStemExtension() {\n    const superStemExtension = super.getStemExtension();\n    if (!this.glyphProps.stem) {\n      return superStemExtension;\n    }\n    const stemDirection = this.getStemDirection();\n    if (stemDirection !== this.calculateOptimalStemDirection()) {\n      return superStemExtension;\n    }\n    let midLineDistance;\n    const MIDDLE_LINE = 3;\n    if (stemDirection === Stem.UP) {\n      midLineDistance = MIDDLE_LINE - this.maxLine;\n    } else {\n      midLineDistance = this.minLine - MIDDLE_LINE;\n    }\n    const linesOverOctaveFromMidLine = midLineDistance - 3.5;\n    if (linesOverOctaveFromMidLine <= 0) {\n      return superStemExtension;\n    }\n    const stave = this.getStave();\n    let spacingBetweenLines = 10;\n    if (stave !== undefined) {\n      spacingBetweenLines = stave.getSpacingBetweenLines();\n    }\n    return superStemExtension + linesOverOctaveFromMidLine * spacingBetweenLines;\n  }\n  draw() {\n    if (this.renderOptions.draw === false) return;\n    if (this.ys.length === 0) {\n      throw new RuntimeError('NoYValues', \"Can't draw note without Y values.\");\n    }\n    const ctx = this.checkContext();\n    const xBegin = this.getNoteHeadBeginX();\n    const shouldRenderStem = this.hasStem() && !this.beam;\n    this._noteHeads.forEach(notehead => notehead.setX(xBegin));\n    if (this.stem) {\n      const stemX = this.getStemX();\n      this.stem.setNoteHeadXBounds(stemX, stemX);\n    }\n    L('Rendering ', this.isChord() ? 'chord :' : 'note :', this.keys);\n    ctx.openGroup('stavenote', this.getAttribute('id'));\n    this.drawLedgerLines();\n    if (shouldRenderStem) this.drawStem();\n    this.drawNoteHeads();\n    this.drawFlag();\n    const bb = this.getBoundingBox();\n    ctx.pointerRect(bb.getX(), bb.getY(), bb.getW(), bb.getH());\n    ctx.closeGroup();\n    this.setRendered();\n  }\n}\nStaveNote.DEBUG = false;", "map": {"version": 3, "names": ["BoundingBox", "Glyphs", "Metrics", "Modifier", "Note", "NoteHead", "<PERSON><PERSON>", "StemmableNote", "Tables", "defined", "log", "midLine", "RuntimeError", "L", "args", "StaveNote", "DEBUG", "isInnerNoteIndex", "note", "index", "getStemDirection", "UP", "keyProps", "length", "shiftRestVertical", "rest", "dir", "delta", "line", "maxLine", "minLine", "setKeyLine", "getKeyLine", "centerRest", "noteU", "noteL", "CATEGORY", "LEDGER_LINE_OFFSET", "minNoteheadPadding", "get", "format", "notes", "state", "notesList", "i", "props", "sortedKeyProps", "minL", "stemDirection", "stemMax", "getStemLength", "stemMin", "getStemMinimumLength", "maxL", "isRest", "Math", "ceil", "_noteHeads", "getTextMetrics", "actualBoundingBoxAscent", "STAVE_LINE_DISTANCE", "actualBoundingBoxDescent", "push", "isrest", "voiceShift", "getVoiceShiftWidth", "isDisplaced", "voices", "undefined", "noteM", "draw", "renderOptions", "voiceXShift", "max", "xShift", "lineSpacing", "hasStem", "duration", "lineDiff", "abs", "noteUHead", "code", "noteLHead", "UNISON", "getModifiers", "filter", "item", "getCategory", "getIndex", "JSON", "stringify", "getStyle", "setXShift", "voice", "setStemDirection", "rightShift", "restHeight", "space", "hasBeam", "postFormat", "for<PERSON>ach", "constructor", "noteStruct", "_a", "_b", "_c", "ledgerLineStyle", "clef", "octaveShift", "glyphProps", "getGlyphProps", "noteType", "displaced", "dotShiftY", "useDefaultHeadX", "modifiers", "Object", "assign", "strokePx", "calculateKeyProps", "buildStem", "autoStem", "reset", "buildFlag", "noteHeadStyles", "map", "noteHead", "buildNoteHeads", "noteHeadStyle", "setStyle", "stave", "setStave", "calcNoteDisplacements", "setBeam", "beam", "stem", "setExtension", "getStemExtension", "setStem", "hide", "keys", "get<PERSON><PERSON><PERSON>", "lastLine", "start", "end", "step", "noteProps", "notehead", "customGlyphCode", "fontInfo", "<PERSON><PERSON><PERSON><PERSON>", "calculateOptimalStemDirection", "MIDDLE_LINE", "decider", "DOWN", "key", "options", "keyProperties", "sort", "a", "b", "getBoundingBox", "boundingBox", "getAbsoluteX", "ys", "mergeWith", "yTop", "yBottom", "getNoteHeadBounds", "noteStemHeight", "getHeight", "stemY", "flag", "hasFlag", "bbFlag", "getLineNumber", "isTopNote", "resultLine", "thisLine", "val", "codeHead", "isChord", "getStemX", "getCenterGlyphX", "WIDTH", "getYForTopText", "textLine", "extents", "getStemExtents", "min", "checkStave", "topY", "annotationSpacing", "getYForBottomText", "baseY", "getY", "setYs", "setYBounds", "setNoteDisplaced", "getTieRightX", "tieStartX", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rightDisplacedHeadPx", "modifierContext", "getRightShift", "getTieLeftX", "tieEndX", "leftDisplacedHeadPx", "getLineForRest", "restLine", "top", "bot", "getModifierStartXY", "position", "preFormatted", "ABOVE", "BELOW", "LEFT", "RIGHT", "Position", "x", "forceFlagRight", "getWidth", "restShift", "getText", "restDoubleWhole", "restWhole", "restHalf", "restQuarter", "rest8th", "rest16th", "rest32nd", "rest64th", "rest128th", "y", "getSpacingBetweenLines", "style", "setGroupStyle", "setStemStyle", "getStem", "getStemStyle", "setLedgerLineStyle", "getLedgerLineStyle", "setFlagStyle", "getFlagStyle", "noteHeads", "setKeyStyle", "setLeftDisplacedHeadPx", "setRightDisplacedHeadPx", "preFormat", "noteHeadPadding", "width", "shouldDrawFlag", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "nonDisplacedX", "displacedX", "highestLine", "getNumLines", "lowestLine", "highestDisplacedLine", "lowestDisplacedLine", "highestNonDisplacedLine", "lowestNonDisplacedLine", "getLine", "getNoteHeadBeginX", "getNoteHeadEndX", "xBegin", "slice", "drawLedgerLines", "ctx", "checkContext", "doubleWidth", "minX", "drawLedgerLine", "normal", "ledgerWidth", "beginPath", "moveTo", "lineTo", "stroke", "getDefaultLedgerLineStyle", "save", "applyStyle", "getYForNote", "restore", "drawModifiers", "noteheadParam", "modifier", "checkIndex", "setContext", "drawWithStyle", "codeFlagUp", "hasNoBeam", "drawFlag", "flagX", "STEM_WIDTH", "flagY", "setX", "setY", "drawNoteHeads", "drawStem", "stemOptions", "adjustHeightForFlag", "superStemExtension", "midLineDistance", "linesOverOctaveFromMidLine", "getStave", "spacingBetweenLines", "shouldRenderStem", "stemX", "setNoteHeadXBounds", "openGroup", "getAttribute", "bb", "pointerRect", "getX", "getW", "getH", "closeGroup", "setRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/stavenote.js"], "sourcesContent": ["import { BoundingBox } from './boundingbox.js';\nimport { Glyphs } from './glyphs.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier } from './modifier.js';\nimport { Note } from './note.js';\nimport { NoteHead } from './notehead.js';\nimport { Stem } from './stem.js';\nimport { StemmableNote } from './stemmablenote.js';\nimport { Tables } from './tables.js';\nimport { defined, log, midLine, RuntimeError } from './util.js';\nfunction L(...args) {\n    if (StaveNote.DEBUG)\n        log('VexFlow.StaveNote', args);\n}\nconst isInnerNoteIndex = (note, index) => index === (note.getStemDirection() === Stem.UP ? note.keyProps.length - 1 : 0);\nfunction shiftRestVertical(rest, note, dir) {\n    const delta = dir;\n    rest.line += delta;\n    rest.maxLine += delta;\n    rest.minLine += delta;\n    rest.note.setKeyLine(0, rest.note.getKeyLine(0) + delta);\n}\nfunction centerRest(rest, noteU, noteL) {\n    const delta = rest.line - midLine(noteU.minLine, noteL.maxLine);\n    rest.note.setKeyLine(0, rest.note.getKeyLine(0) - delta);\n    rest.line -= delta;\n    rest.maxLine -= delta;\n    rest.minLine -= delta;\n}\nexport class StaveNote extends StemmableNote {\n    static get CATEGORY() {\n        return \"StaveNote\";\n    }\n    static get LEDGER_LINE_OFFSET() {\n        return 3;\n    }\n    static get minNoteheadPadding() {\n        return Metrics.get('NoteHead.minPadding');\n    }\n    static format(notes, state) {\n        if (!notes || notes.length < 2)\n            return false;\n        const notesList = [];\n        for (let i = 0; i < notes.length; i++) {\n            const props = notes[i].sortedKeyProps;\n            const line = props[0].keyProps.line;\n            let minL = props[props.length - 1].keyProps.line;\n            const stemDirection = notes[i].getStemDirection();\n            const stemMax = notes[i].getStemLength() / 10;\n            const stemMin = notes[i].getStemMinimumLength() / 10;\n            let maxL;\n            if (notes[i].isRest()) {\n                maxL =\n                    line +\n                        Math.ceil(notes[i]._noteHeads[0].getTextMetrics().actualBoundingBoxAscent / Tables.STAVE_LINE_DISTANCE);\n                minL =\n                    line -\n                        Math.ceil(notes[i]._noteHeads[0].getTextMetrics().actualBoundingBoxDescent / Tables.STAVE_LINE_DISTANCE);\n            }\n            else {\n                maxL =\n                    stemDirection === 1 ? props[props.length - 1].keyProps.line + stemMax : props[props.length - 1].keyProps.line;\n                minL = stemDirection === 1 ? props[0].keyProps.line : props[0].keyProps.line - stemMax;\n            }\n            notesList.push({\n                line: props[0].keyProps.line,\n                maxLine: maxL,\n                minLine: minL,\n                isrest: notes[i].isRest(),\n                stemDirection,\n                stemMax,\n                stemMin,\n                voiceShift: notes[i].getVoiceShiftWidth(),\n                isDisplaced: notes[i].isDisplaced(),\n                note: notes[i],\n            });\n        }\n        let voices = 0;\n        let noteU = undefined;\n        let noteM = undefined;\n        let noteL = undefined;\n        const draw = [false, false, false];\n        for (let i = 0; i < notesList.length; i++) {\n            draw[i] = notesList[i].note.renderOptions.draw !== false;\n        }\n        if (draw[0] && draw[1] && draw[2]) {\n            voices = 3;\n            noteU = notesList[0];\n            noteM = notesList[1];\n            noteL = notesList[2];\n        }\n        else if (draw[0] && draw[1]) {\n            voices = 2;\n            noteU = notesList[0];\n            noteL = notesList[1];\n        }\n        else if (draw[0] && draw[2]) {\n            voices = 2;\n            noteU = notesList[0];\n            noteL = notesList[2];\n        }\n        else if (draw[1] && draw[2]) {\n            voices = 2;\n            noteU = notesList[1];\n            noteL = notesList[2];\n        }\n        else {\n            return true;\n        }\n        if (voices === 2 && noteU.stemDirection === -1 && noteL.stemDirection === 1) {\n            noteU = notesList[1];\n            noteL = notesList[0];\n        }\n        const voiceXShift = Math.max(noteU.voiceShift, noteL.voiceShift);\n        let xShift = 0;\n        if (voices === 2) {\n            const lineSpacing = noteU.note.hasStem() && noteL.note.hasStem() && noteU.stemDirection === noteL.stemDirection ? 0.0 : 0.5;\n            if (noteL.isrest && noteU.isrest && noteU.note.duration === noteL.note.duration) {\n                noteL.note.renderOptions.draw = false;\n            }\n            else if (noteU.minLine <= noteL.maxLine + lineSpacing) {\n                if (noteU.isrest) {\n                    shiftRestVertical(noteU, noteL, 1);\n                }\n                else if (noteL.isrest) {\n                    shiftRestVertical(noteL, noteU, -1);\n                }\n                else {\n                    const lineDiff = Math.abs(noteU.line - noteL.line);\n                    if (noteU.note.hasStem() && noteL.note.hasStem()) {\n                        const noteUHead = noteU.note.sortedKeyProps[0].keyProps.code;\n                        const noteLHead = noteL.note.sortedKeyProps[noteL.note.sortedKeyProps.length - 1].keyProps.code;\n                        if (!Tables.UNISON ||\n                            noteUHead !== noteLHead ||\n                            noteU.note.getModifiers().filter((item) => item.getCategory() === \"Dot\" && item.getIndex() === 0)\n                                .length !==\n                                noteL.note.getModifiers().filter((item) => item.getCategory() === \"Dot\" && item.getIndex() === 0)\n                                    .length ||\n                            (lineDiff < 1 && lineDiff > 0) ||\n                            JSON.stringify(noteU.note.getStyle()) !== JSON.stringify(noteL.note.getStyle())) {\n                            xShift = voiceXShift + 2;\n                            if (noteU.stemDirection === noteL.stemDirection) {\n                                noteU.note.setXShift(xShift);\n                            }\n                            else {\n                                noteL.note.setXShift(xShift);\n                            }\n                        }\n                        else if (noteU.note.voice !== noteL.note.voice) {\n                            if (noteU.stemDirection === noteL.stemDirection) {\n                                if (noteU.line !== noteL.line) {\n                                    xShift = voiceXShift + 2;\n                                    noteU.note.setXShift(xShift);\n                                }\n                                else {\n                                    if (noteL.stemDirection === 1) {\n                                        noteL.stemDirection = -1;\n                                        noteL.note.setStemDirection(-1);\n                                    }\n                                }\n                            }\n                        }\n                    }\n                    else if (lineDiff < 1) {\n                        xShift = voiceXShift + 2;\n                        if (noteU.note.duration < noteL.note.duration) {\n                            noteU.note.setXShift(xShift);\n                        }\n                        else {\n                            noteL.note.setXShift(xShift);\n                        }\n                    }\n                    else if (noteU.note.hasStem()) {\n                        noteU.stemDirection = -noteU.note.getStemDirection();\n                        noteU.note.setStemDirection(noteU.stemDirection);\n                    }\n                    else if (noteL.note.hasStem()) {\n                        noteL.stemDirection = -noteL.note.getStemDirection();\n                        noteL.note.setStemDirection(noteL.stemDirection);\n                    }\n                }\n            }\n            state.rightShift += xShift;\n            return true;\n        }\n        if (!noteM)\n            throw new RuntimeError('InvalidState', 'noteM not defined.');\n        if (noteM.isrest && !noteU.isrest && !noteL.isrest) {\n            if (noteU.minLine <= noteM.maxLine || noteM.minLine <= noteL.maxLine) {\n                const restHeight = noteM.maxLine - noteM.minLine;\n                const space = noteU.minLine - noteL.maxLine;\n                if (restHeight < space) {\n                    centerRest(noteM, noteU, noteL);\n                }\n                else {\n                    xShift = voiceXShift + 2;\n                    noteM.note.setXShift(xShift);\n                    if (noteL.note.hasBeam() === false) {\n                        noteL.stemDirection = -1;\n                        noteL.note.setStemDirection(-1);\n                    }\n                    if (noteU.minLine <= noteL.maxLine && noteU.note.hasBeam() === false) {\n                        noteU.stemDirection = 1;\n                        noteU.note.setStemDirection(1);\n                    }\n                }\n                state.rightShift += xShift;\n                return true;\n            }\n        }\n        if (noteU.isrest && noteM.isrest && noteL.isrest) {\n            noteU.note.renderOptions.draw = false;\n            noteL.note.renderOptions.draw = false;\n            state.rightShift += xShift;\n            return true;\n        }\n        if (noteM.isrest && noteU.isrest && noteM.minLine <= noteL.maxLine) {\n            noteM.note.renderOptions.draw = false;\n        }\n        if (noteM.isrest && noteL.isrest && noteU.minLine <= noteM.maxLine) {\n            noteM.note.renderOptions.draw = false;\n        }\n        if (noteU.isrest && noteU.minLine <= noteM.maxLine) {\n            shiftRestVertical(noteU, noteM, 1);\n        }\n        if (noteL.isrest && noteM.minLine <= noteL.maxLine) {\n            shiftRestVertical(noteL, noteM, -1);\n        }\n        if (noteU.minLine <= noteM.maxLine + 0.5 || noteM.minLine <= noteL.maxLine) {\n            xShift = voiceXShift + 2;\n            noteM.note.setXShift(xShift);\n            if (noteL.note.hasBeam() === false) {\n                noteL.stemDirection = -1;\n                noteL.note.setStemDirection(-1);\n            }\n            if (noteU.minLine <= noteL.maxLine && noteU.note.hasBeam() === false) {\n                noteU.stemDirection = 1;\n                noteU.note.setStemDirection(1);\n            }\n        }\n        state.rightShift += xShift;\n        return true;\n    }\n    static postFormat(notes) {\n        if (!notes)\n            return false;\n        notes.forEach((note) => note.postFormat());\n        return true;\n    }\n    constructor(noteStruct) {\n        var _a, _b, _c;\n        super(noteStruct);\n        this.minLine = 0;\n        this.maxLine = 0;\n        this.sortedKeyProps = [];\n        this.ledgerLineStyle = {};\n        this.clef = (_a = noteStruct.clef) !== null && _a !== void 0 ? _a : 'treble';\n        this.octaveShift = (_b = noteStruct.octaveShift) !== null && _b !== void 0 ? _b : 0;\n        this.glyphProps = Note.getGlyphProps(this.duration, this.noteType);\n        defined(this.glyphProps, 'BadArguments', `No glyph found for duration '${this.duration}' and type '${this.noteType}'`);\n        this.displaced = false;\n        this.dotShiftY = 0;\n        this.useDefaultHeadX = false;\n        this._noteHeads = [];\n        this.modifiers = [];\n        this.renderOptions = Object.assign(Object.assign({}, this.renderOptions), { strokePx: noteStruct.strokePx || StaveNote.LEDGER_LINE_OFFSET });\n        this.calculateKeyProps();\n        this.buildStem();\n        if (noteStruct.autoStem) {\n            this.autoStem();\n        }\n        else {\n            this.setStemDirection((_c = noteStruct.stemDirection) !== null && _c !== void 0 ? _c : Stem.UP);\n        }\n        this.reset();\n        this.buildFlag();\n    }\n    reset() {\n        super.reset();\n        const noteHeadStyles = this._noteHeads.map((noteHead) => noteHead.getStyle());\n        this.buildNoteHeads();\n        this._noteHeads.forEach((noteHead, index) => {\n            const noteHeadStyle = noteHeadStyles[index];\n            if (noteHeadStyle)\n                noteHead.setStyle(noteHeadStyle);\n        });\n        const stave = this.stave;\n        if (stave) {\n            this.setStave(stave);\n        }\n        this.calcNoteDisplacements();\n        return this;\n    }\n    setBeam(beam) {\n        this.beam = beam;\n        this.calcNoteDisplacements();\n        if (this.stem) {\n            this.stem.setExtension(this.getStemExtension());\n        }\n        return this;\n    }\n    buildStem() {\n        this.setStem(new Stem({ hide: this.isRest() }));\n        return this;\n    }\n    buildNoteHeads() {\n        this._noteHeads = [];\n        const stemDirection = this.getStemDirection();\n        const keys = this.getKeys();\n        let lastLine = undefined;\n        let lineDiff = undefined;\n        let displaced = false;\n        let start;\n        let end;\n        let step;\n        if (stemDirection === Stem.UP) {\n            start = 0;\n            end = keys.length;\n            step = 1;\n        }\n        else {\n            start = keys.length - 1;\n            end = -1;\n            step = -1;\n        }\n        for (let i = start; i !== end; i += step) {\n            const noteProps = this.sortedKeyProps[i].keyProps;\n            const line = noteProps.line;\n            if (lastLine === undefined) {\n                lastLine = line;\n            }\n            else {\n                lineDiff = Math.abs(lastLine - line);\n                if (lineDiff === 0 || lineDiff === 0.5) {\n                    displaced = !displaced;\n                }\n                else {\n                    displaced = false;\n                    this.useDefaultHeadX = true;\n                }\n            }\n            lastLine = line;\n            const notehead = new NoteHead({\n                duration: this.duration,\n                noteType: this.noteType,\n                displaced,\n                stemDirection,\n                customGlyphCode: noteProps.code,\n                line: noteProps.line,\n            });\n            notehead.fontInfo = this.fontInfo;\n            this.addChild(notehead);\n            this._noteHeads[this.sortedKeyProps[i].index] = notehead;\n        }\n        return this._noteHeads;\n    }\n    autoStem() {\n        this.setStemDirection(this.calculateOptimalStemDirection());\n    }\n    calculateOptimalStemDirection() {\n        this.minLine = this.sortedKeyProps[0].keyProps.line;\n        this.maxLine = this.sortedKeyProps[this.keyProps.length - 1].keyProps.line;\n        const MIDDLE_LINE = 3;\n        const decider = (this.minLine + this.maxLine) / 2;\n        const stemDirection = decider < MIDDLE_LINE ? Stem.UP : Stem.DOWN;\n        return stemDirection;\n    }\n    calculateKeyProps() {\n        var _a;\n        let lastLine;\n        for (let i = 0; i < this.keys.length; ++i) {\n            const key = this.keys[i];\n            const options = { octaveShift: (_a = this.octaveShift) !== null && _a !== void 0 ? _a : 0, duration: this.duration };\n            const props = Tables.keyProperties(key, this.clef, this.noteType, options);\n            if (!props) {\n                throw new RuntimeError('BadArguments', `Invalid key for note properties: ${key}`);\n            }\n            if (props.key === 'R') {\n                if (this.duration === '1' || this.duration === 'w') {\n                    props.line = 4;\n                }\n                else {\n                    props.line = 3;\n                }\n            }\n            const line = props.line;\n            if (lastLine === undefined) {\n                lastLine = line;\n            }\n            else {\n                if (Math.abs(lastLine - line) < 1) {\n                    this.displaced = true;\n                    props.displaced = true;\n                    if (this.keyProps.length > 0) {\n                        this.keyProps[i - 1].displaced = true;\n                    }\n                }\n            }\n            lastLine = line;\n            this.keyProps.push(props);\n        }\n        this.keyProps.forEach((keyProps, index) => {\n            this.sortedKeyProps.push({ keyProps, index });\n        });\n        this.sortedKeyProps.sort((a, b) => a.keyProps.line - b.keyProps.line);\n    }\n    getBoundingBox() {\n        const boundingBox = new BoundingBox(this.getAbsoluteX(), this.ys[0], 0, 0);\n        this._noteHeads.forEach((notehead) => {\n            boundingBox.mergeWith(notehead.getBoundingBox());\n        });\n        const { yTop, yBottom } = this.getNoteHeadBounds();\n        if (!this.isRest() && this.hasStem()) {\n            const noteStemHeight = this.stem.getHeight();\n            const stemY = this.getStemDirection() === Stem.DOWN\n                ? yTop - noteStemHeight - this.flag.getTextMetrics().actualBoundingBoxDescent\n                : yBottom - noteStemHeight + this.flag.getTextMetrics().actualBoundingBoxAscent;\n            boundingBox.mergeWith(new BoundingBox(this.getAbsoluteX(), stemY, 0, 0));\n        }\n        if (this.hasFlag()) {\n            const bbFlag = this.flag.getBoundingBox();\n            boundingBox.mergeWith(bbFlag);\n        }\n        for (let i = 0; i < this.modifiers.length; i++) {\n            boundingBox.mergeWith(this.modifiers[i].getBoundingBox());\n        }\n        return boundingBox;\n    }\n    getLineNumber(isTopNote) {\n        if (!this.keyProps.length) {\n            throw new RuntimeError('NoKeyProps', \"Can't get bottom note line, because note is not initialized properly.\");\n        }\n        let resultLine = this.keyProps[0].line;\n        for (let i = 0; i < this.keyProps.length; i++) {\n            const thisLine = this.keyProps[i].line;\n            if (isTopNote) {\n                if (thisLine > resultLine)\n                    resultLine = thisLine;\n            }\n            else {\n                if (thisLine < resultLine)\n                    resultLine = thisLine;\n            }\n        }\n        return resultLine;\n    }\n    isRest() {\n        const val = this.glyphProps.codeHead;\n        return val >= '\\ue4e0' && val <= '\\ue4ff';\n    }\n    isChord() {\n        return !this.isRest() && this.keys.length > 1;\n    }\n    hasStem() {\n        return this.glyphProps.stem;\n    }\n    hasFlag() {\n        return super.hasFlag() && !this.isRest();\n    }\n    getStemX() {\n        if (this.noteType === 'r') {\n            return this.getCenterGlyphX();\n        }\n        else {\n            return super.getStemX() + (this.stemDirection ? Stem.WIDTH / (2 * -this.stemDirection) : 0);\n        }\n    }\n    getYForTopText(textLine) {\n        const extents = this.getStemExtents();\n        return Math.min(this.checkStave().getYForTopText(textLine), extents.topY - this.renderOptions.annotationSpacing * (textLine + 1));\n    }\n    getYForBottomText(textLine) {\n        const extents = this.getStemExtents();\n        return Math.max(this.checkStave().getYForTopText(textLine), extents.baseY + this.renderOptions.annotationSpacing * textLine);\n    }\n    setStave(stave) {\n        super.setStave(stave);\n        const ys = this._noteHeads.map((notehead) => {\n            notehead.setStave(stave);\n            return notehead.getY();\n        });\n        this.setYs(ys);\n        if (this.stem) {\n            const { yTop, yBottom } = this.getNoteHeadBounds();\n            this.stem.setYBounds(yTop, yBottom);\n        }\n        return this;\n    }\n    isDisplaced() {\n        return this.displaced;\n    }\n    setNoteDisplaced(displaced) {\n        this.displaced = displaced;\n        return this;\n    }\n    getTieRightX() {\n        let tieStartX = this.getAbsoluteX();\n        tieStartX += this.getGlyphWidth() + this.xShift + this.rightDisplacedHeadPx;\n        if (this.modifierContext)\n            tieStartX += this.modifierContext.getRightShift();\n        return tieStartX;\n    }\n    getTieLeftX() {\n        let tieEndX = this.getAbsoluteX();\n        tieEndX += this.xShift - this.leftDisplacedHeadPx;\n        return tieEndX;\n    }\n    getLineForRest() {\n        let restLine = this.keyProps[0].line;\n        if (this.keyProps.length > 1) {\n            const lastLine = this.keyProps[this.keyProps.length - 1].line;\n            const top = Math.max(restLine, lastLine);\n            const bot = Math.min(restLine, lastLine);\n            restLine = midLine(top, bot);\n        }\n        return restLine;\n    }\n    getModifierStartXY(position, index, options = {}) {\n        if (!this.preFormatted) {\n            throw new RuntimeError('UnformattedNote', \"Can't call GetModifierStartXY on an unformatted note\");\n        }\n        if (this.ys.length === 0) {\n            throw new RuntimeError('NoYValues', 'No Y-Values calculated for this note.');\n        }\n        const { ABOVE, BELOW, LEFT, RIGHT } = Modifier.Position;\n        let x = 0;\n        if (position === LEFT) {\n            x = -1 * 2;\n        }\n        else if (position === RIGHT) {\n            x = this.getGlyphWidth() + this.xShift + 2;\n            if (this.stemDirection === Stem.UP &&\n                this.hasFlag() &&\n                (options.forceFlagRight || isInnerNoteIndex(this, index))) {\n                x += this.flag.getWidth();\n            }\n        }\n        else if (position === BELOW || position === ABOVE) {\n            x = this.getGlyphWidth() / 2;\n        }\n        let restShift = 0;\n        switch (this._noteHeads[index].getText()) {\n            case Glyphs.restDoubleWhole:\n            case Glyphs.restWhole:\n                restShift += 0.5;\n                break;\n            case Glyphs.restHalf:\n            case Glyphs.restQuarter:\n            case Glyphs.rest8th:\n            case Glyphs.rest16th:\n                restShift -= 0.5;\n                break;\n            case Glyphs.rest32nd:\n            case Glyphs.rest64th:\n                restShift -= 1.5;\n                break;\n            case Glyphs.rest128th:\n                restShift -= 2.5;\n                break;\n        }\n        return {\n            x: this.getAbsoluteX() + x,\n            y: this.ys[index] + restShift * this.checkStave().getSpacingBetweenLines(),\n        };\n    }\n    setStyle(style) {\n        return super.setGroupStyle(style);\n    }\n    setStemStyle(style) {\n        const stem = this.getStem();\n        if (stem)\n            stem.setStyle(style);\n        return this;\n    }\n    getStemStyle() {\n        var _a;\n        return (_a = this.stem) === null || _a === void 0 ? void 0 : _a.getStyle();\n    }\n    setLedgerLineStyle(style) {\n        this.ledgerLineStyle = style;\n    }\n    getLedgerLineStyle() {\n        return this.ledgerLineStyle;\n    }\n    setFlagStyle(style) {\n        this.flag.setStyle(style);\n    }\n    getFlagStyle() {\n        return this.flag.getStyle();\n    }\n    getGlyphWidth() {\n        return this.noteHeads[0].getWidth();\n    }\n    setKeyStyle(index, style) {\n        this._noteHeads[index].setStyle(style);\n        return this;\n    }\n    setKeyLine(index, line) {\n        this.keyProps[index].line = line;\n        this.reset();\n        return this;\n    }\n    getKeyLine(index) {\n        return this.keyProps[index].line;\n    }\n    getVoiceShiftWidth() {\n        return this.getGlyphWidth() * (this.displaced ? 2 : 1);\n    }\n    calcNoteDisplacements() {\n        this.setLeftDisplacedHeadPx(this.displaced && this.stemDirection === Stem.DOWN ? this.getGlyphWidth() : 0);\n        this.setRightDisplacedHeadPx(!this.hasFlag() && this.displaced && this.stemDirection === Stem.UP ? this.getGlyphWidth() : 0);\n    }\n    preFormat() {\n        if (this.preFormatted)\n            return;\n        let noteHeadPadding = 0;\n        if (this.modifierContext) {\n            this.modifierContext.preFormat();\n            if (this.modifierContext.getWidth() === 0) {\n                noteHeadPadding = StaveNote.minNoteheadPadding;\n            }\n        }\n        let width = this.getGlyphWidth() + this.leftDisplacedHeadPx + this.rightDisplacedHeadPx + noteHeadPadding;\n        if (this.shouldDrawFlag() && this.stemDirection === Stem.UP) {\n            width += this.getGlyphWidth();\n        }\n        this.setWidth(width);\n        this.preFormatted = true;\n    }\n    getNoteHeadBounds() {\n        let yTop = +Infinity;\n        let yBottom = -Infinity;\n        let nonDisplacedX;\n        let displacedX;\n        let highestLine = this.checkStave().getNumLines();\n        let lowestLine = 1;\n        let highestDisplacedLine;\n        let lowestDisplacedLine;\n        let highestNonDisplacedLine = highestLine;\n        let lowestNonDisplacedLine = lowestLine;\n        this._noteHeads.forEach((notehead) => {\n            const line = notehead.getLine();\n            const y = notehead.getY();\n            yTop = Math.min(y, yTop);\n            yBottom = Math.max(y, yBottom);\n            if (displacedX === undefined && notehead.isDisplaced()) {\n                displacedX = notehead.getAbsoluteX();\n            }\n            if (nonDisplacedX === undefined && !notehead.isDisplaced()) {\n                nonDisplacedX = notehead.getAbsoluteX();\n            }\n            highestLine = Math.max(line, highestLine);\n            lowestLine = Math.min(line, lowestLine);\n            if (notehead.isDisplaced()) {\n                highestDisplacedLine = highestDisplacedLine === undefined ? line : Math.max(line, highestDisplacedLine);\n                lowestDisplacedLine = lowestDisplacedLine === undefined ? line : Math.min(line, lowestDisplacedLine);\n            }\n            else {\n                highestNonDisplacedLine = Math.max(line, highestNonDisplacedLine);\n                lowestNonDisplacedLine = Math.min(line, lowestNonDisplacedLine);\n            }\n        }, this);\n        return {\n            yTop,\n            yBottom,\n            displacedX,\n            nonDisplacedX,\n            highestLine,\n            lowestLine,\n            highestDisplacedLine,\n            lowestDisplacedLine,\n            highestNonDisplacedLine,\n            lowestNonDisplacedLine,\n        };\n    }\n    getNoteHeadBeginX() {\n        return this.getAbsoluteX() + this.xShift;\n    }\n    getNoteHeadEndX() {\n        const xBegin = this.getNoteHeadBeginX();\n        return xBegin + this.getGlyphWidth();\n    }\n    get noteHeads() {\n        return this._noteHeads.slice();\n    }\n    drawLedgerLines() {\n        const stave = this.checkStave();\n        const { renderOptions: { strokePx }, } = this;\n        const ctx = this.checkContext();\n        const width = this.getGlyphWidth() + strokePx * 2;\n        const doubleWidth = 2 * (this.getGlyphWidth() + strokePx) - Stem.WIDTH / 2;\n        if (this.isRest())\n            return;\n        if (!ctx) {\n            throw new RuntimeError('NoCanvasContext', \"Can't draw without a canvas context.\");\n        }\n        const { highestLine, lowestLine, highestDisplacedLine, highestNonDisplacedLine, lowestDisplacedLine, lowestNonDisplacedLine, displacedX, nonDisplacedX, } = this.getNoteHeadBounds();\n        if (highestLine < 6 && lowestLine > 0)\n            return;\n        const minX = Math.min(displacedX !== null && displacedX !== void 0 ? displacedX : 0, nonDisplacedX !== null && nonDisplacedX !== void 0 ? nonDisplacedX : 0);\n        const drawLedgerLine = (y, normal, displaced) => {\n            let x;\n            if (displaced && normal)\n                x = minX - strokePx;\n            else if (normal)\n                x = (nonDisplacedX !== null && nonDisplacedX !== void 0 ? nonDisplacedX : 0) - strokePx;\n            else\n                x = (displacedX !== null && displacedX !== void 0 ? displacedX : 0) - strokePx;\n            const ledgerWidth = normal && displaced ? doubleWidth : width;\n            ctx.beginPath();\n            ctx.moveTo(x, y);\n            ctx.lineTo(x + ledgerWidth, y);\n            ctx.stroke();\n        };\n        const style = Object.assign(Object.assign({}, stave.getDefaultLedgerLineStyle()), this.getLedgerLineStyle());\n        ctx.save();\n        this.applyStyle(ctx, style);\n        for (let line = 6; line <= highestLine; ++line) {\n            const normal = nonDisplacedX !== undefined && line <= highestNonDisplacedLine;\n            const displaced = highestDisplacedLine !== undefined && line <= highestDisplacedLine;\n            drawLedgerLine(stave.getYForNote(line), normal, displaced);\n        }\n        for (let line = 0; line >= lowestLine; --line) {\n            const normal = nonDisplacedX !== undefined && line >= lowestNonDisplacedLine;\n            const displaced = lowestDisplacedLine !== undefined && line >= lowestDisplacedLine;\n            drawLedgerLine(stave.getYForNote(line), normal, displaced);\n        }\n        ctx.restore();\n    }\n    drawModifiers(noteheadParam) {\n        const ctx = this.checkContext();\n        for (let i = 0; i < this.modifiers.length; i++) {\n            const modifier = this.modifiers[i];\n            const index = modifier.checkIndex();\n            const notehead = this._noteHeads[index];\n            if (notehead === noteheadParam) {\n                modifier.setContext(ctx);\n                modifier.drawWithStyle();\n            }\n        }\n    }\n    shouldDrawFlag() {\n        const hasStem = this.stem !== undefined;\n        const hasFlag = this.glyphProps.codeFlagUp !== undefined;\n        const hasNoBeam = this.beam === undefined;\n        return hasStem && hasFlag && hasNoBeam && !this.isRest();\n    }\n    drawFlag() {\n        const ctx = this.checkContext();\n        if (!ctx) {\n            throw new RuntimeError('NoCanvasContext', \"Can't draw without a canvas context.\");\n        }\n        if (this.shouldDrawFlag()) {\n            const { yTop, yBottom } = this.getNoteHeadBounds();\n            const noteStemHeight = this.stem.getHeight();\n            const flagX = this.getStemX() - Tables.STEM_WIDTH / 2;\n            const flagY = this.getStemDirection() === Stem.DOWN\n                ?\n                    yTop - noteStemHeight - this.flag.getTextMetrics().actualBoundingBoxDescent\n                :\n                    yBottom - noteStemHeight + this.flag.getTextMetrics().actualBoundingBoxAscent;\n            this.flag.setContext(ctx).setX(flagX).setY(flagY).drawWithStyle();\n        }\n    }\n    drawNoteHeads() {\n        const ctx = this.checkContext();\n        this._noteHeads.forEach((notehead) => {\n            notehead.setContext(ctx).drawWithStyle();\n        });\n    }\n    drawStem(stemOptions) {\n        const ctx = this.checkContext();\n        if (stemOptions) {\n            this.setStem(new Stem(stemOptions));\n        }\n        if (this.shouldDrawFlag() && this.stem) {\n            this.stem.adjustHeightForFlag();\n        }\n        if (this.stem) {\n            this.stem.setContext(ctx).drawWithStyle();\n        }\n    }\n    getStemExtension() {\n        const superStemExtension = super.getStemExtension();\n        if (!this.glyphProps.stem) {\n            return superStemExtension;\n        }\n        const stemDirection = this.getStemDirection();\n        if (stemDirection !== this.calculateOptimalStemDirection()) {\n            return superStemExtension;\n        }\n        let midLineDistance;\n        const MIDDLE_LINE = 3;\n        if (stemDirection === Stem.UP) {\n            midLineDistance = MIDDLE_LINE - this.maxLine;\n        }\n        else {\n            midLineDistance = this.minLine - MIDDLE_LINE;\n        }\n        const linesOverOctaveFromMidLine = midLineDistance - 3.5;\n        if (linesOverOctaveFromMidLine <= 0) {\n            return superStemExtension;\n        }\n        const stave = this.getStave();\n        let spacingBetweenLines = 10;\n        if (stave !== undefined) {\n            spacingBetweenLines = stave.getSpacingBetweenLines();\n        }\n        return superStemExtension + linesOverOctaveFromMidLine * spacingBetweenLines;\n    }\n    draw() {\n        if (this.renderOptions.draw === false)\n            return;\n        if (this.ys.length === 0) {\n            throw new RuntimeError('NoYValues', \"Can't draw note without Y values.\");\n        }\n        const ctx = this.checkContext();\n        const xBegin = this.getNoteHeadBeginX();\n        const shouldRenderStem = this.hasStem() && !this.beam;\n        this._noteHeads.forEach((notehead) => notehead.setX(xBegin));\n        if (this.stem) {\n            const stemX = this.getStemX();\n            this.stem.setNoteHeadXBounds(stemX, stemX);\n        }\n        L('Rendering ', this.isChord() ? 'chord :' : 'note :', this.keys);\n        ctx.openGroup('stavenote', this.getAttribute('id'));\n        this.drawLedgerLines();\n        if (shouldRenderStem)\n            this.drawStem();\n        this.drawNoteHeads();\n        this.drawFlag();\n        const bb = this.getBoundingBox();\n        ctx.pointerRect(bb.getX(), bb.getY(), bb.getW(), bb.getH());\n        ctx.closeGroup();\n        this.setRendered();\n    }\n}\nStaveNote.DEBUG = false;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAEC,YAAY,QAAQ,WAAW;AAC/D,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,SAAS,CAACC,KAAK,EACfN,GAAG,CAAC,mBAAmB,EAAEI,IAAI,CAAC;AACtC;AACA,MAAMG,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAKA,KAAK,MAAMD,IAAI,CAACE,gBAAgB,CAAC,CAAC,KAAKd,IAAI,CAACe,EAAE,GAAGH,IAAI,CAACI,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACxH,SAASC,iBAAiBA,CAACC,IAAI,EAAEP,IAAI,EAAEQ,GAAG,EAAE;EACxC,MAAMC,KAAK,GAAGD,GAAG;EACjBD,IAAI,CAACG,IAAI,IAAID,KAAK;EAClBF,IAAI,CAACI,OAAO,IAAIF,KAAK;EACrBF,IAAI,CAACK,OAAO,IAAIH,KAAK;EACrBF,IAAI,CAACP,IAAI,CAACa,UAAU,CAAC,CAAC,EAAEN,IAAI,CAACP,IAAI,CAACc,UAAU,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC;AAC5D;AACA,SAASM,UAAUA,CAACR,IAAI,EAAES,KAAK,EAAEC,KAAK,EAAE;EACpC,MAAMR,KAAK,GAAGF,IAAI,CAACG,IAAI,GAAGjB,OAAO,CAACuB,KAAK,CAACJ,OAAO,EAAEK,KAAK,CAACN,OAAO,CAAC;EAC/DJ,IAAI,CAACP,IAAI,CAACa,UAAU,CAAC,CAAC,EAAEN,IAAI,CAACP,IAAI,CAACc,UAAU,CAAC,CAAC,CAAC,GAAGL,KAAK,CAAC;EACxDF,IAAI,CAACG,IAAI,IAAID,KAAK;EAClBF,IAAI,CAACI,OAAO,IAAIF,KAAK;EACrBF,IAAI,CAACK,OAAO,IAAIH,KAAK;AACzB;AACA,OAAO,MAAMZ,SAAS,SAASR,aAAa,CAAC;EACzC,WAAW6B,QAAQA,CAAA,EAAG;IAClB,OAAO,WAAW;EACtB;EACA,WAAWC,kBAAkBA,CAAA,EAAG;IAC5B,OAAO,CAAC;EACZ;EACA,WAAWC,kBAAkBA,CAAA,EAAG;IAC5B,OAAOpC,OAAO,CAACqC,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACA,OAAOC,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACD,KAAK,IAAIA,KAAK,CAAClB,MAAM,GAAG,CAAC,EAC1B,OAAO,KAAK;IAChB,MAAMoB,SAAS,GAAG,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAClB,MAAM,EAAEqB,CAAC,EAAE,EAAE;MACnC,MAAMC,KAAK,GAAGJ,KAAK,CAACG,CAAC,CAAC,CAACE,cAAc;MACrC,MAAMlB,IAAI,GAAGiB,KAAK,CAAC,CAAC,CAAC,CAACvB,QAAQ,CAACM,IAAI;MACnC,IAAImB,IAAI,GAAGF,KAAK,CAACA,KAAK,CAACtB,MAAM,GAAG,CAAC,CAAC,CAACD,QAAQ,CAACM,IAAI;MAChD,MAAMoB,aAAa,GAAGP,KAAK,CAACG,CAAC,CAAC,CAACxB,gBAAgB,CAAC,CAAC;MACjD,MAAM6B,OAAO,GAAGR,KAAK,CAACG,CAAC,CAAC,CAACM,aAAa,CAAC,CAAC,GAAG,EAAE;MAC7C,MAAMC,OAAO,GAAGV,KAAK,CAACG,CAAC,CAAC,CAACQ,oBAAoB,CAAC,CAAC,GAAG,EAAE;MACpD,IAAIC,IAAI;MACR,IAAIZ,KAAK,CAACG,CAAC,CAAC,CAACU,MAAM,CAAC,CAAC,EAAE;QACnBD,IAAI,GACAzB,IAAI,GACA2B,IAAI,CAACC,IAAI,CAACf,KAAK,CAACG,CAAC,CAAC,CAACa,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAACC,uBAAuB,GAAGnD,MAAM,CAACoD,mBAAmB,CAAC;QAC/Gb,IAAI,GACAnB,IAAI,GACA2B,IAAI,CAACC,IAAI,CAACf,KAAK,CAACG,CAAC,CAAC,CAACa,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAACG,wBAAwB,GAAGrD,MAAM,CAACoD,mBAAmB,CAAC;MACpH,CAAC,MACI;QACDP,IAAI,GACAL,aAAa,KAAK,CAAC,GAAGH,KAAK,CAACA,KAAK,CAACtB,MAAM,GAAG,CAAC,CAAC,CAACD,QAAQ,CAACM,IAAI,GAAGqB,OAAO,GAAGJ,KAAK,CAACA,KAAK,CAACtB,MAAM,GAAG,CAAC,CAAC,CAACD,QAAQ,CAACM,IAAI;QACjHmB,IAAI,GAAGC,aAAa,KAAK,CAAC,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACvB,QAAQ,CAACM,IAAI,GAAGiB,KAAK,CAAC,CAAC,CAAC,CAACvB,QAAQ,CAACM,IAAI,GAAGqB,OAAO;MAC1F;MACAN,SAAS,CAACmB,IAAI,CAAC;QACXlC,IAAI,EAAEiB,KAAK,CAAC,CAAC,CAAC,CAACvB,QAAQ,CAACM,IAAI;QAC5BC,OAAO,EAAEwB,IAAI;QACbvB,OAAO,EAAEiB,IAAI;QACbgB,MAAM,EAAEtB,KAAK,CAACG,CAAC,CAAC,CAACU,MAAM,CAAC,CAAC;QACzBN,aAAa;QACbC,OAAO;QACPE,OAAO;QACPa,UAAU,EAAEvB,KAAK,CAACG,CAAC,CAAC,CAACqB,kBAAkB,CAAC,CAAC;QACzCC,WAAW,EAAEzB,KAAK,CAACG,CAAC,CAAC,CAACsB,WAAW,CAAC,CAAC;QACnChD,IAAI,EAAEuB,KAAK,CAACG,CAAC;MACjB,CAAC,CAAC;IACN;IACA,IAAIuB,MAAM,GAAG,CAAC;IACd,IAAIjC,KAAK,GAAGkC,SAAS;IACrB,IAAIC,KAAK,GAAGD,SAAS;IACrB,IAAIjC,KAAK,GAAGiC,SAAS;IACrB,MAAME,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClC,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACpB,MAAM,EAAEqB,CAAC,EAAE,EAAE;MACvC0B,IAAI,CAAC1B,CAAC,CAAC,GAAGD,SAAS,CAACC,CAAC,CAAC,CAAC1B,IAAI,CAACqD,aAAa,CAACD,IAAI,KAAK,KAAK;IAC5D;IACA,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;MAC/BH,MAAM,GAAG,CAAC;MACVjC,KAAK,GAAGS,SAAS,CAAC,CAAC,CAAC;MACpB0B,KAAK,GAAG1B,SAAS,CAAC,CAAC,CAAC;MACpBR,KAAK,GAAGQ,SAAS,CAAC,CAAC,CAAC;IACxB,CAAC,MACI,IAAI2B,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;MACzBH,MAAM,GAAG,CAAC;MACVjC,KAAK,GAAGS,SAAS,CAAC,CAAC,CAAC;MACpBR,KAAK,GAAGQ,SAAS,CAAC,CAAC,CAAC;IACxB,CAAC,MACI,IAAI2B,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;MACzBH,MAAM,GAAG,CAAC;MACVjC,KAAK,GAAGS,SAAS,CAAC,CAAC,CAAC;MACpBR,KAAK,GAAGQ,SAAS,CAAC,CAAC,CAAC;IACxB,CAAC,MACI,IAAI2B,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;MACzBH,MAAM,GAAG,CAAC;MACVjC,KAAK,GAAGS,SAAS,CAAC,CAAC,CAAC;MACpBR,KAAK,GAAGQ,SAAS,CAAC,CAAC,CAAC;IACxB,CAAC,MACI;MACD,OAAO,IAAI;IACf;IACA,IAAIwB,MAAM,KAAK,CAAC,IAAIjC,KAAK,CAACc,aAAa,KAAK,CAAC,CAAC,IAAIb,KAAK,CAACa,aAAa,KAAK,CAAC,EAAE;MACzEd,KAAK,GAAGS,SAAS,CAAC,CAAC,CAAC;MACpBR,KAAK,GAAGQ,SAAS,CAAC,CAAC,CAAC;IACxB;IACA,MAAM6B,WAAW,GAAGjB,IAAI,CAACkB,GAAG,CAACvC,KAAK,CAAC8B,UAAU,EAAE7B,KAAK,CAAC6B,UAAU,CAAC;IAChE,IAAIU,MAAM,GAAG,CAAC;IACd,IAAIP,MAAM,KAAK,CAAC,EAAE;MACd,MAAMQ,WAAW,GAAGzC,KAAK,CAAChB,IAAI,CAAC0D,OAAO,CAAC,CAAC,IAAIzC,KAAK,CAACjB,IAAI,CAAC0D,OAAO,CAAC,CAAC,IAAI1C,KAAK,CAACc,aAAa,KAAKb,KAAK,CAACa,aAAa,GAAG,GAAG,GAAG,GAAG;MAC3H,IAAIb,KAAK,CAAC4B,MAAM,IAAI7B,KAAK,CAAC6B,MAAM,IAAI7B,KAAK,CAAChB,IAAI,CAAC2D,QAAQ,KAAK1C,KAAK,CAACjB,IAAI,CAAC2D,QAAQ,EAAE;QAC7E1C,KAAK,CAACjB,IAAI,CAACqD,aAAa,CAACD,IAAI,GAAG,KAAK;MACzC,CAAC,MACI,IAAIpC,KAAK,CAACJ,OAAO,IAAIK,KAAK,CAACN,OAAO,GAAG8C,WAAW,EAAE;QACnD,IAAIzC,KAAK,CAAC6B,MAAM,EAAE;UACdvC,iBAAiB,CAACU,KAAK,EAAEC,KAAK,EAAE,CAAC,CAAC;QACtC,CAAC,MACI,IAAIA,KAAK,CAAC4B,MAAM,EAAE;UACnBvC,iBAAiB,CAACW,KAAK,EAAED,KAAK,EAAE,CAAC,CAAC,CAAC;QACvC,CAAC,MACI;UACD,MAAM4C,QAAQ,GAAGvB,IAAI,CAACwB,GAAG,CAAC7C,KAAK,CAACN,IAAI,GAAGO,KAAK,CAACP,IAAI,CAAC;UAClD,IAAIM,KAAK,CAAChB,IAAI,CAAC0D,OAAO,CAAC,CAAC,IAAIzC,KAAK,CAACjB,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;YAC9C,MAAMI,SAAS,GAAG9C,KAAK,CAAChB,IAAI,CAAC4B,cAAc,CAAC,CAAC,CAAC,CAACxB,QAAQ,CAAC2D,IAAI;YAC5D,MAAMC,SAAS,GAAG/C,KAAK,CAACjB,IAAI,CAAC4B,cAAc,CAACX,KAAK,CAACjB,IAAI,CAAC4B,cAAc,CAACvB,MAAM,GAAG,CAAC,CAAC,CAACD,QAAQ,CAAC2D,IAAI;YAC/F,IAAI,CAACzE,MAAM,CAAC2E,MAAM,IACdH,SAAS,KAAKE,SAAS,IACvBhD,KAAK,CAAChB,IAAI,CAACkE,YAAY,CAAC,CAAC,CAACC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,IAAID,IAAI,CAACE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAC5FjE,MAAM,KACPY,KAAK,CAACjB,IAAI,CAACkE,YAAY,CAAC,CAAC,CAACC,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,IAAID,IAAI,CAACE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAC5FjE,MAAM,IACduD,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAE,IAC9BW,IAAI,CAACC,SAAS,CAACxD,KAAK,CAAChB,IAAI,CAACyE,QAAQ,CAAC,CAAC,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACvD,KAAK,CAACjB,IAAI,CAACyE,QAAQ,CAAC,CAAC,CAAC,EAAE;cACjFjB,MAAM,GAAGF,WAAW,GAAG,CAAC;cACxB,IAAItC,KAAK,CAACc,aAAa,KAAKb,KAAK,CAACa,aAAa,EAAE;gBAC7Cd,KAAK,CAAChB,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;cAChC,CAAC,MACI;gBACDvC,KAAK,CAACjB,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;cAChC;YACJ,CAAC,MACI,IAAIxC,KAAK,CAAChB,IAAI,CAAC2E,KAAK,KAAK1D,KAAK,CAACjB,IAAI,CAAC2E,KAAK,EAAE;cAC5C,IAAI3D,KAAK,CAACc,aAAa,KAAKb,KAAK,CAACa,aAAa,EAAE;gBAC7C,IAAId,KAAK,CAACN,IAAI,KAAKO,KAAK,CAACP,IAAI,EAAE;kBAC3B8C,MAAM,GAAGF,WAAW,GAAG,CAAC;kBACxBtC,KAAK,CAAChB,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;gBAChC,CAAC,MACI;kBACD,IAAIvC,KAAK,CAACa,aAAa,KAAK,CAAC,EAAE;oBAC3Bb,KAAK,CAACa,aAAa,GAAG,CAAC,CAAC;oBACxBb,KAAK,CAACjB,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,CAAC,CAAC;kBACnC;gBACJ;cACJ;YACJ;UACJ,CAAC,MACI,IAAIhB,QAAQ,GAAG,CAAC,EAAE;YACnBJ,MAAM,GAAGF,WAAW,GAAG,CAAC;YACxB,IAAItC,KAAK,CAAChB,IAAI,CAAC2D,QAAQ,GAAG1C,KAAK,CAACjB,IAAI,CAAC2D,QAAQ,EAAE;cAC3C3C,KAAK,CAAChB,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;YAChC,CAAC,MACI;cACDvC,KAAK,CAACjB,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;YAChC;UACJ,CAAC,MACI,IAAIxC,KAAK,CAAChB,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;YAC3B1C,KAAK,CAACc,aAAa,GAAG,CAACd,KAAK,CAAChB,IAAI,CAACE,gBAAgB,CAAC,CAAC;YACpDc,KAAK,CAAChB,IAAI,CAAC4E,gBAAgB,CAAC5D,KAAK,CAACc,aAAa,CAAC;UACpD,CAAC,MACI,IAAIb,KAAK,CAACjB,IAAI,CAAC0D,OAAO,CAAC,CAAC,EAAE;YAC3BzC,KAAK,CAACa,aAAa,GAAG,CAACb,KAAK,CAACjB,IAAI,CAACE,gBAAgB,CAAC,CAAC;YACpDe,KAAK,CAACjB,IAAI,CAAC4E,gBAAgB,CAAC3D,KAAK,CAACa,aAAa,CAAC;UACpD;QACJ;MACJ;MACAN,KAAK,CAACqD,UAAU,IAAIrB,MAAM;MAC1B,OAAO,IAAI;IACf;IACA,IAAI,CAACL,KAAK,EACN,MAAM,IAAIzD,YAAY,CAAC,cAAc,EAAE,oBAAoB,CAAC;IAChE,IAAIyD,KAAK,CAACN,MAAM,IAAI,CAAC7B,KAAK,CAAC6B,MAAM,IAAI,CAAC5B,KAAK,CAAC4B,MAAM,EAAE;MAChD,IAAI7B,KAAK,CAACJ,OAAO,IAAIuC,KAAK,CAACxC,OAAO,IAAIwC,KAAK,CAACvC,OAAO,IAAIK,KAAK,CAACN,OAAO,EAAE;QAClE,MAAMmE,UAAU,GAAG3B,KAAK,CAACxC,OAAO,GAAGwC,KAAK,CAACvC,OAAO;QAChD,MAAMmE,KAAK,GAAG/D,KAAK,CAACJ,OAAO,GAAGK,KAAK,CAACN,OAAO;QAC3C,IAAImE,UAAU,GAAGC,KAAK,EAAE;UACpBhE,UAAU,CAACoC,KAAK,EAAEnC,KAAK,EAAEC,KAAK,CAAC;QACnC,CAAC,MACI;UACDuC,MAAM,GAAGF,WAAW,GAAG,CAAC;UACxBH,KAAK,CAACnD,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;UAC5B,IAAIvC,KAAK,CAACjB,IAAI,CAACgF,OAAO,CAAC,CAAC,KAAK,KAAK,EAAE;YAChC/D,KAAK,CAACa,aAAa,GAAG,CAAC,CAAC;YACxBb,KAAK,CAACjB,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACnC;UACA,IAAI5D,KAAK,CAACJ,OAAO,IAAIK,KAAK,CAACN,OAAO,IAAIK,KAAK,CAAChB,IAAI,CAACgF,OAAO,CAAC,CAAC,KAAK,KAAK,EAAE;YAClEhE,KAAK,CAACc,aAAa,GAAG,CAAC;YACvBd,KAAK,CAAChB,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,CAAC;UAClC;QACJ;QACApD,KAAK,CAACqD,UAAU,IAAIrB,MAAM;QAC1B,OAAO,IAAI;MACf;IACJ;IACA,IAAIxC,KAAK,CAAC6B,MAAM,IAAIM,KAAK,CAACN,MAAM,IAAI5B,KAAK,CAAC4B,MAAM,EAAE;MAC9C7B,KAAK,CAAChB,IAAI,CAACqD,aAAa,CAACD,IAAI,GAAG,KAAK;MACrCnC,KAAK,CAACjB,IAAI,CAACqD,aAAa,CAACD,IAAI,GAAG,KAAK;MACrC5B,KAAK,CAACqD,UAAU,IAAIrB,MAAM;MAC1B,OAAO,IAAI;IACf;IACA,IAAIL,KAAK,CAACN,MAAM,IAAI7B,KAAK,CAAC6B,MAAM,IAAIM,KAAK,CAACvC,OAAO,IAAIK,KAAK,CAACN,OAAO,EAAE;MAChEwC,KAAK,CAACnD,IAAI,CAACqD,aAAa,CAACD,IAAI,GAAG,KAAK;IACzC;IACA,IAAID,KAAK,CAACN,MAAM,IAAI5B,KAAK,CAAC4B,MAAM,IAAI7B,KAAK,CAACJ,OAAO,IAAIuC,KAAK,CAACxC,OAAO,EAAE;MAChEwC,KAAK,CAACnD,IAAI,CAACqD,aAAa,CAACD,IAAI,GAAG,KAAK;IACzC;IACA,IAAIpC,KAAK,CAAC6B,MAAM,IAAI7B,KAAK,CAACJ,OAAO,IAAIuC,KAAK,CAACxC,OAAO,EAAE;MAChDL,iBAAiB,CAACU,KAAK,EAAEmC,KAAK,EAAE,CAAC,CAAC;IACtC;IACA,IAAIlC,KAAK,CAAC4B,MAAM,IAAIM,KAAK,CAACvC,OAAO,IAAIK,KAAK,CAACN,OAAO,EAAE;MAChDL,iBAAiB,CAACW,KAAK,EAAEkC,KAAK,EAAE,CAAC,CAAC,CAAC;IACvC;IACA,IAAInC,KAAK,CAACJ,OAAO,IAAIuC,KAAK,CAACxC,OAAO,GAAG,GAAG,IAAIwC,KAAK,CAACvC,OAAO,IAAIK,KAAK,CAACN,OAAO,EAAE;MACxE6C,MAAM,GAAGF,WAAW,GAAG,CAAC;MACxBH,KAAK,CAACnD,IAAI,CAAC0E,SAAS,CAAClB,MAAM,CAAC;MAC5B,IAAIvC,KAAK,CAACjB,IAAI,CAACgF,OAAO,CAAC,CAAC,KAAK,KAAK,EAAE;QAChC/D,KAAK,CAACa,aAAa,GAAG,CAAC,CAAC;QACxBb,KAAK,CAACjB,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACnC;MACA,IAAI5D,KAAK,CAACJ,OAAO,IAAIK,KAAK,CAACN,OAAO,IAAIK,KAAK,CAAChB,IAAI,CAACgF,OAAO,CAAC,CAAC,KAAK,KAAK,EAAE;QAClEhE,KAAK,CAACc,aAAa,GAAG,CAAC;QACvBd,KAAK,CAAChB,IAAI,CAAC4E,gBAAgB,CAAC,CAAC,CAAC;MAClC;IACJ;IACApD,KAAK,CAACqD,UAAU,IAAIrB,MAAM;IAC1B,OAAO,IAAI;EACf;EACA,OAAOyB,UAAUA,CAAC1D,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,EACN,OAAO,KAAK;IAChBA,KAAK,CAAC2D,OAAO,CAAElF,IAAI,IAAKA,IAAI,CAACiF,UAAU,CAAC,CAAC,CAAC;IAC1C,OAAO,IAAI;EACf;EACAE,WAAWA,CAACC,UAAU,EAAE;IACpB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,KAAK,CAACH,UAAU,CAAC;IACjB,IAAI,CAACxE,OAAO,GAAG,CAAC;IAChB,IAAI,CAACD,OAAO,GAAG,CAAC;IAChB,IAAI,CAACiB,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC4D,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,IAAI,GAAG,CAACJ,EAAE,GAAGD,UAAU,CAACK,IAAI,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,QAAQ;IAC5E,IAAI,CAACK,WAAW,GAAG,CAACJ,EAAE,GAAGF,UAAU,CAACM,WAAW,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACnF,IAAI,CAACK,UAAU,GAAGzG,IAAI,CAAC0G,aAAa,CAAC,IAAI,CAACjC,QAAQ,EAAE,IAAI,CAACkC,QAAQ,CAAC;IAClEtG,OAAO,CAAC,IAAI,CAACoG,UAAU,EAAE,cAAc,EAAE,gCAAgC,IAAI,CAAChC,QAAQ,eAAe,IAAI,CAACkC,QAAQ,GAAG,CAAC;IACtH,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACzD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC0D,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC5C,aAAa,GAAG6C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC9C,aAAa,CAAC,EAAE;MAAE+C,QAAQ,EAAEhB,UAAU,CAACgB,QAAQ,IAAIvG,SAAS,CAACsB;IAAmB,CAAC,CAAC;IAC5I,IAAI,CAACkF,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,IAAIlB,UAAU,CAACmB,QAAQ,EAAE;MACrB,IAAI,CAACA,QAAQ,CAAC,CAAC;IACnB,CAAC,MACI;MACD,IAAI,CAAC3B,gBAAgB,CAAC,CAACW,EAAE,GAAGH,UAAU,CAACtD,aAAa,MAAM,IAAI,IAAIyD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGnG,IAAI,CAACe,EAAE,CAAC;IACnG;IACA,IAAI,CAACqG,KAAK,CAAC,CAAC;IACZ,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAD,KAAKA,CAAA,EAAG;IACJ,KAAK,CAACA,KAAK,CAAC,CAAC;IACb,MAAME,cAAc,GAAG,IAAI,CAACnE,UAAU,CAACoE,GAAG,CAAEC,QAAQ,IAAKA,QAAQ,CAACnC,QAAQ,CAAC,CAAC,CAAC;IAC7E,IAAI,CAACoC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACtE,UAAU,CAAC2C,OAAO,CAAC,CAAC0B,QAAQ,EAAE3G,KAAK,KAAK;MACzC,MAAM6G,aAAa,GAAGJ,cAAc,CAACzG,KAAK,CAAC;MAC3C,IAAI6G,aAAa,EACbF,QAAQ,CAACG,QAAQ,CAACD,aAAa,CAAC;IACxC,CAAC,CAAC;IACF,MAAME,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC;IACxB;IACA,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAC5B,OAAO,IAAI;EACf;EACAC,OAAOA,CAACC,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACF,qBAAqB,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACG,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACC,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC;IACnD;IACA,OAAO,IAAI;EACf;EACAjB,SAASA,CAAA,EAAG;IACR,IAAI,CAACkB,OAAO,CAAC,IAAIpI,IAAI,CAAC;MAAEqI,IAAI,EAAE,IAAI,CAACrF,MAAM,CAAC;IAAE,CAAC,CAAC,CAAC;IAC/C,OAAO,IAAI;EACf;EACAyE,cAAcA,CAAA,EAAG;IACb,IAAI,CAACtE,UAAU,GAAG,EAAE;IACpB,MAAMT,aAAa,GAAG,IAAI,CAAC5B,gBAAgB,CAAC,CAAC;IAC7C,MAAMwH,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC3B,IAAIC,QAAQ,GAAG1E,SAAS;IACxB,IAAIU,QAAQ,GAAGV,SAAS;IACxB,IAAI4C,SAAS,GAAG,KAAK;IACrB,IAAI+B,KAAK;IACT,IAAIC,GAAG;IACP,IAAIC,IAAI;IACR,IAAIjG,aAAa,KAAK1C,IAAI,CAACe,EAAE,EAAE;MAC3B0H,KAAK,GAAG,CAAC;MACTC,GAAG,GAAGJ,IAAI,CAACrH,MAAM;MACjB0H,IAAI,GAAG,CAAC;IACZ,CAAC,MACI;MACDF,KAAK,GAAGH,IAAI,CAACrH,MAAM,GAAG,CAAC;MACvByH,GAAG,GAAG,CAAC,CAAC;MACRC,IAAI,GAAG,CAAC,CAAC;IACb;IACA,KAAK,IAAIrG,CAAC,GAAGmG,KAAK,EAAEnG,CAAC,KAAKoG,GAAG,EAAEpG,CAAC,IAAIqG,IAAI,EAAE;MACtC,MAAMC,SAAS,GAAG,IAAI,CAACpG,cAAc,CAACF,CAAC,CAAC,CAACtB,QAAQ;MACjD,MAAMM,IAAI,GAAGsH,SAAS,CAACtH,IAAI;MAC3B,IAAIkH,QAAQ,KAAK1E,SAAS,EAAE;QACxB0E,QAAQ,GAAGlH,IAAI;MACnB,CAAC,MACI;QACDkD,QAAQ,GAAGvB,IAAI,CAACwB,GAAG,CAAC+D,QAAQ,GAAGlH,IAAI,CAAC;QACpC,IAAIkD,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,GAAG,EAAE;UACpCkC,SAAS,GAAG,CAACA,SAAS;QAC1B,CAAC,MACI;UACDA,SAAS,GAAG,KAAK;UACjB,IAAI,CAACE,eAAe,GAAG,IAAI;QAC/B;MACJ;MACA4B,QAAQ,GAAGlH,IAAI;MACf,MAAMuH,QAAQ,GAAG,IAAI9I,QAAQ,CAAC;QAC1BwE,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBkC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,SAAS;QACThE,aAAa;QACboG,eAAe,EAAEF,SAAS,CAACjE,IAAI;QAC/BrD,IAAI,EAAEsH,SAAS,CAACtH;MACpB,CAAC,CAAC;MACFuH,QAAQ,CAACE,QAAQ,GAAG,IAAI,CAACA,QAAQ;MACjC,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAAC;MACvB,IAAI,CAAC1F,UAAU,CAAC,IAAI,CAACX,cAAc,CAACF,CAAC,CAAC,CAACzB,KAAK,CAAC,GAAGgI,QAAQ;IAC5D;IACA,OAAO,IAAI,CAAC1F,UAAU;EAC1B;EACAgE,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3B,gBAAgB,CAAC,IAAI,CAACyD,6BAA6B,CAAC,CAAC,CAAC;EAC/D;EACAA,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,CAACzH,OAAO,GAAG,IAAI,CAACgB,cAAc,CAAC,CAAC,CAAC,CAACxB,QAAQ,CAACM,IAAI;IACnD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACiB,cAAc,CAAC,IAAI,CAACxB,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC,CAACD,QAAQ,CAACM,IAAI;IAC1E,MAAM4H,WAAW,GAAG,CAAC;IACrB,MAAMC,OAAO,GAAG,CAAC,IAAI,CAAC3H,OAAO,GAAG,IAAI,CAACD,OAAO,IAAI,CAAC;IACjD,MAAMmB,aAAa,GAAGyG,OAAO,GAAGD,WAAW,GAAGlJ,IAAI,CAACe,EAAE,GAAGf,IAAI,CAACoJ,IAAI;IACjE,OAAO1G,aAAa;EACxB;EACAuE,iBAAiBA,CAAA,EAAG;IAChB,IAAIhB,EAAE;IACN,IAAIuC,QAAQ;IACZ,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgG,IAAI,CAACrH,MAAM,EAAE,EAAEqB,CAAC,EAAE;MACvC,MAAM+G,GAAG,GAAG,IAAI,CAACf,IAAI,CAAChG,CAAC,CAAC;MACxB,MAAMgH,OAAO,GAAG;QAAEhD,WAAW,EAAE,CAACL,EAAE,GAAG,IAAI,CAACK,WAAW,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;QAAE1B,QAAQ,EAAE,IAAI,CAACA;MAAS,CAAC;MACpH,MAAMhC,KAAK,GAAGrC,MAAM,CAACqJ,aAAa,CAACF,GAAG,EAAE,IAAI,CAAChD,IAAI,EAAE,IAAI,CAACI,QAAQ,EAAE6C,OAAO,CAAC;MAC1E,IAAI,CAAC/G,KAAK,EAAE;QACR,MAAM,IAAIjC,YAAY,CAAC,cAAc,EAAE,oCAAoC+I,GAAG,EAAE,CAAC;MACrF;MACA,IAAI9G,KAAK,CAAC8G,GAAG,KAAK,GAAG,EAAE;QACnB,IAAI,IAAI,CAAC9E,QAAQ,KAAK,GAAG,IAAI,IAAI,CAACA,QAAQ,KAAK,GAAG,EAAE;UAChDhC,KAAK,CAACjB,IAAI,GAAG,CAAC;QAClB,CAAC,MACI;UACDiB,KAAK,CAACjB,IAAI,GAAG,CAAC;QAClB;MACJ;MACA,MAAMA,IAAI,GAAGiB,KAAK,CAACjB,IAAI;MACvB,IAAIkH,QAAQ,KAAK1E,SAAS,EAAE;QACxB0E,QAAQ,GAAGlH,IAAI;MACnB,CAAC,MACI;QACD,IAAI2B,IAAI,CAACwB,GAAG,CAAC+D,QAAQ,GAAGlH,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACoF,SAAS,GAAG,IAAI;UACrBnE,KAAK,CAACmE,SAAS,GAAG,IAAI;UACtB,IAAI,IAAI,CAAC1F,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAI,CAACD,QAAQ,CAACsB,CAAC,GAAG,CAAC,CAAC,CAACoE,SAAS,GAAG,IAAI;UACzC;QACJ;MACJ;MACA8B,QAAQ,GAAGlH,IAAI;MACf,IAAI,CAACN,QAAQ,CAACwC,IAAI,CAACjB,KAAK,CAAC;IAC7B;IACA,IAAI,CAACvB,QAAQ,CAAC8E,OAAO,CAAC,CAAC9E,QAAQ,EAAEH,KAAK,KAAK;MACvC,IAAI,CAAC2B,cAAc,CAACgB,IAAI,CAAC;QAAExC,QAAQ;QAAEH;MAAM,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAAC2B,cAAc,CAACgH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzI,QAAQ,CAACM,IAAI,GAAGoI,CAAC,CAAC1I,QAAQ,CAACM,IAAI,CAAC;EACzE;EACAqI,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,IAAIlK,WAAW,CAAC,IAAI,CAACmK,YAAY,CAAC,CAAC,EAAE,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1E,IAAI,CAAC3G,UAAU,CAAC2C,OAAO,CAAE+C,QAAQ,IAAK;MAClCe,WAAW,CAACG,SAAS,CAAClB,QAAQ,CAACc,cAAc,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;IACF,MAAM;MAAEK,IAAI;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAClD,IAAI,CAAC,IAAI,CAAClH,MAAM,CAAC,CAAC,IAAI,IAAI,CAACsB,OAAO,CAAC,CAAC,EAAE;MAClC,MAAM6F,cAAc,GAAG,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAAC,CAAC;MAC5C,MAAMC,KAAK,GAAG,IAAI,CAACvJ,gBAAgB,CAAC,CAAC,KAAKd,IAAI,CAACoJ,IAAI,GAC7CY,IAAI,GAAGG,cAAc,GAAG,IAAI,CAACG,IAAI,CAAClH,cAAc,CAAC,CAAC,CAACG,wBAAwB,GAC3E0G,OAAO,GAAGE,cAAc,GAAG,IAAI,CAACG,IAAI,CAAClH,cAAc,CAAC,CAAC,CAACC,uBAAuB;MACnFuG,WAAW,CAACG,SAAS,CAAC,IAAIrK,WAAW,CAAC,IAAI,CAACmK,YAAY,CAAC,CAAC,EAAEQ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE;MAChB,MAAMC,MAAM,GAAG,IAAI,CAACF,IAAI,CAACX,cAAc,CAAC,CAAC;MACzCC,WAAW,CAACG,SAAS,CAACS,MAAM,CAAC;IACjC;IACA,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACuE,SAAS,CAAC5F,MAAM,EAAEqB,CAAC,EAAE,EAAE;MAC5CsH,WAAW,CAACG,SAAS,CAAC,IAAI,CAAClD,SAAS,CAACvE,CAAC,CAAC,CAACqH,cAAc,CAAC,CAAC,CAAC;IAC7D;IACA,OAAOC,WAAW;EACtB;EACAa,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAAC,IAAI,CAAC1J,QAAQ,CAACC,MAAM,EAAE;MACvB,MAAM,IAAIX,YAAY,CAAC,YAAY,EAAE,uEAAuE,CAAC;IACjH;IACA,IAAIqK,UAAU,GAAG,IAAI,CAAC3J,QAAQ,CAAC,CAAC,CAAC,CAACM,IAAI;IACtC,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,QAAQ,CAACC,MAAM,EAAEqB,CAAC,EAAE,EAAE;MAC3C,MAAMsI,QAAQ,GAAG,IAAI,CAAC5J,QAAQ,CAACsB,CAAC,CAAC,CAAChB,IAAI;MACtC,IAAIoJ,SAAS,EAAE;QACX,IAAIE,QAAQ,GAAGD,UAAU,EACrBA,UAAU,GAAGC,QAAQ;MAC7B,CAAC,MACI;QACD,IAAIA,QAAQ,GAAGD,UAAU,EACrBA,UAAU,GAAGC,QAAQ;MAC7B;IACJ;IACA,OAAOD,UAAU;EACrB;EACA3H,MAAMA,CAAA,EAAG;IACL,MAAM6H,GAAG,GAAG,IAAI,CAACtE,UAAU,CAACuE,QAAQ;IACpC,OAAOD,GAAG,IAAI,QAAQ,IAAIA,GAAG,IAAI,QAAQ;EAC7C;EACAE,OAAOA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAAC/H,MAAM,CAAC,CAAC,IAAI,IAAI,CAACsF,IAAI,CAACrH,MAAM,GAAG,CAAC;EACjD;EACAqD,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACiC,UAAU,CAAC0B,IAAI;EAC/B;EACAsC,OAAOA,CAAA,EAAG;IACN,OAAO,KAAK,CAACA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAACvH,MAAM,CAAC,CAAC;EAC5C;EACAgI,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACvE,QAAQ,KAAK,GAAG,EAAE;MACvB,OAAO,IAAI,CAACwE,eAAe,CAAC,CAAC;IACjC,CAAC,MACI;MACD,OAAO,KAAK,CAACD,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACtI,aAAa,GAAG1C,IAAI,CAACkL,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAACxI,aAAa,CAAC,GAAG,CAAC,CAAC;IAC/F;EACJ;EACAyI,cAAcA,CAACC,QAAQ,EAAE;IACrB,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACrC,OAAOrI,IAAI,CAACsI,GAAG,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAACL,cAAc,CAACC,QAAQ,CAAC,EAAEC,OAAO,CAACI,IAAI,GAAG,IAAI,CAACxH,aAAa,CAACyH,iBAAiB,IAAIN,QAAQ,GAAG,CAAC,CAAC,CAAC;EACrI;EACAO,iBAAiBA,CAACP,QAAQ,EAAE;IACxB,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACrC,OAAOrI,IAAI,CAACkB,GAAG,CAAC,IAAI,CAACqH,UAAU,CAAC,CAAC,CAACL,cAAc,CAACC,QAAQ,CAAC,EAAEC,OAAO,CAACO,KAAK,GAAG,IAAI,CAAC3H,aAAa,CAACyH,iBAAiB,GAAGN,QAAQ,CAAC;EAChI;EACAvD,QAAQA,CAACD,KAAK,EAAE;IACZ,KAAK,CAACC,QAAQ,CAACD,KAAK,CAAC;IACrB,MAAMkC,EAAE,GAAG,IAAI,CAAC3G,UAAU,CAACoE,GAAG,CAAEsB,QAAQ,IAAK;MACzCA,QAAQ,CAAChB,QAAQ,CAACD,KAAK,CAAC;MACxB,OAAOiB,QAAQ,CAACgD,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF,IAAI,CAACC,KAAK,CAAChC,EAAE,CAAC;IACd,IAAI,IAAI,CAAC7B,IAAI,EAAE;MACX,MAAM;QAAE+B,IAAI;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAClD,IAAI,CAACjC,IAAI,CAAC8D,UAAU,CAAC/B,IAAI,EAAEC,OAAO,CAAC;IACvC;IACA,OAAO,IAAI;EACf;EACArG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8C,SAAS;EACzB;EACAsF,gBAAgBA,CAACtF,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,OAAO,IAAI;EACf;EACAuF,YAAYA,CAAA,EAAG;IACX,IAAIC,SAAS,GAAG,IAAI,CAACrC,YAAY,CAAC,CAAC;IACnCqC,SAAS,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC/H,MAAM,GAAG,IAAI,CAACgI,oBAAoB;IAC3E,IAAI,IAAI,CAACC,eAAe,EACpBH,SAAS,IAAI,IAAI,CAACG,eAAe,CAACC,aAAa,CAAC,CAAC;IACrD,OAAOJ,SAAS;EACpB;EACAK,WAAWA,CAAA,EAAG;IACV,IAAIC,OAAO,GAAG,IAAI,CAAC3C,YAAY,CAAC,CAAC;IACjC2C,OAAO,IAAI,IAAI,CAACpI,MAAM,GAAG,IAAI,CAACqI,mBAAmB;IACjD,OAAOD,OAAO;EAClB;EACAE,cAAcA,CAAA,EAAG;IACb,IAAIC,QAAQ,GAAG,IAAI,CAAC3L,QAAQ,CAAC,CAAC,CAAC,CAACM,IAAI;IACpC,IAAI,IAAI,CAACN,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMuH,QAAQ,GAAG,IAAI,CAACxH,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC,CAACK,IAAI;MAC7D,MAAMsL,GAAG,GAAG3J,IAAI,CAACkB,GAAG,CAACwI,QAAQ,EAAEnE,QAAQ,CAAC;MACxC,MAAMqE,GAAG,GAAG5J,IAAI,CAACsI,GAAG,CAACoB,QAAQ,EAAEnE,QAAQ,CAAC;MACxCmE,QAAQ,GAAGtM,OAAO,CAACuM,GAAG,EAAEC,GAAG,CAAC;IAChC;IACA,OAAOF,QAAQ;EACnB;EACAG,kBAAkBA,CAACC,QAAQ,EAAElM,KAAK,EAAEyI,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9C,IAAI,CAAC,IAAI,CAAC0D,YAAY,EAAE;MACpB,MAAM,IAAI1M,YAAY,CAAC,iBAAiB,EAAE,sDAAsD,CAAC;IACrG;IACA,IAAI,IAAI,CAACwJ,EAAE,CAAC7I,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIX,YAAY,CAAC,WAAW,EAAE,uCAAuC,CAAC;IAChF;IACA,MAAM;MAAE2M,KAAK;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGvN,QAAQ,CAACwN,QAAQ;IACvD,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIP,QAAQ,KAAKI,IAAI,EAAE;MACnBG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACd,CAAC,MACI,IAAIP,QAAQ,KAAKK,KAAK,EAAE;MACzBE,CAAC,GAAG,IAAI,CAACnB,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC/H,MAAM,GAAG,CAAC;MAC1C,IAAI,IAAI,CAAC1B,aAAa,KAAK1C,IAAI,CAACe,EAAE,IAC9B,IAAI,CAACwJ,OAAO,CAAC,CAAC,KACbjB,OAAO,CAACiE,cAAc,IAAI5M,gBAAgB,CAAC,IAAI,EAAEE,KAAK,CAAC,CAAC,EAAE;QAC3DyM,CAAC,IAAI,IAAI,CAAChD,IAAI,CAACkD,QAAQ,CAAC,CAAC;MAC7B;IACJ,CAAC,MACI,IAAIT,QAAQ,KAAKG,KAAK,IAAIH,QAAQ,KAAKE,KAAK,EAAE;MAC/CK,CAAC,GAAG,IAAI,CAACnB,aAAa,CAAC,CAAC,GAAG,CAAC;IAChC;IACA,IAAIsB,SAAS,GAAG,CAAC;IACjB,QAAQ,IAAI,CAACtK,UAAU,CAACtC,KAAK,CAAC,CAAC6M,OAAO,CAAC,CAAC;MACpC,KAAK/N,MAAM,CAACgO,eAAe;MAC3B,KAAKhO,MAAM,CAACiO,SAAS;QACjBH,SAAS,IAAI,GAAG;QAChB;MACJ,KAAK9N,MAAM,CAACkO,QAAQ;MACpB,KAAKlO,MAAM,CAACmO,WAAW;MACvB,KAAKnO,MAAM,CAACoO,OAAO;MACnB,KAAKpO,MAAM,CAACqO,QAAQ;QAChBP,SAAS,IAAI,GAAG;QAChB;MACJ,KAAK9N,MAAM,CAACsO,QAAQ;MACpB,KAAKtO,MAAM,CAACuO,QAAQ;QAChBT,SAAS,IAAI,GAAG;QAChB;MACJ,KAAK9N,MAAM,CAACwO,SAAS;QACjBV,SAAS,IAAI,GAAG;QAChB;IACR;IACA,OAAO;MACHH,CAAC,EAAE,IAAI,CAACzD,YAAY,CAAC,CAAC,GAAGyD,CAAC;MAC1Bc,CAAC,EAAE,IAAI,CAACtE,EAAE,CAACjJ,KAAK,CAAC,GAAG4M,SAAS,GAAG,IAAI,CAACjC,UAAU,CAAC,CAAC,CAAC6C,sBAAsB,CAAC;IAC7E,CAAC;EACL;EACA1G,QAAQA,CAAC2G,KAAK,EAAE;IACZ,OAAO,KAAK,CAACC,aAAa,CAACD,KAAK,CAAC;EACrC;EACAE,YAAYA,CAACF,KAAK,EAAE;IAChB,MAAMrG,IAAI,GAAG,IAAI,CAACwG,OAAO,CAAC,CAAC;IAC3B,IAAIxG,IAAI,EACJA,IAAI,CAACN,QAAQ,CAAC2G,KAAK,CAAC;IACxB,OAAO,IAAI;EACf;EACAI,YAAYA,CAAA,EAAG;IACX,IAAIzI,EAAE;IACN,OAAO,CAACA,EAAE,GAAG,IAAI,CAACgC,IAAI,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAC9E;EACAsJ,kBAAkBA,CAACL,KAAK,EAAE;IACtB,IAAI,CAAClI,eAAe,GAAGkI,KAAK;EAChC;EACAM,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxI,eAAe;EAC/B;EACAyI,YAAYA,CAACP,KAAK,EAAE;IAChB,IAAI,CAAChE,IAAI,CAAC3C,QAAQ,CAAC2G,KAAK,CAAC;EAC7B;EACAQ,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACxE,IAAI,CAACjF,QAAQ,CAAC,CAAC;EAC/B;EACA8G,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAACvB,QAAQ,CAAC,CAAC;EACvC;EACAwB,WAAWA,CAACnO,KAAK,EAAEyN,KAAK,EAAE;IACtB,IAAI,CAACnL,UAAU,CAACtC,KAAK,CAAC,CAAC8G,QAAQ,CAAC2G,KAAK,CAAC;IACtC,OAAO,IAAI;EACf;EACA7M,UAAUA,CAACZ,KAAK,EAAES,IAAI,EAAE;IACpB,IAAI,CAACN,QAAQ,CAACH,KAAK,CAAC,CAACS,IAAI,GAAGA,IAAI;IAChC,IAAI,CAAC8F,KAAK,CAAC,CAAC;IACZ,OAAO,IAAI;EACf;EACA1F,UAAUA,CAACb,KAAK,EAAE;IACd,OAAO,IAAI,CAACG,QAAQ,CAACH,KAAK,CAAC,CAACS,IAAI;EACpC;EACAqC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwI,aAAa,CAAC,CAAC,IAAI,IAAI,CAACzF,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;EACAoB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACmH,sBAAsB,CAAC,IAAI,CAACvI,SAAS,IAAI,IAAI,CAAChE,aAAa,KAAK1C,IAAI,CAACoJ,IAAI,GAAG,IAAI,CAAC+C,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1G,IAAI,CAAC+C,uBAAuB,CAAC,CAAC,IAAI,CAAC3E,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC7D,SAAS,IAAI,IAAI,CAAChE,aAAa,KAAK1C,IAAI,CAACe,EAAE,GAAG,IAAI,CAACoL,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;EAChI;EACAgD,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACnC,YAAY,EACjB;IACJ,IAAIoC,eAAe,GAAG,CAAC;IACvB,IAAI,IAAI,CAAC/C,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC8C,SAAS,CAAC,CAAC;MAChC,IAAI,IAAI,CAAC9C,eAAe,CAACmB,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;QACvC4B,eAAe,GAAG3O,SAAS,CAACuB,kBAAkB;MAClD;IACJ;IACA,IAAIqN,KAAK,GAAG,IAAI,CAAClD,aAAa,CAAC,CAAC,GAAG,IAAI,CAACM,mBAAmB,GAAG,IAAI,CAACL,oBAAoB,GAAGgD,eAAe;IACzG,IAAI,IAAI,CAACE,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC5M,aAAa,KAAK1C,IAAI,CAACe,EAAE,EAAE;MACzDsO,KAAK,IAAI,IAAI,CAAClD,aAAa,CAAC,CAAC;IACjC;IACA,IAAI,CAACoD,QAAQ,CAACF,KAAK,CAAC;IACpB,IAAI,CAACrC,YAAY,GAAG,IAAI;EAC5B;EACA9C,iBAAiBA,CAAA,EAAG;IAChB,IAAIF,IAAI,GAAG,CAACwF,QAAQ;IACpB,IAAIvF,OAAO,GAAG,CAACuF,QAAQ;IACvB,IAAIC,aAAa;IACjB,IAAIC,UAAU;IACd,IAAIC,WAAW,GAAG,IAAI,CAACnE,UAAU,CAAC,CAAC,CAACoE,WAAW,CAAC,CAAC;IACjD,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,oBAAoB;IACxB,IAAIC,mBAAmB;IACvB,IAAIC,uBAAuB,GAAGL,WAAW;IACzC,IAAIM,sBAAsB,GAAGJ,UAAU;IACvC,IAAI,CAAC1M,UAAU,CAAC2C,OAAO,CAAE+C,QAAQ,IAAK;MAClC,MAAMvH,IAAI,GAAGuH,QAAQ,CAACqH,OAAO,CAAC,CAAC;MAC/B,MAAM9B,CAAC,GAAGvF,QAAQ,CAACgD,IAAI,CAAC,CAAC;MACzB7B,IAAI,GAAG/G,IAAI,CAACsI,GAAG,CAAC6C,CAAC,EAAEpE,IAAI,CAAC;MACxBC,OAAO,GAAGhH,IAAI,CAACkB,GAAG,CAACiK,CAAC,EAAEnE,OAAO,CAAC;MAC9B,IAAIyF,UAAU,KAAK5L,SAAS,IAAI+E,QAAQ,CAACjF,WAAW,CAAC,CAAC,EAAE;QACpD8L,UAAU,GAAG7G,QAAQ,CAACgB,YAAY,CAAC,CAAC;MACxC;MACA,IAAI4F,aAAa,KAAK3L,SAAS,IAAI,CAAC+E,QAAQ,CAACjF,WAAW,CAAC,CAAC,EAAE;QACxD6L,aAAa,GAAG5G,QAAQ,CAACgB,YAAY,CAAC,CAAC;MAC3C;MACA8F,WAAW,GAAG1M,IAAI,CAACkB,GAAG,CAAC7C,IAAI,EAAEqO,WAAW,CAAC;MACzCE,UAAU,GAAG5M,IAAI,CAACsI,GAAG,CAACjK,IAAI,EAAEuO,UAAU,CAAC;MACvC,IAAIhH,QAAQ,CAACjF,WAAW,CAAC,CAAC,EAAE;QACxBkM,oBAAoB,GAAGA,oBAAoB,KAAKhM,SAAS,GAAGxC,IAAI,GAAG2B,IAAI,CAACkB,GAAG,CAAC7C,IAAI,EAAEwO,oBAAoB,CAAC;QACvGC,mBAAmB,GAAGA,mBAAmB,KAAKjM,SAAS,GAAGxC,IAAI,GAAG2B,IAAI,CAACsI,GAAG,CAACjK,IAAI,EAAEyO,mBAAmB,CAAC;MACxG,CAAC,MACI;QACDC,uBAAuB,GAAG/M,IAAI,CAACkB,GAAG,CAAC7C,IAAI,EAAE0O,uBAAuB,CAAC;QACjEC,sBAAsB,GAAGhN,IAAI,CAACsI,GAAG,CAACjK,IAAI,EAAE2O,sBAAsB,CAAC;MACnE;IACJ,CAAC,EAAE,IAAI,CAAC;IACR,OAAO;MACHjG,IAAI;MACJC,OAAO;MACPyF,UAAU;MACVD,aAAa;MACbE,WAAW;MACXE,UAAU;MACVC,oBAAoB;MACpBC,mBAAmB;MACnBC,uBAAuB;MACvBC;IACJ,CAAC;EACL;EACAE,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtG,YAAY,CAAC,CAAC,GAAG,IAAI,CAACzF,MAAM;EAC5C;EACAgM,eAAeA,CAAA,EAAG;IACd,MAAMC,MAAM,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACvC,OAAOE,MAAM,GAAG,IAAI,CAAClE,aAAa,CAAC,CAAC;EACxC;EACA,IAAI4C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC5L,UAAU,CAACmN,KAAK,CAAC,CAAC;EAClC;EACAC,eAAeA,CAAA,EAAG;IACd,MAAM3I,KAAK,GAAG,IAAI,CAAC4D,UAAU,CAAC,CAAC;IAC/B,MAAM;MAAEvH,aAAa,EAAE;QAAE+C;MAAS;IAAG,CAAC,GAAG,IAAI;IAC7C,MAAMwJ,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMpB,KAAK,GAAG,IAAI,CAAClD,aAAa,CAAC,CAAC,GAAGnF,QAAQ,GAAG,CAAC;IACjD,MAAM0J,WAAW,GAAG,CAAC,IAAI,IAAI,CAACvE,aAAa,CAAC,CAAC,GAAGnF,QAAQ,CAAC,GAAGhH,IAAI,CAACkL,KAAK,GAAG,CAAC;IAC1E,IAAI,IAAI,CAAClI,MAAM,CAAC,CAAC,EACb;IACJ,IAAI,CAACwN,GAAG,EAAE;MACN,MAAM,IAAIlQ,YAAY,CAAC,iBAAiB,EAAE,sCAAsC,CAAC;IACrF;IACA,MAAM;MAAEqP,WAAW;MAAEE,UAAU;MAAEC,oBAAoB;MAAEE,uBAAuB;MAAED,mBAAmB;MAAEE,sBAAsB;MAAEP,UAAU;MAAED;IAAe,CAAC,GAAG,IAAI,CAACvF,iBAAiB,CAAC,CAAC;IACpL,IAAIyF,WAAW,GAAG,CAAC,IAAIE,UAAU,GAAG,CAAC,EACjC;IACJ,MAAMc,IAAI,GAAG1N,IAAI,CAACsI,GAAG,CAACmE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,EAAED,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,CAAC;IAC5J,MAAMmB,cAAc,GAAGA,CAACxC,CAAC,EAAEyC,MAAM,EAAEnK,SAAS,KAAK;MAC7C,IAAI4G,CAAC;MACL,IAAI5G,SAAS,IAAImK,MAAM,EACnBvD,CAAC,GAAGqD,IAAI,GAAG3J,QAAQ,CAAC,KACnB,IAAI6J,MAAM,EACXvD,CAAC,GAAG,CAACmC,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,IAAIzI,QAAQ,CAAC,KAExFsG,CAAC,GAAG,CAACoC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC,IAAI1I,QAAQ;MAClF,MAAM8J,WAAW,GAAGD,MAAM,IAAInK,SAAS,GAAGgK,WAAW,GAAGrB,KAAK;MAC7DmB,GAAG,CAACO,SAAS,CAAC,CAAC;MACfP,GAAG,CAACQ,MAAM,CAAC1D,CAAC,EAAEc,CAAC,CAAC;MAChBoC,GAAG,CAACS,MAAM,CAAC3D,CAAC,GAAGwD,WAAW,EAAE1C,CAAC,CAAC;MAC9BoC,GAAG,CAACU,MAAM,CAAC,CAAC;IAChB,CAAC;IACD,MAAM5C,KAAK,GAAGxH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEa,KAAK,CAACuJ,yBAAyB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACvC,kBAAkB,CAAC,CAAC,CAAC;IAC5G4B,GAAG,CAACY,IAAI,CAAC,CAAC;IACV,IAAI,CAACC,UAAU,CAACb,GAAG,EAAElC,KAAK,CAAC;IAC3B,KAAK,IAAIhN,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAIqO,WAAW,EAAE,EAAErO,IAAI,EAAE;MAC5C,MAAMuP,MAAM,GAAGpB,aAAa,KAAK3L,SAAS,IAAIxC,IAAI,IAAI0O,uBAAuB;MAC7E,MAAMtJ,SAAS,GAAGoJ,oBAAoB,KAAKhM,SAAS,IAAIxC,IAAI,IAAIwO,oBAAoB;MACpFc,cAAc,CAAChJ,KAAK,CAAC0J,WAAW,CAAChQ,IAAI,CAAC,EAAEuP,MAAM,EAAEnK,SAAS,CAAC;IAC9D;IACA,KAAK,IAAIpF,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAIuO,UAAU,EAAE,EAAEvO,IAAI,EAAE;MAC3C,MAAMuP,MAAM,GAAGpB,aAAa,KAAK3L,SAAS,IAAIxC,IAAI,IAAI2O,sBAAsB;MAC5E,MAAMvJ,SAAS,GAAGqJ,mBAAmB,KAAKjM,SAAS,IAAIxC,IAAI,IAAIyO,mBAAmB;MAClFa,cAAc,CAAChJ,KAAK,CAAC0J,WAAW,CAAChQ,IAAI,CAAC,EAAEuP,MAAM,EAAEnK,SAAS,CAAC;IAC9D;IACA8J,GAAG,CAACe,OAAO,CAAC,CAAC;EACjB;EACAC,aAAaA,CAACC,aAAa,EAAE;IACzB,MAAMjB,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,KAAK,IAAInO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACuE,SAAS,CAAC5F,MAAM,EAAEqB,CAAC,EAAE,EAAE;MAC5C,MAAMoP,QAAQ,GAAG,IAAI,CAAC7K,SAAS,CAACvE,CAAC,CAAC;MAClC,MAAMzB,KAAK,GAAG6Q,QAAQ,CAACC,UAAU,CAAC,CAAC;MACnC,MAAM9I,QAAQ,GAAG,IAAI,CAAC1F,UAAU,CAACtC,KAAK,CAAC;MACvC,IAAIgI,QAAQ,KAAK4I,aAAa,EAAE;QAC5BC,QAAQ,CAACE,UAAU,CAACpB,GAAG,CAAC;QACxBkB,QAAQ,CAACG,aAAa,CAAC,CAAC;MAC5B;IACJ;EACJ;EACAvC,cAAcA,CAAA,EAAG;IACb,MAAMhL,OAAO,GAAG,IAAI,CAAC2D,IAAI,KAAKnE,SAAS;IACvC,MAAMyG,OAAO,GAAG,IAAI,CAAChE,UAAU,CAACuL,UAAU,KAAKhO,SAAS;IACxD,MAAMiO,SAAS,GAAG,IAAI,CAAC/J,IAAI,KAAKlE,SAAS;IACzC,OAAOQ,OAAO,IAAIiG,OAAO,IAAIwH,SAAS,IAAI,CAAC,IAAI,CAAC/O,MAAM,CAAC,CAAC;EAC5D;EACAgP,QAAQA,CAAA,EAAG;IACP,MAAMxB,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACD,GAAG,EAAE;MACN,MAAM,IAAIlQ,YAAY,CAAC,iBAAiB,EAAE,sCAAsC,CAAC;IACrF;IACA,IAAI,IAAI,CAACgP,cAAc,CAAC,CAAC,EAAE;MACvB,MAAM;QAAEtF,IAAI;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAClD,MAAMC,cAAc,GAAG,IAAI,CAAClC,IAAI,CAACmC,SAAS,CAAC,CAAC;MAC5C,MAAM6H,KAAK,GAAG,IAAI,CAACjH,QAAQ,CAAC,CAAC,GAAG9K,MAAM,CAACgS,UAAU,GAAG,CAAC;MACrD,MAAMC,KAAK,GAAG,IAAI,CAACrR,gBAAgB,CAAC,CAAC,KAAKd,IAAI,CAACoJ,IAAI,GAE3CY,IAAI,GAAGG,cAAc,GAAG,IAAI,CAACG,IAAI,CAAClH,cAAc,CAAC,CAAC,CAACG,wBAAwB,GAE3E0G,OAAO,GAAGE,cAAc,GAAG,IAAI,CAACG,IAAI,CAAClH,cAAc,CAAC,CAAC,CAACC,uBAAuB;MACrF,IAAI,CAACiH,IAAI,CAACsH,UAAU,CAACpB,GAAG,CAAC,CAAC4B,IAAI,CAACH,KAAK,CAAC,CAACI,IAAI,CAACF,KAAK,CAAC,CAACN,aAAa,CAAC,CAAC;IACrE;EACJ;EACAS,aAAaA,CAAA,EAAG;IACZ,MAAM9B,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACtN,UAAU,CAAC2C,OAAO,CAAE+C,QAAQ,IAAK;MAClCA,QAAQ,CAAC+I,UAAU,CAACpB,GAAG,CAAC,CAACqB,aAAa,CAAC,CAAC;IAC5C,CAAC,CAAC;EACN;EACAU,QAAQA,CAACC,WAAW,EAAE;IAClB,MAAMhC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI+B,WAAW,EAAE;MACb,IAAI,CAACpK,OAAO,CAAC,IAAIpI,IAAI,CAACwS,WAAW,CAAC,CAAC;IACvC;IACA,IAAI,IAAI,CAAClD,cAAc,CAAC,CAAC,IAAI,IAAI,CAACrH,IAAI,EAAE;MACpC,IAAI,CAACA,IAAI,CAACwK,mBAAmB,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACxK,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC2J,UAAU,CAACpB,GAAG,CAAC,CAACqB,aAAa,CAAC,CAAC;IAC7C;EACJ;EACA1J,gBAAgBA,CAAA,EAAG;IACf,MAAMuK,kBAAkB,GAAG,KAAK,CAACvK,gBAAgB,CAAC,CAAC;IACnD,IAAI,CAAC,IAAI,CAAC5B,UAAU,CAAC0B,IAAI,EAAE;MACvB,OAAOyK,kBAAkB;IAC7B;IACA,MAAMhQ,aAAa,GAAG,IAAI,CAAC5B,gBAAgB,CAAC,CAAC;IAC7C,IAAI4B,aAAa,KAAK,IAAI,CAACuG,6BAA6B,CAAC,CAAC,EAAE;MACxD,OAAOyJ,kBAAkB;IAC7B;IACA,IAAIC,eAAe;IACnB,MAAMzJ,WAAW,GAAG,CAAC;IACrB,IAAIxG,aAAa,KAAK1C,IAAI,CAACe,EAAE,EAAE;MAC3B4R,eAAe,GAAGzJ,WAAW,GAAG,IAAI,CAAC3H,OAAO;IAChD,CAAC,MACI;MACDoR,eAAe,GAAG,IAAI,CAACnR,OAAO,GAAG0H,WAAW;IAChD;IACA,MAAM0J,0BAA0B,GAAGD,eAAe,GAAG,GAAG;IACxD,IAAIC,0BAA0B,IAAI,CAAC,EAAE;MACjC,OAAOF,kBAAkB;IAC7B;IACA,MAAM9K,KAAK,GAAG,IAAI,CAACiL,QAAQ,CAAC,CAAC;IAC7B,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,IAAIlL,KAAK,KAAK9D,SAAS,EAAE;MACrBgP,mBAAmB,GAAGlL,KAAK,CAACyG,sBAAsB,CAAC,CAAC;IACxD;IACA,OAAOqE,kBAAkB,GAAGE,0BAA0B,GAAGE,mBAAmB;EAChF;EACA9O,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACC,aAAa,CAACD,IAAI,KAAK,KAAK,EACjC;IACJ,IAAI,IAAI,CAAC8F,EAAE,CAAC7I,MAAM,KAAK,CAAC,EAAE;MACtB,MAAM,IAAIX,YAAY,CAAC,WAAW,EAAE,mCAAmC,CAAC;IAC5E;IACA,MAAMkQ,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMJ,MAAM,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACvC,MAAM4C,gBAAgB,GAAG,IAAI,CAACzO,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0D,IAAI;IACrD,IAAI,CAAC7E,UAAU,CAAC2C,OAAO,CAAE+C,QAAQ,IAAKA,QAAQ,CAACuJ,IAAI,CAAC/B,MAAM,CAAC,CAAC;IAC5D,IAAI,IAAI,CAACpI,IAAI,EAAE;MACX,MAAM+K,KAAK,GAAG,IAAI,CAAChI,QAAQ,CAAC,CAAC;MAC7B,IAAI,CAAC/C,IAAI,CAACgL,kBAAkB,CAACD,KAAK,EAAEA,KAAK,CAAC;IAC9C;IACAzS,CAAC,CAAC,YAAY,EAAE,IAAI,CAACwK,OAAO,CAAC,CAAC,GAAG,SAAS,GAAG,QAAQ,EAAE,IAAI,CAACzC,IAAI,CAAC;IACjEkI,GAAG,CAAC0C,SAAS,CAAC,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACnD,IAAI,CAAC5C,eAAe,CAAC,CAAC;IACtB,IAAIwC,gBAAgB,EAChB,IAAI,CAACR,QAAQ,CAAC,CAAC;IACnB,IAAI,CAACD,aAAa,CAAC,CAAC;IACpB,IAAI,CAACN,QAAQ,CAAC,CAAC;IACf,MAAMoB,EAAE,GAAG,IAAI,CAACzJ,cAAc,CAAC,CAAC;IAChC6G,GAAG,CAAC6C,WAAW,CAACD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAEF,EAAE,CAACvH,IAAI,CAAC,CAAC,EAAEuH,EAAE,CAACG,IAAI,CAAC,CAAC,EAAEH,EAAE,CAACI,IAAI,CAAC,CAAC,CAAC;IAC3DhD,GAAG,CAACiD,UAAU,CAAC,CAAC;IAChB,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;AACJ;AACAjT,SAAS,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}