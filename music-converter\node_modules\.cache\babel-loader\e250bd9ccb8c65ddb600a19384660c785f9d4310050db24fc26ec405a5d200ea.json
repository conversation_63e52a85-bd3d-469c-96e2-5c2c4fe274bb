{"ast": null, "code": "import { getBottomY, getInitialOffset, getTopY } from './articulation.js';\nimport { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { log } from './util.js';\nfunction L(...args) {\n  if (Ornament.DEBUG) log('VexFlow.Ornament', args);\n}\nexport class Ornament extends Modifier {\n  static get CATEGORY() {\n    return \"Ornament\";\n  }\n  static get minPadding() {\n    return Metrics.get('NoteHead.minPadding');\n  }\n  static format(ornaments, state) {\n    var _a, _b;\n    if (!ornaments || ornaments.length === 0) return false;\n    let width = 0;\n    let rightShift = state.rightShift;\n    let leftShift = state.leftShift;\n    for (let i = 0; i < ornaments.length; ++i) {\n      const ornament = ornaments[i];\n      const increment = 2;\n      if (ornament.position === ModifierPosition.RIGHT) {\n        ornament.xShift += rightShift + 2;\n        rightShift += ornament.width + Ornament.minPadding;\n      } else if (ornament.position === ModifierPosition.LEFT) {\n        ornament.xShift -= leftShift + ornament.width + 2;\n        leftShift += ornament.width + Ornament.minPadding;\n      } else if (ornament.position === ModifierPosition.ABOVE) {\n        width = Math.max(ornament.getWidth(), width);\n        const note = ornament.getNote();\n        let curTop = note.getLineNumber(true) + state.topTextLine;\n        const stem = note.getStem();\n        if (stem && note.getStemDirection() === Stem.UP) {\n          curTop += Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n        }\n        const numLines = (_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getNumLines()) !== null && _b !== void 0 ? _b : 0;\n        if (curTop < numLines) state.topTextLine += numLines - curTop;\n        ornament.setTextLine(state.topTextLine);\n        state.topTextLine += increment;\n      } else {\n        width = Math.max(ornament.getWidth(), width);\n        ornament.setTextLine(state.textLine);\n        state.textLine += increment;\n      }\n    }\n    state.leftShift = leftShift + width / 2;\n    state.rightShift = rightShift + width / 2;\n    return true;\n  }\n  static get ornamentNoteTransition() {\n    return ['flip', 'jazzTurn', 'smear'];\n  }\n  static get ornamentAttack() {\n    return ['scoop'];\n  }\n  static get ornamentAlignWithNoteHead() {\n    return ['doit', 'fall', 'fallLong', 'doitLong', 'scoop'];\n  }\n  static get ornamentRelease() {\n    return ['doit', 'fall', 'fallLong', 'doitLong', 'jazzTurn', 'smear', 'flip'];\n  }\n  static get ornamentLeft() {\n    return ['scoop'];\n  }\n  static get ornamentRight() {\n    return ['doit', 'fall', 'fallLong', 'doitLong'];\n  }\n  static get ornamentYShift() {\n    return ['fallLong'];\n  }\n  static get ornamentArticulation() {\n    return ['bend', 'plungerClosed', 'plungerOpen'];\n  }\n  constructor(type) {\n    super();\n    this.position = ModifierPosition.ABOVE;\n    if (Ornament.ornamentRight.indexOf(type) >= 0) {\n      this.position = ModifierPosition.RIGHT;\n    }\n    if (Ornament.ornamentLeft.indexOf(type) >= 0) {\n      this.position = ModifierPosition.LEFT;\n    }\n    this.type = type;\n    this.delayed = false;\n    this.renderOptions = {\n      accidentalLowerPadding: 3,\n      accidentalUpperPadding: 3\n    };\n    this.adjustForStemDirection = false;\n    this.ornamentAlignWithNoteHead = Ornament.ornamentAlignWithNoteHead.indexOf(this.type) >= 0;\n    if (Ornament.ornamentNoteTransition.indexOf(this.type) >= 0) {\n      this.delayed = true;\n    }\n    this.text = Tables.ornamentCodes(this.type);\n  }\n  setNote(note) {\n    super.setNote(note);\n    if (Ornament.ornamentArticulation.indexOf(this.type) >= 0) {\n      if (note.getLineNumber() >= 3) {\n        this.position = Modifier.Position.ABOVE;\n      } else {\n        this.position = Modifier.Position.BELOW;\n      }\n    }\n    return this;\n  }\n  setDelayed(delayed) {\n    this.delayed = delayed;\n    return this;\n  }\n  setUpperAccidental(accid) {\n    this.accidentalUpper = new Element();\n    this.accidentalUpper.setText(Tables.accidentalCodes(accid));\n    return this;\n  }\n  setLowerAccidental(accid) {\n    this.accidentalLower = new Element();\n    this.accidentalLower.setText(Tables.accidentalCodes(accid));\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    const note = this.checkAttachedNote();\n    this.setRendered();\n    const stave = note.checkStave();\n    ctx.openGroup('ornament', this.getAttribute('id'));\n    const start = note.getModifierStartXY(this.position, this.index);\n    let glyphX = start.x;\n    const staffSpace = stave.getSpacingBetweenLines();\n    const initialOffset = getInitialOffset(note, this.position);\n    let glyphY = this.ornamentAlignWithNoteHead ? start.y : 0;\n    if (this.position === ModifierPosition.ABOVE) {\n      glyphY = getTopY(note, this.textLine) - (this.textLine + initialOffset) * staffSpace;\n    }\n    if (this.position === ModifierPosition.BELOW) {\n      glyphY = getBottomY(note, this.textLine) + (this.textLine + initialOffset + 1.5) * staffSpace;\n    }\n    if (this.delayed) {\n      let delayXShift = 0;\n      const startX = note.getTickContext().getX();\n      if (this.delayXShift !== undefined) {\n        delayXShift = this.delayXShift;\n      } else {\n        const tickables = note.getVoice().getTickables();\n        const index = tickables.indexOf(note);\n        const nextContext = index + 1 < tickables.length ? tickables[index + 1].checkTickContext() : undefined;\n        if (nextContext) {\n          delayXShift += (nextContext.getX() - startX) * 0.5;\n        } else {\n          delayXShift += (stave.getX() + stave.getWidth() - glyphX) * 0.5;\n        }\n        this.delayXShift = delayXShift;\n      }\n      glyphX += delayXShift;\n    }\n    L('Rendering ornament: ', this.text.charCodeAt(0), glyphX, glyphY);\n    if (this.accidentalLower) {\n      this.accidentalLower.renderText(ctx, glyphX + this.xShift - this.accidentalLower.getWidth() * 0.5, glyphY + this.yShift - this.accidentalLower.getTextMetrics().actualBoundingBoxDescent);\n      glyphY -= this.accidentalLower.getHeight() + this.renderOptions.accidentalLowerPadding;\n    }\n    if (Ornament.ornamentYShift.indexOf(this.type) >= 0) {\n      this.yShift += this.getHeight();\n    }\n    this.x = glyphX - (this.position === ModifierPosition.ABOVE || this.position === ModifierPosition.BELOW ? this.width * 0.5 : 0);\n    this.y = glyphY;\n    this.renderText(ctx, 0, 0);\n    if (this.accidentalUpper) {\n      glyphY -= this.getHeight() + this.renderOptions.accidentalUpperPadding;\n      this.accidentalUpper.renderText(ctx, glyphX + this.xShift - this.accidentalUpper.getWidth() * 0.5, glyphY + this.yShift - this.accidentalUpper.getTextMetrics().actualBoundingBoxDescent);\n    }\n    ctx.closeGroup();\n  }\n}\nOrnament.DEBUG = false;", "map": {"version": 3, "names": ["getBottomY", "getInitialOffset", "getTopY", "Element", "Metrics", "Modifier", "ModifierPosition", "<PERSON><PERSON>", "Tables", "log", "L", "args", "Ornament", "DEBUG", "CATEGORY", "minPadding", "get", "format", "ornaments", "state", "_a", "_b", "length", "width", "rightShift", "leftShift", "i", "ornament", "increment", "position", "RIGHT", "xShift", "LEFT", "ABOVE", "Math", "max", "getWidth", "note", "getNote", "curTop", "getLineNumber", "topTextLine", "stem", "getStem", "getStemDirection", "UP", "abs", "getHeight", "STAVE_LINE_DISTANCE", "numLines", "getStave", "getNumLines", "setTextLine", "textLine", "ornamentNoteTransition", "ornamentAttack", "ornamentAlignWithNoteHead", "ornamentRelease", "ornamentLeft", "ornamentRight", "ornamentYShift", "ornamentArticulation", "constructor", "type", "indexOf", "delayed", "renderOptions", "accidentalLowerPadding", "accidentalUpperPadding", "adjustForStemDirection", "text", "ornamentCodes", "setNote", "Position", "BELOW", "<PERSON><PERSON><PERSON><PERSON>", "setUpperAccidental", "accid", "accidentalUpper", "setText", "accidentalCodes", "setLowerAccidental", "<PERSON><PERSON><PERSON><PERSON>", "draw", "ctx", "checkContext", "checkAttachedNote", "setRendered", "stave", "checkStave", "openGroup", "getAttribute", "start", "getModifierStartXY", "index", "glyphX", "x", "staffSpace", "getSpacingBetweenLines", "initialOffset", "glyphY", "y", "delayXShift", "startX", "getTickContext", "getX", "undefined", "tickables", "getVoice", "getTickables", "nextContext", "checkTickContext", "charCodeAt", "renderText", "yShift", "getTextMetrics", "actualBoundingBoxDescent", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/ornament.js"], "sourcesContent": ["import { getBottomY, getInitialOffset, getTopY } from './articulation.js';\nimport { Element } from './element.js';\nimport { Metrics } from './metrics.js';\nimport { Modifier, ModifierPosition } from './modifier.js';\nimport { Stem } from './stem.js';\nimport { Tables } from './tables.js';\nimport { log } from './util.js';\nfunction L(...args) {\n    if (Ornament.DEBUG)\n        log('VexFlow.Ornament', args);\n}\nexport class Ornament extends Modifier {\n    static get CATEGORY() {\n        return \"Ornament\";\n    }\n    static get minPadding() {\n        return Metrics.get('NoteHead.minPadding');\n    }\n    static format(ornaments, state) {\n        var _a, _b;\n        if (!ornaments || ornaments.length === 0)\n            return false;\n        let width = 0;\n        let rightShift = state.rightShift;\n        let leftShift = state.leftShift;\n        for (let i = 0; i < ornaments.length; ++i) {\n            const ornament = ornaments[i];\n            const increment = 2;\n            if (ornament.position === ModifierPosition.RIGHT) {\n                ornament.xShift += rightShift + 2;\n                rightShift += ornament.width + Ornament.minPadding;\n            }\n            else if (ornament.position === ModifierPosition.LEFT) {\n                ornament.xShift -= leftShift + ornament.width + 2;\n                leftShift += ornament.width + Ornament.minPadding;\n            }\n            else if (ornament.position === ModifierPosition.ABOVE) {\n                width = Math.max(ornament.getWidth(), width);\n                const note = ornament.getNote();\n                let curTop = note.getLineNumber(true) + state.topTextLine;\n                const stem = note.getStem();\n                if (stem && note.getStemDirection() === Stem.UP) {\n                    curTop += Math.abs(stem.getHeight()) / Tables.STAVE_LINE_DISTANCE;\n                }\n                const numLines = (_b = (_a = note.getStave()) === null || _a === void 0 ? void 0 : _a.getNumLines()) !== null && _b !== void 0 ? _b : 0;\n                if (curTop < numLines)\n                    state.topTextLine += numLines - curTop;\n                ornament.setTextLine(state.topTextLine);\n                state.topTextLine += increment;\n            }\n            else {\n                width = Math.max(ornament.getWidth(), width);\n                ornament.setTextLine(state.textLine);\n                state.textLine += increment;\n            }\n        }\n        state.leftShift = leftShift + width / 2;\n        state.rightShift = rightShift + width / 2;\n        return true;\n    }\n    static get ornamentNoteTransition() {\n        return ['flip', 'jazzTurn', 'smear'];\n    }\n    static get ornamentAttack() {\n        return ['scoop'];\n    }\n    static get ornamentAlignWithNoteHead() {\n        return ['doit', 'fall', 'fallLong', 'doitLong', 'scoop'];\n    }\n    static get ornamentRelease() {\n        return ['doit', 'fall', 'fallLong', 'doitLong', 'jazzTurn', 'smear', 'flip'];\n    }\n    static get ornamentLeft() {\n        return ['scoop'];\n    }\n    static get ornamentRight() {\n        return ['doit', 'fall', 'fallLong', 'doitLong'];\n    }\n    static get ornamentYShift() {\n        return ['fallLong'];\n    }\n    static get ornamentArticulation() {\n        return ['bend', 'plungerClosed', 'plungerOpen'];\n    }\n    constructor(type) {\n        super();\n        this.position = ModifierPosition.ABOVE;\n        if (Ornament.ornamentRight.indexOf(type) >= 0) {\n            this.position = ModifierPosition.RIGHT;\n        }\n        if (Ornament.ornamentLeft.indexOf(type) >= 0) {\n            this.position = ModifierPosition.LEFT;\n        }\n        this.type = type;\n        this.delayed = false;\n        this.renderOptions = {\n            accidentalLowerPadding: 3,\n            accidentalUpperPadding: 3,\n        };\n        this.adjustForStemDirection = false;\n        this.ornamentAlignWithNoteHead = Ornament.ornamentAlignWithNoteHead.indexOf(this.type) >= 0;\n        if (Ornament.ornamentNoteTransition.indexOf(this.type) >= 0) {\n            this.delayed = true;\n        }\n        this.text = Tables.ornamentCodes(this.type);\n    }\n    setNote(note) {\n        super.setNote(note);\n        if (Ornament.ornamentArticulation.indexOf(this.type) >= 0) {\n            if (note.getLineNumber() >= 3) {\n                this.position = Modifier.Position.ABOVE;\n            }\n            else {\n                this.position = Modifier.Position.BELOW;\n            }\n        }\n        return this;\n    }\n    setDelayed(delayed) {\n        this.delayed = delayed;\n        return this;\n    }\n    setUpperAccidental(accid) {\n        this.accidentalUpper = new Element();\n        this.accidentalUpper.setText(Tables.accidentalCodes(accid));\n        return this;\n    }\n    setLowerAccidental(accid) {\n        this.accidentalLower = new Element();\n        this.accidentalLower.setText(Tables.accidentalCodes(accid));\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        const note = this.checkAttachedNote();\n        this.setRendered();\n        const stave = note.checkStave();\n        ctx.openGroup('ornament', this.getAttribute('id'));\n        const start = note.getModifierStartXY(this.position, this.index);\n        let glyphX = start.x;\n        const staffSpace = stave.getSpacingBetweenLines();\n        const initialOffset = getInitialOffset(note, this.position);\n        let glyphY = this.ornamentAlignWithNoteHead ? start.y : 0;\n        if (this.position === ModifierPosition.ABOVE) {\n            glyphY = getTopY(note, this.textLine) - (this.textLine + initialOffset) * staffSpace;\n        }\n        if (this.position === ModifierPosition.BELOW) {\n            glyphY = getBottomY(note, this.textLine) + (this.textLine + initialOffset + 1.5) * staffSpace;\n        }\n        if (this.delayed) {\n            let delayXShift = 0;\n            const startX = note.getTickContext().getX();\n            if (this.delayXShift !== undefined) {\n                delayXShift = this.delayXShift;\n            }\n            else {\n                const tickables = note.getVoice().getTickables();\n                const index = tickables.indexOf(note);\n                const nextContext = index + 1 < tickables.length ? tickables[index + 1].checkTickContext() : undefined;\n                if (nextContext) {\n                    delayXShift += (nextContext.getX() - startX) * 0.5;\n                }\n                else {\n                    delayXShift += (stave.getX() + stave.getWidth() - glyphX) * 0.5;\n                }\n                this.delayXShift = delayXShift;\n            }\n            glyphX += delayXShift;\n        }\n        L('Rendering ornament: ', this.text.charCodeAt(0), glyphX, glyphY);\n        if (this.accidentalLower) {\n            this.accidentalLower.renderText(ctx, glyphX + this.xShift - this.accidentalLower.getWidth() * 0.5, glyphY + this.yShift - this.accidentalLower.getTextMetrics().actualBoundingBoxDescent);\n            glyphY -= this.accidentalLower.getHeight() + this.renderOptions.accidentalLowerPadding;\n        }\n        if (Ornament.ornamentYShift.indexOf(this.type) >= 0) {\n            this.yShift += this.getHeight();\n        }\n        this.x =\n            glyphX -\n                (this.position === ModifierPosition.ABOVE || this.position === ModifierPosition.BELOW ? this.width * 0.5 : 0);\n        this.y = glyphY;\n        this.renderText(ctx, 0, 0);\n        if (this.accidentalUpper) {\n            glyphY -= this.getHeight() + this.renderOptions.accidentalUpperPadding;\n            this.accidentalUpper.renderText(ctx, glyphX + this.xShift - this.accidentalUpper.getWidth() * 0.5, glyphY + this.yShift - this.accidentalUpper.getTextMetrics().actualBoundingBoxDescent);\n        }\n        ctx.closeGroup();\n    }\n}\nOrnament.DEBUG = false;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mBAAmB;AACzE,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,eAAe;AAC1D,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,QAAQ,CAACC,KAAK,EACdJ,GAAG,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACrC;AACA,OAAO,MAAMC,QAAQ,SAASP,QAAQ,CAAC;EACnC,WAAWS,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACA,WAAWC,UAAUA,CAAA,EAAG;IACpB,OAAOX,OAAO,CAACY,GAAG,CAAC,qBAAqB,CAAC;EAC7C;EACA,OAAOC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IAC5B,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAI,CAACH,SAAS,IAAIA,SAAS,CAACI,MAAM,KAAK,CAAC,EACpC,OAAO,KAAK;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,UAAU,GAAGL,KAAK,CAACK,UAAU;IACjC,IAAIC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,CAACI,MAAM,EAAE,EAAEI,CAAC,EAAE;MACvC,MAAMC,QAAQ,GAAGT,SAAS,CAACQ,CAAC,CAAC;MAC7B,MAAME,SAAS,GAAG,CAAC;MACnB,IAAID,QAAQ,CAACE,QAAQ,KAAKvB,gBAAgB,CAACwB,KAAK,EAAE;QAC9CH,QAAQ,CAACI,MAAM,IAAIP,UAAU,GAAG,CAAC;QACjCA,UAAU,IAAIG,QAAQ,CAACJ,KAAK,GAAGX,QAAQ,CAACG,UAAU;MACtD,CAAC,MACI,IAAIY,QAAQ,CAACE,QAAQ,KAAKvB,gBAAgB,CAAC0B,IAAI,EAAE;QAClDL,QAAQ,CAACI,MAAM,IAAIN,SAAS,GAAGE,QAAQ,CAACJ,KAAK,GAAG,CAAC;QACjDE,SAAS,IAAIE,QAAQ,CAACJ,KAAK,GAAGX,QAAQ,CAACG,UAAU;MACrD,CAAC,MACI,IAAIY,QAAQ,CAACE,QAAQ,KAAKvB,gBAAgB,CAAC2B,KAAK,EAAE;QACnDV,KAAK,GAAGW,IAAI,CAACC,GAAG,CAACR,QAAQ,CAACS,QAAQ,CAAC,CAAC,EAAEb,KAAK,CAAC;QAC5C,MAAMc,IAAI,GAAGV,QAAQ,CAACW,OAAO,CAAC,CAAC;QAC/B,IAAIC,MAAM,GAAGF,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC,GAAGrB,KAAK,CAACsB,WAAW;QACzD,MAAMC,IAAI,GAAGL,IAAI,CAACM,OAAO,CAAC,CAAC;QAC3B,IAAID,IAAI,IAAIL,IAAI,CAACO,gBAAgB,CAAC,CAAC,KAAKrC,IAAI,CAACsC,EAAE,EAAE;UAC7CN,MAAM,IAAIL,IAAI,CAACY,GAAG,CAACJ,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAGvC,MAAM,CAACwC,mBAAmB;QACrE;QACA,MAAMC,QAAQ,GAAG,CAAC5B,EAAE,GAAG,CAACD,EAAE,GAAGiB,IAAI,CAACa,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,WAAW,CAAC,CAAC,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;QACvI,IAAIkB,MAAM,GAAGU,QAAQ,EACjB9B,KAAK,CAACsB,WAAW,IAAIQ,QAAQ,GAAGV,MAAM;QAC1CZ,QAAQ,CAACyB,WAAW,CAACjC,KAAK,CAACsB,WAAW,CAAC;QACvCtB,KAAK,CAACsB,WAAW,IAAIb,SAAS;MAClC,CAAC,MACI;QACDL,KAAK,GAAGW,IAAI,CAACC,GAAG,CAACR,QAAQ,CAACS,QAAQ,CAAC,CAAC,EAAEb,KAAK,CAAC;QAC5CI,QAAQ,CAACyB,WAAW,CAACjC,KAAK,CAACkC,QAAQ,CAAC;QACpClC,KAAK,CAACkC,QAAQ,IAAIzB,SAAS;MAC/B;IACJ;IACAT,KAAK,CAACM,SAAS,GAAGA,SAAS,GAAGF,KAAK,GAAG,CAAC;IACvCJ,KAAK,CAACK,UAAU,GAAGA,UAAU,GAAGD,KAAK,GAAG,CAAC;IACzC,OAAO,IAAI;EACf;EACA,WAAW+B,sBAAsBA,CAAA,EAAG;IAChC,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;EACxC;EACA,WAAWC,cAAcA,CAAA,EAAG;IACxB,OAAO,CAAC,OAAO,CAAC;EACpB;EACA,WAAWC,yBAAyBA,CAAA,EAAG;IACnC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC;EAC5D;EACA,WAAWC,eAAeA,CAAA,EAAG;IACzB,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;EAChF;EACA,WAAWC,YAAYA,CAAA,EAAG;IACtB,OAAO,CAAC,OAAO,CAAC;EACpB;EACA,WAAWC,aAAaA,CAAA,EAAG;IACvB,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC;EACnD;EACA,WAAWC,cAAcA,CAAA,EAAG;IACxB,OAAO,CAAC,UAAU,CAAC;EACvB;EACA,WAAWC,oBAAoBA,CAAA,EAAG;IAC9B,OAAO,CAAC,MAAM,EAAE,eAAe,EAAE,aAAa,CAAC;EACnD;EACAC,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAAClC,QAAQ,GAAGvB,gBAAgB,CAAC2B,KAAK;IACtC,IAAIrB,QAAQ,CAAC+C,aAAa,CAACK,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MAC3C,IAAI,CAAClC,QAAQ,GAAGvB,gBAAgB,CAACwB,KAAK;IAC1C;IACA,IAAIlB,QAAQ,CAAC8C,YAAY,CAACM,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MAC1C,IAAI,CAAClC,QAAQ,GAAGvB,gBAAgB,CAAC0B,IAAI;IACzC;IACA,IAAI,CAAC+B,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,aAAa,GAAG;MACjBC,sBAAsB,EAAE,CAAC;MACzBC,sBAAsB,EAAE;IAC5B,CAAC;IACD,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACb,yBAAyB,GAAG5C,QAAQ,CAAC4C,yBAAyB,CAACQ,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;IAC3F,IAAInD,QAAQ,CAAC0C,sBAAsB,CAACU,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MACzD,IAAI,CAACE,OAAO,GAAG,IAAI;IACvB;IACA,IAAI,CAACK,IAAI,GAAG9D,MAAM,CAAC+D,aAAa,CAAC,IAAI,CAACR,IAAI,CAAC;EAC/C;EACAS,OAAOA,CAACnC,IAAI,EAAE;IACV,KAAK,CAACmC,OAAO,CAACnC,IAAI,CAAC;IACnB,IAAIzB,QAAQ,CAACiD,oBAAoB,CAACG,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MACvD,IAAI1B,IAAI,CAACG,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAACX,QAAQ,GAAGxB,QAAQ,CAACoE,QAAQ,CAACxC,KAAK;MAC3C,CAAC,MACI;QACD,IAAI,CAACJ,QAAQ,GAAGxB,QAAQ,CAACoE,QAAQ,CAACC,KAAK;MAC3C;IACJ;IACA,OAAO,IAAI;EACf;EACAC,UAAUA,CAACV,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACAW,kBAAkBA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACC,eAAe,GAAG,IAAI3E,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC2E,eAAe,CAACC,OAAO,CAACvE,MAAM,CAACwE,eAAe,CAACH,KAAK,CAAC,CAAC;IAC3D,OAAO,IAAI;EACf;EACAI,kBAAkBA,CAACJ,KAAK,EAAE;IACtB,IAAI,CAACK,eAAe,GAAG,IAAI/E,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC+E,eAAe,CAACH,OAAO,CAACvE,MAAM,CAACwE,eAAe,CAACH,KAAK,CAAC,CAAC;IAC3D,OAAO,IAAI;EACf;EACAM,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMhD,IAAI,GAAG,IAAI,CAACiD,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAGnD,IAAI,CAACoD,UAAU,CAAC,CAAC;IAC/BL,GAAG,CAACM,SAAS,CAAC,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IAClD,MAAMC,KAAK,GAAGvD,IAAI,CAACwD,kBAAkB,CAAC,IAAI,CAAChE,QAAQ,EAAE,IAAI,CAACiE,KAAK,CAAC;IAChE,IAAIC,MAAM,GAAGH,KAAK,CAACI,CAAC;IACpB,MAAMC,UAAU,GAAGT,KAAK,CAACU,sBAAsB,CAAC,CAAC;IACjD,MAAMC,aAAa,GAAGlG,gBAAgB,CAACoC,IAAI,EAAE,IAAI,CAACR,QAAQ,CAAC;IAC3D,IAAIuE,MAAM,GAAG,IAAI,CAAC5C,yBAAyB,GAAGoC,KAAK,CAACS,CAAC,GAAG,CAAC;IACzD,IAAI,IAAI,CAACxE,QAAQ,KAAKvB,gBAAgB,CAAC2B,KAAK,EAAE;MAC1CmE,MAAM,GAAGlG,OAAO,CAACmC,IAAI,EAAE,IAAI,CAACgB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACA,QAAQ,GAAG8C,aAAa,IAAIF,UAAU;IACxF;IACA,IAAI,IAAI,CAACpE,QAAQ,KAAKvB,gBAAgB,CAACoE,KAAK,EAAE;MAC1C0B,MAAM,GAAGpG,UAAU,CAACqC,IAAI,EAAE,IAAI,CAACgB,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACA,QAAQ,GAAG8C,aAAa,GAAG,GAAG,IAAIF,UAAU;IACjG;IACA,IAAI,IAAI,CAAChC,OAAO,EAAE;MACd,IAAIqC,WAAW,GAAG,CAAC;MACnB,MAAMC,MAAM,GAAGlE,IAAI,CAACmE,cAAc,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACH,WAAW,KAAKI,SAAS,EAAE;QAChCJ,WAAW,GAAG,IAAI,CAACA,WAAW;MAClC,CAAC,MACI;QACD,MAAMK,SAAS,GAAGtE,IAAI,CAACuE,QAAQ,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;QAChD,MAAMf,KAAK,GAAGa,SAAS,CAAC3C,OAAO,CAAC3B,IAAI,CAAC;QACrC,MAAMyE,WAAW,GAAGhB,KAAK,GAAG,CAAC,GAAGa,SAAS,CAACrF,MAAM,GAAGqF,SAAS,CAACb,KAAK,GAAG,CAAC,CAAC,CAACiB,gBAAgB,CAAC,CAAC,GAAGL,SAAS;QACtG,IAAII,WAAW,EAAE;UACbR,WAAW,IAAI,CAACQ,WAAW,CAACL,IAAI,CAAC,CAAC,GAAGF,MAAM,IAAI,GAAG;QACtD,CAAC,MACI;UACDD,WAAW,IAAI,CAACd,KAAK,CAACiB,IAAI,CAAC,CAAC,GAAGjB,KAAK,CAACpD,QAAQ,CAAC,CAAC,GAAG2D,MAAM,IAAI,GAAG;QACnE;QACA,IAAI,CAACO,WAAW,GAAGA,WAAW;MAClC;MACAP,MAAM,IAAIO,WAAW;IACzB;IACA5F,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC4D,IAAI,CAAC0C,UAAU,CAAC,CAAC,CAAC,EAAEjB,MAAM,EAAEK,MAAM,CAAC;IAClE,IAAI,IAAI,CAAClB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC+B,UAAU,CAAC7B,GAAG,EAAEW,MAAM,GAAG,IAAI,CAAChE,MAAM,GAAG,IAAI,CAACmD,eAAe,CAAC9C,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAEgE,MAAM,GAAG,IAAI,CAACc,MAAM,GAAG,IAAI,CAAChC,eAAe,CAACiC,cAAc,CAAC,CAAC,CAACC,wBAAwB,CAAC;MACzLhB,MAAM,IAAI,IAAI,CAAClB,eAAe,CAACnC,SAAS,CAAC,CAAC,GAAG,IAAI,CAACmB,aAAa,CAACC,sBAAsB;IAC1F;IACA,IAAIvD,QAAQ,CAACgD,cAAc,CAACI,OAAO,CAAC,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE;MACjD,IAAI,CAACmD,MAAM,IAAI,IAAI,CAACnE,SAAS,CAAC,CAAC;IACnC;IACA,IAAI,CAACiD,CAAC,GACFD,MAAM,IACD,IAAI,CAAClE,QAAQ,KAAKvB,gBAAgB,CAAC2B,KAAK,IAAI,IAAI,CAACJ,QAAQ,KAAKvB,gBAAgB,CAACoE,KAAK,GAAG,IAAI,CAACnD,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC;IACrH,IAAI,CAAC8E,CAAC,GAAGD,MAAM;IACf,IAAI,CAACa,UAAU,CAAC7B,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACN,eAAe,EAAE;MACtBsB,MAAM,IAAI,IAAI,CAACrD,SAAS,CAAC,CAAC,GAAG,IAAI,CAACmB,aAAa,CAACE,sBAAsB;MACtE,IAAI,CAACU,eAAe,CAACmC,UAAU,CAAC7B,GAAG,EAAEW,MAAM,GAAG,IAAI,CAAChE,MAAM,GAAG,IAAI,CAAC+C,eAAe,CAAC1C,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAEgE,MAAM,GAAG,IAAI,CAACc,MAAM,GAAG,IAAI,CAACpC,eAAe,CAACqC,cAAc,CAAC,CAAC,CAACC,wBAAwB,CAAC;IAC7L;IACAhC,GAAG,CAACiC,UAAU,CAAC,CAAC;EACpB;AACJ;AACAzG,QAAQ,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}