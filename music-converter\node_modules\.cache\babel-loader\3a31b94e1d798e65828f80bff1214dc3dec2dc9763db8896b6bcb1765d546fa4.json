{"ast": null, "code": "import { TabTie } from './tabtie.js';\nimport { RuntimeError } from './util.js';\nexport class Tab<PERSON>lide extends TabTie {\n  static get CATEGORY() {\n    return \"TabSlide\";\n  }\n  static get SLIDE_UP() {\n    return 1;\n  }\n  static get SLIDE_DOWN() {\n    return -1;\n  }\n  static createSlideUp(notes) {\n    return new TabSlide(notes, TabSlide.SLIDE_UP);\n  }\n  static createSlideDown(notes) {\n    return new TabSlide(notes, TabSlide.SLIDE_DOWN);\n  }\n  constructor(notes, direction) {\n    super(notes, 'sl.');\n    if (!direction) {\n      let firstFret = notes.firstNote.getPositions()[0].fret;\n      if (typeof firstFret === 'string') {\n        firstFret = parseInt(firstFret, 10);\n      }\n      let lastFret = notes.lastNote.getPositions()[0].fret;\n      if (typeof lastFret === 'string') {\n        lastFret = parseInt(lastFret, 10);\n      }\n      if (isNaN(firstFret) || isNaN(lastFret)) {\n        direction = TabSlide.SLIDE_UP;\n      } else {\n        direction = firstFret > lastFret ? TabSlide.SLIDE_DOWN : TabSlide.SLIDE_UP;\n      }\n    }\n    this.direction = direction;\n    this.renderOptions.cp1 = 11;\n    this.renderOptions.cp2 = 14;\n    this.renderOptions.yShift = 0.5;\n  }\n  renderTie(params) {\n    if (params.firstYs.length === 0 || params.lastYs.length === 0) {\n      throw new RuntimeError('BadArguments', 'No Y-values to render');\n    }\n    const ctx = this.checkContext();\n    const firstX = params.firstX;\n    const firstYs = params.firstYs;\n    const lastX = params.lastX;\n    const direction = params.direction;\n    if (direction !== TabSlide.SLIDE_UP && direction !== TabSlide.SLIDE_DOWN) {\n      throw new RuntimeError('BadSlide', 'Invalid slide direction');\n    }\n    const firstIndexes = this.notes.firstIndexes;\n    for (let i = 0; i < firstIndexes.length; ++i) {\n      const slideY = firstYs[firstIndexes[i]] + this.renderOptions.yShift;\n      if (isNaN(slideY)) {\n        throw new RuntimeError('BadArguments', 'Bad indexes for slide rendering.');\n      }\n      ctx.beginPath();\n      ctx.moveTo(firstX, slideY + 3 * direction);\n      ctx.lineTo(lastX, slideY - 3 * direction);\n      ctx.closePath();\n      ctx.stroke();\n    }\n    this.setRendered();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "RuntimeError", "TabSlide", "CATEGORY", "SLIDE_UP", "SLIDE_DOWN", "createSlideUp", "notes", "createSlideDown", "constructor", "direction", "firstFret", "firstNote", "getPositions", "fret", "parseInt", "lastFret", "lastNote", "isNaN", "renderOptions", "cp1", "cp2", "yShift", "<PERSON><PERSON>ie", "params", "firstYs", "length", "lastYs", "ctx", "checkContext", "firstX", "lastX", "firstIndexes", "i", "slideY", "beginPath", "moveTo", "lineTo", "closePath", "stroke", "setRendered"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/tabslide.js"], "sourcesContent": ["import { TabTie } from './tabtie.js';\nimport { RuntimeError } from './util.js';\nexport class Tab<PERSON>lide extends TabTie {\n    static get CATEGORY() {\n        return \"TabSlide\";\n    }\n    static get SLIDE_UP() {\n        return 1;\n    }\n    static get SLIDE_DOWN() {\n        return -1;\n    }\n    static createSlideUp(notes) {\n        return new TabSlide(notes, TabSlide.SLIDE_UP);\n    }\n    static createSlideDown(notes) {\n        return new TabSlide(notes, TabSlide.SLIDE_DOWN);\n    }\n    constructor(notes, direction) {\n        super(notes, 'sl.');\n        if (!direction) {\n            let firstFret = notes.firstNote.getPositions()[0].fret;\n            if (typeof firstFret === 'string') {\n                firstFret = parseInt(firstFret, 10);\n            }\n            let lastFret = notes.lastNote.getPositions()[0].fret;\n            if (typeof lastFret === 'string') {\n                lastFret = parseInt(lastFret, 10);\n            }\n            if (isNaN(firstFret) || isNaN(lastFret)) {\n                direction = TabSlide.SLIDE_UP;\n            }\n            else {\n                direction = firstFret > lastFret ? TabSlide.SLIDE_DOWN : TabSlide.SLIDE_UP;\n            }\n        }\n        this.direction = direction;\n        this.renderOptions.cp1 = 11;\n        this.renderOptions.cp2 = 14;\n        this.renderOptions.yShift = 0.5;\n    }\n    renderTie(params) {\n        if (params.firstYs.length === 0 || params.lastYs.length === 0) {\n            throw new RuntimeError('BadArguments', 'No Y-values to render');\n        }\n        const ctx = this.checkContext();\n        const firstX = params.firstX;\n        const firstYs = params.firstYs;\n        const lastX = params.lastX;\n        const direction = params.direction;\n        if (direction !== TabSlide.SLIDE_UP && direction !== TabSlide.SLIDE_DOWN) {\n            throw new RuntimeError('BadSlide', 'Invalid slide direction');\n        }\n        const firstIndexes = this.notes.firstIndexes;\n        for (let i = 0; i < firstIndexes.length; ++i) {\n            const slideY = firstYs[firstIndexes[i]] + this.renderOptions.yShift;\n            if (isNaN(slideY)) {\n                throw new RuntimeError('BadArguments', 'Bad indexes for slide rendering.');\n            }\n            ctx.beginPath();\n            ctx.moveTo(firstX, slideY + 3 * direction);\n            ctx.lineTo(lastX, slideY - 3 * direction);\n            ctx.closePath();\n            ctx.stroke();\n        }\n        this.setRendered();\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAO,MAAMC,QAAQ,SAASF,MAAM,CAAC;EACjC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,UAAU;EACrB;EACA,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAO,CAAC;EACZ;EACA,WAAWC,UAAUA,CAAA,EAAG;IACpB,OAAO,CAAC,CAAC;EACb;EACA,OAAOC,aAAaA,CAACC,KAAK,EAAE;IACxB,OAAO,IAAIL,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACE,QAAQ,CAAC;EACjD;EACA,OAAOI,eAAeA,CAACD,KAAK,EAAE;IAC1B,OAAO,IAAIL,QAAQ,CAACK,KAAK,EAAEL,QAAQ,CAACG,UAAU,CAAC;EACnD;EACAI,WAAWA,CAACF,KAAK,EAAEG,SAAS,EAAE;IAC1B,KAAK,CAACH,KAAK,EAAE,KAAK,CAAC;IACnB,IAAI,CAACG,SAAS,EAAE;MACZ,IAAIC,SAAS,GAAGJ,KAAK,CAACK,SAAS,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI;MACtD,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;QAC/BA,SAAS,GAAGI,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;MACvC;MACA,IAAIK,QAAQ,GAAGT,KAAK,CAACU,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI;MACpD,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;QAC9BA,QAAQ,GAAGD,QAAQ,CAACC,QAAQ,EAAE,EAAE,CAAC;MACrC;MACA,IAAIE,KAAK,CAACP,SAAS,CAAC,IAAIO,KAAK,CAACF,QAAQ,CAAC,EAAE;QACrCN,SAAS,GAAGR,QAAQ,CAACE,QAAQ;MACjC,CAAC,MACI;QACDM,SAAS,GAAGC,SAAS,GAAGK,QAAQ,GAAGd,QAAQ,CAACG,UAAU,GAAGH,QAAQ,CAACE,QAAQ;MAC9E;IACJ;IACA,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACS,aAAa,CAACC,GAAG,GAAG,EAAE;IAC3B,IAAI,CAACD,aAAa,CAACE,GAAG,GAAG,EAAE;IAC3B,IAAI,CAACF,aAAa,CAACG,MAAM,GAAG,GAAG;EACnC;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,IAAIA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,IAAIF,MAAM,CAACG,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE;MAC3D,MAAM,IAAIzB,YAAY,CAAC,cAAc,EAAE,uBAAuB,CAAC;IACnE;IACA,MAAM2B,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,MAAMC,MAAM,GAAGN,MAAM,CAACM,MAAM;IAC5B,MAAML,OAAO,GAAGD,MAAM,CAACC,OAAO;IAC9B,MAAMM,KAAK,GAAGP,MAAM,CAACO,KAAK;IAC1B,MAAMrB,SAAS,GAAGc,MAAM,CAACd,SAAS;IAClC,IAAIA,SAAS,KAAKR,QAAQ,CAACE,QAAQ,IAAIM,SAAS,KAAKR,QAAQ,CAACG,UAAU,EAAE;MACtE,MAAM,IAAIJ,YAAY,CAAC,UAAU,EAAE,yBAAyB,CAAC;IACjE;IACA,MAAM+B,YAAY,GAAG,IAAI,CAACzB,KAAK,CAACyB,YAAY;IAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAACN,MAAM,EAAE,EAAEO,CAAC,EAAE;MAC1C,MAAMC,MAAM,GAAGT,OAAO,CAACO,YAAY,CAACC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACd,aAAa,CAACG,MAAM;MACnE,IAAIJ,KAAK,CAACgB,MAAM,CAAC,EAAE;QACf,MAAM,IAAIjC,YAAY,CAAC,cAAc,EAAE,kCAAkC,CAAC;MAC9E;MACA2B,GAAG,CAACO,SAAS,CAAC,CAAC;MACfP,GAAG,CAACQ,MAAM,CAACN,MAAM,EAAEI,MAAM,GAAG,CAAC,GAAGxB,SAAS,CAAC;MAC1CkB,GAAG,CAACS,MAAM,CAACN,KAAK,EAAEG,MAAM,GAAG,CAAC,GAAGxB,SAAS,CAAC;MACzCkB,GAAG,CAACU,SAAS,CAAC,CAAC;MACfV,GAAG,CAACW,MAAM,CAAC,CAAC;IAChB;IACA,IAAI,CAACC,WAAW,CAAC,CAAC;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}