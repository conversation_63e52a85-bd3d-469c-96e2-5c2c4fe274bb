{"ast": null, "code": "import { BoundingBox } from './boundingbox.js';\nimport { Font } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { Registry } from './registry.js';\nimport { defined, prefix, RuntimeError } from './util.js';\nexport class Element {\n  static get CATEGORY() {\n    return \"Element\";\n  }\n  static newID() {\n    return `auto${Element.ID++}`;\n  }\n  static setTextMeasurementCanvas(canvas) {\n    Element.txtCanvas = canvas;\n  }\n  static getTextMeasurementCanvas() {\n    let txtCanvas = Element.txtCanvas;\n    if (!txtCanvas) {\n      if (typeof document !== 'undefined') {\n        txtCanvas = document.createElement('canvas');\n      } else if (typeof OffscreenCanvas !== 'undefined') {\n        txtCanvas = new OffscreenCanvas(300, 150);\n      }\n      Element.txtCanvas = txtCanvas;\n    }\n    return txtCanvas;\n  }\n  constructor(category) {\n    var _a;\n    this.children = [];\n    this.style = {};\n    this._text = '';\n    this.metricsValid = false;\n    this._textMetrics = {\n      fontBoundingBoxAscent: 0,\n      fontBoundingBoxDescent: 0,\n      actualBoundingBoxAscent: 0,\n      actualBoundingBoxDescent: 0,\n      actualBoundingBoxLeft: 0,\n      actualBoundingBoxRight: 0,\n      width: 0,\n      alphabeticBaseline: 0,\n      emHeightAscent: 0,\n      emHeightDescent: 0,\n      hangingBaseline: 0,\n      ideographicBaseline: 0\n    };\n    this._height = 0;\n    this._width = 0;\n    this.xShift = 0;\n    this.yShift = 0;\n    this.x = 0;\n    this.y = 0;\n    this.attrs = {\n      id: Element.newID(),\n      type: category !== null && category !== void 0 ? category : this.constructor.CATEGORY,\n      class: ''\n    };\n    this.rendered = false;\n    this._fontInfo = Metrics.getFontInfo(this.attrs.type);\n    this.style = Metrics.getStyle(this.attrs.type);\n    this.fontScale = Metrics.get(`${this.attrs.type}.fontScale`);\n    (_a = Registry.getDefaultRegistry()) === null || _a === void 0 ? void 0 : _a.register(this);\n  }\n  addChild(child) {\n    if (child.parent) throw new RuntimeError('Element', 'Parent already defined');\n    child.parent = this;\n    this.children.push(child);\n    return this;\n  }\n  getCategory() {\n    return this.attrs.type;\n  }\n  setStyle(style) {\n    this.style = style;\n    return this;\n  }\n  setGroupStyle(style) {\n    this.style = style;\n    this.children.forEach(child => child.setGroupStyle(style));\n    return this;\n  }\n  getStyle() {\n    return this.style;\n  }\n  applyStyle(context = this.context, style = this.getStyle()) {\n    if (!context) return this;\n    if (style.shadowColor) context.setShadowColor(style.shadowColor);\n    if (style.shadowBlur) context.setShadowBlur(style.shadowBlur);\n    if (style.fillStyle) context.setFillStyle(style.fillStyle);\n    if (style.strokeStyle) context.setStrokeStyle(style.strokeStyle);\n    if (style.lineWidth) context.setLineWidth(style.lineWidth);\n    if (style.lineDash) context.setLineDash(style.lineDash.split(' ').map(Number));\n    return this;\n  }\n  drawWithStyle() {\n    const ctx = this.checkContext();\n    ctx.save();\n    this.applyStyle(ctx);\n    this.draw();\n    ctx.restore();\n    return this;\n  }\n  draw() {\n    throw new RuntimeError('Element', 'Draw not defined');\n  }\n  hasClass(className) {\n    var _a;\n    if (!this.attrs.class) return false;\n    return ((_a = this.attrs.class) === null || _a === void 0 ? void 0 : _a.split(' ').indexOf(className)) !== -1;\n  }\n  addClass(className) {\n    var _a;\n    if (this.hasClass(className)) return this;\n    if (!this.attrs.class) this.attrs.class = `${className}`;else this.attrs.class = `${this.attrs.class} ${className}`;\n    (_a = this.registry) === null || _a === void 0 ? void 0 : _a.onUpdate({\n      id: this.attrs.id,\n      name: 'class',\n      value: className,\n      oldValue: undefined\n    });\n    return this;\n  }\n  removeClass(className) {\n    var _a, _b;\n    if (!this.hasClass(className)) return this;\n    const arr = (_a = this.attrs.class) === null || _a === void 0 ? void 0 : _a.split(' ');\n    if (arr) {\n      arr.splice(arr.indexOf(className));\n      this.attrs.class = arr.join(' ');\n    }\n    (_b = this.registry) === null || _b === void 0 ? void 0 : _b.onUpdate({\n      id: this.attrs.id,\n      name: 'class',\n      value: undefined,\n      oldValue: className\n    });\n    return this;\n  }\n  onRegister(registry) {\n    this.registry = registry;\n    return this;\n  }\n  isRendered() {\n    return this.rendered;\n  }\n  setRendered(rendered = true) {\n    this.rendered = rendered;\n    return this;\n  }\n  getAttributes() {\n    return this.attrs;\n  }\n  getAttribute(name) {\n    return this.attrs[name];\n  }\n  getSVGElement(suffix = '') {\n    const id = prefix(this.attrs.id + suffix);\n    const element = document.getElementById(id);\n    if (element) return element;\n  }\n  setAttribute(name, value) {\n    var _a;\n    const oldID = this.attrs.id;\n    const oldValue = this.attrs[name];\n    this.attrs[name] = value;\n    (_a = this.registry) === null || _a === void 0 ? void 0 : _a.onUpdate({\n      id: oldID,\n      name,\n      value,\n      oldValue\n    });\n    return this;\n  }\n  getBoundingBox() {\n    return new BoundingBox(this.x + this.xShift, this.y + this.yShift - this.textMetrics.actualBoundingBoxAscent, this.width, this.height);\n  }\n  getContext() {\n    return this.context;\n  }\n  setContext(context) {\n    this.context = context;\n    return this;\n  }\n  checkContext() {\n    return defined(this.context, 'NoContext', 'No rendering context attached to instance.');\n  }\n  set font(f) {\n    this.setFont(f);\n  }\n  get font() {\n    return Font.toCSSString(this._fontInfo);\n  }\n  setFont(font, size, weight, style) {\n    const defaultTextFont = Metrics.getFontInfo(this.attrs.type);\n    const fontIsObject = typeof font === 'object';\n    const fontIsString = typeof font === 'string';\n    const sizeWeightStyleAreUndefined = size === undefined && weight === undefined && style === undefined;\n    this.metricsValid = false;\n    if (fontIsObject) {\n      this._fontInfo = Object.assign(Object.assign({}, defaultTextFont), font);\n    } else if (fontIsString && sizeWeightStyleAreUndefined) {\n      this._fontInfo = Font.fromCSSString(font);\n    } else {\n      this._fontInfo = Font.validate(font !== null && font !== void 0 ? font : defaultTextFont.family, size !== null && size !== void 0 ? size : defaultTextFont.size, weight !== null && weight !== void 0 ? weight : defaultTextFont.weight, style !== null && style !== void 0 ? style : defaultTextFont.style);\n    }\n    return this;\n  }\n  getFont() {\n    return Font.toCSSString(this._fontInfo);\n  }\n  get fontInfo() {\n    return this._fontInfo;\n  }\n  set fontInfo(fontInfo) {\n    this.setFont(fontInfo);\n  }\n  setFontSize(size) {\n    const fontInfo = this.fontInfo;\n    this.setFont(fontInfo.family, size, fontInfo.weight, fontInfo.style);\n    return this;\n  }\n  getFontSize() {\n    return this.fontSize;\n  }\n  getFontScale() {\n    return this.fontScale;\n  }\n  set fontSize(size) {\n    this.setFontSize(size);\n  }\n  get fontSize() {\n    let size = this.fontInfo.size;\n    if (typeof size === 'number') {\n      size = `${size}pt`;\n    }\n    return size;\n  }\n  get fontSizeInPoints() {\n    return Font.convertSizeToPointValue(this.fontSize);\n  }\n  get fontSizeInPixels() {\n    return Font.convertSizeToPixelValue(this.fontSize);\n  }\n  get fontStyle() {\n    return this.fontInfo.style;\n  }\n  set fontStyle(style) {\n    const fontInfo = this.fontInfo;\n    this.setFont(fontInfo.family, fontInfo.size, fontInfo.weight, style);\n  }\n  get fontWeight() {\n    return this.fontInfo.weight + '';\n  }\n  set fontWeight(weight) {\n    const fontInfo = this.fontInfo;\n    this.setFont(fontInfo.family, fontInfo.size, weight, fontInfo.style);\n  }\n  getWidth() {\n    return this.width;\n  }\n  get width() {\n    if (!this.metricsValid) this.measureText();\n    return this._width;\n  }\n  setWidth(width) {\n    this.width = width;\n    return this;\n  }\n  set width(width) {\n    if (!this.metricsValid) this.measureText();\n    this._width = width;\n  }\n  setX(x) {\n    this.x = x;\n    return this;\n  }\n  getX() {\n    return this.x;\n  }\n  getY() {\n    return this.y;\n  }\n  setY(y) {\n    this.y = y;\n    return this;\n  }\n  setYShift(yShift) {\n    this.yShift = yShift;\n    return this;\n  }\n  getYShift() {\n    return this.yShift;\n  }\n  setXShift(xShift) {\n    this.xShift = xShift;\n    return this;\n  }\n  getXShift() {\n    return this.xShift;\n  }\n  setText(text) {\n    this.text = text;\n    return this;\n  }\n  set text(text) {\n    this.metricsValid = false;\n    this._text = text;\n  }\n  getText() {\n    return this._text;\n  }\n  get text() {\n    return this._text;\n  }\n  renderText(ctx, xPos, yPos) {\n    ctx.setFont(this._fontInfo);\n    ctx.fillText(this._text, xPos + this.x + this.xShift, yPos + this.y + this.yShift);\n    this.children.forEach(child => {\n      ctx.setFont(child.fontInfo);\n      ctx.fillText(child.text, xPos + child.x + child.xShift, yPos + child.y + child.yShift);\n    });\n  }\n  measureText() {\n    var _a;\n    const context = (_a = Element.getTextMeasurementCanvas()) === null || _a === void 0 ? void 0 : _a.getContext('2d');\n    if (!context) {\n      console.warn('Element: No context for txtCanvas. Returning empty text metrics.');\n      return this._textMetrics;\n    }\n    context.font = Font.toCSSString(Font.validate(this.fontInfo));\n    this._textMetrics = context.measureText(this.text);\n    this._height = this._textMetrics.actualBoundingBoxAscent + this._textMetrics.actualBoundingBoxDescent;\n    this._width = this._textMetrics.width;\n    this.metricsValid = true;\n    return this._textMetrics;\n  }\n  static measureWidth(text, key = '') {\n    var _a;\n    const context = (_a = Element.getTextMeasurementCanvas()) === null || _a === void 0 ? void 0 : _a.getContext('2d');\n    if (!context) {\n      console.warn('Element: No context for txtCanvas. Returning empty text metrics.');\n      return 0;\n    }\n    context.font = Font.toCSSString(Metrics.getFontInfo(key));\n    return context.measureText(text).width;\n  }\n  getTextMetrics() {\n    return this.textMetrics;\n  }\n  get textMetrics() {\n    if (!this.metricsValid) this.measureText();\n    return this._textMetrics;\n  }\n  getHeight() {\n    return this.height;\n  }\n  get height() {\n    if (!this.metricsValid) this.measureText();\n    return this._height;\n  }\n  set height(height) {\n    if (!this.metricsValid) this.measureText();\n    this._height = height;\n  }\n  setOriginX(x) {\n    const bbox = this.getBoundingBox();\n    const originX = Math.abs((bbox.getX() - this.xShift) / bbox.getW());\n    const xShift = (x - originX) * bbox.getW();\n    this.xShift = -xShift;\n  }\n  setOriginY(y) {\n    const bbox = this.getBoundingBox();\n    const originY = Math.abs((bbox.getY() - this.yShift) / bbox.getH());\n    const yShift = (y - originY) * bbox.getH();\n    this.yShift = -yShift;\n  }\n  setOrigin(x, y) {\n    this.setOriginX(x);\n    this.setOriginY(y);\n  }\n}\nElement.ID = 1000;", "map": {"version": 3, "names": ["BoundingBox", "Font", "Metrics", "Registry", "defined", "prefix", "RuntimeError", "Element", "CATEGORY", "newID", "ID", "setTextMeasurementCanvas", "canvas", "txtCanvas", "getTextMeasurementCanvas", "document", "createElement", "OffscreenCanvas", "constructor", "category", "_a", "children", "style", "_text", "metricsValid", "_textMetrics", "fontBoundingBoxAscent", "fontBoundingBoxDescent", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "actualBoundingBoxLeft", "actualBoundingBoxRight", "width", "alphabeticBaseline", "emHeightAscent", "emHeightDescent", "hangingBaseline", "ideographicBaseline", "_height", "_width", "xShift", "yShift", "x", "y", "attrs", "id", "type", "class", "rendered", "_fontInfo", "getFontInfo", "getStyle", "fontScale", "get", "getDefaultRegistry", "register", "<PERSON><PERSON><PERSON><PERSON>", "child", "parent", "push", "getCategory", "setStyle", "setGroupStyle", "for<PERSON>ach", "applyStyle", "context", "shadowColor", "setShadowColor", "<PERSON><PERSON><PERSON><PERSON>", "setShadowBlur", "fillStyle", "setFillStyle", "strokeStyle", "setStrokeStyle", "lineWidth", "setLineWidth", "lineDash", "setLineDash", "split", "map", "Number", "drawWithStyle", "ctx", "checkContext", "save", "draw", "restore", "hasClass", "className", "indexOf", "addClass", "registry", "onUpdate", "name", "value", "oldValue", "undefined", "removeClass", "_b", "arr", "splice", "join", "onRegister", "isRendered", "setRendered", "getAttributes", "getAttribute", "getSVGElement", "suffix", "element", "getElementById", "setAttribute", "oldID", "getBoundingBox", "textMetrics", "height", "getContext", "setContext", "font", "f", "setFont", "toCSSString", "size", "weight", "defaultTextFont", "fontIsObject", "fontIsString", "sizeWeightStyleAreUndefined", "Object", "assign", "fromCSSString", "validate", "family", "getFont", "fontInfo", "setFontSize", "getFontSize", "fontSize", "getFontScale", "fontSizeInPoints", "convertSizeToPointValue", "fontSizeInPixels", "convertSizeToPixelValue", "fontStyle", "fontWeight", "getWidth", "measureText", "<PERSON><PERSON><PERSON><PERSON>", "setX", "getX", "getY", "setY", "setYShift", "getYShift", "setXShift", "getXShift", "setText", "text", "getText", "renderText", "xPos", "yPos", "fillText", "console", "warn", "measureWidth", "key", "getTextMetrics", "getHeight", "setOriginX", "bbox", "originX", "Math", "abs", "getW", "setOriginY", "originY", "getH", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/element.js"], "sourcesContent": ["import { BoundingBox } from './boundingbox.js';\nimport { Font } from './font.js';\nimport { Metrics } from './metrics.js';\nimport { Registry } from './registry.js';\nimport { defined, prefix, RuntimeError } from './util.js';\nexport class Element {\n    static get CATEGORY() {\n        return \"Element\";\n    }\n    static newID() {\n        return `auto${Element.ID++}`;\n    }\n    static setTextMeasurementCanvas(canvas) {\n        Element.txtCanvas = canvas;\n    }\n    static getTextMeasurementCanvas() {\n        let txtCanvas = Element.txtCanvas;\n        if (!txtCanvas) {\n            if (typeof document !== 'undefined') {\n                txtCanvas = document.createElement('canvas');\n            }\n            else if (typeof OffscreenCanvas !== 'undefined') {\n                txtCanvas = new OffscreenCanvas(300, 150);\n            }\n            Element.txtCanvas = txtCanvas;\n        }\n        return txtCanvas;\n    }\n    constructor(category) {\n        var _a;\n        this.children = [];\n        this.style = {};\n        this._text = '';\n        this.metricsValid = false;\n        this._textMetrics = {\n            fontBoundingBoxAscent: 0,\n            fontBoundingBoxDescent: 0,\n            actualBoundingBoxAscent: 0,\n            actualBoundingBoxDescent: 0,\n            actualBoundingBoxLeft: 0,\n            actualBoundingBoxRight: 0,\n            width: 0,\n            alphabeticBaseline: 0,\n            emHeightAscent: 0,\n            emHeightDescent: 0,\n            hangingBaseline: 0,\n            ideographicBaseline: 0,\n        };\n        this._height = 0;\n        this._width = 0;\n        this.xShift = 0;\n        this.yShift = 0;\n        this.x = 0;\n        this.y = 0;\n        this.attrs = {\n            id: Element.newID(),\n            type: category !== null && category !== void 0 ? category : this.constructor.CATEGORY,\n            class: '',\n        };\n        this.rendered = false;\n        this._fontInfo = Metrics.getFontInfo(this.attrs.type);\n        this.style = Metrics.getStyle(this.attrs.type);\n        this.fontScale = Metrics.get(`${this.attrs.type}.fontScale`);\n        (_a = Registry.getDefaultRegistry()) === null || _a === void 0 ? void 0 : _a.register(this);\n    }\n    addChild(child) {\n        if (child.parent)\n            throw new RuntimeError('Element', 'Parent already defined');\n        child.parent = this;\n        this.children.push(child);\n        return this;\n    }\n    getCategory() {\n        return this.attrs.type;\n    }\n    setStyle(style) {\n        this.style = style;\n        return this;\n    }\n    setGroupStyle(style) {\n        this.style = style;\n        this.children.forEach((child) => child.setGroupStyle(style));\n        return this;\n    }\n    getStyle() {\n        return this.style;\n    }\n    applyStyle(context = this.context, style = this.getStyle()) {\n        if (!context)\n            return this;\n        if (style.shadowColor)\n            context.setShadowColor(style.shadowColor);\n        if (style.shadowBlur)\n            context.setShadowBlur(style.shadowBlur);\n        if (style.fillStyle)\n            context.setFillStyle(style.fillStyle);\n        if (style.strokeStyle)\n            context.setStrokeStyle(style.strokeStyle);\n        if (style.lineWidth)\n            context.setLineWidth(style.lineWidth);\n        if (style.lineDash)\n            context.setLineDash(style.lineDash.split(' ').map(Number));\n        return this;\n    }\n    drawWithStyle() {\n        const ctx = this.checkContext();\n        ctx.save();\n        this.applyStyle(ctx);\n        this.draw();\n        ctx.restore();\n        return this;\n    }\n    draw() {\n        throw new RuntimeError('Element', 'Draw not defined');\n    }\n    hasClass(className) {\n        var _a;\n        if (!this.attrs.class)\n            return false;\n        return ((_a = this.attrs.class) === null || _a === void 0 ? void 0 : _a.split(' ').indexOf(className)) !== -1;\n    }\n    addClass(className) {\n        var _a;\n        if (this.hasClass(className))\n            return this;\n        if (!this.attrs.class)\n            this.attrs.class = `${className}`;\n        else\n            this.attrs.class = `${this.attrs.class} ${className}`;\n        (_a = this.registry) === null || _a === void 0 ? void 0 : _a.onUpdate({\n            id: this.attrs.id,\n            name: 'class',\n            value: className,\n            oldValue: undefined,\n        });\n        return this;\n    }\n    removeClass(className) {\n        var _a, _b;\n        if (!this.hasClass(className))\n            return this;\n        const arr = (_a = this.attrs.class) === null || _a === void 0 ? void 0 : _a.split(' ');\n        if (arr) {\n            arr.splice(arr.indexOf(className));\n            this.attrs.class = arr.join(' ');\n        }\n        (_b = this.registry) === null || _b === void 0 ? void 0 : _b.onUpdate({\n            id: this.attrs.id,\n            name: 'class',\n            value: undefined,\n            oldValue: className,\n        });\n        return this;\n    }\n    onRegister(registry) {\n        this.registry = registry;\n        return this;\n    }\n    isRendered() {\n        return this.rendered;\n    }\n    setRendered(rendered = true) {\n        this.rendered = rendered;\n        return this;\n    }\n    getAttributes() {\n        return this.attrs;\n    }\n    getAttribute(name) {\n        return this.attrs[name];\n    }\n    getSVGElement(suffix = '') {\n        const id = prefix(this.attrs.id + suffix);\n        const element = document.getElementById(id);\n        if (element)\n            return element;\n    }\n    setAttribute(name, value) {\n        var _a;\n        const oldID = this.attrs.id;\n        const oldValue = this.attrs[name];\n        this.attrs[name] = value;\n        (_a = this.registry) === null || _a === void 0 ? void 0 : _a.onUpdate({ id: oldID, name, value, oldValue });\n        return this;\n    }\n    getBoundingBox() {\n        return new BoundingBox(this.x + this.xShift, this.y + this.yShift - this.textMetrics.actualBoundingBoxAscent, this.width, this.height);\n    }\n    getContext() {\n        return this.context;\n    }\n    setContext(context) {\n        this.context = context;\n        return this;\n    }\n    checkContext() {\n        return defined(this.context, 'NoContext', 'No rendering context attached to instance.');\n    }\n    set font(f) {\n        this.setFont(f);\n    }\n    get font() {\n        return Font.toCSSString(this._fontInfo);\n    }\n    setFont(font, size, weight, style) {\n        const defaultTextFont = Metrics.getFontInfo(this.attrs.type);\n        const fontIsObject = typeof font === 'object';\n        const fontIsString = typeof font === 'string';\n        const sizeWeightStyleAreUndefined = size === undefined && weight === undefined && style === undefined;\n        this.metricsValid = false;\n        if (fontIsObject) {\n            this._fontInfo = Object.assign(Object.assign({}, defaultTextFont), font);\n        }\n        else if (fontIsString && sizeWeightStyleAreUndefined) {\n            this._fontInfo = Font.fromCSSString(font);\n        }\n        else {\n            this._fontInfo = Font.validate(font !== null && font !== void 0 ? font : defaultTextFont.family, size !== null && size !== void 0 ? size : defaultTextFont.size, weight !== null && weight !== void 0 ? weight : defaultTextFont.weight, style !== null && style !== void 0 ? style : defaultTextFont.style);\n        }\n        return this;\n    }\n    getFont() {\n        return Font.toCSSString(this._fontInfo);\n    }\n    get fontInfo() {\n        return this._fontInfo;\n    }\n    set fontInfo(fontInfo) {\n        this.setFont(fontInfo);\n    }\n    setFontSize(size) {\n        const fontInfo = this.fontInfo;\n        this.setFont(fontInfo.family, size, fontInfo.weight, fontInfo.style);\n        return this;\n    }\n    getFontSize() {\n        return this.fontSize;\n    }\n    getFontScale() {\n        return this.fontScale;\n    }\n    set fontSize(size) {\n        this.setFontSize(size);\n    }\n    get fontSize() {\n        let size = this.fontInfo.size;\n        if (typeof size === 'number') {\n            size = `${size}pt`;\n        }\n        return size;\n    }\n    get fontSizeInPoints() {\n        return Font.convertSizeToPointValue(this.fontSize);\n    }\n    get fontSizeInPixels() {\n        return Font.convertSizeToPixelValue(this.fontSize);\n    }\n    get fontStyle() {\n        return this.fontInfo.style;\n    }\n    set fontStyle(style) {\n        const fontInfo = this.fontInfo;\n        this.setFont(fontInfo.family, fontInfo.size, fontInfo.weight, style);\n    }\n    get fontWeight() {\n        return this.fontInfo.weight + '';\n    }\n    set fontWeight(weight) {\n        const fontInfo = this.fontInfo;\n        this.setFont(fontInfo.family, fontInfo.size, weight, fontInfo.style);\n    }\n    getWidth() {\n        return this.width;\n    }\n    get width() {\n        if (!this.metricsValid)\n            this.measureText();\n        return this._width;\n    }\n    setWidth(width) {\n        this.width = width;\n        return this;\n    }\n    set width(width) {\n        if (!this.metricsValid)\n            this.measureText();\n        this._width = width;\n    }\n    setX(x) {\n        this.x = x;\n        return this;\n    }\n    getX() {\n        return this.x;\n    }\n    getY() {\n        return this.y;\n    }\n    setY(y) {\n        this.y = y;\n        return this;\n    }\n    setYShift(yShift) {\n        this.yShift = yShift;\n        return this;\n    }\n    getYShift() {\n        return this.yShift;\n    }\n    setXShift(xShift) {\n        this.xShift = xShift;\n        return this;\n    }\n    getXShift() {\n        return this.xShift;\n    }\n    setText(text) {\n        this.text = text;\n        return this;\n    }\n    set text(text) {\n        this.metricsValid = false;\n        this._text = text;\n    }\n    getText() {\n        return this._text;\n    }\n    get text() {\n        return this._text;\n    }\n    renderText(ctx, xPos, yPos) {\n        ctx.setFont(this._fontInfo);\n        ctx.fillText(this._text, xPos + this.x + this.xShift, yPos + this.y + this.yShift);\n        this.children.forEach((child) => {\n            ctx.setFont(child.fontInfo);\n            ctx.fillText(child.text, xPos + child.x + child.xShift, yPos + child.y + child.yShift);\n        });\n    }\n    measureText() {\n        var _a;\n        const context = (_a = Element.getTextMeasurementCanvas()) === null || _a === void 0 ? void 0 : _a.getContext('2d');\n        if (!context) {\n            console.warn('Element: No context for txtCanvas. Returning empty text metrics.');\n            return this._textMetrics;\n        }\n        context.font = Font.toCSSString(Font.validate(this.fontInfo));\n        this._textMetrics = context.measureText(this.text);\n        this._height = this._textMetrics.actualBoundingBoxAscent + this._textMetrics.actualBoundingBoxDescent;\n        this._width = this._textMetrics.width;\n        this.metricsValid = true;\n        return this._textMetrics;\n    }\n    static measureWidth(text, key = '') {\n        var _a;\n        const context = (_a = Element.getTextMeasurementCanvas()) === null || _a === void 0 ? void 0 : _a.getContext('2d');\n        if (!context) {\n            console.warn('Element: No context for txtCanvas. Returning empty text metrics.');\n            return 0;\n        }\n        context.font = Font.toCSSString(Metrics.getFontInfo(key));\n        return context.measureText(text).width;\n    }\n    getTextMetrics() {\n        return this.textMetrics;\n    }\n    get textMetrics() {\n        if (!this.metricsValid)\n            this.measureText();\n        return this._textMetrics;\n    }\n    getHeight() {\n        return this.height;\n    }\n    get height() {\n        if (!this.metricsValid)\n            this.measureText();\n        return this._height;\n    }\n    set height(height) {\n        if (!this.metricsValid)\n            this.measureText();\n        this._height = height;\n    }\n    setOriginX(x) {\n        const bbox = this.getBoundingBox();\n        const originX = Math.abs((bbox.getX() - this.xShift) / bbox.getW());\n        const xShift = (x - originX) * bbox.getW();\n        this.xShift = -xShift;\n    }\n    setOriginY(y) {\n        const bbox = this.getBoundingBox();\n        const originY = Math.abs((bbox.getY() - this.yShift) / bbox.getH());\n        const yShift = (y - originY) * bbox.getH();\n        this.yShift = -yShift;\n    }\n    setOrigin(x, y) {\n        this.setOriginX(x);\n        this.setOriginY(y);\n    }\n}\nElement.ID = 1000;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,IAAI,QAAQ,WAAW;AAChC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,OAAO,EAAEC,MAAM,EAAEC,YAAY,QAAQ,WAAW;AACzD,OAAO,MAAMC,OAAO,CAAC;EACjB,WAAWC,QAAQA,CAAA,EAAG;IAClB,OAAO,SAAS;EACpB;EACA,OAAOC,KAAKA,CAAA,EAAG;IACX,OAAO,OAAOF,OAAO,CAACG,EAAE,EAAE,EAAE;EAChC;EACA,OAAOC,wBAAwBA,CAACC,MAAM,EAAE;IACpCL,OAAO,CAACM,SAAS,GAAGD,MAAM;EAC9B;EACA,OAAOE,wBAAwBA,CAAA,EAAG;IAC9B,IAAID,SAAS,GAAGN,OAAO,CAACM,SAAS;IACjC,IAAI,CAACA,SAAS,EAAE;MACZ,IAAI,OAAOE,QAAQ,KAAK,WAAW,EAAE;QACjCF,SAAS,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAChD,CAAC,MACI,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;QAC7CJ,SAAS,GAAG,IAAII,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC;MAC7C;MACAV,OAAO,CAACM,SAAS,GAAGA,SAAS;IACjC;IACA,OAAOA,SAAS;EACpB;EACAK,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAIC,EAAE;IACN,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,YAAY,GAAG;MAChBC,qBAAqB,EAAE,CAAC;MACxBC,sBAAsB,EAAE,CAAC;MACzBC,uBAAuB,EAAE,CAAC;MAC1BC,wBAAwB,EAAE,CAAC;MAC3BC,qBAAqB,EAAE,CAAC;MACxBC,sBAAsB,EAAE,CAAC;MACzBC,KAAK,EAAE,CAAC;MACRC,kBAAkB,EAAE,CAAC;MACrBC,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,eAAe,EAAE,CAAC;MAClBC,mBAAmB,EAAE;IACzB,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,KAAK,GAAG;MACTC,EAAE,EAAEtC,OAAO,CAACE,KAAK,CAAC,CAAC;MACnBqC,IAAI,EAAE3B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,IAAI,CAACD,WAAW,CAACV,QAAQ;MACrFuC,KAAK,EAAE;IACX,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG/C,OAAO,CAACgD,WAAW,CAAC,IAAI,CAACN,KAAK,CAACE,IAAI,CAAC;IACrD,IAAI,CAACxB,KAAK,GAAGpB,OAAO,CAACiD,QAAQ,CAAC,IAAI,CAACP,KAAK,CAACE,IAAI,CAAC;IAC9C,IAAI,CAACM,SAAS,GAAGlD,OAAO,CAACmD,GAAG,CAAC,GAAG,IAAI,CAACT,KAAK,CAACE,IAAI,YAAY,CAAC;IAC5D,CAAC1B,EAAE,GAAGjB,QAAQ,CAACmD,kBAAkB,CAAC,CAAC,MAAM,IAAI,IAAIlC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmC,QAAQ,CAAC,IAAI,CAAC;EAC/F;EACAC,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAIA,KAAK,CAACC,MAAM,EACZ,MAAM,IAAIpD,YAAY,CAAC,SAAS,EAAE,wBAAwB,CAAC;IAC/DmD,KAAK,CAACC,MAAM,GAAG,IAAI;IACnB,IAAI,CAACrC,QAAQ,CAACsC,IAAI,CAACF,KAAK,CAAC;IACzB,OAAO,IAAI;EACf;EACAG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,KAAK,CAACE,IAAI;EAC1B;EACAe,QAAQA,CAACvC,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACAwC,aAAaA,CAACxC,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,QAAQ,CAAC0C,OAAO,CAAEN,KAAK,IAAKA,KAAK,CAACK,aAAa,CAACxC,KAAK,CAAC,CAAC;IAC5D,OAAO,IAAI;EACf;EACA6B,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC7B,KAAK;EACrB;EACA0C,UAAUA,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE3C,KAAK,GAAG,IAAI,CAAC6B,QAAQ,CAAC,CAAC,EAAE;IACxD,IAAI,CAACc,OAAO,EACR,OAAO,IAAI;IACf,IAAI3C,KAAK,CAAC4C,WAAW,EACjBD,OAAO,CAACE,cAAc,CAAC7C,KAAK,CAAC4C,WAAW,CAAC;IAC7C,IAAI5C,KAAK,CAAC8C,UAAU,EAChBH,OAAO,CAACI,aAAa,CAAC/C,KAAK,CAAC8C,UAAU,CAAC;IAC3C,IAAI9C,KAAK,CAACgD,SAAS,EACfL,OAAO,CAACM,YAAY,CAACjD,KAAK,CAACgD,SAAS,CAAC;IACzC,IAAIhD,KAAK,CAACkD,WAAW,EACjBP,OAAO,CAACQ,cAAc,CAACnD,KAAK,CAACkD,WAAW,CAAC;IAC7C,IAAIlD,KAAK,CAACoD,SAAS,EACfT,OAAO,CAACU,YAAY,CAACrD,KAAK,CAACoD,SAAS,CAAC;IACzC,IAAIpD,KAAK,CAACsD,QAAQ,EACdX,OAAO,CAACY,WAAW,CAACvD,KAAK,CAACsD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAAC;IAC9D,OAAO,IAAI;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/BD,GAAG,CAACE,IAAI,CAAC,CAAC;IACV,IAAI,CAACpB,UAAU,CAACkB,GAAG,CAAC;IACpB,IAAI,CAACG,IAAI,CAAC,CAAC;IACXH,GAAG,CAACI,OAAO,CAAC,CAAC;IACb,OAAO,IAAI;EACf;EACAD,IAAIA,CAAA,EAAG;IACH,MAAM,IAAI/E,YAAY,CAAC,SAAS,EAAE,kBAAkB,CAAC;EACzD;EACAiF,QAAQA,CAACC,SAAS,EAAE;IAChB,IAAIpE,EAAE;IACN,IAAI,CAAC,IAAI,CAACwB,KAAK,CAACG,KAAK,EACjB,OAAO,KAAK;IAChB,OAAO,CAAC,CAAC3B,EAAE,GAAG,IAAI,CAACwB,KAAK,CAACG,KAAK,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0D,KAAK,CAAC,GAAG,CAAC,CAACW,OAAO,CAACD,SAAS,CAAC,MAAM,CAAC,CAAC;EACjH;EACAE,QAAQA,CAACF,SAAS,EAAE;IAChB,IAAIpE,EAAE;IACN,IAAI,IAAI,CAACmE,QAAQ,CAACC,SAAS,CAAC,EACxB,OAAO,IAAI;IACf,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAACG,KAAK,EACjB,IAAI,CAACH,KAAK,CAACG,KAAK,GAAG,GAAGyC,SAAS,EAAE,CAAC,KAElC,IAAI,CAAC5C,KAAK,CAACG,KAAK,GAAG,GAAG,IAAI,CAACH,KAAK,CAACG,KAAK,IAAIyC,SAAS,EAAE;IACzD,CAACpE,EAAE,GAAG,IAAI,CAACuE,QAAQ,MAAM,IAAI,IAAIvE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwE,QAAQ,CAAC;MAClE/C,EAAE,EAAE,IAAI,CAACD,KAAK,CAACC,EAAE;MACjBgD,IAAI,EAAE,OAAO;MACbC,KAAK,EAAEN,SAAS;MAChBO,QAAQ,EAAEC;IACd,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAC,WAAWA,CAACT,SAAS,EAAE;IACnB,IAAIpE,EAAE,EAAE8E,EAAE;IACV,IAAI,CAAC,IAAI,CAACX,QAAQ,CAACC,SAAS,CAAC,EACzB,OAAO,IAAI;IACf,MAAMW,GAAG,GAAG,CAAC/E,EAAE,GAAG,IAAI,CAACwB,KAAK,CAACG,KAAK,MAAM,IAAI,IAAI3B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0D,KAAK,CAAC,GAAG,CAAC;IACtF,IAAIqB,GAAG,EAAE;MACLA,GAAG,CAACC,MAAM,CAACD,GAAG,CAACV,OAAO,CAACD,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC5C,KAAK,CAACG,KAAK,GAAGoD,GAAG,CAACE,IAAI,CAAC,GAAG,CAAC;IACpC;IACA,CAACH,EAAE,GAAG,IAAI,CAACP,QAAQ,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACN,QAAQ,CAAC;MAClE/C,EAAE,EAAE,IAAI,CAACD,KAAK,CAACC,EAAE;MACjBgD,IAAI,EAAE,OAAO;MACbC,KAAK,EAAEE,SAAS;MAChBD,QAAQ,EAAEP;IACd,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAc,UAAUA,CAACX,QAAQ,EAAE;IACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;EACf;EACAY,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACvD,QAAQ;EACxB;EACAwD,WAAWA,CAACxD,QAAQ,GAAG,IAAI,EAAE;IACzB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;EACf;EACAyD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7D,KAAK;EACrB;EACA8D,YAAYA,CAACb,IAAI,EAAE;IACf,OAAO,IAAI,CAACjD,KAAK,CAACiD,IAAI,CAAC;EAC3B;EACAc,aAAaA,CAACC,MAAM,GAAG,EAAE,EAAE;IACvB,MAAM/D,EAAE,GAAGxC,MAAM,CAAC,IAAI,CAACuC,KAAK,CAACC,EAAE,GAAG+D,MAAM,CAAC;IACzC,MAAMC,OAAO,GAAG9F,QAAQ,CAAC+F,cAAc,CAACjE,EAAE,CAAC;IAC3C,IAAIgE,OAAO,EACP,OAAOA,OAAO;EACtB;EACAE,YAAYA,CAAClB,IAAI,EAAEC,KAAK,EAAE;IACtB,IAAI1E,EAAE;IACN,MAAM4F,KAAK,GAAG,IAAI,CAACpE,KAAK,CAACC,EAAE;IAC3B,MAAMkD,QAAQ,GAAG,IAAI,CAACnD,KAAK,CAACiD,IAAI,CAAC;IACjC,IAAI,CAACjD,KAAK,CAACiD,IAAI,CAAC,GAAGC,KAAK;IACxB,CAAC1E,EAAE,GAAG,IAAI,CAACuE,QAAQ,MAAM,IAAI,IAAIvE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwE,QAAQ,CAAC;MAAE/C,EAAE,EAAEmE,KAAK;MAAEnB,IAAI;MAAEC,KAAK;MAAEC;IAAS,CAAC,CAAC;IAC3G,OAAO,IAAI;EACf;EACAkB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAIjH,WAAW,CAAC,IAAI,CAAC0C,CAAC,GAAG,IAAI,CAACF,MAAM,EAAE,IAAI,CAACG,CAAC,GAAG,IAAI,CAACF,MAAM,GAAG,IAAI,CAACyE,WAAW,CAACtF,uBAAuB,EAAE,IAAI,CAACI,KAAK,EAAE,IAAI,CAACmF,MAAM,CAAC;EAC1I;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnD,OAAO;EACvB;EACAoD,UAAUA,CAACpD,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,IAAI;EACf;EACAkB,YAAYA,CAAA,EAAG;IACX,OAAO/E,OAAO,CAAC,IAAI,CAAC6D,OAAO,EAAE,WAAW,EAAE,4CAA4C,CAAC;EAC3F;EACA,IAAIqD,IAAIA,CAACC,CAAC,EAAE;IACR,IAAI,CAACC,OAAO,CAACD,CAAC,CAAC;EACnB;EACA,IAAID,IAAIA,CAAA,EAAG;IACP,OAAOrH,IAAI,CAACwH,WAAW,CAAC,IAAI,CAACxE,SAAS,CAAC;EAC3C;EACAuE,OAAOA,CAACF,IAAI,EAAEI,IAAI,EAAEC,MAAM,EAAErG,KAAK,EAAE;IAC/B,MAAMsG,eAAe,GAAG1H,OAAO,CAACgD,WAAW,CAAC,IAAI,CAACN,KAAK,CAACE,IAAI,CAAC;IAC5D,MAAM+E,YAAY,GAAG,OAAOP,IAAI,KAAK,QAAQ;IAC7C,MAAMQ,YAAY,GAAG,OAAOR,IAAI,KAAK,QAAQ;IAC7C,MAAMS,2BAA2B,GAAGL,IAAI,KAAK1B,SAAS,IAAI2B,MAAM,KAAK3B,SAAS,IAAI1E,KAAK,KAAK0E,SAAS;IACrG,IAAI,CAACxE,YAAY,GAAG,KAAK;IACzB,IAAIqG,YAAY,EAAE;MACd,IAAI,CAAC5E,SAAS,GAAG+E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,eAAe,CAAC,EAAEN,IAAI,CAAC;IAC5E,CAAC,MACI,IAAIQ,YAAY,IAAIC,2BAA2B,EAAE;MAClD,IAAI,CAAC9E,SAAS,GAAGhD,IAAI,CAACiI,aAAa,CAACZ,IAAI,CAAC;IAC7C,CAAC,MACI;MACD,IAAI,CAACrE,SAAS,GAAGhD,IAAI,CAACkI,QAAQ,CAACb,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGM,eAAe,CAACQ,MAAM,EAAEV,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGE,eAAe,CAACF,IAAI,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGC,eAAe,CAACD,MAAM,EAAErG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGsG,eAAe,CAACtG,KAAK,CAAC;IAChT;IACA,OAAO,IAAI;EACf;EACA+G,OAAOA,CAAA,EAAG;IACN,OAAOpI,IAAI,CAACwH,WAAW,CAAC,IAAI,CAACxE,SAAS,CAAC;EAC3C;EACA,IAAIqF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrF,SAAS;EACzB;EACA,IAAIqF,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACd,OAAO,CAACc,QAAQ,CAAC;EAC1B;EACAC,WAAWA,CAACb,IAAI,EAAE;IACd,MAAMY,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAACd,OAAO,CAACc,QAAQ,CAACF,MAAM,EAAEV,IAAI,EAAEY,QAAQ,CAACX,MAAM,EAAEW,QAAQ,CAAChH,KAAK,CAAC;IACpE,OAAO,IAAI;EACf;EACAkH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtF,SAAS;EACzB;EACA,IAAIqF,QAAQA,CAACf,IAAI,EAAE;IACf,IAAI,CAACa,WAAW,CAACb,IAAI,CAAC;EAC1B;EACA,IAAIe,QAAQA,CAAA,EAAG;IACX,IAAIf,IAAI,GAAG,IAAI,CAACY,QAAQ,CAACZ,IAAI;IAC7B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC1BA,IAAI,GAAG,GAAGA,IAAI,IAAI;IACtB;IACA,OAAOA,IAAI;EACf;EACA,IAAIiB,gBAAgBA,CAAA,EAAG;IACnB,OAAO1I,IAAI,CAAC2I,uBAAuB,CAAC,IAAI,CAACH,QAAQ,CAAC;EACtD;EACA,IAAII,gBAAgBA,CAAA,EAAG;IACnB,OAAO5I,IAAI,CAAC6I,uBAAuB,CAAC,IAAI,CAACL,QAAQ,CAAC;EACtD;EACA,IAAIM,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACT,QAAQ,CAAChH,KAAK;EAC9B;EACA,IAAIyH,SAASA,CAACzH,KAAK,EAAE;IACjB,MAAMgH,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAACd,OAAO,CAACc,QAAQ,CAACF,MAAM,EAAEE,QAAQ,CAACZ,IAAI,EAAEY,QAAQ,CAACX,MAAM,EAAErG,KAAK,CAAC;EACxE;EACA,IAAI0H,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACV,QAAQ,CAACX,MAAM,GAAG,EAAE;EACpC;EACA,IAAIqB,UAAUA,CAACrB,MAAM,EAAE;IACnB,MAAMW,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI,CAACd,OAAO,CAACc,QAAQ,CAACF,MAAM,EAAEE,QAAQ,CAACZ,IAAI,EAAEC,MAAM,EAAEW,QAAQ,CAAChH,KAAK,CAAC;EACxE;EACA2H,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjH,KAAK;EACrB;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACR,YAAY,EAClB,IAAI,CAAC0H,WAAW,CAAC,CAAC;IACtB,OAAO,IAAI,CAAC3G,MAAM;EACtB;EACA4G,QAAQA,CAACnH,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,OAAO,IAAI;EACf;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAACR,YAAY,EAClB,IAAI,CAAC0H,WAAW,CAAC,CAAC;IACtB,IAAI,CAAC3G,MAAM,GAAGP,KAAK;EACvB;EACAoH,IAAIA,CAAC1G,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACA2G,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAC3G,CAAC;EACjB;EACA4G,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAAC3G,CAAC;EACjB;EACA4G,IAAIA,CAAC5G,CAAC,EAAE;IACJ,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,OAAO,IAAI;EACf;EACA6G,SAASA,CAAC/G,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAgH,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChH,MAAM;EACtB;EACAiH,SAASA,CAAClH,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACf;EACAmH,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnH,MAAM;EACtB;EACAoH,OAAOA,CAACC,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACA,IAAIA,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACrI,YAAY,GAAG,KAAK;IACzB,IAAI,CAACD,KAAK,GAAGsI,IAAI;EACrB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACvI,KAAK;EACrB;EACA,IAAIsI,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACtI,KAAK;EACrB;EACAwI,UAAUA,CAAC7E,GAAG,EAAE8E,IAAI,EAAEC,IAAI,EAAE;IACxB/E,GAAG,CAACsC,OAAO,CAAC,IAAI,CAACvE,SAAS,CAAC;IAC3BiC,GAAG,CAACgF,QAAQ,CAAC,IAAI,CAAC3I,KAAK,EAAEyI,IAAI,GAAG,IAAI,CAACtH,CAAC,GAAG,IAAI,CAACF,MAAM,EAAEyH,IAAI,GAAG,IAAI,CAACtH,CAAC,GAAG,IAAI,CAACF,MAAM,CAAC;IAClF,IAAI,CAACpB,QAAQ,CAAC0C,OAAO,CAAEN,KAAK,IAAK;MAC7ByB,GAAG,CAACsC,OAAO,CAAC/D,KAAK,CAAC6E,QAAQ,CAAC;MAC3BpD,GAAG,CAACgF,QAAQ,CAACzG,KAAK,CAACoG,IAAI,EAAEG,IAAI,GAAGvG,KAAK,CAACf,CAAC,GAAGe,KAAK,CAACjB,MAAM,EAAEyH,IAAI,GAAGxG,KAAK,CAACd,CAAC,GAAGc,KAAK,CAAChB,MAAM,CAAC;IAC1F,CAAC,CAAC;EACN;EACAyG,WAAWA,CAAA,EAAG;IACV,IAAI9H,EAAE;IACN,MAAM6C,OAAO,GAAG,CAAC7C,EAAE,GAAGb,OAAO,CAACO,wBAAwB,CAAC,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgG,UAAU,CAAC,IAAI,CAAC;IAClH,IAAI,CAACnD,OAAO,EAAE;MACVkG,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;MAChF,OAAO,IAAI,CAAC3I,YAAY;IAC5B;IACAwC,OAAO,CAACqD,IAAI,GAAGrH,IAAI,CAACwH,WAAW,CAACxH,IAAI,CAACkI,QAAQ,CAAC,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC7D,IAAI,CAAC7G,YAAY,GAAGwC,OAAO,CAACiF,WAAW,CAAC,IAAI,CAACW,IAAI,CAAC;IAClD,IAAI,CAACvH,OAAO,GAAG,IAAI,CAACb,YAAY,CAACG,uBAAuB,GAAG,IAAI,CAACH,YAAY,CAACI,wBAAwB;IACrG,IAAI,CAACU,MAAM,GAAG,IAAI,CAACd,YAAY,CAACO,KAAK;IACrC,IAAI,CAACR,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,OAAO4I,YAAYA,CAACR,IAAI,EAAES,GAAG,GAAG,EAAE,EAAE;IAChC,IAAIlJ,EAAE;IACN,MAAM6C,OAAO,GAAG,CAAC7C,EAAE,GAAGb,OAAO,CAACO,wBAAwB,CAAC,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgG,UAAU,CAAC,IAAI,CAAC;IAClH,IAAI,CAACnD,OAAO,EAAE;MACVkG,OAAO,CAACC,IAAI,CAAC,kEAAkE,CAAC;MAChF,OAAO,CAAC;IACZ;IACAnG,OAAO,CAACqD,IAAI,GAAGrH,IAAI,CAACwH,WAAW,CAACvH,OAAO,CAACgD,WAAW,CAACoH,GAAG,CAAC,CAAC;IACzD,OAAOrG,OAAO,CAACiF,WAAW,CAACW,IAAI,CAAC,CAAC7H,KAAK;EAC1C;EACAuI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrD,WAAW;EAC3B;EACA,IAAIA,WAAWA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAAC1F,YAAY,EAClB,IAAI,CAAC0H,WAAW,CAAC,CAAC;IACtB,OAAO,IAAI,CAACzH,YAAY;EAC5B;EACA+I,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACrD,MAAM;EACtB;EACA,IAAIA,MAAMA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC3F,YAAY,EAClB,IAAI,CAAC0H,WAAW,CAAC,CAAC;IACtB,OAAO,IAAI,CAAC5G,OAAO;EACvB;EACA,IAAI6E,MAAMA,CAACA,MAAM,EAAE;IACf,IAAI,CAAC,IAAI,CAAC3F,YAAY,EAClB,IAAI,CAAC0H,WAAW,CAAC,CAAC;IACtB,IAAI,CAAC5G,OAAO,GAAG6E,MAAM;EACzB;EACAsD,UAAUA,CAAC/H,CAAC,EAAE;IACV,MAAMgI,IAAI,GAAG,IAAI,CAACzD,cAAc,CAAC,CAAC;IAClC,MAAM0D,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACH,IAAI,CAACrB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC7G,MAAM,IAAIkI,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC;IACnE,MAAMtI,MAAM,GAAG,CAACE,CAAC,GAAGiI,OAAO,IAAID,IAAI,CAACI,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACtI,MAAM,GAAG,CAACA,MAAM;EACzB;EACAuI,UAAUA,CAACpI,CAAC,EAAE;IACV,MAAM+H,IAAI,GAAG,IAAI,CAACzD,cAAc,CAAC,CAAC;IAClC,MAAM+D,OAAO,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAACH,IAAI,CAACpB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC7G,MAAM,IAAIiI,IAAI,CAACO,IAAI,CAAC,CAAC,CAAC;IACnE,MAAMxI,MAAM,GAAG,CAACE,CAAC,GAAGqI,OAAO,IAAIN,IAAI,CAACO,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACxI,MAAM,GAAG,CAACA,MAAM;EACzB;EACAyI,SAASA,CAACxI,CAAC,EAAEC,CAAC,EAAE;IACZ,IAAI,CAAC8H,UAAU,CAAC/H,CAAC,CAAC;IAClB,IAAI,CAACqI,UAAU,CAACpI,CAAC,CAAC;EACtB;AACJ;AACApC,OAAO,CAACG,EAAE,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}