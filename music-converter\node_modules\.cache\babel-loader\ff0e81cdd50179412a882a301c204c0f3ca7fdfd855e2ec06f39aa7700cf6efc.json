{"ast": null, "code": "import { Note } from './note.js';\nimport { TimeSignature } from './timesignature.js';\nexport class TimeSigNote extends Note {\n  static get CATEGORY() {\n    return \"TimeSigNote\";\n  }\n  constructor(timeSpec, customPadding) {\n    super({\n      duration: 'b'\n    });\n    this.timeSig = new TimeSignature(timeSpec, customPadding);\n    this.setWidth(this.timeSig.getWidth());\n    this.ignoreTicks = true;\n  }\n  addToModifierContext(mc) {\n    return this;\n  }\n  preFormat() {\n    this.preFormatted = true;\n    return this;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = this.checkContext();\n    this.setRendered();\n    ctx.openGroup('timesignote', this.getAttribute('id'));\n    this.timeSig.drawAt(ctx, stave, this.getAbsoluteX());\n    ctx.closeGroup();\n  }\n}", "map": {"version": 3, "names": ["Note", "TimeSignature", "TimeSigNote", "CATEGORY", "constructor", "timeSpec", "customPadding", "duration", "timeSig", "<PERSON><PERSON><PERSON><PERSON>", "getWidth", "ignoreTicks", "addToModifierContext", "mc", "preFormat", "preFormatted", "draw", "stave", "checkStave", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "drawAt", "getAbsoluteX", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/timesignote.js"], "sourcesContent": ["import { Note } from './note.js';\nimport { TimeSignature } from './timesignature.js';\nexport class TimeSigNote extends Note {\n    static get CATEGORY() {\n        return \"TimeSigNote\";\n    }\n    constructor(timeSpec, customPadding) {\n        super({ duration: 'b' });\n        this.timeSig = new TimeSignature(timeSpec, customPadding);\n        this.setWidth(this.timeSig.getWidth());\n        this.ignoreTicks = true;\n    }\n    addToModifierContext(mc) {\n        return this;\n    }\n    preFormat() {\n        this.preFormatted = true;\n        return this;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = this.checkContext();\n        this.setRendered();\n        ctx.openGroup('timesignote', this.getAttribute('id'));\n        this.timeSig.drawAt(ctx, stave, this.getAbsoluteX());\n        ctx.closeGroup();\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,WAAW;AAChC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,MAAMC,WAAW,SAASF,IAAI,CAAC;EAClC,WAAWG,QAAQA,CAAA,EAAG;IAClB,OAAO,aAAa;EACxB;EACAC,WAAWA,CAACC,QAAQ,EAAEC,aAAa,EAAE;IACjC,KAAK,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC;IACxB,IAAI,CAACC,OAAO,GAAG,IAAIP,aAAa,CAACI,QAAQ,EAAEC,aAAa,CAAC;IACzD,IAAI,CAACG,QAAQ,CAAC,IAAI,CAACD,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IACtC,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,oBAAoBA,CAACC,EAAE,EAAE;IACrB,OAAO,IAAI;EACf;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,aAAa,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACrD,IAAI,CAACf,OAAO,CAACgB,MAAM,CAACL,GAAG,EAAEF,KAAK,EAAE,IAAI,CAACQ,YAAY,CAAC,CAAC,CAAC;IACpDN,GAAG,CAACO,UAAU,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}