{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\music\\\\music-converter\\\\src\\\\components\\\\Converter.jsx\";\nimport React from 'react';\nimport VexFlowComponent from './VexFlowComponent';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Converter() {\n  // NOTE CONSTANTS\n\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\n  return /*#__PURE__*/_jsxDEV(\"main\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"hehe poopoo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = Converter;\nexport default Converter;\nvar _c;\n$RefreshReg$(_c, \"Converter\");", "map": {"version": 3, "names": ["React", "VexFlowComponent", "jsxDEV", "_jsxDEV", "Converter", "NOTES", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/src/components/Converter.jsx"], "sourcesContent": ["import React from 'react';\r\nimport VexFlowComponent from './VexFlowComponent';\r\n\r\nfunction Converter() {\r\n\r\n  // NOTE CONSTANTS\r\n\r\n  const NOTES = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];\r\n\r\n  return (\r\n    <main>\r\n      {/* ----- INPUT AREA ----- */}\r\n      <div>\r\n        <section>\r\n          <h3>Input</h3>\r\n\r\n          <div>\r\n\r\n          </div>\r\n\r\n        </section>\r\n        \r\n      </div>\r\n\r\n      {/* ----- OUTPUT AREA ----- */}\r\n      <div>\r\n        <h2>hehe poopoo</h2>\r\n      </div>\r\n\r\n    </main>\r\n  )\r\n}\r\n\r\nexport default Converter;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,SAASA,CAAA,EAAG;EAEnB;;EAEA,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAEjD,oBACEF,OAAA;IAAAG,QAAA,gBAEEH,OAAA;MAAAG,QAAA,eACEH,OAAA;QAAAG,QAAA,gBACEH,OAAA;UAAAG,QAAA,EAAI;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEdP,OAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEP,CAAC,eAGNP,OAAA;MAAAG,QAAA,eACEH,OAAA;QAAAG,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEF,CAAC;AAEX;AAACC,EAAA,GA5BQP,SAAS;AA8BlB,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}