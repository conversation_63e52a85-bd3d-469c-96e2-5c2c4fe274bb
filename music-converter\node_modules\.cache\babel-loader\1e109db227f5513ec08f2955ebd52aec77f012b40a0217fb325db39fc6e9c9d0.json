{"ast": null, "code": "import { BoundingBox } from './boundingbox.js';\nimport { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Stave } from './stave.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nimport { defined } from './util.js';\nexport class KeySignature extends StaveModifier {\n  static get CATEGORY() {\n    return \"KeySignature\";\n  }\n  constructor(keySpec, cancelKeySpec, alterKeySpec) {\n    super();\n    this.accList = [];\n    this.setKeySig(keySpec, cancelKeySpec, alterKeySpec);\n    this.setPosition(StaveModifierPosition.BEGIN);\n    this.children = [];\n    this.paddingForced = false;\n  }\n  convertToGlyph(acc, nextAcc, stave) {\n    const code = Tables.accidentalCodes(acc.type);\n    const glyph = new Element(\"KeySignature\");\n    glyph.setText(code);\n    let extraWidth = 1;\n    glyph.setYShift(stave.getYForLine(acc.line));\n    if (this.children.length > 0) {\n      const prevGlyph = this.children[this.children.length - 1];\n      const isNatural = el => el.getText() === Glyphs.accidentalNatural;\n      const yShiftDiff = (el1, el2) => Math.abs(el2.getYShift() - el1.getYShift());\n      if ((isNatural(prevGlyph) || isNatural(glyph)) && yShiftDiff(prevGlyph, glyph) < 10) {\n        extraWidth = 2;\n      }\n      glyph.setXShift(prevGlyph.getXShift() + prevGlyph.getWidth() + extraWidth);\n    }\n    this.children.push(glyph);\n    this.width += glyph.getWidth() + extraWidth;\n  }\n  cancelKey(spec) {\n    this.formatted = false;\n    this.cancelKeySpec = spec;\n    return this;\n  }\n  convertToCancelAccList(spec) {\n    const cancelAccList = Tables.keySignature(spec);\n    const differentTypes = this.accList.length > 0 && cancelAccList.length > 0 && cancelAccList[0].type !== this.accList[0].type;\n    const naturals = differentTypes ? cancelAccList.length : cancelAccList.length - this.accList.length;\n    if (naturals < 1) return undefined;\n    const cancelled = [];\n    for (let i = 0; i < naturals; i++) {\n      let index = i;\n      if (!differentTypes) {\n        index = cancelAccList.length - naturals + i;\n      }\n      const acc = cancelAccList[index];\n      cancelled.push({\n        type: 'n',\n        line: acc.line\n      });\n    }\n    this.accList = cancelled.concat(this.accList);\n    return {\n      accList: cancelled,\n      type: cancelAccList[0].type\n    };\n  }\n  addToStave(stave) {\n    this.paddingForced = true;\n    stave.addModifier(this);\n    return this;\n  }\n  setStave(stave) {\n    this.formatted = false;\n    return super.setStave(stave);\n  }\n  getBoundingBox() {\n    if (!this.formatted) this.format();\n    return super.getBoundingBox();\n  }\n  calculateDimensions() {\n    let boundingBox;\n    if (this.children.length > 0) {\n      boundingBox = this.children[0].getBoundingBox();\n    } else {\n      boundingBox = new BoundingBox(this.x + this.xShift, this.y + this.yShift, 0, 0);\n    }\n    this.children.forEach(glyph => {\n      boundingBox.mergeWith(glyph.getBoundingBox());\n    });\n    this.width = boundingBox.getW();\n    this.height = boundingBox.getH();\n    this.y = boundingBox.getY();\n  }\n  convertAccLines(clef, type, accList = this.accList) {\n    let offset = 0.0;\n    let customLines;\n    switch (clef) {\n      case 'soprano':\n        if (type === '#') customLines = [2.5, 0.5, 2, 0, 1.5, -0.5, 1];else offset = -1;\n        break;\n      case 'mezzo-soprano':\n        if (type === 'b') customLines = [0, 2, 0.5, 2.5, 1, 3, 1.5];else offset = 1.5;\n        break;\n      case 'alto':\n        offset = 0.5;\n        break;\n      case 'tenor':\n        if (type === '#') customLines = [3, 1, 2.5, 0.5, 2, 0, 1.5];else offset = -0.5;\n        break;\n      case 'baritone-f':\n      case 'baritone-c':\n        if (type === 'b') customLines = [0.5, 2.5, 1, 3, 1.5, 3.5, 2];else offset = 2;\n        break;\n      case 'bass':\n      case 'french':\n        offset = 1;\n        break;\n      default:\n        break;\n    }\n    let i;\n    if (typeof customLines !== 'undefined') {\n      for (i = 0; i < accList.length; ++i) {\n        accList[i].line = customLines[i];\n      }\n    } else if (offset !== 0) {\n      for (i = 0; i < accList.length; ++i) {\n        accList[i].line += offset;\n      }\n    }\n  }\n  getPadding(index) {\n    if (!this.formatted) this.format();\n    return this.children.length === 0 || !this.paddingForced && index < 2 ? 0 : this.padding;\n  }\n  getWidth() {\n    if (!this.formatted) this.format();\n    return this.width;\n  }\n  setKeySig(keySpec, cancelKeySpec, alterKeySpec) {\n    this.formatted = false;\n    this.keySpec = keySpec;\n    this.cancelKeySpec = cancelKeySpec;\n    this.alterKeySpec = alterKeySpec;\n    return this;\n  }\n  alterKey(alterKeySpec) {\n    this.formatted = false;\n    this.alterKeySpec = alterKeySpec;\n    return this;\n  }\n  convertToAlterAccList(alterKeySpec) {\n    const max = Math.min(alterKeySpec.length, this.accList.length);\n    for (let i = 0; i < max; ++i) {\n      if (alterKeySpec[i]) {\n        this.accList[i].type = alterKeySpec[i];\n      }\n    }\n  }\n  format() {\n    let stave = this.getStave();\n    if (!stave) {\n      stave = new Stave(0, 0, 100);\n      this.setStave(stave);\n    }\n    this.width = 0;\n    this.children = [];\n    this.accList = Tables.keySignature(defined(this.keySpec));\n    const accList = this.accList;\n    const firstAccidentalType = accList.length > 0 ? accList[0].type : undefined;\n    let cancelAccList;\n    if (this.cancelKeySpec) {\n      cancelAccList = this.convertToCancelAccList(this.cancelKeySpec);\n    }\n    if (this.alterKeySpec) {\n      this.convertToAlterAccList(this.alterKeySpec);\n    }\n    if (this.accList.length > 0) {\n      const clef = (this.position === StaveModifierPosition.END ? stave.getEndClef() : stave.getClef()) || stave.getClef();\n      if (cancelAccList) {\n        this.convertAccLines(clef, cancelAccList.type, cancelAccList.accList);\n      }\n      this.convertAccLines(clef, firstAccidentalType, accList);\n      for (let i = 0; i < this.accList.length; ++i) {\n        this.convertToGlyph(this.accList[i], this.accList[i + 1], stave);\n      }\n    }\n    this.calculateDimensions();\n    this.formatted = true;\n  }\n  draw() {\n    const stave = this.checkStave();\n    const ctx = stave.checkContext();\n    if (!this.formatted) this.format();\n    this.setRendered();\n    ctx.openGroup('keysignature', this.getAttribute('id'));\n    for (let i = 0; i < this.children.length; i++) {\n      const glyph = this.children[i];\n      glyph.renderText(ctx, this.x, 0);\n    }\n    ctx.closeGroup();\n  }\n}", "map": {"version": 3, "names": ["BoundingBox", "Element", "Glyphs", "Stave", "StaveModifier", "StaveModifierPosition", "Tables", "defined", "KeySignature", "CATEGORY", "constructor", "keySpec", "cancelKeySpec", "alterKeySpec", "accList", "setKeySig", "setPosition", "BEGIN", "children", "paddingForced", "convertToGlyph", "acc", "nextAcc", "stave", "code", "accidentalCodes", "type", "glyph", "setText", "extraWidth", "setYShift", "getYForLine", "line", "length", "prevGlyph", "isNatural", "el", "getText", "accidentalNatural", "yShiftDiff", "el1", "el2", "Math", "abs", "getYShift", "setXShift", "getXShift", "getWidth", "push", "width", "cancel<PERSON>ey", "spec", "formatted", "convertToCancelAccList", "cancelAccList", "keySignature", "differentTypes", "naturals", "undefined", "cancelled", "i", "index", "concat", "addToStave", "addModifier", "setStave", "getBoundingBox", "format", "calculateDimensions", "boundingBox", "x", "xShift", "y", "yShift", "for<PERSON>ach", "mergeWith", "getW", "height", "getH", "getY", "convertAccLines", "clef", "offset", "customLines", "getPadding", "padding", "<PERSON><PERSON><PERSON>", "convertToAlterAccList", "max", "min", "getStave", "firstAccidentalType", "position", "END", "getEndClef", "getClef", "draw", "checkStave", "ctx", "checkContext", "setRendered", "openGroup", "getAttribute", "renderText", "closeGroup"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/keysignature.js"], "sourcesContent": ["import { BoundingBox } from './boundingbox.js';\nimport { Element } from './element.js';\nimport { Glyphs } from './glyphs.js';\nimport { Stave } from './stave.js';\nimport { StaveModifier, StaveModifierPosition } from './stavemodifier.js';\nimport { Tables } from './tables.js';\nimport { defined } from './util.js';\nexport class KeySignature extends StaveModifier {\n    static get CATEGORY() {\n        return \"KeySignature\";\n    }\n    constructor(keySpec, cancelKeySpec, alterKeySpec) {\n        super();\n        this.accList = [];\n        this.setKeySig(keySpec, cancelKeySpec, alterKeySpec);\n        this.setPosition(StaveModifierPosition.BEGIN);\n        this.children = [];\n        this.paddingForced = false;\n    }\n    convertToGlyph(acc, nextAcc, stave) {\n        const code = Tables.accidentalCodes(acc.type);\n        const glyph = new Element(\"KeySignature\");\n        glyph.setText(code);\n        let extraWidth = 1;\n        glyph.setYShift(stave.getYForLine(acc.line));\n        if (this.children.length > 0) {\n            const prevGlyph = this.children[this.children.length - 1];\n            const isNatural = (el) => el.getText() === Glyphs.accidentalNatural;\n            const yShiftDiff = (el1, el2) => Math.abs(el2.getYShift() - el1.getYShift());\n            if ((isNatural(prevGlyph) || isNatural(glyph)) && yShiftDiff(prevGlyph, glyph) < 10) {\n                extraWidth = 2;\n            }\n            glyph.setXShift(prevGlyph.getXShift() + prevGlyph.getWidth() + extraWidth);\n        }\n        this.children.push(glyph);\n        this.width += glyph.getWidth() + extraWidth;\n    }\n    cancelKey(spec) {\n        this.formatted = false;\n        this.cancelKeySpec = spec;\n        return this;\n    }\n    convertToCancelAccList(spec) {\n        const cancelAccList = Tables.keySignature(spec);\n        const differentTypes = this.accList.length > 0 && cancelAccList.length > 0 && cancelAccList[0].type !== this.accList[0].type;\n        const naturals = differentTypes ? cancelAccList.length : cancelAccList.length - this.accList.length;\n        if (naturals < 1)\n            return undefined;\n        const cancelled = [];\n        for (let i = 0; i < naturals; i++) {\n            let index = i;\n            if (!differentTypes) {\n                index = cancelAccList.length - naturals + i;\n            }\n            const acc = cancelAccList[index];\n            cancelled.push({ type: 'n', line: acc.line });\n        }\n        this.accList = cancelled.concat(this.accList);\n        return {\n            accList: cancelled,\n            type: cancelAccList[0].type,\n        };\n    }\n    addToStave(stave) {\n        this.paddingForced = true;\n        stave.addModifier(this);\n        return this;\n    }\n    setStave(stave) {\n        this.formatted = false;\n        return super.setStave(stave);\n    }\n    getBoundingBox() {\n        if (!this.formatted)\n            this.format();\n        return super.getBoundingBox();\n    }\n    calculateDimensions() {\n        let boundingBox;\n        if (this.children.length > 0) {\n            boundingBox = this.children[0].getBoundingBox();\n        }\n        else {\n            boundingBox = new BoundingBox(this.x + this.xShift, this.y + this.yShift, 0, 0);\n        }\n        this.children.forEach((glyph) => {\n            boundingBox.mergeWith(glyph.getBoundingBox());\n        });\n        this.width = boundingBox.getW();\n        this.height = boundingBox.getH();\n        this.y = boundingBox.getY();\n    }\n    convertAccLines(clef, type, accList = this.accList) {\n        let offset = 0.0;\n        let customLines;\n        switch (clef) {\n            case 'soprano':\n                if (type === '#')\n                    customLines = [2.5, 0.5, 2, 0, 1.5, -0.5, 1];\n                else\n                    offset = -1;\n                break;\n            case 'mezzo-soprano':\n                if (type === 'b')\n                    customLines = [0, 2, 0.5, 2.5, 1, 3, 1.5];\n                else\n                    offset = 1.5;\n                break;\n            case 'alto':\n                offset = 0.5;\n                break;\n            case 'tenor':\n                if (type === '#')\n                    customLines = [3, 1, 2.5, 0.5, 2, 0, 1.5];\n                else\n                    offset = -0.5;\n                break;\n            case 'baritone-f':\n            case 'baritone-c':\n                if (type === 'b')\n                    customLines = [0.5, 2.5, 1, 3, 1.5, 3.5, 2];\n                else\n                    offset = 2;\n                break;\n            case 'bass':\n            case 'french':\n                offset = 1;\n                break;\n            default:\n                break;\n        }\n        let i;\n        if (typeof customLines !== 'undefined') {\n            for (i = 0; i < accList.length; ++i) {\n                accList[i].line = customLines[i];\n            }\n        }\n        else if (offset !== 0) {\n            for (i = 0; i < accList.length; ++i) {\n                accList[i].line += offset;\n            }\n        }\n    }\n    getPadding(index) {\n        if (!this.formatted)\n            this.format();\n        return this.children.length === 0 || (!this.paddingForced && index < 2) ? 0 : this.padding;\n    }\n    getWidth() {\n        if (!this.formatted)\n            this.format();\n        return this.width;\n    }\n    setKeySig(keySpec, cancelKeySpec, alterKeySpec) {\n        this.formatted = false;\n        this.keySpec = keySpec;\n        this.cancelKeySpec = cancelKeySpec;\n        this.alterKeySpec = alterKeySpec;\n        return this;\n    }\n    alterKey(alterKeySpec) {\n        this.formatted = false;\n        this.alterKeySpec = alterKeySpec;\n        return this;\n    }\n    convertToAlterAccList(alterKeySpec) {\n        const max = Math.min(alterKeySpec.length, this.accList.length);\n        for (let i = 0; i < max; ++i) {\n            if (alterKeySpec[i]) {\n                this.accList[i].type = alterKeySpec[i];\n            }\n        }\n    }\n    format() {\n        let stave = this.getStave();\n        if (!stave) {\n            stave = new Stave(0, 0, 100);\n            this.setStave(stave);\n        }\n        this.width = 0;\n        this.children = [];\n        this.accList = Tables.keySignature(defined(this.keySpec));\n        const accList = this.accList;\n        const firstAccidentalType = accList.length > 0 ? accList[0].type : undefined;\n        let cancelAccList;\n        if (this.cancelKeySpec) {\n            cancelAccList = this.convertToCancelAccList(this.cancelKeySpec);\n        }\n        if (this.alterKeySpec) {\n            this.convertToAlterAccList(this.alterKeySpec);\n        }\n        if (this.accList.length > 0) {\n            const clef = (this.position === StaveModifierPosition.END ? stave.getEndClef() : stave.getClef()) || stave.getClef();\n            if (cancelAccList) {\n                this.convertAccLines(clef, cancelAccList.type, cancelAccList.accList);\n            }\n            this.convertAccLines(clef, firstAccidentalType, accList);\n            for (let i = 0; i < this.accList.length; ++i) {\n                this.convertToGlyph(this.accList[i], this.accList[i + 1], stave);\n            }\n        }\n        this.calculateDimensions();\n        this.formatted = true;\n    }\n    draw() {\n        const stave = this.checkStave();\n        const ctx = stave.checkContext();\n        if (!this.formatted)\n            this.format();\n        this.setRendered();\n        ctx.openGroup('keysignature', this.getAttribute('id'));\n        for (let i = 0; i < this.children.length; i++) {\n            const glyph = this.children[i];\n            glyph.renderText(ctx, this.x, 0);\n        }\n        ctx.closeGroup();\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,oBAAoB;AACzE,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,YAAY,SAASJ,aAAa,CAAC;EAC5C,WAAWK,QAAQA,CAAA,EAAG;IAClB,OAAO,cAAc;EACzB;EACAC,WAAWA,CAACC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAE;IAC9C,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,CAACJ,OAAO,EAAEC,aAAa,EAAEC,YAAY,CAAC;IACpD,IAAI,CAACG,WAAW,CAACX,qBAAqB,CAACY,KAAK,CAAC;IAC7C,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC9B;EACAC,cAAcA,CAACC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAChC,MAAMC,IAAI,GAAGlB,MAAM,CAACmB,eAAe,CAACJ,GAAG,CAACK,IAAI,CAAC;IAC7C,MAAMC,KAAK,GAAG,IAAI1B,OAAO,CAAC,cAAc,CAAC;IACzC0B,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC;IACnB,IAAIK,UAAU,GAAG,CAAC;IAClBF,KAAK,CAACG,SAAS,CAACP,KAAK,CAACQ,WAAW,CAACV,GAAG,CAACW,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,CAACd,QAAQ,CAACe,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAG,IAAI,CAAChB,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACe,MAAM,GAAG,CAAC,CAAC;MACzD,MAAME,SAAS,GAAIC,EAAE,IAAKA,EAAE,CAACC,OAAO,CAAC,CAAC,KAAKnC,MAAM,CAACoC,iBAAiB;MACnE,MAAMC,UAAU,GAAGA,CAACC,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACC,GAAG,CAACF,GAAG,CAACG,SAAS,CAAC,CAAC,GAAGJ,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC;MAC5E,IAAI,CAACT,SAAS,CAACD,SAAS,CAAC,IAAIC,SAAS,CAACR,KAAK,CAAC,KAAKY,UAAU,CAACL,SAAS,EAAEP,KAAK,CAAC,GAAG,EAAE,EAAE;QACjFE,UAAU,GAAG,CAAC;MAClB;MACAF,KAAK,CAACkB,SAAS,CAACX,SAAS,CAACY,SAAS,CAAC,CAAC,GAAGZ,SAAS,CAACa,QAAQ,CAAC,CAAC,GAAGlB,UAAU,CAAC;IAC9E;IACA,IAAI,CAACX,QAAQ,CAAC8B,IAAI,CAACrB,KAAK,CAAC;IACzB,IAAI,CAACsB,KAAK,IAAItB,KAAK,CAACoB,QAAQ,CAAC,CAAC,GAAGlB,UAAU;EAC/C;EACAqB,SAASA,CAACC,IAAI,EAAE;IACZ,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACxC,aAAa,GAAGuC,IAAI;IACzB,OAAO,IAAI;EACf;EACAE,sBAAsBA,CAACF,IAAI,EAAE;IACzB,MAAMG,aAAa,GAAGhD,MAAM,CAACiD,YAAY,CAACJ,IAAI,CAAC;IAC/C,MAAMK,cAAc,GAAG,IAAI,CAAC1C,OAAO,CAACmB,MAAM,GAAG,CAAC,IAAIqB,aAAa,CAACrB,MAAM,GAAG,CAAC,IAAIqB,aAAa,CAAC,CAAC,CAAC,CAAC5B,IAAI,KAAK,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC,CAACY,IAAI;IAC5H,MAAM+B,QAAQ,GAAGD,cAAc,GAAGF,aAAa,CAACrB,MAAM,GAAGqB,aAAa,CAACrB,MAAM,GAAG,IAAI,CAACnB,OAAO,CAACmB,MAAM;IACnG,IAAIwB,QAAQ,GAAG,CAAC,EACZ,OAAOC,SAAS;IACpB,MAAMC,SAAS,GAAG,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,EAAEG,CAAC,EAAE,EAAE;MAC/B,IAAIC,KAAK,GAAGD,CAAC;MACb,IAAI,CAACJ,cAAc,EAAE;QACjBK,KAAK,GAAGP,aAAa,CAACrB,MAAM,GAAGwB,QAAQ,GAAGG,CAAC;MAC/C;MACA,MAAMvC,GAAG,GAAGiC,aAAa,CAACO,KAAK,CAAC;MAChCF,SAAS,CAACX,IAAI,CAAC;QAAEtB,IAAI,EAAE,GAAG;QAAEM,IAAI,EAAEX,GAAG,CAACW;MAAK,CAAC,CAAC;IACjD;IACA,IAAI,CAAClB,OAAO,GAAG6C,SAAS,CAACG,MAAM,CAAC,IAAI,CAAChD,OAAO,CAAC;IAC7C,OAAO;MACHA,OAAO,EAAE6C,SAAS;MAClBjC,IAAI,EAAE4B,aAAa,CAAC,CAAC,CAAC,CAAC5B;IAC3B,CAAC;EACL;EACAqC,UAAUA,CAACxC,KAAK,EAAE;IACd,IAAI,CAACJ,aAAa,GAAG,IAAI;IACzBI,KAAK,CAACyC,WAAW,CAAC,IAAI,CAAC;IACvB,OAAO,IAAI;EACf;EACAC,QAAQA,CAAC1C,KAAK,EAAE;IACZ,IAAI,CAAC6B,SAAS,GAAG,KAAK;IACtB,OAAO,KAAK,CAACa,QAAQ,CAAC1C,KAAK,CAAC;EAChC;EACA2C,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACd,SAAS,EACf,IAAI,CAACe,MAAM,CAAC,CAAC;IACjB,OAAO,KAAK,CAACD,cAAc,CAAC,CAAC;EACjC;EACAE,mBAAmBA,CAAA,EAAG;IAClB,IAAIC,WAAW;IACf,IAAI,IAAI,CAACnD,QAAQ,CAACe,MAAM,GAAG,CAAC,EAAE;MAC1BoC,WAAW,GAAG,IAAI,CAACnD,QAAQ,CAAC,CAAC,CAAC,CAACgD,cAAc,CAAC,CAAC;IACnD,CAAC,MACI;MACDG,WAAW,GAAG,IAAIrE,WAAW,CAAC,IAAI,CAACsE,CAAC,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACnF;IACA,IAAI,CAACvD,QAAQ,CAACwD,OAAO,CAAE/C,KAAK,IAAK;MAC7B0C,WAAW,CAACM,SAAS,CAAChD,KAAK,CAACuC,cAAc,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,IAAI,CAACjB,KAAK,GAAGoB,WAAW,CAACO,IAAI,CAAC,CAAC;IAC/B,IAAI,CAACC,MAAM,GAAGR,WAAW,CAACS,IAAI,CAAC,CAAC;IAChC,IAAI,CAACN,CAAC,GAAGH,WAAW,CAACU,IAAI,CAAC,CAAC;EAC/B;EACAC,eAAeA,CAACC,IAAI,EAAEvD,IAAI,EAAEZ,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IAChD,IAAIoE,MAAM,GAAG,GAAG;IAChB,IAAIC,WAAW;IACf,QAAQF,IAAI;MACR,KAAK,SAAS;QACV,IAAIvD,IAAI,KAAK,GAAG,EACZyD,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAE7CD,MAAM,GAAG,CAAC,CAAC;QACf;MACJ,KAAK,eAAe;QAChB,IAAIxD,IAAI,KAAK,GAAG,EACZyD,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,KAE1CD,MAAM,GAAG,GAAG;QAChB;MACJ,KAAK,MAAM;QACPA,MAAM,GAAG,GAAG;QACZ;MACJ,KAAK,OAAO;QACR,IAAIxD,IAAI,KAAK,GAAG,EACZyD,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,KAE1CD,MAAM,GAAG,CAAC,GAAG;QACjB;MACJ,KAAK,YAAY;MACjB,KAAK,YAAY;QACb,IAAIxD,IAAI,KAAK,GAAG,EACZyD,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAE5CD,MAAM,GAAG,CAAC;QACd;MACJ,KAAK,MAAM;MACX,KAAK,QAAQ;QACTA,MAAM,GAAG,CAAC;QACV;MACJ;QACI;IACR;IACA,IAAItB,CAAC;IACL,IAAI,OAAOuB,WAAW,KAAK,WAAW,EAAE;MACpC,KAAKvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,OAAO,CAACmB,MAAM,EAAE,EAAE2B,CAAC,EAAE;QACjC9C,OAAO,CAAC8C,CAAC,CAAC,CAAC5B,IAAI,GAAGmD,WAAW,CAACvB,CAAC,CAAC;MACpC;IACJ,CAAC,MACI,IAAIsB,MAAM,KAAK,CAAC,EAAE;MACnB,KAAKtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9C,OAAO,CAACmB,MAAM,EAAE,EAAE2B,CAAC,EAAE;QACjC9C,OAAO,CAAC8C,CAAC,CAAC,CAAC5B,IAAI,IAAIkD,MAAM;MAC7B;IACJ;EACJ;EACAE,UAAUA,CAACvB,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAACT,SAAS,EACf,IAAI,CAACe,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI,CAACjD,QAAQ,CAACe,MAAM,KAAK,CAAC,IAAK,CAAC,IAAI,CAACd,aAAa,IAAI0C,KAAK,GAAG,CAAE,GAAG,CAAC,GAAG,IAAI,CAACwB,OAAO;EAC9F;EACAtC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACK,SAAS,EACf,IAAI,CAACe,MAAM,CAAC,CAAC;IACjB,OAAO,IAAI,CAAClB,KAAK;EACrB;EACAlC,SAASA,CAACJ,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAE;IAC5C,IAAI,CAACuC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACzC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI;EACf;EACAyE,QAAQA,CAACzE,YAAY,EAAE;IACnB,IAAI,CAACuC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACvC,YAAY,GAAGA,YAAY;IAChC,OAAO,IAAI;EACf;EACA0E,qBAAqBA,CAAC1E,YAAY,EAAE;IAChC,MAAM2E,GAAG,GAAG9C,IAAI,CAAC+C,GAAG,CAAC5E,YAAY,CAACoB,MAAM,EAAE,IAAI,CAACnB,OAAO,CAACmB,MAAM,CAAC;IAC9D,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,GAAG,EAAE,EAAE5B,CAAC,EAAE;MAC1B,IAAI/C,YAAY,CAAC+C,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC9C,OAAO,CAAC8C,CAAC,CAAC,CAAClC,IAAI,GAAGb,YAAY,CAAC+C,CAAC,CAAC;MAC1C;IACJ;EACJ;EACAO,MAAMA,CAAA,EAAG;IACL,IAAI5C,KAAK,GAAG,IAAI,CAACmE,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACnE,KAAK,EAAE;MACRA,KAAK,GAAG,IAAIpB,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC;MAC5B,IAAI,CAAC8D,QAAQ,CAAC1C,KAAK,CAAC;IACxB;IACA,IAAI,CAAC0B,KAAK,GAAG,CAAC;IACd,IAAI,CAAC/B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACJ,OAAO,GAAGR,MAAM,CAACiD,YAAY,CAAChD,OAAO,CAAC,IAAI,CAACI,OAAO,CAAC,CAAC;IACzD,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAM6E,mBAAmB,GAAG7E,OAAO,CAACmB,MAAM,GAAG,CAAC,GAAGnB,OAAO,CAAC,CAAC,CAAC,CAACY,IAAI,GAAGgC,SAAS;IAC5E,IAAIJ,aAAa;IACjB,IAAI,IAAI,CAAC1C,aAAa,EAAE;MACpB0C,aAAa,GAAG,IAAI,CAACD,sBAAsB,CAAC,IAAI,CAACzC,aAAa,CAAC;IACnE;IACA,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAAC0E,qBAAqB,CAAC,IAAI,CAAC1E,YAAY,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,CAACmB,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMgD,IAAI,GAAG,CAAC,IAAI,CAACW,QAAQ,KAAKvF,qBAAqB,CAACwF,GAAG,GAAGtE,KAAK,CAACuE,UAAU,CAAC,CAAC,GAAGvE,KAAK,CAACwE,OAAO,CAAC,CAAC,KAAKxE,KAAK,CAACwE,OAAO,CAAC,CAAC;MACpH,IAAIzC,aAAa,EAAE;QACf,IAAI,CAAC0B,eAAe,CAACC,IAAI,EAAE3B,aAAa,CAAC5B,IAAI,EAAE4B,aAAa,CAACxC,OAAO,CAAC;MACzE;MACA,IAAI,CAACkE,eAAe,CAACC,IAAI,EAAEU,mBAAmB,EAAE7E,OAAO,CAAC;MACxD,KAAK,IAAI8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9C,OAAO,CAACmB,MAAM,EAAE,EAAE2B,CAAC,EAAE;QAC1C,IAAI,CAACxC,cAAc,CAAC,IAAI,CAACN,OAAO,CAAC8C,CAAC,CAAC,EAAE,IAAI,CAAC9C,OAAO,CAAC8C,CAAC,GAAG,CAAC,CAAC,EAAErC,KAAK,CAAC;MACpE;IACJ;IACA,IAAI,CAAC6C,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAChB,SAAS,GAAG,IAAI;EACzB;EACA4C,IAAIA,CAAA,EAAG;IACH,MAAMzE,KAAK,GAAG,IAAI,CAAC0E,UAAU,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAG3E,KAAK,CAAC4E,YAAY,CAAC,CAAC;IAChC,IAAI,CAAC,IAAI,CAAC/C,SAAS,EACf,IAAI,CAACe,MAAM,CAAC,CAAC;IACjB,IAAI,CAACiC,WAAW,CAAC,CAAC;IAClBF,GAAG,CAACG,SAAS,CAAC,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;IACtD,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC1C,QAAQ,CAACe,MAAM,EAAE2B,CAAC,EAAE,EAAE;MAC3C,MAAMjC,KAAK,GAAG,IAAI,CAACT,QAAQ,CAAC0C,CAAC,CAAC;MAC9BjC,KAAK,CAAC4E,UAAU,CAACL,GAAG,EAAE,IAAI,CAAC5B,CAAC,EAAE,CAAC,CAAC;IACpC;IACA4B,GAAG,CAACM,UAAU,CAAC,CAAC;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}