{"ast": null, "code": "import { Element } from './element.js';\nimport { log } from './util.js';\nimport { Vibrato } from './vibrato.js';\nfunction L(...args) {\n  if (VibratoBracket.DEBUG) log('VexFlow.VibratoBracket', args);\n}\nexport class VibratoBracket extends Element {\n  static get CATEGORY() {\n    return \"VibratoBracket\";\n  }\n  constructor(bracketData) {\n    super();\n    this.vibrato = new Vibrato();\n    if (bracketData.start) this.start = bracketData.start;\n    if (bracketData.stop) this.stop = bracketData.stop;\n    this.line = 1;\n  }\n  setLine(line) {\n    this.line = line;\n    return this;\n  }\n  setVibratoCode(code) {\n    this.vibrato.setVibratoCode(code);\n    return this;\n  }\n  draw() {\n    const ctx = this.checkContext();\n    this.setRendered();\n    const y = this.start && this.start.checkStave().getYForTopText(this.line) || this.stop && this.stop.checkStave().getYForTopText(this.line) || 0;\n    const startX = this.start && this.start.getAbsoluteX() || this.stop && this.stop.checkStave().getTieStartX() || 0;\n    const stopX = this.stop && this.stop.getAbsoluteX() - this.stop.getWidth() - 5 || this.start && this.start.checkStave().getTieEndX() - 10 || 0;\n    this.vibrato.setVibratoWidth(stopX - startX);\n    L('Rendering VibratoBracket: startX:', startX, 'stopX:', stopX, 'y:', y);\n    this.vibrato.renderText(ctx, startX, y);\n  }\n}\nVibratoBracket.DEBUG = false;", "map": {"version": 3, "names": ["Element", "log", "Vibrato", "L", "args", "VibratoBracket", "DEBUG", "CATEGORY", "constructor", "bracketData", "vibrato", "start", "stop", "line", "setLine", "setVibratoCode", "code", "draw", "ctx", "checkContext", "setRendered", "y", "checkStave", "getYForTopText", "startX", "getAbsoluteX", "getTieStartX", "stopX", "getWidth", "getTieEndX", "setVibratoWidth", "renderText"], "sources": ["C:/Users/<USER>/Desktop/music/music-converter/node_modules/vexflow/build/esm/src/vibratobracket.js"], "sourcesContent": ["import { Element } from './element.js';\nimport { log } from './util.js';\nimport { Vibrato } from './vibrato.js';\nfunction L(...args) {\n    if (VibratoBracket.DEBUG)\n        log('VexFlow.VibratoBracket', args);\n}\nexport class VibratoBracket extends Element {\n    static get CATEGORY() {\n        return \"VibratoBracket\";\n    }\n    constructor(bracketData) {\n        super();\n        this.vibrato = new Vibrato();\n        if (bracketData.start)\n            this.start = bracketData.start;\n        if (bracketData.stop)\n            this.stop = bracketData.stop;\n        this.line = 1;\n    }\n    setLine(line) {\n        this.line = line;\n        return this;\n    }\n    setVibratoCode(code) {\n        this.vibrato.setVibratoCode(code);\n        return this;\n    }\n    draw() {\n        const ctx = this.checkContext();\n        this.setRendered();\n        const y = (this.start && this.start.checkStave().getYForTopText(this.line)) ||\n            (this.stop && this.stop.checkStave().getYForTopText(this.line)) ||\n            0;\n        const startX = (this.start && this.start.getAbsoluteX()) || (this.stop && this.stop.checkStave().getTieStartX()) || 0;\n        const stopX = (this.stop && this.stop.getAbsoluteX() - this.stop.getWidth() - 5) ||\n            (this.start && this.start.checkStave().getTieEndX() - 10) ||\n            0;\n        this.vibrato.setVibratoWidth(stopX - startX);\n        L('Rendering VibratoBracket: startX:', startX, 'stopX:', stopX, 'y:', y);\n        this.vibrato.renderText(ctx, startX, y);\n    }\n}\nVibratoBracket.DEBUG = false;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,CAACA,CAAC,GAAGC,IAAI,EAAE;EAChB,IAAIC,cAAc,CAACC,KAAK,EACpBL,GAAG,CAAC,wBAAwB,EAAEG,IAAI,CAAC;AAC3C;AACA,OAAO,MAAMC,cAAc,SAASL,OAAO,CAAC;EACxC,WAAWO,QAAQA,CAAA,EAAG;IAClB,OAAO,gBAAgB;EAC3B;EACAC,WAAWA,CAACC,WAAW,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAG,IAAIR,OAAO,CAAC,CAAC;IAC5B,IAAIO,WAAW,CAACE,KAAK,EACjB,IAAI,CAACA,KAAK,GAAGF,WAAW,CAACE,KAAK;IAClC,IAAIF,WAAW,CAACG,IAAI,EAChB,IAAI,CAACA,IAAI,GAAGH,WAAW,CAACG,IAAI;IAChC,IAAI,CAACC,IAAI,GAAG,CAAC;EACjB;EACAC,OAAOA,CAACD,IAAI,EAAE;IACV,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,OAAO,IAAI;EACf;EACAE,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACN,OAAO,CAACK,cAAc,CAACC,IAAI,CAAC;IACjC,OAAO,IAAI;EACf;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,GAAG,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,MAAMC,CAAC,GAAI,IAAI,CAACV,KAAK,IAAI,IAAI,CAACA,KAAK,CAACW,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,IAAI,CAACV,IAAI,CAAC,IACrE,IAAI,CAACD,IAAI,IAAI,IAAI,CAACA,IAAI,CAACU,UAAU,CAAC,CAAC,CAACC,cAAc,CAAC,IAAI,CAACV,IAAI,CAAE,IAC/D,CAAC;IACL,MAAMW,MAAM,GAAI,IAAI,CAACb,KAAK,IAAI,IAAI,CAACA,KAAK,CAACc,YAAY,CAAC,CAAC,IAAM,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACU,UAAU,CAAC,CAAC,CAACI,YAAY,CAAC,CAAE,IAAI,CAAC;IACrH,MAAMC,KAAK,GAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACa,YAAY,CAAC,CAAC,GAAG,IAAI,CAACb,IAAI,CAACgB,QAAQ,CAAC,CAAC,GAAG,CAAC,IAC1E,IAAI,CAACjB,KAAK,IAAI,IAAI,CAACA,KAAK,CAACW,UAAU,CAAC,CAAC,CAACO,UAAU,CAAC,CAAC,GAAG,EAAG,IACzD,CAAC;IACL,IAAI,CAACnB,OAAO,CAACoB,eAAe,CAACH,KAAK,GAAGH,MAAM,CAAC;IAC5CrB,CAAC,CAAC,mCAAmC,EAAEqB,MAAM,EAAE,QAAQ,EAAEG,KAAK,EAAE,IAAI,EAAEN,CAAC,CAAC;IACxE,IAAI,CAACX,OAAO,CAACqB,UAAU,CAACb,GAAG,EAAEM,MAAM,EAAEH,CAAC,CAAC;EAC3C;AACJ;AACAhB,cAAc,CAACC,KAAK,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}